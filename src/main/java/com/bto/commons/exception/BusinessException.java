package com.bto.commons.exception;

import com.bto.commons.response.ResultEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 业务异常类
 *
 * <AUTHOR>
 * @date 2023/3/29 13:59
 */

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BusinessException extends RuntimeException {
    /**
     *业务异常状态码
     */
    private String code;

    /**
     * 业务异常状态信息
     */
    private String message;
    /**
     * 业务异常状态信息
     */
    private String data;

    public BusinessException(String message) {
        this.code = ResultEnum.SYSTEM_RUNTIME_FAILED.getCode();
        this.message = message;
    }

    public BusinessException(ResultEnum resultEnum) {
        this.code = resultEnum.getCode();
        this.message = resultEnum.getMessage();
    }

    public BusinessException(ResultEnum resultEnum, String data) {
        this.code = resultEnum.getCode();
        this.message = resultEnum.getMessage();
        this.data = data;
    }

    public BusinessException(String code, String message) {
        this.code = code;
        this.message = message;
    }


}
