package com.bto.commons.pojo.vo;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/19 14:24
 */

@Data
@ApiModel("逆变器图表数据")
@AllArgsConstructor
public class InverterChartByDayVO implements Serializable {
    private static final long serialVersionUID = -6530423701797215440L;
    private DeviceVO device;
    private List<?> powerInfoDTOList;
    private String totalElectricity;
}
