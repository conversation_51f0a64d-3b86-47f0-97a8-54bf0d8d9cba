package com.bto.commons.pojo.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.bto.commons.enums.UserEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户信息
 *
 * <AUTHOR>
 * @date 2023/5/18 8:56
 */
@Data
@ApiModel("用户信息")
public class UserInfoVO implements Serializable {
    private static final long serialVersionUID = 1863005399883443191L;
    @ExcelIgnore
    @ApiModelProperty("用户Uid")
    private String userUid;

    @ExcelProperty("用户名")
    @ApiModelProperty("用户名")
    private String userName;

    @ExcelProperty("用户手机号")
    @ApiModelProperty("用户手机号")
    private String userPhone;

    @ExcelIgnore
    @ApiModelProperty("用户邮箱")
    private String userEmail;

    @ExcelProperty("用户状态")
    @ApiModelProperty("用户状态")
    private String userStatus;
    @ExcelIgnore
    @ApiModelProperty("用户头像")
    private String userAvatar;

    @ExcelProperty("用户类型")
    @ApiModelProperty("用户类型")
    private String userType;

    @ExcelIgnore
    @ApiModelProperty("创建者")
    private String creator;

    @ExcelIgnore
    @ApiModelProperty("角色ID")
    private String roleID;

    @ExcelProperty("角色名称")
    @ApiModelProperty("角色名称")
    private String roleName;

    @ExcelProperty("项目专项")
    @ApiModelProperty("项目专项")
    private String projectName;

    @ExcelIgnore
    @ApiModelProperty("项目编号")
    private String projectID;

    @ExcelProperty("创建时间")
    @ApiModelProperty("创建时间")
    private String createTime;

    @ApiModelProperty("布局")
    private String layout;

    public String getUserStatus() {
        return userStatus;
    }

    public void setUserStatus(String userStatus) {
        if (UserEnum.USER_STATUS_ENABLE.getCode().equals(userStatus)){
            this.userStatus = UserEnum.USER_STATUS_ENABLE.getName();
        }else if(UserEnum.USER_STATUS_DISENABLE.getCode().equals(userStatus)){
            this.userStatus = UserEnum.USER_STATUS_DISENABLE.getName();
        }else{
            this.userStatus = userStatus;
        }
    }

    public void setUserType(String userType) {
        if (UserEnum.USER_OF_ENTERPRISE.getCode().equals(userType)){
            this.userType = UserEnum.USER_OF_ENTERPRISE.getName();
        }else if(UserEnum.USER_OF_INDIVIDUAL.getCode().equals(userType)){
            this.userType = UserEnum.USER_OF_INDIVIDUAL.getName();
        }else {
            this.userStatus = userStatus;
        }
    }
}
