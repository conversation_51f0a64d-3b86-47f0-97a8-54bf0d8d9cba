package com.bto.commons.pojo.vo;

import com.bto.commons.utils.TreeNode;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
* 城市地图
*
* <AUTHOR> 
* @since  2024-04-17
*/
@Data
@Schema(description = "城市地图")
public class CityMapVO extends TreeNode<CityMapVO> implements Serializable {
	private static final long serialVersionUID = 1L;
	@ApiModelProperty("城市名")
	private String name;
	@ApiModelProperty("城市类型：provide，city，area")
	private String type;
	@ApiModelProperty("地图id")
	private String mapId;
}