package com.bto.commons.pojo.vo;

import com.bto.commons.constant.*;
import com.bto.commons.enums.*;
import com.bto.commons.utils.BusinessCalculateUtil;
import com.bto.commons.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @TableName v_user_plant
 */
@Data
@ApiModel("电站列表视图实体")
public class PlantInfoVO implements Serializable {

    private static final long serialVersionUID = -147130069781709128L;

    @NotBlank(message = "[电站uid]不能为空")
    @Size(max = 125, message = "编码长度不能超过125")
    @ApiModelProperty("电站uid")
    @Length(max = 125, message = "编码长度不能超过125")
    private String plantUid;

    @NotBlank(message = "[电站名称]不能为空")
    @Size(max = 255, message = "编码长度不能超过255")
    @ApiModelProperty("电站名称")
    @Length(max = 255, message = "编码长度不能超过255")
    private String plantName;

    @NotNull(message = "[装机容量(以最小单位Wp存储)kWp*1000]不能为空")
    @ApiModelProperty("装机容量(以最小单位Wp存储)kWp*1000")
    private String plantCapacity;



    @ApiModelProperty("电站朝向")
    private String orientationLabel;

    @Size(max = 10, message = "编码长度不能超过10")
    @ApiModelProperty("电站朝向（0：不一致，1：一致; -1 :无朝向）")
    @Length(max = 10, message = "编码长度不能超过10")
    private String orientation;
    @NotNull(message = "[电站状态（0：离线，1：正常运行，2：告警运行,3:自检提示,5:逆变器夜间离线）]不能为空")
    @ApiModelProperty("电站状态（0：离线，1：正常运行，2：告警运行,3:自检提示,5:逆变器夜间离线）")
    private String plantStatus;

    @NotNull(message = "[0：并网，1：储能，2：混合，3：交流耦合]不能为空")
    @ApiModelProperty("0：并网，1：储能，2：混合，3：交流耦合")
    private String plantType;

    @NotNull(message = "[逆变器数量]不能为空")
    @ApiModelProperty("逆变器数量")
    private Integer inverterNum;

    @NotNull(message = "[逆变器Sn]不能为空")
    @ApiModelProperty("逆变器Sn")
    private String inverterSn;

    @NotBlank(message = "[配电箱状态（0：正常 1：  -1:未初始化）]不能为空")
    @Size(max = 20, message = "编码长度不能超过20")
    @ApiModelProperty("配电箱状态（0：正常 1：  -1:未初始化）")
    @Length(max = 20, message = "编码长度不能超过20")
    private String powerDistributor;

    @Size(max = 20, message = "编码长度不能超过20")
    @ApiModelProperty("国家")
    @Length(max = 20, message = "编码长度不能超过20")
    private String country;

    @Size(max = 125, message = "编码长度不能超过125")
    @ApiModelProperty("省")
    @Length(max = 125, message = "编码长度不能超过125")
    private String province;

    @Size(max = 70, message = "编码长度不能超过70")
    @ApiModelProperty("市/州")
    @Length(max = 70, message = "编码长度不能超过70")
    private String city;

    @Size(max = 55, message = "编码长度不能超过55")
    @ApiModelProperty("县/区")
    @Length(max = 55, message = "编码长度不能超过55")
    private String area;

    @Size(max = 55, message = "编码长度不能超过55")
    @ApiModelProperty("镇/街道")
    @Length(max = 55, message = "编码长度不能超过55")
    private String town;

    @Size(max = 255, message = "编码长度不能超过255")
    @ApiModelProperty("详细地址")
    @Length(max = 255, message = "编码长度不能超过255")
    private String address;

    @NotBlank(message = "[经度]不能为空")
    @Size(max = 255, message = "编码长度不能超过255")
    @ApiModelProperty("经度")
    @Length(max = 255, message = "编码长度不能超过255")
    private String longitude;

    @NotBlank(message = "[纬度]不能为空")
    @Size(max = 255, message = "编码长度不能超过255")
    @ApiModelProperty("纬度")
    @Length(max = 255, message = "编码长度不能超过255")
    private String latitude;

    @NotNull(message = "[功率(以最小单位W存储)]不能为空")
    @ApiModelProperty("功率(以最小单位W存储)")
    private Integer power;

    @NotNull(message = "[日发电量,KWh*100(以最小单位存储,最小单位KWh代表一度电，存在2位小数，需要乘100不保留小数)]不能为空")
    @ApiModelProperty("日发电量,KWh*100(以最小单位存储,最小单位KWh代表一度电，存在2位小数，需要乘100不保留小数)")
    private String todayElectricity;

    @NotNull(message = "[月发电量，KWh*100(以最小单位存储,最小单位KWh代表一度电，存在2位小数，需要乘100不保留小数)]不能为空")
    @ApiModelProperty("月发电量，KWh*100(以最小单位存储,最小单位KWh代表一度电，存在2位小数，需要乘100不保留小数)")
    private String monthElectricity;

    @NotNull(message = "[年发电量,kWh(以最小单位存储,最小单位KWh代表一度电，存在2位小数，需要乘100不保留小数)]不能为空")
    @ApiModelProperty("年发电量,kWh(以最小单位存储,最小单位KWh代表一度电，存在2位小数，需要乘100不保留小数)")
    private String yearElectricity;

    @NotNull(message = "[累计发电量,kWh(以最小单位存储,最小单位KWh代表一度电，存在2位小数，需要乘100不保留小数)]不能为空")
    @ApiModelProperty("累计发电量,kWh(以最小单位存储,最小单位KWh代表一度电，存在2位小数，需要乘100不保留小数)")
    private String totalElectricity;
    @ApiModelProperty("日等效小时")
    private String dailyEfficiencyPerHour;
    @ApiModelProperty("年等效小时")
    private String yearlyEfficiencyPerHour;

    @ApiModelProperty("电站电量数据接收时间")
    private String receiveTime;

    @ApiModelProperty("出售自发电价（元）")
    private String salePrice;

    @ApiModelProperty("项目分类id(1:户用，2:整县-河源。。。。)")
    private String projectId;

    @ApiModelProperty("项目分类id(1:户用，2:整县-河源。。。。)")
    private String projectName;

    @ApiModelProperty("项目分类id(1:户用，2:整县-河源。。。。)")
    private String projectCompany;

    @Size(max = 125, message = "编码长度不能超过125")
    @ApiModelProperty("电表编号")
    @Length(max = 125, message = "编码长度不能超过125")
    private String meterId;

    @NotBlank(message = "[用户uid]不能为空")
    @Size(max = 70, message = "编码长度不能超过70")
    @ApiModelProperty("用户uid")
    @Length(max = 70, message = "编码长度不能超过70")
    private String userUid;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime createTime;

    @ApiModelProperty("质保时间")
    @JsonFormat(pattern = DateUtils.DATE_PATTERN)
    private String warrantyTime;

    @ApiModelProperty("更新时间")
    private String updateTime;

    @Size(max = 50, message = "编码长度不能超过50")
    @ApiModelProperty("用户名")
    @Length(max = 50, message = "编码长度不能超过50")
    private String userName;

    @Size(max = 15, message = "编码长度不能超过15")
    @ApiModelProperty("手机号码")
    @Length(max = 15, message = "编码长度不能超过15")
    private String userPhone;

    public void setWarrantyTime(String warrantyTime) {
        this.warrantyTime =  DateUtils.getDateOffsetByYear(warrantyTime.toString(), Constant.WARRANTY_TERM);
    }

    public void setPlantCapacity(String plantCapacity) {
        this.plantCapacity = BusinessCalculateUtil.getRealPlantCapacity(plantCapacity);
    }

    // public void setOrientation(String orientation) {
    //     this.orientation = OrientationEnum.getNameByCode(orientation);
    // }


    public String getOrientationLabel() {
        return  OrientationEnum.getNameByCode(orientation);
    }

    public void setPlantStatus(String plantStatus) {
        this.plantStatus = PlantStatusEnum.getNameByCode(plantStatus);
    }

    public void setPlantType(String plantType) {
        this.plantType = PlantTypeEnum.getNameByCode(plantType);
    }

    public void setProjectCompany(String projectCompany) {
        this.projectCompany = ProjectTypeEnum.getProjectNameById(projectCompany);
    }

    public void setPowerDistributor(String powerDistributor) {
        this.powerDistributor = PowerDistributorEnum.getNameByCode(powerDistributor);
    }

    public void setPower(Integer power) {
        this.power = power;
    }

    public void setTodayElectricity(String todayElectricity) {
        this.todayElectricity = BusinessCalculateUtil.getRealElectricity(todayElectricity);
    }

    public void setMonthElectricity(String monthElectricity) {
        this.monthElectricity = BusinessCalculateUtil.getRealElectricity(monthElectricity);
    }

    public void setYearElectricity(String yearElectricity) {
        this.yearElectricity = BusinessCalculateUtil.getRealElectricity(yearElectricity);
    }

    public void setTotalElectricity(String totalElectricity) {
        this.totalElectricity = BusinessCalculateUtil.getRealElectricity(totalElectricity);
    }

    public void setProjectName(String projectName) {
        this.projectName = ProjectTypeEnum.getProjectNameById(projectName);
    }

}
