package com.bto.commons.pojo.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/4/26 14:35
 */
@Data
@ApiModel("发电信息")
public class ElectricityInfoVO implements Serializable {
    private static final long serialVersionUID = 6902309469678448432L;
    @ApiModelProperty("发电量")
    @ExcelProperty("发电量")
    private String electricity;
    @ApiModelProperty("装机容量")
    @ExcelProperty("装机容量")
    private String plantCapacity;
    @ApiModelProperty("数据时间")
    @ExcelProperty("数据时间")
    private String dataTime;
    @ApiModelProperty("发电效率")
    @ExcelProperty("发电效率")
    private String electricityEfficiency;
    @ApiModelProperty("电站电价")
    @ExcelProperty("电站电价")
    private String plantPrice;
    @ApiModelProperty("收益")
    @ExcelProperty("收益")
    private String income;
    @ApiModelProperty("等效利用小时")
    @ExcelProperty("等效利用小时")
    private String efficiencyPerHours;

    @ApiModelProperty("同比:on year-on-year basis")
    @ExcelProperty("同比")
    private String yoyRatio;
    @ApiModelProperty("环比: month-on-month ratio")
    @ExcelProperty("环比")
    private String momRatio;
    {
        this.electricity = "0";
        this.plantPrice = "0";
        this.electricityEfficiency = "-";
        this.yoyRatio = "-";
        this.momRatio = "-";
    }
    public String getElectricityEfficiency() {
        return electricityEfficiency;
    }

    public void setElectricityEfficiency(String electricityEfficiency) {
        BigDecimal b1 = new BigDecimal(electricityEfficiency).multiply(BigDecimal.valueOf(10)).setScale(2,BigDecimal.ROUND_HALF_UP);
        this.electricityEfficiency = b1.toString();
    }

}
