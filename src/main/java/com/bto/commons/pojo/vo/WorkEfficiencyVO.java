package com.bto.commons.pojo.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/5/5 11:42
 */
@Data
public class WorkEfficiencyVO implements Serializable {
    private String plantUid;
    private String inverterId;
    private String plantName;
    private String workEfficiency;

    public String getWorkEfficiency() {
        return workEfficiency;
    }

    public void setWorkEfficiency(String workEfficiency) {
        BigDecimal b1 = new BigDecimal(workEfficiency).multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.workEfficiency = b1.toString();
    }
}
