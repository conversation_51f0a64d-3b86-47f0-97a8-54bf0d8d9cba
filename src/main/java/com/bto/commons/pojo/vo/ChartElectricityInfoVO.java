package com.bto.commons.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @date 2023/5/5 17:57
 */
@Data
@ApiModel
public class ChartElectricityInfoVO implements Serializable {
    private static final long serialVersionUID = 6972877226208976604L;
    @ApiModelProperty("日期")
    private String collectDate;
    @ApiModelProperty("发电量")
    private String electricity;

    public void setElectricity(String electricity) {
        BigDecimal b1 = new BigDecimal(electricity).divide(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
        this.electricity = b1.toString();
    }

}
