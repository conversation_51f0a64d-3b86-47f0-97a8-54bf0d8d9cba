package com.bto.commons.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *  逆变器实时数据信息实体
 * <AUTHOR>
 * @date 2023/5/8 11:27
 */
@Data
@ApiModel("逆变器实时数据实体")
public class InverterRealTimeInfoVO implements Serializable {
    private static final long serialVersionUID = 3575362944694696585L;
    @ApiModelProperty("逆变器SN")
    private String inverterSN;
    @ApiModelProperty("数据时间")
    private String initTime;
    @ApiModelProperty("输入电流1路")
    private String ipv1;
    @ApiModelProperty("输入电流2路")
    private String ipv2;
    @ApiModelProperty("输入电流3路")
    private String ipv3;
    @ApiModelProperty("输入电流4路")
    private String ipv4;
    @ApiModelProperty("输入电流5路")
    private String ipv5;
    @ApiModelProperty("输入电流6路")
    private String ipv6;
    @ApiModelProperty("输入电流7路")
    private String ipv7;
    @ApiModelProperty("输入电流8路")
    private String ipv8;
    @ApiModelProperty("输入电流9路")
    private String ipv9;
    @ApiModelProperty("输入电流10路")
    private String ipv10;
    @ApiModelProperty("输入电流11路")
    private String ipv11;
    @ApiModelProperty("输入电流12路")
    private String ipv12;
    @ApiModelProperty("输入电压1路")
    private String vpv1;
    @ApiModelProperty("输入电压2路")
    private String vpv2;
    @ApiModelProperty("输入电压3路")
    private String vpv3;
    @ApiModelProperty("输入电压4路")
    private String vpv4;
    @ApiModelProperty("输入电压5路")
    private String vpv5;
    @ApiModelProperty("输入电压6路")
    private String vpv6;
    @ApiModelProperty("输入电压7路")
    private String vpv7;
    @ApiModelProperty("输入电压8路")
    private String vpv8;
    @ApiModelProperty("输入电压9路")
    private String vpv9;
    @ApiModelProperty("输入电压10路")
    private String vpv10;
    @ApiModelProperty("输入电压11路")
    private String vpv11;
    @ApiModelProperty("输入电压12路")
    private String vpv12;
    @ApiModelProperty("输出电流1路")
    private String iac1;
    @ApiModelProperty("输出电流2路")
    private String iac2;
    @ApiModelProperty("输出电流3路")
    private String iac3;
    @ApiModelProperty("输出电压1路")
    private String vac1;
    @ApiModelProperty("输出电压2路")
    private String vac2;
    @ApiModelProperty("输出电压3路")
    private String vac3;
    @ApiModelProperty("1路频率")
    private String fac1;
    @ApiModelProperty("2路频率")
    private String fac2;
    @ApiModelProperty("3路频率")
    private String fac3;
    @ApiModelProperty("实时功率")
    private String power;
    @ApiModelProperty("pv1功率")
    private String power1;
    @ApiModelProperty("pv2功率")
    private String power2;
    @ApiModelProperty("pv3功率")
    private String power3;
    @ApiModelProperty("pv4功率")
    private String power4;
    @ApiModelProperty("pv5功率")
    private String power5;
    @ApiModelProperty("pv6功率")
    private String power6;
    @ApiModelProperty("pv7功率")
    private String power7;
    @ApiModelProperty("pv8功率")
    private String power8;
    @ApiModelProperty("pv9功率")
    private String power9;
    @ApiModelProperty("pv10功率")
    private String power10;
    @ApiModelProperty("pv11功率")
    private String power11;
    @ApiModelProperty("pv12功率")
    private String power12;
    @ApiModelProperty("1路组串信息")
    private String pv1;
    @ApiModelProperty("2路组串信息")
    private String pv2;
    @ApiModelProperty("3路组串信息")
    private String pv3;
    @ApiModelProperty("4路组串信息")
    private String pv4;
    @ApiModelProperty("5路组串信息")
    private String pv5;
    @ApiModelProperty("6路组串信息")
    private String pv6;
    @ApiModelProperty("7路组串信息")
    private String pv7;
    @ApiModelProperty("8路组串信息")
    private String pv8;
    @ApiModelProperty("9路组串信息")
    private String pv9;
    @ApiModelProperty("10路组串信息")
    private String pv10;
    @ApiModelProperty("11路组串信息")
    private String pv11;
    @ApiModelProperty("12路组串信息")
    private String pv12;
    @ApiModelProperty("日发电量")
    private String todayElectricity;
    @ApiModelProperty("总发电量")
    private String totalElectricity;
    @ApiModelProperty("更新时间")
    private String updateTime;
    @ApiModelProperty("温度")
    private String temp;
    {
        this.ipv1 = "0";
        this.ipv2 = "0";
        this.ipv3 = "0";
        this.ipv4 = "0";
        this.ipv5 = "0";
        this.ipv6 = "0";
        this.ipv7 = "0";
        this.ipv8 = "0";
        this.ipv9 = "0";
        this.ipv10 = "0";
        this.ipv11 = "0";
        this.ipv12 = "0";
        this.vpv1 = "0";
        this.vpv2 = "0";
        this.vpv3 = "0";
        this.vpv4 = "0";
        this.vpv5 = "0";
        this.vpv6 = "0";
        this.vpv7 = "0";
        this.vpv8 = "0";
        this.vpv9 = "0";
        this.vpv10 = "0";
        this.vpv11 = "0";
        this.vpv12 = "0";
        this.vac1 = "0";
        this.vac2 = "0";
        this.vac3 = "0";
        this.iac1 = "0";
        this.iac2 = "0";
        this.iac3 = "0";
        this.fac1 = "0";
        this.fac2 = "0";
        this.fac3 = "0";
        this.power = "0";
        this.power1 = "0";
        this.power2 = "0";
        this.power3 = "0";
        this.power4 = "0";
        this.power5 = "0";
        this.power6 = "0";
        this.power7 = "0";
        this.power8 = "0";
        this.power9 = "0";
        this.power10 = "0";
        this.power11 = "0";
        this.power12 = "0";
        this.todayElectricity = "0";
        this.totalElectricity = "0";
        this.temp="0";
    }

    public String getIpv1() {
        return ipv1;
    }

    public void setIpv1(String ipv1) {
        BigDecimal b1 = new BigDecimal(ipv1);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.ipv1 = b2.toString();
    }

    public String getIpv2() {
        return ipv2;
    }

    public void setIpv2(String ipv2) {
        BigDecimal b1 = new BigDecimal(ipv2);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.ipv2 = b2.toString();
    }

    public String getIpv3() {
        return ipv3;
    }

    public void setIpv3(String ipv3) {
        BigDecimal b1 = new BigDecimal(ipv3);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.ipv3 = b2.toString();
    }

    public String getIpv4() {
        return ipv4;
    }

    public void setIpv4(String ipv4) {
        BigDecimal b1 = new BigDecimal(ipv4);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.ipv4 = b2.toString();
    }

    public String getIpv5() {
        return ipv5;
    }

    public void setIpv5(String ipv5) {
        BigDecimal b1 = new BigDecimal(ipv5);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.ipv5 = b2.toString();
    }

    public String getIpv6() {
        return ipv6;
    }

    public void setIpv6(String ipv6) {
        BigDecimal b1 = new BigDecimal(ipv6);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.ipv6 = b2.toString();
    }

    public String getIpv7() {
        return ipv7;
    }

    public void setIpv7(String ipv7) {
        BigDecimal b1 = new BigDecimal(ipv7);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.ipv7 = b2.toString();
    }

    public String getIpv8() {
        return ipv8;
    }

    public void setIpv8(String ipv8) {
        BigDecimal b1 = new BigDecimal(ipv8);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.ipv8 = b2.toString();
    }

    public String getIpv9() {
        return ipv9;
    }

    public void setIpv9(String ipv9) {
        BigDecimal b1 = new BigDecimal(ipv9);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.ipv9 = b2.toString();
    }

    public String getIpv10() {
        return ipv10;
    }

    public void setIpv10(String ipv10) {
        BigDecimal b1 = new BigDecimal(ipv10);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.ipv10 = b2.toString();
    }

    public String getIpv11() {
        return ipv11;
    }

    public void setIpv11(String ipv11) {
        BigDecimal b1 = new BigDecimal(ipv11);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.ipv11 = b2.toString();
    }

    public String getIpv12() {
        return ipv12;
    }

    public void setIpv12(String ipv12) {
        BigDecimal b1 = new BigDecimal(ipv12);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.ipv12 = b2.toString();
    }

    public String getVpv1() {
        return vpv1;
    }

    public void setVpv1(String vpv1) {
        BigDecimal b1 = new BigDecimal(vpv1);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.vpv1 = b2.toString();
    }

    public String getVpv2() {
        return vpv2;
    }

    public void setVpv2(String vpv2) {
        BigDecimal b1 = new BigDecimal(vpv2);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.vpv2 = b2.toString();
    }

    public String getVpv3() {
        return vpv3;
    }

    public void setVpv3(String vpv3) {
        BigDecimal b1 = new BigDecimal(vpv3);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.vpv3 = b2.toString();
    }

    public String getVpv4() {
        return vpv4;
    }

    public void setVpv4(String vpv4) {
        BigDecimal b1 = new BigDecimal(vpv4);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.vpv4 = b2.toString();
    }

    public String getVpv5() {
        return vpv5;
    }

    public void setVpv5(String vpv5) {
        BigDecimal b1 = new BigDecimal(vpv5);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.vpv5 = b2.toString();
    }

    public String getVpv6() {
        return vpv6;
    }

    public void setVpv6(String vpv6) {
        BigDecimal b1 = new BigDecimal(vpv6);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.vpv6 = b2.toString();
    }

    public String getVpv7() {
        return vpv7;
    }

    public void setVpv7(String vpv7) {
        BigDecimal b1 = new BigDecimal(vpv7);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.vpv7 = b2.toString();
    }

    public String getVpv8() {
        return vpv8;
    }

    public void setVpv8(String vpv8) {
        BigDecimal b1 = new BigDecimal(vpv8);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.vpv8 = b2.toString();
    }

    public String getVpv9() {
        return vpv9;
    }

    public void setVpv9(String vpv9) {
        BigDecimal b1 = new BigDecimal(vpv9);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.vpv9 = b2.toString();
    }

    public String getVpv10() {
        return vpv10;
    }

    public void setVpv10(String vpv10) {
        BigDecimal b1 = new BigDecimal(vpv10);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.vpv10 = b2.toString();
    }

    public String getVpv11() {
        return vpv11;
    }

    public void setVpv11(String vpv11) {
        BigDecimal b1 = new BigDecimal(vpv11);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.vpv11 = b2.toString();
    }

    public String getVpv12() {
        return vpv12;
    }

    public void setVpv12(String vpv12) {
        BigDecimal b1 = new BigDecimal(vpv12);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.vpv12 = b2.toString();
    }

    public String getIac1() {
        return iac1;
    }

    public void setIac1(String iac1) {
        BigDecimal b1 = new BigDecimal(iac1);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.iac1 = b2.toString();
    }

    public String getIac2() {
        return iac2;
    }

    public void setIac2(String iac2) {
        BigDecimal b1 = new BigDecimal(iac2);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.iac2 = b2.toString();
    }

    public String getIac3() {
        return iac3;
    }

    public void setIac3(String iac3) {
        BigDecimal b1 = new BigDecimal(iac3);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.iac3 = b2.toString();
    }

    public String getVac1() {
        return vac1;
    }

    public void setVac1(String vac1) {
        BigDecimal b1 = new BigDecimal(vac1);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.vac1 = b2.toString();
    }

    public String getVac2() {
        return vac2;
    }

    public void setVac2(String vac2) {
        BigDecimal b1 = new BigDecimal(vac2);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.vac2 = b2.toString();
    }

    public String getVac3() {
        return vac3;
    }

    public void setVac3(String vac3) {
        BigDecimal b1 = new BigDecimal(vac3);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.vac3 = b2.toString();
    }

    public String getFac1() {
        return fac1;
    }

    public void setFac1(String fac1) {
        BigDecimal b1 = new BigDecimal(fac1);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.fac1 = b2.toString();
    }

    public String getFac2() {
        return fac2;
    }

    public void setFac2(String fac2) {
        BigDecimal b1 = new BigDecimal(fac2);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.fac2 = b2.toString();
    }

    public String getFac3() {
        return fac3;
    }

    public void setFac3(String fac3) {
        BigDecimal b1 = new BigDecimal(fac3);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.fac3 = b2.toString();
    }

    public String getTodayElectricity() {
        return todayElectricity;
    }

    public void setTodayElectricity(String todayElectricity) {
        BigDecimal b1 = new BigDecimal(todayElectricity);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.todayElectricity = b2.toString();
    }

    public String getTotalElectricity() {
        return totalElectricity;
    }

    public void setTotalElectricity(String totalElectricity) {
        BigDecimal b1 = new BigDecimal(totalElectricity);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.totalElectricity = b2.toString();
    }

}
