package com.bto.commons.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/5/9 10:26
 */
@Data
@ApiModel("逆变器实时数据查询条件")
public class InverterRealTimeQueryVO implements Serializable {

    private static final long serialVersionUID = -1142277650366793549L;
    @ApiModelProperty("查询日期")
    private String date;
    @ApiModelProperty("逆变器SN")
    private String inverterSN;
    @ApiModelProperty("当前页码")
   private Integer currentPage;
    @ApiModelProperty("页面大小")
   private Integer pageSize;
}
