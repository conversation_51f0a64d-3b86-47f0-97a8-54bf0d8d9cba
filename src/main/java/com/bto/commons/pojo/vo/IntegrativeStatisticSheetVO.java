package com.bto.commons.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @date 2023/6/15 17:54
 */
@Data
@ApiModel("综合统计表")
public class IntegrativeStatisticSheetVO implements Serializable {
    private static final long serialVersionUID = 5946870077897231081L;
    @ApiModelProperty("电站数量")
    private String plantNum;
    @ApiModelProperty("电站装机容量")
    private String plantCapacity;
    @ApiModelProperty("功率")
    private String power;
    @ApiModelProperty("发电量")
    private String electricity;
    @ApiModelProperty("区间发电量")
    private String periodElectricity;
    @ApiModelProperty("收入")
    private String income;
    @ApiModelProperty("CO2减排")
    private String reduceCo2;
    @ApiModelProperty("节约煤炭")
    private String reduceCoal;
    @ApiModelProperty("等效植树")
    private String treeNum;
    @ApiModelProperty("等效利用小时")
    private String efficiencyPerHours;
    @ApiModelProperty("正常设备数")
    private String normalDeviceNum;
    @ApiModelProperty("离线设备数")
    private String offlineDeviceNum;
    @ApiModelProperty("自检提示设备数")
    private String selfCheckDeviceNum;
    @ApiModelProperty("告警设备数")
    private String alarmDeviceNum;
    @ApiModelProperty("未初始化设备数")
    private String noInitDeviceNum;
    @ApiModelProperty("正常关机设备数")
    private String normalOfflineDeviceNum;
    @ApiModelProperty("统计时间")
    private String statisticalTime;

    {
        this.normalDeviceNum = "0";
        this.offlineDeviceNum = "0";
        this.selfCheckDeviceNum = "0";
        this.alarmDeviceNum = "0";
        this.noInitDeviceNum = "0";
        this.normalOfflineDeviceNum = "0";
        this.periodElectricity = "0";
        this.efficiencyPerHours = "0";
        this.plantCapacity = "0";
        this.electricity = "0";
    }

    public void setPlantCapacity(String plantCapacity) {

        this.plantCapacity = plantCapacity;
    }

    public void setElectricity(String electricity) {
        this.electricity = electricity;
    }

    public void setPeriodElectricity(String periodElectricity) {
        if (periodElectricity!=null){
            BigDecimal b1 = new BigDecimal(periodElectricity);
            this.periodElectricity = b1.divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).toString();
        }
    }
}
