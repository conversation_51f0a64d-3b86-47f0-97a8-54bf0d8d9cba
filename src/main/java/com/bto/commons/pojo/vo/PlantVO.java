package com.bto.commons.pojo.vo;

import cn.hutool.core.util.StrUtil;
import com.bto.commons.enums.OrientationEnum;
import com.bto.commons.enums.PlantStatusEnum;
import com.bto.commons.enums.PlantTypeEnum;
import com.bto.commons.enums.PowerDistributorEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 电站详情信息类
 * <AUTHOR>
 * @date 2023/08/14
 */

@Data
@NoArgsConstructor
@ApiModel("电站详情信息类")
public class PlantVO implements Serializable {
    private static final long serialVersionUID = -1741629957754887940L;
    @ApiModelProperty("电站Uid")
    private String plantUid;
    @ApiModelProperty("电站名称")
    private String plantName;
    @ApiModelProperty("装机容量(以最小单位Wp存储)")
    private String plantCapacity;
    @ApiModelProperty("电站朝向（0：不一致，1：一致）")
    private String orientation;
    @ApiModelProperty("电站状态")
    private String plantStatus;
    @ApiModelProperty("电站类型")
    private String plantTypeId;
    @ApiModelProperty("逆变器数量")
    private Integer inverterNum;
    @ApiModelProperty("配电箱状态")
    private String powerDistributor;
    @ApiModelProperty("国家")
    private String country;
    @ApiModelProperty("省")
    private String province;
    @ApiModelProperty("市/州")
    private String city;
    @ApiModelProperty("县/区")
    private String area;
    @ApiModelProperty("镇/街道")
    private String town;
    @ApiModelProperty("详细地址")
    private String address;
    @ApiModelProperty("经度")
    private String longitude;
    @ApiModelProperty("纬度")
    private String latitude;
    @ApiModelProperty("功率(以最小单位W存储)")
    private Integer power;
    @ApiModelProperty("日发电量")
    private String todayElectricity;
    @ApiModelProperty("月发电量")
    private String monthElectricity;
    @ApiModelProperty("年发电量")
    private String yearElectricity;
    @ApiModelProperty("累计发电量")
    private String totalElectricity;
    @ApiModelProperty("电站电量数据接收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private String receiveTime;
    @ApiModelProperty("出售自发电价")
    private String salePrice;
    @ApiModelProperty("项目Id")
    private Integer projectId;
    @ApiModelProperty("电表编号")
    private String meterId;
    @ApiModelProperty("用户id")
    private String userUid;
    @ApiModelProperty("电站工作效率 ")
    private String workEfficiency;
    @ApiModelProperty("等效植树")
    private String treeNum;
    @ApiModelProperty("CO2减排")
    private String reduceCo2;
    @ApiModelProperty("当日收益")
    private String todayIncome;
    @ApiModelProperty("累计收益")
    private String totalIncome;
    @ApiModelProperty("当日告警次数")
    private Integer todayAlarmNum;
    @ApiModelProperty("峰值功率")
    private String maxPower;
    @ApiModelProperty("在线逆变器数量")
    private String onlineInverterNum;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @ApiModelProperty("建站时间")
    private String createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @ApiModelProperty("质保时间")
    private String warrantyTime;
    @ApiModelProperty("用户名称")
    private String userName;
    @ApiModelProperty("用户电话")
    private String userPhone;
    @ApiModelProperty("日等效小时")
    private String dailyEfficiencyPerHour;
    @ApiModelProperty("年等效小时")
    private String yearlyEfficiencyPerHour;
    @ApiModelProperty("逆变器sn列表")
    private List<String> inverterSN;

    @ApiModelProperty("消防模块ID列表")
    private List<String> fireFighting;

    {
        this.inverterNum = 0;
        this.power = 0;
        this.maxPower = "0";
        this.salePrice = "0.45";
    }

    public void setMaxPower(String maxPower) {
        if (StrUtil.isEmpty(maxPower)){
            maxPower = "0";
        }
        this.maxPower = maxPower;
    }

    public void setOrientation(String orientation) {
        this.orientation = OrientationEnum.getNameByCode(orientation);
    }

    public void setPlantStatus(String plantStatus) {
        this.plantStatus = PlantStatusEnum.getNameByCode(plantStatus);
    }

    public void setPowerDistributor(String powerDistributor) {
        this.powerDistributor = PowerDistributorEnum.getNameByCode(powerDistributor);
    }

    public void setPlantTypeId(String plantTypeId) {
        this.plantTypeId = PlantTypeEnum.getNameByCode(plantTypeId);
    }
}