package com.bto.commons.pojo.vo;

import com.bto.commons.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* 配电室档案
*
* <AUTHOR> 
* @since  2024-06-03
*/
@Data
@Schema(description = "配电室档案")
public class PdArchivesVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@Schema(description = "配电室id")
	private String pdId;

	@Schema(description = "配电室名称")
	private String pdName;

	@Schema(description = "配电室图片地址")
	private String pdPhotoUrl;

	@Schema(description = "配电室类型")
	private String pdType;

	@Schema(description = "配电室型号")
	private String pdModule;

	@Schema(description = "配电室地址")
	private String pdAddress;

	@Schema(description = "创建时间")
	@JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
	private Date createTime;

	@Schema(description = "更新时间")
	@JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
	private Date updateTime;


}