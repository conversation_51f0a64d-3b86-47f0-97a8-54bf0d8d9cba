package com.bto.commons.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(description = "气象站信息")
public class WeatherStationVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "气象站id")
    private String stationUid;

    @Schema(description = "气象站名称")
    private String stationName;

    @Schema(description = "运维器SN")
    private String edgServerSn;

    @Schema(description = "运维器通讯模块imei")
    private String imei;

    // @Schema(description = "设备地址（逻辑地址）")
    // private String deviceAddress;

    // @Schema(description = "国家")
    // private String country;

    // @Schema(description = "省")
    // private String province;

    @Schema(description = "市/州")
    private String city;

    @Schema(description = "县/区")
    private String area;

    @Schema(description = "镇/街道")
    private String town;

    @Schema(description = "村")
    private String village;

    @Schema(description = "详细地址")
    private String address;

    @Schema(description = "经度")
    private String longitude;

    @Schema(description = "纬度")
    private String latitude;

    // @Schema(description = "电站状态（0：存在，1：删除）")
    // private Integer isDeleted;

    // @Schema(description = "数据创建时间")
    // @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    // private Date createTime;
    //
    // @Schema(description = "数据更新时间")
    // @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    // private Date updateTime;

    @Schema(description = "备注")
    private String remarks;

}