package com.bto.commons.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/7/4 8:21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InverterAdditionalPropertyVO implements Serializable {

    private static final long serialVersionUID = -2988644325773224747L;

    /**
     * 主键id
     */
    @NotNull(message = "[主键id]不能为空")
    @ApiModelProperty("主键id")
    private String id;
    /**
     * 逆变器SN
     */
    @NotBlank(message = "[逆变器SN]不能为空")
    @Size(max = 50, message = "编码长度不能超过50")
    @ApiModelProperty("逆变器SN")
    @Length(max = 50, message = "编码长度不能超过50")
    private String inverterSn;
    /**
     * 功率（W）
     */
    @NotNull(message = "[功率（W）]不能为空")
    @ApiModelProperty("功率（W）")
    private String power;
    /**
     * 当天发电量（kWh）*100
     */
    @NotNull(message = "[当天发电量（kWh）*100]不能为空")
    @ApiModelProperty("当天发电量（kWh）*100")
    private String todayElectricity;
    /**
     * 当月发电量（kWh）*100
     */
    @NotNull(message = "[当月发电量（kWh）*100]不能为空")
    @ApiModelProperty("当月发电量（kWh）*100")
    private String monthElectricity;
    /**
     * 当年发电量（kWh）*100
     */
    @NotNull(message = "[当年发电量（kWh）*100]不能为空")
    @ApiModelProperty("当年发电量（kWh）*100")
    private String yearElectricity;
    /**
     * 累计发电量（kWh）*100
     */
    @NotNull(message = "[累计发电量（kWh）*100]不能为空")
    @ApiModelProperty("累计发电量（kWh）*100")
    private String totalElectricity;
    /**
     * 数据时间
     */
    @ApiModelProperty("数据时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String initTime;
    /**
     * 输入电流1路（A）*100
     */
    @ApiModelProperty("输入电流1路（A）*100")
    private String ipv1;
    /**
     * 输入电流2路（A）*100
     */
    @ApiModelProperty("输入电流2路（A）*100")
    private String ipv2;
    /**
     * 输入电流3路（A）*100
     */
    @ApiModelProperty("输入电流3路（A）*100")
    private String ipv3;
    /**
     * 输入电流4路（A）*100
     */
    @ApiModelProperty("输入电流4路（A）*100")
    private String ipv4;
    /**
     * 输入电流5路（A）*100
     */
    @ApiModelProperty("输入电流5路（A）*100")
    private String ipv5;
    /**
     * 输入电流6路（A）*100
     */
    @ApiModelProperty("输入电流6路（A）*100")
    private String ipv6;
    /**
     * 输入电流7路（A）*100
     */
    @ApiModelProperty("输入电流7路（A）*100")
    private String ipv7;
    /**
     * 输入电流8路（A）*100
     */
    @ApiModelProperty("输入电流8路（A）*100")
    private String ipv8;
    /**
     * 输入电流9路（A）*100
     */
    @ApiModelProperty("输入电流9路（A）*100")
    private String ipv9;
    /**
     * 输入电流10路（A）*100
     */
    @ApiModelProperty("输入电流10路（A）*100")
    private String ipv10;
    /**
     * 输入电流11路（A）*100
     */
    @ApiModelProperty("输入电流11路（A）*100")
    private String ipv11;
    /**
     * 输入电流12路（A）*100
     */
    @ApiModelProperty("输入电流12路（A）*100")
    private String ipv12;
    /**
     * 输入电压1路（V）*100
     */
    @ApiModelProperty("输入电压1路（V）*100")
    private String vpv1;
    /**
     * 输入电压2路（V）*100
     */
    @ApiModelProperty("输入电压2路（V）*100")
    private String vpv2;
    /**
     * 输入电压3路（V）*100
     */
    @ApiModelProperty("输入电压3路（V）*100")
    private String vpv3;
    /**
     * 输入电压4路（V）*100
     */
    @ApiModelProperty("输入电压4路（V）*100")
    private String vpv4;
    /**
     * 输入电压5路（V）*100
     */
    @ApiModelProperty("输入电压5路（V）*100")
    private String vpv5;
    /**
     * 输入电压6路（V）*100
     */
    @ApiModelProperty("输入电压6路（V）*100")
    private String vpv6;
    /**
     * 输入电压7路（V）*100
     */
    @ApiModelProperty("输入电压7路（V）*100")
    private String vpv7;
    /**
     * 输入电压8路（V）*100
     */
    @ApiModelProperty("输入电压8路（V）*100")
    private String vpv8;
    /**
     * 输入电压9路（V）*100
     */
    @ApiModelProperty("输入电压9路（V）*100")
    private String vpv9;
    /**
     * 输入电压10路（V）*100
     */
    @ApiModelProperty("输入电压10路（V）*100")
    private String vpv10;
    /**
     * 输入电压11路（V）*100
     */
    @ApiModelProperty("输入电压11路（V）*100")
    private String vpv11;
    /**
     * 输入电压12路（V）*100
     */
    @ApiModelProperty("输入电压12路（V）*100")
    private String vpv12;
    /**
     * 输出电流1路（A）*100
     */
    @ApiModelProperty("输出电流1路（A）*100")
    private String iac1;
    /**
     * 输出电流2路（A）*100
     */
    @ApiModelProperty("输出电流2路（A）*100")
    private String iac2;
    /**
     * 输出电流3路（A）*100
     */
    @ApiModelProperty("输出电流3路（A）*100")
    private String iac3;
    /**
     * 输出电压1路（V）*100
     */
    @ApiModelProperty("输出电压1路（V）*100")
    private String vac1;
    /**
     * 输出电压2路（V）*100
     */
    @ApiModelProperty("输出电压2路（V）*100")
    private String vac2;
    /**
     * 输出电压3路（V）*100
     */
    @ApiModelProperty("输出电压3路（V）*100")
    private String vac3;
    /**
     * 温度（℃）
     */
    @Size(max = 12, message = "编码长度不能超过12")
    @ApiModelProperty("温度（℃）")
    @Length(max = 12, message = "编码长度不能超过12")
    private String temp;
    /**
     * 频率1（Hz）*100
     */
    @ApiModelProperty("频率1（Hz）*100")
    private String fac1;
    /**
     * 频率2（Hz）*100
     */
    @ApiModelProperty("频率2（Hz）*100")
    private String fac2;
    /**
     * 频率3（Hz）*100
     */
    @ApiModelProperty("频率3（Hz）*100")
    private String fac3;
    /**
     * pv1功率
     */
    @ApiModelProperty("pv1功率")
    private String pv1Power;
    /**
     * pv2功率
     */
    @ApiModelProperty("pv2功率")
    private String pv2Power;
    /**
     * pv3功率
     */
    @ApiModelProperty("pv3功率")
    private String pv3Power;
    /**
     * pv4功率
     */
    @ApiModelProperty("pv4功率")
    private String pv4Power;
    /**
     * pv5功率
     */
    @ApiModelProperty("pv5功率")
    private String pv5Power;
    /**
     * pv6功率
     */
    @ApiModelProperty("pv6功率")
    private String pv6Power;
    /**
     * pv7功率
     */
    @ApiModelProperty("pv7功率")
    private String pv7Power;
    /**
     * pv8功率
     */
    @ApiModelProperty("pv8功率")
    private String pv8Power;
    /**
     * pv9功率
     */
    @ApiModelProperty("pv9功率")
    private String pv9Power;
    /**
     * pv10功率
     */
    @ApiModelProperty("pv10功率")
    private String pv10Power;
    /**
     * pv11功率
     */
    @ApiModelProperty("pv11功率")
    private String pv11Power;
    /**
     * pv12功率
     */
    @ApiModelProperty("pv12功率")
    private String pv12Power;
    /**
     * 三晶数据标识（三晶：0  ，自取：1）
     */
    @NotNull(message = "[三晶数据标识（三晶：0  ，自取：1）]不能为空")
    @ApiModelProperty("三晶数据标识（三晶：0  ，自取：1）")
    private String state;
    /**
     * 数据插入时间
     */
    @ApiModelProperty("数据插入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String updateTime;

}
