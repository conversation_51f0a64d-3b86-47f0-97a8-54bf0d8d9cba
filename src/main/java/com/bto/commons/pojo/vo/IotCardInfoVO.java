package com.bto.commons.pojo.vo;

import com.bto.commons.enums.IotCardEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/11/10 9:18
 */
@Data
@ApiModel("物联网卡信息")
public class IotCardInfoVO implements Serializable {
    private static final long serialVersionUID = -3461527095100819964L;
    @ApiModelProperty("电站编号")
    private String plantUid;
    @ApiModelProperty("电站名称")
    private String plantName;
    @ApiModelProperty("用户名称")
    private String userName;
    @ApiModelProperty("电话号码")
    private String phoneNumber;
    @ApiModelProperty("物联网卡状态：0:已过期 1:正常")
    private String cardStatus;
    @ApiModelProperty("imei码")
    private String imei;
    @ApiModelProperty("cimi码")
    private String cimi;
    @ApiModelProperty("物联网卡号码")
    private String iccid;
    @ApiModelProperty("激活时间")
    private String startTime;
    @ApiModelProperty("到期时间")
    private String endTime;
    @ApiModelProperty("剩余天数")
    private Integer remainingDays;
    @ApiModelProperty("物联网卡状态：0:已过期 1:正常")
    private String cardStatusStr;
    @ApiModelProperty("建站时间")
    private String createTime;

    {
        remainingDays = null;
        cardStatus = IotCardEnum.NORMAL.getName();
    }

    public void setCardStatusStr(String cardStatusStr) {
        this.cardStatusStr = IotCardEnum.getNameByCode(cardStatusStr);
    }
}
