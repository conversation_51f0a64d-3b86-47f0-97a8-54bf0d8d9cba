package com.bto.commons.pojo.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/10/30 17:56
 */
@Data
@ApiModel("型号部分信息视图层")
public class ModelVO implements Serializable {

    private static final long serialVersionUID = 3328898819567412621L;
    @NotNull(message="[自增id]不能为空")
    @ApiModelProperty("自增id")
    @JsonIgnore
    private String id;
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("型号")
    @Length(max= 50,message="编码长度不能超过50")
    private String model;
    @ApiModelProperty("电路数")
    private String circuit;

    @ApiModelProperty("创建时间")
    private String createTime;
    @ApiModelProperty("更新时间")
    private String updateTime;
}
