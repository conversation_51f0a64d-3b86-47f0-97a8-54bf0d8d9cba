package com.bto.commons.pojo.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.bto.commons.enums.DeviceEnable;
import com.bto.commons.enums.DeviceStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/9/16 15:32
 */
@Data
@ApiModel("逆变器信息列表视图层对象")
@EqualsAndHashCode
@HeadRowHeight(60)
@ContentRowHeight(25)
@ColumnWidth(15)
@HeadStyle(fillForegroundColor = 44)
@NoArgsConstructor
@AllArgsConstructor
public class OperatorListVO implements Serializable {
    private static final long serialVersionUID = -4329838938341848275L;

    @ApiModelProperty("电站Uid")
    @ExcelIgnore
    private String plantUid;

    @ApiModelProperty("运维器sn")
    @ExcelProperty("运维器sn")
    @ColumnWidth(24)
    private String operatorSn;
    @ApiModelProperty("iccid")
    @ExcelProperty("iccid")
    private String iccid;

    @ApiModelProperty("电站名称")
    @ExcelProperty("电站名称")
    @ColumnWidth(30)
    private String plantName;

    @ApiModelProperty("数据日期")
    @ExcelIgnore
    private String collectDate;

    @ApiModelProperty("运维器IMEI")
    @ExcelProperty("运维器IMEI")
    private String imei;

    @ApiModelProperty("光伏板发电,PV发电,当日发电量(以最小单位存储,不保留小数后两位)")
    @ExcelProperty("光伏板发电")
    private String generationElectricity;

    @ApiModelProperty("负载用电,当日用电量)")
    @ExcelProperty("负载用电")
    private String useElectricity;

    @ApiModelProperty("自发自用")
    @ExcelProperty("自发自用")
    private String selfUseElectricity;

    @ApiModelProperty("买第三方电量")
    @ExcelProperty("买电量")
    private String buyElectricity;

    @ApiModelProperty("卖自发电量")
    @ExcelProperty("卖自发电量")
    private String sellElectricity;

    @ApiModelProperty("A点电压")
    @ExcelIgnore
    private String apv;

    @ApiModelProperty("B点电压")
    @ExcelIgnore
    private String bpv;

    @ApiModelProperty("C点电压")
    @ExcelIgnore
    private String cpv;

    @ApiModelProperty("D点电压")
    @ExcelIgnore
    private String dpv;

    @ApiModelProperty("（当前）正向有功总电能（impep）")
    @ExcelIgnore
    private String impep;

    @ApiModelProperty("（当前）反向有功总电能（expep）")
    @ExcelIgnore
    private String expep;

    @ApiModelProperty("三晶数据标识（三晶：0  ，自取：1）")
    @Length(max= 0,message="编码长度不能超过0")
    @ExcelIgnore
    private String state;

    @ApiModelProperty("创建时间")
    @ExcelIgnore
    private String createTime;
    @ApiModelProperty("更新时间")
    @ExcelProperty("更新时间")
    private String updateTime;

    @ApiModelProperty("运维器启动状态-状态（0：离线，1：正常运行，2：告警运行,3:自检提示) 4 夜间离线")
    @ExcelProperty("运维器启动状态")
    @ExcelIgnore
    private String status;

    @ExcelProperty("运维器启动状态")
    private String statusStr;

    @ApiModelProperty("运维器运行状态（0：关机，1：启动）")
    @ExcelProperty("运维器运行状态")
    @ExcelIgnore
    private String enable;

    @ExcelProperty("运维器运行状态")
    private String enableStr;

    public void setStatusStr(String statusStr) {
        this.statusStr = DeviceStatus.getNameByCode(statusStr);
    }

    public void setEnableStr(String enableStr) {
        this.enableStr = DeviceEnable.getNameByCode(enableStr);
    }
}
