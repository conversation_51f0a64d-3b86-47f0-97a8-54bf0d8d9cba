package com.bto.commons.pojo.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/26 14:32
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ElectricityStaticsInfoVO implements Serializable {
    private static final long serialVersionUID = 478138343735835617L;
    private String plantUid;
    private String plantName;
    private List<ElectricityInfoVO> electricityList;

    String totalIncome;
    String totalElectricity;
    String totalEfficiencyPerHours;

    String avgIncome;
    String avgElectricity;
    String avgEfficiencyPerHours;
}

