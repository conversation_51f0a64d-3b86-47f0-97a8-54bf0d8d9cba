package com.bto.commons.pojo.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/5/13 15:35
 */
@Data
@AllArgsConstructor
public class CellStyleVO implements Serializable {
    private XSSFWorkbook workbook;
//    private String sheetName;
    private HorizontalAlignment horizontalAlignment;
    private VerticalAlignment verticalAlignment;
    private FillPatternType pattern;
    private Boolean isWrapText;
//    private XSSFFont font;
}
