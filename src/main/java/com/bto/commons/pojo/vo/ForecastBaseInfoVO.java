package com.bto.commons.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> by zhb on 2023/12/22.
 */
@Data
@ApiModel("发电预测基础数据")
public class ForecastBaseInfoVO {

    @Schema(description = "风速（m/s）")
    private BigDecimal windSpeed;

    @Schema(description = "风向（0-北风，1-东北风，2-东风，3-东南风，4-南风，5-西南风，6-西风，7-西北风）")
    private String windDirection;

    @Schema(description = "太阳辐射（W/m²）")
    private Integer solarRadiation;

    @Schema(description = "温度")
    private String temp;

    @Schema(description = "相对湿度")
    private String humidity;

    @Schema(description = "云量")
    private Double cloud;

    @Schema(description = "等效小时")
    private String equivalentHour;

    @Schema(description = "当日发电量:kWh")
    private String todayElectricity;

    @Schema(description = "电站容量:kWp")
    private String plantCapacity;

    @Schema(description = "电站状态（0：离线，1：正常运行，2：告警运行,3:自检提示,5:逆变器夜间离线）")
    private String plantStatus;

    @Schema(description = "预测当日发电量:kWh")
    private String forecastTodayElectricity;

    @Schema(description = "辐射量（kW·h/m²）")
    private String radiantQuantity;

    @Schema(description = "理论辐射量（kW·h/m²）")
    private String theoryRadiantQuantity;

    @Schema(description = "当日收益")
    private String todayEarning;

    @Schema(description = "预测当日收益")
    private String forecastTodayEarning;

    @Schema(description = "电站效率")
    private String plantEfficiency;

    @Schema(description = "功率(以最小单位W存储)")
    private Integer power;

    @Schema(description = "经度")
    private String longitude;

    @Schema(description = "纬度")
    private String latitude;

    @Schema(description = "电站朝向（0：不一致，1：一致）")
    private String orientation;

    @Schema(description = "电站补充信息")
    private BtoPlantInfoVO plantReplenishInfo;

}