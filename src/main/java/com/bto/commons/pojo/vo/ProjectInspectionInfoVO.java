package com.bto.commons.pojo.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.bto.commons.enums.ProjectTypeEnum;
import com.bto.commons.utils.BusinessCalculateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 项目巡检信息
 * <AUTHOR>
 * @date 2023/10/11 11:05
 */
@Data
@ApiModel("项目巡检信息")
@EqualsAndHashCode
@HeadRowHeight(60)
@ContentRowHeight(25)
@ColumnWidth(15)
@HeadStyle(fillForegroundColor = 44)
@NoArgsConstructor
@AllArgsConstructor
public class ProjectInspectionInfoVO implements Serializable {
    private static final long serialVersionUID = -2792149169385631015L;
    @ColumnWidth(20)
    @ExcelProperty({"电站类型"})
    @ApiModelProperty("电站类型")
    private String plantType;
    @ColumnWidth(20)
    @ExcelProperty({"区域"})
    @ApiModelProperty("区域")
    private String city;
    @ColumnWidth(25)
    @ExcelProperty({"项目名称"})
    @ApiModelProperty("项目名称")
    private String projectId;
    @ColumnWidth(20)
    @ExcelProperty({"电站数量"})
    @ApiModelProperty("电站数量")
    private String plantNum;
    @ColumnWidth(20)
    @ExcelProperty({"装机容量:KWp"})
    @ApiModelProperty("装机容量:KWp")
    private String plantCapacity;
    @ColumnWidth(20)
    @ExcelProperty({"发电量:KWh"})
    @ApiModelProperty("发电量:KWh")
    private String electricity;
    @ColumnWidth(20)
    @ExcelProperty({"日等效小时"})
    @ApiModelProperty("日等效小时")
    private String dailyEfficiencyPerHour;
    @ColumnWidth(20)
    @ExcelProperty({"告警电站数量"})
    @ApiModelProperty("告警电站数量")
    private String alarmPlantNum;
    @ColumnWidth(20)
    @ExcelProperty({"信息子集"})
    @ApiModelProperty("信息子集")
    private List<ProjectInspectionInfoVO> projectInspectionInfoList;

    public void setPlantType(String plantType) {
        this.plantType = ProjectTypeEnum.getProjectNameById(plantType);
    }
    public void setProjectId(String projectId) {
        this.projectId = ProjectTypeEnum.getProjectNameById(projectId);
    }

    public void setPlantCapacity(String plantCapacity) {
        this.plantCapacity = BusinessCalculateUtil.getRealPlantCapacity(plantCapacity);
    }

    public void setElectricity(String electricity) {
        this.electricity = BusinessCalculateUtil.getRealElectricity(electricity);
    }
}
