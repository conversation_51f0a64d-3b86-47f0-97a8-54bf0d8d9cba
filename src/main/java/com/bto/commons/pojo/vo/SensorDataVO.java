package com.bto.commons.pojo.vo;

import com.bto.commons.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
* 传感器数据
*
* <AUTHOR> 
* @since 1.0.0 2024-06-17
*/
@Data
@Schema(description = "传感器数据")
public class SensorDataVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@Schema(description = "电站id")
	private String plantUid;

	@Schema(description = "传感器ID")
	private String sensorId;

	@Schema(description = "烟雾浓度(PPM)")
	private Integer smokeConcentr;

	@Schema(description = "温度(℃)")
	private BigDecimal temp;

	@Schema(description = "湿度（%RH）")
	private BigDecimal humidity;

	@Schema(description = "烟雾报警器状态(0:未告警，1：已告警)")
	private Integer alarmStatus;

	@Schema(description = "创建时间")
	@JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
	private Date createTime;

	@Schema(description = "更新时间")
	@JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
	private Date updateTime;


}