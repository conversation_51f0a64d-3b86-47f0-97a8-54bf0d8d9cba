package com.bto.commons.pojo.vo;

import com.bto.commons.enums.CounterAlarmTypeEnum;
import com.bto.commons.enums.CounterElectrifyStatusEnum;
import com.bto.commons.enums.CounterStatusEnum;
import com.bto.commons.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 配电柜数据
 *
 * <AUTHOR>
 * @since 2024-07-24
 */
@Data
@ApiModel("配电柜数据")
public class CounterRecordVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("自增ID")
    private Integer id;

    @ApiModelProperty("配电柜ID")
    private String counterId;
    @ApiModelProperty("配电柜名")
    private String counterName;

    @ApiModelProperty("数据时间")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date initTime;

    @ApiModelProperty("过压值(250-300)")
    private Integer overtension;

    @ApiModelProperty("欠压值(150-200)")
    private Integer undervoltage;

    @ApiModelProperty("漏电等级（表1）")
    private Integer drainGrade;

    @ApiModelProperty("Ir1 整定值")
    private Integer lr1Sv;

    @ApiModelProperty("Ir1 延迟时间(s)")
    private Integer lr1Time;

    @ApiModelProperty("Ir2 整定值")
    private Integer lr2Sv;

    @ApiModelProperty("Ir2 延迟时间")
    private Integer lr2Time;

    @ApiModelProperty("Ir3 整定值")
    private Integer lr3Sv;

    @ApiModelProperty("上电试合闸 0:禁用，1:启用")
    private Integer electrifyStatus;

    @ApiModelProperty("故障类型(100/250/400/630/800)")
    private Integer alarmType;

    @ApiModelProperty("故障时间")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date alarmTime;

    @ApiModelProperty("设备类型（表2）")
    private String deviceType;

    @ApiModelProperty("设备状态(表3)")
    private Integer status;

    @ApiModelProperty("故障 ID")
    private Integer alarmId;

    @ApiModelProperty("电压 A")
    private Integer vac1;

    @ApiModelProperty("电压 B")
    private Integer vac2;

    @ApiModelProperty("电压 C")
    private Integer vac3;

    @ApiModelProperty("电流 A")
    private Integer iac1;

    @ApiModelProperty("电流 B")
    private Integer iac2;

    @ApiModelProperty("电流 C")
    private Integer iac3;

    @ApiModelProperty("漏电流")
    private Integer drainCurrent;

    @ApiModelProperty("电流 N")
    private Integer currentN;

    @ApiModelProperty("数据插入时间")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date updateTime;

    public Integer getDrainGrade() {

        return drainGrade;
    }

    public String getAlarmType() {
        return CounterAlarmTypeEnum.getNameByValue(this.alarmType);
    }

    public String getElectrifyStatus() {
        return CounterElectrifyStatusEnum.getNameByValue(this.electrifyStatus);
    }

    public String getStatus() {
        return  CounterStatusEnum.getNameByValue(this.status);
    }
}