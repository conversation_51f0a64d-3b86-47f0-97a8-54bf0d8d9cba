package com.bto.commons.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/5/24 14:57
 */
@Data
@ApiModel("省市-地理坐标与电站数量")
public class AreaCoordinateInfoVO implements Serializable {
    private static final long serialVersionUID = 7601522565769393997L;
    @ApiModelProperty("区域名称")
    private String area;
    @ApiModelProperty("电站数量")
    private String plantNum;
    @ApiModelProperty("经度")
    private String longitude;
    @ApiModelProperty("纬度")
    private String latitude;

}
