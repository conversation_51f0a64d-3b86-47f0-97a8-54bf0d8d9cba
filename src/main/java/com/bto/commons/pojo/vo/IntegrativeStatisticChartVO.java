package com.bto.commons.pojo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @date 2023/6/15 14:51
 */
@Data
public class IntegrativeStatisticChartVO implements Serializable {
    private static final long serialVersionUID = 8502228259365431213L;
    @ApiModelProperty("日期")
    private String collectDate;
    @ApiModelProperty("发电量")
    private String electricity;
    @ApiModelProperty("收益")
    private String income;
    @ApiModelProperty("电站装机容量")
    private String plantCapacity;
    @ApiModelProperty("日等效小时")
    private String dailyEfficiencyPerHour;
    @ApiModelProperty("电站数量")
    private String plantNum;
    @ApiModelProperty("异常电站数量")
    private String abnormalPlantNum;
    @ApiModelProperty("正常电站数量")
    private String normalPlantNum;

    public void setPlantCapacity(String plantCapacity) {
        BigDecimal b1 = new BigDecimal(plantCapacity).divide(BigDecimal.valueOf(1000)).setScale(2, RoundingMode.HALF_UP);
        this.plantCapacity = b1.toString();
    }
    public void setElectricity(String electricity) {
        BigDecimal b1 = new BigDecimal(electricity).divide(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
        this.electricity = b1.toString();
    }

}
