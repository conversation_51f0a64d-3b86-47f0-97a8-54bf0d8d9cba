package com.bto.commons.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> by zhb on 2023/12/28.
 */
@Data
@ApiModel("发电量预测（天）")
public class BatteryDivinerVO implements Serializable {


    @ApiModelProperty("日期")
    private String collectDate;
    @ApiModelProperty("发电量")
    private String electricity;
}
