package com.bto.commons.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bto.commons.enums.ProjectTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Objects;

/**
 * 项目(客户)信息checked
 *
 * <AUTHOR>
 * @TableName bto_contract
 */
@TableName("bto_contract")
@Data
public class CustomerContractVO implements Serializable {

    private static final long serialVersionUID = 7376348936359749830L;
    /**
     * 合同号
     */
    @NotNull(message = "[合同号]不能为空")
    @ApiModelProperty("合同号")
    private String contractId;
    // @NotNull(message = "[进件编号]不能为空")
    @ApiModelProperty("进件编号")
    private String orderId;
    /**
     * 用户名
     */
    @NotBlank(message = "[用户名]不能为空")
    @Size(max = 50, message = "编码长度不能超过50")
    @ApiModelProperty("用户名")
    @Length(max = 50, message = "编码长度不能超过50")
    private String userName;
    /**
     * 电话
     */
    @NotBlank(message = "[电话]不能为空")
    @Size(max = 15, message = "编码长度不能超过15")
    @ApiModelProperty("电话")
    @Length(max = 15, message = "编码长度不能超过15")
    private String userPhone;
    /**
     * 电站名称
     */
    @NotBlank(message = "[电站名称]不能为空")
    @Size(max = 255, message = "编码长度不能超过255")
    @ApiModelProperty("电站名称")
    @Length(max = 255, message = "编码长度不能超过255")
    private String plantName;
    /**
     * 装机容量(kWp)*1000
     */
    @ApiModelProperty("装机容量(kWp)*1000")
    private String plantCapacity;
    /**
     * 电站地址
     */
    @Size(max = 255, message = "编码长度不能超过255")
    @ApiModelProperty("电站地址")
    @Length(max = 255, message = "编码长度不能超过255")
    private String address;
    /**
     * 用户状态(0:未注册；1：已注册)
     */
    @ApiModelProperty("用户状态(0:未注册；1：已注册)")
    private Integer state;
    /**
     * 项目专项(1:户用，2：整县)
     */
    @ApiModelProperty("项目专项(1:户用，2：整县)")
    @TableField("project_special")
    private Integer projectId;
    @ApiModelProperty("项目名称")
    @TableField(value = "project_special",exist = false)
    private String projectName;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private String createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private String updateTime;
    /**
     * 创建者
     */
    @NotBlank(message = "[创建者]不能为空")
    @Size(max = 70, message = "编码长度不能超过70")
    @ApiModelProperty("创建者")
    @Length(max = 70, message = "编码长度不能超过70")
    private String creator;
    /**
     * 更新者
     */
    @NotBlank(message = "[更新者]不能为空")
    @Size(max = 70, message = "编码长度不能超过70")
    @ApiModelProperty("更新者")
    @Length(max = 70, message = "编码长度不能超过70")
    private String updater;

    {
        this.state = 0;
        this.updater = "";
    }

    public void setProjectName(String projectId) {
        this.projectName = ProjectTypeEnum.getProjectNameById(projectId);
    }


    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        CustomerContractVO other = (CustomerContractVO) obj;
        return Objects.equals(this.contractId, other.contractId)
                && Objects.equals(this.userName, other.userName)
                && Objects.equals(this.userPhone, other.userPhone)
                && Objects.equals(this.address, other.address)
                && Objects.equals(this.plantCapacity, other.plantCapacity)
                && Objects.equals(this.plantName, other.plantName)
                && Objects.equals(this.projectId, other.projectId)
                && Objects.equals(this.state, other.state)
                && Objects.equals(this.creator, other.creator)
                && Objects.equals(this.updater, other.updater);
    }

    @Override
    public int hashCode() {
        return Objects.hash(contractId, userName,userPhone, address,plantCapacity,
                plantName, projectId, state, creator, updater);
    }
}
