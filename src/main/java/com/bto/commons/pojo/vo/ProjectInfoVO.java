package com.bto.commons.pojo.vo;

import com.bto.commons.utils.TreeNode;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 *  项目信息
 * <AUTHOR>
 * @date 2023/5/18 15:50
 */
@Data
public class ProjectInfoVO extends TreeNode<ProjectInfoVO> implements Serializable {
    private static final long serialVersionUID = 2927514403394705946L;
    /**
     * 项目id
     */
    private String id;
    /**
     * 父级id
     */
    private String pid;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 项目创建时间
     */

    @ApiModelProperty("项目logo图片地址")
    private String imgUrl;

    @ApiModelProperty("屏幕logo图片地址")
    private String screenLogo;

    private String createTime;
}
