package com.bto.commons.pojo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 带有传感器电站树
 *
 * <AUTHOR>
 * @since 2024-06-17 16:38:58
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PlantWithSensorTree {
    @ApiModelProperty("电站Uid")
    private String plantUid;
    @ApiModelProperty("设备名")
    private String deviceName;
    @ApiModelProperty("设备集")
    private List<SensorDataVO> children;

}