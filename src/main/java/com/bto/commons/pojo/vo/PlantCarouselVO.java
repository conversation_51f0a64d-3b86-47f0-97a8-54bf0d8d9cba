package com.bto.commons.pojo.vo;

import com.bto.commons.enums.PlantStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/4/23 15:07
 */
@Data
@ApiModel("站点轮播数据")
public class PlantCarouselVO implements Serializable {
    @ApiModelProperty("序号")
    private Integer index;
    @ApiModelProperty("电站名称")
    private String plantName;
    @ApiModelProperty("所有人")
    private String userName;
    @ApiModelProperty("电站区域")
    private String city;
    @ApiModelProperty("当前功率:W")
    private String power;
    @ApiModelProperty("电站容量:kWp")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal plantCapacity;
    @ApiModelProperty("当日发电量:kWh")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal todayElectricity;
    @ApiModelProperty("当月发电量:kWh")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal monthElectricity;
    @ApiModelProperty("当年发电量:kWh")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal yearElectricity;
    @ApiModelProperty("累计发电量:kWh")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal totalElectricity;
    @ApiModelProperty("CO2减排:t")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal co2;
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String createTime;
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String updateTime;
    @ApiModelProperty("离线时间")
    private String offlineTime;
    @ApiModelProperty("运行状态")
    private String status;
    @ApiModelProperty("电站效率")
    private String plantEfficiency;


    // 设置 createTime 字段的方法
    public void setCreateTime(Date createTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        this.createTime = sdf.format(createTime);
    }


    public String getStatus() {
        return status;
    }

    /**
     * 0:离线 1:正常运行  2:告警运行 3:自检提示 4:电站未初始化
     *
     * @param status
     */
    public void setStatus(String status) {
        this.status = PlantStatusEnum.getNameByCode(status);
    }

    public BigDecimal getPlantCapacity() {

        return plantCapacity.setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    public void setPlantCapacity(BigDecimal plantCapacity) {
        BigDecimal b1 = plantCapacity.divide(BigDecimal.valueOf(1000)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.plantCapacity = b1;
    }

    public BigDecimal getTodayElectricity() {
        return todayElectricity.setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    public void setTodayElectricity(BigDecimal todayElectricity) {
        this.todayElectricity = todayElectricity.setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    public BigDecimal getTotalElectricity() {
        return totalElectricity.setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    public void setTotalElectricity(BigDecimal totalElectricity) {
        this.totalElectricity = totalElectricity.setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    public void setMonthElectricity(BigDecimal monthElectricity) {
        this.monthElectricity = monthElectricity.setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    public void setYearElectricity(BigDecimal yearElectricity) {
        this.yearElectricity = yearElectricity.setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    public BigDecimal getCo2() {
        return co2.setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    public void setCo2(BigDecimal co2) {
        this.co2 = co2.setScale(2, BigDecimal.ROUND_HALF_UP);
    }
}
