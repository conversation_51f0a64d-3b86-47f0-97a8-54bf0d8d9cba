package com.bto.commons.pojo.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.bto.commons.enums.OrientationEnum;
import com.bto.commons.converter.DateConverter;
import com.bto.commons.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;


/**
 * <AUTHOR>
 * @since 2024/2/28 11:45
 */


@Data
@NoArgsConstructor
public class PowerPlantInfoVO implements Serializable {
    private static final long serialVersionUID = 1317031824383357177L;

    @ExcelProperty("电站Uid")
    @ApiModelProperty("电站Uid")
    private String plantUid;

    @ExcelProperty("电站名称")
    @ApiModelProperty("电站名称")
    private String plantName;

    @ExcelProperty("装机容量(单位Wp)")
    @ApiModelProperty("装机容量(单位Wp)")
    private String plantCapacity;

    @ExcelProperty("电站朝向")
    @ApiModelProperty("电站朝向（0：不一致，1：一致）")
    private String orientation;

    @ExcelIgnore
    @ApiModelProperty("省")
    private String province;

    @ExcelProperty("市/州")
    @ApiModelProperty("市/州")
    private String city;

    @ExcelIgnore
    @ApiModelProperty("县/区")
    private String area;

    @ExcelIgnore
    @ApiModelProperty("镇/街道")
    private String town;

    @ExcelProperty("安装角度")
    @ApiModelProperty("安装角度")
    private Integer bankAngle;

    @ExcelProperty("方位角")
    @ApiModelProperty("方位角")
    private Integer azimuth;

    @ExcelProperty(value = "日期",converter = DateConverter.class)
    @ApiModelProperty("日期")
    @JsonFormat(pattern = DateUtils.DATE_PATTERN, timezone = "GMT+8")
    private Date collect;

    @ExcelProperty("辐射量(kW·h/m²)")
    @ApiModelProperty("辐射量(kW·h/m²)")
    private String radiation;

    @ExcelProperty("发电量(kWh")
    @ApiModelProperty("发电量(kWh")
    private String electricity;

    @ExcelProperty("发电量效率")
    @ApiModelProperty("发电量效率")
    private String electricityEfficiency;

    public String getOrientation() {
        double divide = Double.parseDouble(OrientationEnum.divide.getCode());
        double same = Double.parseDouble(OrientationEnum.SAME.getCode());
        double dub = Double.parseDouble(orientation);

        if (divide == dub) {
            return OrientationEnum.HERRINGBONE.getName();
        } else if (same == dub) {
            return OrientationEnum.IN_LINE.getName();
        } else {
            return OrientationEnum.NONE.getName();
        }
    }
}
