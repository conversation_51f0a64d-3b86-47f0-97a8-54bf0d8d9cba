package com.bto.commons.pojo.vo;

import com.bto.commons.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> by zhb on 2023/12/22.
 */
@Data
@ApiModel("气象信息")
public class MeteorologyVO implements Serializable {
    private static final long serialVersionUID = 1L;

    // @Schema(description = "气象站id")
    // private String stationUid;

    @Schema(description = "太阳辐射（W/m²）")
    private Integer solarRadiation;

    @Schema(description = "风速（m/s）")
    private BigDecimal windSpeed;

    @Schema(description = "风向（0-北风，1-东北风，2-东风，3-东南风，4-南风，5-西南风，6-西风，7-西北风）")
    private String windDirection;

    @Schema(description = "风度（°）")
    private Integer windDegree;

    @Schema(description = "时间")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date updateTime;
}
