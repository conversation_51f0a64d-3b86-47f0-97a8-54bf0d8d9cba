package com.bto.commons.pojo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/5 10:30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PlantWithArchivesTree {
    @ApiModelProperty("电站Uid")
    private String plantUid;
    @ApiModelProperty("电站名称")
    private String plantName;
    @ApiModelProperty("设备集")
    private List<PdArchivesVO> children;

}
