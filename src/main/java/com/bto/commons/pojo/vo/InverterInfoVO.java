package com.bto.commons.pojo.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bto.commons.enums.InverterStatusEnum;
import com.bto.commons.utils.BusinessCalculateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/9/15 16:10
 */

@Data
@ApiModel("逆变器列表信息对象")
@EqualsAndHashCode
@HeadRowHeight(60)
@ContentRowHeight(25)
@ColumnWidth(15)
@HeadStyle(fillForegroundColor = 44)
@NoArgsConstructor
@AllArgsConstructor
public class InverterInfoVO implements Serializable {

    private static final long serialVersionUID = 7881502289254682018L;

    /**
     * 电站Uid
     */
    @TableId("plant_uid")
    @ApiModelProperty("电站Uid")
    @ExcelIgnore
    @ColumnWidth(20)
    private String plantUid;

    /**
     * 设备编号（设备SN码）||逆变器和非三晶运维器
     */
    @TableField("inverter_sn")
    @ApiModelProperty("设备编号(设备SN码)")
    @ExcelProperty(value = "逆变器SN")
    @ColumnWidth(20)
    private String inverterSN;

    /**
     * 项目专项(1:户用，2：整县-河源)
     */
    @TableField("project_special")
    @ApiModelProperty("项目专项(1:户用，2：整县-河源)")
    @ExcelIgnore
    @ColumnWidth(20)
    private Integer projectSpecial;

    /**
     * 数据创建时间
     */
    @TableField("create_time")
    @ApiModelProperty("数据创建时间")
    @ExcelIgnore
    @ColumnWidth(20)
    private String createTime;


    /**
     * 电站名称
     */
    @TableField("plant_name")
    @ApiModelProperty("电站名称")
    @ExcelProperty(value = "电站名称")
    @ColumnWidth(25)
    private String plantName;
    /**
     * 逆变器运行状态
     */
    @TableField("inverter_status")
    @ApiModelProperty("逆变器状态")
    @ExcelProperty(value = "逆变器状态")
    @ColumnWidth(15)
    private String inverterStatus;
    /**
     * 实时功率
     */
    @TableField("power")
    @ApiModelProperty("实时功率")
    @ExcelProperty(value = "实时功率")
    @ColumnWidth(15)
    private String power;

    @TableField("today_electricity")
    @ApiModelProperty("日发电量")
    @ExcelProperty(value = "日发电量")
    @ColumnWidth(15)
    private String todayElectricity;

    @TableField("month_electricity")
    @ApiModelProperty("月发电量")
    @ExcelProperty(value = "月发电量")
    @ColumnWidth(15)
    private String monthElectricity;

    @TableField("year_electricity")
    @ApiModelProperty("年发电量")
    @ExcelProperty("年发电量")
    @ColumnWidth(15)
    private String yearElectricity;

    @TableField("total_electricity")
    @ApiModelProperty("总发电量")
    @ExcelProperty("总发电量")
    @ColumnWidth(15)
    private String totalElectricity;

    @TableField("user_uid")
    @ExcelIgnore
    private String userUid;

    /**
     * 数据更新时间
     */
    @TableField("update_time")
    @ApiModelProperty("数据更新时间")
    @ExcelProperty(value = "数据更新时间")
    @ColumnWidth(20)
    private String updateTime;

    public void setInverterStatus(String inverterStatus) {
        this.inverterStatus = InverterStatusEnum.getNameByCode(inverterStatus);
    }

    public void setTodayElectricity(String todayElectricity) {
        this.todayElectricity = BusinessCalculateUtil.getRealElectricity(todayElectricity);
    }

    public void setMonthElectricity(String monthElectricity) {
        this.monthElectricity =  BusinessCalculateUtil.getRealElectricity(monthElectricity);
    }

    public void setYearElectricity(String yearElectricity) {
        this.yearElectricity = BusinessCalculateUtil.getRealElectricity(yearElectricity);
    }

    public void setTotalElectricity(String totalElectricity) {
        this.totalElectricity = BusinessCalculateUtil.getRealElectricity(totalElectricity);
    }
}
