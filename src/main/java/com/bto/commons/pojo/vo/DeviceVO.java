package com.bto.commons.pojo.vo;

import com.bto.commons.enums.ManufacturerEnum;
import com.bto.commons.utils.TreeNode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/11/1 10:10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("设备信息视图层")
public class DeviceVO implements Serializable {
    private static final long serialVersionUID = 6963466516486247413L;
    @ApiModelProperty("电站Uid")
    private String plantUid;
    @ApiModelProperty("设备编号（设备SN码）||逆变器和非三晶运维器")
    private String deviceId;
    @ApiModelProperty("运维器通讯模块imei || 等同于三晶 wisdom_device_sn")
    private String imei;
    @ApiModelProperty("1:逆变器、2:运维器、3:电表,4：气象站,10、配电房,11、配电柜，12、温湿度、烟感采集器")
    private Integer deviceType;
    @ApiModelProperty("厂家")
    private String manufacturer;
    @ApiModelProperty("型号")
    private String module;
    @ApiModelProperty("项目专项")
    private Integer projectSpecial;
    @ApiModelProperty("设备地址")
    private String deviceAddress;
    @ApiModelProperty("设备pc码，所有设备都有pc码")
    private String devicePc;
    @ApiModelProperty("cimi（三晶逆变器也存在iotnum）")
    private String cimi;
    @ApiModelProperty("ICCID (物联网卡号)")
    private String iccid;
    @ApiModelProperty("设备状态（0：离线，1：正常运行，2：告警运行,3:自检提示 4 为未初始化)")
    private String status;
    @ApiModelProperty("设备开关机状态（0：关机，1：启动）")
    private String enable;
    @ApiModelProperty("运维器是否采集配电箱数据（1：是 ；0：否）")
    private String cluster;
    @ApiModelProperty("激活时间")
    private String startTime;
    @ApiModelProperty("到期时间/质保时间")
    private String endTime;
    @ApiModelProperty("软件版本号")
    private String softwareVersion;
    @ApiModelProperty("显示版本号")
    private String displayVersion;
    @ApiModelProperty("控制版本号")
    private String controlVersion;
    @ApiModelProperty("工程师id(创建者)")
    private String creator;
    @ApiModelProperty("逆变器接入类型（0：并网，1：储能）")
    private String receiveType;
    @ApiModelProperty(value = "设备名称（目前电表独有：如总配电、宿舍楼、办公室、XX厂区（用于区分电表监控区域））")
    private String deviceName;
    @ApiModelProperty("电流互感器变比(电表独有)")
    private Integer currentTransformer;
    @ApiModelProperty("电压互感器变比(电表独有)")
    private Integer potentialTransformer;
    @ApiModelProperty("上级id")
    private String pId;
    public void setManufacturer(String manufacturer) {
        this.manufacturer = ManufacturerEnum.getNameByCode(manufacturer);
    }
}
