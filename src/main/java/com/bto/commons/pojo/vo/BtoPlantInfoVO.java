package com.bto.commons.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 电站信息（补充）
 *
 * <AUTHOR>
 * @since 1.0.0 2024-01-04
 */
@Data
@Schema(description = "电站信息（补充）")
public class BtoPlantInfoVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "电站uid")
    private String plantUid;

    @Schema(description = "安装角度/倾斜角度")
    private Integer bankAngle;

    @Schema(description = "方位角")
    private Integer azimuth;
}