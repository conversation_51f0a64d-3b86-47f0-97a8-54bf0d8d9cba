package com.bto.commons.pojo.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/7/3 10:11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ExcelIgnoreUnannotated
public class PlantDigestVO implements Serializable {


    private static final long serialVersionUID = 5959755388335691432L;
    /**
     * 电站Uid
     */
    @TableField(value = "plant_uid")
    @NotBlank(message = "[电站Uid]不能为空")
    @ApiModelProperty("电站Uid")
    @Length(max = 125, message = "编码长度不能超过125")
    private String plantUid;

    /**
     * 电站名称
     */
    @ExcelProperty("电站名称")
    @TableField(value = "plant_name")
    @NotBlank(message = "[电站名称]不能为空")
    @ApiModelProperty("电站名称")
    @Length(max = 255, message = "编码长度不能超过255")
    private String plantName;

    /**
     * 装机容量(以最小单位Wp存储)
     */
    @ExcelProperty("装机容量")
    @TableField(value = "plant_capacity")
    @NotNull(message = "[装机容量]不能为空")
    @ApiModelProperty("装机容量")
    private String plantCapacity;

    /**
     * 电站朝向（0：不一致，1：一致）
     */
    @TableField(value = "orientation")
    @NotNull(message = "[电站朝向]不能为空")
    @ApiModelProperty("电站朝向")
    private String orientation ;

    /**
     * 电站状态（0：离线，1：正常运行，2：告警运行,3:自检提示）
     */
    @ExcelProperty("电站状态")
    @TableField(value = "plant_status")
    @NotNull(message = "[电站状态]不能为空")
    @ApiModelProperty("电站状态")
    private String plantStatus;
    /**
     * 电站类型（0：并网，1：储能，2：混合，3：交流耦合）
     */
    @ExcelProperty("站点类型")
    @TableField(value = "plant_type_id")
    @NotNull(message = "[电站类型]不能为空")
    @ApiModelProperty("电站类型")
    private String plantTypeId;

    /**
     * 逆变器数量
     */
    @ExcelProperty("逆变器数量")
    @TableField(value = "inverter_num")
    @NotNull(message = "[逆变器数量]不能为空")
    @ApiModelProperty("逆变器数量")
    private String inverterNum;

    /**
     * 配电箱状态
     */
    @ExcelProperty("配电箱状态")
    @TableField(value = "power_distributor")
    @NotNull(message = "[配电箱状态]不能为空")
    @ApiModelProperty("配电箱状态")
    private String powerDistributor;
    /**
     * 国家
     */
    @TableField(value = "country")
    @ApiModelProperty("国家")
    private String country;
    /**
     * 省
     */
    @TableField(value = "province")
    @Size(max = 125, message = "编码长度不能超过125")
    @ApiModelProperty("省")
    @Length(max = 125, message = "编码长度不能超过125")
    private String province;
    /**
     * 市/州
     */
    @ExcelProperty("地址")
    @TableField(value = "city")
    @Size(max = 125, message = "编码长度不能超过125")
    @ApiModelProperty("市/州")
    @Length(max = 125, message = "编码长度不能超过125")
    private String city;
    /**
     * 县/区
     */
    @TableField(value = "area")
    @Size(max = 55, message = "编码长度不能超过55")
    @ApiModelProperty("县/区")
    @Length(max = 55, message = "编码长度不能超过55")
    private String area;
    /**
     * 镇/街道
     */
    @TableField(value = "town")
    @Size(max = 55, message = "编码长度不能超过55")
    @ApiModelProperty("镇/街道")
    @Length(max = 55, message = "编码长度不能超过55")
    private String town;
    /**
     * 详细地址
     */
    @TableField(value = "address")
    @Size(max = 255, message = "编码长度不能超过255")
    @ApiModelProperty("详细地址")
    @Length(max = 255, message = "编码长度不能超过255")
    private String address;
    /**
     * 经度
     */
    @TableField(value = "longitude")
    @NotBlank(message = "[经度]不能为空")
    @Size(max = 255, message = "编码长度不能超过255")
    @ApiModelProperty("经度")
    @Length(max = 255, message = "编码长度不能超过255")
    private String longitude;
    /**
     * 纬度
     */
    @TableField(value = "latitude")
    @NotBlank(message = "[纬度]不能为空")
    @Size(max = 255, message = "编码长度不能超过255")
    @ApiModelProperty("纬度")
    @Length(max = 255, message = "编码长度不能超过255")
    private String latitude;
    /**
     * 功率(以最小单位W存储)
     */
    @ExcelProperty("功率")
    @TableField(value = "power")
    @NotNull(message = "[功率]不能为空")
    @ApiModelProperty("功率(以最小单位W存储)")
    private String power;
    /**
     * 日发电量,KWh*100(以最小单位存储,最小单位KWh代表一度电，存在2位小数，需要乘100不保留小数)
     */
    @TableField(value = "today_electricity")
    @NotNull(message = "[日发电量]不能为空")
    @ApiModelProperty("日发电量")
    @ExcelProperty("日发电量")
    private String todayElectricity;
    /**
     * 月发电量，KWh*100(以最小单位存储,最小单位KWh代表一度电，存在2位小数，需要乘100不保留小数)
     */
    @TableField(value = "month_electricity")
    @NotNull(message = "[月发电量]不能为空")
    @ApiModelProperty("月发电量")
    private String monthElectricity;
    /**
     * 年发电量,kWh(以最小单位存储,最小单位KWh代表一度电，存在2位小数，需要乘100不保留小数)
     */
    @TableField(value = "year_electricity")
    @NotNull(message = "[年发电量]不能为空")
    @ApiModelProperty("年发电量")
    private String yearElectricity;

    /**
     * 累计发电量,kWh(以最小单位存储,最小单位KWh代表一度电，存在2位小数，需要乘100不保留小数)
     */
    @ExcelProperty("累计发电量")
    @TableField(value = "total_electricity")
    @NotNull(message = "[累计发电量]不能为空")
    @ApiModelProperty("累计发电量")
    private String totalElectricity;

    /**
     * 电站电量数据接收时间
     */
    @TableField(value = "receive_time")
    @ApiModelProperty("电站电量数据接收时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private String receiveTime;

    /**
     * 出售自发电价（元）
     */
    @TableField(value = "sale_price")
    @ApiModelProperty("出售自发电价（元）")
    private String salePrice;

    /**
     * 项目分类id(1:户用，2:整县-河源。。。。)
     */
    @TableField(value = "project_special")
    @NotNull(message = "[项目分类id]不能为空")
    @ApiModelProperty("项目分类id(1:户用，2:整县-河源)")
    private Integer projectSpecial;
    /**
     * 电表编号
     */
    @TableField(value = "meter_id")
    @NotNull(message = "[电表编号]不能为空")
    @ApiModelProperty("电表编号")
    private String meterId;

    /**
     * 用户id
     */
    @TableField(value = "user_uid")
    @NotBlank(message = "[用户id]不能为空")
    @ApiModelProperty("用户id")
    private String userUid;



    /**
     * 电站工作效率
     */
    @ApiModelProperty("电站工作效率 ")
    private String efficiency="";

    /**
     * 等效植树 Equivalent tree planting
     */
    @ApiModelProperty("等效植树")
    private String equivalentTreePlanting="";

    /**
     * 二氧化碳 carbon dioxide
     */
    @ApiModelProperty("二氧化碳")
    private String carbonDioxide="";

    /**
     * 当日收益
     */
    @ApiModelProperty("当日收益")
    private String todayEarning="";

    /**
     * 累计收益
     */
    @ApiModelProperty("累计收益")
    private String totalEarning="";

    /**
     * 当日告警次数
     */
    @ApiModelProperty("当日告警次数")
    private String todayAlarmNum="";
    /**
     * 峰值功率
     */
    @ApiModelProperty("峰值功率")
    private String peakPower="";
    /**
     * 在线逆变器数量
     */
    @ApiModelProperty("在线逆变器数量")
    private String onlineInverterStats="";

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private String createTime="";

    /**
     * 质保时间
     */
    @ApiModelProperty("质保时间")
    private String warrantyTime="";
    /**
     * 用户名称
     */
    @ApiModelProperty("用户名称")
    private String userName="";
    /**
     * 用户电话
     */
    @ApiModelProperty("用户电话")
    private String userPhone="";

    @ApiModelProperty("日等效小时")
    @ExcelProperty("日等效小时")
    private String dailyEfficiencyPerHour="";

    @ApiModelProperty("年等效小时")
    @ExcelProperty("年等效小时")
    private String yearlyEfficiencyPerHour="";

    @ApiModelProperty("创建开始时间")
    private String createTimeStart="";

    @ApiModelProperty("创建结束时间")
    @ExcelIgnore
    private String createTimeEnd="";

    @ApiModelProperty("装机容量最小值")
    private String minPlantCapacity="";

    @ApiModelProperty("装机容量最大值")
    private String maxPlantCapacity="";




}
