package com.bto.commons.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 角色信息
 * <AUTHOR>
 * @date 2023/5/18 17:36
 */
@Data
@ApiModel("角色信息")
public class RoleInfoVO implements Serializable {
    private static final long serialVersionUID = 5697630141909720138L;
    @ApiModelProperty("角色ID")
    private String roleID;
    @ApiModelProperty("角色名称")
    private String roleName;
    @ApiModelProperty("备注")
    private String roleRemark;
    @ApiModelProperty("创建者")
    private String creator;
    @ApiModelProperty("创建时间")
    private String createTime;
    @ApiModelProperty("菜单数量")
    private String menuCount;
    @ApiModelProperty("角色菜单")
    private List<String> menuList;

}
