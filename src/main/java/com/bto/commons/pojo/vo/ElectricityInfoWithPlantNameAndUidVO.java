package com.bto.commons.pojo.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/6/30 9:36
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ElectricityInfoWithPlantNameAndUidVO implements Serializable {
    private static final long serialVersionUID = 1831753051046410533L;
    @ApiModelProperty("电站编号")
    @ExcelProperty("电站编号")
    private String plantUid;
    @ApiModelProperty("电站名称")
    @ExcelProperty("电站名称")
    private String plantName;

    @ApiModelProperty("发电量")
    @ExcelProperty("发电量")
    private String electricity;
    @ApiModelProperty("装机容量")
    @ExcelProperty("装机容量")
    private String plantCapacity;
    @ApiModelProperty("数据时间")
    @ExcelProperty("数据时间")
    private String dataTime;
    @ApiModelProperty("发电效率")
    @ExcelProperty("发电效率")
    private String electricityEfficiency;
    @ApiModelProperty("电站电价")
    @ExcelProperty("电站电价")
    private String plantPrice;
    @ApiModelProperty("收益")
    @ExcelProperty("收益")
    private String income;
    @ApiModelProperty("等效利用小时")
    @ExcelProperty("等效利用小时")
    private String efficiencyPerHours;

    @ApiModelProperty("同比:on year-on-year basis")
    @ExcelProperty("同比")
    private String yoyRatio;
    @ApiModelProperty("环比: month-on-month ratio")
    @ExcelProperty("环比")
    private String momRatio;
    {
        this.electricity = "0";
        this.plantPrice = "0";
        this.dataTime = "-";
        this.electricityEfficiency = "-";
        this.yoyRatio = "-";
        this.momRatio = "-";
    }

}
