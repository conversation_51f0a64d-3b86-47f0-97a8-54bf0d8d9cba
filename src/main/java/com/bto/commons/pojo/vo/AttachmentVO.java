package com.bto.commons.pojo.vo;

import com.bto.commons.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/11/28 16:34
 */
@Data
@ApiModel(value = "附件管理")
public class AttachmentVO implements Serializable {

    private static final long serialVersionUID = -1914591769124019263L;
    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "附件名称")
    private String name;

    @ApiModelProperty(value = "附件地址")
    private String url;

    @ApiModelProperty(value = "附件大小")
    private Long size;

    @ApiModelProperty(value = "存储平台")
    private String platform;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date createTime;
}
