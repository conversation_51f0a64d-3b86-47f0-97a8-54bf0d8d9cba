package com.bto.commons.pojo.vo;

import com.bto.commons.enums.ManufacturerEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/7/8 16:21
 */
@Data
@ApiModel("逆变器详细信息数据")
public class InverterDetailsVO implements Serializable {
    private static final long serialVersionUID = 9017473306452363033L;
    @ApiModelProperty("逆变器SN")
    private String inverterSN;
    @ApiModelProperty("所属电站名称")
    private String plantName;
    @ApiModelProperty("生产厂商")
    private String manufacturer ;
    @ApiModelProperty("逆变器型号")
    private String module ;
    @ApiModelProperty("软件版本")
    private String softwareVersion ;
    @ApiModelProperty("显示版本")
    private String displayVersion ;
    @ApiModelProperty("控制版本")
    private String controlVersion ;
    @ApiModelProperty("创建时间")
    private String createTime ;
    @ApiModelProperty("设备地址")
    private String address ;
    @ApiModelProperty("逆变器状态")
    private String inverterStatus;
    @ApiModelProperty("实时功率")
    private String power;
    @ApiModelProperty("信号强度")
    private String signalStrength ;
    @ApiModelProperty("日发电量")
    private String todayElectricity ;
    @ApiModelProperty("月发电量")
    private String monthElectricity ;
    @ApiModelProperty("年发电量")
    private String yearElectricity ;
    @ApiModelProperty("总发电量")
    private String totalElectricity ;
    @ApiModelProperty("额定容量")
    private Integer deviceCapacity ;

    @ApiModelProperty("逆变器告警（未处理）数")
    private Integer alarmCount; ;
    @ApiModelProperty("更新时间")
    private String updateTime;
    {
        this.inverterSN = "-";
        this.manufacturer = "-";
        this.module = "-";
        this.softwareVersion = "-";
        this.displayVersion = "-";
        this.controlVersion = "-";
        this.createTime = "-";
        this.address = "-";
        this.inverterStatus = "-";
        this.power = "0";
        this.signalStrength = "0";
        this.todayElectricity = "0.00";
        this.monthElectricity = "0.00";
        this.yearElectricity = "0.00";
        this.totalElectricity = "0.00";
        this.updateTime = "-";
        this.deviceCapacity = 0;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = ManufacturerEnum.getNameByCode(manufacturer);
    }
}
