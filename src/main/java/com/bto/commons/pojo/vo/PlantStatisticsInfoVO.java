package com.bto.commons.pojo.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.bto.commons.enums.PlantStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "PlantStatisticsInfo", description = "电站统计信息实体类")
@HeadRowHeight(50)
@ContentRowHeight(30)
@ColumnWidth(100)
public class PlantStatisticsInfoVO implements Serializable {
    private static final long serialVersionUID = 4572002318739828157L;
    @ApiModelProperty(value = "电厂(交易对象)编号", notes = "")
    @ExcelProperty({"电站统计","电厂(交易对象)编号"})
    private String meterID;
    @ApiModelProperty("电站Uid")
    @ExcelProperty("电站Uid")
    private String plantUid;
    @ApiModelProperty(value = "电站名称")
    @ExcelProperty("电站名称")
    private String plantName;
    @ApiModelProperty(value = "电站所属用户")
    @ExcelProperty(value = "电站所属用户")
    private String userName;
    @ApiModelProperty(value = "装机容量")
    @ExcelProperty(value = "装机容量")
    private String plantCapacity;
    @ApiModelProperty(value = "总发电量")
    @ExcelProperty("总发电量")
    private String totalElectricity;
    @ApiModelProperty(value = "电站地址")
    @ExcelProperty("电站地址")
    private String address;
    @ApiModelProperty(value = "电站状态")
    @ExcelProperty("电站状态")
    private String status;
    @ApiModelProperty(value = "建站时间")
    @ExcelProperty("建站时间")
    private String createTime;
    @ApiModelProperty(value = "数据更新时间")
    @ExcelProperty("数据更新时间")
    private String updateTime;
    @ApiModelProperty(value = "逆变器数量")
    @ExcelProperty("逆变器数量")
    private String inverterNum;
    @ApiModelProperty(value = "发电量")
    @ExcelProperty("发电量")
    private String electricity;
    @ApiModelProperty(value = "告警数量")
    @ExcelProperty("告警数量")
    private String alarmNum;
    @ApiModelProperty(value = "等效利用小时", notes = "(需自己计算)")
    @ExcelProperty("等效利用小时")
    private String equivalentUseHour;
    @ApiModelProperty(value = "电价")
    @ExcelProperty("电价")
    private String electricityPrice;
    @ApiModelProperty(value = "收入", notes = "(需自己计算)")
    @ExcelProperty("收入")
    private String income;
    @ApiModelProperty(value = "二氧化碳减排 (吨)", notes = "(需自己计算)")
    @ExcelProperty("二氧化碳减排")
    private String totalReduceCo2;
    @ApiModelProperty(value = "减少砍伐树木 (棵)", notes = "(需自己计算)")
    @ExcelProperty("减少砍伐树木")
    private String totalPlantTreeNum;
    @ApiModelProperty(value = "连续离线时长", notes = "(需自己计算)")
    @ExcelProperty("连续离线时长")
    private String offlineTime;
    @ApiModelProperty(value = "0：并网，1：储能，2：混合，3：交流耦合")
    @ExcelProperty("电站类型")
    private String plantType;
    @ExcelProperty("配电箱状态")
    @ApiModelProperty(value = "配电箱状态（0：正常 1：  -1:未初始化）")
    private String powerDistributor;
    @ApiModelProperty(value = "项目分类id(1:户用，2:整县-河源。。。。)")
    @ExcelProperty("项目类型")
    private String projectName;
    {
        this.meterID = "";
        this.plantUid = "";
        this.plantName = "";
        this.userName = "";
        this.totalElectricity = "";
        this.status = "";
        this.plantCapacity = "";
        this.address = "";
        this.createTime = "";
        this.updateTime = "";
        this.electricity = "";
        this.inverterNum = "";
        this.alarmNum = "0";
        this.equivalentUseHour = "";
        this.electricityPrice = "0.45";
        this.income = "";
        this.totalReduceCo2 = "";
        this.totalPlantTreeNum = "";
        this.offlineTime  = "0小时";
        this.plantType = "";
        this.powerDistributor = "";
        this.projectName = "";
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = PlantStatusEnum.getNameByCode(status);
    }

    public String getTotalReduceCo2() {
        return totalReduceCo2;
    }

    /**
     * 累计二氧化碳减排 = ((electricity/100) * 0.997 ) / 10 00
     *
     * @param totalReduceCo2
     */
    public void setTotalReduceCo2(String totalReduceCo2) {
        BigDecimal b1 = new BigDecimal(totalReduceCo2).divide(BigDecimal.valueOf(100), BigDecimal.ROUND_HALF_UP);
        BigDecimal b2 = b1.multiply(BigDecimal.valueOf(0.997)).divide(BigDecimal.valueOf(1000)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.totalReduceCo2 = b2.toString();
    }

    public String getTotalPlantTreeNum() {
        return totalPlantTreeNum;
    }

    /**
     * 等效植树 = (electricity/100*0.832)/1800 取 整 数
     *
     * @param totalPlantTreeNum
     */
    public void setTotalPlantTreeNum(String totalPlantTreeNum) {
        BigDecimal b1 = new BigDecimal(totalPlantTreeNum).divide(BigDecimal.valueOf(100), RoundingMode.HALF_UP);
        BigDecimal b2 = b1.multiply(BigDecimal.valueOf(0.832)).divide(BigDecimal.valueOf(1800), RoundingMode.HALF_UP);
        this.totalPlantTreeNum = b2.toString();
    }

    public String getPlantCapacity() {
        return plantCapacity;
    }

    public void setPlantCapacity(String plantCapacity) {
        BigDecimal b1 = new BigDecimal(plantCapacity);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(1000)).setScale(3, BigDecimal.ROUND_HALF_UP);
        this.plantCapacity = b2.toString();
    }

    public String getTotalElectricity() {
        return totalElectricity;
    }

    public void setTotalElectricity(String totalElectricity) {
        BigDecimal b1 = new BigDecimal(totalElectricity);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.totalElectricity = b2.toString();
    }

    public String getElectricity() {
        return electricity;
    }

    public void setElectricity(String electricity) {
        BigDecimal b1 = new BigDecimal(electricity);
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.electricity = b2.toString();
    }

}
