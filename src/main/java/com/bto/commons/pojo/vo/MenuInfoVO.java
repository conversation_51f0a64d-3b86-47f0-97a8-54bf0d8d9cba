package com.bto.commons.pojo.vo;

import com.bto.commons.utils.TreeNode;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/5/18 18:20
 */
@Data
public class MenuInfoVO extends TreeNode<MenuInfoVO> implements Serializable {
    private static final long serialVersionUID = 4965363541002344445L;
    @ApiModelProperty("菜单ID")
    private String id;
    @ApiModelProperty("父级ID")
    private String pid;
    @ApiModelProperty("菜单名称")
    private String label;
    @ApiModelProperty("菜单地址")
    private String path;
    @ApiModelProperty("菜单权限")
    private String auth;
    @ApiModelProperty("菜单类型")
    private String type;
    @ApiModelProperty("打开方式")
    private String openStyle;
    @ApiModelProperty("菜单图标")
    private String icon;
    @ApiModelProperty("创建者")
    private String creator;
    @ApiModelProperty("创建时间")
    private String createTime;
    @ApiModelProperty("排序")
    private String sort;
    @ApiModelProperty("菜单版本")
    private String version;


    @Range(min = 0, max = 1)
    private Integer hidden;
    private String title;

    @Range(min = 0, max = 1)
    private Integer breadcrumb;

    @Range(min = 0, max = 1)
    private Integer affix;

    @Range(min = 0, max = 1)
    private Integer alwaysShow;

    private String activeMenu;

    @Range(min = 0, max = 1)
    private Integer keepAlive;


    @ApiModelProperty("组件")
    private String component;

    private String redirect;
}
