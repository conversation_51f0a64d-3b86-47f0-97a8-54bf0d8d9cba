package com.bto.commons.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.Date;

/**
 * 用户基本表-(BtoUser)实体类
 *
 * <AUTHOR>
 * @date 2023/3/28 10:39
 */
@TableName("bto_user")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class User implements Serializable {

    private static final long serialVersionUID = 5072292930297157235L;

    /**
     * 主键
     * 用户Uid
     */
    @TableId("user_uid")
    @NotBlank(message = "[用户Uid]不能为空")
    @ApiModelProperty("用户Uid")
    private String userUid;

    @TableField("user_type")
    @NotBlank(message = "[用户类型]不能为空")
    @ApiModelProperty("用户类型")
    private String userType;
    /**
     * 用户名
     */
    @TableField("user_name")
    @NotBlank(message = "[用户名称]不能为空")
    @ApiModelProperty("用户名称")
    private String userName;
    /**
     * 手机号码
     */
    @TableField("user_phone")
    @Pattern(regexp = "^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\\d{8}$",message = "手机号格式异常")
    @ApiModelProperty("用户手机号")
    private String userPhone;
    /**
     * 用户邮箱
     */
    @TableField("user_email")
    @Pattern(regexp = "^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$",message = "邮箱号格式异常")
    @ApiModelProperty("用户邮箱号")
    private String userEmail;
    /**
     * 用户密码
     */
    @TableField("user_password")
    @Pattern(regexp = "^(?=.*\\d)(?=.*[a-zA-Z])(?=.*[^\\da-zA-Z\\s]).{8,16}$",message ="密码格式必须包含数字、字母以及特殊字符(英文字符问号、英文字符等号、英文字符小数点),并且字符串位数8到16位" )
    @ApiModelProperty("用户密码")
    private String userPassword;
    /**
     * 用户账号状态(0:正常，1：异常)
     */
    @TableField("user_status")
    @NotBlank(message = "[用户账号状态]不能为空")
    @Pattern(regexp = "[01]{1}")
    @ApiModelProperty("用户账号状态")
    private Integer userStatus;
    /**
     * 合同id
     */
    @TableField("contract_id")
    @NotBlank(message = "[合同id]不能为空")
    @ApiModelProperty("合同id")
    private String contractID;
    /**
     * 项目id
     */
    @TableField("project_special")
    @NotBlank(message = "[项目id]不能为空")
    @ApiModelProperty("项目id")
    private Integer projectID;
    /**
     * 角色id
     */
    @TableField("role_id")
    @NotBlank(message = "[用户角色id]不能为空")
    @ApiModelProperty("用户角色id")
    private String roleID;
    @TableField("avatar")
    @ApiModelProperty("用户头像")
    private String userAvatar;
    /**
     * 创建者
     */
    @TableField("creator")
    @NotBlank(message = "[用户创建者]不能为空")
    @ApiModelProperty("用户创建者")
    private String creator;
    /**
     * 创建时间
     */
    @TableField("create_time")
    @NotBlank(message = "[用户创建时间]不能为空")
    @ApiModelProperty("用户创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField("update_time")
    @NotBlank(message = "[用户更新时间]不能为空")
    @ApiModelProperty("用户更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;
    /**
     * 更新者
     */
    @TableField("updater")
    @NotBlank(message = "[用户更新者]不能为空")
    @ApiModelProperty("用户更新者")
    private String updater;
    /**
     * 逻辑删除
     */
    @TableField("is_deleted")
    @NotBlank(message = "[用户删除]不能为空")
    @ApiModelProperty("逻辑删除")
    @TableLogic
    private Integer isDeleted;

    @ApiModelProperty("布局")
    @TableField("layout")
    private String layout;
//    /***
//     * 预留字段1
//     */
//    private String res1;
//    /***
//     * 预留字段2
//     */
//    private String res2;
//    /***
//     * 预留字段3
//     */
//    private String res3;


    public String getUserName() {
        if(this.userName==null||"".equals(this.userName)){
            return "用户名称异常";
        }
        return userName;
    }

    public String getUserPhone() {
        if(this.userPhone==null||"".equals(this.userPhone)){
            return "用户电话异常";
        }
        return userPhone;
    }
}

