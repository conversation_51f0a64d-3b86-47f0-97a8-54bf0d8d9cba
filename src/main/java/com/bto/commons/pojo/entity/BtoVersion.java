package com.bto.commons.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 版本信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-11-20
 */

@Data
@TableName("bto_version")
public class BtoVersion {
	/**
	* 序号
	*/
	@TableId(type = IdType.AUTO)
	private Long id;

	/**
	* 版本号
	*/
	private String versionNumber;

	/**
	* 更新内容
	*/
	private String versionInfo;

	/**
	* 版本时间
	*/
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date updateTime;

}