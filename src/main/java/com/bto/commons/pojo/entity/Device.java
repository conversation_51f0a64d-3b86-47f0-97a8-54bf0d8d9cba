package com.bto.commons.pojo.entity;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 设备表,creater:matt
 *
 * <AUTHOR>
 * @TableName bto_device
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("bto_device")
public class Device implements Serializable {

    private static final long serialVersionUID = 1539187608006664380L;
    /**
     * 电站Uid
     */
    @TableField("plant_uid")
    @NotBlank(message = "[电站Uid]不能为空")
    @Size(max = 125, message = "编码长度不能超过125")
    @ApiModelProperty("电站Uid")
    @Length(max = 125, message = "编码长度不能超过125")
    private String plantUid;
    /**
     * 设备编号（设备SN码）||逆变器和非三晶运维器
     */
    @TableField("device_id")
    @NotBlank(message = "[设备编号（设备SN码）||逆变器和非三晶运维器]不能为空")
    @Size(max = 70, message = "编码长度不能超过70")
    @ApiModelProperty("设备编号（设备SN码）||逆变器和非三晶运维器")
    @Length(max = 70, message = "编码长度不能超过70")
    private String deviceId;
    /**
     * 运维器通讯模块imei || 等同于三晶 wisdom_device_sn
     */
    @TableField("imei")
    @NotBlank(message = "[运维器通讯模块imei || 等同于三晶 wisdom_device_sn]不能为空")
    @Size(max = 50, message = "编码长度不能超过50")
    @ApiModelProperty("运维器通讯模块imei || 等同于三晶 wisdom_device_sn")
    @Length(max = 50, message = "编码长度不能超过50")
    private String imei;
    /**
     * 设备类型(1:逆变器、2:运维器)
     */
    @TableField("device_type")
    @NotNull(message = "[设备类型(1:逆变器、2:运维器、3:电表,4：气象站,10、配电房,11、配电柜，12、温湿度、烟感采集器)]不能为空")
    @ApiModelProperty("1:逆变器、2:运维器、3:电表,4：气象站,10、配电房,11、配电柜，12、温湿度、烟感采集器")
    private Integer deviceType;
    /**
     * 厂家
     */
    @TableField("manufacturer")
    @NotBlank(message = "[厂家]不能为空")
    @Size(max = 50, message = "编码长度不能超过50")
    @ApiModelProperty("厂家")
    @Length(max = 50, message = "编码长度不能超过50")
    private String manufacturer;

    /**
     * 型号
     */
    @TableField("module")
    @NotBlank(message = "[型号]不能为空")
    @Size(max = 50, message = "编码长度不能超过50")
    @ApiModelProperty("型号")
    @Length(max = 50, message = "编码长度不能超过50")
    private String module;
    /**
     * 项目专项(1:户用，2：整县-河源)
     */
    @TableField("project_special")
    @NotNull(message = "[项目专项(1:户用，2：整县-河源)]不能为空")
    @ApiModelProperty("项目专项(1:户用，2：整县-河源)")
    private Integer projectSpecial;
    /**
     * 设备地址
     */
    @TableField("device_address")
    @NotNull(message = "[设备地址]不能为空")
    @ApiModelProperty("设备地址")
    private String deviceAddress;
    /**
     * 设备pc码
     */
    @TableField("device_pc")
    @NotNull(message = "[设备pc码]不能为空")
    @ApiModelProperty("设备pc码，所有设备都有pc码")
    private String devicePc;
    /**
     * cimi（三晶逆变器也存在iotnum）
     */
    @TableField("cimi")
    @NotNull(message = "[cimi（三晶逆变器也存在iotnum）]不能为空")
    @ApiModelProperty("cimi（三晶逆变器也存在iotnum）")
    private String cimi;
    /**
     * ICCID (物联网卡号)
     */
    @TableField("iccid")
    @ApiModelProperty("ICCID (物联网卡号)")
    private String iccid;
    /**
     * 设备状态（0：离线，1：正常运行，2：告警运行,3:自检提示 4 为未初始化)
     */
    @TableField("status")
    @ApiModelProperty("设备状态（0：离线，1：正常运行，2：告警运行,3:自检提示 4 为未初始化)")
    private String status;
    /**
     * 设备开关机状态（0：关机，1：启动）
     */
    @TableField("enable")
    @ApiModelProperty("设备开关机状态（0：关机，1：启动）")
    private String enable;
    /**
     * 运维器是否采集配电箱数据（1：是 ；0：否）
     */
    @TableField("cluster")
    @ApiModelProperty("运维器是否采集配电箱数据（1：是 ；0：否）")
    private String cluster;
    /**
     * 激活时间
     */
    @TableField("start_time")
    @ApiModelProperty("激活时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String startTime;
    /**
     * 到期时间/质保时间
     */
    @TableField("end_time")
    @ApiModelProperty("到期时间/质保时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String endTime;
    /**
     * 软件版本号
     */
    @TableField("software_version")
    @NotNull(message = "[软件版本号]不能为空")
    @ApiModelProperty("软件版本号")
    private String softwareVersion;
    /**
     * 显示版本号
     */
    @TableField("display_version")
    @NotNull(message = "[显示版本号]不能为空")
    @ApiModelProperty("显示版本号")
    private String displayVersion;
    /**
     * 控制版本号
     */
    @TableField("control_version")
    @NotNull(message = "[控制版本号]不能为空")
    @ApiModelProperty("控制版本号")
    private String controlVersion;
    /**
     * 状态（0：存在，1：删除）
     */
    @TableField("is_deleted")
    @NotNull(message = "[状态（0：存在，1：删除）]不能为空")
    @ApiModelProperty("状态（0：存在，1：删除）")
    private Integer isDeleted;
    /**
     * 工程师id(创建者)
     */
    @TableField("creator")
    @Size(max = 70, message = "编码长度不能超过70")
    @ApiModelProperty("工程师id(创建者)")
    @Length(max = 70, message = "编码长度不能超过70")
    private String creator;
    /**
     * 数据创建时间
     */
    @TableField("create_time")
    @ApiModelProperty("数据创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String createTime;
    /**
     * 数据更新时间
     */
    @TableField("update_time")
    @ApiModelProperty("数据更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String updateTime;
    /**
     * 逆变器接入类型（0：并网，1：储能）
     */
    @TableField("receive_type")
    @NotBlank(message = "[逆变器接入类型（0：并网，1：储能）]不能为空")
    @Size(max = 10, message = "编码长度不能超过10")
    @ApiModelProperty("逆变器接入类型（0：并网，1：储能）")
    @Length(max = 10, message = "编码长度不能超过10")
    private String receiveType;
    @ApiModelProperty(value = "设备名称（目前电表独有：如总配电、宿舍楼、办公室、XX厂区（用于区分电表监控区域））")
    private String deviceName;

    /**
     * 电流互感器变比(电表独有)
     */
    @ApiModelProperty("电流互感器变比(电表独有)")
    private Integer currentTransformer;
    /**
     * 电压互感器变比(电表独有)
     */
    @ApiModelProperty("电压互感器变比(电表独有)")
    private Integer potentialTransformer;

    {
        this.deviceAddress = "";
        this.devicePc = "";
        this.cluster = "";
        this.createTime = DateUtil.now();
        this.receiveType = "";
        this.deviceName = "";
        this.currentTransformer = null;
        this.potentialTransformer = null;
    }
}
