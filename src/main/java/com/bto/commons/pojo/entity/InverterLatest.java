package com.bto.commons.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 逆变器最新数据
 *
 * <AUTHOR>
 * @TableName bto_inverter_latest
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("bto_inverter_latest")
public class InverterLatest implements Serializable {

    /**
     * 电站Uid
     */
    @TableField("plant_uid")
    @NotBlank(message = "[电站Uid]不能为空")
    @Size(max = 125, message = "编码长度不能超过125")
    @ApiModelProperty("电站Uid")
    @Length(max = 125, message = "编码长度不能超过125")
    private String plantUid;
    /**
     * 逆变器sn
     */
    @TableId("inverter_sn")
    @NotBlank(message = "[逆变器sn]不能为空")
    @Size(max = 50, message = "编码长度不能超过50")
    @ApiModelProperty("逆变器sn")
    @Length(max = 50, message = "编码长度不能超过50")
    private String inverterSn;
    /**
     * 状态（0：离线，1：正常运行，2：告警运行,3:自检提示）
     */
    @TableField("inverter_status")
    @NotNull(message = "[状态（0：离线，1：正常运行，2：告警运行,3:自检提示）]不能为空")
    @ApiModelProperty("状态（0：离线，1：正常运行，2：告警运行,3:自检提示）")
    private String inverterStatus;
    /**
     * 功率
     */
    @TableField("power")
    @NotNull(message = "[功率]不能为空")
    @ApiModelProperty("功率")
    private String power;
    /**
     * 日发电量(kWh)*100
     */
    @TableField("today_electricity")
    @NotNull(message = "[日发电量(kWh)*100]不能为空")
    @ApiModelProperty("日发电量(kWh)*100")
    private String todayElectricity;
    /**
     * 月发电量(kWh)*100
     */
    @TableField("month_electricity")
    @NotNull(message = "[月发电量(kWh)*100]不能为空")
    @ApiModelProperty("月发电量(kWh)*100")
    private String monthElectricity;
    /**
     * 年发电量(kWh)*100
     */
    @TableField("year_electricity")
    @NotNull(message = "[年发电量(kWh)*100]不能为空")
    @ApiModelProperty("年发电量(kWh)*100")
    private String yearElectricity;
    /**
     * 总发电量(kWh)*100
     */
    @TableField("total_electricity")
    @NotNull(message = "[总发电量(kWh)*100]不能为空")
    @ApiModelProperty("总发电量(kWh)*100")
    private String totalElectricity;
    /**
     * 信号强度
     */
    @TableField("signal_strength")
    @NotNull(message = "[信号强度]不能为空")
    @ApiModelProperty("信号强度")
    private String signalStrength;
    /**
     * 项目专项（1：户用。2：整县-河源，3：）
     */
    @TableField("project_special")
    @NotNull(message = "[项目专项（1：户用。2：整县-河源，3：）]不能为空")
    @ApiModelProperty("项目专项（1：户用。2：整县-河源，3：）")
    private String projectSpecial;
    /**
     * 创建时间
     */
    @TableField("create_time")
    @NotNull(message = "[创建时间]不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @ApiModelProperty("创建时间")
    private String createTime;
    /**
     * 三晶数据标识（三晶：0  ，自取：1）
     */
    @TableField("state")
    @NotBlank(message = "[三晶数据标识（三晶：0  ，自取：1）]不能为空")
    @Size(max = 0, message = "编码长度不能超过0")
    @ApiModelProperty("三晶数据标识（三晶：0  ，自取：1）")
    @Length(max = 0, message = "编码长度不能超过0")
    private String state;
    /**
     * 数据更新时间
     */
    @TableField("update_time")
    @NotNull(message = "[数据更新时间]不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @ApiModelProperty("数据更新时间")
    private String updateTime;

    @ApiModelProperty("设备imei码")
    @TableField("device_imei")
    private String deviceImei;

    @ApiModelProperty("逻辑删除字段")
    @TableField("is_deleted")
    private String isDeleted;


}
