package com.bto.commons.pojo.entity;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.github.jeffreyning.mybatisplus.anno.MppMultiId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/5/20 15:25
 */
@Data
@TableName("bto_role_has_menu")
public class RoleHasMenu implements Serializable {
    private static final long serialVersionUID = -9220846707917329729L;
    /**
     * 角色ID
     */
    @MppMultiId
    @TableField(value = "role_id")
    private Long roleId;
    /**
     * 菜单ID
     */
    @MppMultiId
    @TableField(value = "menu_id")
    private Long menUid;
    /**
     * 菜单ID
     */
    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

    @TableField("is_deleted")
    private Integer isDeleted;
}
