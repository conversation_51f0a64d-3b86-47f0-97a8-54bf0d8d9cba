package com.bto.commons.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
* 运维器数据
* @TableName bto_edge_server_realtime
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("bto_edge_server_realtime")
public class EdgeServerRealtime implements Serializable {

    /**
    * 电站Uid
    */
    @NotBlank(message="[电站Uid]不能为空")
    @Size(max= 125,message="编码长度不能超过125")
    @ApiModelProperty("电站Uid")
    @Length(max= 125,message="编码长度不能超过125")
    private String plantUid;
    /**
    * 数据日期
    */
    @NotNull(message="[数据日期]不能为空")
    @ApiModelProperty("数据日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private String collectDate;
    /**
    * 运维器IMEI
    */
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("运维器IMEI")
    @Length(max= 50,message="编码长度不能超过50")
    private String imei;
    /**
    * 光伏板发电,PV发电,当日发电量(以最小单位存储,不保留小数后两位)
    */
    @NotNull(message="[光伏板发电,PV发电,当日发电量(以最小单位存储,不保留小数后两位)]不能为空")
    @ApiModelProperty("光伏板发电,PV发电,当日发电量(以最小单位存储,不保留小数后两位)")
    private String generationElectricity;
    /**
    * 负载用电,当日用电量
    */
    @NotNull(message="[负载用电,当日用电量]不能为空")
    @ApiModelProperty("负载用电,当日用电量)")
    private String useElectricity;
    /**
    * 自发自用
    */
    @NotNull(message="[自发自用]不能为空")
    @ApiModelProperty("自发自用")
    private String deprecatedGenerateElectricity;
    /**
    * 买第三方电量(以最小单位存储,不保留小数后两位)
    */
    @NotNull(message="[买第三方电量]不能为空")
    @ApiModelProperty("买第三方电量")
    private Integer buyElectricity;
    /**
    * 卖自发电量(以最小单位存储,不保留小数后两位)
    */
    @NotNull(message="[卖自发电量]不能为空")
    @ApiModelProperty("卖自发电量")
    private String sellElectricity;
    /**
    * A点电压
    */
    @NotNull(message="[A点电压]不能为空")
    @ApiModelProperty("A点电压")
    private Integer apv;
    /**
    * B点电压
    */
    @NotNull(message="[B点电压]不能为空")
    @ApiModelProperty("B点电压")
    private String bpv;
    /**
    * C点电压
    */
    @NotNull(message="[C点电压]不能为空")
    @ApiModelProperty("C点电压")
    private String cpv;
    /**
    * D点电压
    */
    @NotNull(message="[D点电压]不能为空")
    @ApiModelProperty("D点电压")
    private String dpv;
    /**
    * （当前）正向有功总电能（impep）
    */
    @NotNull(message="[（当前）正向有功总电能（impep）]不能为空")
    @ApiModelProperty("（当前）正向有功总电能（impep）")
    private String impep;
    /**
    * （当前）反向有功总电能（expep）
    */
    @NotNull(message="[（当前）反向有功总电能（expep）]不能为空")
    @ApiModelProperty("（当前）反向有功总电能（expep）")
    private String expep;
    /**
    * 三晶数据标识（三晶：0  ，自取：1）
    */
    @NotBlank(message="[三晶数据标识（三晶：0  ，自取：1）]不能为空")
    @Size(max= 0,message="编码长度不能超过0")
    @ApiModelProperty("三晶数据标识（三晶：0  ，自取：1）")
    @Length(max= 0,message="编码长度不能超过0")
    private String state;
    /**
    * 创建时间
    */
    @NotNull(message="[创建时间]不能为空")
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private String createTime;
    /**
    * 更新时间
    */
    @NotNull(message="[更新时间]不能为空")
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private String updateTime;

}
