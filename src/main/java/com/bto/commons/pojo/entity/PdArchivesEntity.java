package com.bto.commons.pojo.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import java.util.Date;

/**
 * 配电室档案
 *
 * <AUTHOR> 
 * @since  2024-06-03
 */

@Data
@TableName("pd_archives")
public class PdArchivesEntity {
	/**
	* 配电室id
	*/
	@TableId
	private String pdId;

	/**
	* 配电室名称
	*/
	private String pdName;

	/**
	* 配电室图片地址
	*/
	private String pdPhotoUrl;

	/**
	* 配电室类型
	*/
	private String pdType;

	/**
	* 配电室型号
	*/
	private String pdModule;

	/**
	* 配电室地址
	*/
	private String pdAddress;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;

}