package com.bto.commons.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("bto_user_read_notices")
public class UserReadNotices implements Serializable {

    private static final long serialVersionUID = -4278120399841207722L;

    @TableField("id")
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    @TableField("user_uid")
    private String userUid;

    @TableField("notices_id")
    private String noticesId;

    public UserReadNotices(String noticesId,String userUid) {
        this.userUid = userUid;
        this.noticesId = noticesId;
    }
}
