package com.bto.commons.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 *
* 逆变器故障信息
* @TableName bto_inverter_alarm
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("bto_inverter_alarm")
@ApiModel("逆变器告警类")
public class InverterAlarm implements Serializable {
    private static final long serialVersionUID = 1603190177828713878L;
    /**
    * 主键id
    */
    @NotNull(message="[主键id]不能为空")
    @ApiModelProperty("主键id")
    private Long id;
    /**
    * 逆变器SN
    */
    @NotBlank(message="[逆变器SN]不能为空")
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("逆变器SN")
    @Length(max= 50,message="编码长度不能超过50")
    private String inverterSn;
    /**
    * 报警码
    */
    @NotBlank(message="[报警码]不能为空")
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("报警码")
    @Length(max= 50,message="编码长度不能超过50")
    private String alarmCode;
    /**
    * 报警信息
    */
    @NotBlank(message="[报警信息]不能为空")
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("报警信息")
    @Length(max= 50,message="编码长度不能超过50")
    private String alarmInfo;
    /**
    * 开始时间
    */
    @NotNull(message="[开始时间]不能为空")
    @ApiModelProperty("开始时间")
    private Date startTime;
    /**
    * 结束时间
    */
    @ApiModelProperty("结束时间")
    private Date endTime;
    /**
    * 状态（0未处理，1已处理 , 2失效）
    */
    @NotNull(message="[状态（0未处理，1已处理 , 2失效）]不能为空")
    @ApiModelProperty("状态（0未处理，1已处理 , 2失效）")
    private Integer status;
    /**
    * 三晶数据标识（三晶：0  ，自取：1）
    */
    @NotBlank(message="[三晶数据标识（三晶：0  ，自取：1）]不能为空")
    @Size(max= 10,message="编码长度不能超过10")
    @ApiModelProperty("三晶数据标识（三晶：0  ，自取：1）")
    @Length(max= 10,message="编码长度不能超过10")
    private String state;
    /**
    * 创建时间
    */
    @NotNull(message="[创建时间]不能为空")
    @ApiModelProperty("创建时间")
    private Date createTime;
    /**
    * 更新时间
    */
    @NotNull(message="[更新时间]不能为空")
    @ApiModelProperty("更新时间")
    private Date updateTime;
    /**
    * 数据日期
    */
    @ApiModelProperty("数据日期")
    @TableField("alarm_date")
    private Date alarmDate;

}
