package com.bto.commons.pojo.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 传感器数据
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-06-17
 */

@Data
@TableName("sensor_data")
public class SensorDataEntity {
	/**
	* 电站id
	*/
	private String plantUid;

	/**
	* 传感器ID
	*/
	@TableId
	private String sensorId;

	/**
	* 烟雾浓度(PPM)
	*/
	private Integer smokeConcentr;

	/**
	* 温度(℃)
	*/
	private BigDecimal temp;

	/**
	* 湿度（%RH）
	*/
	private BigDecimal humidity;

	/**
	* 烟雾报警器状态(0:未告警，1：已告警)
	*/
	private Integer alarmStatus;

	/**
	* 创建时间
	*/

	@TableField(fill = FieldFill.INSERT)
	private Date createTime;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;

}