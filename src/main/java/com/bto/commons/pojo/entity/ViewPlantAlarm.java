package com.bto.commons.pojo.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bto.commons.enums.AlarmStatus;
import com.bto.commons.enums.DeviceType;
import com.bto.commons.enums.ProjectTypeEnum;
import com.bto.commons.enums.AlarmSource;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
* 
* <AUTHOR>
 * @TableName v_plant_alarm
*/
@TableName("v_plant_alarm")
@Data
@ApiModel("电站告警信息")
public class ViewPlantAlarm implements Serializable {

    private static final long serialVersionUID = -5770674333076505018L;

    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    @ApiModelProperty("告警id")
    @ExcelProperty("告警id")
    private String alarmId;
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    @ApiModelProperty("电站编号")
    @ExcelProperty("电站编号")
    private String plantUid;
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    @ApiModelProperty("电站名称")
    @ExcelProperty("电站名称")
    private String plantName;

    @NotBlank(message="[]不能为空")
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("设备编号")
    @Length(max= 50,message="编码长度不能超过50")
    @ExcelProperty("设备编号")
    private String deviceId;

    @NotBlank(message="[]不能为空")
    @Size(max= 3,message="编码长度不能超过3")
    @ApiModelProperty("设备类型: 1:逆变器 2:运维器")
    @Length(max= 3,message="编码长度不能超过3")
    @ExcelProperty("设备类型")
    private String deviceType;

    @NotBlank(message="[]不能为空")
    @Size(max= 3,message="编码长度不能超过3")
    @ApiModelProperty("数据来源：0:三晶 1:博通")
    @Length(max= 3,message="编码长度不能超过3")
    @ExcelProperty("数据来源")
    private String source;

    @NotNull(message="[]不能为空")
    @ApiModelProperty("告警开始时间")
    @ExcelProperty("告警开始时间")
    private String startTime;

    @Size(max= 255,message="编码长度不能超过255")
    @ApiModelProperty("告警内容")
    @Length(max= 255,message="编码长度不能超过255")
    @ExcelProperty("告警内容")
    private String alarmMean;

    @NotNull(message="[]不能为空")
    @ApiModelProperty("告警状态: 0:未处理 1:已处理 2:状态失效 -1:状态异常")
    @ExcelProperty("告警状态")
    private String status;

    @ApiModelProperty("项目ID")
    @ExcelProperty("项目ID")
    private Integer projectSpecial;

    @ApiModelProperty("项目名称")
    @ExcelProperty("项目名称")
    private String projectName;

    public void setProjectName(String projectName) {
        this.projectName = ProjectTypeEnum.getProjectNameById(projectName);
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = DeviceType.getNameByCode(deviceType);
    }

    public void setSource(String source) {
        this.source = AlarmSource.getNameByCode(source);
    }

    public void setStatus(Integer status) {
        this.status = AlarmStatus.getNameByCode(String.valueOf(status));
    }
}
