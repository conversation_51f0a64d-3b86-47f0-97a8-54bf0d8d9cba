package com.bto.commons.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("bto_functional_instrument")
public class FunctionalInstrumentEntity {
    /**
     * 设备编号（设备SN码）||逆变器和非三晶运维器
     */
    @TableId
    private String deviceId;

    /**
     * 发电量(kWh)*100
     */
    private Integer electricity;

    /**
     * 数据时间
     */
    private Date initTime;

    /**
     * A点电压
     */
    private Integer apv;

    /**
     * B点电压
     */
    private Integer bpv;

    /**
     * C点电压
     */
    private Integer cpv;

    /**
     * A点电流
     */
    private Integer aac;

    /**
     * B点电流
     */
    private Integer bac;

    /**
     * C点电流
     */
    private Integer cac;

    /**
     * 数据创建时间
     */
    private Date createTime;

    /**
     * 数据更新时间
     */
    private Date updateTime;

}