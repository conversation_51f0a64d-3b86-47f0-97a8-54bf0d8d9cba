package com.bto.commons.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/5/19 16:45
 */
@Data
@TableName("bto_project_category")
public class Project implements Serializable {
    private static final long serialVersionUID = 2876814378816187894L;
    /**
     * 项目id
     */
    @NotNull(message="[项目id]不能为空")
    @ApiModelProperty("项目id")
    @TableId("id")
    private Integer id;
    /**
     * 项目分类名称
     */
    @NotBlank(message="[项目分类名称]不能为空")
    @Size(max= 25,message="编码长度不能超过25")
    @ApiModelProperty("项目分类名称")
    @Length(max= 25,message="编码长度不能超过25")
    @TableField("name")
    private String projectName;
    /**
     * 项目logo图片地址
     */
    @NotBlank(message="[项目logo图片地址]不能为空")
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    @TableField("img_url")
    @ApiModelProperty("项目logo图片地址")
    private String imgUrl;

    @ApiModelProperty("屏幕logo图片地址")
    @TableField("screen_logo")
    private String screenLogo;
    /**
     * 父id
     */
    @ApiModelProperty("父id")
    @TableField("pid")
    private Integer pid;
    /**
     * 逻辑删除（0：正常，1：已删除）
     */
    @NotNull(message="[逻辑删除（0：正常，1：已删除）]不能为空")
    @ApiModelProperty("逻辑删除（0：正常，1：已删除）")
    @TableLogic
    private Integer isDeleted;
    /**
     * 创建时间
     */
    @NotNull(message="[创建时间]不能为空")
    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private Date createTime;
    /**
     * 更新时间
     */
    @NotNull(message="[更新时间]不能为空")
    @ApiModelProperty("更新时间")
    @TableField("update_time")
    private Date updateTime;

}
