package com.bto.commons.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
* 逆变器所属光伏组件
* @TableName bto_inverter_component
*/

@TableName("bto_inverter_component")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InverterComponent implements Serializable {

    /**
    * 逆变器sn
    */
    @NotBlank(message="[逆变器sn]不能为空")
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("逆变器sn")
    @Length(max= 50,message="编码长度不能超过50")
    private String inverterSn;
    /**
    * 电站Uid
    */
    @NotBlank(message="[电站Uid]不能为空")
    @Size(max= 125,message="编码长度不能超过125")
    @ApiModelProperty("电站Uid")
    @Length(max= 125,message="编码长度不能超过125")
    private String plantUid;
    /**
    * 单块光伏板容量（Wp）
    */
    @NotNull(message="[单块光伏板容量（Wp）]不能为空")
    @ApiModelProperty("单块光伏板容量（Wp）")
    private Integer singleCapacity;
    /**
    * 朝向（0：不一致，1：一致）
    */
    @NotBlank(message="[朝向（0：不一致，1：一致）]不能为空")
    @Size(max= 10,message="编码长度不能超过10")
    @ApiModelProperty("朝向（0：不一致，1：一致）")
    @Length(max= 10,message="编码长度不能超过10")
    private String orientation;
    /**
    * pv1Dc1
    */
    @ApiModelProperty("pv1Dc1")
    private Integer pv1Dc1;
    /**
    * pv1Dc2
    */
    @ApiModelProperty("pv1Dc2")
    private Integer pv1Dc2;
    /**
    * pv1Dc3
    */
    @ApiModelProperty("pv1Dc3")
    private Integer pv1Dc3;
    /**
    * pv1Dc4
    */
    @ApiModelProperty("pv1Dc4")
    private Integer pv1Dc4;
    /**
    * pv2Dc1
    */
    @ApiModelProperty("pv2Dc1")
    private Integer pv2Dc1;
    /**
    * pv2Dc2
    */
    @ApiModelProperty("pv2Dc2")
    private Integer pv2Dc2;
    /**
    * pv2Dc3
    */
    @ApiModelProperty("pv2Dc3")
    private Integer pv2Dc3;
    /**
    * pv2Dc4
    */
    @ApiModelProperty("pv2Dc4")
    private Integer pv2Dc4;
    /**
    * pv3Dc1
    */
    @ApiModelProperty("pv3Dc1")
    private Integer pv3Dc1;
    /**
    * pv3Dc2
    */
    @ApiModelProperty("pv3Dc2")
    private Integer pv3Dc2;
    /**
    * pv3Dc3
    */
    @ApiModelProperty("pv3Dc3")
    private Integer pv3Dc3;
    /**
    * pv3Dc4
    */
    @ApiModelProperty("pv3Dc4")
    private Integer pv3Dc4;
    /**
    * pv4Dc1
    */
    @ApiModelProperty("pv4Dc1")
    private Integer pv4Dc1;
    /**
    * pv4Dc2
    */
    @ApiModelProperty("pv4Dc2")
    private Integer pv4Dc2;
    /**
    * pv4Dc3
    */
    @ApiModelProperty("pv4Dc3")
    private Integer pv4Dc3;
    /**
    * pv4Dc4
    */
    @ApiModelProperty("pv4Dc4")
    private Integer pv4Dc4;
    /**
    * k1
    */
    @ApiModelProperty("k1")
    private BigDecimal k1;
    /**
    * k2
    */
    @ApiModelProperty("k2")
    private BigDecimal k2;
    /**
    * k3
    */
    @ApiModelProperty("k3")
    private BigDecimal k3;
    /**
    * k4
    */
    @ApiModelProperty("k4")
    private BigDecimal k4;
    /**
    * k5
    */
    @ApiModelProperty("k5")
    private BigDecimal k5;
    /**
    * k6
    */
    @ApiModelProperty("k6")
    private BigDecimal k6;
    /**
    * 创建时间
    */
    @ApiModelProperty("创建时间")
    private Date createTime;
    /**
    * 更新时间
    */
    @ApiModelProperty("更新时间")
    private Date updateTime;
    /**
    * pv5Dc1
    */
    @ApiModelProperty("pv5Dc1")
    private Integer pv5Dc1;
    /**
    * pv5Dc2
    */
    @ApiModelProperty("pv5Dc2")
    private Integer pv5Dc2;
    /**
    * pv5Dc3
    */
    @ApiModelProperty("pv5Dc3")
    private Integer pv5Dc3;
    /**
    * pv5Dc4
    */
    @ApiModelProperty("pv5Dc4")
    private Integer pv5Dc4;
    /**
    * k7
    */
    @ApiModelProperty("k7")
    private BigDecimal k7;
    /**
    * k8
    */
    @ApiModelProperty("k8")
    private BigDecimal k8;
    /**
    * pv6Dc1
    */
    @ApiModelProperty("pv6Dc1")
    private Integer pv6Dc1;
    /**
    * pv6Dc2
    */
    @ApiModelProperty("pv6Dc2")
    private Integer pv6Dc2;
    /**
    * pv6Dc3
    */
    @ApiModelProperty("pv6Dc3")
    private Integer pv6Dc3;
    /**
    * pv6Dc4
    */
    @ApiModelProperty("pv6Dc4")
    private Integer pv6Dc4;
    /**
    * k9
    */
    @ApiModelProperty("k9")
    private BigDecimal k9;
    /**
    * k10
    */
    @ApiModelProperty("k10")
    private BigDecimal k10;
    /**
    * pv7Dc1
    */
    @ApiModelProperty("pv7Dc1")
    private Integer pv7Dc1;
    /**
    * pv7Dc2
    */
    @ApiModelProperty("pv7Dc2")
    private Integer pv7Dc2;
    /**
    * pv7Dc3
    */
    @ApiModelProperty("pv7Dc3")
    private Integer pv7Dc3;
    /**
    * pv7Dc4
    */
    @ApiModelProperty("pv7Dc4")
    private Integer pv7Dc4;
    /**
    *  k11
    */
    @ApiModelProperty("k11")
    private BigDecimal k11;
    /**
    * k12
    */
    @ApiModelProperty("k12")
    private BigDecimal k12;
    /**
    * pv8Dc1
    */
    @ApiModelProperty("pv8Dc1")
    private Integer pv8Dc1;
    /**
    * pv8Dc2
    */
    @ApiModelProperty("pv8Dc2")
    private Integer pv8Dc2;
    /**
    * pv8Dc3
    */
    @ApiModelProperty("pv8Dc3")
    private Integer pv8Dc3;
    /**
    * pv8Dc4
    */
    @ApiModelProperty("pv8Dc4")
    private Integer pv8Dc4;
    /**
    * k13
    */
    @ApiModelProperty("k13")
    private BigDecimal k13;
    /**
    * k14
    */
    @ApiModelProperty("k14")
    private BigDecimal k14;
    /**
    * pv9Dc1
    */
    @ApiModelProperty("pv9Dc1")
    private Integer pv9Dc1;
    /**
    * pv9Dc2
    */
    @ApiModelProperty("pv9Dc2")
    private Integer pv9Dc2;
    /**
    * pv9Dc3
    */
    @ApiModelProperty("pv9Dc3")
    private Integer pv9Dc3;
    /**
    * pv9Dc4
    */
    @ApiModelProperty("pv9Dc4")
    private Integer pv9Dc4;
    /**
    * k15

    */
    @ApiModelProperty("k15")
    private BigDecimal k15;
    /**
    * k16
    */
    @ApiModelProperty("k16")
    private BigDecimal k16;
    /**
    * pv10Dc1
    */
    @ApiModelProperty("pv10Dc1")
    private Integer pv10Dc1;
    /**
    * pv10Dc2
    */
    @ApiModelProperty("pv10Dc2")
    private Integer pv10Dc2;
    /**
    * pv10Dc3
    */
    @ApiModelProperty("pv10Dc3")
    private Integer pv10Dc3;
    /**
    * pv10Dc4
    */
    @ApiModelProperty("pv10Dc4")
    private Integer pv10Dc4;
    /**
    * k17
    */
    @ApiModelProperty("k17")
    private BigDecimal k17;
    /**
    * k18
    */
    @ApiModelProperty("k18")
    private BigDecimal k18;
    /**
    * pv11Dc1
    */
    @ApiModelProperty("pv11Dc1")
    private Integer pv11Dc1;
    /**
    * pv11Dc2
    */
    @ApiModelProperty("pv11Dc2")
    private Integer pv11Dc2;
    /**
    * pv11Dc3
    */
    @ApiModelProperty("pv11Dc3")
    private Integer pv11Dc3;
    /**
    * pv11Dc4
    */
    @ApiModelProperty("pv11Dc4")
    private Integer pv11Dc4;
    /**
    * k19
    */
    @ApiModelProperty("k19")
    private BigDecimal k19;
    /**
    * k20
    */
    @ApiModelProperty("k20")
    private BigDecimal k20;
    /**
    * pv12Dc1
    */
    @ApiModelProperty("pv12Dc1")
    private Integer pv12Dc1;
    /**
    * pv12Dc2
    */
    @ApiModelProperty("pv12Dc2")
    private Integer pv12Dc2;
    /**
    * pv12Dc3
    */
    @ApiModelProperty("pv12Dc3")
    private Integer pv12Dc3;
    /**
    * pv12Dc4
    */
    @ApiModelProperty("pv12Dc4")
    private Integer pv12Dc4;
    /**
    * k21
    */
    @ApiModelProperty("k21")
    private BigDecimal k21;
    /**
    * k22
    */
    @ApiModelProperty("k22")
    private BigDecimal k22;

}
