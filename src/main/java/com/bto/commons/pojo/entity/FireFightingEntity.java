package com.bto.commons.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 消防联动
 *
 * <AUTHOR>
 * @since 1.0.0 2024-09-27
 */

@Data
@TableName("bto_fire_fighting")
public class FireFightingEntity {
    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Integer id;

    /**
     * 状态
     */
    private String status;

    /**
     * 时间
     */
    private Date time;

    /**
     * 区域
     */
    private String region;

    /**
     * 更新时间
     */
    private Date updateTime;

}