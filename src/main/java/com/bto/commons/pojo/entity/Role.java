package com.bto.commons.pojo.entity;

/**
 * <AUTHOR>
 * @date 2023/5/20 14:29
 */

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * 角色管理-checked
 * <AUTHOR>
 * @TableName bto_role
 */
@Data
@TableName("bto_role")
public class Role implements Serializable {

    private static final long serialVersionUID = -6920662968929217454L;
    /**
     * id
     */
    @NotNull(message = "[id]不能为空")
    @ApiModelProperty("id")
    @TableId("id")
    private Long roleID;
    /**
     * 角色名称
     */
    @NotBlank(message = "[角色名称]不能为空")
    @Size(max = 50, message = "编码长度不能超过50")
    @ApiModelProperty("角色名称")
    @Length(max = 50, message = "编码长度不能超过50")
    @TableField("role_name")
    private String roleName;
    /**
     * 备注
     */
    @NotBlank(message = "[备注]不能为空")
    @Size(max = 100, message = "编码长度不能超过100")
    @ApiModelProperty("备注")
    @Length(max = 100, message = "编码长度不能超过100")
    @TableField("role_remark")
    private String roleRemark;
    /**
     * 删除标识  0：正常   1：已删除
     */
    @NotNull(message = "[删除标识  0：正常   1：已删除]不能为空")
    @ApiModelProperty("删除标识  0：正常   1：已删除")
    @TableLogic
    private Integer isDeleted;
    /**
     * 创建者
     */
    @Size(max = 70, message = "编码长度不能超过70")
    @ApiModelProperty("创建者")
    @Length(max = 70, message = "编码长度不能超过70")
    private String creator;
    /**
     * 创建时间
     */
    @NotNull(message = "[创建时间]不能为空")
    @ApiModelProperty("创建时间")
    private Date createTime;
    /**
     * 更新者
     */
    @Size(max = 70, message = "编码长度不能超过70")
    @ApiModelProperty("更新者")
    @Length(max = 70, message = "编码长度不能超过70")
    private String updater;
    /**
     * 更新时间
     */
    @NotNull(message = "[更新时间]不能为空")
    @ApiModelProperty("更新时间")
    private Date updateTime;
    /**
     * 数据版本号
     */
    @NotNull(message = "[数据版本号]不能为空")
    @ApiModelProperty("数据版本号")
    private Integer version;

}
