package com.bto.commons.pojo.entity;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;

/**
 * 配电柜数据
 *
 * <AUTHOR>
 * @since 2024-07-24
 */

@Data
@TableName("bto_counter")
public class CounterRecordEntity {
    /**
     * 自增ID
     */
    @TableId
    private Integer id;

    /**
     * 配电柜ID
     */
    private String counterId;

    /**
     * 数据时间
     */
    private Date initTime;

    /**
     * 过压值(250-300)
     */
    private Integer overtension;

    /**
     * 欠压值(150-200)
     */
    private Integer undervoltage;

    /**
     * 漏电等级（表1）
     * <p>
     * 值 值
     * 0 不跟踪 自动 1 自动 2 自动 3 自动 4 不跟踪
     * 1 自动 1 0 50 100 200 50 50
     * 2 自动 2 1 100 200 300 100 100
     * 3 自动 3 2 200 300 400 200 200
     * 4 自动 4 3 300 400 600 300 300
     * 5 关闭保护 4 400 500 800 400 400
     */
    private Integer drainGrade;

    /**
     * Ir1 整定值"整定值"（Setting Value 或 Pickup Value）
     * 值 250A 400A 读写
     * 0 100 200
     * 1 125 225
     * 2 140 250
     * 3 160 315
     * 4 180 350
     * 5 200 400
     * 6 225 ----
     * 7 250 ---
     */
    private Integer lr1Sv;

    /**
     * Ir1 延迟时间(s)
     * <p>
     * 值 延时时间
     * 0 3
     * 1 4
     * 2 6
     * 3 8
     * 4 10
     * 5 12
     * 6 16
     * 7 18
     * 8 of
     */
    private Integer lr1Time;

    /**
     * Ir2 整定值
     * <p>
     * 值 倍数（Ir2=n*Ir1）
     * 0 2
     * 1 2.5
     * 2 3
     * 3 4
     * 4 5
     * 5 6
     * 6 7
     * 7 8
     * 8 10
     * 9 12
     */
    private Integer lr2Sv;

    /**
     * Ir2 延迟时间
     * <p>
     * <p>
     * 值 延时时间
     * 0 0.1
     * 1 0.1
     * 2 0.2
     * 3 0.3
     * 4 0.4
     * 5 0.6
     * 6 0.8
     * 7 1.0
     * 8 of
     */
    private Integer lr2Time;

    /**
     * Ir3 整定值
     * <p>
     * <p>
     * 值 倍数(Ir3=n*Ir1)
     * 0 4
     * 1 6
     * 2 7
     * 3 8
     * 4 10
     * 5 11
     * 6 12
     * 7 13
     * 8 1
     */
    private Integer lr3Sv;

    /**
     * 上电试合闸
     * 0:禁用，1:启用
     */
    private Integer electrifyStatus;

    /**
     * 故障类型(100/250/400/630/800)
     */
    private Integer alarmType;

    /**
     * 故障时间
     */
    private Date alarmTime;

    /**
     * 设备类型（表2）
     */
    private String deviceType;

    /**
     * 设备状态(表3)
     */
    private Integer status;

    /**
     * 故障 ID
     */
    private Integer alarmId;

    /**
     * 电压 A
     */
    private Integer vac1;

    /**
     * 电压 B
     */
    private Integer vac2;

    /**
     * 电压 C
     */
    private Integer vac3;

    /**
     * 电流 A
     */
    private Integer iac1;

    /**
     * 电流 B
     */
    private Integer iac2;

    /**
     * 电流 C
     */
    private Integer iac3;

    /**
     * 漏电流
     */
    private Integer drainCurrent;

    /**
     * 电流 N
     */
    private Integer currentN;

    /**
     * 数据插入时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

}