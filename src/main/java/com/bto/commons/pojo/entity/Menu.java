package com.bto.commons.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * 菜单管理
 * <AUTHOR>
 * @date 2023/5/20 15:17
 */
@Data
@TableName("bto_menu")
public class Menu implements Serializable {
    private static final long serialVersionUID = -8129113743666757470L;

    @Schema(description = "id")
    @NotNull(message="[id]不能为空")
    @ApiModelProperty("id")
    @TableId("id")
    private Integer id;
    /**
     * 上级ID，一级菜单为0
     */
    @NotNull(message="[上级ID，一级菜单为0]不能为空")
    @ApiModelProperty("上级ID，一级菜单为0")
    @TableField(value = "pid")
    private Integer pid;
    /**
     * 菜单名称
     */
    @NotBlank(message="[菜单名称]不能为空")
    @Size(max= 200,message="编码长度不能超过200")
    @ApiModelProperty("菜单名称")
    @Length(max= 200,message="编码长度不能超过200")
    @TableField("menu_name")
    private String label;
    /**
     * 菜单URL
     */
    @Size(max= 200,message="编码长度不能超过200")
    @ApiModelProperty("菜单URL")
    @Length(max= 200,message="编码长度不能超过200")
    @TableField("menu_url")
    private String path;
    /**
     * 授权标识(多个用逗号分隔，如：sys:menu:list,sys:menu:save)
     */
    @NotBlank(message="[授权标识(多个用逗号分隔，如：sys:menu:list,sys:menu:save)]不能为空")
    @Size(max= 500,message="编码长度不能超过500")
    @ApiModelProperty("授权标识(多个用逗号分隔，如：sys:menu:list,sys:menu:save)")
    @Length(max= 500,message="编码长度不能超过500")
    @TableField("auth")
    private String auth;
    /**
     * 类型   0：菜单   1：按钮   2：接口
     */
    @ApiModelProperty("类型   0：菜单   1：按钮   2：接口")
    @TableField("type")
    private Integer type;
    /**
     * 打开方式   0：内部   1：外部
     */
    @ApiModelProperty("打开方式   0：内部   1：外部")
    @TableField("open_style")
    private Integer openStyle;
    /**
     * 菜单图标
     */
    @NotBlank(message="[菜单图标]不能为空")
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("菜单图标")
    @Length(max= 50,message="编码长度不能超过50")
    @TableField("icon")
    private String icon;
    /**
     * 删除标识  0：正常   1：已删除
     */
    @NotNull(message="[删除标识  0：正常   1：已删除]不能为空")
    @ApiModelProperty("删除标识  0：正常   1：已删除")
    @TableField("is_deleted")
    @TableLogic
    private Integer isDeleted;
    /**
     * 创建者
     */
    @NotBlank(message="[创建者]不能为空")
    @Size(max= 70,message="编码长度不能超过70")
    @ApiModelProperty("创建者")
    @Length(max= 70,message="编码长度不能超过70")
    @TableField("creator")
    private String creator;
    /**
     * 创建时间
     */
    @NotNull(message="[创建时间]不能为空")
    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private Date createTime;
    /**
     * 更新者
     */
    @NotBlank(message="[更新者]不能为空")
    @Size(max= 70,message="编码长度不能超过70")
    @ApiModelProperty("更新者")
    @Length(max= 70,message="编码长度不能超过70")
    @TableField("updater")
    private String updater;
    /**
     * 更新时间
     */
    @NotNull(message="[更新时间]不能为空")
    @ApiModelProperty("更新时间")
    @TableField("update_time")
    private Date updateTime;
    /**
     * 排序
     */
    @NotNull(message="[排序]不能为空")
    @ApiModelProperty("排序")
    @TableField("sort")
    private Integer sort;

    @Range(min = 0, max = 1)
    private Integer hidden;

    private String title;

    @Range(min = 0, max = 1)
    private Integer breadcrumb;

    @Range(min = 0, max = 1)
    private Integer affix;

    @Range(min = 0, max = 1)
    @TableField("always_show")
    private Integer alwaysShow;

    @TableField("active_menu")
    private String activeMenu;

    @Range(min = 0, max = 1)
    @TableField("keep_alive")
    private Integer keepAlive;

    @ApiModelProperty("组件")
    private String component;
    @ApiModelProperty("菜单版本")
    private String version;

    private String redirect;
}
