package com.bto.commons.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
* 设备类型
* <AUTHOR>
 * @TableName bto_device_type
*/
@Data
@TableName("bto_device_type")
public class BtoDeviceModel implements Serializable {
    private static final long serialVersionUID = -1031707290160986322L;

    @NotNull(message="[自增id]不能为空")
    @ApiModelProperty("自增id")
    @JsonIgnore
    private String id;
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("厂家")
    @Length(max= 50,message="编码长度不能超过50")
    private String manufacturer;
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("系列")
    @Length(max= 50,message="编码长度不能超过50")
    private String series;
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("型号")
    @Length(max= 50,message="编码长度不能超过50")
    private String model;
    @ApiModelProperty("电路数")
    private String circuit;
    @Size(max= 255,message="编码长度不能超过255")
    @ApiModelProperty("设备类型（1-逆变器 ；2-运维器；3-电表）")
    @Length(max= 255,message="编码长度不能超过255")
    private String deviceType;
    @ApiModelProperty("创建时间")
    private String createTime;
    @ApiModelProperty("更新时间")
    private String updateTime;
    @ApiModelProperty("更新时间")
    private List<BtoDeviceModel> children;

}
