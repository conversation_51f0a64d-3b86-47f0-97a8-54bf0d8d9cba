package com.bto.commons.pojo.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/6/20 14:16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EdgeServerSIMCard {
    @ApiModelProperty("电站Uid")
    private String plantUid;
    @ApiModelProperty("电站名称")
    private String plantName;
    @ApiModelProperty("imei")
    private String IMEI;
    @ApiModelProperty("用户名称")
    private String userName;
    @ApiModelProperty("运维器SN")
    private String operatorSN;
    @ApiModelProperty("激活时间")
    private String startTime;
    @ApiModelProperty("过期时间")
    private String endTime;
    @ApiModelProperty("信号强度")
    private String signalStrength;
}
