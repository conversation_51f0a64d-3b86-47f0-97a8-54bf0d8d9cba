package com.bto.commons.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/4/25 14:24
 */
@Data
@ApiModel("电站地址条件")
public class PlantAddressDTO implements Serializable {
    private static final long serialVersionUID = -7929458098359458370L;
    @ApiModelProperty("国家")
    private String country;
    @ApiModelProperty("省份")
    private String province;
    @ApiModelProperty("城市")
    private String city;
    @ApiModelProperty("县/区")
    private String area;
    @ApiModelProperty("镇/乡")
    private String town;
}
