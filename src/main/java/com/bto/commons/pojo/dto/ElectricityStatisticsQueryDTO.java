package com.bto.commons.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/26 9:18
 */
@Data
@ApiModel("电量统计查询")
public class ElectricityStatisticsQueryDTO implements Serializable {
    private static final long serialVersionUID = 8912808677051621144L;
    @ApiModelProperty("电站Uid数组")
    private List<String> plantUids;
    @ApiModelProperty("查询日期")
    private  String date;
    @ApiModelProperty("电站名称(模糊)")
    private  String address;
    @ApiModelProperty("当前页码")
    private  Integer currentPage;
    @ApiModelProperty("页面大小")
    private  Integer pageSize;


}
