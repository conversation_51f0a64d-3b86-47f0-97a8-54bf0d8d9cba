package com.bto.commons.pojo.dto;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/9/16 11:19
 */

@Data
@ApiModel("运维器列表查询条件对象")
@EqualsAndHashCode(callSuper = true)
public class OperatorQueryDTO extends PageDTO implements Serializable {
    private static final long serialVersionUID = 1132076743591747552L;
    @ApiModelProperty("电站Uid")
    @ExcelIgnore
    private String plantUid;

    @ApiModelProperty("运维器sn")
    @ExcelProperty("运维器sn")
    private String operatorSn;

    @ApiModelProperty("运维器IMEI")
    @ExcelProperty("运维器IMEI")
    private String imei;

    @ApiModelProperty("三晶数据标识（三晶：0  ，自取：1）")
    @Length(max= 0,message="编码长度不能超过0")
    @ExcelIgnore
    private String state;

    @ApiModelProperty("电站名称")
    @ExcelProperty("电站名称")
    private String plantName;

    @ApiModelProperty("数据采集日期")
    private String collectDate;

    @ApiModelProperty("运维器启动状态")
    @ExcelProperty("运维器启动状态")
    private String status;

    @ApiModelProperty("运维器运行状态")
    @ExcelProperty("运维器运行状态")
    private String enable;

    {
        collectDate = DateUtil.today();
    }


}
