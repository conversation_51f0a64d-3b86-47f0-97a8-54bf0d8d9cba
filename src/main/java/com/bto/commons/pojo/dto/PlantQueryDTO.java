package com.bto.commons.pojo.dto;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/8 15:56
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("电站列表查询条件")
public class PlantQueryDTO extends PageDTO implements Serializable {
    private static final long serialVersionUID = 1193058432288795849L;
    @ApiModelProperty("电站编号")
    private String plantUid;
    @ApiModelProperty("电站名称")
    private String plantName;
    @ApiModelProperty("电站类型")
    private String plantType;
    @ApiModelProperty("项目id")
    private String projectId;
    @ApiModelProperty("电站状态:一个或多个")
    private List<String> multiPlantStatus;

    @ApiModelProperty("装机容量起始区间")
    private String minPlantCapacity;
    @ApiModelProperty("装机容量最大区间")
    private String maxPlantCapacity;
    @ApiModelProperty("配电箱状态")
    private String powerDistributor;
    @ApiModelProperty(value = "省份")
    private String province;
    @ApiModelProperty(value = "市/州")
    private String city;
    @ApiModelProperty(value = "气象站城市")
    private List<String> stationPlantList;
    @ApiModelProperty("县/区")
    private String area;
    @ApiModelProperty("县/区")
    private String town;
    @ApiModelProperty("电站地址")
    private String address;
    @ApiModelProperty("创建时间开始区间")
    private String startCreateTime;
    @ApiModelProperty("创建时间结束区间")
    private String endCreateTime;

    {
        this.setCurrentPage(1);
        this.setPageSize(10);
    }

    public void setMinPlantCapacity(String minPlantCapacity) {
        if (StrUtil.isNotEmpty(minPlantCapacity)) {
            BigDecimal b1 = new BigDecimal(minPlantCapacity);
            this.minPlantCapacity = b1.multiply(new BigDecimal(1000)).toString();
        }
    }

    public void setMaxPlantCapacity(String maxPlantCapacity) {
        if (StrUtil.isNotEmpty(maxPlantCapacity)) {
            BigDecimal b1 = new BigDecimal(maxPlantCapacity);
            this.maxPlantCapacity = b1.multiply(new BigDecimal(1000)).toString();
        }
    }
}
