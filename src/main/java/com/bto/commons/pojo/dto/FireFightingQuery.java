package com.bto.commons.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 消防联动查询
 *
 * <AUTHOR>
 * @since 1.0.0 2024-09-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "消防联动查询")
public class FireFightingQuery extends PageDTO {
    @ApiModelProperty(example = "aaa12,bb23")
    List<Integer> id;
}