package com.bto.commons.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/29 16:54
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper=false)
public class ElectricityStatisticsExportFileDTO extends ExportFileDTO implements Serializable {

    private static final long serialVersionUID = 611678972830379948L;
    @ApiModelProperty("电站Uid数组")
    private List<String> plantUids;
    @ApiModelProperty("查询日期")
    private  String date;
    @ApiModelProperty("地址区域(模糊)")
    private  String address;
    @ApiModelProperty("当前页码")
    private  String currentPage;
    @ApiModelProperty("页面大小")
    private  String pageSize;

}
