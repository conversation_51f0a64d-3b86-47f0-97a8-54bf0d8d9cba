package com.bto.commons.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/4/25 16:05
 */
@Data
@ApiModel("运维器告警信息列表查询条件")
public class OperatorAlarmQueryDTO extends PageDTO implements Serializable {
    private static final long serialVersionUID = -8418250108738798819L;
    @ApiModelProperty(name="plantUid",value = "电站编号")
    private String plantUid;
    @ApiModelProperty(name="plantName",value = "电站名称(模糊查询)")
    private String plantName;
    @ApiModelProperty(name="imei",value = "imei")
    private String imei;
    @ApiModelProperty(name="address",value = "电站地址(模糊查询)")
    private String address;
    @ApiModelProperty(value = "查询条件：开始时间",notes = "默认为当天")
    private String startTime;
    @ApiModelProperty(value ="查询条件：结束时间",notes = "默认为当天")
    private String endTime;
    @ApiModelProperty(value ="查询条件：告警状态",notes = "0：待处理 ，1：已处理，2：失效")
    private String alarmStatus;

    {
        this.plantName = "";
        this.address = "";
        this.startTime = "";
        this.endTime = "";
    }

}
