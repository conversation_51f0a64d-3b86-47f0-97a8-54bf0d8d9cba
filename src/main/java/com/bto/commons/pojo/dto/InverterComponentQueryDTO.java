package com.bto.commons.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/5/25 9:37
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InverterComponentQueryDTO {

    /**
     * 逆变器sn
     */
    @NotBlank(message="[逆变器sn]不能为空")
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("逆变器sn")
    @Length(max= 50,message="编码长度不能超过50")
    private String inverterSn;
    /**
     * 电站Uid
     */
    @NotBlank(message="[电站Uid]不能为空")
    @Size(max= 125,message="编码长度不能超过125")
    @ApiModelProperty("电站Uid")
    @Length(max= 125,message="编码长度不能超过125")
    private String plantUid;
    /**
     * 单块光伏板容量（Wp）
     */
    @NotNull(message="[单块光伏板容量（Wp）]不能为空")
    @ApiModelProperty("单块光伏板容量（Wp）")
    private Integer singleCapacity;
    /**
     * 朝向（0：不一致，1：一致）
     */
    @NotBlank(message="[朝向（0：不一致，1：一致）]不能为空")
    @Size(max= 10,message="编码长度不能超过10")
    @ApiModelProperty("朝向（0：不一致，1：一致）")
    @Length(max= 10,message="编码长度不能超过10")
    private String orientation;
    /**
     * pv1Dc1
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv1Dc1")
    private Integer pv1Dc1;
    /**
     * pv1Dc2
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv1Dc2")
    private Integer pv1Dc2;
    /**
     * pv1Dc3
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv1Dc3")
    private Integer pv1Dc3;
    /**
     * pv1Dc4
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv1Dc4")
    private Integer pv1Dc4;
    /**
     * pv2Dc1
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv2Dc1")
    private Integer pv2Dc1;
    /**
     * pv2Dc2
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv2Dc2")
    private Integer pv2Dc2;
    /**
     * pv2Dc3
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv2Dc3")
    private Integer pv2Dc3;
    /**
     * pv2Dc4
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv2Dc4")
    private Integer pv2Dc4;
    /**
     * pv3Dc1
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv3Dc1")
    private Integer pv3Dc1;
    /**
     * pv3Dc2
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv3Dc2")
    private Integer pv3Dc2;
    /**
     * pv3Dc3
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv3Dc3")
    private Integer pv3Dc3;
    /**
     * pv3Dc4
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv3Dc4")
    private Integer pv3Dc4;
    /**
     * pv4Dc1
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv4Dc1")
    private Integer pv4Dc1;
    /**
     * pv4Dc2
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv4Dc2")
    private Integer pv4Dc2;
    /**
     * pv4Dc3
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv4Dc3")
    private Integer pv4Dc3;
    /**
     * pv4Dc4
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv4Dc4")
    private Integer pv4Dc4;
    /**
     * 创建时间
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("更新时间")
    private Date updateTime;
    /**
     * pv5Dc1
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv5Dc1")
    private Integer pv5Dc1;
    /**
     * pv5Dc2
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv5Dc2")
    private Integer pv5Dc2;
    /**
     * pv5Dc3
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv5Dc3")
    private Integer pv5Dc3;
    /**
     * pv5Dc4
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv5Dc4")
    private Integer pv5Dc4;
    /**
     * pv6Dc1
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv6Dc1")
    private Integer pv6Dc1;
    /**
     * pv6Dc2
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv6Dc2")
    private Integer pv6Dc2;
    /**
     * pv6Dc3
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv6Dc3")
    private Integer pv6Dc3;
    /**
     * pv6Dc4
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv6Dc4")
    private Integer pv6Dc4;
    /**
     * pv7Dc1
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv7Dc1")
    private Integer pv7Dc1;
    /**
     * pv7Dc2
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv7Dc2")
    private Integer pv7Dc2;
    /**
     * pv7Dc3
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv7Dc3")
    private Integer pv7Dc3;
    /**
     * pv7Dc4
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv7Dc4")
    private Integer pv7Dc4;
    /**
     * pv8Dc1
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv8Dc1")
    private Integer pv8Dc1;
    /**
     * pv8Dc2
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv8Dc2")
    private Integer pv8Dc2;
    /**
     * pv8Dc3
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv8Dc3")
    private Integer pv8Dc3;
    /**
     * pv8Dc4
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv8Dc4")
    private Integer pv8Dc4;
    /**
     * pv9Dc1
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv9Dc1")
    private Integer pv9Dc1;
    /**
     * pv9Dc2
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv9Dc2")
    private Integer pv9Dc2;
    /**
     * pv9Dc3
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv9Dc3")
    private Integer pv9Dc3;
    /**
     * pv9Dc4
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv9Dc4")
    private Integer pv9Dc4;
    /**
     * pv10Dc1
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv10Dc1")
    private Integer pv10Dc1;
    /**
     * pv10Dc2
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv10Dc2")
    private Integer pv10Dc2;
    /**
     * pv10Dc3
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv10Dc3")
    private Integer pv10Dc3;
    /**
     * pv10Dc4
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv10Dc4")
    private Integer pv10Dc4;
    /**
     * pv11Dc1
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv11Dc1")
    private Integer pv11Dc1;
    /**
     * pv11Dc2
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv11Dc2")
    private Integer pv11Dc2;
    /**
     * pv11Dc3
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv11Dc3")
    private Integer pv11Dc3;
    /**
     * pv11Dc4
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv11Dc4")
    private Integer pv11Dc4;
    /**
     * pv12Dc1
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv12Dc1")
    private Integer pv12Dc1;
    /**
     * pv12Dc2
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv12Dc2")
    private Integer pv12Dc2;
    /**
     * pv12Dc3
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv12Dc3")
    private Integer pv12Dc3;
    /**
     * pv12Dc4
     */
    @Digits(integer =5,fraction = 2)
    @NotNull
    @ApiModelProperty("pv12Dc4")
    private Integer pv12Dc4;
}
