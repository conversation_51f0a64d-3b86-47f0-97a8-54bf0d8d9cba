package com.bto.commons.pojo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/09/06 10:32:18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("电站更新信息对象")
public class PlantUpdateDTO implements Serializable {
    private static final long serialVersionUID = 6932167805688283490L;
    @ApiModelProperty(value = "电站地址")
    private String address;
    @ApiModelProperty(value = "省")
    private String province;
    @ApiModelProperty(value = "市")
    private String city;
    @ApiModelProperty(value = "区")
    private String area;
    @ApiModelProperty(value = "纬度")
    private String latitude;
    @ApiModelProperty(value = "经度")
    private String longitude;
    @ApiModelProperty(value = "电站名称")
    private String plantName;
    @ApiModelProperty(value = "电站容量")
    private String plantCapacity;
    @ApiModelProperty(value = "电表编号")
    private String meterId;
    @ApiModelProperty(value = "电站编号")
    private String plantUid;
    @ApiModelProperty(value = "并网时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private String createTime;
    @ApiModelProperty(value = "电价")
    private String salePrice;
    @ApiModelProperty(value = "电站朝向")
    private String orientation;
}
