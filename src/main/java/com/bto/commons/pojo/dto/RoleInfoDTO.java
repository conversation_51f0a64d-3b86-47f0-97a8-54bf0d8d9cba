package com.bto.commons.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/20 14:13
 */
@Data
@ApiModel("角色信息")
public class RoleInfoDTO implements Serializable {
    private static final long serialVersionUID = 1928786352188917397L;
    @ApiModelProperty("角色编号--新增页面不传")
    private Long roleID;
    @ApiModelProperty("角色名称")
    private String roleName;
    @ApiModelProperty("角色备注")
    private String roleRemark;
    @ApiModelProperty("菜单权限: 列表")
    private List<Long> menuList;
}
