package com.bto.commons.pojo.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/5/20 8:47
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InverterAdditionalPropertyDTO implements Serializable {

    /**
     * 逆变器SN
     */
    @NotBlank(message = "[逆变器SN]不能为空")
    @Size(max = 50, message = "编码长度不能超过50")
    @ApiModelProperty("逆变器SN")
    @Length(max = 50, message = "编码长度不能超过50")
    private String inverterSn;
    /**
     * 功率（W）
     */
    @NotNull(message = "[功率（W）]不能为空")
    @ApiModelProperty("功率（W）")
    private String power;
    /**
     * 当天发电量（kWh）*100
     */
    @NotNull(message = "[当天发电量（kWh）*100]不能为空")
    @ApiModelProperty("当天发电量（kWh）*100")
    private String todayElectricity;
    /**
     * 当月发电量（kWh）*100
     */
    @NotNull(message = "[当月发电量（kWh）*100]不能为空")
    @ApiModelProperty("当月发电量（kWh）*100")
    private String monthElectricity;
    /**
     * 当年发电量（kWh）*100
     */
    @NotNull(message = "[当年发电量（kWh）*100]不能为空")
    @ApiModelProperty("当年发电量（kWh）*100")
    private String yearElectricity;
    /**
     * 累计发电量（kWh）*100
     */
    @NotNull(message = "[累计发电量（kWh）*100]不能为空")
    @ApiModelProperty("累计发电量（kWh）*100")
    private String totalElectricity;
    /**
     * 数据时间
     */
    @ApiModelProperty("数据时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private String initTime;
    /**
     * 输入电流1路（A）*100
     */
    @ApiModelProperty("输入电流1路（A）*100")
    private String ipv1;
    /**
     * 输入电流2路（A）*100
     */
    @ApiModelProperty("输入电流2路（A）*100")
    private String ipv2;
    /**
     * 输入电流3路（A）*100
     */
    @ApiModelProperty("输入电流3路（A）*100")
    private String ipv3;
    /**
     * 输入电流4路（A）*100
     */
    @ApiModelProperty("输入电流4路（A）*100")
    private String ipv4;
    /**
     * 输入电流5路（A）*100
     */
    @ApiModelProperty("输入电流5路（A）*100")
    private String ipv5;
    /**
     * 输入电流6路（A）*100
     */
    @ApiModelProperty("输入电流6路（A）*100")
    private String ipv6;
    /**
     * 输入电流7路（A）*100
     */
    @ApiModelProperty("输入电流7路（A）*100")
    private String ipv7;
    /**
     * 输入电流8路（A）*100
     */
    @ApiModelProperty("输入电流8路（A）*100")
    private String ipv8;
    /**
     * 输入电流9路（A）*100
     */
    @ApiModelProperty("输入电流9路（A）*100")
    private String ipv9;
    /**
     * 输入电流10路（A）*100
     */
    @ApiModelProperty("输入电流10路（A）*100")
    private String ipv10;
    /**
     * 输入电流11路（A）*100
     */
    @ApiModelProperty("输入电流11路（A）*100")
    private String ipv11;
    /**
     * 输入电流12路（A）*100
     */
    @ApiModelProperty("输入电流12路（A）*100")
    private String ipv12;
    /**
     * 输入电压1路（V）*100
     */
    @ApiModelProperty("输入电压1路（V）*100")
    private String vpv1;
    /**
     * 输入电压2路（V）*100
     */
    @ApiModelProperty("输入电压2路（V）*100")
    private String vpv2;
    /**
     * 输入电压3路（V）*100
     */
    @ApiModelProperty("输入电压3路（V）*100")
    private String vpv3;
    /**
     * 输入电压4路（V）*100
     */
    @ApiModelProperty("输入电压4路（V）*100")
    private String vpv4;
    /**
     * 输入电压5路（V）*100
     */
    @ApiModelProperty("输入电压5路（V）*100")
    private String vpv5;
    /**
     * 输入电压6路（V）*100
     */
    @ApiModelProperty("输入电压6路（V）*100")
    private String vpv6;
    /**
     * 输入电压7路（V）*100
     */
    @ApiModelProperty("输入电压7路（V）*100")
    private String vpv7;
    /**
     * 输入电压8路（V）*100
     */
    @ApiModelProperty("输入电压8路（V）*100")
    private String vpv8;
    /**
     * 输入电压9路（V）*100
     */
    @ApiModelProperty("输入电压9路（V）*100")
    private String vpv9;
    /**
     * 输入电压10路（V）*100
     */
    @ApiModelProperty("输入电压10路（V）*100")
    private String vpv10;
    /**
     * 输入电压11路（V）*100
     */
    @ApiModelProperty("输入电压11路（V）*100")
    private String vpv11;
    /**
     * 输入电压12路（V）*100
     */
    @ApiModelProperty("输入电压12路（V）*100")
    private String vpv12;
    /**
     * 输出电流1路（A）*100
     */
    @ApiModelProperty("输出电流1路（A）*100")
    private String iac1;
    /**
     * 输出电流2路（A）*100
     */
    @ApiModelProperty("输出电流2路（A）*100")
    private String iac2;
    /**
     * 输出电流3路（A）*100
     */
    @ApiModelProperty("输出电流3路（A）*100")
    private String iac3;
    /**
     * 输出电压1路（V）*100
     */
    @ApiModelProperty("输出电压1路（V）*100")
    private String vac1;
    /**
     * 输出电压2路（V）*100
     */
    @ApiModelProperty("输出电压2路（V）*100")
    private String vac2;
    /**
     * 输出电压3路（V）*100
     */
    @ApiModelProperty("输出电压3路（V）*100")
    private String vac3;
    /**
     * 温度（℃）
     */
    @Size(max = 12, message = "编码长度不能超过12")
    @ApiModelProperty("温度（℃）")
    @Length(max = 12, message = "编码长度不能超过12")
    private String temp;
    /**
     * 频率1（Hz）*100
     */
    @ApiModelProperty("频率1（Hz）*100")
    private String fac1;
    /**
     * 频率2（Hz）*100
     */
    @ApiModelProperty("频率2（Hz）*100")
    private String fac2;
    /**
     * 频率3（Hz）*100
     */
    @ApiModelProperty("频率3（Hz）*100")
    private String fac3;

    /**
     * pv1功率
     */
    @ApiModelProperty("pv1功率")
    private String pvPower1;
    /**
     * pv2功率
     */
    @ApiModelProperty("pv2功率")
    private String pvPower2;
    /**
     * pv3功率
     */
    @ApiModelProperty("pv3功率")
    private String pvPower3;
    /**
     * pv4功率
     */
    @ApiModelProperty("pv4功率")
    private String pvPower4;
    /**
     * pv5功率
     */
    @ApiModelProperty("pv5功率")
    private String pvPower5;
    /**
     * pv6功率
     */
    @ApiModelProperty("pv6功率")
    private String pvPower6;
    /**
     * pv7功率
     */
    @ApiModelProperty("pv7功率")
    private String pvPower7;
    /**
     * pv8功率
     */
    @ApiModelProperty("pv8功率")
    private String pvPower8;
    /**
     * pv9功率
     */
    @ApiModelProperty("pv9功率")
    private String pvPower9;
    /**
     * pv10功率
     */
    @ApiModelProperty("pv10功率")
    private String pvPower10;
    /**
     * pv11功率
     */
    @ApiModelProperty("pv11功率")
    private String pvPower11;
    /**
     * pv12功率
     */
    @ApiModelProperty("pv12功率")
    private String pvPower12;

//    public void setTodayElectricity() {
//        if (this.todayElectricity != null) {
//            new BigDecimal(todayElectricity).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//    public void setMonthElectricity() {
//        if (this.monthElectricity != null) {
//            new BigDecimal(monthElectricity).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//    public void setYearElectricity() {
//        if (this.yearElectricity != null) {
//            new BigDecimal(yearElectricity).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//    public void setTotalElectricity() {
//        if (this.totalElectricity != null) {
//            new BigDecimal(totalElectricity).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//    public void setIpv1() {
//        if (this.ipv1 != null) {
//            new BigDecimal(ipv1).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//    public void setIpv2() {
//        if (this.ipv2 != null) {
//            new BigDecimal(ipv2).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//    public void setIpv3() {
//        if (this.ipv3 != null) {
//            new BigDecimal(ipv3).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//    public void setIpv4() {
//        if (this.ipv4 != null) {
//            new BigDecimal(ipv4).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//    public void setIpv5() {
//        if (this.ipv5 != null) {
//            new BigDecimal(ipv5).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//    public void setIpv6() {
//        if (this.ipv6 != null) {
//            new BigDecimal(ipv6).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//    public void setIpv7() {
//        if (this.ipv7 != null) {
//            new BigDecimal(ipv7).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//    public void setIpv8() {
//        if (this.ipv8 != null) {
//            new BigDecimal(ipv8).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//    public void setIpv9() {
//        if (this.ipv9 != null) {
//            new BigDecimal(ipv9).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//    public void setIpv10() {
//        if (this.ipv10 != null) {
//            new BigDecimal(ipv10).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//    public void setIpv11() {
//        if (this.ipv11 != null) {
//            new BigDecimal(ipv11).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//    public void setIpv12() {
//        if (this.ipv12 != null) {
//            new BigDecimal(ipv12).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//    public void setVpv1() {
//        if (this.vpv1 != null) {
//            new BigDecimal(vpv1).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//    public void setVpv2() {
//        if (this.vpv2 != null) {
//            new BigDecimal(vpv2).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//    public void setVpv3() {
//        if (this.vpv3 != null) {
//            new BigDecimal(vpv3).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//    public void setVpv4() {
//        if (this.vpv4 != null) {
//            new BigDecimal(vpv4).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//    public void setVpv5() {
//        if (this.vpv5 != null) {
//            new BigDecimal(vpv5).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//    public void setVpv6() {
//        if (this.vpv6 != null) {
//            new BigDecimal(vpv6).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//    public void setVpv7() {
//        if (this.vpv7 != null) {
//            new BigDecimal(vpv7).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//    public void setVpv8() {
//        if (this.vpv8 != null) {
//            new BigDecimal(vpv8).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//    public void setVpv9() {
//        if (this.vpv9 != null) {
//            new BigDecimal(vpv9).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//    public void setVpv10() {
//        if (this.vpv10 != null) {
//        }
//    }
//
//    public void setVpv11() {
//        if (this.vpv11 != null) {
//            new BigDecimal(vpv11).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//
//    public void setVpv12() {
//        if (this.vpv12 != null) {
//            new BigDecimal(vpv12).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//    public void setIac1() {
//        if (this.iac1 != null) {
//            new BigDecimal(iac1).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//    public void setIac2() {
//        if (this.iac2 != null) {
//            new BigDecimal(iac2).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//    public void setIac3() {
//        if (this.iac3 != null) {
//            new BigDecimal(iac3).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//    public void setVac1() {
//        if (this.vac1 != null) {
//            new BigDecimal(vac1).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//    public void setVac2() {
//        if (this.vac2 != null) {
//            new BigDecimal(vac2).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//    public void setVac3() {
//        if (this.vac3 != null) {
//            new BigDecimal(vac3).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//    public void setFac1() {
//        if (this.fac1 != null) {
//            new BigDecimal(fac1).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//    public void setFac2() {
//        if (this.fac2 != null) {
//            new BigDecimal(fac2).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }
//
//    public void setFac3() {
//        if (this.fac2 != null) {
//            new BigDecimal(fac3).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
//        }
//    }

}
