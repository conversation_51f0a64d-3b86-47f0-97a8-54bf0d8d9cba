package com.bto.commons.pojo.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.validator.constraints.Length;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/4/27 11:55
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EdgeServerDTO extends PageDTO{

    /**
     * 电站Uid
     */
    @ApiModelProperty("电站Uid")
    @ExcelIgnore
    private String plantUid;
    /**
     * 数据日期
     */
    @ApiModelProperty("数据日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @ExcelIgnore
    private String collectDate;
    /**
     * 运维器IMEI
     */
    @ApiModelProperty("运维器IMEI")
    @ExcelProperty("运维器IMEI")
    private String imei;
    /**
     * 光伏板发电,PV发电,当日发电量(以最小单位存储,不保留小数后两位)
     */
    @ApiModelProperty("光伏板发电,PV发电,当日发电量(以最小单位存储,不保留小数后两位)")
    @ExcelProperty("光伏板发电")
    private String generationElectricity;
    /**
     * 负载用电,当日用电量
     */
    @ApiModelProperty("负载用电,当日用电量)")
    @ExcelProperty("负载用电")
    private String useElectricity;
    /**
     * 自发自用
     */
    @ApiModelProperty("自发自用")
    @ExcelProperty("自发自用")
    private String deprecatedGenerateElectricity;
    /**
     * 买第三方电量(以最小单位存储,不保留小数后两位)
     */
    @ApiModelProperty("买第三方电量")
    @ExcelProperty("买电量")
    private String buyElectricity;
    /**
     * 卖自发电量(以最小单位存储,不保留小数后两位)
     */
    @ApiModelProperty("卖自发电量")
    @ExcelProperty("卖自发电量")
    private String sellElectricity;
    /**
     * A点电压
     */
    @ApiModelProperty("A点电压")
    @ExcelIgnore
    private String apv;
    /**
     * B点电压
     */
    @ApiModelProperty("B点电压")
    @ExcelIgnore
    private String bpv;
    /**
     * C点电压
     */
    @ApiModelProperty("C点电压")
    @ExcelIgnore
    private String cpv;
    /**
     * D点电压
     */
    @ApiModelProperty("D点电压")
    @ExcelIgnore
    private String dpv;
    /**
     * （当前）正向有功总电能（impep）
     */
    @ApiModelProperty("（当前）正向有功总电能（impep）")
    @ExcelIgnore
    private String impep;
    /**
     * （当前）反向有功总电能（expep）
     */
    @ApiModelProperty("（当前）反向有功总电能（expep）")
    @ExcelIgnore
    private String expep;
    /**
     * 三晶数据标识（三晶：0  ，自取：1）
     */
    @ApiModelProperty("三晶数据标识（三晶：0  ，自取：1）")
    @Length(max= 0,message="编码长度不能超过0")
    @ExcelIgnore
    private String state;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @ExcelIgnore
    private String createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @ExcelProperty("更新时间")
    private String updateTime;


    /**
     * 电站名称
     */
    @ApiModelProperty("电站名称")
    @ExcelProperty("电站名称")
    private String plantName;


    /**
     * 运维器启动状态
     */
    @ApiModelProperty("运维器启动状态")
    @ExcelProperty("运维器启动状态")
    private String status;

    /**
     * 运维器运行状态
     */
    @ApiModelProperty("运维器运行状态")
    @ExcelProperty("运维器运行状态")
    private String enable;

    /**
     * 运维器sn
     */
    @ApiModelProperty("运维器sn")
    @ExcelProperty("运维器sn")
    private String edgeServerSN;


    public void setGenerationElectricity(String generationElectricity) {
        if (generationElectricity != null) {
            this.generationElectricity =  new BigDecimal(generationElectricity).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
        }else {
            this.generationElectricity = BigDecimal.ZERO.toString();
        }
    }
    public String getUseElectricity() {
        if (this.useElectricity != null) {
            return new BigDecimal(useElectricity).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();

        }
        return this.useElectricity;
    }

    public String getDeprecatedGenerateElectricity() {
        if (this.deprecatedGenerateElectricity != null) {
            return new BigDecimal(deprecatedGenerateElectricity).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
        }
        return this.deprecatedGenerateElectricity;
    }

    public String getBuyElectricity() {
        if (this.buyElectricity != null) {
            return new BigDecimal(buyElectricity).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
        }
        return this.buyElectricity;
    }

    public String getSellElectricity() {
        if (this.sellElectricity!=null) {
            return new BigDecimal(sellElectricity).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
        }
        return this.sellElectricity;
    }

    public String getApv() {
        if (this.apv != null) {
            return new BigDecimal(apv).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
        }
        return this.apv;
    }

    public String getBpv() {
        if (this.apv != null) {
            return new BigDecimal(bpv).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
        }
        return this.apv;
    }

    public String getCpv() {
        if (cpv != null) {
            return new BigDecimal(cpv).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
        }
        return this.cpv;
    }

    public String getDpv() {
        if (this.dpv != null) {
            return new BigDecimal(dpv).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString();
        }
        return this.dpv;
    }
}
