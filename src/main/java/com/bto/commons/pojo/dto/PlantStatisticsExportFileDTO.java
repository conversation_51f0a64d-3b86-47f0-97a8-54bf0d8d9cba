package com.bto.commons.pojo.dto;

import com.bto.commons.pojo.vo.RequireParamsDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/28 11:22
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PlantStatisticsExportFileDTO extends ExportFileDTO implements Serializable {
    private static final long serialVersionUID = 9120351582818175777L;
    @ApiModelProperty("电站名称（模糊查询）")
    private String plantName;
    @ApiModelProperty("电站地址（模糊查询）")
    private String address;
    @ApiModelProperty("建站时间-开始区间")
    private String createStartTime;
    @ApiModelProperty("建站时间-结束区间")
    private String createEndTime;
    @ApiModelProperty("发电时间-开始区间")
    private String powerStartTime;
    @ApiModelProperty("发电时间-结束区间")
    private String powerEndTime;
    @ApiModelProperty("当前页码")
    private String currentPage;
    @ApiModelProperty("页面大小")
    private String pageSize;
    @ApiModelProperty("邮箱集合（发送邮件时传递）")
    private List<String> emailList;
    @ApiModelProperty(hidden = true)
    RequireParamsDTO userInfo;
}
