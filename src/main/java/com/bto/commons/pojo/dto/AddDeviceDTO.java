package com.bto.commons.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel("设备添加实体")
public class AddDeviceDTO implements Serializable {
    private static final long serialVersionUID = 1986198364313155777L;
    @ApiModelProperty(value = "电站uid",required = true)
    private String plantUid;
    @ApiModelProperty(value = "设备编号（设备SN码）||逆变器和非三晶运维器",required = true)
    private String deviceId;
    @ApiModelProperty(value = "运维器通讯模块imei || 等同于三晶 wisdom_device_sn",required = true)
    private String imei;
    @ApiModelProperty(value = "设备类型(1:逆变器、2:运维器、3:电表,，4：气象站,11、配电柜，12、温湿度、烟感采集器",required = true)
    private String deviceType;
    @ApiModelProperty(value = "设备名称（目前电表独有：如总配电、宿舍楼、办公室、XX厂区（用于区分电表监控区域））")
    private String deviceName;
    @ApiModelProperty(value = "厂家",required = true)
    private String manufacturer;
    @ApiModelProperty(value = "型号",required = true)
    private String module;
    @ApiModelProperty(value = "项目专项(1:户用，2：整县-河源)",required = true)
    private String projectSpecial;
    @ApiModelProperty(value = "设备地址（逻辑地址)")
    private String deviceAddress;
    @ApiModelProperty("设备pc码")
    private String devicePc;
    @ApiModelProperty("ICCID (物联网卡号)")
    private String iccid;
    @ApiModelProperty("运维器是否采集配电箱数据（1：是 ；0：否）")
    private String cluster;
    @ApiModelProperty("逆变器接入类型（0：并网，1：储能）")
    private String receiveType;
    @ApiModelProperty("电流互感器变比(电表独有)")
    private Integer currentTransformer;
    @ApiModelProperty("电压互感器变比(电表独有)")
    private Integer potentialTransformer;

}