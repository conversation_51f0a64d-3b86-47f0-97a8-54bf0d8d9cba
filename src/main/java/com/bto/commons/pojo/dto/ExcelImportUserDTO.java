package com.bto.commons.pojo.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.bto.commons.enums.UserEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/12/5 9:19
 */
@Data
public class ExcelImportUserDTO implements Serializable {
    @ExcelProperty("用户UID")
    @ApiModelProperty("用户UID")
    private String userUID;
    @ExcelProperty("用户名")
    @ApiModelProperty("用户名")
    private String userName;
    @ExcelProperty("用户手机号")
    @ApiModelProperty("用户手机号")
    private String userPhone;
    @ExcelProperty("用户邮箱")
    @ApiModelProperty("用户邮箱")
    private String userEmail;

    // @ApiModelProperty("用户状态")
    // @ExcelProperty("用户状态")
    // private String userStatusLabel;
    // private String userStatus;
    @ExcelProperty("用户类型")
    @ApiModelProperty("用户类型")
    private String userTypeLabel;
    private String userType;

    @ApiModelProperty("角色ID")
    private Long roleID;
    @ExcelProperty("角色名称")
    @ApiModelProperty("角色名称")
    private String roleName;

    @ExcelProperty("项目专项")
    @ApiModelProperty("项目专项")
    private String projectName;
    @ApiModelProperty("项目编号")
    private Integer projectID;


    // public String getUserStatus() {
    //     if (UserEnum.USER_STATUS_ENABLE.getName().equals(this.userStatusLabel)) {
    //         return UserEnum.USER_STATUS_ENABLE.getCode();
    //     } else if (UserEnum.USER_STATUS_DISENABLE.getName().equals(this.userStatusLabel)) {
    //         return UserEnum.USER_STATUS_DISENABLE.getCode();
    //     } else {
    //         return userStatus;
    //     }
    // }

    public String getUserType() {
        if (UserEnum.USER_OF_ENTERPRISE.getName().equals(this.userTypeLabel)) {
            return UserEnum.USER_OF_ENTERPRISE.getCode();
        } else if (UserEnum.USER_OF_INDIVIDUAL.getName().equals(userType)) {
            return UserEnum.USER_OF_INDIVIDUAL.getCode();
        } else {
            return userType;
        }
    }
}
