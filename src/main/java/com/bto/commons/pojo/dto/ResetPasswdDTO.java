package com.bto.commons.pojo.dto;

import cn.hutool.core.date.DateTime;
import com.bto.commons.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/12/26 17:20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ResetPasswdDTO {
    private String code;

    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN,timezone = "Asia/Shanghai")
    private DateTime createTime;

    // private long exTime; // 添加用于记录过期时间的属性
}
