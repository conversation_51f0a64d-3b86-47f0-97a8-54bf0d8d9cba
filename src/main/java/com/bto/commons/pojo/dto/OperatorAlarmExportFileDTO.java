package com.bto.commons.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/6/28 17:22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OperatorAlarmExportFileDTO extends ExportFileDTO implements Serializable {

    private static final long serialVersionUID = -8418250108738798819L;
    @ApiModelProperty(name="plantUid",value = "电站编号")
    private String plantUid;
    @ApiModelProperty(name="plantName",value = "电站名称(模糊查询)")
    private String plantName;
    @ApiModelProperty(name="address",value = "电站地址(模糊查询)")
    private String address;
    @ApiModelProperty(value = "查询条件：开始时间",notes = "默认为当天")
    private String startTime;
    @ApiModelProperty(value ="查询条件：结束时间",notes = "默认为当天")
    private String endTime;
    @ApiModelProperty("当前页码")
    private String currentPage;
    @ApiModelProperty("页面大小")
    private String pageSize;

}
