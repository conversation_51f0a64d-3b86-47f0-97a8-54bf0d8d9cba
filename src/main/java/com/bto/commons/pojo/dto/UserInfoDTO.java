package com.bto.commons.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户信息
 * <AUTHOR>
 * @date 2023/5/19 9:17
 */
@Data
@ApiModel("用户信息")
public class UserInfoDTO implements Serializable {
    private static final long serialVersionUID = 1154800974170590658L;
    @ApiModelProperty("用户编号")
    private String userUid;
    @ApiModelProperty("用户名")
    private String userName;
    @ApiModelProperty("用户密码")
    private String userPassword;
    @ApiModelProperty("用户类型")
    private String userType;
    @ApiModelProperty("用户手机号")
    private String userPhone;
    @ApiModelProperty("用户邮箱")
    private String userEmail;
    @ApiModelProperty("用户状态")
    private Integer userStatus;
    @ApiModelProperty("用户角色ID")
    private String roleID;
    @ApiModelProperty("所属项目ID")
    private Integer projectID;
    @ApiModelProperty("合同ID")
    private String contractID;

}
