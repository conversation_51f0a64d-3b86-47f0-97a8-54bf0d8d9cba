package com.bto.commons.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/5/18 17:31
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RoleQueryDTO implements Serializable {

    private static final long serialVersionUID = 283262147469764966L;
    @ApiModelProperty("角色名称")
    private String roleName;
    @ApiModelProperty("当前页面")
    private Integer currentPage;
    @ApiModelProperty("页面大小")
    private Integer pageSize;
}
