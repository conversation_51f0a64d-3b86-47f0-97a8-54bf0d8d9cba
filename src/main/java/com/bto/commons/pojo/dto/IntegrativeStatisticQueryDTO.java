package com.bto.commons.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/6/14 10:15
 */
@Data
@ApiModel("综合统计查询条件")
public class IntegrativeStatisticQueryDTO implements Serializable {
    private static final long serialVersionUID = 1198148611463869981L;
    @ApiModelProperty("项目id")
    private String projectId;
    @ApiModelProperty("建站开始时间")
    private String createStartTime;
    @ApiModelProperty("建站结束时间")
    private String createEndTime;
    @ApiModelProperty("数据开始时间")
    private String dataStartTime;
    @ApiModelProperty("数据结束时间")
    private String dataEndTime;
    @ApiModelProperty(value = "电价" )
    private String electricityPrice;

}
