package com.bto.commons.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 多功能仪表数据查询
 *
 * <AUTHOR>
 * @since 2024-10-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "多功能仪表数据查询")
public class FunctionalInstrumentQuery extends PageDTO {
    @ApiModelProperty(example = "aaa12,bb23")
    List<String> deviceId;
}