package com.bto.commons.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/17 14:42
 */
@Data
@ApiModel("用户信息查询条件")
public class UserQueryDTO extends PageDTO implements Serializable {
    private static final long serialVersionUID = -4412835833408642864L;
    @ApiModelProperty("用户名")
    private String userName;
    @ApiModelProperty("用户类型")
    private String userType;
    @ApiModelProperty("用户手机号")
    private String userPhone;
    @ApiModelProperty("用户状态")
    private String userStatus;
    @ApiModelProperty("项目专项")
    private List<String> projectSpecial;
    @ApiModelProperty("角色ID")
    private String roleId;
}
