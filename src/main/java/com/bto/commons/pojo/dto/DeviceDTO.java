package com.bto.commons.pojo.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/4/11 8:27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeviceDTO {

    @TableField("plant_uid")
    @ApiModelProperty("电站Uid")
    private String plantUid;

    @TableField("device_id")
    @ApiModelProperty("设备编号（设备SN码）||逆变器和非三晶运维器")
    private String deviceId;

    @TableField("imei")
    @ApiModelProperty("运维器通讯模块imei || 等同于三晶 wisdom_device_sn")
    private String imei;

    @TableField("device_type")
    @ApiModelProperty("设备类型(1:逆变器、2:运维器)")
    private Integer deviceType;

    @TableField("manufacturer")
    @ApiModelProperty("厂家")
    private String manufacturer;

    @TableField("module")
    @ApiModelProperty("型号")
    private String module;

    @TableField("project_special")
    @ApiModelProperty("项目专项(1:户用，2：整县-河源)")
    private Integer projectSpecial;

    @TableField("device_address")
    @ApiModelProperty("设备地址")
    private String deviceAddress;

    @TableField("device_pc")
    @ApiModelProperty("设备pc码")
    private String devicePc;

    @TableField("cimi")
    @ApiModelProperty("cimi（三晶逆变器也存在iotnum）")
    private String cimi;

    @TableField("iccid")
    @ApiModelProperty("ICCID (物联网卡号)")
    private String iccid;

    @TableField("start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @ApiModelProperty("激活时间")
    private Date startTime;

    @TableField("end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @ApiModelProperty("到期时间/质保时间")
    private Date endTime;

    @TableField("software_version")
    @ApiModelProperty("软件版本号")
    private String softwareVersion;

    @TableField("display_version")
    @ApiModelProperty("显示版本号")
    private String displayVersion;

    @TableField("control_version")
    @ApiModelProperty("控制版本号")
    private String controlVersion;

    @TableField("is_deleted")
    @ApiModelProperty("状态（0：存在，1：删除）")
    private Integer isDeleted;

    @TableField("creator")
    @ApiModelProperty("工程师id(创建者)")
    private String creator;

    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @ApiModelProperty("数据创建时间")
    private Date createTime;

    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @ApiModelProperty("数据更新时间")
    private Date updateTime;

    @TableField("receive_type")
    @ApiModelProperty("逆变器接入类型（0：并网，1：储能）")
    private String receiveType;
}
