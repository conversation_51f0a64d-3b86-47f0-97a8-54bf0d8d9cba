package com.bto.commons.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> by zhb on 2024/1/11.
 */

@Data
@ApiModel("角色分配用户")
public class RoleUserDTO {
    @ApiModelProperty("角色ID")
    @NotNull(message="角色ID不能为空")
    private String roleId;

    @ApiModelProperty("用户ID集合")
    @NotNull(message="用户ID集合不能为空")
    private List<String> userIdList;
}
