package com.bto.commons.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2024-02-29 15:03:52
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("查询条件")
public class PowerPlantInfoQueryDTO extends PageDTO implements Serializable {
    private static final long serialVersionUID = -616967216704799528L;
    @ApiModelProperty("电站编号")
    private String plantUid;
    @ApiModelProperty("电站名称 [String集合]")
    private List<String> plantName;
    @ApiModelProperty(value = "省份")
    private List<String> province;
    @ApiModelProperty(value = "市/州")
    private List<String> city;
    @ApiModelProperty("创建时间开始区间")
    private String startCreateTime;
    @ApiModelProperty("创建时间结束区间")
    private String endCreateTime;
    @ApiModelProperty("项目号")
    private String projectSpecial;
}