package com.bto.commons.pojo.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 电站统计查询条件-实体
 * <AUTHOR>
 * @date 2023/5/8 8:21
 */
@Data
@ApiModel("电站统计查询条件")
public class PlantStatisticsQueryDTO extends PageDTO implements Serializable {
    private static final long serialVersionUID = 9194957275228244262L;
    @ApiModelProperty("电站名称（模糊查询）")
    private String plantName;
    @ApiModelProperty("电站地址（模糊查询）")
    private String address;
    @ApiModelProperty("建站时间-开始区间")
    private String createStartTime;
    @ApiModelProperty("建站时间-结束区间")
    private String createEndTime;
    @ApiModelProperty("发电时间-开始区间")
    private String powerStartTime;
    @ApiModelProperty("发电时间-结束区间")
    private String powerEndTime;
    @ApiModelProperty("电站状态（0：离线，1：正常运行，2：告警运行,3:自检提示,5:逆变器夜间离线）")
    private List<String> plantStatus;
    @ExcelProperty("配电箱状态(1——正常,2——配电箱开关故障,3——电表箱开关故障,4——市电停电,5——电表箱开关采样异常,6——市电采样异常,7——市电、电表箱开关采样异常,8——过流开关故障,9——失压开关故障,10——失压开关采样异常,11——市电、失压开关采样异常,-1——无状态)")
    private String powerDistributor;
    private String projectId;

}