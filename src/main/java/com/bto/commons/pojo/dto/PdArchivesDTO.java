package com.bto.commons.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(description = "配电室档案")
public class PdArchivesDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	@Schema(description = "配电室名称")
	private String pdName;

	@Schema(description = "配电室图片地址")
	private String pdPhotoUrl;

	@Schema(description = "配电室类型")
	private String pdType;

	@Schema(description = "配电室型号")
	private String pdModule;

	@Schema(description = "配电室地址")
	private String pdAddress;

}