package com.bto.commons.pojo.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.TableField;
import com.bto.commons.enums.ProjectTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 电站效率详情视图层类
 *
 * <AUTHOR>
 * @date 2023/4/3 16:24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("电站视图层类")
public class PlantVO implements Serializable {
    private static final long serialVersionUID = 1693227487289728686L;

    @Length(max = 125, message = "编码长度不能超过125")
    @ApiModelProperty(value = "电站Uid", example = "0004544C-034B-4C3E-B54F-3600720D863E")
    private String plantUid;

    @ApiModelProperty(value = "电站名称", example = "杨惠良")
    private String plantName;

    @ApiModelProperty("装机容量(以最小单位Wp存储)")
    private String plantCapacity;

    @ApiModelProperty("电站朝向（0：不一致，1：一致）")
    private String orientation;

    @ApiModelProperty("电站状态")
    private String plantStatus;

    @ApiModelProperty(value = "电站类型",example = "0")
    private String plantTypeId;

    @ApiModelProperty("逆变器数量")
    private Integer inverterNum;

    @TableField(value = "power_distributor")
    @NotNull(message = "[配电箱状态]不能为空")
    @ApiModelProperty(value = "配电箱状态", example = "0")
    private String powerDistributor;

    @ApiModelProperty(value = "国家", example = "中国")
    private String country;

    @ApiModelProperty(value = "省份", example = "广东省")
    private String province;

    @ApiModelProperty(value = "市/州", example = "东莞市")
    private String city;

    @ApiModelProperty("县/区")
    private String area;

    @ApiModelProperty("镇/街道")
    private String town;

    @ApiModelProperty("详细地址")
    private String address;

    @ApiModelProperty("经度")
    private String longitude;

    @ApiModelProperty("纬度")
    private String latitude;

    @ApiModelProperty("功率(以最小单位W存储)")
    private Integer power;

    @ApiModelProperty("日发电量")
    private String todayElectricity;

    @ApiModelProperty("月发电量")
    private String monthElectricity;

    @ApiModelProperty("年发电量")
    private String yearElectricity;

    @ApiModelProperty("累计发电量")
    private String totalElectricity;

    @ApiModelProperty("电站电量数据接收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private String receiveTime;

    @ApiModelProperty("出售自发电价")
    private BigDecimal salePrice;

    @ApiModelProperty(value = "项目分类id",example = "0")
    private String projectSpecial;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("电表编号")
    private String meterId;

    @ApiModelProperty("用户id")
    private String userUid;

    @ApiModelProperty("电站工作效率 ")
    private String efficiency;

    @ApiModelProperty("等效植树")
    private String equivalentTreePlanting;

    @ApiModelProperty("二氧化碳")
    private String carbonDioxide;

    @ApiModelProperty("当日收益")
    private String todayEarning;

    @ApiModelProperty("累计收益")
    private String totalEarning;

    @ApiModelProperty("当日告警次数")
    private String todayAlarmNum;

    @ApiModelProperty("峰值功率")
    private String peakPower;

    @ApiModelProperty("在线逆变器数量")
    private String onlineInverterStats;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @ApiModelProperty("创建时间")
    private String createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @ApiModelProperty("质保时间")
    private String warrantyTime;
    @ApiModelProperty("用户名称")
    private String userName;
    @ApiModelProperty("用户电话")
    private String userPhone;
    @ApiModelProperty("逆变器sn列表")
    private List<String> inverterSN;
    @ApiModelProperty("日等效小时")
    private String dailyEfficiencyPerHour;
    @ApiModelProperty("年等效小时")
    private String yearlyEfficiencyPerHour;
    @ApiModelProperty("创建开始时间")
    private String createTimeStart;
    @ApiModelProperty("创建结束时间")
    @ExcelIgnore
    private String createTimeEnd;
    @ApiModelProperty(value = "查询多个电站状态")
    @ExcelIgnore
    private List<String> multiPlantStatus;
    @ApiModelProperty("装机容量最小值")
    private String minPlantCapacity;
    @ApiModelProperty("装机容量最大值")
    private String maxPlantCapacity;
    {
        this.plantTypeId = "0";
        this.projectSpecial = "0";
    }

    public void setProjectName(String projectId) {
        this.projectName = ProjectTypeEnum.getProjectNameById(projectId);
    }
}