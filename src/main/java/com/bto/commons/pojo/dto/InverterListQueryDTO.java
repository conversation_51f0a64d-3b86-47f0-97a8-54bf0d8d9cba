package com.bto.commons.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/15 15:24
 */
@Data
@ApiModel("逆变器列表查询条件对象")
public class InverterListQueryDTO extends PageDTO implements Serializable {
    private static final long serialVersionUID = -4489142028681111885L;
    @ApiModelProperty("电站编号")
    private String plantUid;
    @ApiModelProperty("设备编号(设备SN码)")
    private String deviceId;
    @ApiModelProperty("逆变器状态(一个或多个)")
    private List<String> multiInverterStatus;
    @ApiModelProperty("电站名称")
    private String plantName;


}
