package com.bto.commons.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2023/11/28 16:40
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "附件管理查询")
public class AttachmentQuery extends PageDTO {
    private static final long serialVersionUID = 3772430293510576749L;
    @ApiModelProperty(value = "附件名称")
    private String name;

    @ApiModelProperty(value = "存储平台")
    private String platform;
}
