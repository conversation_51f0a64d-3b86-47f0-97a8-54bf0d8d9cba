package com.bto.commons.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import springfox.documentation.annotations.ApiIgnore;

/**
* 配电柜数据查询
*
* <AUTHOR> 
* @since  2024-07-24
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "配电柜数据查询")
public class CounterQuery extends PageDTO{
    @ApiModelProperty(value = "电站uid")
    private String plantUid;
    @ApiModelProperty(value = "设备编号（设备SN码）||逆变器和非三晶运维器")
    private String deviceId;
    @ApiModelProperty(value = "1:逆变器、2:运维器、3:电表,4：气象站,10、配电房,11、配电柜，12、温湿度、烟感采集器",hidden = true)
    private String deviceType;
    @ApiModelProperty(value = "电站名")
    private String plantName;
    @ApiModelProperty(value = "配电柜id")
    private String counterId;
    @ApiModelProperty(value = "配电柜名")
    private String counterName;
}