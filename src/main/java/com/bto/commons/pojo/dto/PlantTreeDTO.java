package com.bto.commons.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @date 2023/4/4 17:22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PlantTreeDTO {
    @NotBlank(message = "[电站Uid]不能为空")
    @Size(max = 125, message = "编码长度不能超过125")
    @ApiModelProperty("电站Uid")
    @Length(max = 125, message = "编码长度不能超过125")
    private String plantUid;

    /**
     * 电站名称
     */
    @NotBlank(message = "[电站名称]不能为空")
    @Size(max = 255, message = "编码长度不能超过255")
    @ApiModelProperty("电站名称")
    @Length(max = 255, message = "编码长度不能超过255")
    private String plantName;
}
