package com.bto.commons.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/6/29 16:13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper=false)
public class InverterAlarmExportFileDTO extends ExportFileDTO implements Serializable {

    private static final long serialVersionUID = 2815704677126837037L;
    @ApiModelProperty(value = "查询告警类型: alarm:查询告警 selfCheck:查询自检提示",required = true)
    private String alarmType;

    @ApiModelProperty(value = "需要查询的电站编号")
    private String plantUid;
    @ApiModelProperty(value = "需要查询的电站名称(模糊查询)")
    private String plantName;
    @ApiModelProperty(value = "查询条件：开始时间",notes = "默认为当天")
    private String startTime;
    @ApiModelProperty(value ="查询条件：结束时间",notes = "默认为当天")
    private String endTime;
    @ApiModelProperty("当前页码")
    private String currentPage;
    @ApiModelProperty("页面大小")
    private String pageSize;
    @ApiModelProperty("筛选条件1：告警信息")
    private String alarmInfo;
    @ApiModelProperty("筛选条件2：事件描述")
    private String alarmMean;
    @ApiModelProperty("筛选条件3：级别")
    private String alarmLevel;
    @ApiModelProperty("筛选条件4：状态")
    private String alarmStatus;
}
