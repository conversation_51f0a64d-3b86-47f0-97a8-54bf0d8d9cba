package com.bto.commons.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/6/17 16:23
 */
@Data
@ApiModel("分页信息条件")
@EqualsAndHashCode
public class PageDTO implements Serializable {
    private static final long serialVersionUID = -7169732208619385253L;
    @ApiModelProperty("当前页")
    private Integer currentPage;
    @ApiModelProperty("页面大小")
    private Integer pageSize;
    @ApiModelProperty("排序字段")
    private String order;
    @ApiModelProperty("是否升序")
    private Boolean isAsc;

    {
        this.isAsc = false;
        this.currentPage = 1;
        this.pageSize = 10;
    }


}
