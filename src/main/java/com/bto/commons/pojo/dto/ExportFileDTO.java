package com.bto.commons.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/13 16:46
 */
@Data
@ApiModel("导出文件-必备条件")
public class ExportFileDTO implements Serializable {
    private static final long serialVersionUID = 5200500304323306486L;
    @ApiModelProperty("选择导出的列名")
    private List<String> columnsList;
    @ApiModelProperty("表格名称")
    private String sheetName;
}
