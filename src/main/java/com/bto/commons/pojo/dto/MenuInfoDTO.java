package com.bto.commons.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 菜单信息
 *
 * <AUTHOR>
 * @date 2023/5/20 17:46
 */
@Data
@ApiModel("菜单信息")
public class MenuInfoDTO implements Serializable {
    private static final long serialVersionUID = -6133353057552154820L;
    @ApiModelProperty("ID --新增不传")
    private Integer id;
    @ApiModelProperty("父级ID --修改不传")
    @NotNull(message = "pid不能为空")
    private Integer pid;
    @ApiModelProperty("菜单名称")
    private String label;
    @ApiModelProperty("菜单地址")
    private String path;
    @ApiModelProperty("菜单权限")
    private String auth;
    @ApiModelProperty("菜单类型")
    private Integer type;
    @ApiModelProperty("打开方式")
    private String openStyle;
    @ApiModelProperty("菜单图标")
    private String icon;
    @ApiModelProperty("排序")
    private String sort;
    @ApiModelProperty("菜单版本")
    private String version;

    @Range(min = 0, max = 1)
    @ApiModelProperty("是否隐藏  默认为1-> false，为 0->true 的时候该路由不会在侧边栏出现")
    private Integer hidden;

    private String title;

    @Range(min = 0, max = 1)
    @ApiModelProperty("默认0-> true，为 1->false，则不会在面包屑中显示")
    private Integer breadcrumb;

    @Range(min = 0, max = 1)
    @ApiModelProperty("默认为1-> false，为 0->true 它则会固定在 tags-view 中")
    private Integer affix;

    @Range(min = 0, max = 1)
    @ApiModelProperty("只有一个时，会将那个子路由当做根路由显示在侧边栏")
    private Integer alwaysShow;

    @ApiModelProperty(" 当设置了该属性进入路由时，则会高亮 activeMenu 属性对应的侧边栏")
    private String activeMenu;

    @Range(min = 0, max = 1)
    @ApiModelProperty("默认为1-> false，为 0->true 时代表需要缓存，此时该路由和该页面都需要设置一致的 Name")
    private Integer keepAlive;

    @ApiModelProperty("组件")
    private String component;

    private String redirect;
}
