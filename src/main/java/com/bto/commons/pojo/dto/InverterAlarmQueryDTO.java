package com.bto.commons.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 逆变器告警信息查询条件
 * <AUTHOR>
 * @date 2023/4/20 15:07
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("逆变器告警查询条件")
public class InverterAlarmQueryDTO extends PageDTO implements Serializable {
    private static final long serialVersionUID = 2099383923882482448L;
    @ApiModelProperty(value = "查询告警类型: alarm:查询告警 selfCheck:查询自检提示",required = true)
    private String alarmType;
    @ApiModelProperty(value = "需要查询的电站编号")
    private String inverterSN;
    @ApiModelProperty(value = "需要查询的电站编号")
    private String plantUid;
    @ApiModelProperty(value = "需要查询的电站名称(模糊查询)")
    private String plantName;
    @ApiModelProperty(value = "查询条件：开始时间",notes = "默认为当天")
    private String startTime;
    @ApiModelProperty(value ="查询条件：结束时间",notes = "默认为当天")
    private String endTime;
    @ApiModelProperty("筛选条件1：告警信息")
    private String alarmInfo;
    @ApiModelProperty("筛选条件2：事件描述")
    private String alarmMean;
    @ApiModelProperty("筛选条件3：级别")
    private String alarmLevel;
    @ApiModelProperty("筛选条件4：状态（0未处理，1已处理 , 2失效）")
    private String alarmStatus;


}
