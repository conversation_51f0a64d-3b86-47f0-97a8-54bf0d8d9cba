package com.bto.commons.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/27 16:33
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper=false)
public class PlantExportFileDTO extends ExportFileDTO implements Serializable  {

    private static final long serialVersionUID = -9150625171810032506L;

    @ApiModelProperty("电站名称")
    private String plantName;

    @ApiModelProperty(value = "查询多个电站状态")
    private List<String > multiPlantStatus;

    @ApiModelProperty("配电箱状态")
    private String powerDistributor;

//    @ApiModelProperty("选择导出的列名")
//    private List<String> columnsList;
//
//    @ApiModelProperty("表格名称")
//    private String sheetName;

}
