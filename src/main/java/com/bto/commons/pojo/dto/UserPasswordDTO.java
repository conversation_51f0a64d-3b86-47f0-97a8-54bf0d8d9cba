package com.bto.commons.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/6/3 10:54
 */
@Data
@ApiModel("用户密码修改实体")
public class UserPasswordDTO implements Serializable {
    private static final long serialVersionUID = 1603531754548912479L;
    @ApiModelProperty(value = "旧密码",required = true,example = "暂时由前端使用MD5加密修改")
    private String oldPass;
    @ApiModelProperty(value = "新密码",required = true,example = "暂时由前端使用MD5加密修改")
    private String newPass;
}
