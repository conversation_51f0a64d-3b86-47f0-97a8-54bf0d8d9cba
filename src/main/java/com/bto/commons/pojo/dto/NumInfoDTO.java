package com.bto.commons.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/4/24 16:58
 */
@Data
@ApiModel("电站/逆变器统计数量信息")
public class NumInfoDTO implements Serializable {
    @ApiModelProperty("总数量")
    private String totalNum;
    @ApiModelProperty("在线/光精灵在线 数量")
    private String onlineNum;
    @ApiModelProperty("离线/光精灵离线 数量")
    private String offlineNum;
    @ApiModelProperty("正常数量")
    private String normalNum;
//    @ApiModelProperty("异常数量")
//    private String abnormalNum;
    @ApiModelProperty("告警数量")
    private String alarmNum;
    @ApiModelProperty("自检数量")
    private String selfCheckNum;

    @ApiModelProperty("逆变器 夜间离线数量")
    private String inverterShutdownNum;
    @ApiModelProperty("设备夜间离线数量")
    private String deviceShutdownNum;
    @ApiModelProperty("在线率")
    private String onlineRate;
    @ApiModelProperty("正常运行率")
    private String normalRate;

    {
        this.totalNum = "0";
        this.onlineNum = "0";
        this.offlineNum = "0";
        this.normalNum = "0";
        this.alarmNum = "0";
        this.selfCheckNum = "0";
        this.inverterShutdownNum = "0";
        this.deviceShutdownNum = "0";
        this.onlineRate = "0";
        this.normalRate = "0";
    }

    public String getOnlineRate() {
        return onlineRate;
    }

    public void setOnlineRate(String onlineNum,String totalNum) {
        BigDecimal b1 = new BigDecimal(onlineNum);
        BigDecimal b2 = new BigDecimal(totalNum);
         if (b2.compareTo(BigDecimal.ZERO)!=0){
            this.onlineRate = b1.divide(b2, 4, BigDecimal.ROUND_DOWN).toString();
        }else {
            this.onlineRate = "0";
        }
    }

    public void setNormalRate(String normalNum,String totalNum) {
        BigDecimal b1 = new BigDecimal(normalNum);
        BigDecimal b2 = new BigDecimal(totalNum);
        if (b2.compareTo(BigDecimal.ZERO)!=0){
            this.normalRate = b1.divide(b2, 4, BigDecimal.ROUND_DOWN).toString();
        }else {
            this.normalRate = "0";
        }
    }
}
