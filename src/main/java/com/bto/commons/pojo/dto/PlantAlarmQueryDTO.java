package com.bto.commons.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 电站告警列表查询条件
 *
 * <AUTHOR>
 * @date 2023/8/24 9:08
 */


@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("电站告警查询条件")
public class PlantAlarmQueryDTO extends PageDTO implements Serializable {
    private static final long serialVersionUID = 5870036166659247012L;
    @ApiModelProperty("设备编号")
    private String deviceId;
    @ApiModelProperty("电站名称")
    private String plantName;
    @ApiModelProperty("设备类型")
    private String deviceType;
    @ApiModelProperty("数据来源")
    private String source;
    @ApiModelProperty("报警启动时间的开始区间")
    private String startAlarmBeginTime;
    @ApiModelProperty("报警启动时间的结束区间")
    private String startAlarmFinishTime;
    @ApiModelProperty("告警信息[关键词]")
    private String alarmInfo;
    @ApiModelProperty("是否去重")
    private Boolean isDistinct;

    @ApiModelProperty(value = "是否分页",example = "true")
    private Boolean isPage;
    {
        isPage = true;
        isDistinct = false;
    }
}
