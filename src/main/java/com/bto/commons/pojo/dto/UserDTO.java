package com.bto.commons.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/5/31 9:48
 */
@Data
public class UserDTO implements Serializable {
        private static final long serialVersionUID = -4788644254942504791L;
        @ApiModelProperty("用户名")
        private String username;
        @ApiModelProperty("用户编号")
        private String userUid;
        @ApiModelProperty("用户类型")
        private String userType;
        @ApiModelProperty("角色编号")
        private String roleID;
        @ApiModelProperty("项目编号")
        private String projectID;
        @ApiModelProperty("项目logo地址")
        private String imgUrl;
        @ApiModelProperty("角色名称")
        private String roleName;
        @ApiModelProperty("项目名称")
        private String projectName;
        @ApiModelProperty("项目标题")
        private String projectTitle;
        @ApiModelProperty("布局")
        private String layout;
        @ApiModelProperty("大屏logo地址")
        private String screenLogo;
        @ApiModelProperty("头像")
        private String avatar;
}
