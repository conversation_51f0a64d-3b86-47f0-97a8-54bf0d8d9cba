package com.bto.commons.pojo.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
/**
 * <AUTHOR>
 * @date 2023/5/20 8:45
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class InverterInfoDTO implements Serializable {
    private static final long serialVersionUID = -7242638397050557745L;
    /**
     * 电站Uid
     */
    @TableId("plant_uid")
    @ApiModelProperty("电站Uid")
    @ExcelIgnore
    private String plantUid;

    /**
     * 设备编号（设备SN码）||逆变器和非三晶运维器
     */
    @TableField("device_id")
    @ApiModelProperty("设备编号（设备SN码）||逆变器和非三晶运维器")
    @ExcelProperty(value = "逆变器SN")
    private String deviceId;
    /**
     * 设备类型(1:逆变器、2:运维器)
     */
    @TableField("device_type")
    @ApiModelProperty("设备类型(1:逆变器、2:运维器)")
    @ExcelIgnore
    private Integer deviceType;
    /**
     * 厂家
     */
    @TableField("manufacturer")
    @ApiModelProperty("厂家")
    @ExcelIgnore
    private String manufacturer;
    /**
     * 型号
     */
    @TableField("module")
    @ApiModelProperty("型号")
    @ExcelIgnore
    private String module;
    /**
     * 项目专项(1:户用，2：整县-河源)
     */
    @TableField("project_special")
    @ApiModelProperty("项目专项(1:户用，2：整县-河源)")
    @ExcelIgnore
    private Integer projectSpecial;
    /**
     * 设备地址
     */
    @TableField("device_address")
    @ApiModelProperty("设备地址")
    @ExcelIgnore
    private String deviceAddress;
    /**
     * 设备pc码
     */
    @TableField("device_pc")
    @ApiModelProperty("设备pc码")
    @ExcelIgnore
    private String devicePc;
    /**
     * cimi（三晶逆变器也存在iotnum）
     */
    @TableField("cimi")
    @ApiModelProperty("cimi（三晶逆变器也存在iotnum）")
    @ExcelIgnore
    private String cimi;
    /**
     * ICCID (物联网卡号)
     */
    @TableField("iccid")
    @ApiModelProperty("ICCID (物联网卡号)")
    @ExcelIgnore
    private String iccid;
    /**
     * 激活时间
     */
    @TableField("start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @ApiModelProperty("激活时间")
    @ExcelIgnore
    private Date startTime;
    /**
     * 到期时间/质保时间
     */
    @TableField("end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @ApiModelProperty("到期时间/质保时间")
    @ExcelIgnore
    private Date endTime;
    /**
     * 软件版本号
     */
    @TableField("software_version")
    @ApiModelProperty("软件版本号")
    @ExcelIgnore
    private String softwareVersion;
    /**
     * 显示版本号
     */
    @TableField("display_version")
    @ApiModelProperty("显示版本号")
    @ExcelIgnore
    private String displayVersion;
    /**
     * 控制版本号
     */
    @TableField("control_version")
    @ApiModelProperty("控制版本号")
    @ExcelIgnore
    private String controlVersion;
    /**
     * 状态（0：存在，1：删除）
     */
    @TableField("is_deleted")
    @ApiModelProperty("状态（0：存在，1：删除）")
    @ExcelIgnore
    private Integer isDeleted;

    /**
     * 工程师id(创建者)
     */
    @TableField("creator")
    @ApiModelProperty("工程师id(创建者)")
    @ExcelIgnore
    private String creator;
    /**
     * 数据创建时间
     */
    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @ApiModelProperty("数据创建时间")
    @ExcelIgnore
    private Date createTime;
    /**
     * 数据更新时间
     */
    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @ApiModelProperty("数据更新时间")
    @ExcelProperty(value = "数据更新时间")
    private Date updateTime;
    /**
     * 逆变器接入类型（0：并网，1：储能）
     */
    @TableField("receive_type")
    @ApiModelProperty("逆变器接入类型（0：并网，1：储能）")
    @ExcelIgnore
    private String receiveType;
    /**
     * 电站名称
     */
    @TableField("plant_name")
    @ApiModelProperty("电站名称")
    @ExcelProperty(value = "电站名称")
    private String plantName;
    /**
     * 逆变器运行状态
     */
    @TableField("inverter_status")
    @ApiModelProperty("逆变器状态")
    @ExcelProperty(value = "逆变器状态")
    private String inverterStatus;
    /**
     * 实时功率
     */
    @TableField("power")
    @ApiModelProperty("实时功率")
    @ExcelProperty(value = "实时功率")
    private String power;

    @TableField("today_electricity")
    @ApiModelProperty("日发电量")
    @ExcelProperty(value = "日发电量")
    private String todayElectricity;

    @TableField("month_electricity")
    @ApiModelProperty("月发电量")
    @ExcelProperty(value = "月发电量")
    private String monthElectricity;

    @TableField("year_electricity")
    @ApiModelProperty("年发电量")
    @ExcelProperty("年发电量")
    private String yearElectricity;

    @TableField("total_electricity")
    @ApiModelProperty("总发电量")
    @ExcelProperty("总发电量")
    private String totalElectricity;

    @ApiModelProperty("查询多个逆变器状态")
    @ExcelIgnore
    private List<String> multiInverterStatus;
}
