package com.bto.commons.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/5/19 16:36
 */
@Data
@ApiModel("项目信息")
public class ProjectInfoDTO implements Serializable {
    private static final long serialVersionUID = 4792162587765553187L;
    @ApiModelProperty("父级id：仅新增需要")
    private String pid;
    @ApiModelProperty("项目id：新增、编辑页面不传")
    private String id;
    @ApiModelProperty("项目名称")
    private String projectName;
}
