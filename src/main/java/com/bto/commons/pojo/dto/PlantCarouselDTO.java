package com.bto.commons.pojo.dto;

import com.bto.commons.pojo.vo.PlantCarouselVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/23 15:48
 */
@Data
@ApiModel("站点轮播数据")
public class PlantCarouselDTO implements Serializable {
    private static final long serialVersionUID = 2259818360873819265L;
    @ApiModelProperty("返回结果集")
    private List<PlantCarouselVO> records;
    @ApiModelProperty("站点总数量")
    private Integer total;
}
