package com.bto.commons.utils;

import java.time.LocalDate;

/**
 *  ID生成工具类
 * <AUTHOR>
 * @date 2023/5/20 14:53
 */
public class IDUtils {

    /**
     * 生成10位带有时间信息的ID
     * @return
     */
    public static Long generateShortId() {
        // 2 位 年份的后两位 22001 后五位走随机  每天清一次缓存 99999 10
        StringBuilder idSb = new StringBuilder();
        /// 年份后两位  和  一年中的第几天
        LocalDate now = LocalDate.now();
        String year = now.getYear() + "";
        year = year.substring(2);
        String day = now.getDayOfYear() + "";
        /// 补0
        if (day.length() < 3) {
            StringBuilder sb = new StringBuilder();
            for (int i = day.length(); i < 3; i++) {
                sb.append("0");
            }
            day = sb.append(day).toString();
        }
        idSb.append(year).append(day);
        /// 后五位补随机数
        for (int i = idSb.length(); i < 10; i++) {
            idSb.append((int) (Math.random() * 10));
        }
        return Long.parseLong(idSb.toString());
    }
}
