package com.bto.commons.utils;

import cn.hutool.core.lang.tree.TreeUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 树节点类，
 * 继承该类用于实现树节点需求
 *
 * <AUTHOR>
 * @date 2023/5/18 15:23
 */
@Data
public class TreeNode<T> implements Serializable {

    private static final long serialVersionUID = 8720012088982388579L;
    /**
     * 主键id
     */
    @Schema(description = "id")
    private String id;
    /**
     * 父级id
     */
    @Schema(description = "父级ID")
    private String pid;
    /**
     * 子节点列表
     */
    private List<T> children = new ArrayList<>();
}
