package com.bto.commons.utils;

import com.bto.commons.enums.ConditionEnum;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.response.ResultEnum;

/**
 * 校验日期格式工具
 * <AUTHOR>
 * @date 2023/5/15 15:06
 */
public class CheckDateUtils {


    /**
     * 校验日期格式是否为 yyyy-MM-dd 格式
     * @param startTimeStr
     * @param endTimeStr
     * @return
     */
    public static Boolean isDateTimeFormatCorrect(String startTimeStr, String endTimeStr) {
        if (startTimeStr.length() == ConditionEnum.DATE_LENGTH_OF_DAY.getLength()
                && endTimeStr.length() == ConditionEnum.DATE_LENGTH_OF_DAY.getLength()) {
            return true;
        } else {
            throw new BusinessException(ResultEnum.DATETIME_FORMAT_FAILED);
        }
    }
}
