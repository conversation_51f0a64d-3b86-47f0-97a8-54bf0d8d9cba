package com.bto.commons.utils;

import com.bto.commons.enums.BusinessEnum;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.response.ResultEnum;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.List;

/**
 *  反射工具类
 * <AUTHOR>
 * @date 2023/6/16 15:10
 */
public class ReflectUtil {
    /**
     * 动态给Java对象指定的属性赋值
     * @param obj 要设置值的对象
     * @param fieldName 要设置值的属性名称
     * @param value 要设置的值
     */
    public static void setFieldValueByName(Object obj, String fieldName, Object value) {
        try {
            // 获取obj类的字节文件对象
            Class<? extends Object> clazz = obj.getClass();
            // 获取该类的成员变量
            Field field = clazz.getDeclaredField(fieldName);
            // 取消语言访问检查
            field.setAccessible(true);
            // 给变量赋值
            field.set(obj, value);
        } catch (Exception e) {
            throw new BusinessException(ResultEnum.SYSTEM_RUNTIME_FAILED);
        }
    }

    /**
     * 获取java对象的所有属性名称
     * @param obj
     * @return
     */
    public static List<String> getFieldNameByObj(Object obj){
        // 获取obj类的字节文件对象
        Class<? extends Object> clazz = obj.getClass();
        Field[] fields = clazz.getDeclaredFields();
        List<String> list = new ArrayList<>();
        for (Field field : fields) {
            // 取消语言访问检查
            field.setAccessible(true);
            if( Modifier.isStatic(field.getModifiers())) {
                continue;
            }
            String fieldName = field.getName();
            String columnName = BusinessEnum.getNameByColumn(fieldName);
            list.add(columnName);
        }
        return list;

    }



}
