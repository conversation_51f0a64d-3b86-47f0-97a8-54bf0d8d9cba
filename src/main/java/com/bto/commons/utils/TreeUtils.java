package com.bto.commons.utils;

import cn.hutool.core.collection.CollUtil;
import com.bto.commons.pojo.vo.ProjectInfoVO;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * 树形结构数据处理工具类
 *
 * <AUTHOR>
 * @date 2023/5/18 15:08
 */
public class TreeUtils {
    /**
     * 根据父级ID构建树节点
     *
     * @param treeNodes
     * @param pid
     * @param <T>
     * @return
     */
    public static <T extends TreeNode<T>> List<T> build(List<T> treeNodes, String pid) {
        //父级id不能为空
        VerificationUtils.isNull(pid, "父级ID");
        List<T> treeList = new ArrayList<>();
        for (int i = 0; i < treeNodes.size(); i++) {
            if (pid.equals(treeNodes.get(i).getPid())) {
                treeList.add(selectChildren(treeNodes, treeNodes.get(i)));
            }
        }
        return treeList;
    }

    /**
     * 查找叶子节点
     *
     * @param treeNodes
     * @param rootNode
     * @param <T>
     * @return
     */
    private static <T extends TreeNode<T>> T selectChildren(List<T> treeNodes, T rootNode) {
        for (int i = 0; i < treeNodes.size(); i++) {
            if (rootNode.equals(treeNodes.get(i).getPid())) {
                rootNode.getChildren().add(selectChildren(treeNodes, treeNodes.get(i)));
            }
        }
        return rootNode;
    }

    /**
     * 构建树节点
     *
     * @param treeNodes
     * @param <T>
     * @return
     */
    public static <T extends TreeNode<T>> List<T> build(List<T> treeNodes) {
        List<T> result = new ArrayList<>();
        //将List数据转换为Map
        HashMap<String, T> nodeMap = new LinkedHashMap<>(treeNodes.size());
        for (T treeNode : treeNodes) {
            //将每个节点数据根据ID进行映射存储
            nodeMap.put(treeNode.getId(), treeNode);
        }
        //遍历每一个节点ID的数据
        for (T node : nodeMap.values()) {
            //获取父级对象
            T parent = nodeMap.get(node.getPid());
            if (parent != null && !(node.getId().equals(parent.getId()))) {
                parent.getChildren().add(node);
                continue;
            }
            result.add(node);
        }
        return result;
    }

    /**
     * 获取树的所有叶子节点并返回
     * @param list
     * @return
     */

    public static List<ProjectInfoVO> getAllLeafNode2(List<ProjectInfoVO> list){
        List<ProjectInfoVO> result = new ArrayList<ProjectInfoVO>();
        for (ProjectInfoVO projectInfoVO : list) {
            if (projectInfoVO.getChildren().size()>0){
                getAllLeafNode(projectInfoVO.getChildren());
                result.addAll(getAllLeafNode(projectInfoVO.getChildren()));
            }else {
                result.add(projectInfoVO);
            }
        }
        return result;
    }
    /**
     * 获取树的所有叶子节点并返回
     * @param list
     * @return
     */
    public static <T extends TreeNode<T>> List<T> getAllLeafNode(List<T> list){
        List<T> result = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            if (list.get(i).getChildren().size()>0){
                getAllLeafNode(list.get(i).getChildren());
                result.addAll(getAllLeafNode(list.get(i).getChildren()));
            }else {
                result.add(list.get(i));
            }
        }
        return result;
    }

    /**
     * 获取树某个特定节点下的所有子节点并返回
     * @param list
     * @return
     */
    public static <T extends TreeNode<T>> List<T> getAllChildNode(List<T> list){
        List<T> result = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            if (CollUtil.isNotEmpty(list.get(i).getChildren())){
                result.add(list.get(i));
                getAllChildNode(list.get(i).getChildren());
                result.addAll(getAllChildNode(list.get(i).getChildren()));
            }else {
                result.add(list.get(i));
            }
        }
        return result;
    }


}
