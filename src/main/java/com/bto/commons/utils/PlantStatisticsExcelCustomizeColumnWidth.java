package com.bto.commons.utils;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import org.apache.poi.ss.usermodel.Cell;

import java.util.List;

/**
 * <AUTHOR> by zhb on 2024/2/22.
 */

public class PlantStatisticsExcelCustomizeColumnWidth extends AbstractColumnWidthStyleStrategy {
    @Override
    protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<WriteCellData<?>> list, Cell cell, Head head, Integer integer, Boolean isHead) {
        // COLUMN 宽度定制.
        if (isHead && cell.getRowIndex() == 1) {
            int columnWidth = cell.getStringCellValue().getBytes().length;
            // 手动调整各列宽度
            switch (cell.getColumnIndex()) {
                case 0:
                    columnWidth = columnWidth + 8;
                    break;
                case 1:
                case 2:
                case 6:
                    columnWidth = columnWidth + 10;
                    break;
                case 3:
                case 8:
                case 9:
                    columnWidth = columnWidth + 5;
                    break;
                default:
                    break;
            }
            int cellIndex = cell.getColumnIndex();
            if (columnWidth > 255) {
                columnWidth = 255;
            }
            writeSheetHolder.getSheet().setColumnWidth(cellIndex, columnWidth * 256);
        }
    }
}