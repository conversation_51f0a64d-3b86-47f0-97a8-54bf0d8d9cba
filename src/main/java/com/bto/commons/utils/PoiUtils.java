package com.bto.commons.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.poi.excel.ExcelReader;
import com.bto.commons.enums.BusinessEnum;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.response.ResultEnum;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.util.IOUtils;

import java.io.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * POI相关操作
 *
 * <AUTHOR>
 * @date 2023/5/6 14:35
 */
public class PoiUtils {

    /**
     * 生成Excel文件
     *
     * @param workbook
     * @param fileName
     * @return
     */
    public static File createExcelFile(Workbook workbook, String fileName) {
        OutputStream stream = null;
        File file = null;
        try {
            //用了createTempFile，这是创建临时文件，系统会自动给你的临时文件编号，
            // 所以后面有号码，
            // 使用用createNewFile即可按照指定的名称
            file = File.createTempFile(fileName, ".xlsx");
            stream = new FileOutputStream(file.getAbsoluteFile());
            workbook.write(stream);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {            //这里调用了IO工具包控制开关
            IOUtils.closeQuietly(workbook);
            IOUtils.closeQuietly(stream);
        }
        return file;
    }

    /**
     * 获取正确字符的长度
     *
     * @param str
     * @return
     */
    public static int getStrLength(String str) {
        if (StringUtils.isEmpty(str)) {
            return 0;
        }
        //中文匹配
//        String patternStr = "[\u4e00-\u9fa5]+";
        String patternStr = "[\\u0391-\\uFFE5]+";
        Pattern pattern = Pattern.compile(patternStr);
        int lengthPtn = 0;
        int lengthNOtPtn = 0;
        char[] array = str.toCharArray();

        for (int i = 0; i < array.length; i++) {
            Matcher matcher = pattern.matcher(String.valueOf(array[i]));
            if (matcher.matches()) {
                lengthPtn++;
            }
        }
        lengthNOtPtn = array.length - lengthPtn;
        return lengthPtn * 3 + lengthNOtPtn * 2;
    }

    public static <T> List<T> getObjectList(ExcelReader excelReader) {
        List<List<Object>> list = excelReader.read();
        //从表格第二行开始赋值
        if (CollUtil.isNotEmpty(list) && list.size() >= 2) {
            //获取表格头部各个字段
            List<Object> fieldList = list.get(0);
            if (CollUtil.isEmpty(fieldList)) {
                throw new BusinessException(ResultEnum.FILE_CONTENT_ERROR);
            }
            List<HashMap<String, Object>> result = new ArrayList<>();
            for (int i = 1; i < list.size(); i++) {
                List<Object> objects = list.get(i);
                HashMap<String, Object> tempMap = new HashMap<>();
                for (int j = 0; j < objects.size(); j++) {
                    String key = BusinessEnum.getColumnByName(fieldList.get(j).toString());
                    String value = objects.get(j).toString();
                    tempMap.put(key,value);
                }
                result.add(tempMap);
            }
            return (List<T>) result;
        } else {
            throw new BusinessException(ResultEnum.FILE_NULL_ERROR);
        }
    }
}