package com.bto.commons.utils;

import java.util.regex.Pattern;

/**
 * 首字符小写字母转大写字母工具类
 *
 * <AUTHOR>
 * @date 2023/4/4 9:28
 */
public class StringUtil {

    public static String capString(String str){
        String reg = "^[a-z]\\w*";
        //判断字符串不等于null或者不等于空字符串
        if(str!=null&&!"".equals(str)){
            //匹配字符串首字符是不是小写字符
            if(Pattern.matches(reg,str)){
                char[] chars = str.toCharArray();
                //使用ascii码运算，小写转大写
                chars[0] -= 32;
                return String.valueOf(chars);
            }
        }
        return str;
    }

}
