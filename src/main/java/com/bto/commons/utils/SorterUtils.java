package com.bto.commons.utils;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.pojo.dto.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 1. 基础排序对象，包含排序字段和排序方式
 *
 * <AUTHOR>
 */
@Data
public class SorterUtils {

    @ApiModelProperty(value = "排序字段", example = "userName")
    private String order;

    @ApiModelProperty(value = "排序方式", example = "asc/desc")
    private Boolean isAsc;

    public SorterUtils(String order, Boolean isAsc) {
        this.order = order;
        this.isAsc = isAsc;
    }

    /**
     * 根据查询条件拼接得到order by语句
     *
     * @param sorter 分页查询条件
     * @return String
     */
    public static String getStatement(SorterUtils sorter) {
        String sort;
        String[] sortArray = {};
        String[] orderArray = {};
        String order = sorter.getIsAsc() ? "asc" : "desc";
        String sortColumn = sorter.getOrder();
        StringBuilder statement = new StringBuilder();

        // 多字段排序
        if (StrUtil.isNotEmpty(sortColumn)) {
            // 驼峰命名转为下划线
            sort = StrUtil.toUnderlineCase(sortColumn);

            if (sort.contains(",")) {
                sortArray = sort.split(",");
            }
        } else {
            return "";
        }
        if (StrUtil.isNotEmpty(order)) {
            if (order.contains(",")) {
                orderArray = order.split(",");
            }
        } else {
            return "";
        }

        if (sortArray.length > 0 && orderArray.length > 0) {
            int length = sortArray.length;

            for (int i = 0; i < length; i++) {
                statement.append(sortArray[i]);
                statement.append(" ");
                statement.append(orderArray[i]);

                if (i < length - 1) {
                    statement.append(", ");
                }
            }
        } else {
            // " #{sort} #{order}“
            statement.append(sort);
            statement.append(" ");
            statement.append(order);
        }
        return statement.toString();
    }

    /**
     * 根据查询条件拼接得到order by语句
     *
     * @param sorter 分页查询条件
     * @return String
     */
    public static String getOrderByStatement(SorterUtils sorter) {
        String statement = getStatement(sorter);

        if (StrUtil.isNotEmpty(statement)) {
            return " order by " + statement;
        } else {
            return statement;
        }
    }

    public static <T> Page<T> getPage(PageDTO query) {
        Page<T> page = new Page<>(query.getCurrentPage(), query.getPageSize());
        // 排序
        if (StringUtils.isNotBlank(query.getOrder())) {
            // 驼峰转下划线
            String order = StrUtil.toUnderlineCase(query.getOrder());
            if (query.getIsAsc()) {
                page.addOrder(OrderItem.asc(order));
            } else {
                page.addOrder(OrderItem.desc(order));
            }
        }
        return page;
    }
}
