package com.bto.commons.utils;


import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
/**
 * <AUTHOR>
 */
public class ListPageUtils implements Serializable {

    /**
     * 利用subList方法进行分页
     * @param list 分页数据
     * @param pagesize  页面大小
     * @param currentPage   当前页面
     */
    public static <T> List<T> pageBySubTList(List<T> list, int currentPage, int pagesize) {
        List<T> subList = new ArrayList<>();
        try {
            int totalcount = list.size();
//            currentPage =currentPage+1;
            int pagecount = 0;
            int m = totalcount % pagesize;
            if (m > 0) {
                pagecount = totalcount / pagesize + 1;
            } else {
                pagecount = totalcount / pagesize;
            }
            if (m == 0) {
                subList = list.subList((currentPage - 1) * pagesize, pagesize * (currentPage));
            } else {
                if (currentPage == pagecount) {
                    subList = list.subList((currentPage - 1) * pagesize, totalcount);
                } else {
                    subList = list.subList((currentPage - 1) * pagesize, pagesize * (currentPage));
                }
            }
        }catch (Exception e){
        }
        return subList;
    }

}
