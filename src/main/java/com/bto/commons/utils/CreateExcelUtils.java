package com.bto.commons.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.bto.commons.enums.BusinessEnum;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.ExportFileDTO;
import com.bto.commons.pojo.vo.CellStyleVO;
import com.bto.commons.pojo.vo.ElectricityStaticsInfoVO;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.strategy.ExcelCellWidthStyleStrategy;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static com.bto.commons.utils.ExcelCellStyle.createCellStyle;
import static com.bto.commons.utils.PoiUtils.getStrLength;

/**
 * <AUTHOR>
 * @date 2023/5/13 9:12
 */
public class CreateExcelUtils {
    public static File createUserExcelFile(List<?> list) {
        if (list == null) {
            list = new ArrayList<>();
        }
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFCellStyle cellStyle = workbook.createCellStyle();
        XSSFFont font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 30);
        font.setFontName("宋体");
        cellStyle.setFont(font);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 2、设置背景色
        cellStyle.setFillForegroundColor(IndexedColors.PALE_BLUE.getIndex());
        //创建一个sheet,括号里可以输入sheet名称，默认为sheet0
        XSSFSheet sheet = workbook.createSheet();
        Row row0 = sheet.createRow(0);
        row0.setRowStyle(cellStyle);
        row0.createCell(0).setCellValue("能效收益统计");
        CellRangeAddress range = new CellRangeAddress(0, 0, 0, 3);
        sheet.addMergedRegion(range);
        row0.setRowStyle(cellStyle);
        Row row1 = sheet.createRow(1);
        int columnIndex = 0;
        //创建表格单元格
        row1.createCell(columnIndex).setCellValue("No");
        row1.createCell(++columnIndex).setCellValue("ID");
        row1.createCell(++columnIndex).setCellValue("用户名");
        for (int i = 0; i < list.size(); i++) {
            ElectricityStaticsInfoVO electricityStaticsInfo = (ElectricityStaticsInfoVO) list.get(i);
            Row row = sheet.createRow(i + 1);
            for (int j = 0; j < columnIndex + 1; j++) {
                row.createCell(j);
            }
            columnIndex = 0;
            row.getCell(columnIndex).setCellValue(i + 1);
            row.getCell(++columnIndex).setCellValue(electricityStaticsInfo.getPlantUid());
            row.getCell(++columnIndex).setCellValue(electricityStaticsInfo.getPlantName());
        }
        //调用PoiUtils工具包
        return PoiUtils.createExcelFile(workbook, "能效收益统计");
    }

    /**
     * 创建分析对象数据Excel文件
     *
     * @param list
     * @return
     */

    public static File createAnalyzeObjectDataExcelFile(List<?> list, List<String> columnList) throws IllegalAccessException {
        if (list == null) {
            list = new ArrayList<>();
        }
        //创建HSSFWorkbook对象
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFFont meterHeaderFont = workbook.createFont();
        meterHeaderFont.setBold(true);
        meterHeaderFont.setFontHeightInPoints((short) 16);
        meterHeaderFont.setFontName("华文楷体");
        //创建样式
        CellStyleVO cellStyleInfo = new CellStyleVO(workbook, HorizontalAlignment.CENTER, VerticalAlignment.CENTER, FillPatternType.SOLID_FOREGROUND, false);
        XSSFCellStyle cellStyle = createCellStyle(cellStyleInfo);
        cellStyle.setFont(meterHeaderFont);
        //建立sheet对象
        XSSFSheet sheet = workbook.createSheet("分析对象数据报表");
        //在sheet里创建第一行，参数为行索引,这里是0开始的
        int rowNum = 0;
        XSSFRow row0 = sheet.createRow(rowNum++);
        //合并表头
//        int length = AnalyzeObjectDataDTO.class.getDeclaredFields().length;
        CellRangeAddress range = new CellRangeAddress(0, 0, 0, columnList.size() - 1);
        sheet.addMergedRegion(range);
        XSSFCell cell0 = row0.createCell(0);
        cell0.setCellStyle(cellStyle);
        cell0.setCellValue("电站分析对象数据");
        //自适应宽度：对合并表头生效
        sheet.autoSizeColumn(list.size(), true);
        //在sheet里创建第二行
        XSSFRow row1 = sheet.createRow(rowNum++);
        List<String> secondRow = new ArrayList<>();
        for (int i = 0; i < columnList.size(); i++) {
            for (BusinessEnum businessEnum : BusinessEnum.values()) {

                if (businessEnum.getColumn().equals(columnList.get(i))) {
                    secondRow.add(businessEnum.getChineseName());
                }
            }
        }
        //创建第二行单元格并设置单元格内容
        for (int i = 0; i < secondRow.size(); i++) {
            XSSFCell cell1 = row1.createCell(i);
            cell1.setCellStyle(cellStyle);
            cell1.setCellValue(secondRow.get(i));
        }
        //动态创建列
        for (int i = 0; i < list.size(); i++) {
            Object objectData = list.get(i);
            XSSFRow row = sheet.createRow(i + rowNum);
            int filesLength = objectData.getClass().getDeclaredFields().length;
            //通过getDeclaredFields()方法获取对象类中的所有属性（含私有）
            Field[] fields = objectData.getClass().getDeclaredFields();
            HashMap<String, String> dataMap = new HashMap<>();
            for (Field field : fields) {
                //设置允许通过反射访问私有变量
                field.setAccessible(true);
                //获取字段的值
                String value = field.get(objectData).toString();
                //获取字段属性名称
                String name = field.getName();
                //其他自定义操作
                dataMap.put(name, value);
            }
            for (int j = 0; j < columnList.size(); j++) {
                //动态创建每一列的每一行
                XSSFCell cell2 = row.createCell(j);
                //创建样式
                CellStyleVO contentCellStyleInfo = new CellStyleVO(workbook, HorizontalAlignment.CENTER, VerticalAlignment.CENTER, FillPatternType.SOLID_FOREGROUND, false);
                XSSFCellStyle contentCellStyle = createCellStyle(contentCellStyleInfo);
                //设置水平居中
                contentCellStyle.setAlignment(HorizontalAlignment.CENTER);
                //设置垂直居中
                contentCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                //填充模式
                contentCellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                cell2.setCellStyle(contentCellStyle);
                cell2.setCellValue(dataMap.get(columnList.get(j)));
                if (j == 0) {
                    sheet.setColumnWidth(j, getStrLength(dataMap.get(columnList.get(j))) * 200);
                } else {
                    sheet.setColumnWidth(j, getStrLength(dataMap.get(columnList.get(j))) * 330);
                }
            }
            //对合并表头生效
            sheet.autoSizeColumn(list.size(), true);
        }//调用PoiUtils工具包
        return PoiUtils.createExcelFile(workbook, "分析对象数据统计");
    }


    /**
     * 创建Excel文件(泛型)
     * @param list
     * @param query
     * @return
     * @throws IllegalAccessException
     */
    public static File createExcelFile(List<?> list, ExportFileDTO query) throws IllegalAccessException {
        if (list == null) {
            list = new ArrayList<>();
        }
        //创建HSSFWorkbook对象
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFFont meterHeaderFont = workbook.createFont();
        meterHeaderFont.setBold(true);
        meterHeaderFont.setFontHeightInPoints((short) 16);
        meterHeaderFont.setFontName("华文楷体");
        //创建样式
        CellStyleVO cellStyleInfo = new CellStyleVO(
                workbook, HorizontalAlignment.CENTER,
                VerticalAlignment.CENTER, FillPatternType.SOLID_FOREGROUND, false
        );
        XSSFCellStyle cellStyle = createCellStyle(cellStyleInfo);
        cellStyle.setFont(meterHeaderFont);
        //建立sheet对象
        XSSFSheet sheet = workbook.createSheet(query.getSheetName());
        //在sheet里创建第一行，参数为行索引,这里是0开始的
        int rowNum = 0;
        XSSFRow row0 = sheet.createRow(rowNum++);
        //合并表头
//        int length = AnalyzeObjectDataDTO.class.getDeclaredFields().length;
        CellRangeAddress range = new CellRangeAddress(0, 0, 0, query.getColumnsList().size() - 1);
        sheet.addMergedRegion(range);
        XSSFCell cell0 = row0.createCell(0);
        cell0.setCellStyle(cellStyle);
        cell0.setCellValue(query.getSheetName());
        //自适应宽度：对合并表头生效
        sheet.autoSizeColumn(list.size(), true);
        //在sheet里创建第二行
        XSSFRow row1 = sheet.createRow(rowNum++);
        List<String> secondRow = new ArrayList<>();
        for (int i = 0; i < query.getColumnsList().size(); i++) {
            secondRow.add(BusinessEnum.getNameByColumn(query.getColumnsList().get(i)));
//            for (BusinessEnum businessEnum : BusinessEnum.values()) {
//                if (businessEnum.getColumn().equals(query.getColumnsList().get(i))) {
//                    secondRow.add(businessEnum.getChineseName());
//                }
//            }
        }
        //创建第二行单元格并设置单元格内容
        for (int i = 0; i < secondRow.size(); i++) {
            XSSFCell cell1 = row1.createCell(i);
            cell1.setCellStyle(cellStyle);
            cell1.setCellValue(secondRow.get(i));
        }
        //动态创建列
        for (int i = 0; i < list.size(); i++) {
            Object objectData = list.get(i);
            XSSFRow row = sheet.createRow(i + rowNum);
            int filesLength = objectData.getClass().getDeclaredFields().length;
            //通过getDeclaredFields()方法获取对象类中的所有属性（含私有）
            Field[] fields = objectData.getClass().getDeclaredFields();
            HashMap<String, String> dataMap = new HashMap<>();
            for (Field field : fields) {
                //设置允许通过反射访问私有变量
                System.out.println("======="+field);
                field.setAccessible(true);
                //获取字段的值
                System.out.println(objectData);
                String value = field.get(objectData).toString();
                //获取字段属性名称
                String name = field.getName();
                System.out.println(name);
                //其他自定义操作
                dataMap.put(name, value);
//                System.out.println(dataMap.toString());
            }
            for (int j = 0; j < query.getColumnsList().size(); j++) {
                //动态创建每一列的每一行
                XSSFCell cell2 = row.createCell(j);
                //创建样式
                CellStyleVO contentCellStyleInfo = new CellStyleVO(
                        workbook, HorizontalAlignment.CENTER,
                        VerticalAlignment.CENTER, FillPatternType.SOLID_FOREGROUND, false
                );
                XSSFCellStyle contentCellStyle = createCellStyle(contentCellStyleInfo);
                //设置水平居中
                contentCellStyle.setAlignment(HorizontalAlignment.CENTER);
                //设置垂直居中
                contentCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                //填充模式
                contentCellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                cell2.setCellStyle(contentCellStyle);
                cell2.setCellValue(dataMap.get(query.getColumnsList().get(j)));
                if (j == 0) {
                    sheet.setColumnWidth(j, getStrLength(dataMap.get(query.getColumnsList().get(j))) * 200);
                } else {
                    sheet.setColumnWidth(j, getStrLength(dataMap.get(query.getColumnsList().get(j))) * 330);
                }
            }
            //对合并表头生效
            sheet.autoSizeColumn(list.size(), true);
        }//调用PoiUtils工具包
        return PoiUtils.createExcelFile(workbook, query.getSheetName());
    }

    public static void exportToExcel(List<?> data, Class<?> clazz, String sheetName, HttpServletResponse response) {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = String.format(sheetName + "_" + System.currentTimeMillis());
            fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            OutputStream out = response.getOutputStream();
            HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(StyleUtils.getHeadStyle(), StyleUtils.getContentStyle());
            ExcelCellWidthStyleStrategy widthStyleStrategy = new ExcelCellWidthStyleStrategy();
            ExcelWriter excelWriter = ((ExcelWriterBuilder)((ExcelWriterBuilder) EasyExcel.write(out).registerWriteHandler(horizontalCellStyleStrategy)).registerWriteHandler(widthStyleStrategy)).build();
            WriteSheet writeSheet = ((ExcelWriterSheetBuilder)EasyExcel.writerSheet(0, sheetName).head(clazz)).build();
            excelWriter.write(data, writeSheet);
            excelWriter.finish();
        } catch (IOException var10) {
            throw new BusinessException(ResultEnum.SYSTEM_RUNTIME_FAILED.getCode(), var10.getMessage());
        }
    }

}
