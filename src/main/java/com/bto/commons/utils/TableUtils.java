package com.bto.commons.utils;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.response.ResultEnum;

import java.util.ArrayList;
import java.util.List;

/**
 * 数据库表处理工具类
 * <AUTHOR>
 * @date 2023/5/15 14:50
 */
public class TableUtils {
    /**
     * 获取指定时间区间内的表名
     * @param TABLE_NAME_PREFIX
     * @param startTimeStr
     * @param endTimeStr
     * @return
     */
    public static List<String> getTableSuffix(String TABLE_NAME_PREFIX, String startTimeStr, String endTimeStr) {
        if (CheckDateUtils.isDateTimeFormatCorrect(startTimeStr,endTimeStr)) {
            List<String> tableNames = new ArrayList<>();
            DateTime startTime = DateUtil.parse(startTimeStr);
            DateTime endTime = DateUtil.parse(endTimeStr);
            DateTime cursor = startTime;
            while (cursor.isBeforeOrEquals(endTime)) {
                if (cursor.equals(DateUtil.endOfMonth(cursor))) {
                    if (cursor.toString("yyyy").equals(endTime.toString("yyyy"))) {
                        // 时间区间的结束日期在本年，只需查询本年的最后一个月
                        String tableName = TABLE_NAME_PREFIX + cursor.toString("yyyyMM");
                        tableNames.add(tableName);
                        break;
                    } else {
                        // 时间区间跨越了年，查询当前年的最后一个月
                        String tableName = TABLE_NAME_PREFIX + cursor.toString("yyyyMM");
                        tableNames.add(tableName);
                        cursor = DateUtil.parse(cursor.toString("yyyy") + "-12-01");
                        continue;
                    }
                }
                String tableName = TABLE_NAME_PREFIX + cursor.toString("yyyyMM");
                tableNames.add(tableName);
                cursor = DateUtil.offsetMonth(cursor, 1);
            }
            return tableNames;
        }else {
            throw new BusinessException(ResultEnum.DATETIME_FORMAT_FAILED);
        }
    }

    // public static String getTableName(String TABLE_NAME_PREFIX, String dateTime) {
    //     String tableName = dateTime.replace("-", "");
    //     return TABLE_NAME_PREFIX+tableName;
    // }

    public static String getTableName(String TABLE_NAME_PREFIX, String dateTime) {
        String[] split = dateTime.split("-");
        return TABLE_NAME_PREFIX+split[0];
    }

}
