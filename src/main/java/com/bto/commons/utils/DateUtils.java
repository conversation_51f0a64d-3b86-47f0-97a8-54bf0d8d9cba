package com.bto.commons.utils;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.bto.commons.constant.Constant;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.response.ResultEnum;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/15 14:03
 */
public class DateUtils {
    /**
     * 时间格式(yyyy-MM-dd)
     */
    public final static String DATE_PATTERN = "yyyy-MM-dd";
    /**
     * 时间格式(yyyy-MM-dd HH:mm:ss)
     */
    public final static String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    public static String getDateOffsetByYear(String createTime, Integer offset) {
        Date date = DateUtil.parse(createTime);
        return DateUtil.offsetMonth(date, Constant.YEAR_OFFSET * offset).toString();
    }

    /**
     * 对年份进行偏移计算
     *
     * @param date
     * @param offset
     * @return
     */
    public static final String getYearOffset(String date, Integer offset) {
        // 解析输入的日期字符串
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy");
        Year year = Year.parse(date, formatter);
        // 对日期进行月份偏移
        Year offsetYear = year.plusYears(offset);
        // 将偏移后的日期格式化为字符串
        return offsetYear.format(formatter);
    }

    /**
     * 对月份进行偏移计算
     *
     * @param date
     * @param offset
     * @return
     */
    public static final String getMonthOffset(String date, Integer offset) {
        // 解析输入的日期字符串
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        YearMonth yearMonth = YearMonth.parse(date, formatter);
        // 对日期进行月份偏移
        YearMonth offsetYearMonth = yearMonth.plusMonths(offset);
        // 将偏移后的日期格式化为字符串
        return offsetYearMonth.format(formatter);
    }

    /**
     * 获取指定月份的第一天日期
     *
     * @param date 传入的年月：yyyy-MM
     * @return
     */
    public static String getBeginOfMonth(String date) {
        if (date.length() == Constant.DAY_LENGTH) {
            return date;
        } else if (date.length() == Constant.MONTH_LENGTH) {
            DateTime dateTime = DateUtil.parse(date + "-01");
            return DateUtil.beginOfMonth(dateTime).toDateStr();
        } else {
            throw new BusinessException(ResultEnum.DATETIME_FORMAT_FAILED);
        }
    }

    /**
     * 获取指定月份的最后一天日期
     *
     * @param date 传入的年月：yyyy-MM
     * @return
     */
    public static String getEndOfMonth(String date) {
        if (date.length() == Constant.DAY_LENGTH) {
            return date;
        } else if (date.length() == Constant.MONTH_LENGTH) {
            DateTime dateTime = DateUtil.parse(date + "-01");
            return DateUtil.endOfMonth(dateTime).toDateStr();
        } else {
            throw new BusinessException(ResultEnum.DATETIME_FORMAT_FAILED);
        }
    }

    /**
     * 判断字符串是否为日期格式
     * (yyyy-MM-dd、yyyy-MM、yyyy)
     *
     * @param dateStr
     * @return
     */
    public static Boolean checkDate(String dateStr) {
        SimpleDateFormat format;
        if (dateStr.length() == Constant.DAY_LENGTH) {
            format = new SimpleDateFormat("yyyy-MM-dd");
        } else if (dateStr.length() == Constant.MONTH_LENGTH) {
            format = new SimpleDateFormat("yyyy-MM");
        } else if (dateStr.length() == Constant.YEAR_LENGTH) {
            format = new SimpleDateFormat("yyyy");
        } else {
            return false;
        }
        boolean dateflag;
        try {
            format.setLenient(false);
            format.parse(dateStr);
            dateflag = true;
        } catch (ParseException e) {
            dateflag = false;
        }
        return dateflag;
    }

    /**
     * 如果 start >= end 返回 flase
     * start < end 返回 true
     *
     * @param start
     * @param end
     * @return
     */
    public static Boolean compareDate(String start, String end) {
        if (StrUtil.isEmpty(start) && StrUtil.isEmpty(end)) {
            return false;
        }
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date startDate = new Date();
        Date endDate = new Date();
        try {
            startDate = format.parse(start);
            endDate = format.parse(end);
        } catch (ParseException e) {
            throw new BusinessException(ResultEnum.DATETIME_FORMAT_FAILED);
        }
        return DateUtil.compare(startDate, endDate) > 0;
    }

    /**
     * 获取当前月份时间格式：yyyyMM
     */
    public static String getCurrentMonth(Date date) {
        return DateUtil.format(date, "yyyyMM");
    }

    public static String getCurrentMonth(LocalDate date) {
        return date.format(DateTimeFormatter.ofPattern("yyyyMM"));
    }

    /**
     * 获取一天的开始 yyyy:MM:dd 00:00:00
     */
    public static String getDayStart(Date date) {
        return DateUtil.beginOfDay(date).toString();
    }

    /**
     * 获取一天的结束 yyyy:MM:dd 23:59:59
     */
    public static String getDayEnd(Date date) {
        return DateUtil.endOfDay(date).toString();
    }


    /**
     * 获取距离今天结束的秒数
     *
     * @return long
     * <AUTHOR>
     * @since 2024-01-08 15:36:47
     */
    public static long getExpTimeByEndOfToday() {
        LocalDateTime currentDateTime = LocalDateTime.now();
        LocalDateTime endOfDay = currentDateTime.with(LocalTime.MAX);
        return Duration.between(currentDateTime, endOfDay).getSeconds();
    }

    /**
     * 获取两个日期之间的所有日期
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return {@link List }<{@link LocalDate }>
     * <AUTHOR>
     * @since 2024-03-01 10:42:47
     */
    public static List<LocalDate> getDatesBetween(LocalDate startDate, LocalDate endDate) {
        List<LocalDate> dates = new ArrayList<>();
        long daysBetween = ChronoUnit.DAYS.between(startDate, endDate);

        for (int i = 0; i <= daysBetween; i++) {
            LocalDate currentDate = startDate.plusDays(i);
            dates.add(currentDate);
        }

        return dates;
    }
}
