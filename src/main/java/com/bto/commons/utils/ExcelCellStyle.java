package com.bto.commons.utils;

import com.bto.commons.pojo.vo.CellStyleVO;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

/**
 * <AUTHOR>
 * @date 2023/5/13 15:33
 */
public class ExcelCellStyle {
    public static XSSFCellStyle createCellStyle(CellStyleVO cellStyleVO) {
        //创建HSSFWorkbook对象
        XSSFWorkbook workbook = cellStyleVO.getWorkbook();
        //建立sheet对象
        XSSFCellStyle cellStyle = workbook.createCellStyle();
        //设置水平居中
        cellStyle.setAlignment(cellStyleVO.getHorizontalAlignment());
        //设置垂直居中
        cellStyle.setVerticalAlignment(cellStyleVO.getVerticalAlignment());
        //填充模式
        cellStyle.setFillPattern(cellStyleVO.getPattern());
        //自动换行
        cellStyle.setWrapText(cellStyleVO.getIsWrapText());
        // 顶边栏
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        // 右边栏
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
        // 底边栏
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        // 左边栏
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        //设置字体样式
//        XSSFFont font = workbook.createFont();
//        font.setBold(font.getBold());
//        font.setFontHeightInPoints(font.getFontHeight());
//        font.setFontName(font.getFontName());
//        cellStyle.setFont(font);
        return cellStyle;
    }
}
