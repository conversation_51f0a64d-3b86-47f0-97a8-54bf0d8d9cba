package com.bto.commons.utils;
import cn.hutool.core.util.ObjectUtil;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

import java.beans.PropertyDescriptor;
import java.util.HashSet;
import java.util.Set;

/**
 * 获取null属性名（工具类）
 * <AUTHOR>
 * @date 2023/5/19 15:08
 */

public class PropertyUtils {
    public static String[] getNullPropertyNames(Object source) {
        if (ObjectUtil.isNull(source)){
            return new String[0];
        }
        BeanWrapper src = new BeanWrapperImpl(source);
        PropertyDescriptor[] pds = src.getPropertyDescriptors();
        Set<String> emptyNames = new HashSet<>();
        for (PropertyDescriptor pd : pds) {
            //检查此属性的值是否为null，然后将其添加到集合
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null || "".equals(srcValue.toString())){
                emptyNames.add(pd.getName());
            }
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }
}
