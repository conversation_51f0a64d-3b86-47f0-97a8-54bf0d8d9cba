package com.bto.commons.utils;

import cn.hutool.core.util.StrUtil;
import com.bto.commons.pojo.dto.NumInfoDTO;

import java.lang.reflect.Field;
import java.math.BigDecimal;

/**
 * 业务公式计算工具类
 *
 * <AUTHOR>
 * @date 2023/6/16 15:15
 */
public class BusinessCalculateUtil {
    /**
     * 求等效利用小时
     *
     * @param electricity
     * @return 保留两位小鼠
     */
    public static String getEfficiencyPerHours(String electricity, String plantCapacity) {
        BigDecimal plantCapacityNum = new BigDecimal(plantCapacity);
        if (plantCapacityNum.compareTo(BigDecimal.ZERO) == 0) {
            return "0";
        }
        BigDecimal electricityNum = new BigDecimal(electricity).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP);
        BigDecimal realPlantCapacity = plantCapacityNum.divide(BigDecimal.valueOf(1000), 2, BigDecimal.ROUND_HALF_UP);
        return electricityNum.divide(realPlantCapacity, 2, BigDecimal.ROUND_HALF_UP).toString();
    }

    /**
     * 求等效利用小时(Real)
     *
     * @param electricity
     * @return 保留两位小鼠
     */
    public static String getRealEfficiencyPerHours(String electricity, String plantCapacity) {
        BigDecimal electricityNum = new BigDecimal(electricity);
        BigDecimal plantCapacityNum = new BigDecimal(plantCapacity);

        if (plantCapacityNum.compareTo(BigDecimal.ZERO) == 0) {
            return "0";
        }
        return electricityNum.divide(plantCapacityNum, 2, BigDecimal.ROUND_HALF_UP).toString();
    }

    /**
     * 求工作效率= 工作效率 = (power/plant_capacity)*100%     -- 保留两位
     *
     * @param power
     * @param plantCapacity
     * @return 保留两位小鼠
     */
    public static String getWorkEfficiency(String power, String plantCapacity) {
        BigDecimal plantCapacityNum = new BigDecimal(plantCapacity);
        if (plantCapacityNum.compareTo(BigDecimal.ZERO) == 0) {
            return "0";
        }
        return new BigDecimal(power).divide(plantCapacityNum, 4, BigDecimal.ROUND_HALF_UP).toString();
    }

    /**
     * 发电效率 = （electricity/(3.5*装机容量)）
     *
     * @param electricity   发电量
     * @param plantCapacity 装机容量
     * @return {@link String }
     * <AUTHOR>
     * @since 2024-02-28 15:21:05
     */
    public static String getElectricityEfficiency(String electricity, String plantCapacity) {
        BigDecimal plantCapacityNum = new BigDecimal(plantCapacity);
        if (plantCapacityNum.compareTo(BigDecimal.ZERO) == 0) {
            return "0";
        }
        return new BigDecimal(electricity).divide(plantCapacityNum.multiply(BigDecimal.valueOf(3.5)), BigDecimal.ROUND_HALF_UP).setScale(2).toString();
    }

    /**
     * 求真实发电量= 发电量/100 (单位换算，保留2位)
     *
     * @param electricity
     * @return 保留两位小鼠
     */
    public static String getRealElectricity(String electricity) {
        if (StrUtil.isBlank(electricity)) {
            return "0.00";

        }
        electricity = electricity.replace(",", "");
        BigDecimal b1 = new BigDecimal(electricity).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP);
        return b1.toString();
    }

    /**
     * 求真实装机容量= 装机容量/100 (单位换算，保留2位)
     *
     * @param plantCapacity
     * @return 保留两位小鼠
     */
    public static String getRealPlantCapacity(String plantCapacity) {
        BigDecimal b1 = new BigDecimal(plantCapacity).divide(BigDecimal.valueOf(1000), 3, BigDecimal.ROUND_HALF_UP);
        return b1.toString();
    }

    /**
     * 等效植树 （棵）= (发电量/100*0.832)/1800
     *
     * @param electricity
     * @return 保留两位小鼠
     */
    public static String getTreeNum(String electricity) {
        BigDecimal b1 = new BigDecimal(electricity).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(0.832));
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(1800), 2, BigDecimal.ROUND_HALF_UP);
        return b2.toString();
    }

    /**
     * 累计二氧化碳减排 (吨)= (发电量*0.000997/100)
     *
     * @param electricity
     * @return 保留两位小数
     */
    public static String getReduceCo2(String electricity) {
        BigDecimal b1 = new BigDecimal(electricity).multiply(BigDecimal.valueOf(0.000997));
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP);
        return b2.toString();
    }

    /**
     * 累计煤炭减排 (吨)=((发电量/100) * 0.3025) / 1000
     *
     * @param electricity
     * @return 保留两位小数
     */
    public static String getReduceCoal(String electricity) {
        BigDecimal b1 = new BigDecimal(electricity).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(0.3025));
        BigDecimal b2 = b1.divide(BigDecimal.valueOf(1000), 2, BigDecimal.ROUND_HALF_UP);
        return b2.toString();
    }

    /**
     * 当日收益 = 发电量/100*sale_price
     *
     * @param electricity
     * @return
     */
    public static String getIncome(String electricity, String electricityPrice) {
        BigDecimal b1 = new BigDecimal(electricity).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP);
        BigDecimal b2 = b1.multiply(new BigDecimal(electricityPrice)).setScale(2, BigDecimal.ROUND_HALF_UP);
        return b2.toString();
    }

    /**
     * 计算对象中所有属性值之和
     *
     * @param object
     * @return String
     */
    public static String getTotalNum(Object object) {
        Field[] fields = object.getClass().getDeclaredFields();
        BigDecimal totalNum = BigDecimal.ZERO;
        for (Field field : fields) {
            try {
                field.setAccessible(true);
                String value = (String) field.get(object);
                totalNum = totalNum.add(new BigDecimal(value));
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        }
        return totalNum.toString();
    }

    /**
     * 计算电站-光精灵在线数量
     * 在线电站数（运维在线） = 运维器连接数= 正常电站数 + 告警电站数+逆变器夜间离线数+自检提示数
     *
     * @param numInfo
     * @return String
     */
    public static String getPlantOnlineNum(NumInfoDTO numInfo) {
        BigDecimal onlineNum = new BigDecimal(numInfo.getOnlineNum());
        BigDecimal normalNum = new BigDecimal(numInfo.getNormalNum());
        BigDecimal selfCheckNum = new BigDecimal(numInfo.getSelfCheckNum());
        BigDecimal alarmNum = new BigDecimal(numInfo.getAlarmNum());
        BigDecimal inverterShutdownNum = new BigDecimal(numInfo.getInverterShutdownNum());
        onlineNum = onlineNum.add(normalNum).add(alarmNum).add(selfCheckNum).add(inverterShutdownNum);
        return onlineNum.toString();
    }

    /**
     * 计算电站-光精灵/逆变器 正常数量
     * 在线电站数/逆变器数（运维在线） = 运维器连接数= 正常电站/逆变器数 + 自检提示/逆变器数
     *
     * @param numInfo
     * @return String
     */
    public static String getRealNormalNum(NumInfoDTO numInfo) {
        BigDecimal normalNum = new BigDecimal(numInfo.getNormalNum());
        BigDecimal selfCheckNum = new BigDecimal(numInfo.getSelfCheckNum());
        normalNum = normalNum.add(selfCheckNum);
        return normalNum.toString();
    }

    /**
     * 计算逆变器在线数量
     * 逆变器在线数量 = 正常逆变器数量数 +告警逆变器数量数+ 自检提示逆变器数量数
     *
     * @param numInfo
     * @return String
     */
    public static String getInverterOnlineNum(NumInfoDTO numInfo) {
        BigDecimal normalNum = new BigDecimal(numInfo.getNormalNum());
        BigDecimal selfCheckNum = new BigDecimal(numInfo.getSelfCheckNum());
        BigDecimal alarmNum = new BigDecimal(numInfo.getAlarmNum());
        normalNum = normalNum.add(alarmNum).add(selfCheckNum);
        return normalNum.toString();
    }

    /**
     * 计算-设备离线数量
     * 设备离线数量 = 设备离线数量 + 设备夜间离线数量
     *
     * @param numInfo
     * @return
     */
    public static String getRealOfflineNum(NumInfoDTO numInfo) {
        BigDecimal offlineNum = new BigDecimal(numInfo.getOfflineNum());
        BigDecimal deviceShutdownNum = new BigDecimal(numInfo.getDeviceShutdownNum());
        offlineNum = offlineNum.add(deviceShutdownNum);
        return offlineNum.toString();
    }

    /**
     * 辐射单位换算
     */
    public static Double radiationConversion(Double radiation) {
        return radiation / 1000;
    }

    public static String getRadiation(String electricity, String capacity) {
        BigDecimal plantCapacityNum = new BigDecimal(capacity);
        if (plantCapacityNum.compareTo(BigDecimal.ZERO) == 0) {
            return "0";
        }
        return new BigDecimal(electricity).divide(plantCapacityNum, 2, BigDecimal.ROUND_HALF_UP).toString();
    }
}
