package com.bto.commons.utils;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.bto.commons.exception.DataException;

/**
 * 校验工具类
 * <AUTHOR>
 * @date 2023/5/18 15:11
 */
public class VerificationUtils {
    public static void isBlank(String str, String variable) {
        if (StrUtil.isBlank(str)) {
            throw new DataException(variable + "不能为空！");
        }
    }

    public static void isNull(Object object, String variable) {
        if (object == null) {
            throw new DataException(variable + "不能为空！");
        }
    }

    public static void isArrayEmpty(Object[] array, String variable) {
        if(ArrayUtil.isEmpty(array)){
            throw new DataException(variable + "不能为空！");
        }
    }
}
