package com.bto.commons.handler;

import com.bto.commons.exception.BusinessException;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.ServletException;
import javax.validation.ValidationException;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 统一捕获异常处理
 *
 * <AUTHOR>
 * @date 2023/3/29 10:46
 */

@Slf4j
@RestControllerAdvice
public class CommonExceptionHandler {
    /**
     * 业务异常捕获
     *
     * @param businessException
     * @return Result
     */
    @ExceptionHandler({BusinessException.class})
    @ResponseBody
    public Result<?> handlerBusinessException(BusinessException businessException) {
        log.error(businessException.getMessage(), businessException);
        return Result.instance(businessException.getCode(), businessException.getMessage(),businessException.getData());
    }

    /**
     * 访问权限异常
     *
     * @param forbiddenException 访问权限失败类型的枚举类
     * @return Result
     */
    // @ExceptionHandler({ForbiddenException.class})
    // public Result<?> handleForbidenException(ForbiddenException forbiddenException) {
    //     log.error(forbiddenException.getMessage(), forbiddenException);
    //     return Result.failed(ResultEnum.ACCESS_AUTHORIZATION_FAILED);
    // }

    @ExceptionHandler({HttpMessageNotReadableException.class})
    public Result<?> handleHttpMessageNotReadableException(HttpMessageNotReadableException httpMessageNotReadableException){
        log.error(httpMessageNotReadableException.getMessage(),httpMessageNotReadableException);
        return Result.failed(ResultEnum.REQUIREDPARAM_EMPTY );
    }


    /**
     * 顶级异常捕获，当其他异常无法处理时选择使用
     *
     * @param exception
     * @return
     */
    @ExceptionHandler({Exception.class})
    public Result<?> handleException(Exception exception) {
        log.error(exception.getMessage(), exception);
        return Result.failed(ResultEnum.SYSTEM_RUNTIME_FAILED);
    }

    /**
     * 捕获已知的系统级别异常
     *
     * @param exception
     * @return
     */
    @ExceptionHandler({ServletException.class})
    public Result<?> handlerKnownException(Exception exception) {
        log.error(exception .getMessage(), exception);
        return Result.failed(exception.getMessage());
    }
    /**
     * 捕获处理程序访问被拒绝异常
     *
     * @param exception
     * @return
     */
    @ExceptionHandler({AccessDeniedException.class})
    public Result<?> handlerAccessDeniedException(AccessDeniedException exception) {
        return Result.instance(ResultEnum.ACCESS_REFUSED_NO_AUTHORITY);
    }


    /**
     * 处理参数校验
     *
     * @param ex 异常
     * @return {@link Result }<{@link ? }>
     * <AUTHOR>
     * @since 2024-01-27 17:33:03
     */
    @ExceptionHandler(ValidationException.class)
    public Result<?> bindException(ValidationException ex) {
        String msg = ex.getMessage();

        // 生成返回消息
        String resultMsg = Arrays.stream(msg.split(","))
                .map(item -> item.split(":")[1].trim())
                .collect(Collectors.joining(","));

        return Result.failed(ResultEnum.REQUESTPARAM_ERROR.getCode(), resultMsg);
    }



}
