package com.bto.commons.response;

import com.bto.commons.exception.BusinessException;

/**
 * 全局返回结果枚举
 * --定义返回状态枚举
 *
 * <AUTHOR>
 * @date 2023/3/31 10:40
 */

public enum ResultEnum implements IResult {
    /**
     * 返回结果信息状态
     */
    LOGIN_SUCCESS("00000", "登录成功"),
    REFRESH_TOKEN_SUCCESS("00000", "令牌刷新成功"),
    SUCCESS("00000", "请求成功"),
    OPERATION_SUCCESS("00000", "操作成功"),

    NO_CONTENT("00204", "无查询结果"),
    SMS_NO_CONTENT("00204", "物联卡相关数据为空"),
    IDENTITY_VERIFICATION_FAILED("A0220", "用户身份校验失败"),
    USER_REGISTRY_FAILED("A0100", "用户注册失败"),
    PASSWORD_VERIFICATION_FAILED("A0120", "密码输入错误"),
    USER_NON_EXISTENT("A0201", "用户不存在"),
    USER_ACCOUNT_NOT_ENABLE("A0203", "用户账户未启用"),
    USER_ACCOUNT_INVALIDATED("A0203", "用户账户已作废"),
    USER_ACCOUNT_DATA_EXCEPTION("A0204", "用户帐户数据异常"),
    USER_LOGIN_EXPIRED("A0230", "用户登录状态过期"),
    REFRESH_TOKEN_EXPIRED("A0231", "刷新令牌已过期"),
    ACCESS_AUTHORIZATION_FAILED("A0300", "访问权限异常"),
    CLIENT_VERIFICATION_FAILED("A0301", "client校验失败"),
    NONE_CLIENT_INFO("A0301", "无client客户端信息"),
    INVALID_CLIENT_INFO("A0301", "无效的基本身份验证令牌"),
    CLIENT_SECRET_ERROR("A0302", "client客户端密钥错误"),

    REQUESTPARAM_ERROR("A0400", "请求参数出错"),
    DATA_PARAMS_INCONSISTENT("A0400", "数据参数不一致"),

    ACCESS_REFUSED_GATEWAY("A0403", "访问不被允许，请通过网关访问资源"),
    ACCESS_REFUSED_NO_AUTHORITY("A0403", "访问不被允许，该用户无权限访问此接口"),
    OPERATION_FAILED("A0440", "操作失败!"),
    SERVER_BUSY("A0503 ", "服务器繁忙，请稍后再试!"),
    REQUIREDPARAM_EMPTY("A0410", "缺少请求必填参数"),
    REQUEST_PARAM_NULL_ERROR("A0410", "请求参数不能为空"),
    DATA_NULL_ERROR("A0410", "数据不能为空"),
    DATA_EXISTED_ERROR("A0204", "该数据已经存在"),
    DELETE_DEVICE_FAIL("A0411", "删除设备失败"),
    COLLECT_PLANT_FAIL("A0411", "收藏电站失败"),
    DELETE_PLANT_FAIL("A0412", "删除电站失败"),
    PLANT_NOT_EXIST("A0413", "电站不存在"),
    DEVICE_NOT_EXIST("A0414", "设备不存在"),

    STARTTIME_GT_ENDTIME("A0450", "开始时间大于结束时间"),
    NOT_SAME_DAY("A0451", "不支持跨日期查询"),
    DATETIME_FORMAT_FAILED("A0452", "时间格式异常"),
    DATETIME_CHECK_FAILED("A0453", "最小时间不能大于最大时间!"),
    FEIGNCLINET_REQUESTEST_FAILD("A0510", "微服务远程调用失败"),
    UPLOAD_FILE_FAILED("A0700", "上传文件失败"),
    FILE_NULL_ERROR("A0701", "文件内容为空"),
    FILE_CONTENT_ERROR("A0701", "文件内容错误"),
    CRON_CONTENT_ERROR("A0701", "Cron表达式不正确"),

    SYSTEM_RUNTIME_FAILED("B0001", "系统执行出错"),
    ADD_BEAN_FAILED("B0001", "只允许添加有@Service注解的Bean！"),
    SYSTEM_LOG_FAILED("B0002", "日志服务执行出错"),
    SCHEDULE_JOB_FAILED("B0002", "定时任务执行出错"),
    BATCH_INSERT_ERROR("B0003", "批量插入执行出错,请检查提交的数据是否正确"),
    SINGLE_INSERT_ERROR("B0004", "添加失败,请检查提交的数据是否正确"),
    SINGLE_UPDATE_ERROR("B0004", "修改失败,请检查提交的数据是否正确"),
    SINGLE_DELETE_ERROR("B0004", "删除失败"),
    DYNAMIC_SQL_ERROR("B0300", "动态SQL执行失败，请检查SQL是否正确！"),
    VERSION_NUMBER_EXISTED("A0800","版本号已存在")
    ;


    /**
     * 返回状态码
     */
    private String code;
    /**
     * 返回状态信息
     */
    private String message;

    ResultEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public static void throwExceptionByCode(String code) {
        for (ResultEnum resultEnum : ResultEnum.values()) {
            if (resultEnum.getCode().equals(code)) {
                throw new BusinessException(resultEnum);
            }
        }
        throw new RuntimeException();
    }

    /**
     * 获取状态码
     *
     * @return 返回状态码
     */
    @Override
    public String getCode() {
        return this.code = code;
    }

    /**
     * 设置状态码
     *
     * @param code
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * 获取状态信息
     *
     * @return 返回状态信息
     */
    @Override
    public String getMessage() {
        return this.message = message;
    }

    /**
     * 设置状态信息
     *
     * @param message
     */
    public void setMessage(String message) {
        this.message = message;
    }
}
