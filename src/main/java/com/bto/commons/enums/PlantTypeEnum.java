package com.bto.commons.enums;

/**
 * <AUTHOR>
 * @date 2023/7/3 11:57
 */
public enum PlantTypeEnum {
    /**
     * 电站类型枚举
     */
    GRID_CONNECTED("0", "并网"),
    ENERGY_STORAGE("1", "储能"),
    HYBRID("2", "混合"),
    AC_COUPLING("3", "交流耦合"),
    NONE("-1", "");

    private String code;
    private String name;

    PlantTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        for(PlantTypeEnum plantTypeEnum:PlantTypeEnum.values()){
            if(plantTypeEnum.getCode().equals(code)){
                return plantTypeEnum.getName();
            }
        }
        return NONE.getName();
    }
    /**
     * code属性getter方法
     * @return
     */
    public String getCode() {
        return code;
    }

    /**
     * code属性setter方法
     * @param code
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * name属性getter方法
     * @return
     */
    public String getName() {
        return name;
    }

    /**
     * name属性setter方法
     * @param name
     */
    public void setName(String name) {
        this.name = name;
    }
}
