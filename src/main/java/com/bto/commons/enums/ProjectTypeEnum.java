package com.bto.commons.enums;


/**
 * 项目类型类
 *
 * <AUTHOR>
 * @date 2023/5/11 16:14
 */

public enum ProjectTypeEnum {
    /**
     * 项目类型
     */
    SUPER_ADMIN(0, "超级管理员"),
    HOUSEHOLD(1, "户用"),
    WHOLE_COUNTY(2, "整县"),
    INDUSTRY_COMMERCE(3, "工商业"),
    HOUSEHOLD_LEASE(4, "户租"),
    BTO_COMPANY(10,"博通新能源"),
    YD_COMPANY(20,"广东省粤电集团有限公司"),
    BTO_OF_IC(30,"博通新能源工商业"),
    YX_ENERGY_COMPANY(40,"广州越秀新能源投资有限公司"),
    GIN_LANG_ENERGY_COMPANY(41,"宁波锦浪智慧能源有限公司"),
    BTO_HUIZHOU_COMPANY(100,"惠州户用"),
    BTO_GUANGZHOU_COMPANY(101,"广州户用"),
    BTO_JIANGMEN_COMPANY(102,"江门户用"),
    BTO_DONGGUAN_COMPANY(103,"东莞户用"),
    BTO_ZHUHAI_COMPANY(104,"珠海户用"),
    BTO_ZHONGSHAN_COMPANY(105,"中山户用"),
    BTO_FOSHAN_COMPANY(106,"佛山户用"),
    FENGDIAN_HEYUAN__COMPANY(200,"广东枫电新能源有限公司(龙川)"),
    GUANGZHOU_YUESHENG__COMPANY(400,"广州越晟光伏科技有限公司"),
    YUNFU_YUEFENG__COMPANY(401,"云浮越丰光伏科技有限公司"),
    QINGYUAN_YUEYANG__COMPANY(402,"清远越洋光伏科技有限公司"),
    ZHANJIANG_YUEQIANG__COMPANY(403,"湛江越强光伏科技有限公司"),
    HEYUAN_YUEFU__COMPANY(404,"河源越富光伏科技有限公司"),
    GUANGZHOU_YUELI__COMPANY(405,"广州越利光伏科技有限公司"),
    GUILIN_YUEAN_COMPANY(406,"桂林越安光伏科技有限公司  "),
    GUILIN_YUEYAO_COMPANY(407,"桂林越瑶光伏科技有限公司 "),
    BTO_YUEXIU_ENERGY(408,"广东博通新能源越秀户租"),
    BTO_YUEZENG_ENERGY(409,"河源越增光伏科技有限公司"),
    BTO_ZHONGSHAN_ENERGY(410,"中山越河光伏科技有限公司"),
    BTO_ZHANJIANG_BORUI_ENERGY(411,"博瑞(湛江)"),
    BTO_SHAOGUAN_HOUSEHOLD_RENT(4000 ,"博通新能源韶关户租"),
    YUEDIAN_ENERGY(4010 ,"越电新能源 "),
    HUAYANG_ENERGY(40101 ,"华阳新能源 "),
    LVJIN_ENERGY(4020 ,"绿景新能源"),
    BTO_ZHANJIANG_HOUSEHOLD_RENT(4030 ,"博通新能源湛江户租"),
    BTO_HEYUAN_HOUSEHOLD_RENT(4040 ,"博通新能源河源户租"),
    BTO_JIGUANG_RENT(4041 ,"极光河源户租"),
    BTO_GUANGZHOU_HOUSEHOLD_RENT(4050 ,"博通新能源广州户租"),
    BTO_ZHUHAI_HOUSEHOLD_RENT(4051 ,"博通新能源珠海户租"),
    BTO_GUILIN_PINLE_HOUSEHOLD_RENT(4060 ,"博通新能源桂林平乐户租"),
    BTO_GUILIN_GONGCHENG_HOUSEHOLD_RENT(4070 ,"博通新能源桂林恭城户租"),
    PRODUCT_SERVICE(5, "产品服务"),
    GUANGDONG_DONGYAO_COMPANY(50, "广东东尧新能源科技有限公司"),
    BTO_YUNFU_HOUSEHOLD_RENT(4080,"博通新能源云浮户租"),
    BTO_CHAOZHOU_HOUSEHOLD_RENT(4081,"博通新能源潮州户租"),
    BTO_DONGGUAN_HOUSEHOLD_RENT(4082,"博通新能源东莞户租"),
    BTO_HUIZHOU_HOUSEHOLD_RENT(4083,"博通新能源惠州户租"),
    BTO_XINLANYUE_ENERGY(4090,"新蓝粤河源户租"),
    BTO_ZHONGRUN_ENERGY(4100,"中润电力中山户租"),

    BTO_YUEXIU_MAOMING_ENERGY(4084,"博通新能源茂名户租"),
    BTO_YUEXIU_XIYUE_ENERGY(40840,"广东喜悦科技发展有限公司"),
    BTO_YUEXIU_BOLIANG_ENERGY(40841,"化州博量新能源科技有限公司")
    ;

    public static String getProjectNameById(String code){
        for(ProjectTypeEnum projectTypeEnum:ProjectTypeEnum.values()){
            if(code.equals(projectTypeEnum.getProjectID().toString())){
                return projectTypeEnum.getProjectName();
            }
        }
        return code;
    }

    ProjectTypeEnum(Integer projectID, String projectName) {
        this.projectID = projectID;
        this.projectName = projectName;
    }

    public Integer projectID;
    public String projectName;

    public Integer getProjectID() {
        return projectID;
    }

    public void setProjectID(Integer projectID) {
        this.projectID = projectID;
    }

    public String getProjectName() {
        return this.projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

}
