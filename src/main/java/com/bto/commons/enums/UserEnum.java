package com.bto.commons.enums;

/**
 * <AUTHOR>
 * @date 2023/5/18 9:11
 */
public enum UserEnum {
    /**
     * 用户类型
     */
    USER_OF_INDIVIDUAL("0", "个人用户"),
    USER_OF_ENTERPRISE("1", "企业用户"),
    USER_OF_API("2", "API用户"),
    /**
     * 用户状态
     */
    USER_STATUS_DISENABLE("0", "禁用"),
    USER_STATUS_ENABLE("1", "启用");

    UserEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    private String code;
    private String name;

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
