package com.bto.commons.enums;

/**
 * <AUTHOR>
 * @date 2023/4/28 14:33
 */
public enum AlarmStatus {
    /**
     * 运维器告警状态枚举
     */
    IS_NOT_HANDLED("0", "未处理"),
    IS_HANDLED("1", "已处理"),
    IS_EXPIRED("2", "状态失效"),
    NONE("-1", "状态异常");

    private String code;
    private String name;

    AlarmStatus(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code) {
        for (AlarmStatus alarmStatus : AlarmStatus.values()) {
            if (alarmStatus.getCode().equals(code)) {
                return alarmStatus.getName();
            }
        }
        return NONE.getName();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}