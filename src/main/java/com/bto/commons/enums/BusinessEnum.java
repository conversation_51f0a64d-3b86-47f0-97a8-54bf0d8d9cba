package com.bto.commons.enums;

/**
 * <AUTHOR>
 * @date 2023/5/13 17:27
 */
public enum BusinessEnum {
    /**
     * 业务枚举
     */
    plant_uid("plantUid", "电站编号"),
    plant_uid_2("plantUid", "电站编号"),
    PLANTNAME("plantName", "电站名称"),
    PLANT_STATUS("plantStatus", "电站状态"),
    POWER("power", "实时功率"),
    PLANT_CAPACITY("plantCapacity", "装机容量"),
    ELECTRICITY_EFFICIENCY("electricityEfficiency", "发电效率"),
    WORKEFFICIENCY("workEfficiency", "工作效率"),
    FAILURERATE("failureRate", "故障率"),
    ELECTRICITY("electricity", "发电量"),
    MAX_VALUE("maxValue", "峰值"),
    AVG_VALUE("avgValue", "平均值"),
    TODAYELECTRICITY("todayElectricity", "日发电量"),
    MONTHELECTRICITY("monthElectricity", "月发电量"),
    YEARELECTRICITY("yearElectricity", "年发电量"),
    TOTALELECTRICITY("totalElectricity", "总发电量"),
    CREATETIME("createTime", "创建时间"),
    INITTIME("initTime", "初始时间"),
    UPDATETIME("updateTime", "更新时间"),
    POWER_DISTRIBUTOR("powerDistributor", "配电箱状态"),
    INVERTER_NUM("inverterNum", "逆变器数量"),
    PLANT_TYPE("plantTypeId", "站点类型"),
    DAILY_EFFICIENCY_PERHOUR("dailyEfficiencyPerHour", "日等效小时"),
    YEARLY_EFFICIENCY_PERHOUR("yearlyEfficiencyPerHour", "年等效小时"),
    PLANT_ADDRESS("address", "电站地址"),
    POWER_TIME("powerTime","发电时间"),
    USER_NAME("userName","用户"),
    METER_ID("MeterId","电厂编号"),
    METER_ID2("MeterID","电厂编号"),
    METER_ID3("meterID","电厂编号"),
    ALARM_NUM("alarmNum","告警数量"),
    EQUIVALENT_USE_HOUR("equivalentUseHour","等效小时"),
    ELECTRICITY_PRICE("electricityPrice","电价"),
    INCOME("income","收入"),
    TOTAL_REDUCE_CO2("totalReduceCo2","二氧化碳减排"),
    TOTAL_PLANT_TREE_NUM("totalPlantTreeNum","累计植树"),
    OFFLINE_TIME("offlineTime","离线时长"),
    ALARM_MSG("alarmMsg","告警信息"),
    ALARM_STATUS("alarmStatus","告警状态"),
    A_POINT_ALIAS("aPointAlias","A点别名"),
    A_POINT_VOLTAGE("aPointVoltage","A点电压"),
    B_POINT_ALIAS("bPointAlias","B点别名"),
    B_POINT_VOLTAGE("bPointVoltage","B点电压"),
    C_POINT_ALIAS("cPointAlias","C点别名"),
    C_POINT_VOLTAGE("cPointVoltage","C点电压"),
    D_POINT_ALIAS("dPointAlias","D点别名"),
    D_POINT_VOLTAGE("dPointVoltage","D点电压"),
    START_TIME("startTime","开始时间"),
    END_TIME("endTime","结束时间"),
    IMEI("IMEI","运维器IMEI"),
    PLANT_PHONE("plantPhone","电站电话"),
    PLANT_CITY("city","城市"),

    CONTRACT_ID("contractId", "合同ID"),
    USERNAME("userName", "客户名称"),
    USER_PHONE("userPhone", "手机号码"),
    PROJECT_ID("projectId", "项目ID"),
    ORDER_ID("orderId", "进件编号"),
    CREATOR("creator", "创建人"),
    STATUS("status", "状态"),
    PLANT_TYPE_NAME("plantType", "电站类型"),

    PROJECT_TYPE("projectName", "项目类型"),

    NONE("none", "无定义");

    public static String getNameByColumn(String column) {
        for (BusinessEnum businessEnum :
                BusinessEnum.values()) {
            if (businessEnum.getColumn().equals(column)) {
                return businessEnum.getChineseName();
            }
        }
        return NONE.getChineseName();
    }

    public static String getColumnByName(String chineseName) {
        for (BusinessEnum businessEnum :
                BusinessEnum.values()) {
            if (businessEnum.getChineseName().equals(chineseName)) {
                return businessEnum.getColumn();
            }
        }
        return NONE.getColumn();
    }

    private String column;
    private String chineseName;

    BusinessEnum(String column, String chineseName) {
        this.column = column;
        this.chineseName = chineseName;
    }

    public String getColumn() {
        return column;
    }

    public void setColumn(String column) {
        this.column = column;
    }

    public String getChineseName() {
        return chineseName;
    }

    public void setChineseName(String chineseName) {
        this.chineseName = chineseName;
    }
}