package com.bto.commons.enums;

/**
 * <AUTHOR>
 * @date 2023/5/18 9:11
 */
public enum SystemEnum {
    /**
     * token枚举
     */
    SIGN_KEY("secret", "bto-solarman"),
    invalid_token("401","invalid_token"),
    unauthorized_token("401","unauthorized");


    SystemEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    private String code;
    private String name;

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
