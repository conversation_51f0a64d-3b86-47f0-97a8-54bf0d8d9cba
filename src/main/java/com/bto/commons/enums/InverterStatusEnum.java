package com.bto.commons.enums;

/**
 * <AUTHOR>
 * @date 2023/4/21 15:12
 */
public enum InverterStatusEnum {
    /**
     * 逆变器状态
     */
    OFFLINE("0", "离线","offlineNum"),
    ONLINE("1", "正常","normalNum"),
    ALARMING("2", "告警","alarmNum"),
    SELF_CHECK("3", "自检提示","selfCheckNum"),
    INVERTER_SHUTDOWN("4","夜间离线","inverterShutdownNum"),

    NONE("-1","电站状态异常","");

    private String code;
    private String name;
    private String fieldName;
    private InverterStatusEnum(String code , String name, String fieldName){
        this.code=code;
        this.name=name;
        this.fieldName=fieldName;
    }

    public static String getNameByCode(String code){
        for (InverterStatusEnum inverterStatusEnum :
                InverterStatusEnum.values()) {
            if(inverterStatusEnum.getCode().equals(code)){
                return inverterStatusEnum.getName();
            }
        }
        return NONE.getName();
    }
    public static String getFieldNameByCode(String code){
        for(InverterStatusEnum inverterStatusEnum : InverterStatusEnum.values()){
            if(inverterStatusEnum.getCode().equals(code)){
                return inverterStatusEnum.getFieldName();
            }
        }
        return NONE.getFieldName();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }
}
