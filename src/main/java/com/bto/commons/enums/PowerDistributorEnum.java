package com.bto.commons.enums;

/**
 * <AUTHOR>
 * @date 2023/4/26 17:18
 */
public enum PowerDistributorEnum {
    /**
     * 配电箱状态信息
     */
    NORMAL("1","正常"),
    POWER_DISTRIBUTOR_SWITCH_MALFUNCTION("2","配电箱开关故障"),
    ELECTRICITY_METER_SWITCH_MALFUNCTION("3","电表箱开关故障"),
    MASTER_NO_GRID_ERROR("4","市电停电"),
    ELECTRICITY_METER_COLLECT_MALFUNCTION("5","电表箱开关采样异常"),
    MASTER_COLLECT_MALFUNCTION("6","市电采样异常"),
    MASTER_AND_ELECTRICITY_SWITCH_MALFUNCTION("7","市电、电表箱开关采样异常"),
    OVERCURRENT_SWITCH_MALFUNCTION("8","过流开关故障"),
    VALTAGE_LOSS_SWITCH_MALFUNCTION("9","失压开关故障"),
    VALTAGE_LOSS_SWITCH_COLLECT_MALFUNCTION("10","失压开关"),
    MASTER_AND_VALTAGE_LOSS_SWITCH_COLLECT_MALFUNCTION("11","市电、失压开关采样异常"),
    NONE("-1","未初始化");

    private String code;
    private String name;

    PowerDistributorEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        for(PowerDistributorEnum powerDistributorEnum : PowerDistributorEnum.values()){
            if(powerDistributorEnum.getCode().equals(code)){
                return powerDistributorEnum.getName();
            }
        }
        return NONE.getName();
    }
    /**
     * code属性getter方法
     * @return
     */
    public String getCode() {
        return code;
    }

    /**
     * code属性setter方法
     * @param code
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * name属性getter方法
     * @return
     */
    public String getName() {
        return name;
    }

    /**
     * name属性setter方法
     * @param name
     */
    public void setName(String name) {
        this.name = name;
    }
}
