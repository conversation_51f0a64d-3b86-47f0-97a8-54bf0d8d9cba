package com.bto.commons.enums;

/**
 * <AUTHOR>
 * @date 2023/6/19 17:03
 */

public enum ManufacturerEnum {
    /**
     * 制造商枚举
     */
    MANUFACTURE_SAJ("SAJ","三晶"),
    MANUFACTURE_LANG("LANG","锦浪"),
    MANUFACTURE_HW("HW","华为"),
    MANUFACTURE_AISWEI("AISWEI","爱士惟"),
    MANUFACTURE_NONE("NONE","无制造商信息");
    private final String code;
    private final String name;
    private ManufacturerEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    public static String getNameByCode(String code){
        for(ManufacturerEnum manufacturer:ManufacturerEnum.values()){
            if(manufacturer.getCode().equals(code)){
                return manufacturer.getName();
            }
        }
        return code;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
