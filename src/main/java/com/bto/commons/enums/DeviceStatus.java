package com.bto.commons.enums;

/**
 * <AUTHOR>
 * @date 2023/5/11 9:35
 */
public enum DeviceStatus {
    /**
     * 设备运行状态
     */
    OFFLINE("0", "离线","offlineNum"),
    ONLINE("1", "正常运行","normalNum"),
    ALARMING("2", "告警运行","alarmNum"),
    SELF_CHECK("3", "自检提示","selfCheckNum"),
    NO_INIT("4","夜间离线","deviceShutdownNum"),

    NONE("-1","无设备运行状态","none");

    private String code;
    private String name;
    private String fieldName;
    private DeviceStatus(String code ,String name,String fieldName){
        this.code=code;
        this.name=name;
        this.fieldName = fieldName;
    }

    public static String getNameByCode(String code){
        for (DeviceStatus deviceStatus :
                DeviceStatus.values()) {
            if(deviceStatus.getCode().equals(code)){
                return deviceStatus.getName();
            }
        }
        return NONE.getName();
    }
    public static String getFieldNameByCode(String code){
        for(DeviceStatus deviceStatus : DeviceStatus.values()){
            if(deviceStatus.getCode().equals(code)){
                return deviceStatus.getFieldName();
            }
        }
        return NONE.getFieldName();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }
}
