package com.bto.commons.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/6/12 15:47
 */
@Getter
@AllArgsConstructor
public enum OperatorEnum {
    /**
     * 运维器的枚举
     */
    OFFLINE("1", "正常"),
    OTHER("", "异常");
    private final String value;
    private final String name;

    public static String getNameByCode(String code){
        for(OperatorEnum operatorEnum:OperatorEnum.values()){
            if(operatorEnum.getValue().equals(code)){
                return operatorEnum.getName();
            }
        }
        return OTHER.getName();
    }
}
