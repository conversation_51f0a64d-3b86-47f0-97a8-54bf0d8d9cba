package com.bto.commons.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/7/27 10:09
 */
@Getter
@AllArgsConstructor
@SuppressWarnings("all")
public enum CounterStatusEnum {
    UNKNOWN(0, "未知"),
    CLOSING_RUNNING(1, "合闸运行"),
    OPEN_WAITING(2, "分闸待机"),
    CLOSING_FAILED(3, "合闸失败"),
    RECLOSING(4, "重合闸中"),
    RECLOSING_FAILED(5, "重合闸失败"),
    OPENING(6, "分闸中"),
    OPENING_FAILED(7, "分闸失败"),
    OPEN_LOCKED(8, "分闸闭锁"),
    TEST_CLOSING(9, "试合闸中"),
    TEST_OPENING(10, "试分闸中"),
    TEST_CLOSING_FAILED(11, "试合闸失败"),
    TEST_OPENING_FAILED(12, "试分闸失败");

    private final int value;
    private final String name;

    public static String getNameByValue(int value) {
        for (CounterStatusEnum s : CounterStatusEnum.values()) {
            if (s.getValue() == value) {
                return s.getName();
            }
        }
        return "";
    }

    public static Integer getValueByName(String name) {
        for (CounterStatusEnum s : CounterStatusEnum.values()) {
            if (Objects.equals(s.getName(), name)) {
                return s.getValue();
            }
        }
        return null;
    }
}
