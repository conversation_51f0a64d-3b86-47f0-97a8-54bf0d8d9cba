package com.bto.commons.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 风向枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum WindEnum {

    NORTHERN("0", "北风"),
    NORTHEASTER("1", "东北风"),
    EAST("2", "东风"),
    SOUTHEASTER("3", "东南风"),
    SOUTH("4", "南风"),
    SOUTHWESTER("5", "西南风"),
    WEST("6", "西风"),
    NORTHWEST("7", "西北风");


    private final String value;
    private final String name;

    public static String getValueByName(String value) {
        for (WindEnum s : WindEnum.values()) {
            if (Objects.equals(value, s.getValue())) {
                return s.getName();
            }
        }
        return "";
    }


}
