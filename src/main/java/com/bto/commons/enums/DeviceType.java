package com.bto.commons.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/8/17 18:02
 */
@Getter
public enum DeviceType {
    // 1:逆变器、2:运维器、3:电表,，4：气象站,11、配电柜，12、温湿度、烟感采集器
    /**
     * 设备类型枚举
     */
    INVERTER("1","逆变器","inverter"),
    OPERATOR("2", "运维器","operator"),

    ELECTRIC_METER("3", "电表", "electricMeter"),
    WEATHER_STATION("4", "气象站", "weatherStation"),
    DISTRIBUTION_ROOM("10", "配电房", "distributionRoom"),
    COUNTER("11", "配电柜", "counter"),
    ENVIRONMENT_SENSOR("12", "温湿度、烟感采集器", "environmentSensor"),

    NONE("-1","未识别的类型","none");
    private String code;
    private String name;
    private String fieldName;
    private DeviceType(String code ,String name,String fieldName){
        this.code=code;
        this.name=name;
        this.fieldName = fieldName;
    }

    public static String getNameByCode(String code){
        for (DeviceType deviceType :
                DeviceType.values()) {
            if(deviceType.getCode().equals(code)){
                return deviceType.getName();
            }
        }
        return NONE.getName();
    }
    public static String getFieldNameByCode(String code){
        for(DeviceType deviceType : DeviceType.values()){
            if(deviceType.getCode().equals(code)){
                return deviceType.getFieldName();
            }
        }
        return NONE.getFieldName();
    }
}
