package com.bto.commons.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/7/27 10:04
 */
@Getter
@AllArgsConstructor
@SuppressWarnings("ALL")
public enum CounterAlarmTypeEnum {

    NO_FAULT(0, "无故障"),
    SHORT_CIRCUIT_A(1, "短路-A"),
    SHORT_CIRCUIT_B(2, "短路-B"),
    SHORT_CIRCUIT_C(3, "短路-C"),
    SHORT_CIRCUIT_AB(4, "短路-AB"),
    SHORT_CIRCUIT_AC(5, "短路-AC"),
    SHORT_CIRCUIT_BC(6, "短路-BC"),
    SHORT_CIRCUIT_ABC(7, "短路-ABC"),
    QUICK_BREAK_A(8, "速断-A"),
    QUICK_BREAK_B(9, "速断-B"),
    QUICK_BREAK_C(10, "速断-C"),
    QUICK_BREAK_AB(11, "速断-AB"),
    QUICK_BREAK_AC(12, "速断-AC"),
    QUICK_BREAK_ABC(13, "速断-ABC"),
    N_HALF(14, "N_Half"),
    N_ONE(15, "N_One"),
    N_OVER(16, "N_Over"),
    OVERLOAD(17, "过载"),
    OVER_VOLTAGE(18, "过压"),
    UNDER_VOLTAGE(19, "欠压"),
    IMBALANCE(20, "不平衡"),
    LEAKAGE(21, "漏电"),
    LEAKAGE_LOCKOUT(22, "漏电闭锁"),
    PHASE_MISSING_A(23, "缺相-A"),
    PHASE_MISSING_B(24, "缺相-B"),
    PHASE_MISSING_C(25, "缺相-C"),
    PHASE_MISSING_AB(26, "缺相-AB"),
    PHASE_MISSING_AC(27, "缺相-AC"),
    PHASE_MISSING_BC(28, "缺相-BC");

    private final int value;
    private final String name;

    public static String getNameByValue(int value) {
        for (CounterAlarmTypeEnum s : CounterAlarmTypeEnum.values()) {
            if (s.getValue() == value) {
                return s.getName();
            }
        }
        return "";
    }

    public static Integer getValueByName(String name) {
        for (CounterAlarmTypeEnum s : CounterAlarmTypeEnum.values()) {
            if (Objects.equals(s.getName(), name)) {
                return s.getValue();
            }
        }
        return null;
    }
}
