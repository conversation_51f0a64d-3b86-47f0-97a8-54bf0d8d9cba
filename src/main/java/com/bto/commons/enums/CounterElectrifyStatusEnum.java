package com.bto.commons.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/7/27 10:31
 */
@Getter
@AllArgsConstructor
@SuppressWarnings("ALL")
public enum CounterElectrifyStatusEnum {
    ENABLE(0, "禁用"),
    DISABLE(1, "启用");
    private final int value;
    private final String name;

    public static String getNameByValue(int value) {
        for (CounterElectrifyStatusEnum s : CounterElectrifyStatusEnum.values()) {
            if (s.getValue() == value) {
                return s.getName();
            }
        }
        return "";
    }

    public static Integer getValueByName(String name) {
        for (CounterElectrifyStatusEnum s : CounterElectrifyStatusEnum.values()) {
            if (Objects.equals(s.getName(), name)) {
                return s.getValue();
            }
        }
        return null;
    }
}
