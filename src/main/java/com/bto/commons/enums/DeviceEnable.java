package com.bto.commons.enums;

/**
 * <AUTHOR>
 * @date 2023/5/11 9:36
 */
public enum DeviceEnable {
    /**
     * 设备开关机状态
     */
    IS_ON("0","关机"),
    IS_OFF("1","开机"),

    NONE("-1","无设备开关机状态");

    private String code;
    private String name;
    private DeviceEnable(String code ,String name){
        this.code=code;
        this.name=name;
    }

    public static String getNameByCode(String code){
        for (DeviceEnable deviceEnable : DeviceEnable.values()) {
            if(deviceEnable.getCode().equals(code)){
                return deviceEnable.getName();
            }
        }
        return NONE.getName();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
