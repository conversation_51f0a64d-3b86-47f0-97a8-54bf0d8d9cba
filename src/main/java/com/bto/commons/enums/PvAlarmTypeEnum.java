package com.bto.commons.enums;

import lombok.Getter;

import java.util.*;

/**
 * <AUTHOR> by zhb on 2024/2/28.
 */
@Getter
public enum PvAlarmTypeEnum {

    DYJC("DYJC", "电压检测"),
    DLJC("DLJC", "电流检测"),
    ZCJC("ZCJC", "组串检测"),
    PVJC("PVJC", "PV检测"),
    DFDL("DFDL", "低发电量检测");

    private final String code;
    private final String name;

    PvAlarmTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String name) {
        for (PvAlarmTypeEnum s : PvAlarmTypeEnum.values()) {
            if (Objects.equals(name, s.getName())) {
                return s.getCode();
            }
        }
        return "";
    }

    public static String getCodeByName(String code) {
        for (PvAlarmTypeEnum s : PvAlarmTypeEnum.values()) {
            if (Objects.equals(code, s.getCode())) {
                return s.getName();
            }
        }
        return "";
    }

    // 提供一个静态方法以获取Map
    public static List<String> getTypeList() {
        ArrayList<String> typeList = new ArrayList<>();
        for (PvAlarmTypeEnum s : PvAlarmTypeEnum.values()) {
            typeList.add(s.getName());
        }
        return typeList;
    }

}