package com.bto.commons.enums;

/**
 * <AUTHOR>
 * @date 2023/7/7 14:53
 */
public enum FormatEnum {
    /**
     * 格式化类型
     */
    DATE_FORMAT("日期格式","yyyy-MM-dd"),
    DATETIME_FORMAT("时间格式","yyyy-MM-dd HH:mm:ss");
    private String name;
    private String value;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    FormatEnum(String name, String value) {
        this.name = name;
        this.value = value;
    }
}
