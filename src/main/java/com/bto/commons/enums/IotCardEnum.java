package com.bto.commons.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/11/10 10:57
 */
@Getter
public enum IotCardEnum {
    /**
     * 电站类型枚举
     */
    OVERDUE("0", "已过期"),
    NORMAL("1", "正常");

    private String code;
    private String name;

    IotCardEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        for(IotCardEnum iotCardEnum:IotCardEnum.values()){
            if(iotCardEnum.getCode().equals(code)){
                return iotCardEnum.getName();
            }
        }
        return NORMAL.getName();
    }
}
