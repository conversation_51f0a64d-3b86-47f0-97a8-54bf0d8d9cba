package com.bto.commons.enums;

/**
 * <AUTHOR>
 * @date 2023/5/11 9:35
 */
public enum DeviceStatusField {

    /**
     * 设备状态字段名
     */
    OFFLINE_DEVICE_NUM("0","offlineDeviceNum"),
    NORMAL_DEVICE_NUM("1","normalDeviceNum"),
    ALARM_DEVICE_NUM("2","alarmDeviceNum"),
    SELFCHECK_DEVICE_NUM("3","selfCheckDeviceNum"),
    NOINIT_DEVICE_NUM("4","noInitDeviceNum"),
    NORMAL_OFFLINEDEVICE_NUM("5","normalOfflineDeviceNum"),

    NONE("-1","无设备运行状态");

    private String code;
    private String name;
    private DeviceStatusField(String code , String name){
        this.code=code;
        this.name=name;
    }

    public static String getNameByCode(String code){
        for (DeviceStatusField deviceStatus :
                DeviceStatusField.values()) {
            if(deviceStatus.getCode().equals(code)){
                return deviceStatus.getName();
            }
        }
        return NONE.getName();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
