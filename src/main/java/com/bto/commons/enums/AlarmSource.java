package com.bto.commons.enums;

import lombok.Getter;

/**
 * <AUTHOR> by zhb on 2024/1/16.
 */
@Getter
public enum AlarmSource {

    SAJ("0", "三晶"),
    BTO("1", "博通");

    private String code;
    private String name;

    AlarmSource(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code) {
        for (AlarmSource alarmSource : AlarmSource.values()) {
            if (alarmSource.getCode().equals(code)) {
                return alarmSource.getName();
            }
        }
        return "";
    }

}