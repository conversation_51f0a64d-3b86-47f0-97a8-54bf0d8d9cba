package com.bto.commons.enums;

/**
 * <AUTHOR>
 * @date 2023/4/8 16:56
 */

public enum PlantStatusEnum {
    /**
     * 电站 plantStatue 属性的状态信息
     */
    OFFLINE("0", "光精灵离线","offlineNum"),
    ONLINE("1", "正常","normalNum"),
    ALARMING("2", "告警","alarmNum"),
    SELF_CHECK("3", "自检提示","selfCheckNum"),
    INVERTER_SHUTDOWN("4","逆变器夜间离线","inverterShutdownNum"),

    NONE("-1","电站状态异常","");

    private String code;
    private String name;
    private String fieldName;

    private PlantStatusEnum(String code, String name,String fieldName) {
        this.code = code;
        this.name = name;
        this.fieldName = fieldName;
    }

    public static String getNameByCode(String code){
        for(PlantStatusEnum plantStatus:PlantStatusEnum.values()){
            if(plantStatus.getCode().equals(code) || plantStatus.getName().equals(code)){
                return plantStatus.getName();
            }
        }
        return NONE.getName();
    }
    public static String getFieldNameByCode(String code){
        for(PlantStatusEnum plantStatus:PlantStatusEnum.values()){
            if(plantStatus.getCode().equals(code)){
                return plantStatus.getFieldName();
            }
        }
        return NONE.getFieldName();
    }
    /**
     * code属性getter方法
     * @return
     */
    public String getCode() {
        return code;
    }

    /**
     * code属性setter方法
     * @param code
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * name属性getter方法
     * @return
     */
    public String getName() {
        return name;
    }

    /**
     * name属性setter方法
     * @param name
     */
    public void setName(String name) {
        this.name = name;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }
}
