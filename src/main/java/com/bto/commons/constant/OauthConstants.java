package com.bto.commons.constant;

/**
 * <AUTHOR>
 * @date 2023/7/19 16:05
 */
public interface OauthConstants {
    String AUTH_TYPE_BASIC = "Basic";
    String HEAD_TYPE_AUTHORIZATION = "Authorization";
    String TOKEN_TYPE_BEARER = "bearer";
    String USER_NAME = "username";
    String USER_UID = "userUid";
    String PASSWORD = "password";
    String CLIENT_CREDENTIALS = "client_credentials";
    String CLIENT_ID= "client_id";
    String CLIENT_SECRET= "client_secret";
    String REFRESH_TOKEN = "refresh_token";
    String TOKEN_OF_REFRESH= "refreshToken";
    String TOKEN_OF_ACCESS= "accessToken";
    String GRANT_TYPE = "grant_type";
    String TOKEN_TYPE = "tokenType";
    String SCOPE = "scope";
    String JWT_ID = "jti";
    String EXPIRATION = "expiresIn";
    String RESOURCE_IDS = "ResourceIds";
    String MENU_LIST = "menuList";
    String AUTHORITIES = "authorities";
    String ACCOUNT_NON_EXPIRED = "accountNonExpired";
    String ACCOUNT_NON_LOCKED = "accountNonLocked";
    String CREDENTIALS_NON_EXPIRED = "credentialsNonExpired";
    String AUTHORITY_SET = "authoritySet";
    String USER_INFO = "user_info";
    String USERINFO = "userInfo";
    String ENABLED = "enabled";
    String PROJECT_ID ="projectId";
    String USER_TYPE ="userType";
}
