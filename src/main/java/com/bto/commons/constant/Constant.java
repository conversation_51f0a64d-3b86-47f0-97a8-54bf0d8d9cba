package com.bto.commons.constant;

/**
 * <AUTHOR>
 * @date 2023-06-15
 */
public interface Constant {
    /**
     * 保质期
     */
    Integer WARRANTY_TERM = 5;
    /**
     * 年偏移月数量
     */
    Integer YEAR_OFFSET = 12;
    /**
     * 表名前缀
     */
    String TABLE_PREFIX = "bto_inverter_";
    /**
     * 月份（字符串）：起始下标
     */
    Integer MONTH_START_INDEX = 5;
    /**
     * 月份（字符串）：结束下标
     */
    Integer MONTH_END_INDEX = 7;
    /**
     * 天数（字符串）：起始下标
     */
    Integer DAY_START_INDEX = 8;
    /**
     * 天数（字符串）：结束下标
     */
    Integer DAY_END_INDEX = 10;

    /**
     * 日期长度
     */
    Integer DAY_LENGTH = 10;
    /**
     * 月份长度
     */
    Integer MONTH_LENGTH = 7;
    /**
     * 年份长度
     */
    Integer YEAR_LENGTH = 4;
    /**
     * 密码模式
     */
    String PASSWORD_MODE = "password";
    /**
     * 刷新令牌模式
     */
    String REFRESH_MODE = "refresh_token";
    /**
     * 客户端模式
     */
    String CLIENT_MODE = "client_credentials";
    /**
     * 返回结果常量
     */
    String STATUS = "status";
    String MESSAGE = "message";
    String DATA = "data";

    /**
     * 验证码过期时间
     */
    long VALIDATION_CODE_SEND_INTERVAL = 240000L;
}