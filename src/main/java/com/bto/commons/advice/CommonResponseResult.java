package com.bto.commons.advice;

import com.alibaba.fastjson.JSON;
import com.bto.commons.annotation.ResponseNotIntercept;
import com.bto.commons.response.Result;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

/**
 * 实现ResponseBodyAdvice接口,统一返回数据格式
 *
 * <AUTHOR>
 * @date 2023/3/30 8:28
 */
//@RestControllerAdvice
public class CommonResponseResult implements ResponseBodyAdvice<Object> {

    /**
     * 该方法支持注解@ResponseIntercept,使方法返回原生值
     *
     * @param returnType    返回类型
     * @param converterType 转换器类型
     * @return true, 执行beforeBodyWrite方法
     * @return false, 原生值返回前端
     */
    @Override

    public boolean supports(MethodParameter returnType,
                            Class<? extends HttpMessageConverter<?>> converterType) {

        if (returnType.getDeclaringClass().isAnnotationPresent(ResponseNotIntercept.class)) {
            return false;
        }
        if (returnType.getMethod().isAnnotationPresent(ResponseNotIntercept.class)) {
            //若方法上加了@ResponseNotIntercept 则该方法不用做统一的拦截
            return false;
        }
        return true;
    }

    /**
     * 对返回的结果格式进行封装
     *
     * @param body
     * @param returnType
     * @param selectedContentType
     * @param selectedConverterType
     * @param request
     * @param response
     * @return
     */
    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType,
                                  MediaType selectedContentType,
                                  Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                  ServerHttpRequest request,
                                  ServerHttpResponse response) {
        //如果body已经被封装为Result类，直接返回
        if (body instanceof Result) {
            return body;
        }
        //序列化字符串json格式，然后进行封装为Result类
        if (body instanceof String) {
            return JSON.toJSONString(Result.failed("系统返回未处理数据"));
        }
        return Result.success(body);
    }
}