package com.bto.commons.converter.dto;

import com.bto.commons.pojo.vo.InverterAdditionalPropertyVO;
import com.bto.commons.pojo.entity.InverterAdditionalProperty;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/7/4 8:18
 */
@Mapper(componentModel = "spring")
public interface InverterAdditionalPropertyDTOMapper {
    InverterAdditionalPropertyDTOMapper INSTACE = Mappers.getMapper(InverterAdditionalPropertyDTOMapper.class);

    InverterAdditionalProperty inverterAdditionalPropertyDTO2InverterAdditionalProperty(InverterAdditionalPropertyVO inverterAdditionalPropertyVO);

    InverterAdditionalPropertyVO inverterAdditionalProperty2InverterAdditionalPropertyDTO(InverterAdditionalProperty inverterAdditionalProperty);

    static final BigDecimal ONE_HUNDRED = new BigDecimal("100");

    default InverterAdditionalPropertyVO inverterAdditionalProperty2InverterAdditionalPropertyDTODecorator(InverterAdditionalProperty inverterAdditionalProperty){
        BigDecimal todayElectricity = new BigDecimal(inverterAdditionalProperty.getTodayElectricity());
        BigDecimal monthElectricity = new BigDecimal(inverterAdditionalProperty.getMonthElectricity());
        BigDecimal yearElectricity = new BigDecimal(inverterAdditionalProperty.getYearElectricity());
        BigDecimal totalElectricity = new BigDecimal(inverterAdditionalProperty.getTotalElectricity());
        BigDecimal ipv1 = new BigDecimal(inverterAdditionalProperty.getIpv1());
        BigDecimal ipv2 = new BigDecimal(inverterAdditionalProperty.getIpv2());
        BigDecimal ipv3 = new BigDecimal(inverterAdditionalProperty.getIpv3());
        BigDecimal ipv4 = new BigDecimal(inverterAdditionalProperty.getIpv4());
        BigDecimal ipv5 = new BigDecimal(inverterAdditionalProperty.getIpv5());
        BigDecimal ipv6 = new BigDecimal(inverterAdditionalProperty.getIpv6());
        BigDecimal ipv7 = new BigDecimal(inverterAdditionalProperty.getIpv7());
        BigDecimal ipv8 = new BigDecimal(inverterAdditionalProperty.getIpv8());
        BigDecimal ipv9 = new BigDecimal(inverterAdditionalProperty.getIpv9());
        BigDecimal ipv10 = new BigDecimal(inverterAdditionalProperty.getIpv10());
        BigDecimal ipv11 = new BigDecimal(inverterAdditionalProperty.getIpv11());
        BigDecimal ipv12 = new BigDecimal(inverterAdditionalProperty.getIpv12());
        BigDecimal vpv1 = new BigDecimal(inverterAdditionalProperty.getVpv1());
        BigDecimal vpv2 = new BigDecimal(inverterAdditionalProperty.getVpv2());
        BigDecimal vpv3 = new BigDecimal(inverterAdditionalProperty.getVpv3());
        BigDecimal vpv4 = new BigDecimal(inverterAdditionalProperty.getVpv4());
        BigDecimal vpv5 = new BigDecimal(inverterAdditionalProperty.getVpv5());
        BigDecimal vpv6 = new BigDecimal(inverterAdditionalProperty.getVpv6());
        BigDecimal vpv7 = new BigDecimal(inverterAdditionalProperty.getVpv7());
        BigDecimal vpv8 = new BigDecimal(inverterAdditionalProperty.getVpv8());
        BigDecimal vpv9 = new BigDecimal(inverterAdditionalProperty.getVpv9());
        BigDecimal vpv10 = new BigDecimal(inverterAdditionalProperty.getVpv10());
        BigDecimal vpv11 = new BigDecimal(inverterAdditionalProperty.getVpv11());
        BigDecimal vpv12 = new BigDecimal(inverterAdditionalProperty.getVpv12());
        BigDecimal iac1 = new BigDecimal(inverterAdditionalProperty.getIac1());
        BigDecimal iac2 = new BigDecimal(inverterAdditionalProperty.getIac2());
        BigDecimal iac3 = new BigDecimal(inverterAdditionalProperty.getIac3());
        BigDecimal vac1 = new BigDecimal(inverterAdditionalProperty.getVac1());
        BigDecimal vac2 = new BigDecimal(inverterAdditionalProperty.getVac2());
        BigDecimal vac3 = new BigDecimal(inverterAdditionalProperty.getVac3());
        BigDecimal fac1 = new BigDecimal(inverterAdditionalProperty.getFac1());
        BigDecimal fac2 = new BigDecimal(inverterAdditionalProperty.getFac2());
        BigDecimal fac3 = new BigDecimal(inverterAdditionalProperty.getFac3());
        InverterAdditionalPropertyVO inverterAdditionalPropertyVO = inverterAdditionalProperty2InverterAdditionalPropertyDTO(inverterAdditionalProperty);
        inverterAdditionalPropertyVO.setTodayElectricity(todayElectricity.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setMonthElectricity(monthElectricity.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setYearElectricity(yearElectricity.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setTotalElectricity(totalElectricity.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setIpv1(ipv1.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setIpv2(ipv2.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setIpv3(ipv3.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setIpv4(ipv4.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setIpv5(ipv5.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setIpv6(ipv6.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setIpv7(ipv7.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setIpv8(ipv8.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setIpv9(ipv9.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setIpv10(ipv10.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setIpv11(ipv11.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setIpv12(ipv12.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setVpv1(vpv1.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setVpv2(vpv2.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setVpv3(vpv3.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setVpv4(vpv4.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setVpv5(vpv5.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setVpv6(vpv6.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setVpv7(vpv7.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setVpv8(vpv8.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setVpv9(vpv9.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setVpv10(vpv10.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setVpv11(vpv11.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setVpv12(vpv12.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setIac1(iac1.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setIac2(iac2.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setIac3(iac3.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setVac1(vac1.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setVac2(vac2.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setVac3(vac3.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setFac1(fac1.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setFac2(fac2.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        inverterAdditionalPropertyVO.setFac3(fac3.divide(ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        return inverterAdditionalPropertyVO;

    }

}
