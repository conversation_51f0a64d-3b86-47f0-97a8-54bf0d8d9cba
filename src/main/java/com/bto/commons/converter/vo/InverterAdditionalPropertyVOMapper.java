package com.bto.commons.converter.vo;

import com.bto.commons.pojo.entity.InverterAdditionalProperty;
import com.bto.commons.pojo.dto.InverterAdditionalPropertyDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2023/4/23 10:37
 */
@Mapper(componentModel = "spring")
public interface InverterAdditionalPropertyVOMapper {
    InverterAdditionalPropertyVOMapper INSTANCE = Mappers.getMapper(InverterAdditionalPropertyVOMapper.class);

    @Mappings({
            @Mapping(source= "pv1Power",target="pvPower1"),
            @Mapping(source = "pv2Power",target="pvPower2"),
            @Mapping(source = "pv3Power",target="pvPower3"),
            @Mapping(source = "pv4Power",target="pvPower4"),
            @Mapping(source = "pv5Power",target="pvPower5"),
            @Mapping(source = "pv6Power",target="pvPower6"),
            @Mapping(source = "pv7Power",target="pvPower7"),
            @Mapping(source = "pv8Power",target="pvPower8"),
            @Mapping(source = "pv9Power",target="pvPower9"),
            @Mapping(source = "pv10Power",target="pvPower10"),
            @Mapping(source = "pv11Power",target="pvPower11"),
            @Mapping(source = "pv12Power",target="pvPower12"),
    })
    public InverterAdditionalPropertyDTO inverterAP2InverterAPVO(InverterAdditionalProperty inverterAdditionalProperty);

    public InverterAdditionalProperty inverterAPVO2inverterAP(InverterAdditionalPropertyDTO inverterAdditionalPropertyDTO);
}
