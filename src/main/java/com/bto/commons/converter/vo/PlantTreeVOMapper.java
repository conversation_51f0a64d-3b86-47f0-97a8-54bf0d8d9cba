package com.bto.commons.converter.vo;

import com.bto.commons.pojo.dto.PlantTreeDTO;
import com.bto.commons.pojo.entity.Plant;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2023/4/4 17:28
 */
@Mapper(componentModel = "spring")
public interface PlantTreeVOMapper {
    PlantTreeVOMapper INSTANCE = Mappers.getMapper(PlantTreeVOMapper.class);

    PlantTreeDTO plant2PlantTreeVo(Plant plant);

    Plant plantTreeVO2plant(PlantTreeDTO plantTreeDTO);
}
