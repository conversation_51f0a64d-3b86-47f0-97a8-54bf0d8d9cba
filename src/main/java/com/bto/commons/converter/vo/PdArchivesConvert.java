package com.bto.commons.converter.vo;

import com.bto.commons.pojo.dto.PdArchivesDTO;
import com.bto.commons.pojo.entity.PdArchivesEntity;
import com.bto.commons.pojo.vo.PdArchivesVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 配电室档案
*
* <AUTHOR> 
* @since  2024-06-03
*/
@Mapper
public interface PdArchivesConvert {
    PdArchivesConvert INSTANCE = Mappers.getMapper(PdArchivesConvert.class);

    PdArchivesEntity convert(PdArchivesVO vo);
    PdArchivesEntity convert(PdArchivesDTO vo);

    PdArchivesVO convert(PdArchivesEntity entity);

    List<PdArchivesVO> convertList(List<PdArchivesEntity> list);

}