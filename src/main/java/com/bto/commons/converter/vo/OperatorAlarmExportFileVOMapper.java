package com.bto.commons.converter.vo;

import com.bto.commons.pojo.dto.OperatorAlarmExportFileDTO;
import com.bto.commons.pojo.dto.OperatorAlarmQueryDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2023/6/29 10:00
 */
@Mapper(componentModel = "spring")
public interface OperatorAlarmExportFileVOMapper {
    OperatorAlarmExportFileVOMapper INSTANCE = Mappers.getMapper(OperatorAlarmExportFileVOMapper.class);

    @Mappings({
            @Mapping(source = "pageSize",target = "pageSize"),
            @Mapping(source = "currentPage",target="currentPage")
    })
    OperatorAlarmQueryDTO operatorAlarmExportFileVO2OperatorAlarmQueryVO(OperatorAlarmExportFileDTO operatorAlarmExportFileVO);
    @Mappings({
            @Mapping(source = "pageSize",target = "pageSize"),
            @Mapping(source = "currentPage",target="currentPage")
    })
    OperatorAlarmExportFileDTO operatorAlarmQueryVO2OperatorAlarmExportFileVO(OperatorAlarmQueryDTO operatorAlarmQueryDTO);
}
