package com.bto.commons.converter.vo;

import com.bto.commons.pojo.entity.InverterComponent;
import com.bto.commons.pojo.dto.InverterComponentQueryDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2023/5/25 11:07
 */
@Mapper(componentModel = "spring")
public interface InverterComponentQueryVOMapper {

    InverterComponentQueryVOMapper INSTANCE = Mappers.getMapper(InverterComponentQueryVOMapper.class);

    InverterComponent inverterComponentQueryVO2InverterComponent(InverterComponentQueryDTO inverterComponentQueryDTO);


}
