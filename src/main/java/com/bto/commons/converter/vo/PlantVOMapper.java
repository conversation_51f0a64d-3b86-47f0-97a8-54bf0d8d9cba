package com.bto.commons.converter.vo;


import com.bto.commons.pojo.entity.Plant;
import com.bto.commons.pojo.dto.PlantVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * 电站视图层类和电站数据库类之间的转换接口
 *
 * <AUTHOR>
 * @date 2023/4/3 16:33
 */
@Mapper(componentModel = "spring")
public interface PlantVOMapper {
    PlantVOMapper INSTANCE = Mappers.getMapper(PlantVOMapper.class);

    @Mappings({
            @Mapping(source = "createTime",target = "warrantyTime")
    })
    PlantVO plant2PlantVO(Plant plant);


    Plant plantVO2Plant(PlantVO plantVO);
}
