package com.bto.commons.converter.vo;

import com.bto.commons.pojo.entity.SensorDataEntity;
import com.bto.commons.pojo.vo.SensorDataVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 传感器数据
*
* <AUTHOR> 
* @since 1.0.0 2024-06-17
*/
@Mapper
public interface SensorDataConvert {
    SensorDataConvert INSTANCE = Mappers.getMapper(SensorDataConvert.class);

    SensorDataEntity convert(SensorDataVO vo);

    SensorDataVO convert(SensorDataEntity entity);

    List<SensorDataVO> convertList(List<SensorDataEntity> list);

}