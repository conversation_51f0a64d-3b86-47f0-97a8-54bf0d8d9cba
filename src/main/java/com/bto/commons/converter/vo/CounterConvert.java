package com.bto.commons.converter.vo;

import com.bto.commons.pojo.entity.CounterRecordEntity;
import com.bto.commons.pojo.vo.CounterRecordVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 配电柜数据
*
* <AUTHOR> 
* @since  2024-07-24
*/
@Mapper
public interface CounterConvert {
    CounterConvert INSTANCE = Mappers.getMapper(CounterConvert.class);

    CounterRecordEntity convert(CounterRecordVO vo);

    CounterRecordVO convert(CounterRecordEntity entity);

    List<CounterRecordVO> convertList(List<CounterRecordEntity> list);

}