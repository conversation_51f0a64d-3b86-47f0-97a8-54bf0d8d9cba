package com.bto.commons.converter.vo;

import com.bto.commons.pojo.dto.PlantStatisticsExportFileDTO;
import com.bto.commons.pojo.dto.PlantStatisticsQueryDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2023/6/28 15:07
 */
@Mapper(componentModel = "spring")
public interface PlantStatisticsExportFileVOMapper {
    PlantStatisticsExportFileVOMapper INSTANCE = Mappers.getMapper(PlantStatisticsExportFileVOMapper.class);

    @Mappings({
            @Mapping(source = "pageSize",target = "pageSize"),
            @Mapping(source = "currentPage",target="currentPage")
    })
    PlantStatisticsQueryDTO plantStatisticsExportFileVO2PlantStatisticsQueryVO(PlantStatisticsExportFileDTO plantStatisticsExportFileVO);
    @Mappings({
            @Mapping(source = "pageSize",target = "pageSize"),
            @Mapping(source = "currentPage",target="currentPage")
    })
    PlantStatisticsExportFileDTO PlantStatisticsQueryVO2PlantStatisticsExportFileVO(PlantStatisticsQueryDTO plantStatisticsQueryDTO);
}
