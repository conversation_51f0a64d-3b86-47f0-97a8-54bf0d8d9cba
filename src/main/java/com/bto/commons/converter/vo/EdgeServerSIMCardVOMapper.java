package com.bto.commons.converter.vo;

import com.bto.commons.pojo.entity.EdgeServerSIMCard;
import com.bto.commons.pojo.dto.EdgeServerSIMCardDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2023/6/20 14:57
 */
@Mapper(componentModel = "spring")
public interface EdgeServerSIMCardVOMapper {
    EdgeServerSIMCardVOMapper INSTANCE = Mappers.getMapper(EdgeServerSIMCardVOMapper.class);

    EdgeServerSIMCardDTO edgeServerSIMCard2EdgeServerSIMCardVO(EdgeServerSIMCard edgeServerSIMCard);

    EdgeServerSIMCard edgeServerSIMCardVO2EdgeServerSIMCard(EdgeServerSIMCardDTO edgeServerSIMCardDTO);
}
