package com.bto.system.storage.service;

import com.alibaba.fastjson.JSONObject;
import com.bto.system.storage.properties.StorageProperties;
import com.qiniu.http.Response;
import com.qiniu.storage.BucketManager;
import com.qiniu.storage.Configuration;
import com.qiniu.storage.Region;
import com.qiniu.storage.UploadManager;
import com.qiniu.util.Auth;
import com.qiniu.util.IOUtils;

import java.io.IOException;
import java.io.InputStream;
import java.time.Instant;
import java.util.Base64;

/**
 * 七牛云存储
 */
public class QiniuStorageService extends StorageService {
    private final UploadManager uploadManager;
    private final BucketManager bucketManager;
    private String token;
    private Auth auth;

    public QiniuStorageService(StorageProperties properties) {
        auth = Auth.create(properties.getQiniu().getAccessKey(), properties.getQiniu().getSecretKey());
        this.properties = properties;
        Configuration configuration = new Configuration(Region.autoRegion());
        uploadManager = new UploadManager(configuration);
        bucketManager = new BucketManager(auth, configuration);
        token = auth.uploadToken(properties.getQiniu().getBucketName());

    }
    @Override
    public String upload(byte[] data, String path) {
        if (tokenVerify(token)){
            auth = Auth.create(properties.getQiniu().getAccessKey(), properties.getQiniu().getSecretKey());
            token = auth.uploadToken(properties.getQiniu().getBucketName());
        }
        try {
            Response res = uploadManager.put(data, path, token);
            if (!res.isOK()) {
                throw new RuntimeException(res.toString());
            }

            return properties.getConfig().getDomain() + "/" + path;
        } catch (Exception e) {
            throw new RuntimeException("上传文件失败：", e);
        }
    }


    @Override
    public String upload(InputStream inputStream, String path) {
        try {
            byte[] data = IOUtils.toByteArray(inputStream);
            return this.upload(data, path);
        } catch (IOException e) {
            throw new RuntimeException("上传文件失败：", e);
        }
    }

    /**
     * 七牛云token校验是否过期
     */
    private Boolean tokenVerify(String token) {
        String[] tokenInfo = token.split(":");
        // 进行Base64解码
        byte[] decodedBytes = Base64.getDecoder().decode(tokenInfo[2]);
        // 将字节数组转换为字符串
        String decodedString = new String(decodedBytes);
        JSONObject jsonObject = JSONObject.parseObject(decodedString);
        // 获取当前时间戳进行比较是否过期
        long timestamp = Instant.now().getEpochSecond();
        long deadline = Long.parseLong(jsonObject.get("deadline").toString());
        return timestamp > deadline;
    }


}
