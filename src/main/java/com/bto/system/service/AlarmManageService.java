package com.bto.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bto.commons.pojo.dto.PlantAlarmQueryDTO;
import com.bto.commons.pojo.entity.ViewPlantAlarm;
import com.bto.commons.pojo.vo.RequireParamsDTO;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;

/**
 * <AUTHOR> by zhb on 2024/5/9.
 */

public interface AlarmManageService {

    /**
     * 查询电站告警信息列表
     * @param query
     * @param userInfo
     * @return
     */
    IPage<ViewPlantAlarm> getPlantAlarmInfoList(PlantAlarmQueryDTO query, RequireParamsDTO userInfo);

    void exportInverterAndOperatorAlarmInfo(PlantAlarmQueryDTO query, HttpServletResponse response);

    /**
     * 获取告警数量信息统计
     */
    HashMap<String, String> getAlarmNumInfo(RequireParamsDTO userInfo);



}
