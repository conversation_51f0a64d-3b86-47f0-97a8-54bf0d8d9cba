package com.bto.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bto.commons.pojo.dto.*;
import com.bto.commons.pojo.vo.PlantVO;
import com.bto.commons.pojo.vo.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/9 11:31
 */
public interface PlantService {
    /**
     * 获取电站列表
     *
     * @param query
     * @param userInfo
     * @return
     */
    IPage<PlantInfoVO> getPlantList(PlantQueryDTO query, RequireParamsDTO userInfo);

    List<PlantInfoVO> selectAll(RequireParamsDTO userInfo);

    PlantVO getPlantDetail(String plantUid);

    /**
     * 获取各省-各市电站信息与坐标
     * @param plantAddressDTO
     * @param userInfo
     * @return
     */
    List<AreaCoordinateInfoVO> getPlantInfoCoordinateOfArea(PlantAddressDTO plantAddressDTO, RequireParamsDTO userInfo);

    /**
     * 站点轮播
     * @param startIndex
     * @param endIndex
     * @param userInfo
     * @return
     */
    PlantCarouselDTO getPlantCarousel(String startIndex, String endIndex, RequireParamsDTO userInfo);

    IPage<WorkEfficiencyVO> getPlantElectricityRank(PowerPlantInfoQueryDTO query);

    /**
     * 生成文件
     * @param userInfo
     * @param query
     * @return List<PlantDigestDTO>
     */
    List<PlantDigestVO> exportPlantList(RequireParamsDTO userInfo, PlantExportFileDTO query) throws IllegalAccessException;
}
