package com.bto.system.service;

import com.bto.commons.pojo.dto.PdArchivesDTO;
import com.bto.commons.pojo.dto.PdArchivesQuery;
import com.bto.commons.pojo.entity.PdArchivesEntity;
import com.bto.commons.pojo.vo.PageResult;
import com.bto.commons.pojo.vo.PdArchivesVO;
import com.bto.commons.pojo.vo.PlantWithArchivesTree;

import java.util.List;

/**
 * 配电室档案
 *
 * <AUTHOR> 
 * @since  2024-06-03
 */
public interface PdArchivesService extends BaseService<PdArchivesEntity> {

    PageResult<PdArchivesVO> page(PdArchivesQuery query);

    void save(PdArchivesDTO dto);

    void update(PdArchivesVO vo);

    void delete(List<Long> idList);

    List<PlantWithArchivesTree> tree(PdArchivesQuery query);
}