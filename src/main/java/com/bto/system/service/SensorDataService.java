package com.bto.system.service;

import com.bto.commons.pojo.dto.SensorDataQuery;
import com.bto.commons.pojo.entity.SensorDataEntity;
import com.bto.commons.pojo.vo.PlantWithSensorTree;
import com.bto.commons.pojo.vo.SensorDataVO;

import java.util.List;

/**
 * 传感器数据
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-06-17
 */
public interface SensorDataService extends BaseService<SensorDataEntity> {


    void save(SensorDataVO vo);

    void update(SensorDataVO vo);

    void delete(List<Long> idList);

    List<PlantWithSensorTree> tree(SensorDataQuery query);
}