package com.bto.system.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.pojo.dto.*;
import com.bto.commons.pojo.vo.*;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * <AUTHOR> by zhb on 2024/5/9.
 */

public interface StatisticService {

    /**
     * 获取电站统计数量信息
     */
    NumInfoDTO getPlantNumInfo(RequireParamsDTO userInfo) throws NoSuchFieldException, IllegalAccessException;

    /**
     * 获取电站发电量与工作效率数据
     * @return
     */
    PlantElectricityVO getPlantElectricityInfo(RequireParamsDTO userInfo);

    /**
     * 获取逆变器统计数量信息
     * @return
     */
    NumInfoDTO getInverterNumInfo(RequireParamsDTO userInfo) throws NoSuchFieldException, IllegalAccessException;

    /**
     * 获取近六个月发电量数据
     */
    List<ChartElectricityInfoVO> getElectricityBySixMonth(RequireParamsDTO userInfo);

    /**
     * 查询所有电站当日每小时发电量统计
     */
    List<ChartElectricityInfoVO> getEveryHourElectricityInfo(RequireParamsDTO userInfo);

    /**
     * 能效收益统计-区域/发电统计
     * @param query
     * @param userInfo
     * @return
     */
    PageInfo<ElectricityStaticsInfoVO> getElectricityStatisticsInfo(ElectricityStatisticsQueryDTO query, RequireParamsDTO userInfo);


    /**
     * 导出Excel电量统计信息
     * @param query
     * @param userInfo
     * @return
     */
    PageInfo<ElectricityStaticsInfoVO> exportExcelElectricityStatisticsInfo(ElectricityStatisticsExportFileDTO query, RequireParamsDTO userInfo);

    /**
     * 获取电站统计数据信息
     * @param query
     */
    Page<PlantStatisticsInfoVO> getPlantStatisticsInfo(PlantStatisticsQueryDTO query, RequireParamsDTO userInfo);

    /**
     * 创建电站收益统计Excel文件
     *
     * @param userInfo
     * @param query
     * @return
     * @throws IllegalAccessException
     */
    Page<PlantStatisticsInfoVO> exportExcelPlantStatistics(RequireParamsDTO userInfo, PlantStatisticsExportFileDTO query) throws IllegalAccessException;

    /**
     * 综合统计图形数据查询
     * @param userInfo 用户信息
     * @param query 查询条件
     * @return
     */
    List<IntegrativeStatisticChartVO> integrativeStatisticChart(RequireParamsDTO userInfo, IntegrativeStatisticQueryDTO query);

    /**
     *  综合统计表格数据查询
     * @param userInfo
     * @param query
     * @return
     */
    IntegrativeStatisticSheetVO integrativeStatisticSheet(RequireParamsDTO userInfo, IntegrativeStatisticQueryDTO query) throws Exception;


    /**
     * 获取最近一个月每日的发电量数据
     */
    List<ChartElectricityInfoVO> getElectricityByNumDay(RequireParamsDTO userInfo, Integer dayNums);

    /**
     * 获取设备统计数量信息
     * @param userInfo
     * @return
     * @throws NoSuchFieldException
     * @throws IllegalAccessException
     */
    NumInfoDTO getDeviceNumInfo(RequireParamsDTO userInfo ) throws NoSuchFieldException, IllegalAccessException;

}
