package com.bto.system.service;

import com.bto.commons.pojo.dto.FunctionalInstrumentQuery;
import com.bto.commons.pojo.entity.FunctionalInstrumentEntity;
import com.bto.commons.pojo.vo.PageResult;

/**
 * 多功能仪表数据
 *
 * <AUTHOR>
 * @since 2024-10-09
 */
public interface FunctionalInstrumentService extends BaseService<FunctionalInstrumentEntity> {

    PageResult<FunctionalInstrumentEntity> page(FunctionalInstrumentQuery query);

}