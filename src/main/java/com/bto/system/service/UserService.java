package com.bto.system.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.pojo.dto.*;
import com.bto.commons.pojo.entity.User;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.pojo.vo.UserInfoVO;
import com.bto.commons.response.Result;
import com.bto.oauth.entity.UserLogin;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/4/20 14:43
 */
public interface UserService {
    public Map<String, String> getUserNameAndPhoneByUserUid(String userUid);

    /**
     * 用户列表多条件分页查询
     * @param query
     * @param userInfo
     */
    Page<UserInfoVO> getUserList(UserQueryDTO query, RequireParamsDTO userInfo);

    /**
     * 新增用户
     * @param userInfo
     * @return
     */
    Integer addUser(UserInfoDTO userInfo);

    /**
     * 修改用户
     * @param userInfo
     * @return
     */
    Integer editUser(UserInfoDTO userInfo);

    /**
     * 删除用户
     * @param userUid
     * @return
     */
    Integer deleteUser(String userUid);

    /**
     * 查询用户信息
     * @param userLogin
     */
    UserDTO selectUserInfo(UserLogin userLogin);

    /**
     * 根据用户Uid查询所属所有电站
     * @param userUid
     * @return
     */
    List<String> getPlantUidList(String userUid);

    /**
     * 用户密码修改
     * @param userPasswordDTO
     * @return
     */
    Integer updatePassword(UserPasswordDTO userPasswordDTO, RequireParamsDTO userInfo);

    /**
     * 根据用户id查询用户信息
     * @param userUid
     * @return
     */
    User getUserInfoByUserUid(String userUid);

    Result importByExcel(MultipartFile file, String password);

    void export(UserQueryDTO query, HttpServletResponse response);

    /**
     * 角色分配用户
     * @param roleUserDTO 角色ID、用户ID集合
     */
    void assignRoles(RoleUserDTO roleUserDTO);

    /**
     * 通过电话检查用户是否存在
     *
     * @param phoneNumber 电话号码
     * @return {@link HashMap }<{@link Boolean }, {@link String }>
     * <AUTHOR>
     * @since 2024-01-08 14:15:23
     */
    HashMap<Boolean, String> checkUserExistByPhone(String phoneNumber);

    Result resetPasswdByCode(String phoneNumber, String code,String password);

    String getLayoutByUser(String userId);

    Result updateLayoutByUser(String userId, String layout);
}
