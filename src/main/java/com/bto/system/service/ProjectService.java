package com.bto.system.service;

import com.bto.commons.pojo.vo.ProjectInfoVO;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.pojo.dto.ProjectInfoDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/5/18 14:51
 */
public interface ProjectService {
    /**
     * 查询项目专项信息
     * @param userInfo
     * @return
     */
    List<ProjectInfoVO> getProjectSpecialInfo(RequireParamsDTO userInfo);

    /**
     * 新增项目
     * @param projectInfo
     * @return
     */
    Integer addProject(ProjectInfoDTO projectInfo);

    /**
     * 修改项目名称
     * @param projectInfo
     * @return
     */
    Integer editProject(ProjectInfoDTO projectInfo);

    /**
     * 删除项目
     * @param projectID
     * @return
     */
    Integer deleteProject(Integer projectID);

    /**
     *查询项目父级id下所有项目id
     * @param projectID
     * @return
     */
    List<String> getProjectIDListByPid(String projectID);

    Map<String, Integer> getProjectMap();

    List<ProjectInfoVO> getProjectSpecialByPid(String projectId);
}
