package com.bto.system.service;

import com.bto.commons.pojo.vo.MenuInfoVO;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.pojo.dto.MenuInfoDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/18 18:09
 */
public interface MenuService {
    /**
     * 菜单列表多条件查询
     * @param userInfo
     * @return
     */
    List<MenuInfoVO> getMenuList(RequireParamsDTO userInfo);

    /**
     * 新增菜单
     * @param menuInfo
     * @return
     */
    Integer addMenu(MenuInfoDTO menuInfo);

    /**
     * 修改菜单
     * @param menuInfo
     * @return
     */
    Integer editMenu(MenuInfoDTO menuInfo);

    /**
     * 删除菜单
     * @param menUid
     * @return
     */
    Integer deleteMenu(String menUid);
}
