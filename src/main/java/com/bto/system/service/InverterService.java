package com.bto.system.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.pojo.dto.InverterListQueryDTO;
import com.bto.commons.pojo.vo.InverterDetailsVO;
import com.bto.commons.pojo.vo.InverterInfoVO;
import com.bto.commons.pojo.vo.InverterRealTimeQueryVO;
import com.bto.commons.pojo.vo.RequireParamsDTO;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/8 15:50
 */
public interface InverterService {


    /**
     * 获取逆变器日/月/年/总图表数据
     *
     * @param date
     * @param plantUid
     * @param <T>
     * @return
     */
    <T> List<T> getInverterChartInfo(String date, String plantUid);

    /**
     * 获取逆变器PV路数
     *
     * @param inverterSN
     */
    HashMap<String, Integer> getPvNums(String inverterSN);

    /**
     * 逆变器详情-图表曲线数据信息
     *
     * @param date
     * @param inverterSN
     * @return
     */
    List<LinkedHashMap<String, Object>> getInverterInfoChart(String date, String inverterSN);

    /**
     * inverterSN 获取逆变器详细信息
     *
     * @param inverterSN
     * @return
     */
    InverterDetailsVO getInverterDetails(String inverterSN);


    /**
     * 查询逆变器信息列表
     *
     * @param userInfo
     * @param query
     * @return
     */
    Page<InverterInfoVO> InverterInfoList(RequireParamsDTO userInfo, InverterListQueryDTO query);

    List<String> getInverterSn(String plantUid);

    String getOnlineInverterStats(String plantUid);

    String getPlantMaxPower(String today, List<String> inverterSN);

    Integer getInverterAlarmCount(@NotNull String inverterSN);

    /**
     * 获取逆变器实时数据
     * @param query
     */
    HashMap<String,Object> getInverterRealTimeData(InverterRealTimeQueryVO query);
}
