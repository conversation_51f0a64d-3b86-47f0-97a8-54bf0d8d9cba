package com.bto.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bto.commons.pojo.dto.AttachmentQuery;
import com.bto.commons.pojo.dto.StorageDTO;
import com.bto.commons.pojo.vo.AttachmentVO;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2023/6/17 11:57
 */
public interface ResourceFileService {
    /**
     * 项目logo图片上传
     * @param imgfile
     * @param projectId
     * @return
     * @throws IOException
     */
    StorageDTO uploadProjectLogo(MultipartFile imgfile, String projectId) throws IOException;

    /**
     * 用户头像上传
     * @param imgFile
     * @param userUid
     */
    StorageDTO uploadUserAvatar(MultipartFile imgFile, String userUid) throws IOException;

    /**
     * 分页查询附件列表
     * @param query
     * @return
     */
    IPage<AttachmentVO> getAttachmentByPage(AttachmentQuery query);

    StorageDTO uploadScreenLogo(MultipartFile imgFile, String projectId) throws IOException;

    StorageDTO uploadWorkOrderIssuePhoto(MultipartFile file);
}
