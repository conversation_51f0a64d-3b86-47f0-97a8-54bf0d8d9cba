package com.bto.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.pojo.dto.AddDeviceDTO;
import com.bto.commons.pojo.dto.CounterQuery;
import com.bto.commons.pojo.dto.OperatorQueryDTO;
import com.bto.commons.pojo.entity.OperatorRealTimeInfo;
import com.bto.commons.pojo.vo.CounterVO;
import com.bto.commons.pojo.vo.DeviceVO;
import com.bto.commons.pojo.vo.OperatorListVO;
import com.bto.commons.pojo.vo.RequireParamsDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/9 14:39
 */
public interface DeviceService {
    OperatorRealTimeInfo getPointStatusByPlantUid(String plantUid);

    Page<OperatorListVO> getOperatorInfo(RequireParamsDTO userInfo, OperatorQueryDTO query);

    void addDevice(AddDeviceDTO addDeviceDTO);

    void deleteDevice(List<String> ids);

    IPage<CounterVO> getCounterPage(CounterQuery query);

    void update(AddDeviceDTO dto);

    List<DeviceVO> counterMonitor(List<String> typeList);



    // /**
    //  * 根据电站编号查询电站所属所有逆变器SN
    //  * @param plantUid
    //  * @return
    //  */
    // public List<String> getInverterSn(String plantUid);
    //
    // Result getOnlineInverterStats(String plantUid);
    //
    // Result getPlantMaxPower(String today, List<String> inverterSN);
}
