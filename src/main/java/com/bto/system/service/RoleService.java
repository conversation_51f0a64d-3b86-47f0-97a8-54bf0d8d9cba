package com.bto.system.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.pojo.dto.RoleInfoDTO;
import com.bto.commons.pojo.dto.RoleQueryDTO;
import com.bto.commons.pojo.vo.RoleInfoVO;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/5/18 17:30
 */
public interface RoleService {
    /**
     * 角色列表多条件分页查询
     *
     * @param query
     * @return
     */
    Page<RoleInfoVO> getRoleList(RoleQueryDTO query);

    /**
     * 批量新增
     *
     * @param roleInfoDTO
     * @return
     */
    Integer addRole(RoleInfoDTO roleInfoDTO);

    /**
     * 修改角色信息
     *
     * @param roleInfoDTO
     * @return
     */
    Integer editRole(RoleInfoDTO roleInfoDTO);

    /**
     * 删除角色信息
     *
     * @param roleID
     * @return
     */

    Integer deleteRole(String roleID);

    Map<String, Long> getRoleMap(RoleQueryDTO query);
}
