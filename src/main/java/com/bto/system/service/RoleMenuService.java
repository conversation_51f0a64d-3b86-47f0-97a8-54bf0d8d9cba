package com.bto.system.service;

import com.bto.commons.pojo.vo.MenuInfoVO;
import com.bto.commons.pojo.entity.RoleHasMenu;

import java.util.List;

/**
 *  角色与菜单对应关系
 * <AUTHOR>
 * @date 2023/5/20 14:42
 */
public interface RoleMenuService extends BaseService<RoleHasMenu> {

    /**
     * 通过角色ID查询菜单列表
     * @param roleId
     * @return
     */
    List<MenuInfoVO> getMenuListByRole(Long roleId);
    /**
     * 新增或修改 角色&菜单关系
     * @param roleId      角色ID
     * @param menUidList  菜单ID列表
     */
    Integer saveOrUpdate(Long roleId, List<Long> menUidList);

    /**
     * 删除角色菜单关系
     * @param roleId
     */
    Integer delete(Long roleId);
}
