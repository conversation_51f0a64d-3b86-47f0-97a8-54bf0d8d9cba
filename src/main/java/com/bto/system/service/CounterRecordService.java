package com.bto.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bto.commons.pojo.dto.CounterQuery;
import com.bto.commons.pojo.entity.CounterRecordEntity;
import com.bto.commons.pojo.vo.CounterRecordVO;

import java.util.List;

/**
 * 配电柜数据
 *
 * <AUTHOR> 
 * @since  2024-07-24
 */
public interface CounterRecordService extends BaseService<CounterRecordEntity> {

    IPage<CounterRecordVO> page(CounterQuery query);

    void save(CounterRecordVO vo);

    void update(CounterRecordVO vo);

    void delete(List<Long> idList);
}