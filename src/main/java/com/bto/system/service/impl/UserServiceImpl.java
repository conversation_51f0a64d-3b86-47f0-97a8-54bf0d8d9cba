package com.bto.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bto.commons.enums.UserEnum;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.*;
import com.bto.commons.pojo.entity.Role;
import com.bto.commons.pojo.entity.User;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.pojo.vo.UserInfoVO;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.utils.CreateExcelUtils;
import com.bto.commons.utils.PropertyUtils;
import com.bto.oauth.entity.UserLogin;
import com.bto.system.dao.RoleMapper;
import com.bto.system.dao.UserMapper;
import com.bto.system.service.ProjectService;
import com.bto.system.service.RoleService;
import com.bto.system.service.UserService;
import com.bto.system.utils.GlobalParamUtil;
import com.bto.system.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

import static com.bto.commons.constant.RedisKey.RESET_PASSWD_CODE;

/**
 * <AUTHOR>
 * @date 2023/4/20 14:46
 */
@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {
    @Autowired
    private UserMapper userMapper;
    @Resource
    private RoleService roleService;
    @Resource
    private ProjectService projectService;
    @Resource
    private GlobalParamUtil globalParamUtil;

    @Autowired
    private RoleMapper roleMapper;
    @Autowired
    private RedisUtil redisUtil;

    @Override
    public Page<UserInfoVO> getUserList(UserQueryDTO query, RequireParamsDTO userInfo) {
        Page<UserInfoVO> userPage = new Page<>(query.getCurrentPage(), query.getPageSize());
        Page<UserInfoVO> page = userMapper.getUserList(userPage, query, userInfo);
        return page;
    }

    @Override
    public Integer addUser(UserInfoDTO userInfo) {

        if (userInfo.getUserPhone() != null) {
            boolean exist = checkUserPhone(userInfo.getUserPhone());
            if (exist) {
                throw new BusinessException("手机号已存在");
            }
        }


        User user = new User();
        user.setUserUid(UUID.randomUUID().toString().toUpperCase());
        user.setCreateTime(DateUtil.date());
        BeanUtils.copyProperties(userInfo, user, PropertyUtils.getNullPropertyNames(userInfo));
        Integer count = userMapper.insert(user);
        return count;
    }

    /**
     * 检查用户电话
     *
     * @param phone 电话
     * @return boolean
     * <AUTHOR>
     * @since 2024-01-09 15:48:31
     */
    private boolean checkUserPhone(String phone) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getUserPhone, phone);
        User user = this.getOne(wrapper);
        if (user != null) {
            return true;
        }
        return false;
    }

    @Override
    public Integer editUser(UserInfoDTO userInfo) {

        String userPhone = userInfo.getUserPhone();
        if (userPhone != null) {
            LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(User::getUserPhone, userPhone)
                    .ne(User::getUserUid, userInfo.getUserUid());
            User user = this.getOne(wrapper);
            if (user != null) {
                throw new BusinessException("手机号已存在");
            }
        }


        // 判断用户是否存在
        User user = userMapper.selectById(userInfo.getUserUid());
        if (user != null) {
            BeanUtils.copyProperties(userInfo, user, PropertyUtils.getNullPropertyNames(userInfo));
            // 更新用户
            int count = userMapper.updateById(user);
            return count;
        } else {
            throw new BusinessException(
                    ResultEnum.USER_NON_EXISTENT.getCode(),
                    ResultEnum.USER_NON_EXISTENT.getMessage()
            );
        }
    }

    @Override
    public Integer deleteUser(String userUid) {
        // 判断用户是否存在
        User user = userMapper.selectById(userUid);
        if (user != null) {
            // 更新用户
            int count = userMapper.deleteById(userUid);
            return count;
        } else {
            throw new BusinessException(
                    ResultEnum.USER_NON_EXISTENT.getCode(),
                    ResultEnum.USER_NON_EXISTENT.getMessage()
            );
        }
    }

    @Override
    public UserDTO selectUserInfo(UserLogin userLogin) {
        UserDTO userInfo = userMapper.selectUserByUserInfo(userLogin);
        return userInfo;
    }

    @Override
    public List<String> getPlantUidList(String userUid) {
        List<String> plantUidList = userMapper.getPlantUidList(userUid);
        return plantUidList;
    }

    @Override
    public Integer updatePassword(UserPasswordDTO userPasswordDTO, RequireParamsDTO userInfo) {
        QueryWrapper<User> userQuery = new QueryWrapper<>();
        userQuery.eq("user_uid", userInfo.getUserUid());
        userQuery.eq("user_password", userPasswordDTO.getOldPass());
        User user = userMapper.selectOne(userQuery);
        if (user == null) {
            throw new BusinessException(
                    ResultEnum.PASSWORD_VERIFICATION_FAILED.getCode(),
                    ResultEnum.PASSWORD_VERIFICATION_FAILED.getMessage()
            );
        }
        user.setUserPassword(userPasswordDTO.getNewPass());
        return userMapper.updateById(user);
    }


    @Override
    public Map<String, String> getUserNameAndPhoneByUserUid(String userUid) {
        QueryWrapper<User> userQueryWrapper = new QueryWrapper<>();
        userQueryWrapper.eq("user_uid", userUid);
        User user = userMapper.selectOne(userQueryWrapper);
        if (user == null) {
            return null;
        }
        HashMap<String, String> userNameAndPhone = new HashMap<>();
        userNameAndPhone.put("userName", user.getUserName());
        userNameAndPhone.put("userPhone", user.getUserPhone());
        return userNameAndPhone;
    }

    @Override
    public User getUserInfoByUserUid(String userUid) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getUserUid, userUid);
        return userMapper.selectOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result importByExcel(MultipartFile file, String password) {

        ArrayList<User> users = new ArrayList<>();
        List<User> insertUserList;
        try {
            EasyExcel.read(file.getInputStream(), ExcelImportUserDTO.class, new PageReadListener<ExcelImportUserDTO>(dataList -> {
                Map<String, Long> roleMap = roleService.getRoleMap(RoleQueryDTO.builder().build());
                Map<String, Integer> projectMap = projectService.getProjectMap();
                for (ExcelImportUserDTO data : dataList) {
                    String projectName = data.getProjectName();
                    data.setProjectID(projectMap.get(projectName));
                    String roleName = data.getRoleName();
                    data.setRoleID(roleMap.get(roleName));
                    User user = BeanUtil.copyProperties(data, User.class);
                    // 初始化市数据
                    user.setUserPassword(password);
                    user.setUserStatus(Integer.parseInt(UserEnum.USER_STATUS_ENABLE.getCode()));
                    // 检查是否存在相同的用户名或手机号
                    if (users.stream().anyMatch(u -> u.getUserName().equals(user.getUserName()) || u.getUserPhone().equals(user.getUserPhone()))) {
                        // 存在相同数据，抛出异常
                        throw new BusinessException(ResultEnum.DATA_EXISTED_ERROR.getCode(), "Excel中存在相同的用户名或手机号：" + user.getUserName() + ", " + user.getUserPhone());
                    }
                    users.add(user);
                }
            })).sheet().doRead();
        } catch (IOException e) {
            throw new BusinessException(ResultEnum.SYSTEM_RUNTIME_FAILED.getCode(), e.getMessage());
        }
        Set<String> userNames = users.stream().map(User::getUserName).collect(Collectors.toSet());
        Set<String> userPhones = users.stream().map(User::getUserPhone).collect(Collectors.toSet());
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(User::getUserName, userNames).or().in(User::getUserPhone, userPhones);
        List<User> dbData = userMapper.selectList(wrapper);

        int start = users.size();
        // 如果有重复的数据  就去除重复的数据
        List<User> filteredUsers = new ArrayList<>();
        if (CollUtil.isNotEmpty(dbData)) {
            Set<String> existingUsernames = dbData.stream().map(User::getUserName).collect(Collectors.toSet());
            Set<String> existingPhoneNumbers = dbData.stream().map(User::getUserPhone).collect(Collectors.toSet());

            insertUserList = users.stream()
                    .peek(user -> {
                        if (existingUsernames.contains(user.getUserName()) || existingPhoneNumbers.contains(user.getUserPhone())) {
                            filteredUsers.add(user);
                        }
                    })
                    .filter(user -> !existingUsernames.contains(user.getUserName()) && !existingPhoneNumbers.contains(user.getUserPhone()))
                    .collect(Collectors.toList());

        } else {
            insertUserList = users;
        }

        // 插入数据库
        if (CollUtil.isNotEmpty(insertUserList)) {
            try {
                this.saveBatch(insertUserList);
            } catch (Exception e) {
                Throwable cause = e.getCause();
                if (cause instanceof SQLException) {
                    String message = cause.getMessage();
                    if (message.contains("Duplicate entry")) {
                        // 解析出用户名和手机号
                        String[] parts = message.split("'");
                        if (parts.length >= 2) {
                            String item = parts[1];
                            throw new BusinessException(ResultEnum.DATA_EXISTED_ERROR.getCode(),
                                    "存在已删除的相同的用户名或手机号：" + item);
                        }
                    }
                }
                // 如果无法解析，抛出通用异常
                throw new BusinessException(ResultEnum.OPERATION_FAILED.getCode(), "数据插入失败,请联系管理员");
            }
        }
        int end = insertUserList.size();

        return Result.success(String.format("共 %d 条数据，成功插入 %d 条，失败 %d 条。", start, insertUserList.size(), start - end), filteredUsers);
    }

    @Override
    public void export(UserQueryDTO query, HttpServletResponse response) {
            query.setOrder(StrUtil.isEmpty(query.getOrder()) ? "createTime" : query.getOrder());
            Page<UserInfoVO> page = this.getUserList(query, globalParamUtil.getUserInfo());
            List<UserInfoVO> records = page.getRecords();
            CreateExcelUtils.exportToExcel(records,UserInfoVO.class,"user_info",response);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void assignRoles(RoleUserDTO roleUserDTO) {
        String roleId = roleUserDTO.getRoleId();
        roleUserDTO.getUserIdList().forEach(userId -> {
            User user = userMapper.selectById(userId);
            if (Objects.isNull(user)) {
                throw new BusinessException("用户Id：" + userId + "不存在");
            }
            if (roleId.equals(user.getRoleID())) {
                throw new BusinessException("用户：" + user.getUserName() + "已设置该角色，请不要重复设置");
            }
            Role role = roleMapper.selectById(roleId);
            if (Objects.isNull(role)) {
                throw new BusinessException("角色Id：" + roleId + "不存在");
            }
            user.setRoleID(roleId);
            userMapper.updateById(user);
        });
    }

    @Override
    public HashMap<Boolean, String> checkUserExistByPhone(String phoneNumber) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .eq(User::getUserPhone, phoneNumber);
        User user = this.getOne(wrapper);

        HashMap<Boolean, String> map = new HashMap<>();
        if (user == null) {
            map.put(Boolean.FALSE, "手机号未注册");
            return map;
        }

        if (user.getUserStatus().equals(0)) {
            map.put(Boolean.FALSE, "该账号未启用");
        }

        map.put(Boolean.TRUE, "");
        return map;
    }

    @Override
    public Result resetPasswdByCode(String phoneNumber, String code, String password) {
        ResetPasswdDTO dto = (ResetPasswdDTO) redisUtil.get(RESET_PASSWD_CODE + phoneNumber);
        if (dto == null || !code.equals(dto.getCode())) {
            return Result.failed("验证码错误");
        }

        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .eq(User::getUserPhone, phoneNumber)
                .eq(User::getUserStatus, 1);

        User user = this.getOne(wrapper);

        if (user == null) {
            return Result.failed("用户不存在");
        }
        user.setUserPassword(password);
        boolean success = this.updateById(user);
        if (success) {
            return Result.success(ResultEnum.OPERATION_SUCCESS);
        }
        return Result.failed(ResultEnum.OPERATION_FAILED);
    }

    @Override
    public String getLayoutByUser(String userId) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getUserUid, userId);
        User user = this.getOne(wrapper);
        if (user != null) {
            return user.getLayout();
        }
        return null;
    }

    @Override
    public Result updateLayoutByUser(String userId, String layout) {
        boolean update = this.lambdaUpdate().eq(User::getUserUid, userId).set(User::getLayout, layout).update();
        if (update) {
            return Result.success(ResultEnum.OPERATION_SUCCESS);
        }
        return Result.failed(ResultEnum.OPERATION_FAILED);
    }
}