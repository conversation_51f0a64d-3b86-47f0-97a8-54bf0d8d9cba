package com.bto.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bto.commons.pojo.dto.FireFightingQuery;
import com.bto.commons.pojo.entity.FireFightingEntity;
import com.bto.commons.pojo.vo.PageResult;
import com.bto.system.dao.FireFightingDao;
import com.bto.system.service.FireFightingService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 消防联动
 *
 * <AUTHOR>
 * @since 1.0.0 2024-09-27
 */
@Service
@AllArgsConstructor
public class FireFightingServiceImpl extends ServiceImpl<FireFightingDao, FireFightingEntity> implements FireFightingService {

    @Override
    public PageResult<FireFightingEntity> page(FireFightingQuery query) {
        Page<FireFightingEntity> page = new Page<>(query.getCurrentPage(), query.getPageSize());
        page = baseMapper.page(page, query, null);

        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    private LambdaQueryWrapper<FireFightingEntity> getWrapper(FireFightingQuery query) {
        LambdaQueryWrapper<FireFightingEntity> wrapper = Wrappers.lambdaQuery();
        wrapper
                .orderByDesc(FireFightingEntity::getUpdateTime)
        ;

        return wrapper;
    }


}