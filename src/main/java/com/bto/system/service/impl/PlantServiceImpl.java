package com.bto.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.constant.Constant;
import com.bto.commons.converter.dto.PlantDTOMapper;
import com.bto.commons.enums.DeviceStatus;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.*;
import com.bto.commons.pojo.entity.InverterAlarm;
import com.bto.commons.pojo.entity.Plant;
import com.bto.commons.pojo.vo.PlantVO;
import com.bto.commons.pojo.vo.*;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.utils.BusinessCalculateUtil;
import com.bto.commons.utils.DateUtils;
import com.bto.system.dao.DeviceMapper;
import com.bto.system.dao.InverterAlarmMapper;
import com.bto.system.dao.PlantMapper;
import com.bto.system.service.InverterService;
import com.bto.system.service.PlantService;
import com.bto.system.utils.GlobalParamUtil;
import io.jsonwebtoken.lang.Collections;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/5/9 11:32
 */
@Service(value = "plantService")
@Slf4j
public class PlantServiceImpl implements PlantService {
    @Autowired
    private PlantMapper plantMapper;
    @Autowired
    private InverterService inverterService;
    @Autowired
    private InverterAlarmMapper inverterAlarmMapper;

    @Autowired
    private GlobalParamUtil globalParamUtil;

    @Autowired
    private PlantDTOMapper plantDTOMapper;

    @Autowired
    private DeviceMapper deviceMapper;

    @Override
    public IPage<PlantInfoVO> getPlantList(PlantQueryDTO query, RequireParamsDTO userInfo) {
        if (DateUtils.compareDate(query.getStartCreateTime(), query.getEndCreateTime())) {
            throw new BusinessException(ResultEnum.DATETIME_CHECK_FAILED);
        }
        Page<PlantInfoVO> page = new Page<>(query.getCurrentPage(), query.getPageSize());
        if (ObjectUtil.isEmpty(query.getOrder())) {
            query.setOrder("create_time");
        }
        return plantMapper.getPlantList(query, userInfo, page);
    }

    @Override
    public List<PlantInfoVO> selectAll(RequireParamsDTO userInfo) {
        return plantMapper.selectAll(userInfo, null);
    }

    @Override
    public PlantVO getPlantDetail(String plantUid) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        // 获取消防模块信息
        CompletableFuture<List<DeviceVO>> mission = CompletableFuture.supplyAsync(() -> deviceMapper.selectListByType(plantUid, userInfo, 13));

        // todo fei调用
        String today = DateUtil.today();

        // 查询电站基础信息和用户信息
        PlantVO plantInfo = plantMapper.selectPlantInfo(plantUid);

        if (ObjectUtil.isEmpty(plantInfo)) {
            throw new BusinessException(ResultEnum.PLANT_NOT_EXIST);
        }
        // 查询电站所属所有逆变器SN列表
        List<String> inverterSnList = inverterService.getInverterSn(plantUid);
        plantInfo.setInverterSN(inverterSnList);

        // 如果该电站存在逆变器
        if (CollUtil.isNotEmpty(plantInfo.getInverterSN())) {
            // 查询逆变器当日告警数量

            Integer inverterAlarmNum = this.getInverterAlarmNum(plantInfo.getInverterSN(), today);
            plantInfo.setTodayAlarmNum(inverterAlarmNum);
            // 查询在线逆变器数量
            String onlineInverterNum = inverterService.getOnlineInverterStats(plantUid);
            plantInfo.setOnlineInverterNum(onlineInverterNum);
            // 查询峰值功率
            String plantMaxPower = inverterService.getPlantMaxPower(today, plantInfo.getInverterSN());
            plantInfo.setMaxPower(plantMaxPower);
        }
        String plantCapacity = plantInfo.getPlantCapacity();
        String totalElectricity = plantInfo.getTotalElectricity();
        // 计算工作效率
        plantInfo.setWorkEfficiency(BusinessCalculateUtil.getWorkEfficiency(plantInfo.getPower().toString(), plantCapacity));
        // 计算等效植树数量
        plantInfo.setTreeNum(BusinessCalculateUtil.getTreeNum(totalElectricity));
        // 计算Co2减排量
        plantInfo.setReduceCo2(BusinessCalculateUtil.getReduceCo2(totalElectricity));
        // 计算日、总 收益
        plantInfo.setTodayIncome(BusinessCalculateUtil.getIncome(plantInfo.getTodayElectricity(), plantInfo.getSalePrice()));
        plantInfo.setTotalIncome(BusinessCalculateUtil.getIncome(totalElectricity, plantInfo.getSalePrice()));
        // 计算日、年 等效利用小时
        plantInfo.setDailyEfficiencyPerHour(BusinessCalculateUtil.getEfficiencyPerHours(plantInfo.getTodayElectricity(), plantCapacity));
        plantInfo.setYearlyEfficiencyPerHour(BusinessCalculateUtil.getEfficiencyPerHours(totalElectricity, plantCapacity));
        // 计算质保日期
        plantInfo.setWarrantyTime(DateUtils.getDateOffsetByYear(plantInfo.getCreateTime(), Constant.WARRANTY_TERM));
        // 发电量单位换算
        plantInfo.setTodayElectricity(BusinessCalculateUtil.getRealElectricity(plantInfo.getTodayElectricity()));
        plantInfo.setMonthElectricity(BusinessCalculateUtil.getRealElectricity(plantInfo.getMonthElectricity()));
        plantInfo.setYearElectricity(BusinessCalculateUtil.getRealElectricity(plantInfo.getYearElectricity()));
        plantInfo.setTotalElectricity(BusinessCalculateUtil.getRealElectricity(plantInfo.getTotalElectricity()));
        plantInfo.setPlantCapacity(BusinessCalculateUtil.getRealPlantCapacity(plantInfo.getPlantCapacity()));

        try {
            List<DeviceVO> list = mission.get(300, TimeUnit.SECONDS);
            List<String> fireFighting = list.stream().map(DeviceVO::getDeviceId).collect(Collectors.toList());
            plantInfo.setFireFighting(fireFighting);
        } catch (Exception e) {
            log.error("获取消防模块超时,e =" + e);
            throw new BusinessException("获取消防模块超时");
        }

        return plantInfo;
    }

    @NotNull
    private Integer getInverterAlarmNum(List<String> inverterSnList, String date) {
        LambdaQueryWrapper<InverterAlarm> countQueryWrapper = new LambdaQueryWrapper<>();
        countQueryWrapper.in(InverterAlarm::getInverterSn, inverterSnList)
                .eq(InverterAlarm::getAlarmDate, date);
        Long count = inverterAlarmMapper.selectCount(countQueryWrapper);
        return count.intValue();
    }

    @Override
    public List<AreaCoordinateInfoVO> getPlantInfoCoordinateOfArea(PlantAddressDTO plantAddressDTO, RequireParamsDTO userInfo) {
        List<AreaCoordinateInfoVO> result = plantMapper.getPlantInfoCoordinateOfArea(plantAddressDTO, userInfo);
        return result;
    }

    @Override
    public PlantCarouselDTO getPlantCarousel(String startIndex, String endIndex, RequireParamsDTO userInfo) {

        Integer start = Integer.valueOf(startIndex);
        Integer end = Integer.valueOf(endIndex);

        PlantCarouselDTO result = new PlantCarouselDTO();
        List<PlantCarouselVO> resultList = new ArrayList<>();


        List<PlantCarouselVO> plantList = plantMapper.selectPlantList(userInfo);
        if (start < end) {
            for (int i = start; i < end && i < plantList.size(); i++) {
                plantList.get(i).setIndex(i + 1);
                resultList.add(plantList.get(i));
            }
        } else {
            for (int i = start; i < plantList.size(); i++) {
                plantList.get(i).setIndex(i + 1);
                resultList.add(plantList.get(i));
            }
            for (int i = 0; i < Math.min(end, plantList.size()); i++) {
                plantList.get(i).setIndex(i + 1);
                resultList.add(plantList.get(i));
            }
        }

        // 计算电站效率与离线时间
        LocalDateTime now = LocalDateTime.now();
        // 定义updateTime字符串的日期/时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        for (PlantCarouselVO plantCarouselDTO : resultList) {
            String workEfficiency = BusinessCalculateUtil.getWorkEfficiency(plantCarouselDTO.getPower(), plantCarouselDTO.getPlantCapacity().multiply(BigDecimal.valueOf(1000)).toString());
            plantCarouselDTO.setPlantEfficiency(workEfficiency);
            if (DeviceStatus.OFFLINE.getName().equals(plantCarouselDTO.getStatus())) {
                // 将updateTime字符串解析为LocalDateTime对象
                LocalDateTime updateTime = LocalDateTime.parse(plantCarouselDTO.getUpdateTime(), formatter);
                // 计算updateTime和当前时间之间的小时差异
                Duration duration = Duration.between(updateTime, now);
                long hoursDiff = duration.toHours();
                // 计算天数差异
                long daysDiff = hoursDiff / 24;
                // 剩余的小时数
                hoursDiff = hoursDiff % 24;
                String timeDiff;
                timeDiff = hoursDiff == 0 ? "-" : daysDiff + "天" + String.format("%02d", hoursDiff) + "小时";
                plantCarouselDTO.setOfflineTime(timeDiff);
            } else {
                String timeDiff = "-";
                plantCarouselDTO.setOfflineTime(timeDiff);
            }
        }
        result.setRecords(resultList);
        result.setTotal(plantList.size());

        return result;
    }

    @Override
    public IPage<WorkEfficiencyVO> getPlantElectricityRank(PowerPlantInfoQueryDTO query) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        IPage<WorkEfficiencyVO> page = plantMapper.getPlantElectricityRank(query, userInfo, new Page<>(query.getCurrentPage(), query.getPageSize()));
        return page;
    }

    public List<PlantDigestVO> exportPlantList(RequireParamsDTO userInfo, PlantExportFileDTO query) throws IllegalAccessException {
        if (!StringUtils.isNotBlank(query.getSheetName()) && !StringUtils.isNotEmpty(query.getSheetName())) {
            query.setSheetName("电站列表");
        }
        QueryWrapper<Plant> plantQueryWrapper = new QueryWrapper<>();
        plantQueryWrapper.eq("is_deleted", "0");
        if (userInfo.getPlantList() != null && userInfo.getPlantList().size() > 0) {
            plantQueryWrapper.in("plant_uid", userInfo.getPlantList());
            plantQueryWrapper.like(StringUtils.isNotBlank(query.getPlantName()), "plant_name", query.getPlantName());
            plantQueryWrapper.in(!Collections.isEmpty(query.getMultiPlantStatus()), "plant_status", query.getMultiPlantStatus());
            plantQueryWrapper.eq(StringUtils.isNotBlank(query.getPowerDistributor()), "power_distributor", query.getPowerDistributor());
        } else if (userInfo.getProjectList() != null && userInfo.getProjectList().size() > 0) {
            plantQueryWrapper.in("project_special", userInfo.getProjectList());
            plantQueryWrapper.like(StringUtils.isNotBlank(query.getPlantName()), "plant_name", query.getPlantName());
            plantQueryWrapper.in(CollectionUtil.isNotEmpty(query.getMultiPlantStatus()), "plant_status", query.getMultiPlantStatus());
            plantQueryWrapper.eq(StringUtils.isNotBlank(query.getPowerDistributor()), "power_distributor", query.getPowerDistributor());
        } else {
            throw new BusinessException(ResultEnum.USER_ACCOUNT_INVALIDATED);
        }
        plantQueryWrapper.orderByAsc("plant_name");
        List<Plant> plantList = plantMapper.selectList(plantQueryWrapper);
        List<PlantDigestVO> plantDigestVOList = new ArrayList<PlantDigestVO>();
        for (Plant plant : plantList) {
            PlantDigestVO plantDigestVO = plantDTOMapper.plant2PlantDTODecorator(plant);
            plantDigestVOList.add(plantDigestVO);
        }
        return plantDigestVOList;
    }

}
