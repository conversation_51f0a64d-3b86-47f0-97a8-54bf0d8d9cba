package com.bto.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bto.commons.converter.vo.CounterConvert;
import com.bto.commons.pojo.dto.CounterQuery;
import com.bto.commons.pojo.entity.CounterRecordEntity;
import com.bto.commons.pojo.vo.CounterRecordVO;
import com.bto.system.dao.CounterRecordDao;
import com.bto.system.service.CounterRecordService;
import com.bto.system.utils.GlobalParamUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 配电柜数据
 *
 * <AUTHOR>
 * @since 2024-07-24
 */
@Service
@AllArgsConstructor
public class CounterRecordServiceImpl extends ServiceImpl<CounterRecordDao, CounterRecordEntity> implements CounterRecordService {

    private final GlobalParamUtil globalParamUtil;

    @Override
    public IPage<CounterRecordVO> page(CounterQuery query) {
        IPage<CounterRecordEntity> page = new Page<>(query.getCurrentPage(), query.getPageSize());
        LambdaQueryWrapper<CounterRecordEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .eq(StrUtil.isNotBlank(query.getCounterId()),CounterRecordEntity::getCounterId,query.getCounterId())
                .orderByDesc(CounterRecordEntity::getInitTime)
        ;

        page  = baseMapper.selectPage(page, wrapper);
        IPage<CounterRecordVO> voPage = new Page<>();
        BeanUtil.copyProperties(page,voPage);
        List<CounterRecordVO> vos = BeanUtil.copyToList(page.getRecords(), CounterRecordVO.class);
        voPage.setRecords(vos);
        return voPage;
    }

    private LambdaQueryWrapper<CounterRecordEntity> getWrapper(CounterQuery query) {
        LambdaQueryWrapper<CounterRecordEntity> wrapper = Wrappers.lambdaQuery();

        return wrapper;
    }

    @Override
    public void save(CounterRecordVO vo) {
        CounterRecordEntity entity = CounterConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    @Override
    public void update(CounterRecordVO vo) {
        CounterRecordEntity entity = CounterConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        removeByIds(idList);
    }

}