package com.bto.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.constant.*;
import com.bto.commons.converter.vo.ElectricityStatisticsExportFileVOMapper;
import com.bto.commons.converter.vo.PlantStatisticsExportFileVOMapper;
import com.bto.commons.enums.*;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.*;
import com.bto.commons.pojo.vo.*;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.utils.BusinessCalculateUtil;
import com.bto.commons.utils.DateUtils;
import com.bto.commons.utils.ReflectUtil;
import com.bto.commons.utils.TableUtils;
import com.bto.system.dao.StatisticsMapper;
import com.bto.system.service.ProjectService;
import com.bto.system.service.StatisticService;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.bto.commons.constant.Constant.*;

/**
 * <AUTHOR> by zhb on 2024/5/9.
 */

@Service
public class StatisticServiceImpl implements StatisticService {

    @Autowired
    private StatisticsMapper statisticsMapper;

    @Autowired
    private ElectricityStatisticsExportFileVOMapper electricityStatisticsExportFileVOMapper;

    @Autowired
    private PlantStatisticsExportFileVOMapper plantStatisticsExportFileVOMapper;

    @Autowired
    private ProjectService projectService;
    @Override
    public NumInfoDTO getPlantNumInfo(RequireParamsDTO userInfo) throws NoSuchFieldException, IllegalAccessException {
        NumInfoDTO numInfo = new NumInfoDTO();
        List<NumInfoVO> numInfoList = statisticsMapper.getPlantNumInfo(userInfo);
        for (NumInfoVO infoDTO : numInfoList) {
            String fieldName = PlantStatusEnum.getFieldNameByCode(infoDTO.getStatus());
            Field field = numInfo.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(numInfo, infoDTO.getStatusNum());
        }
        String totalNum = BusinessCalculateUtil.getTotalNum(numInfo);
        String onlineNum = BusinessCalculateUtil.getPlantOnlineNum(numInfo);
        String normalNum = BusinessCalculateUtil.getRealNormalNum(numInfo);
        numInfo.setOnlineRate(onlineNum, totalNum);
        numInfo.setNormalRate(normalNum, totalNum);
        numInfo.setTotalNum(totalNum);
        numInfo.setOnlineNum(onlineNum);
        numInfo.setNormalNum(normalNum);
        return numInfo;
    }

    @Override
    public PlantElectricityVO getPlantElectricityInfo(RequireParamsDTO userInfo) {
        PlantElectricityVO plantElectricityInfo = statisticsMapper.getPlantElectricityInfo(userInfo);
        PlantElectricityVO workEfficiencyInfo = statisticsMapper.getWorkEfficiencyInfo(userInfo);
        String plantMass = null;
        if (UserEnum.USER_OF_ENTERPRISE.getCode().equals(userInfo.getUserType()) && !userInfo.getProjectList().isEmpty()) {
            // 获取电站故障率
            plantMass = statisticsMapper.getPlantMassByProjectId(userInfo, DateUtil.today());
        } else {
            Integer errorNumber = statisticsMapper.getPlantMassByPlantList(userInfo);
            if (Objects.isNull(errorNumber)) {
                plantMass = "0";
            } else {
                double v = (double) errorNumber / userInfo.getPlantList().size();
                plantMass = Double.toString(v);
            }
        }

        if (workEfficiencyInfo == null) {
            plantElectricityInfo = new PlantElectricityVO();
            workEfficiencyInfo = new PlantElectricityVO();
        }
        plantElectricityInfo.setAverageEfficiency(workEfficiencyInfo.getAverageEfficiency());
        plantElectricityInfo.setPlantName(workEfficiencyInfo.getPlantName());
        plantElectricityInfo.setMaxEfficiency(workEfficiencyInfo.getMaxEfficiency());
        String realEfficiencyPerHours = BusinessCalculateUtil.getRealEfficiencyPerHours(plantElectricityInfo.getTodayElectricity(), plantElectricityInfo.getPlantCapacity());
        plantElectricityInfo.setDailyEfficiencyPerHour(realEfficiencyPerHours);
        plantElectricityInfo.setFailureRate(plantMass);
        return plantElectricityInfo;
    }

    @Override
    public NumInfoDTO getInverterNumInfo(RequireParamsDTO userInfo) throws NoSuchFieldException, IllegalAccessException {
        NumInfoDTO numInfo = new NumInfoDTO();
        List<NumInfoVO> inverterNumList = statisticsMapper.getInverterNumInfo(userInfo);
        for (NumInfoVO infoDTO : inverterNumList) {
            String fieldName = InverterStatusEnum.getFieldNameByCode(infoDTO.getStatus());
            Field field = numInfo.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(numInfo, infoDTO.getStatusNum());
        }
        String totalNum = BusinessCalculateUtil.getTotalNum(numInfo);
        String onlineNum = BusinessCalculateUtil.getInverterOnlineNum(numInfo);
        String normalNum = BusinessCalculateUtil.getRealNormalNum(numInfo);
        numInfo.setTotalNum(totalNum);
        numInfo.setOnlineRate(onlineNum, totalNum);
        numInfo.setNormalRate(normalNum, totalNum);
        numInfo.setOnlineNum(onlineNum);
        numInfo.setNormalNum(normalNum);
        return numInfo;
    }

    @Override
    public List<ChartElectricityInfoVO> getElectricityBySixMonth(RequireParamsDTO userInfo) {
        List<ChartElectricityInfoVO> electricityBySixMonth = statisticsMapper.getElectricityBySixMonth(userInfo);
        electricityBySixMonth.sort(Comparator.comparing(ChartElectricityInfoVO::getCollectDate));
        return electricityBySixMonth;
    }

    @Override
    public List<ChartElectricityInfoVO> getEveryHourElectricityInfo(RequireParamsDTO userInfo) {
        // 获取当日日期
        String date = DateUtil.today();
        String tableSuffix = DateUtil.today().split("-")[0];
        List<ChartElectricityInfoVO> everyHourElectricityInfo = statisticsMapper.getEveryHourElectricityInfo(tableSuffix, userInfo,date);
        return everyHourElectricityInfo;
    }

    @Override
    public PageInfo<ElectricityStaticsInfoVO> getElectricityStatisticsInfo(ElectricityStatisticsQueryDTO query, RequireParamsDTO userInfo) {
        IPage<PlantStatisticsInfoVO> page = new Page<>(query.getCurrentPage(), query.getPageSize());
        PlantStatisticsQueryDTO queryInfo = new PlantStatisticsQueryDTO();
        queryInfo.setPlantName(query.getAddress());
        Page<PlantStatisticsInfoVO> plantInfo = statisticsMapper.selectPlantUidList(queryInfo, page, userInfo, null);
        List<String> plantUidArray = plantInfo.getRecords().stream().map(PlantStatisticsInfoVO::getPlantUid).collect(Collectors.toList());
        List<ElectricityStaticsInfoVO> result;
        if (CollUtil.isEmpty(plantUidArray)) {
            return new PageInfo<>();
        }
        if (query.getDate().length() == Constant.DAY_LENGTH) {
            String tableName = TableUtils.getTableName(Constant.TABLE_PREFIX, query.getDate());
            result = statisticsMapper.getElectricityStatisticsInfoByDay(query, tableName, plantUidArray);
            handleResult(result, Constant.DAY_LENGTH);
        } else if (query.getDate().length() == Constant.MONTH_LENGTH) {
            result = statisticsMapper.getElectricityStatisticsInfoByMonth(query, plantUidArray);
            // 计算同比环比
            query.setDate(DateUtils.getMonthOffset(query.getDate(), -1));
            // 查询上个月数据
            List<ElectricityStaticsInfoVO> lastMonthResult = statisticsMapper.getElectricityStatisticsInfoByMonth(query, plantUidArray);
            if (CollUtil.isNotEmpty(lastMonthResult)) {
                // 调用计算同比方法
                result = CalculateYoyRatio(lastMonthResult, result, DAY_START_INDEX, DAY_END_INDEX);
                // 调用计算环比方法
                result = CalculateMomRatio(result, lastMonthResult, DAY_START_INDEX, DAY_END_INDEX);
            }
            handleResult(result, Constant.MONTH_LENGTH);
        } else if (query.getDate().length() == Constant.YEAR_LENGTH) {
            result = statisticsMapper.getElectricityStatisticsInfoByYear(query, plantUidArray);
            // 计算同比环比
            query.setDate(DateUtils.getYearOffset(query.getDate(), -1));
            List<ElectricityStaticsInfoVO> lastYearResult = statisticsMapper.getElectricityStatisticsInfoByYear(query, plantUidArray);
            if (CollUtil.isNotEmpty(lastYearResult)) {
                // 调用计算同比方法
                result = CalculateYoyRatio(lastYearResult, result, MONTH_START_INDEX, MONTH_END_INDEX);
                // 调用计算环比方法
                result = CalculateMomRatio(result, lastYearResult, MONTH_START_INDEX, MONTH_END_INDEX);
            }
            handleResult(result, Constant.YEAR_LENGTH);
        } else {
            throw new BusinessException(ResultEnum.DATETIME_FORMAT_FAILED.getCode(), ResultEnum.DATETIME_FORMAT_FAILED.getMessage());
        }
        PageInfo<ElectricityStaticsInfoVO> pageInfo = new PageInfo<>(result);
        pageInfo.setTotal(page.getTotal());
        return pageInfo;
    }

    /**
     * 处理能效收益统计数据（）
     *
     * @param result
     */
    private void handleResult(List<ElectricityStaticsInfoVO> result, Integer dateLength) {
        result.forEach(electricityStaticsInfo -> {
            List<ElectricityInfoVO> electricityList = electricityStaticsInfo.getElectricityList();
            Iterator<ElectricityInfoVO> iterator = electricityList.iterator();

            BigDecimal totalIncome = BigDecimal.ZERO;
            BigDecimal totalElectricity = BigDecimal.ZERO;
            BigDecimal totalEfficiencyPerHours = BigDecimal.ZERO;

            BigDecimal count = BigDecimal.ZERO;

            while (iterator.hasNext()) {
                count = count.add(BigDecimal.ONE);
                ElectricityInfoVO electricityInfo = iterator.next();

                // 判断是否是最后一个元素
                boolean isLast = !iterator.hasNext();

                // 处理数据
                electricityInfo.setEfficiencyPerHours(BusinessCalculateUtil.getEfficiencyPerHours(electricityInfo.getElectricity(), electricityInfo.getPlantCapacity()));
                electricityInfo.setIncome(BusinessCalculateUtil.getIncome(electricityInfo.getElectricity(), electricityInfo.getPlantPrice()));
                electricityInfo.setElectricity(BusinessCalculateUtil.getRealElectricity(electricityInfo.getElectricity()));
                electricityInfo.setPlantCapacity(BusinessCalculateUtil.getRealPlantCapacity(electricityInfo.getPlantCapacity()));

                // 总收入
                totalIncome = totalIncome.add(new BigDecimal(electricityInfo.getIncome()));
                // 总发电量
                totalElectricity = totalElectricity.add(new BigDecimal(electricityInfo.getElectricity()));
                // 总等效小时
                totalEfficiencyPerHours = totalEfficiencyPerHours.add(new BigDecimal(electricityInfo.getEfficiencyPerHours()));

                // 如果是最后一个元素，统计与平均值
                if (isLast && !dateLength.equals(DAY_LENGTH)) {
                    electricityStaticsInfo.setTotalIncome(totalIncome.toString());
                    electricityStaticsInfo.setTotalElectricity(totalElectricity.toString());
                    electricityStaticsInfo.setTotalEfficiencyPerHours(totalEfficiencyPerHours.toString());

                    electricityStaticsInfo.setAvgIncome(totalIncome.divide(count, 2, RoundingMode.HALF_UP).toString());
                    electricityStaticsInfo.setAvgElectricity(totalElectricity.divide(count, 2, RoundingMode.HALF_UP).toString());
                    electricityStaticsInfo.setAvgEfficiencyPerHours(totalEfficiencyPerHours.divide(count, 2, RoundingMode.HALF_UP).toString());
                }
            }
        });
    }

    /**
     * 同比计算方法
     *
     * @param lastMonthResult
     * @param result
     */
    private List<ElectricityStaticsInfoVO> CalculateYoyRatio(List<ElectricityStaticsInfoVO> lastMonthResult,
                                                             List<ElectricityStaticsInfoVO> result,
                                                             Integer startIndex,
                                                             Integer endIndex) {
        // 判断本月数据记录数是否大于上月数据记录数
        for (int i = 0, j = 0; i < lastMonthResult.size(); i++) {
            String lastPlantName = lastMonthResult.get(i).getPlantName();
            boolean flag = true;
            while (!result.get(j).getPlantName().equals(lastPlantName) && flag) {
                if (j < result.size() - 1) {
                    j++;
                } else {
                    flag = false;
                }
            }
            // 当前电站上月份数据
            List<ElectricityInfoVO> lastDataList = lastMonthResult.get(i).getElectricityList();
            // 当前电站本月份数据
            List<ElectricityInfoVO> curDataList = result.get(j).getElectricityList();
            // 计算同比并赋值给当前电站的月数据
            for (int k = 0, h = 0; k < lastDataList.size(); k++) {
                String lastDataTime = lastDataList.get(k).getDataTime().substring(startIndex, endIndex);
                boolean dateFlag = true;
                while (!lastDataTime.equals(curDataList.get(h).getDataTime().substring(startIndex, endIndex)) && dateFlag) {
                    if (h < curDataList.size() - 1) {
                        h++;
                    } else {
                        dateFlag = false;
                    }
                }
                BigDecimal b1 = new BigDecimal(curDataList.get(h).getElectricity()).subtract(new BigDecimal(lastDataList.get(k).getElectricity())).multiply(new BigDecimal(100));
                BigDecimal b2 = new BigDecimal(lastDataList.get(k).getElectricity());
                if (b2.intValue() > 0) {
                    BigDecimal yoyRatio = b1.divide(b2, 2, RoundingMode.HALF_UP);
                    curDataList.get(h).setYoyRatio(yoyRatio.toString() + "%");
                }
                if (h < curDataList.size() - 1) {
                    h++;
                }
            }
            result.get(j).setElectricityList(curDataList);
            j++;
        }
        return result;
    }

    /**
     * 环比计算方法
     *
     * @param currentResult
     */
    private List<ElectricityStaticsInfoVO> CalculateMomRatio(List<ElectricityStaticsInfoVO> currentResult,
                                                             List<ElectricityStaticsInfoVO> lastResult,
                                                             Integer startIndex,
                                                             Integer endIndex) {
        for (int i = 0, j = 0; i < currentResult.size(); i++) {
            List<ElectricityInfoVO> curElectricityList = currentResult.get(i).getElectricityList();
            String firstDay = currentResult.get(i).getElectricityList().get(0).getDataTime().substring(startIndex, endIndex);
            // 每月的一号需要单独计算（与上个月份最后一天进行对比）
            if ("01".equals(firstDay)) {
                boolean flag = true;
                // 如果第一条数据为1号，则取上个月对应电站的最后一条数据进行比对
                while (!currentResult.get(i).getPlantUid().equals(lastResult.get(j).getPlantUid()) && flag) {
                    if (j < lastResult.size() - 1) {
                        j++;
                    } else {
                        flag = false;
                    }
                }
                List<ElectricityInfoVO> lastElectricityList = lastResult.get(j).getElectricityList();
                if (lastElectricityList.size() > 0) {
                    // 取上月份最后一条数据
                    String endDayElectricity = lastElectricityList.get(lastElectricityList.size() - 1).getElectricity();
                    // 取出本月份第一条数据
                    String firstDayElectricity = curElectricityList.get(0).getElectricity();
                    // 求出环比
                    BigDecimal b1 = new BigDecimal(firstDayElectricity).subtract(new BigDecimal(endDayElectricity)).multiply(new BigDecimal(100));
                    BigDecimal b2 = new BigDecimal(endDayElectricity);
                    if (b2.intValue() > 0) {
                        BigDecimal momRatio = b1.divide(b2, 2, BigDecimal.ROUND_HALF_UP);
                        curElectricityList.get(0).setMomRatio(momRatio.toString() + "%");
                    }
                }
            }
            if (curElectricityList.size() <= 1) {
                currentResult.get(i).setElectricityList(curElectricityList);
                continue;
            }
            // 其余情况皆为当月数据之间的比对
            for (int k = 1; k < curElectricityList.size(); k++) {
                BigDecimal b1 = new BigDecimal(curElectricityList.get(k).getElectricity()).subtract(new BigDecimal(curElectricityList.get(k - 1).getElectricity())).multiply(new BigDecimal(100));
                BigDecimal b2 = new BigDecimal(curElectricityList.get(k - 1).getElectricity());
                if (b2.intValue() > 0) {
                    BigDecimal momRatio = b1.divide(b2, 2, BigDecimal.ROUND_HALF_UP);
                    curElectricityList.get(k).setMomRatio(momRatio.toString() + "%");
                }
            }
        }
        return currentResult;
    }

    @Override
    public Page<PlantStatisticsInfoVO> getPlantStatisticsInfo(PlantStatisticsQueryDTO query, RequireParamsDTO userInfo) {
        Page<PlantStatisticsInfoVO> page = new Page<>(query.getCurrentPage(), query.getPageSize());
        String dateType = "";
        if (query.getPowerStartTime() != null && query.getPowerEndTime() != null) {
            int startlength = query.getPowerStartTime().length();
            int endlength = query.getPowerEndTime().length();
            if (startlength == 0 && endlength == 0) {
                dateType = "all";
            } else if (startlength == 10 && endlength == 10) {
                dateType = "day";
            } else if (startlength == 7 && endlength == 7) {
                dateType = "month";
            } else if (startlength == 4 && endlength == 4) {
                dateType = "year";
            } else {
                throw new BusinessException(ResultEnum.DATETIME_FORMAT_FAILED.getCode(),
                        ResultEnum.DATETIME_FORMAT_FAILED.getMessage());
            }
        }
        // 条件查询电站Uid
        IPage<PlantStatisticsInfoVO> plantPage = statisticsMapper.selectPlantUidList(query, page, userInfo, dateType);
        List<String> plantUidArray = plantPage.getRecords().stream().map(PlantStatisticsInfoVO::getPlantUid).collect(Collectors.toList());
        if (plantUidArray.size() <= 0) {
            return page;
        }
        // 通过电站Uid数组查询电站统计数据
        List<PlantStatisticsInfoVO> plantBaseInfoList = statisticsMapper.getPlantBaseInfo(query, plantUidArray, dateType);
        // 通过电站Uid数组查询电站告警数量
        List<PlantStatisticsInfoVO> PlantAlarmNumList = statisticsMapper.getPlantAlarmNum(query, plantUidArray, dateType);
        List<PlantInfoVO> plantStatusInfo = statisticsMapper.getPlantStatusInfo(plantUidArray);
        for (PlantStatisticsInfoVO plantStatisticsInfoVO : plantBaseInfoList) {
            for (PlantInfoVO plantInfoVO : plantStatusInfo) {
                if (plantStatisticsInfoVO.getPlantUid().equals(plantInfoVO.getPlantUid())) {
                    plantStatisticsInfoVO.setPlantType(plantInfoVO.getPlantType());
                    plantStatisticsInfoVO.setStatus(plantInfoVO.getPlantStatus());
                    plantStatisticsInfoVO.setPowerDistributor(plantInfoVO.getPowerDistributor());
                    plantStatisticsInfoVO.setProjectName(plantInfoVO.getProjectName());
                }
            }
        }
        for (int i = 0, j = 0; i < PlantAlarmNumList.size() && j < plantBaseInfoList.size() - 1; i++) {
            if (PlantAlarmNumList.get(i).equals(plantBaseInfoList.get(j).getPlantName())) {
                plantBaseInfoList.get(j).setAlarmNum(PlantAlarmNumList.get(i).getAlarmNum());
            } else {
                plantBaseInfoList.get(j).setAlarmNum("0");
                i--;
            }
            j++;
        }
        setPlantStatics(plantBaseInfoList);
        Page<PlantStatisticsInfoVO> result = page.setRecords(plantBaseInfoList);
        return result;
    }

    private static void setPlantStatics(List<PlantStatisticsInfoVO> plantBaseInfoList) {
        for (int i = 0; i < plantBaseInfoList.size(); i++) {
            BigDecimal electricity = new BigDecimal(plantBaseInfoList.get(i).getElectricity());
            BigDecimal plantCapacity = new BigDecimal(plantBaseInfoList.get(i).getPlantCapacity());
            BigDecimal electricityPrice = new BigDecimal(plantBaseInfoList.get(i).getElectricityPrice());
            if ("0".equals(plantBaseInfoList.get(i).getStatus())) {
                Date updateTime = DateUtil.parseDate(plantBaseInfoList.get(i).getUpdateTime());
                long betweenMS = DateUtil.between(updateTime, DateUtil.date(), DateUnit.MS);
                plantBaseInfoList.get(i).setOfflineTime(DateUtil.formatBetween(betweenMS, BetweenFormatter.Level.MINUTE));
            }
            // 等效小时=(electricity/100)/(plant_capacity/1000)
            BigDecimal equivalentUseHour = electricity.divide(plantCapacity, BigDecimal.ROUND_HALF_UP).setScale(2, BigDecimal.ROUND_HALF_UP);
            plantBaseInfoList.get(i).setEquivalentUseHour(equivalentUseHour.toString());
            // 收益=electricity/100*sale_price
            BigDecimal income = electricity.multiply(electricityPrice).setScale(2, BigDecimal.ROUND_HALF_UP);
            plantBaseInfoList.get(i).setIncome(income.toString());
        }
    }

    @Override
    public PageInfo<ElectricityStaticsInfoVO> exportExcelElectricityStatisticsInfo(ElectricityStatisticsExportFileDTO query, RequireParamsDTO userInfo) {
        if(!StringUtils.isNotBlank(query.getSheetName()) && !StringUtils.isNotEmpty(query.getSheetName())){
            query.setSheetName("能效收益统计");
        }
        if((!StringUtils.isNotBlank(query.getCurrentPage()) && !StringUtils.isNotEmpty(query.getCurrentPage()))&&(!StringUtils.isNotBlank(query.getPageSize()) && !StringUtils.isNotEmpty(query.getPageSize()))){
            query.setPageSize("1048576");
            query.setCurrentPage("1");
        }
        ElectricityStatisticsQueryDTO electricityStatisticsQueryDTO = electricityStatisticsExportFileVOMapper.electricityStatisticsExportFileVO2ElectricityStatisticsQueryVO(query);
        return  getElectricityStatisticsInfo(electricityStatisticsQueryDTO, userInfo);
    }

    @Override
    public Page<PlantStatisticsInfoVO> exportExcelPlantStatistics(RequireParamsDTO userInfo, PlantStatisticsExportFileDTO query) {
        if (!StringUtils.isNotBlank(query.getSheetName()) && !StringUtils.isNotEmpty(query.getSheetName())) {
            query.setSheetName("电站统计");
        }
        if((!StringUtils.isNotBlank(query.getCurrentPage()) && !StringUtils.isNotEmpty(query.getCurrentPage()))&&(!StringUtils.isNotBlank(query.getPageSize()) && !StringUtils.isNotEmpty(query.getPageSize()))){
            query.setPageSize("1048576");
            query.setCurrentPage("1");
        }
        PlantStatisticsQueryDTO plantStatisticsQueryDTO = plantStatisticsExportFileVOMapper.plantStatisticsExportFileVO2PlantStatisticsQueryVO(query);
        return getPlantStatisticsInfo(plantStatisticsQueryDTO, userInfo);
    }

    @Override
    public List<IntegrativeStatisticChartVO> integrativeStatisticChart(RequireParamsDTO userInfo, IntegrativeStatisticQueryDTO query) {
        if (query.getProjectId() != null && !"".equals(query.getProjectId())) {
            List<String> projectList = projectService.getProjectIDListByPid(query.getProjectId());
            userInfo.setProjectList(projectList);
        }
        //判断传入时间类型是日期类型
        Boolean isMonth = false;
        if (query.getDataStartTime().length() == Constant.MONTH_LENGTH) {
            isMonth = true;
        }
        query.setDataStartTime(DateUtils.getBeginOfMonth(query.getDataStartTime()));
        query.setDataEndTime(DateUtils.getEndOfMonth(query.getDataEndTime()));
        List<IntegrativeStatisticChartVO> chart = statisticsMapper.getIntegrativeStatisticChart(userInfo, query, isMonth);
        chart.forEach(chartInfo -> {
            BigDecimal electricity = new BigDecimal(chartInfo.getElectricity());
            BigDecimal plantCapacity = new BigDecimal(chartInfo.getPlantCapacity());
            chartInfo.setIncome(electricity.multiply(new BigDecimal(query.getElectricityPrice())).setScale(2, RoundingMode.HALF_UP).toString());
            if (plantCapacity.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal dailyEfficiencyPerHour = electricity.divide(plantCapacity, 2, BigDecimal.ROUND_HALF_UP);
                chartInfo.setDailyEfficiencyPerHour(dailyEfficiencyPerHour.toString());
            } else {
                chartInfo.setDailyEfficiencyPerHour(BigDecimal.ZERO.toString());
            }
        });
        return chart;
    }

    @Override
    public IntegrativeStatisticSheetVO integrativeStatisticSheet(RequireParamsDTO userInfo, IntegrativeStatisticQueryDTO query) throws Exception {
        if (query.getProjectId() != null && !"".equals(query.getProjectId())) {
            List<String> projectList = projectService.getProjectIDListByPid(query.getProjectId());
            userInfo.setProjectList(projectList);
        }
        //查询电站基础信息统计数据
        IntegrativeStatisticSheetVO integrativeStatisticSheet = statisticsMapper.getIntegrativeStatisticSheet(userInfo, query);
        String periodElectricity = statisticsMapper.getPeriodElectricity(userInfo, query);
        integrativeStatisticSheet.setPeriodElectricity(periodElectricity);
        List<HashMap<String, String>> deviceNumInfo = statisticsMapper.getDeviceNumInfo(userInfo, query);
        //计算电站基础信息统计数据
        String electricity = integrativeStatisticSheet.getElectricity();
        String electricityPrice = query.getElectricityPrice();
        integrativeStatisticSheet.setIncome(BusinessCalculateUtil.getIncome(electricity, electricityPrice));
        integrativeStatisticSheet.setElectricity(BusinessCalculateUtil.getRealElectricity(electricity));
        integrativeStatisticSheet.setPlantCapacity(BusinessCalculateUtil.getRealPlantCapacity(integrativeStatisticSheet.getPlantCapacity()));
        integrativeStatisticSheet.setTreeNum(BusinessCalculateUtil.getTreeNum(electricity));
        integrativeStatisticSheet.setEfficiencyPerHours( String.format("%.2f", Double.parseDouble(periodElectricity) / 100 / Double.parseDouble(integrativeStatisticSheet.getPlantCapacity())));
        integrativeStatisticSheet.setReduceCo2(BusinessCalculateUtil.getReduceCo2(electricity));
        integrativeStatisticSheet.setReduceCoal(BusinessCalculateUtil.getReduceCoal(electricity));
        //通过反射动态赋值
        for (HashMap<String, String> map : deviceNumInfo) {
            //利用枚举寻找字段值
            String status = DeviceStatusField.getNameByCode(String.valueOf(map.get("plantStatus")));
            String statusNum = String.valueOf(map.get("statusNum"));
            ReflectUtil.setFieldValueByName(integrativeStatisticSheet, status, statusNum);
        }
        return integrativeStatisticSheet;
    }

    @Override
    public List<ChartElectricityInfoVO> getElectricityByNumDay(RequireParamsDTO userInfo, Integer dayNums) {
        List<ChartElectricityInfoVO> electricityByNumDay = statisticsMapper.getElectricityByNumDay(userInfo, dayNums);
        electricityByNumDay.sort(Comparator.comparing(ChartElectricityInfoVO::getCollectDate));
        return electricityByNumDay;
    }


    @Override
    public NumInfoDTO getDeviceNumInfo(RequireParamsDTO userInfo ) throws NoSuchFieldException, IllegalAccessException {
        //查询设备各状态的数量
        List<NumInfoVO> deviceStatusNumList = statisticsMapper.getDeviceStatusNumInfo(userInfo);
        NumInfoDTO numInfo = new NumInfoDTO();
        for (NumInfoVO infoDTO : deviceStatusNumList) {
            String fieldName = DeviceStatus.getFieldNameByCode(infoDTO.getStatus());
            Field field = numInfo.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(numInfo, infoDTO.getStatusNum());
        }
        String totalNum = BusinessCalculateUtil.getTotalNum(numInfo);
        String onlineNum = BusinessCalculateUtil.getInverterOnlineNum(numInfo);
        String normalNum = BusinessCalculateUtil.getRealNormalNum(numInfo);
        String offlineNum = BusinessCalculateUtil.getRealOfflineNum(numInfo);
        numInfo.setTotalNum(totalNum);
        numInfo.setOnlineRate(onlineNum, totalNum);
        numInfo.setNormalRate(normalNum, totalNum);
        numInfo.setOnlineNum(onlineNum);
        numInfo.setNormalNum(normalNum);
        numInfo.setOfflineNum(offlineNum);
        return numInfo;
    }

}
