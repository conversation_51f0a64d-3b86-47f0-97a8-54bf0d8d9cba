package com.bto.system.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.pojo.vo.RoleInfoVO;
import com.bto.commons.pojo.entity.Role;
import com.bto.commons.pojo.dto.RoleInfoDTO;
import com.bto.commons.pojo.dto.RoleQueryDTO;
import com.bto.commons.utils.ListPageUtils;
import com.bto.commons.utils.PropertyUtils;
import com.bto.system.dao.RoleMapper;
import com.bto.system.service.RoleMenuService;
import com.bto.system.service.RoleService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bto.commons.utils.IDUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/18 17:30
 */
@Service
public class RoleServiceImpl implements RoleService {
    @Autowired
    private RoleMapper roleMapper;
    @Autowired
    private RoleMenuService roleMenuService;

    @Override
    public Page<RoleInfoVO> getRoleList(RoleQueryDTO query) {
        Page<RoleInfoVO> page = new Page<>(query.getCurrentPage(), query.getPageSize());
        List<RoleInfoVO> roleList = roleMapper.getRoleList(query);
        // TODO: 2023/5/25  由于page自动分页与返回结构有冲突，
        //  导致数据条目不正确，因此暂时采用手动分页的方法,后面需要优化这个问题
        List<RoleInfoVO> result = ListPageUtils.pageBySubTList(roleList, query.getCurrentPage(), query.getPageSize());
        page.setTotal(roleList.size());
        page.setRecords(result);
        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer addRole(RoleInfoDTO roleInfoDTO) {
        Role role = new Role();
        // 生成10位数的Long类型ID
        Long roleId = IDUtils.generateShortId();
        role.setRoleID(roleId);
        role.setCreateTime(DateUtil.date());
        BeanUtils.copyProperties(roleInfoDTO, role, PropertyUtils.getNullPropertyNames(roleInfoDTO));
        // 新增角色
        Integer count = roleMapper.insert(role);
        // 保存角色菜单关系
        Integer result = roleMenuService.saveOrUpdate(roleId, roleInfoDTO.getMenuList());
        return count + result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer editRole(RoleInfoDTO roleInfoDTO) {
        Role role = roleMapper.selectById(roleInfoDTO.getRoleID());
        BeanUtils.copyProperties(roleInfoDTO, role, PropertyUtils.getNullPropertyNames(roleInfoDTO));
        // 修改角色信息
        Integer roleCount = roleMapper.updateById(role);
        // 修改角色菜单关系
        Integer roleMenuCount = roleMenuService.saveOrUpdate(roleInfoDTO.getRoleID(), roleInfoDTO.getMenuList());
        return roleCount + roleMenuCount;
    }

    @Override
    public Integer deleteRole(String roleID) {
        // 删除角色信息
        Integer roleCount = roleMapper.deleteById(roleID);
        // 删除角色菜单关系
        Integer roleMenuCount = roleMenuService.delete(Long.valueOf(roleID));
        return roleCount + roleMenuCount;
    }

    @Override
    public Map<String, Long> getRoleMap(RoleQueryDTO query) {
        List<Role> roles = roleMapper.selectList(Wrappers.emptyWrapper());
        return roles.stream()
                .collect(Collectors.toMap(Role::getRoleName, Role::getRoleID));
    }
}
