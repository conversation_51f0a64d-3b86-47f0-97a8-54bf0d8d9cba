package com.bto.system.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.ProjectInfoDTO;
import com.bto.commons.pojo.entity.Project;
import com.bto.commons.pojo.vo.ProjectInfoVO;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.utils.PropertyUtils;
import com.bto.commons.utils.TreeUtils;
import com.bto.system.dao.ProjectMapper;
import com.bto.system.service.ProjectService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/18 14:52
 */
@Service
public class ProjectServiceImpl implements ProjectService {
    @Autowired
    private ProjectMapper projectMapper;

    @Override
    public List<ProjectInfoVO> getProjectSpecialInfo(RequireParamsDTO userInfo) {
        List<ProjectInfoVO> projectInfoList = projectMapper.getProjectSpecialInfoList(userInfo);
        List<ProjectInfoVO> result = TreeUtils.build(projectInfoList);
        return result;
    }

    @Override
    public Integer addProject(ProjectInfoDTO projectInfo) {
        // 查询所有父级ID为指定父级ID的项目并排序，取出id最大的一条
        Project project = projectMapper.selectProject(projectInfo.getPid());
        if (project != null) {
            project.setId(project.getId() + 1);
        } else {
            project = new Project();
            project.setId(Integer.parseInt(projectInfo.getPid()) * 10);
        }
        project.setCreateTime(DateUtil.date());
        project.setPid(Integer.parseInt(projectInfo.getPid()));
        BeanUtils.copyProperties(projectInfo, project, PropertyUtils.getNullPropertyNames(projectInfo));
        return projectMapper.insert(project);
    }

    @Override
    public Integer editProject(ProjectInfoDTO projectInfo) {
        Project project = projectMapper.selectById(projectInfo.getId());
        project.setProjectName(projectInfo.getProjectName());
        int count = projectMapper.updateById(project);
        return count;
    }

    @Override
    public Integer deleteProject(Integer projectID) {
        // 判断用户是否存在
        Project project = projectMapper.selectById(projectID);
        if (project != null) {
            // 更新用户
            int count = projectMapper.deleteById(projectID);
            return count;
        } else {
            throw new BusinessException(
                    ResultEnum.USER_NON_EXISTENT.getCode(),
                    ResultEnum.USER_NON_EXISTENT.getMessage()
            );
        }
    }

    @Override
    public List<String> getProjectIDListByPid(String projectID) {
        List<ProjectInfoVO> projectInfoList = projectMapper.getProjectIDListByPid(projectID);
        List<ProjectInfoVO> plantTree = TreeUtils.build(projectInfoList);
        List<ProjectInfoVO> allLeafNode = TreeUtils.getAllChildNode(plantTree);

        List<String> result = allLeafNode.stream().map(ProjectInfoVO::getId).collect(Collectors.toList());
        return result;
    }

    @Override
    public Map<String, Integer> getProjectMap() {
        List<Project> projects = projectMapper.selectList(Wrappers.emptyWrapper());

        return projects.stream().collect(Collectors.toMap(Project::getProjectName, Project::getId));
    }

    @Override
    public List<ProjectInfoVO> getProjectSpecialByPid(String projectId) {

        List<ProjectInfoVO> projectInfoList = projectMapper.getProjectIDListByPid(projectId);
        // List<ProjectInfoVO> collect = projectInfoList.stream()
        //         .filter(projectInfoVO ->
        //                 String.valueOf(ProjectTypeEnum.HOUSEHOLD.getProjectID()).equals(projectInfoVO.getPid()) ||
        //                         String.valueOf(ProjectTypeEnum.BTO_COMPANY.getProjectID()).equals(projectInfoVO.getPid()))
        //         .collect(Collectors.toList());
        // projectInfoList.removeAll(collect);
        return TreeUtils.build(projectInfoList);
    }
}
