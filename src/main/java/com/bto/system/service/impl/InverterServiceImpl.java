package com.bto.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bto.commons.constant.Constant;
import com.bto.commons.enums.InverterStatusEnum;
import com.bto.commons.pojo.dto.InverterListQueryDTO;
import com.bto.commons.pojo.entity.Device;
import com.bto.commons.pojo.entity.InverterAlarm;
import com.bto.commons.pojo.vo.*;
import com.bto.commons.utils.BusinessCalculateUtil;
import com.bto.commons.utils.TableUtils;
import com.bto.system.dao.DeviceMapper;
import com.bto.system.dao.InverterAlarmMapper;
import com.bto.system.dao.InverterLatestMapper;
import com.bto.system.dao.InverterMapper;
import com.bto.system.service.InverterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.bto.commons.enums.BusinessEnum.*;
import static com.bto.commons.constant.Constant.TABLE_PREFIX;

/**
 * <AUTHOR>
 * @date 2023/4/8 15:51
 */
@Slf4j
@Service("inverterService")
public class InverterServiceImpl extends ServiceImpl<InverterAlarmMapper, InverterAlarm> implements InverterService {
    @Autowired
    private DeviceMapper deviceMapper;
    @Autowired
    private InverterMapper inverterMapper;

    @Autowired
    private InverterLatestMapper inverterLatestMapper;

    @Override
    public <T> List<T> getInverterChartInfo(String date, String plantUid) {
        LambdaQueryWrapper<Device> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Device::getPlantUid, plantUid);
        queryWrapper.eq(Device::getDeviceType, 1);
        List<Device> devices = deviceMapper.selectList(queryWrapper);
        if (date.length() == Constant.DAY_LENGTH) {
            List<InverterChartByDayVO> results = new ArrayList<>();
            Double totalElectricity = 0.00;
            for (Device device : devices) {
                String inverterSN = device.getDeviceId();
                DeviceVO deviceVO = new DeviceVO();
                BeanUtil.copyProperties(device, deviceVO);
                String tableName = TableUtils.getTableName(TABLE_PREFIX, date);
                List<InverterRealTimeInfoVO> powerList = inverterMapper.getInverterDataByDay(tableName, inverterSN);
                if (powerList.size() > 0) {
                    results.add(new InverterChartByDayVO(deviceVO, powerList, powerList.get(0).getTodayElectricity()));
                    totalElectricity += Double.valueOf(powerList.get(0).getTodayElectricity());
                } else {
                    results.add(new InverterChartByDayVO(deviceVO, powerList, "0"));
                    totalElectricity += 0;
                }
            }
            String tableName = TableUtils.getTableName(TABLE_PREFIX, date);
            List<PlantPowerVO> totalPowerList = inverterMapper.getTotalPowerList(tableName, plantUid);
            DeviceVO totalDevice = new DeviceVO();
            totalDevice.setPlantUid(plantUid);
            totalDevice.setDeviceId("total");
            results.add(new InverterChartByDayVO(totalDevice, totalPowerList, BigDecimal.valueOf(totalElectricity).setScale(2, RoundingMode.HALF_UP).toString()));
            return (List<T>) results;
        } else {
            List<InverterChartByOtherVO> results = new ArrayList<>();
            ArrayList<DeviceVO> deviceInfos = new ArrayList<>();
            devices.forEach(device -> {
                DeviceVO deviceVO = new DeviceVO();
                BeanUtils.copyProperties(device, deviceVO);
                deviceInfos.add(deviceVO);
            });
            List<ChartElectricityInfoVO> electricityList = inverterMapper.getElectricityList(date.trim(), plantUid);
            double sum = electricityList.stream().mapToDouble(num -> Double.parseDouble(num.getElectricity())).sum();
            results.add(new InverterChartByOtherVO(deviceInfos, electricityList, BigDecimal.valueOf(sum).setScale(2, RoundingMode.HALF_UP).toString()));
            return (List<T>) results;
        }
    }

    @Override
    public HashMap<String, Integer> getPvNums(String inverterSN) {
        Integer pvNum = inverterLatestMapper.selectPvNum(inverterSN);
        HashMap<String, Integer> result = new HashMap<>();
        if (pvNum != null) {
            result.put("pvNum", pvNum);
        }
        return result;
    }

    @Override
    public List<LinkedHashMap<String, Object>> getInverterInfoChart(String date, String inverterSN) {
        // 查询PV路数
        Integer pvNum = inverterLatestMapper.selectPvNum(inverterSN);

        if (ObjUtil.isNull(pvNum)) {
            return Collections.emptyList();
        }

        List<String> pvModuleFields = inverterMapper.getPvModuleFields(inverterSN);
        // 查询日期所在表名
        String tableName = TableUtils.getTableName(TABLE_PREFIX, date);
        List<LinkedHashMap<String, Object>> inverterInfoChart = inverterMapper.getInverterInfoChart(pvModuleFields, inverterSN, tableName);
        for (int i = 0; i < inverterInfoChart.size(); i++) {
            LinkedHashMap<String, Object> hashMap = inverterInfoChart.get(i);
            for (int j = 1; j <= pvNum; j++) {
                String key = "pv" + j + "_power";
                BigDecimal bigDecimal = new BigDecimal(hashMap.get(key).toString());
                BigDecimal value = bigDecimal.multiply(BigDecimal.valueOf(100)).setScale(0, RoundingMode.HALF_UP);
                hashMap.put(key, value.toString());
            }
            String todayElectricity = BusinessCalculateUtil.getRealElectricity(hashMap.get(TODAYELECTRICITY.getColumn()).toString());
            ;
            String monthElectricity = BusinessCalculateUtil.getRealElectricity(hashMap.get(MONTHELECTRICITY.getColumn()).toString());
            ;
            String yearElectricity = BusinessCalculateUtil.getRealElectricity(hashMap.get(YEARELECTRICITY.getColumn()).toString());
            ;
            String totalElectricity = BusinessCalculateUtil.getRealElectricity(hashMap.get(TOTALELECTRICITY.getColumn()).toString());
            ;
            hashMap.put("todayElectricity", todayElectricity);
            hashMap.put("monthElectricity", monthElectricity);
            hashMap.put("yearElectricity", yearElectricity);
            hashMap.put("totalElectricity", totalElectricity);
            DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime dateTIme = (LocalDateTime) hashMap.get(INITTIME.getColumn());
            String initTime = DateUtil.parse(dateTIme.format(format)).toString();
            hashMap.put("initTime", initTime);
            inverterInfoChart.set(i, hashMap);
        }
        return inverterInfoChart;
    }

    @Override
    public InverterDetailsVO getInverterDetails(String inverterSN) {
        InverterDetailsVO inverterDetails = inverterMapper.getInverterDetails(inverterSN);

        if (ObjUtil.isNull(inverterDetails)) {
            return inverterDetails;
        }

        String module = inverterDetails.getModule();
        String[] split = module.split("-", 2);
        if (split.length > 1) {
            String result = split[1];
            Integer ratedCapacity = inverterMapper.getInverterRatedCapacityByModel(result);
            inverterDetails.setDeviceCapacity(ratedCapacity != null ? ratedCapacity : 0);
        }

        Integer alarmCount = getInverterAlarmCount(inverterSN);
        inverterDetails.setAlarmCount(alarmCount);

        inverterDetails.setInverterStatus(InverterStatusEnum.getNameByCode(inverterDetails.getInverterStatus()));
        inverterDetails.setTodayElectricity(BusinessCalculateUtil.getRealElectricity(inverterDetails.getTodayElectricity()));
        inverterDetails.setMonthElectricity(BusinessCalculateUtil.getRealElectricity(inverterDetails.getMonthElectricity()));
        inverterDetails.setYearElectricity(BusinessCalculateUtil.getRealElectricity(inverterDetails.getYearElectricity()));
        inverterDetails.setTotalElectricity(BusinessCalculateUtil.getRealElectricity(inverterDetails.getTotalElectricity()));
        return inverterDetails;
    }


    @Override
    public Page<InverterInfoVO> InverterInfoList(RequireParamsDTO userInfo, InverterListQueryDTO query) {
        Page<InverterInfoVO> page = new Page<>(query.getCurrentPage(), query.getPageSize());
        query.setOrder(StrUtil.isEmpty(query.getOrder()) ? "createTime" : query.getOrder());
        return inverterMapper.selectInverterInfoList(page, userInfo, query);
    }


    @Override
    public List<String> getInverterSn(String plantUid) {
        QueryWrapper<Device> InverterQueryWrapper = new QueryWrapper<>();
        InverterQueryWrapper.eq("plant_uid", plantUid);
        InverterQueryWrapper.eq("device_type", "1");
        InverterQueryWrapper.eq("is_deleted", "0");
        InverterQueryWrapper.select("device_id");
        List<Device> inverterListResult = deviceMapper.selectList(InverterQueryWrapper);
        List<String> returnInverterSnList = new ArrayList<>();
        if (inverterListResult == null || inverterListResult.size() <= 0) {
            returnInverterSnList.add("该电站没有逆变器");
        } else {
            for (Device temp : inverterListResult) {
                returnInverterSnList.add(temp.getDeviceId());
            }
        }
        return returnInverterSnList;
    }

    @Override
    public String getOnlineInverterStats(String plantUid) {
        QueryWrapper<Device> deviceQueryWrapper = new QueryWrapper<>();
        deviceQueryWrapper.eq("is_deleted", "0");
        deviceQueryWrapper.eq("device_type", "1");
        deviceQueryWrapper.ne("status", "0");
        deviceQueryWrapper.eq("plant_uid", plantUid);

        List<Device> devices = deviceMapper.selectList(deviceQueryWrapper);
        return String.valueOf(devices.size());
    }


    @Override
    public String getPlantMaxPower(String date, List<String> inverterSnList) {
        String tableName = TableUtils.getTableName(TABLE_PREFIX, date);
        return inverterMapper.getPlantMaxPower(tableName, inverterSnList);
    }

    @Override
    public Integer getInverterAlarmCount(String inverterSN) {
        LambdaQueryWrapper<InverterAlarm> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .eq(InverterAlarm::getInverterSn, inverterSN)
                .eq(InverterAlarm::getStatus, 0)
        ;
        long count = this.count(wrapper);

        return Math.toIntExact(count);
    }

    @Override
    public HashMap<String, Object> getInverterRealTimeData(InverterRealTimeQueryVO query) {
        String tableSuffix = "";
        if (query.getDate() != null && !"".equals(query.getDate())) {
            // tableSuffix = query.getDate().replace("-", "");
            tableSuffix = query.getDate().split("-")[0];
        } else {
            // tableSuffix = DateUtil.today().replace("-", "");
            tableSuffix = DateUtil.today().split("-")[0];
        }
        Integer pvNum = inverterLatestMapper.selectPvNum(query.getInverterSN());
        Page<InverterRealTimeInfoVO> page = new Page<>(query.getCurrentPage(), query.getPageSize());
        IPage<InverterRealTimeInfoVO> inverterRealTimeDataPage = inverterLatestMapper.getInverterRealTimeData(query, tableSuffix, page);
        HashMap<String, Object> result = new HashMap<>();
        result.put("pvNum", pvNum);
        result.put("records", inverterRealTimeDataPage.getRecords());
        result.put("total", inverterRealTimeDataPage.getTotal());
        result.put("currentPage", inverterRealTimeDataPage.getCurrent());
        result.put("pageSize", inverterRealTimeDataPage.getSize());
        return result;
    }
}
