package com.bto.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.AttachmentQuery;
import com.bto.commons.pojo.dto.StorageDTO;
import com.bto.commons.pojo.entity.Attachment;
import com.bto.commons.pojo.entity.Project;
import com.bto.commons.pojo.entity.User;
import com.bto.commons.pojo.vo.AttachmentVO;
import com.bto.commons.response.ResultEnum;
import com.bto.system.dao.AttachmentMapper;
import com.bto.system.dao.ProjectMapper;
import com.bto.system.dao.UserMapper;
import com.bto.system.service.ResourceFileService;
import com.bto.system.storage.service.StorageService;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

import static com.bto.commons.constant.SystemConstant.*;

/**
 * <AUTHOR>
 * @date 2023/6/17 11:57
 */
@Service
@AllArgsConstructor
public class ResourceFileServiceImpl implements ResourceFileService {
    @Autowired
    private ProjectMapper projectMapper;
    private final StorageService storageService;
    private final UserMapper userMapper;
    private final AttachmentMapper attachmentMapper;

    @Override
    public StorageDTO uploadWorkOrderIssuePhoto(MultipartFile file)  {
        //将文件上传到服务器，并返回文件信息
        StorageDTO dto;
        try {
            dto = upload(file, WORK_ORDER_CLOUD);
        } catch (Exception e) {
            throw new BusinessException(ResultEnum.UPLOAD_FILE_FAILED);
        }
        return dto;
    }

    @Override
    public StorageDTO uploadScreenLogo(MultipartFile imgFile, String projectId) throws IOException {
        //将文件上传到服务器，并返回文件信息
        StorageDTO storageDTO = upload(imgFile, PROJECT_SCREEN_LOGO_URL + projectId);
        //更新项目表logo
        LambdaUpdateChainWrapper<Project> updateChainWrapper = new LambdaUpdateChainWrapper<>(projectMapper);
        try {
            updateChainWrapper
                    .eq(StrUtil.isNotEmpty(projectId), Project::getId, projectId)
                    .set(StrUtil.isNotEmpty(storageDTO.getUrl()), Project::getScreenLogo, storageDTO.getUrl())
                    .set(Project::getUpdateTime, DateUtil.now())
                    .update();
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(ResultEnum.USER_NON_EXISTENT);
        }
        return storageDTO;
    }

    @Override
    public StorageDTO uploadProjectLogo(MultipartFile imgFile, String projectId) throws IOException {
        //将文件上传到服务器，并返回文件信息
        StorageDTO storageDTO = upload(imgFile, PROJECT_LOGO_URL + projectId);
        //更新项目表logo
        LambdaUpdateChainWrapper<Project> updateChainWrapper = new LambdaUpdateChainWrapper<>(projectMapper);
        try {
            updateChainWrapper
                    .eq(StrUtil.isNotEmpty(projectId), Project::getId, projectId)
                    .set(StrUtil.isNotEmpty(storageDTO.getUrl()), Project::getImgUrl, storageDTO.getUrl())
                    .set(Project::getUpdateTime, DateUtil.now())
                    .update();
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(ResultEnum.USER_NON_EXISTENT);
        }
        return storageDTO;
    }

    @Override
    public StorageDTO uploadUserAvatar(MultipartFile imgFile, String userUid) throws IOException {
        StorageDTO storageDTO = upload(imgFile, USER_IMAGE_URL + userUid);
        LambdaUpdateChainWrapper<User> updateChainWrapper = new LambdaUpdateChainWrapper<>(userMapper);
        //更新用户表头像
        try {
            updateChainWrapper
                    .eq(StrUtil.isNotEmpty(userUid), User::getUserUid, userUid)
                    .set(StrUtil.isNotEmpty(storageDTO.getUrl()), User::getUserAvatar, storageDTO.getUrl())
                    .set(User::getUpdateTime, DateUtil.now())
                    .update();
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(ResultEnum.USER_NON_EXISTENT);
        }
        return storageDTO;
    }

    @Override
    public IPage<AttachmentVO> getAttachmentByPage(AttachmentQuery query) {
        IPage<Attachment> page = new Page<>(query.getCurrentPage(), query.getPageSize());
        LambdaQueryWrapper<Attachment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(ObjectUtil.isNotEmpty(query.getName()), Attachment::getName, query.getName());
        queryWrapper.like(ObjectUtil.isNotEmpty(query.getPlatform()), Attachment::getPlatform, query.getPlatform());
        page = attachmentMapper.selectPage(page, queryWrapper);
        return page.convert(obj -> {
            AttachmentVO attachmentVO = new AttachmentVO();
            BeanUtil.copyProperties(obj, attachmentVO);
            return attachmentVO;
        });

    }


    /**
     * 调用文件上传api
     *
     * @param file
     * @return
     * @throws IOException
     */
    public StorageDTO upload(MultipartFile file, String folder) throws IOException {
        // 是否为空
        if (file.isEmpty()) {
            return null;
        }
        // 上传路径
        String path = folder + storageService.getPath(file.getOriginalFilename());
        // 上传文件
        String url = storageService.upload(file.getBytes(), path);

        // 上传信息
        StorageDTO storage = new StorageDTO();
        storage.setUrl(url);
        storage.setSize(file.getSize());
        return storage;
    }


}
