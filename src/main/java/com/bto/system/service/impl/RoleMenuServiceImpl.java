package com.bto.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bto.commons.pojo.vo.MenuInfoVO;
import com.bto.commons.pojo.entity.RoleHasMenu;
import com.bto.commons.utils.TreeUtils;
import com.bto.system.dao.MenuMapper;
import com.bto.system.dao.RoleHasMenuMapper;
import com.bto.system.service.RoleMenuMPPService;
import com.bto.system.service.RoleMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/20 14:42
 */
@Service
public class RoleMenuServiceImpl extends BaseServiceImpl<RoleHasMenuMapper, RoleHasMenu> implements RoleMenuService {
    @Autowired
    private MenuMapper menuMapper;
    @Autowired
    private RoleHasMenuMapper roleHasMenuMapper;
    @Autowired
    private RoleMenuMPPService roleMenuMPPService;

    @Override
    public List<MenuInfoVO> getMenuListByRole(Long roleId) {
        boolean deletedField = true;
        List<Long> menUidList = menuMapper.getMenUidList(roleId,deletedField);
        if (CollUtil.isEmpty(menUidList)){
            return new ArrayList<>();
        }
        List<MenuInfoVO> menuList = menuMapper.getMenuListByRole(menUidList);
        if (menuList.size()>0){
            return TreeUtils.build(menuList);
        }
        return menuList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer saveOrUpdate(Long roleId, List<Long> menUidList) {
        int result = 0;
        // 查询该角色,数据库菜单的ID列表
        boolean deletedField = true;
        List<Long> dbNotDelMenuList = menuMapper.getMenUidList(roleId,deletedField);
        //前端改动的菜单ID列表存在但数据库未删除的ID列表中不存在的菜单ID
        Collection<Long> insertMenUidList = CollUtil.subtract(menUidList, dbNotDelMenuList);
        if (CollUtil.isNotEmpty(insertMenUidList)) {
            List<RoleHasMenu> menuList = insertMenUidList.stream().map(menUid -> {
                RoleHasMenu roleMenu = new RoleHasMenu();
                roleMenu.setRoleId(roleId);
                roleMenu.setMenUid(menUid);
                roleMenu.setCreateTime(DateUtil.date());
                roleMenu.setUpdateTime(DateUtil.date());
                roleMenu.setIsDeleted(0);
                return roleMenu;
            }).collect(Collectors.toList());
            //批量新增记录
            roleMenuMPPService.saveOrUpdateBatchByMultiId(menuList);
            result++;
        }
        //求出数据库存在，但前端不存在的菜单ID（即为前端进行删除的ID）
        Collection<Long> deleteMenUidList = CollUtil.subtract(dbNotDelMenuList, menUidList);
        // 删除多余的菜单ID
            if (CollUtil.isNotEmpty(deleteMenUidList)) {
                List<RoleHasMenu> menuList = deleteMenUidList.stream().map(menUid -> {
                    RoleHasMenu roleMenu = new RoleHasMenu();
                    roleMenu.setRoleId(roleId);
                    roleMenu.setMenUid(menUid);
                    roleMenu.setUpdateTime((DateUtil.date()));
                    roleMenu.setIsDeleted(1);
                    return roleMenu;
                }).collect(Collectors.toList());
                //批量新增记录
                roleMenuMPPService.saveOrUpdateBatchByMultiId(menuList);
                result++;
            }
        return result;
    }

    @Override
    public Integer delete(Long roleId) {
        QueryWrapper<RoleHasMenu> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_id", roleId);
        return roleHasMenuMapper.delete(queryWrapper);
    }
}
