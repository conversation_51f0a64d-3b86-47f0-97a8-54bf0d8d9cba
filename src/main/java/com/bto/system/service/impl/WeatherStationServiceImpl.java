package com.bto.system.service.impl;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.response.Result;
import com.bto.system.service.WeatherStationService;
import org.springframework.stereotype.Service;

import static com.bto.commons.constant.SystemConstant.PHOTOVOLTAIC_PREFIX_URL;
import static com.bto.commons.constant.SystemConstant.WEATHER_STATION_CUSTOMER_URL;

/**
 * <AUTHOR>
 * @since 2024/9/21 16:42
 */
@Service
public class WeatherStationServiceImpl implements WeatherStationService {
    @Override
    public Result<Object> getCustomer() {
        HttpResponse response = HttpUtil.createPost(PHOTOVOLTAIC_PREFIX_URL+WEATHER_STATION_CUSTOMER_URL).execute();
        String result = response.body();
        JSONObject jsonObject = JSONObject.parseObject(result);
        Object o = jsonObject.get("status");
        if (o != null && "00000".equals(o.toString())) {
            Object body = jsonObject.get("data");
            return Result.success(body);
        } else {
            String msg = jsonObject.get("message").toString();
            throw new BusinessException(msg);
        }
    }
}
