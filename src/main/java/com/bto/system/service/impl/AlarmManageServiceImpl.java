package com.bto.system.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.pojo.dto.PlantAlarmQueryDTO;
import com.bto.commons.pojo.entity.ViewPlantAlarm;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.utils.CreateExcelUtils;
import com.bto.system.utils.GlobalParamUtil;
import com.bto.system.dao.AlarmManageMapper;
import com.bto.system.service.AlarmManageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR> by zhb on 2024/5/9.
 */

@Service
public class AlarmManageServiceImpl implements AlarmManageService {

    @Autowired
    private AlarmManageMapper alarmManageMapper;
    @Resource
    private GlobalParamUtil globalParamUtil;
    @Override
    public IPage<ViewPlantAlarm> getPlantAlarmInfoList(PlantAlarmQueryDTO query, RequireParamsDTO userInfo) {
        IPage<ViewPlantAlarm> iPage = new Page<>(query.getCurrentPage(), query.getPageSize());
        if (!query.getIsPage()) {
            iPage = new Page<>(-1,-1);
        }
        if (StrUtil.isEmpty(query.getOrder())) {
            query.setOrder("start_time");
        }
        return alarmManageMapper.getPlantAlarmInfoList(iPage, userInfo, query);
    }

    @Override
    public void exportInverterAndOperatorAlarmInfo(PlantAlarmQueryDTO query, HttpServletResponse response) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        query.setCurrentPage(-1);
        query.setPageSize(-1);
        IPage<ViewPlantAlarm> page = getPlantAlarmInfoList(query, userInfo);
        List<ViewPlantAlarm> records = page.getRecords();
        CreateExcelUtils.exportToExcel(records, ViewPlantAlarm.class, "inverter_and_operator_alarm_info", response);
    }

    @Override
    public HashMap<String, String> getAlarmNumInfo(RequireParamsDTO userInfo) {
        //查询告警电站数量
        Integer alarmPlantNum = alarmManageMapper.getAlarmPlantNum(userInfo);
        //查询告警信息数量
        Integer alarmInfoNum = alarmManageMapper.getAlarmInfoNum(userInfo);
        HashMap<String, String> result = new HashMap<>();
        result.put("alarmPlantNum", alarmPlantNum.toString());
        result.put("alarmInfoNum", alarmInfoNum.toString());
        return result;
    }



}
