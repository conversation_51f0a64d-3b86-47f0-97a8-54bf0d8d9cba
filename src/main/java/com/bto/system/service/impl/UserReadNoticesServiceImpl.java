package com.bto.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bto.commons.pojo.entity.UserReadNotices;
import com.bto.system.dao.UserReadNoticesMapper;
import com.bto.system.service.UserReadNoticesService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class UserReadNoticesServiceImpl extends ServiceImpl<UserReadNoticesMapper, UserReadNotices> implements UserReadNoticesService  {

    @Override
    public void updateReadLog(String noticesId, String userUid) {
        UserReadNotices userReadNotices = new UserReadNotices();
        userReadNotices.setNoticesId(noticesId);
        userReadNotices.setUserUid(userUid);
        UserReadNotices notice = this.lambdaQuery()
                .eq(UserReadNotices::getUserUid, userUid)
                .one();

        if (notice !=null){
            notice.setNoticesId(noticesId);
            this.updateById(notice);
        }else {
            this.save(userReadNotices);
        }
    }

}