package com.bto.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bto.commons.converter.vo.SensorDataConvert;
import com.bto.commons.pojo.dto.SensorDataQuery;
import com.bto.commons.pojo.entity.SensorDataEntity;
import com.bto.commons.pojo.vo.DeviceVO;
import com.bto.commons.pojo.vo.PlantWithSensorTree;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.pojo.vo.SensorDataVO;
import com.bto.system.dao.DeviceMapper;
import com.bto.system.dao.SensorDataDao;
import com.bto.system.service.SensorDataService;
import com.bto.system.utils.GlobalParamUtil;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 传感器数据
 *
 * <AUTHOR>
 * @since 1.0.0 2024-06-17
 */
@Service
@AllArgsConstructor
public class SensorDataServiceImpl extends BaseServiceImpl<SensorDataDao, SensorDataEntity> implements SensorDataService {


    @Autowired
    private GlobalParamUtil globalParamUtil;
    @Autowired
    private DeviceMapper deviceMapper;

    @Override
    public List<PlantWithSensorTree> tree(SensorDataQuery query) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        List<DeviceVO> devices = deviceMapper.selectListByType(null, userInfo, 12);
        List<String> deviceIds = devices.stream().map(DeviceVO::getDeviceId).distinct().collect(Collectors.toList());

        List<SensorDataEntity> sensorList = baseMapper.selectLatest(deviceIds);

        if (CollUtil.isEmpty(devices)) {
            return Collections.emptyList();
        }
        List<PlantWithSensorTree> result = new ArrayList<>();
        List<SensorDataVO> sensors = SensorDataConvert.INSTANCE.convertList(sensorList);
        for (DeviceVO device : devices) {
            if (ObjectUtil.isNull(device)) {
                continue;
            }
            PlantWithSensorTree tree = PlantWithSensorTree.builder()
                    .plantUid(device.getPlantUid())
                    .deviceName(device.getDeviceName()).children(new ArrayList<>()).build();
            for (SensorDataVO sensor : sensors) {
                if (ObjectUtil.isNull(sensor)) {
                    continue;
                }
                if (sensor.getSensorId().equals(device.getDeviceId())) {
                    tree.getChildren().add(sensor);
                }
            }
            result.add(tree);

        }
        return result;
    }

    private LambdaQueryWrapper<SensorDataEntity> getWrapper(SensorDataQuery query) {
        LambdaQueryWrapper<SensorDataEntity> wrapper = Wrappers.lambdaQuery();

        return wrapper;
    }

    @Override
    public void save(SensorDataVO vo) {
        SensorDataEntity entity = SensorDataConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    @Override
    public void update(SensorDataVO vo) {
        SensorDataEntity entity = SensorDataConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        removeByIds(idList);
    }

}