package com.bto.system.service.impl;

import cn.hutool.core.date.DateUtil;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.vo.MenuInfoVO;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.pojo.entity.Menu;
import com.bto.commons.pojo.dto.MenuInfoDTO;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.utils.PropertyUtils;
import com.bto.commons.utils.TreeUtils;
import com.bto.system.dao.MenuMapper;
import com.bto.system.service.MenuService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/18 18:09
 */
@Service
public class MenuServiceImpl implements MenuService {
    @Autowired
    private MenuMapper menuMapper;

    @Override
    public List<MenuInfoVO> getMenuList(RequireParamsDTO userInfo) {
        List<MenuInfoVO> menuList = menuMapper.getMenuList(userInfo);
        List<MenuInfoVO> result = TreeUtils.build(menuList);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer addMenu(MenuInfoDTO menuInfo) {
        Integer pid = menuInfo.getPid();
        // 查询所有父级ID为指定父级ID的项目并排序，取出id最大的一条
        Menu menu = menuMapper.getMenu(pid);
        if (menu != null) {
            // 排除10、100 ...的顶层id
            // 若id=9,19,29,...则+2
            if (menu.getId() % 10 / 9 == 1) {
                menu.setId(menu.getId() + 2);
            }else {
                menu.setId(menu.getId() + 1);
            }
        } else {
            menu = new Menu();
            menu.setId(menuInfo.getPid() * 10);
        }
        menu.setCreateTime(DateUtil.date());
        BeanUtils.copyProperties(menuInfo, menu, PropertyUtils.getNullPropertyNames(menuInfo));
        int count = menuMapper.insert(menu);
        return count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer editMenu(MenuInfoDTO menuInfo) {
        Menu menu = menuMapper.selectById(menuInfo.getId());
        menu.setUpdateTime(DateUtil.date());
        BeanUtils.copyProperties(menuInfo, menu, PropertyUtils.getNullPropertyNames(menuInfo));
        int count = menuMapper.updateById(menu);
        return count;
    }

    @Override
    public Integer deleteMenu(String menUid) {
        // 判断菜单是否存在
        Menu menu = menuMapper.selectById(menUid);
        if (menu != null) {
            // 删除菜单
            int count = menuMapper.deleteById(menUid);
            return count;
        } else {
            throw new BusinessException(
                    ResultEnum.USER_NON_EXISTENT.getCode(),
                    ResultEnum.USER_NON_EXISTENT.getMessage()
            );
        }
    }
}
