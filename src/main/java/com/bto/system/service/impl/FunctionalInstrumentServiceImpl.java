package com.bto.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.pojo.dto.FunctionalInstrumentQuery;
import com.bto.commons.pojo.entity.FunctionalInstrumentEntity;
import com.bto.commons.pojo.vo.PageResult;
import com.bto.system.dao.FunctionalInstrumentDao;
import com.bto.system.service.FunctionalInstrumentService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 多功能仪表数据
 *
 * <AUTHOR>
 * @since 2024-10-09
 */
@Service
@AllArgsConstructor
public class FunctionalInstrumentServiceImpl extends BaseServiceImpl<FunctionalInstrumentDao, FunctionalInstrumentEntity> implements FunctionalInstrumentService {

    @Override
    public PageResult<FunctionalInstrumentEntity> page(FunctionalInstrumentQuery query) {
        Page<FunctionalInstrumentEntity> page = new Page<>(query.getCurrentPage(), query.getPageSize());
        page = baseMapper.page(page, query, null);

        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    private LambdaQueryWrapper<FunctionalInstrumentEntity> getWrapper(FunctionalInstrumentQuery query) {
        LambdaQueryWrapper<FunctionalInstrumentEntity> wrapper = Wrappers.lambdaQuery();

        return wrapper;
    }


}