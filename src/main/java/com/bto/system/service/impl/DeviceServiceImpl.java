package com.bto.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.enums.DeviceType;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.AddDeviceDTO;
import com.bto.commons.pojo.dto.CounterQuery;
import com.bto.commons.pojo.dto.OperatorQueryDTO;
import com.bto.commons.pojo.entity.Device;
import com.bto.commons.pojo.entity.OperatorRealTimeInfo;
import com.bto.commons.pojo.vo.CounterVO;
import com.bto.commons.pojo.vo.DeviceVO;
import com.bto.commons.pojo.vo.OperatorListVO;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.response.ResultEnum;
import com.bto.system.dao.DeviceMapper;
import com.bto.system.dao.OperatorMapper;
import com.bto.system.service.DeviceService;
import com.bto.system.service.PlantService;
import com.bto.system.utils.GlobalParamUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/9 14:40
 */
@Service(value = "deviceService")
@Slf4j
public class DeviceServiceImpl implements DeviceService {
    @Resource
    private OperatorMapper operatorMapper;

    @Resource
    private PlantService plantService;
    @Resource
    private DeviceMapper deviceMapper;
    @Resource
    private GlobalParamUtil globalParamUtil;


    @Override
    public OperatorRealTimeInfo getPointStatusByPlantUid(String plantUid) {
        LambdaQueryWrapper<OperatorRealTimeInfo> queryWrapper = new LambdaQueryWrapper<>();
        String today = DateUtil.today();
        queryWrapper.eq(OperatorRealTimeInfo::getPlantUid, plantUid)
                .eq(OperatorRealTimeInfo::getCollectDate, today);
        return operatorMapper.selectOne(queryWrapper);
    }

    @Override
    public Page<OperatorListVO> getOperatorInfo(RequireParamsDTO userInfo, OperatorQueryDTO query) {
        Page<OperatorListVO> page = new Page<>(query.getCurrentPage(), query.getPageSize());
        query.setOrder(StrUtil.isEmpty(query.getOrder()) ? "createTime" : query.getOrder());
        return operatorMapper.getOperatorInfo(userInfo, query, page);
    }


    @Override
    public void addDevice(AddDeviceDTO addDeviceDTO) {
        Device device = new Device();
        BeanUtil.copyProperties(addDeviceDTO, device);
        try {
            deviceMapper.insert(device);
        } catch (Exception e) {
            throw new BusinessException(ResultEnum.SINGLE_INSERT_ERROR);
        }
    }

    @Override
    public void deleteDevice(List<String> ids) {
        LambdaQueryWrapper<Device> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .in(Device::getDeviceId, ids)
                .eq(Device::getDeviceType, DeviceType.COUNTER.getCode());
        deviceMapper.delete(wrapper);
    }

    @Override
    public IPage<CounterVO> getCounterPage(CounterQuery query) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        query.setDeviceType(DeviceType.COUNTER.getCode());
        IPage<CounterVO> page = new Page<>(query.getCurrentPage(), query.getPageSize());

        page = deviceMapper.getCounterPage(page, userInfo, query);
        return page;
    }

    @Override
    public void update(AddDeviceDTO dto) {
        Device device = BeanUtil.copyProperties(dto, Device.class);
        LambdaQueryWrapper<Device> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .eq(Device::getDeviceId, dto.getDeviceId())
                .eq(StrUtil.isNotBlank(dto.getDeviceType()), Device::getDeviceType, dto.getDeviceType());
        deviceMapper.update(device, wrapper);
    }

    @Override
    public List<DeviceVO> counterMonitor(List<String> typeList) {
        if (CollUtil.isEmpty(typeList)) {
            // 查找配电房 配电柜 逆变器 烟雾传感器
            typeList = Arrays.asList(
                    DeviceType.DISTRIBUTION_ROOM.getCode(),
                    DeviceType.COUNTER.getCode(),
                    DeviceType.INVERTER.getCode(),
                    DeviceType.ENVIRONMENT_SENSOR.getCode());
        }

        List<DeviceVO> list = deviceMapper.counterMonitor(typeList);

        return list;

    }
}
