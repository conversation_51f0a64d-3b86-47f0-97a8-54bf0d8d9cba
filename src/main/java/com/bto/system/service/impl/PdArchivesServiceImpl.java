package com.bto.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.converter.vo.PdArchivesConvert;
import com.bto.commons.pojo.dto.PdArchivesDTO;
import com.bto.commons.pojo.dto.PdArchivesQuery;
import com.bto.commons.pojo.entity.PdArchivesEntity;
import com.bto.commons.pojo.vo.*;
import com.bto.system.dao.PdArchivesDao;
import com.bto.system.service.PdArchivesService;
import com.bto.system.utils.GlobalParamUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 配电室档案
 *
 * <AUTHOR>
 * @since 2024-06-03
 */
@Service
@AllArgsConstructor
public class PdArchivesServiceImpl extends BaseServiceImpl<PdArchivesDao, PdArchivesEntity> implements PdArchivesService {

    private final GlobalParamUtil globalParamUtil;

    @Override
    public PageResult<PdArchivesVO> page(PdArchivesQuery query) {
        Page<PdArchivesEntity> page = new Page<>(query.getCurrentPage(), query.getPageSize());
        if (ObjectUtil.isEmpty(query.getOrder())) {
            query.setOrder("create_time");
        }
        IPage<PdArchivesEntity> result = baseMapper.selectPage(page, getWrapper(query));
        List<PdArchivesVO> list = PdArchivesConvert.INSTANCE.convertList(result.getRecords());
        return new PageResult<>(list, result.getTotal());
    }

    private LambdaQueryWrapper<PdArchivesEntity> getWrapper(PdArchivesQuery query) {
        LambdaQueryWrapper<PdArchivesEntity> wrapper = Wrappers.lambdaQuery();

        return wrapper;
    }

    @Override
    public void save(PdArchivesDTO dto) {

        PdArchivesEntity entity = PdArchivesConvert.INSTANCE.convert(dto);

        baseMapper.insert(entity);
    }

    @Override
    public void update(PdArchivesVO vo) {
        PdArchivesEntity entity = PdArchivesConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        removeByIds(idList);
    }

    @Override
    public List<PlantWithArchivesTree> tree(PdArchivesQuery query) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        List<PlantWithArchiveVO> list = baseMapper.tree(userInfo, query);

        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }

        // 使用Map按plantUid收集数据，方便后续构建树形结构
        Map<String, PlantWithArchivesTree> treeMap = Maps.newHashMap();
        Map<String, List<PdArchivesVO>> pdArchivesVoMap = Maps.newHashMap();

        for (PlantWithArchiveVO vo : list) {
            // 初始化PlantWithArchivesTree并放入treeMap
            treeMap.putIfAbsent(vo.getPlantUid(), new PlantWithArchivesTree(vo.getPlantUid(), vo.getPlantName(), Lists.newArrayList()));

            // 将PdArchivesVO按plantUid收集
            pdArchivesVoMap.computeIfAbsent(vo.getPlantUid(), k -> Lists.newArrayList()).add(
                    BeanUtil.copyProperties(vo, PdArchivesVO.class));
        }

        // 构建children列表
        for (Map.Entry<String, PlantWithArchivesTree> entry : treeMap.entrySet()) {
            entry.getValue().setChildren(pdArchivesVoMap.get(entry.getKey()));
        }

        // 返回构建好的树形结构列表
        return new ArrayList<>(treeMap.values());
    }
}