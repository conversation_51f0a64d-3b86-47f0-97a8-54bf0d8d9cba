package com.bto.system.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.pojo.dto.FunctionalInstrumentQuery;
import com.bto.commons.pojo.entity.FunctionalInstrumentEntity;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 多功能仪表数据
 *
 * <AUTHOR>
 * @since 2024-10-09
 */
@Mapper
public interface FunctionalInstrumentDao extends BaseMapper<FunctionalInstrumentEntity> {
    Page<FunctionalInstrumentEntity> page(@Param("page") Page<FunctionalInstrumentEntity> page, @Param("query") FunctionalInstrumentQuery query, @Param("userInfo") RequireParamsDTO userInfo);

}