package com.bto.system.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bto.commons.pojo.dto.PdArchivesQuery;
import com.bto.commons.pojo.entity.PdArchivesEntity;
import com.bto.commons.pojo.vo.PlantWithArchiveVO;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 配电室档案
 *
 * <AUTHOR>
 * @since 2024-06-03
 */
@Mapper
public interface PdArchivesDao extends BaseMapper<PdArchivesEntity> {

    List<PlantWithArchiveVO> tree(@Param("userInfo") RequireParamsDTO userInfo, @Param("query") PdArchivesQuery query);
}