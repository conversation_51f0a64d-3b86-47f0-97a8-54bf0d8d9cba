package com.bto.system.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bto.commons.pojo.vo.MenuInfoVO;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.pojo.entity.Menu;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/18 18:12
 */
@Mapper
public interface MenuMapper extends BaseMapper<Menu> {
    /**
     * 获取菜单列表
     * @return
     */
    List<MenuInfoVO> getMenuList(RequireParamsDTO userInfo);

    /**
     * 获取菜单列表
     * @return
     */
    List<MenuInfoVO> getMenuListByRole(@Param("menuArray") List<Long> menuArray);
    /**
     * 获取菜单ID列表
     */
    List<Long> getMenUidList(@Param("roleId") Long roleId
            ,@Param("deletedField") Boolean deletedField);
    /**
     * 获取已被删除的菜单ID列表
     */
    List<Long> getDelMenUidList(@Param("roleId") Long roleId);

    /**
     *  查询所有父级ID为指定父级ID的项目并排序，取出id最大的一条
     * @param pid
     * @return
     */
    Menu getMenu(@Param("pid") Integer pid);
}
