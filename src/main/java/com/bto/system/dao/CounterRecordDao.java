package com.bto.system.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bto.commons.pojo.dto.CounterQuery;
import com.bto.commons.pojo.entity.CounterRecordEntity;
import com.bto.commons.pojo.vo.CounterRecordVO;
import com.bto.commons.pojo.vo.CounterVO;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 配电柜数据
 *
 * <AUTHOR>
 * @since 2024-07-24
 */
@Mapper
public interface CounterRecordDao extends BaseMapper<CounterRecordEntity> {

    IPage<CounterRecordVO> getPage(@Param("page") IPage<CounterRecordVO> page, @Param("userInfo") RequireParamsDTO userInfo, @Param("query") CounterQuery query);
}