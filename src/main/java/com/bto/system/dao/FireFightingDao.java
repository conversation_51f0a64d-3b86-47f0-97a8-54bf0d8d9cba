package com.bto.system.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.pojo.dto.FireFightingQuery;
import com.bto.commons.pojo.entity.FireFightingEntity;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 消防联动
 *
 * <AUTHOR>
 * @since 1.0.0 2024-09-27
 */
@Mapper
public interface FireFightingDao extends BaseMapper<FireFightingEntity> {

    Page<FireFightingEntity> page(@Param("page") Page<FireFightingEntity> page, @Param("query") FireFightingQuery query, @Param("userInfo") RequireParamsDTO userInfo);

}