package com.bto.system.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.pojo.dto.PlantAddressDTO;
import com.bto.commons.pojo.dto.PlantQueryDTO;
import com.bto.commons.pojo.dto.PowerPlantInfoQueryDTO;
import com.bto.commons.pojo.entity.Plant;
import com.bto.commons.pojo.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PlantMapper extends BaseMapper<Plant> {
    /**
     * 获取电站列表
     *
     * @param query
     * @param userInfo
     * @param page
     * @return
     */
    IPage<PlantInfoVO> getPlantList(@Param("query") PlantQueryDTO query,
                                    @Param("userInfo") RequireParamsDTO userInfo,
                                    @Param("page") Page<PlantInfoVO> page
    );

    List<PlantInfoVO> selectAll(@Param("userInfo") RequireParamsDTO userInfo, @Param("projectId") String projectId);

    PlantVO selectPlantInfo(@Param("plantUid") String plantUid);

    /**
     * 获取各省/各市电站信息与坐标
     *
     * @param query
     * @param userInfo
     * @return List<AreaCoordinateInfoDTO>
     */
    List<AreaCoordinateInfoVO> getPlantInfoCoordinateOfArea(@Param("query") PlantAddressDTO query,
                                                            @Param("userInfo") RequireParamsDTO userInfo);

    /**
     * 查询站点轮播数据
     *
     * @return
     */
    List<PlantCarouselVO> selectPlantList(@Param("userInfo") RequireParamsDTO userInfo);

    IPage<WorkEfficiencyVO> getPlantElectricityRank(@Param("query") PowerPlantInfoQueryDTO query, @Param("userInfo") RequireParamsDTO userInfo, @Param("page") Page<PowerPlantInfoVO> page);
}