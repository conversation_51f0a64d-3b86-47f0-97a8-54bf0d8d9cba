package com.bto.system.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bto.commons.pojo.dto.PlantAlarmQueryDTO;
import com.bto.commons.pojo.entity.ViewPlantAlarm;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2023/8/24 11:36
 */
@Mapper
public interface AlarmManageMapper extends BaseMapper<ViewPlantAlarm> {


    /**
     * 查询电站告警信息列表
     * @param iPage
     * @param userInfo
     * @param query
     * @return
     */
    IPage<ViewPlantAlarm> getPlantAlarmInfoList(IPage<ViewPlantAlarm> iPage,
                                                @Param("userInfo") RequireParamsDTO userInfo,
                                                @Param("query") PlantAlarmQueryDTO query);

    /**
     * 获取告警数量信息统计
     * @param userInfo
     * @return
     */
    Integer getAlarmInfoNum(@Param("userInfo") RequireParamsDTO userInfo);

    /**
     * 查询告警电站数量
     * @param userInfo
     * @return
     */
    Integer getAlarmPlantNum(@Param("userInfo") RequireParamsDTO userInfo);


}