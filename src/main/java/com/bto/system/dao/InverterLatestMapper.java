package com.bto.system.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.pojo.dto.InverterInfoDTO;
import com.bto.commons.pojo.entity.InverterLatest;
import com.bto.commons.pojo.vo.InverterRealTimeInfoVO;
import com.bto.commons.pojo.vo.InverterRealTimeQueryVO;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InverterLatestMapper extends BaseMapper<InverterLatest> {
     List<InverterInfoDTO> getInverters(
            @Param("inverterInfoVOPage") Page<InverterInfoDTO> inverterInfoVOPage,
            @Param("inverterInfoDTO") InverterInfoDTO inverterInfoDTO,
            @Param("userInfo") RequireParamsDTO userInfo

    );

    /**
     * 获取逆变器实时数据
     * @param inverterSN
     */
    IPage<InverterRealTimeInfoVO> getInverterRealTimeData(@Param("query") InverterRealTimeQueryVO query,
                                                          @Param("tableSuffix") String tableSuffix,
                                                          IPage<InverterRealTimeInfoVO> page);

    /**
     * 查询逆变器PV数量
     * @param inverterSN
     * @return
     */
    Integer selectPvNum(@Param("inverterSN") String inverterSN);
}
