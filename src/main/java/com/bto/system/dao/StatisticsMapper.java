package com.bto.system.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.pojo.dto.ElectricityStatisticsQueryDTO;
import com.bto.commons.pojo.dto.IntegrativeStatisticQueryDTO;
import com.bto.commons.pojo.dto.PlantStatisticsQueryDTO;
import com.bto.commons.pojo.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/24 9:34
 */
@Mapper
public interface StatisticsMapper {

    /**
     * 获取电站统计数量信息
     */
    List<NumInfoVO> getPlantNumInfo(@Param("userInfo") RequireParamsDTO userInfo);

    /**
     * 获取电站发电量数据
     *
     * @param userInfo
     * @return
     */
    PlantElectricityVO getPlantElectricityInfo(@Param("userInfo") RequireParamsDTO userInfo);

    /**
     * 查询工作效率信息
     *
     * @param userInfo
     * @return
     */
    PlantElectricityVO getWorkEfficiencyInfo(@Param("userInfo") RequireParamsDTO userInfo);

    String getPlantMassByProjectId(@Param("userInfo") RequireParamsDTO userInfo, @Param("date") String date);

    Integer getPlantMassByPlantList(@Param("userInfo") RequireParamsDTO userInfo);

    /**
     * 获取逆变器统计数量信息
     *
     * @param userInfo
     * @return
     */
    List<NumInfoVO> getInverterNumInfo(@Param("userInfo") RequireParamsDTO userInfo);

    /**
     * 获取近六个月发电量数据
     *
     * @param userInfo
     * @return
     */
    List<ChartElectricityInfoVO> getElectricityBySixMonth(@Param("userInfo") RequireParamsDTO userInfo);

    /**
     * 查询所有电站当日每小时发电量统计
     *
     * @param tableSuffix
     * @param userInfo
     * @param date
     * @return
     */
    List<ChartElectricityInfoVO> getEveryHourElectricityInfo(@Param("tableSuffix") String tableSuffix
            , @Param("userInfo") RequireParamsDTO userInfo, @Param("date") String date);

    /**
     * 根据条件查询电站Uid
     *
     * @param query
     * @param page
     * @param userInfo
     * @param dateType
     * @return
     */
    Page<PlantStatisticsInfoVO> selectPlantUidList(@Param("query") PlantStatisticsQueryDTO query,
                                                   @Param("page") IPage<PlantStatisticsInfoVO> page,
                                                   @Param("userInfo") RequireParamsDTO userInfo,
                                                   @Param("dateType") String dateType);

    /**
     * 电站每月中日发电量数据查询
     *
     * @param query
     * @param plantUidArray
     * @return
     */
    List<ElectricityStaticsInfoVO> getElectricityStatisticsInfoByMonth(@Param("query") ElectricityStatisticsQueryDTO query,
                                                                       @Param("plantUidArray") List<String> plantUidArray);

    /**
     * 电站每日各时间段发电量数据查询
     *
     * @param query
     * @param tableSuffix
     * @param plantUidArray
     * @return
     */
    List<ElectricityStaticsInfoVO> getElectricityStatisticsInfoByDay(@Param("query") ElectricityStatisticsQueryDTO query, @Param("tableName") String tableName
            , @Param("plantUidArray") List<String> plantUidArray);

    /**
     * 电站每年中月发电量数据查询
     *
     * @param query
     * @param plantUidArray
     * @return
     */
    List<ElectricityStaticsInfoVO> getElectricityStatisticsInfoByYear(@Param("query") ElectricityStatisticsQueryDTO query, @Param("plantUidArray") List<String> plantUidArray);

    /**
     * 电站基本信息查询-（多条件查询）
     *
     * @param query
     * @param plantUidArray
     * @param dateType
     * @return
     */
    List<PlantStatisticsInfoVO> getPlantBaseInfo(@Param("query") PlantStatisticsQueryDTO query,
                                                 @Param("plantUidArray") List<String> plantUidArray,
                                                 @Param("dateType") String dateType);

    /**
     * 通过电站Uid数组查询电站告警数量
     *
     * @param query
     * @param records
     * @param dateType
     * @return
     */
    List<PlantStatisticsInfoVO> getPlantAlarmNum(@Param("query") PlantStatisticsQueryDTO query,
                                                 @Param("plantUidArray") List<String> records,
                                                 @Param("dateType") String dateType
    );

    List<PlantInfoVO> getPlantStatusInfo(@Param("plantUid") List<String> plantUidArray);

    /**
     * 综合统计表格数据查询
     *
     * @param userInfo
     * @param query
     * @return
     */
    IntegrativeStatisticSheetVO getIntegrativeStatisticSheet(@Param("userInfo") RequireParamsDTO userInfo,
                                                             @Param("query") IntegrativeStatisticQueryDTO query);

    /**
     * 获取设备数量信息
     *
     * @param userInfo
     * @param query
     * @return
     */
    List<HashMap<String, String>> getDeviceNumInfo(@Param("userInfo") RequireParamsDTO userInfo,
                                                   @Param("query") IntegrativeStatisticQueryDTO query);


    /**
     * 查询指定区间发电量数据
     *
     * @param userInfo
     * @param query
     * @return
     */
    String getPeriodElectricity(@Param("userInfo") RequireParamsDTO userInfo,
                                @Param("query") IntegrativeStatisticQueryDTO query);

    /**
     * 综合统计图形数据查询
     *
     * @param userInfo 用户信息
     * @param query    查询条件
     * @return
     */
    List<IntegrativeStatisticChartVO> getIntegrativeStatisticChart(@Param("userInfo") RequireParamsDTO userInfo,
                                                                   @Param("query") IntegrativeStatisticQueryDTO query,
                                                                   @Param("isMonth") Boolean isMonth);

    /**
     * 获取近七天发电量数据
     *
     * @param userInfo
     * @param nums
     * @return
     */
    List<ChartElectricityInfoVO> getElectricityByNumDay(@Param("userInfo") RequireParamsDTO userInfo
            , @Param("nums") Integer nums);

    /**
     * 获取设备统计数量信息
     *
     * @param userInfo
     * @return
     */
    List<NumInfoVO> getDeviceStatusNumInfo(@Param("userInfo") RequireParamsDTO userInfo);

}
