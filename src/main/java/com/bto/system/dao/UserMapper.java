package com.bto.system.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.pojo.vo.UserInfoVO;
import com.bto.commons.pojo.entity.User;
import com.bto.commons.pojo.dto.UserQueryDTO;
import com.bto.commons.pojo.dto.UserDTO;
import com.bto.oauth.entity.UserLogin;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/20 14:40
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {
    /**
     * 用户列表多条件分页查询
     * @param userPage
     * @param query
     * @return
     */
    Page<UserInfoVO> getUserList(Page<UserInfoVO> userPage,
                                 @Param("query") UserQueryDTO query,
                                 @Param("userInfo") RequireParamsDTO userInfo);

    default User getByUsername(String username){
        return this.selectOne(new QueryWrapper<User>().eq("user_name", username));
    }

    /**
     * 查询用户信息
     */
    UserDTO selectUserByUserInfo(UserLogin query);

    /**
     * 根据用户Uid查询所属所有电站
     * @param userUid
     * @return
     */
    List<String> getPlantUidList(@Param("userUid") String userUid);
}
