package com.bto.system.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bto.commons.pojo.dto.CounterQuery;
import com.bto.commons.pojo.entity.Device;
import com.bto.commons.pojo.vo.CounterVO;
import com.bto.commons.pojo.vo.DeviceVO;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/9 14:39
 */
@Mapper
public interface DeviceMapper extends BaseMapper<Device> {


    List<DeviceVO> selectListByType(@Param("plantUid") String plantUid, @Param("userInfo") RequireParamsDTO userInfo, @Param("type") Integer type);

    IPage<CounterVO> getCounterPage(@Param("page") IPage<CounterVO> page, @Param("userInfo") RequireParamsDTO userInfo, @Param("query") CounterQuery query);

    List<DeviceVO> counterMonitor(@Param("typeList") List<String> typeList);
}
