package com.bto.system.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bto.commons.pojo.vo.RoleInfoVO;
import com.bto.commons.pojo.entity.Role;
import com.bto.commons.pojo.dto.RoleQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/18 17:33
 */
@Mapper
public interface RoleMapper extends BaseMapper<Role> {

    /**
     * 角色列表多条件分页查询
     * @param query
     */
    List<RoleInfoVO> getRoleList(@Param("query") RoleQueryDTO query);
}
