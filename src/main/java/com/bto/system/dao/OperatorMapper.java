package com.bto.system.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.pojo.dto.OperatorQueryDTO;
import com.bto.commons.pojo.entity.OperatorRealTimeInfo;
import com.bto.commons.pojo.vo.OperatorListVO;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface OperatorMapper extends BaseMapper<OperatorRealTimeInfo> {
    Page<OperatorListVO> getOperatorInfo(@Param("userInfo") RequireParamsDTO userInfo,@Param("query") OperatorQueryDTO query,@Param("page") Page<OperatorListVO> page);
}