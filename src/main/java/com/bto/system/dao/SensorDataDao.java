package com.bto.system.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bto.commons.pojo.entity.SensorDataEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* 传感器数据
*
* <AUTHOR> 
* @since 1.0.0 2024-06-17
*/
@Mapper
public interface SensorDataDao extends BaseMapper<SensorDataEntity> {

    List<SensorDataEntity> selectLatest(@Param("deviceIds") List<String> deviceIds);
}