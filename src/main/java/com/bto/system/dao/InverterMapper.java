package com.bto.system.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.pojo.dto.InverterListQueryDTO;
import com.bto.commons.pojo.entity.Device;
import com.bto.commons.pojo.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.LinkedHashMap;
import java.util.List;

@Mapper
public interface InverterMapper extends BaseMapper<Device> {

    /**
     * 查询指定日逆变器数据信息
     * @param tableName
     * @param inverterSN
     */
    List<InverterRealTimeInfoVO> getInverterDataByDay(@Param("tableName") String tableName,
                                                      @Param("inverterSN") String inverterSN);

    /**
     * 获取月-年-总发电量数据
     * @param plantUid
     * @return
     */
    List<ChartElectricityInfoVO> getElectricityList(@Param("date") String date,
                                                    @Param("plantUid") String plantUid);

    /**
     * 动态获取逆变器图表数据信息
     * @param pvModuleFields
     * @param inverterSN
     * @param tableName
     * @return
     */
    List<LinkedHashMap<String,Object>> getInverterInfoChart(@Param("pvModuleFields") List<String> pvModuleFields,
                                                            @Param("inverterSN") String inverterSN,
                                                            @Param("tableName") String tableName);

    /**
     * 获取逆变器线路字段集合
     * @param inverterSN
     * @return
     */
     List<String> getPvModuleFields(@Param("inverterSN") String inverterSN);

    /**
     * 根据 inverterSN 获取逆变器详细信息
     * @param inverterSN
     * @return
     */
    InverterDetailsVO getInverterDetails(@Param("inverterSN") String inverterSN);

    /**
     * 查询电站所有逆变器的各时间点功率之和
     * @param tableName
     * @param plantUid
     * @return
     */
    List<PlantPowerVO> getTotalPowerList(@Param("tableName")String tableName, @Param("plantUid")String plantUid);

    /**
     * 求电站当日峰值功率
     * @param tableName
     * @param inverterSnList
     * @return
     */
    String getPlantMaxPower(@Param("tableName") String tableName,
                          @Param("inverterSnList") List<String> inverterSnList);


    /**
     * 分页查询逆变器信息列表
     * @param page
     * @param query
     * @param userInfo
     * @return
     */
    Page<InverterInfoVO> selectInverterInfoList(@Param("page") Page<InverterInfoVO> page,
                                                @Param("userInfo") RequireParamsDTO userInfo,
                                                @Param("query") InverterListQueryDTO query);

    Integer getInverterRatedCapacityByModel(@Param("model") String model);
}
