package com.bto.system.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bto.commons.pojo.vo.ProjectInfoVO;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.pojo.entity.Project;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/18 15:46
 */
@Mapper
public interface ProjectMapper extends BaseMapper<Project> {
    /**
     * 查询项目专项信息
     * @return
     */
    List<ProjectInfoVO> getProjectSpecialInfoList(RequireParamsDTO userInfo);

    /**
     * /查询所有父级ID为指定父级ID的项目并排序，取出id最大的一条
     * @param pid
     * @return
     */
    Project selectProject(@Param("pid") String pid);

    /**
     * 查询项目父级id下所有项目id
     * @param projectID
     */
    List<ProjectInfoVO> getProjectIDListByPid(@Param("projectId") String projectId);
}
