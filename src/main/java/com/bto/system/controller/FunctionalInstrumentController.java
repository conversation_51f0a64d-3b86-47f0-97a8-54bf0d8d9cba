package com.bto.system.controller;

import com.bto.commons.pojo.dto.FunctionalInstrumentQuery;
import com.bto.commons.pojo.entity.FunctionalInstrumentEntity;
import com.bto.commons.pojo.vo.PageResult;
import com.bto.commons.response.Result;
import com.bto.system.service.FunctionalInstrumentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping("functionalInstrument")
@Api(tags = "多功能仪表数据")
@AllArgsConstructor
public class FunctionalInstrumentController {
    private final FunctionalInstrumentService functionalInstrumentService;

    @GetMapping("page")
    @ApiOperation("分页")
    public Result<PageResult<FunctionalInstrumentEntity>> page(@Valid FunctionalInstrumentQuery query) {
        PageResult<FunctionalInstrumentEntity> page = functionalInstrumentService.page(query);

        return Result.success(page);
    }

}