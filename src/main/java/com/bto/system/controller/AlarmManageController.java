package com.bto.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bto.commons.pojo.dto.PlantAlarmQueryDTO;
import com.bto.commons.pojo.entity.ViewPlantAlarm;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.response.Result;
import com.bto.system.utils.GlobalParamUtil;
import com.bto.system.service.AlarmManageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR> by zhb on 2024/5/9.
 */
@RestController
@RequestMapping("alarm")
@Api(tags = "电站告警模块")
public class AlarmManageController {

    @Autowired
    private AlarmManageService alarmManageService;
    @Autowired
    private GlobalParamUtil globalParamUtil;

    @PostMapping("list")
    @ApiOperation("查询电站告警信息列表")
    public Result<IPage<ViewPlantAlarm>> getPlantAlarmInfoList(@RequestBody PlantAlarmQueryDTO query) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        IPage<ViewPlantAlarm> result =  alarmManageService.getPlantAlarmInfoList(query,userInfo);
        return Result.success(result);
    }

    @PostMapping("exportInverterAndOperatorAlarmInfo")
    @ApiOperation(value = "导出逆变器与运维器告警列表信息--EXCEL表格")
    public void exportInverterAndOperatorAlarmInfo(@RequestBody PlantAlarmQueryDTO query, HttpServletResponse response) {
        alarmManageService.exportInverterAndOperatorAlarmInfo(query,response);
    }


}
