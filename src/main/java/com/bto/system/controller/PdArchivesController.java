package com.bto.system.controller;

import com.bto.commons.converter.vo.PdArchivesConvert;
import com.bto.commons.pojo.dto.PdArchivesDTO;
import com.bto.commons.pojo.dto.PdArchivesQuery;
import com.bto.commons.pojo.entity.PdArchivesEntity;
import com.bto.commons.pojo.vo.PageResult;
import com.bto.commons.pojo.vo.PdArchivesVO;
import com.bto.commons.pojo.vo.PlantWithArchiveVO;
import com.bto.commons.pojo.vo.PlantWithArchivesTree;
import com.bto.commons.response.Result;
import com.bto.system.service.PdArchivesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 配电室档案
 *
 * <AUTHOR>
 * @since 2024-06-03
 */
@RestController
@RequestMapping("pdArchives")
@Api(tags = "配电室档案")
@AllArgsConstructor
public class PdArchivesController {
    private final PdArchivesService pdArchivesService;

    @GetMapping("page")
    @ApiOperation("分页")
    public Result<PageResult<PdArchivesVO>> page(@Valid PdArchivesQuery query) {
        PageResult<PdArchivesVO> page = pdArchivesService.page(query);

        return Result.success(page);
    }

    @GetMapping("tree")
    @ApiOperation("电站-配电室")
    public Result<List<PlantWithArchivesTree>> tree(@Valid PdArchivesQuery query) {
        List<PlantWithArchivesTree> result = pdArchivesService.tree(query);

        return Result.success(result);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    public Result<PdArchivesVO> get(@PathVariable("id") Long id) {
        PdArchivesEntity entity = pdArchivesService.getById(id);

        return Result.success(PdArchivesConvert.INSTANCE.convert(entity));
    }

    @PostMapping
    @ApiOperation("保存")
    public Result<String> save(@RequestBody PdArchivesDTO dto) {
        pdArchivesService.save(dto);

        return Result.success();
    }

    @PutMapping
    @ApiOperation("修改")
    public Result<String> update(@RequestBody @Valid PdArchivesVO vo) {
        pdArchivesService.update(vo);

        return Result.success();
    }

    @DeleteMapping
    @ApiOperation("删除")
    public Result<String> delete(@RequestBody List<Long> idList) {
        pdArchivesService.delete(idList);

        return Result.success();
    }
}