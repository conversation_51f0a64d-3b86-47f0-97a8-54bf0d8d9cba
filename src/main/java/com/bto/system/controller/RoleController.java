package com.bto.system.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.pojo.vo.RoleInfoVO;
import com.bto.commons.pojo.dto.RoleInfoDTO;
import com.bto.commons.pojo.dto.RoleQueryDTO;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import com.bto.system.service.RoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2023/5/18 16:57
 */
@RestController
@RequestMapping("role")
@Api(tags = "角色管理模块")
public class RoleController {
    @Autowired
    private RoleService roleService;
    @PostMapping("/getRoleList")
    @ApiOperation("角色列表多条件分页查询")
    public Result getRoleList(@RequestBody RoleQueryDTO query) {
        Page<RoleInfoVO> page = roleService.getRoleList(query);
        if (page.getTotal() > 0) {
            return Result.success(page);
        } else {
            return Result.instance(ResultEnum.NO_CONTENT.getCode(), ResultEnum.NO_CONTENT.getMessage(), page);
        }
    }

    @PostMapping("addRole")
    @ApiOperation("新增角色")
    public Result addRole(@RequestBody RoleInfoDTO roleInfoDTO){
        Integer result = roleService.addRole(roleInfoDTO);
        if (result > 0) {
            return Result.success(ResultEnum.OPERATION_SUCCESS.getMessage());
        } else {
            return Result.instance(ResultEnum.SYSTEM_RUNTIME_FAILED.getCode(), ResultEnum.SYSTEM_RUNTIME_FAILED.getMessage(),"");
        }
    }

    @PutMapping("editRole")
    @ApiOperation("修改角色")
    public Result editRole(@RequestBody RoleInfoDTO roleInfoDTO){
        Integer result = roleService.editRole(roleInfoDTO);
        if (result > 0) {
            return Result.success(ResultEnum.OPERATION_SUCCESS.getMessage());
        } else {
            return Result.instance(ResultEnum.SYSTEM_RUNTIME_FAILED.getCode(), ResultEnum.SYSTEM_RUNTIME_FAILED.getMessage(),"");
        }
    }

    @DeleteMapping ("deleteRole/{roleID}")
    @ApiOperation("删除角色")
    public Result deleteRole(@PathVariable String roleID){
        Integer result = roleService.deleteRole(roleID);
        if (result > 0) {
            return Result.success(ResultEnum.OPERATION_SUCCESS.getMessage());
        } else {
            return Result.instance(ResultEnum.SYSTEM_RUNTIME_FAILED.getCode(), ResultEnum.SYSTEM_RUNTIME_FAILED.getMessage(),"");
        }
    }




}
