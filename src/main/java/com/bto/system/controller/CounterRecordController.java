package com.bto.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bto.commons.pojo.dto.CounterQuery;
import com.bto.commons.pojo.entity.CounterRecordEntity;
import com.bto.commons.pojo.vo.CounterRecordVO;
import com.bto.commons.response.Result;
import com.bto.system.service.CounterRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 配电柜数据
 *
 * <AUTHOR>
 * @since 2024-07-24
 */
@RestController
@RequestMapping("counterRecord")
@Api(tags = "配电柜数据记录管理")
@AllArgsConstructor
public class CounterRecordController {
    private final CounterRecordService counterRecordService;

    @GetMapping("page")
    @ApiOperation("分页")
    public Result<IPage<CounterRecordVO>> page(@Valid CounterQuery query) {

        return Result.success(counterRecordService.page(query));
    }

    // @GetMapping("{id}")
    // @ApiOperation("信息")
    // public Result<CounterRecordVO> get(@PathVariable("id") Long id) {
    //     CounterRecordEntity entity = counterService.getById(id);
    //
    //     return Result.success(CounterConvert.INSTANCE.convert(entity));
    // }
    //
    // @PostMapping
    // @ApiOperation("保存")
    // public Result<String> save(@RequestBody CounterRecordVO vo) {
    //     counterService.save(vo);
    //
    //     return Result.success();
    // }
    //
    // @PutMapping
    // @ApiOperation("修改")
    // public Result<String> update(@RequestBody @Valid CounterRecordVO vo) {
    //     counterService.update(vo);
    //
    //     return Result.success();
    // }
    //
    // @DeleteMapping
    // @ApiOperation("删除")
    // public Result<String> delete(@RequestBody List<Long> idList) {
    //     counterService.delete(idList);
    //     return Result.success();
    // }
}