package com.bto.system.controller;

import com.bto.commons.converter.vo.SensorDataConvert;
import com.bto.commons.pojo.dto.PdArchivesQuery;
import com.bto.commons.pojo.dto.SensorDataQuery;
import com.bto.commons.pojo.entity.SensorDataEntity;
import com.bto.commons.pojo.vo.PlantWithArchivesTree;
import com.bto.commons.pojo.vo.PlantWithSensorTree;
import com.bto.commons.pojo.vo.SensorDataVO;
import com.bto.commons.response.Result;
import com.bto.system.service.SensorDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 传感器数据
 *
 * <AUTHOR>
 * @since 1.0.0 2024-06-17
 */
@RestController
@RequestMapping("sensor")
@Api(tags = "传感器数据")
@AllArgsConstructor
public class SensorDataController {
    private final SensorDataService sensorDataService;

    @GetMapping("tree")
    @ApiOperation("电站-传感器(不用传参，查所有)")
    public Result<List<PlantWithSensorTree>> tree(@Valid SensorDataQuery query) {
        List<PlantWithSensorTree> result = sensorDataService.tree(query);

        return Result.success(result);
    }


    @GetMapping("{id}")
    @ApiOperation("信息")
    public Result<SensorDataVO> get(@PathVariable("id") Long id) {
        SensorDataEntity entity = sensorDataService.getById(id);

        return Result.success(SensorDataConvert.INSTANCE.convert(entity));
    }

    @PostMapping
    @ApiOperation("保存")
    public Result<String> save(@RequestBody SensorDataVO vo) {
        sensorDataService.save(vo);

        return Result.success();
    }

    @PutMapping
    @ApiOperation("修改")
    public Result<String> update(@RequestBody @Valid SensorDataVO vo) {
        sensorDataService.update(vo);

        return Result.success();
    }

    @DeleteMapping
    @ApiOperation("删除")
    public Result<String> delete(@RequestBody List<Long> idList) {
        sensorDataService.delete(idList);

        return Result.success();
    }
}