package com.bto.system.controller;

import com.bto.commons.response.Result;
import com.bto.system.service.WeatherStationService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/9/21 16:40
 */
@RestController
@RequestMapping("weatherStation")
@Api(tags = "气象站模块")
public class WeatherStationController {
    @Autowired
    private WeatherStationService weatherStationService;


    @GetMapping("list")
    public Result<Object> getCustomStation() {
       return weatherStationService.getCustomer();
    }
}
