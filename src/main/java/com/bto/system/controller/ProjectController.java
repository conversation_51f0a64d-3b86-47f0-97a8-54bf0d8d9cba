package com.bto.system.controller;

import com.bto.commons.pojo.vo.ProjectInfoVO;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.pojo.dto.ProjectInfoDTO;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import com.bto.system.utils.GlobalParamUtil;
import com.bto.system.service.ProjectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/18 14:48
 */
@Api(tags = "项目管理模块")
@RequestMapping("project")
@RestController
public class ProjectController {
    @Autowired
    private ProjectService projectService;
    @Autowired
    private GlobalParamUtil globalParamUtil;
    @GetMapping("/getProjectSpecialInfo")
    @ApiOperation("查询项目专项信息")
    public Result<List<ProjectInfoVO>> getProjectSpecialInfo(){
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        List<ProjectInfoVO> result = projectService.getProjectSpecialInfo(userInfo);
        if (result.size()>0){
            return Result.success(result);
        }else {
            return Result.instance(ResultEnum.NO_CONTENT.getCode(),
                    ResultEnum.NO_CONTENT.getMessage(), result);
        }
    }
    @PostMapping("addProject")
    @ApiOperation("新增项目")
    public Result addProject(@RequestBody ProjectInfoDTO projectInfo) {
        Integer result = projectService.addProject(projectInfo);
        if (result > 0) {
            return Result.success(ResultEnum.OPERATION_SUCCESS.getMessage());
        } else {
            return Result.instance(ResultEnum.SYSTEM_RUNTIME_FAILED.getCode(), ResultEnum.SYSTEM_RUNTIME_FAILED.getMessage(),"");
        }
    }

    @PutMapping("editProject")
    @ApiOperation("修改项目名称")
    public Result editProject(@RequestBody ProjectInfoDTO projectInfo) {
        Integer result = projectService.editProject(projectInfo);
        if (result > 0) {
            return Result.success(ResultEnum.OPERATION_SUCCESS.getMessage());
        } else {
            return Result.instance(ResultEnum.SYSTEM_RUNTIME_FAILED.getCode(), ResultEnum.SYSTEM_RUNTIME_FAILED.getMessage(),"");
        }
    }

    @DeleteMapping("deleteProject/{projectID}")
    @ApiOperation("删除项目")
    public Result deleteProject(@PathVariable Integer projectID) {
        Integer result = projectService.deleteProject(projectID);
        if (result > 0) {
            return Result.success(ResultEnum.OPERATION_SUCCESS.getMessage());
        } else {
            return Result.instance(ResultEnum.SYSTEM_RUNTIME_FAILED.getCode(), ResultEnum.SYSTEM_RUNTIME_FAILED.getMessage(),"");
        }
    }

    @GetMapping("/getProjectIDList/{projectID}")
    @ApiOperation("查询项目父级id下所有项目id")
    public Result<List<String>> getProjectIDListByPid(@PathVariable("projectID") String projectID) {
        List<String> result = projectService.getProjectIDListByPid(projectID);
        if (result.size()>0){
            return Result.success(result);
        }else {
            return Result.instance(ResultEnum.NO_CONTENT.getCode(),
                    ResultEnum.NO_CONTENT.getMessage(), result);
        }
    }

    @GetMapping("/getProjectSpecialByPid/{projectId}")
    @ApiOperation("根据父id查询所有子项目信息")
    public Result<List<ProjectInfoVO>> getProjectSpecialByPid(@PathVariable("projectId") String projectId) {
        List<ProjectInfoVO> result = projectService.getProjectSpecialByPid(projectId);
        if (result.isEmpty()) {
            return Result.instance(ResultEnum.NO_CONTENT, result);
        }
        return Result.success(result);
    }
}
