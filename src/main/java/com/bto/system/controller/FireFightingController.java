package com.bto.system.controller;

import com.bto.commons.pojo.dto.FireFightingQuery;
import com.bto.commons.pojo.entity.FireFightingEntity;
import com.bto.commons.pojo.vo.PageResult;
import com.bto.commons.response.Result;
import com.bto.system.service.FireFightingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 消防联动
 *
 * <AUTHOR>
 * @since 1.0.0 2024-09-27
 */
@RestController
@RequestMapping("fireFighting")
@Api(tags = "消防联动管理")
@AllArgsConstructor
public class FireFightingController {
    private final FireFightingService fireFightingService;

    @GetMapping("page")
    @ApiOperation("分页")
    public Result<PageResult<FireFightingEntity>> page(@Valid FireFightingQuery query) {
        PageResult<FireFightingEntity> page = fireFightingService.page(query);

        return Result.success(page);
    }
}