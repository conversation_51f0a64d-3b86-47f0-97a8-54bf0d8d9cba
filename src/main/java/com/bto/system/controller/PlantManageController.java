package com.bto.system.controller;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.*;
import com.bto.commons.pojo.vo.*;
import com.bto.commons.pojo.vo.PlantVO;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.utils.BusinessCalculateUtil;
import com.bto.system.utils.GlobalParamUtil;
import com.bto.system.service.PlantService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/9 11:24
 */
@RestController
@RequestMapping("plantManage")
@Api(tags = "电站管理")
public class PlantManageController {
    @Autowired
    private GlobalParamUtil globalParamUtil;
    @Autowired
    private PlantService plantService;
    @PostMapping("/getPlantList")
    @ApiOperation("获取电站列表")
    public Result<IPage<PlantInfoVO>> getPlantList(@RequestBody PlantQueryDTO query) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        IPage<PlantInfoVO> result = plantService.getPlantList(query, userInfo);
        if (result.getTotal() > 0) {
            List<PlantInfoVO> records = result.getRecords();
            records.forEach(
                    record -> {
                        String todayElectricity = record.getTodayElectricity();
                        String yearElectricity = record.getYearElectricity();
                        String plantCapacity = record.getPlantCapacity();
                        record.setDailyEfficiencyPerHour(BusinessCalculateUtil.getRealEfficiencyPerHours(todayElectricity, plantCapacity));
                        record.setYearlyEfficiencyPerHour(BusinessCalculateUtil.getRealEfficiencyPerHours(yearElectricity, plantCapacity));
                    }
            );
            result.setRecords(records);
            return Result.success(result);
        } else {
            return Result.instance(ResultEnum.NO_CONTENT, result);
        }
    }

    @PostMapping("/selectAll")
    @ApiOperation("获取所有电站id和名称")
    public Result<List<PlantInfoVO>> selectAll() {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        List<PlantInfoVO> list = plantService.selectAll(userInfo);
        if (list.size() == 0) {
            return Result.instance(ResultEnum.NO_CONTENT, list);
        }

        return Result.success(list);
    }
    @GetMapping("/plantDetail/{plantUid}")
    @ApiOperation(value = "根据电站编号查询电站详情信息")
    public Result<PlantVO> getPlantDetail(@PathVariable String plantUid) {
        PlantVO result = plantService.getPlantDetail(plantUid);
        return Result.success(result);
    }

    @ApiOperation("获取电站数量信息")
    @PostMapping("getPlantInfoCoordinateOfArea")
    public Result<List<AreaCoordinateInfoVO>> getPlantInfoCoordinateOfArea(@RequestBody PlantAddressDTO plantAddressDTO){
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        List<AreaCoordinateInfoVO> result = plantService.getPlantInfoCoordinateOfArea(plantAddressDTO,userInfo);
        if(result.size()>0){
            return Result.success(result);
        }else {
            return Result.instance(ResultEnum.NO_CONTENT.getCode(), ResultEnum.NO_CONTENT.getMessage(),result);
        }
    }

    @ApiOperation("站点轮播")
    @GetMapping("getPlantCarousel")
    @ApiImplicitParams({
            //参数效验
            @ApiImplicitParam(name = "startIndex", value = "开始索引", required = true, paramType = "query",defaultValue = "0"),
            @ApiImplicitParam(name = "endIndex", value = "结束索引", required = true, paramType = "query",defaultValue = "50")
    })
    public Result<PlantCarouselDTO> getPlantCarousel(@RequestParam("startIndex") String startIndex, @RequestParam("endIndex") String endIndex){
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        PlantCarouselDTO plantCarousel = plantService.getPlantCarousel(startIndex, endIndex,userInfo);
        if(plantCarousel.getRecords().size()>0){
            return Result.success(plantCarousel);
        }else {
            return Result.instance(ResultEnum.NO_CONTENT.getCode(), ResultEnum.NO_CONTENT.getMessage(),plantCarousel);
        }
    }

    @ApiOperation("发电量排名分页")
    @PostMapping("getPlantElectricityRank")
    public Result<IPage<WorkEfficiencyVO>> getPlantElectricityRank(@RequestBody PowerPlantInfoQueryDTO query) {
        return Result.success(plantService.getPlantElectricityRank(query));
    }

    @PostMapping(value="/exportPlantList",produces = "application/octet-stream")
    @ApiOperation("电站列表数据-Excel报表导出")
    public void exportPlantList(@RequestBody PlantExportFileDTO query, HttpServletResponse response) throws IOException, IllegalAccessException {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        List<PlantDigestVO> plantDigestVOList = plantService.exportPlantList(userInfo, query);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + "电站列表" + ".xlsx");
        try {
            EasyExcel.write(response.getOutputStream(), PlantDigestVO.class).sheet("电站列表").doWrite(plantDigestVOList);
        }catch(Exception e){
            throw new BusinessException(ResultEnum.SYSTEM_RUNTIME_FAILED);
        }
    }

}
