package com.bto.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bto.commons.enums.DeviceType;
import com.bto.commons.pojo.dto.AddDeviceDTO;
import com.bto.commons.pojo.dto.CounterQuery;
import com.bto.commons.pojo.vo.CounterVO;
import com.bto.commons.pojo.vo.DeviceVO;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import com.bto.system.service.DeviceService;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* 配电柜数据
*
* <AUTHOR> 
* @since  2024-07-24
*/
@RestController
@RequestMapping("counter")
@Api(tags = "配电柜管理")
@AllArgsConstructor
public class CounterController {
    private final DeviceService deviceService;

   @PostMapping
    @ApiOperation("添加配电柜")
    public Result addDevice(@RequestBody AddDeviceDTO addDeviceDTO) {
        addDeviceDTO.setDeviceType(DeviceType.COUNTER.getCode());
        deviceService.addDevice(addDeviceDTO);
        return Result.success(ResultEnum.OPERATION_SUCCESS);
    }

    @DeleteMapping
    @ApiOperation("根据设备id删除配电柜")
    public Result deleteDevice(@RequestParam List<String> ids) {
        deviceService.deleteDevice(ids);
        return Result.success(ResultEnum.OPERATION_SUCCESS);
    }

    @GetMapping("page")
    @ApiOperation("分页")
    public Result<IPage<CounterVO>> page(@Valid CounterQuery query){
        IPage<CounterVO> page = deviceService.getCounterPage(query);

        return Result.success(page);
    }
    @PutMapping
    @ApiOperation("修改配电柜")
    public Result<String> update(@RequestBody @Valid AddDeviceDTO dto){
        dto.setDeviceType(DeviceType.COUNTER.getCode());
        deviceService.update(dto);

        return Result.success();
    }
    @GetMapping("counterMonitor")
    @ApiOperation("配电柜监控")

    public Result<List<DeviceVO>> counterMonitor(@RequestParam(required = false)
                                                 @ApiParam(value = "1:逆变器、2:运维器、3:电表,4：气象站,10、配电房,11、配电柜，12、温湿度、烟感采集器",defaultValue = "1,10,11,12")
                                                 List<String> typeList){
        List<DeviceVO> list = deviceService.counterMonitor(typeList);

        return Result.success(list);
    }

}