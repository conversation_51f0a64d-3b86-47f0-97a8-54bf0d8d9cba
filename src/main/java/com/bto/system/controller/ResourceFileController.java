package com.bto.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.AttachmentQuery;
import com.bto.commons.pojo.dto.StorageDTO;
import com.bto.commons.pojo.vo.AttachmentVO;
import com.bto.commons.pojo.vo.SysFileUploadVO;
import com.bto.commons.response.Result;
import com.bto.system.service.ResourceFileService;
import com.bto.system.storage.service.StorageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;


/**
 * <AUTHOR>
 * @date 2023/6/17 11:40
 */
@RestController
@Api(tags = "资源管理")
@RequestMapping("/resource")
public class ResourceFileController {
    @Autowired
    private ResourceFileService resourceFileService;

    @Autowired
    private StorageService storageService;

    @PostMapping("uploadScreenLogo/{projectId}")
    @ApiOperation(value = "屏幕logo图片上传")
    public Result<StorageDTO> uploadScreenLogo(@RequestParam("file") MultipartFile imgFile,
                                               @PathVariable String projectId) throws IOException {
        StorageDTO storageDTO = resourceFileService.uploadScreenLogo(imgFile, projectId);
        return Result.success(storageDTO);
    }
    @PostMapping("uploadProjectLogo/{projectId}")
    @ApiOperation(value = "项目logo图片上传")
    public Result<StorageDTO> uploadProjectLogo(@RequestParam("file") MultipartFile imgFile,
                                                @PathVariable String projectId) throws IOException {
        StorageDTO storageDTO = resourceFileService.uploadProjectLogo(imgFile, projectId);
        return Result.success(storageDTO);
    }

    @PostMapping("uploadUserAvatar/{userUid}")
    @ApiOperation(value = "用户头像上传")
    public Result<StorageDTO> uploadUserAvatar(@RequestParam("file") MultipartFile imgFile,
                                               @PathVariable String userUid) throws IOException {
        StorageDTO storageDTO = resourceFileService.uploadUserAvatar(imgFile, userUid);
        return Result.success(storageDTO);
    }

    @PostMapping("getAttachmentByPage")
    @ApiOperation(value = "分页查询附件列表")
    public Result<IPage<AttachmentVO>> getAttachmentByPage(@RequestBody AttachmentQuery query) {
        IPage<AttachmentVO> page = resourceFileService.getAttachmentByPage(query);
        return Result.success(page);
    }

    @PostMapping("uploadWorkOrderIssuePhoto")
    @Operation(summary = "上传工单报修照片")
    public Result uploadWorkOrderIssuePhoto(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return Result.failed("请选择需要上传的文件");
        }
        StorageDTO storageDTO = resourceFileService.uploadWorkOrderIssuePhoto(file);
        return Result.success(storageDTO);
    }

    @PostMapping("upload")
    @Operation(summary = "上传")
    public Result<SysFileUploadVO> upload(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new BusinessException("请选择需要上传的文件");
        }

        // 上传路径
        String path = storageService.getPath(file.getOriginalFilename());
        // 上传文件
        String url = storageService.upload(file.getBytes(), path);

        SysFileUploadVO vo = new SysFileUploadVO();
        vo.setUrl(url);
        vo.setSize(file.getSize());
        vo.setName(file.getOriginalFilename());
        vo.setPlatform(storageService.properties.getConfig().getType().name());

        return Result.success(vo);
    }


}
