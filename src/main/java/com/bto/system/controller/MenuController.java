package com.bto.system.controller;

import com.bto.commons.pojo.vo.MenuInfoVO;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.pojo.dto.MenuInfoDTO;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import com.bto.system.utils.GlobalParamUtil;
import com.bto.system.service.MenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/18 18:07
 */
@RestController
@RequestMapping("menu")
@Api(tags = "菜单管理模块")
public class MenuController {
    @Autowired
    private MenuService menuService;
    @Autowired
    private GlobalParamUtil globalParamUtil;
    @GetMapping("/getMenuList")
    @ApiOperation("菜单列表查询")
    public Result getMenuList() {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        List<MenuInfoVO> result = menuService.getMenuList(userInfo);
        if (result.size() > 0) {
            return Result.success(result);
        } else {
            return Result.instance(ResultEnum.NO_CONTENT.getCode(), ResultEnum.NO_CONTENT.getMessage(), result);
        }
    }

    @PostMapping("/addMenu")
    @ApiOperation("新增菜单")
    public Result addMenu(@RequestBody MenuInfoDTO menuInfo) {
        Integer result = menuService.addMenu(menuInfo);
        if (result > 0) {
            return Result.success(result);
        } else {
            return Result.instance(ResultEnum.NO_CONTENT.getCode(), ResultEnum.NO_CONTENT.getMessage(), result);
        }
    }

    @PostMapping("/editMenu")
    @ApiOperation("修改菜单")
    public Result editMenu(@RequestBody MenuInfoDTO menuInfo) {
        Integer result = menuService.editMenu(menuInfo);
        if (result > 0) {
            return Result.success(result);
        } else {
            return Result.instance(ResultEnum.NO_CONTENT.getCode(), ResultEnum.NO_CONTENT.getMessage(), result);
        }
    }

    @DeleteMapping("/deleteMenu/{menUid}")
    @ApiOperation("删除菜单")
    public Result deleteMenu(@PathVariable String menUid) {
        Integer result = menuService.deleteMenu(menUid);
        if (result > 0) {
            return Result.success(result);
        } else {
            return Result.instance(ResultEnum.NO_CONTENT.getCode(), ResultEnum.NO_CONTENT.getMessage(), result);
        }
    }




}
