package com.bto.system.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.constant.Constant;
import com.bto.commons.pojo.dto.AddDeviceDTO;
import com.bto.commons.pojo.dto.InverterListQueryDTO;
import com.bto.commons.pojo.dto.OperatorQueryDTO;
import com.bto.commons.pojo.entity.OperatorRealTimeInfo;
import com.bto.commons.pojo.vo.*;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import com.bto.system.utils.GlobalParamUtil;
import com.bto.system.service.DeviceService;
import com.bto.system.service.InverterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/9 14:31
 */
@RestController
@RequestMapping("deviceManage")
@Api(tags = "设备管理")
public class DeviceManageController {
    @Autowired
    private DeviceService deviceService;

    @Autowired
    private InverterService inverterService;

    @Resource
    private GlobalParamUtil globalParamUtil;
    @GetMapping("inverterChartInfo/{plantUid}")
    @ApiOperation("获取逆变器日/月/年/总图表数据")
    public Result getInverterChartInfo(@RequestParam(required = false) String date, @PathVariable String plantUid) {
        if (date.length() == Constant.DAY_LENGTH) {
            List<InverterChartByDayVO> result = inverterService.getInverterChartInfo(date, plantUid);
            return CollUtil.isNotEmpty(result)?Result.success(result):Result.instance(ResultEnum.NO_CONTENT, result);
        } else {
            List<InverterChartByOtherVO> result = inverterService.getInverterChartInfo(date, plantUid);
            return CollUtil.isNotEmpty(result)?Result.success(result):Result.instance(ResultEnum.NO_CONTENT, result);
        }
    }
    @ApiOperation("通过电站Uid查询各点状态")
    @GetMapping("/operator/getPointStatus/{plantUid}")
    public Result<OperatorRealTimeInfo> getPointStatusByPlantUid(@PathVariable String plantUid) {
        OperatorRealTimeInfo result = deviceService.getPointStatusByPlantUid(plantUid);
        if (ObjectUtil.isNull(result)) {
            return Result.instance(ResultEnum.NO_CONTENT, new OperatorRealTimeInfo());
        }
        return Result.success(result);
    }

    @GetMapping("getPvNums/{inverterSN}")
    @ApiOperation("获取逆变器PV路数")
    public Result getPvNums(@PathVariable String inverterSN) {
        HashMap<String, Integer> result = inverterService.getPvNums(inverterSN);
        if (CollUtil.isNotEmpty(result)) {
            return Result.success(result);
        } else {
            return Result.instance(ResultEnum.NO_CONTENT.getCode(),
                    ResultEnum.NO_CONTENT.getMessage(), result);
        }
    }

    @GetMapping("/inverterDetails/{inverterSN}")
    @ApiOperation("逆变器详情-设备详细信息")
    public Result<InverterDetailsVO> getInverterDetails(@PathVariable String inverterSN) {
        InverterDetailsVO inverterDetails = inverterService.getInverterDetails(inverterSN);
        return Result.success(inverterDetails);
    }


    @ApiOperation("逆变器信息列表")
    @PostMapping("/deviceManage/inverterInfoList")
    public Result<Page<InverterInfoVO>> inverterInfoList(@RequestBody InverterListQueryDTO query){
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        Page<InverterInfoVO> result = inverterService.InverterInfoList(userInfo, query);
        if (result.getTotal()>0){
            return Result.success(result);
        }
        return Result.instance(ResultEnum.NO_CONTENT,result);
    }

    @GetMapping("/inverterInfoChart/{inverterSN}")
    @ApiOperation("逆变器详情-图表曲线数据信息")
    public Result<List<LinkedHashMap<String, Object>>> getInverterInfoChart(@RequestParam("date") String date, @PathVariable String inverterSN) {
        List<LinkedHashMap<String, Object>> inverterInfoChart = inverterService.getInverterInfoChart(date, inverterSN);
        if (CollUtil.isNotEmpty(inverterInfoChart)) {
            return Result.success(inverterInfoChart);
        } else {
            return Result.instance(ResultEnum.NO_CONTENT.getCode(), ResultEnum.NO_CONTENT.getMessage(), inverterInfoChart);
        }

    }

    @PostMapping("/operator/list")
    @ApiOperation("运维器信息列表")
    public Result<Page<OperatorListVO>> getOperatorInfo(@RequestBody OperatorQueryDTO query) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        Page<OperatorListVO> result = deviceService.getOperatorInfo(userInfo, query);
        if (result.getTotal() > 0) {
            return Result.success(result);
        }
        return Result.instance(ResultEnum.NO_CONTENT, result);
    }

    /**
     * 获取逆变器实时数据
     *
     * @param query
     * @return
     */
    @PostMapping("/deviceId/realTime")
    @ApiOperation(value = "获取逆变器实时数据")
    public Result getInverterRealTimeData(@RequestBody InverterRealTimeQueryVO query) {

        HashMap<String, Object> inverterRealTimeData = inverterService.getInverterRealTimeData(query);
        Long total = (Long) inverterRealTimeData.get("total");
        if (total > 0) {
            return Result.success(inverterRealTimeData);
        } else {
            return Result.instance(ResultEnum.NO_CONTENT.getCode(), ResultEnum.NO_CONTENT.getMessage(), inverterRealTimeData);
        }
    }

}
