package com.bto.system.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.pojo.dto.RoleUserDTO;
import com.bto.commons.pojo.dto.UserInfoDTO;
import com.bto.commons.pojo.dto.UserPasswordDTO;
import com.bto.commons.pojo.dto.UserQueryDTO;
import com.bto.commons.pojo.entity.User;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.pojo.vo.UserInfoVO;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import com.bto.system.utils.GlobalParamUtil;
import com.bto.system.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/4/20 16:35
 */
@RestController
@RequestMapping("user")
@Api(tags = "用户管理模块")
public class UserController {
    @Autowired
    private UserService userService;
    @Autowired
    private GlobalParamUtil globalParamUtil;

    @PostMapping("/getUserList")
    @ApiOperation("用户列表多条件分页查询")
    public Result getUserList(@RequestBody UserQueryDTO query) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        query.setOrder(StrUtil.isEmpty(query.getOrder()) ? "createTime" : query.getOrder());
        Page<UserInfoVO> page = userService.getUserList(query, userInfo);
        if (page.getTotal() > 0) {
            return Result.success(page);
        } else {
            return Result.instance(ResultEnum.NO_CONTENT.getCode(), ResultEnum.NO_CONTENT.getMessage(), page);
        }
    }

    @GetMapping("/getUserInfo/{userUid}")
    @ApiOperation("根据用户id查询用户信息")
    public Result<User> getUserInfoByUserUid(@PathVariable String userUid) {
        User user = userService.getUserInfoByUserUid(userUid);
        return ObjectUtil.isNotEmpty(user) ? Result.success(user) : Result.instance(ResultEnum.NO_CONTENT, user);
    }

    @PostMapping("addUser")
    @ApiOperation("新增用户")
    public Result addUser(@RequestBody UserInfoDTO userInfo) {
        Integer result = userService.addUser(userInfo);
        if (result > 0) {
            return Result.success(ResultEnum.OPERATION_SUCCESS.getMessage());
        } else {
            return Result.instance(ResultEnum.SYSTEM_RUNTIME_FAILED.getCode(), ResultEnum.SYSTEM_RUNTIME_FAILED.getMessage(), "");
        }
    }

    @PutMapping("editUser")
    @ApiOperation("修改用户")
    public Result editUser(@RequestBody UserInfoDTO userInfo) {
        Integer result = userService.editUser(userInfo);
        if (result > 0) {
            return Result.success(ResultEnum.OPERATION_SUCCESS.getMessage());
        } else {
            return Result.instance(ResultEnum.SYSTEM_RUNTIME_FAILED.getCode(), ResultEnum.SYSTEM_RUNTIME_FAILED.getMessage(), "");
        }
    }

    @PostMapping("deleteUser/{userUid}")
    @ApiOperation("删除用户")
    public Result deleteUser(@PathVariable String userUid) {
        Integer result = userService.deleteUser(userUid);
        if (result > 0) {
            return Result.success(ResultEnum.OPERATION_SUCCESS.getMessage());
        } else {
            return Result.instance(ResultEnum.SYSTEM_RUNTIME_FAILED.getCode(), ResultEnum.SYSTEM_RUNTIME_FAILED.getMessage(), "");
        }
    }


    @RequestMapping(value = "/userNameAndPhone/{userUid}", method = RequestMethod.GET)
    public Result getUserNameAndPhoneByUserUid(@PathVariable("userUid") String userUid) {
        Map<String, String> userNameAndPhoneMap = userService.getUserNameAndPhoneByUserUid(userUid);
        if (userNameAndPhoneMap == null) {
            return Result.instance(ResultEnum.NO_CONTENT.getCode(), ResultEnum.NO_CONTENT.getMessage(), "没有该用户信息");
        }
        return Result.success(userNameAndPhoneMap);
    }

    @GetMapping("getPlantUidList/{userUid}")
    @ApiOperation("根据用户Uid查询所属所有电站")
    public Result<List<String>> getPlantUidListByUser(@PathVariable("userUid") String userUid) {
        List<String> result = userService.getPlantUidList(userUid);
        if (result.size() > 0) {
            return Result.success(result);
        } else {
            return Result.instance(ResultEnum.NO_CONTENT.getCode(), ResultEnum.NO_CONTENT.getMessage(), result);
        }
    }

    @PutMapping("updatePassword")
    @ApiOperation("用户密码修改")
    public Result updatePassword(@RequestBody UserPasswordDTO userPasswordDTO) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        Integer result = userService.updatePassword(userPasswordDTO, userInfo);
        if (result > 0) {
            return Result.success(ResultEnum.OPERATION_SUCCESS.getMessage());
        } else {
            return Result.instance(ResultEnum.SYSTEM_RUNTIME_FAILED.getCode(), ResultEnum.SYSTEM_RUNTIME_FAILED.getMessage(), "");
        }
    }

    @PostMapping("import")
    @Operation(summary = "导入用户")
    public Result importExcel(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return Result.failed("请选择需要上传的文件");
        }
        return userService.importByExcel(file, SecureUtil.md5("123456"));
    }

    @PostMapping("assignRoles")
    @ApiOperation("分配角色给用户列表")
    public Result<String> assignRolesToUsers(@RequestBody RoleUserDTO roleUserDTO) {
        userService.assignRoles(roleUserDTO);

        return Result.success(ResultEnum.OPERATION_SUCCESS.getMessage());
    }

    @GetMapping("export")
    @Operation(summary = "导出用户")
    public void export(UserQueryDTO query, HttpServletResponse response) {
        userService.export(query, response);
    }

    @GetMapping("layout")
    @Operation(summary = "查询布局")
    public String getLayoutByUser() {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        String userUid = userInfo.getUserUid();
        return userService.getLayoutByUser(userUid);
    }


    @PutMapping("layout")
    @Operation(summary = "修改布局")
    public Result updateLayoutByUser(@RequestBody String layout) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        String userUid = userInfo.getUserUid();
        return userService.updateLayoutByUser(userUid, layout);
    }
}
