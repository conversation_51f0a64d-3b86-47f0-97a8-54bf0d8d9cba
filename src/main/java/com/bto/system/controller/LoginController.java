package com.bto.system.controller;

import cn.hutool.core.collection.CollUtil;
import com.bto.commons.constant.OauthConstants;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.UserDTO;
import com.bto.commons.pojo.vo.MenuInfoVO;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import com.bto.oauth.entity.ClientInfo;
import com.bto.oauth.entity.OauthResponse;
import com.bto.oauth.entity.UserLogin;
import com.bto.oauth.service.CustomClientService;
import com.bto.system.utils.RedisUtil;
import com.bto.system.service.ClientService;
import com.bto.system.service.RoleMenuService;
import com.bto.system.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.oauth2.provider.ClientDetails;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.ClientRegistrationException;
import org.springframework.security.oauth2.provider.endpoint.TokenEndpoint;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/5/25 15:23
 */
@RestController
@RequestMapping("/oauth")
@Api(tags = "登录模块")
@Validated
public class LoginController {
    @Autowired
    private TokenEndpoint tokenEndpoint;
    @Autowired
    private UserService userService;
    @Autowired
    private ClientService clientService;
    @Autowired
    private RoleMenuService roleMenuService;
    @Autowired
    private ClientDetailsService clientDetailsService;

    /**
     * 用户登录接口-获取access_token
     * TODO 下个版本即将废弃
     */
    @PostMapping("/login")
    public Object login(@RequestBody UserLogin userLogin) throws HttpRequestMethodNotSupportedException {
        // 这里设定，调用这个接口的都是资源服务
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        User clientUser = customClientService.checkClientInfo(requestAttributes);
        // 生成已经认证的client
        UsernamePasswordAuthenticationToken token = new UsernamePasswordAuthenticationToken(clientUser, null, new ArrayList<>());
        // 封装成一个UserPassword方式的参数体
        Map<String, String> map = new HashMap<>(16);
        map.put(OauthConstants.USER_NAME, userLogin.getUsername());
        map.put(OauthConstants.PASSWORD, userLogin.getPassword());
        map.put(OauthConstants.REFRESH_TOKEN, userLogin.getRefreshToken());
        map.put(OauthConstants.GRANT_TYPE, userLogin.getGrantType());
        map.put(OauthConstants.SCOPE, userLogin.getScope());
        // 调用自带的获取token方法。
        OauthResponse resultToken = (OauthResponse) tokenEndpoint.postAccessToken(token, map).getBody();
        if (OauthConstants.REFRESH_TOKEN.equals(userLogin.getGrantType())) {
            return Result.success(resultToken);
        }
        if (resultToken.getData() != null) {
            // 查询用户信息
            UserDTO userInfo = userService.selectUserInfo(userLogin);
            // 查询菜单列表信息
            List<MenuInfoVO> menuList = roleMenuService.getMenuListByRole(Long.valueOf(userInfo.getRoleID()));
            resultToken.setMenuList(menuList);
            resultToken.setUserInfo(userInfo);
        }
        return Result.success(resultToken);
    }

    /**
     * 用户登录接口-获取access_token
     */
    @Autowired
    private CustomClientService customClientService;
    @Autowired
    private RedisUtil redisUtil;

    @PostMapping("/userLogin")
    public Object newlogin(@RequestBody UserLogin userLogin) throws HttpRequestMethodNotSupportedException {
        // 这里设定，调用这个接口的都是资源服务
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        User clientUser = customClientService.checkClientInfo(requestAttributes);
        UsernamePasswordAuthenticationToken token = new UsernamePasswordAuthenticationToken(clientUser, null, new ArrayList<>());
        // 封装成一个UserPassword方式的参数体
        Map<String, String> map = new HashMap<>(16);
        map.put(OauthConstants.USER_NAME, userLogin.getUsername());
        map.put(OauthConstants.PASSWORD, userLogin.getPassword());
        map.put(OauthConstants.REFRESH_TOKEN, userLogin.getRefreshToken());
        map.put(OauthConstants.GRANT_TYPE, userLogin.getGrantType());
        map.put(OauthConstants.SCOPE, userLogin.getScope());
        // 调用自带的获取token方法。
        OauthResponse resultToken = (OauthResponse) tokenEndpoint.postAccessToken(token, map).getBody();
        if (OauthConstants.REFRESH_TOKEN.equals(userLogin.getGrantType())) {
            return resultToken;
        }
        if (resultToken.getData() != null) {
            // 查询用户信息
            UserDTO userInfo = userService.selectUserInfo(userLogin);
            // 查询菜单列表信息
            List<MenuInfoVO> menuList = roleMenuService.getMenuListByRole(Long.valueOf(userInfo.getRoleID()));
            resultToken.setMenuList(menuList);
            resultToken.setUserInfo(userInfo);
        }
        return resultToken;
    }

    @PostMapping("/getAccessToken")
    @ApiOperation("客户端模式获取token")
    public Object getAccessToken(@RequestBody ClientInfo clientInfo) throws HttpRequestMethodNotSupportedException {
        ClientDetails clientDetails = null;
        try {
            clientDetails = clientDetailsService.loadClientByClientId(clientInfo.getClientId());
        } catch (ClientRegistrationException e) {
            throw new BusinessException(ResultEnum.NONE_CLIENT_INFO, e.getMessage());
        }
        if (clientDetails == null) {
            throw new BusinessException(ResultEnum.NONE_CLIENT_INFO);
        } else if (!clientInfo.getClientSecret().equals(clientDetails.getClientSecret())) {
            throw new BusinessException(ResultEnum.CLIENT_SECRET_ERROR);
        }
        User clientUser = new User(clientInfo.getClientId(), clientInfo.getClientSecret(), new ArrayList<>());
        // 生成已经认证的client
        UsernamePasswordAuthenticationToken token = new UsernamePasswordAuthenticationToken(clientUser, null, new ArrayList<>());
        // 封装成一个UserPassword方式的参数体
        Map<String, String> map = new HashMap<>(16);
        map.put(OauthConstants.CLIENT_ID, clientInfo.getClientId());
        map.put(OauthConstants.CLIENT_SECRET, clientInfo.getClientSecret());
        map.put(OauthConstants.SCOPE, clientInfo.getScope());
        map.put(OauthConstants.GRANT_TYPE, clientInfo.getGrantType());
        // 调用自带的获取token方法。
        OauthResponse response = null;
        try {
            response = (OauthResponse) tokenEndpoint.postAccessToken(token, map).getBody();
            redisUtil.set("bto_" + clientInfo.getClientId(), response.getData().toString(), -1L);
        } catch (Exception e) {
            throw e;
        }
        return response;
    }

    @GetMapping("getClientIds")
    @ApiOperation("获取所有客户端id")
    public Result getClientIds() {
        List<String> clientIds = clientService.getClientIds();
        if (CollUtil.isEmpty(clientIds)) {
            return Result.instance(ResultEnum.NO_CONTENT);
        }
        return Result.success(clientIds);
    }

    /**
     * 发送重置密码的验证码
     *
     * @param phoneNumber 电话号码
     * @return {@link Result }
     * <AUTHOR>
     * @since 2023-12-26 14:20:52
     */

    @ApiOperation("发送重置密码的验证码")
    @GetMapping("sendResetCodeMsg")
    public Result sendResetCodeMsg(@RequestParam @NotBlank(message = "手机号不能为空") String phoneNumber) {
        HashMap<Boolean, String> map = userService.checkUserExistByPhone(phoneNumber);
        if (map.containsKey(Boolean.FALSE)) {
            return Result.failed(map.get(Boolean.FALSE));
        }
        return Result.failed("本地部署暂不支持发送短信，请联系管理员重置密码");
    }

    @ApiOperation("根据验证码重置密码 （密码由前端md5加密）")
    @PostMapping("resetPasswdByCode")
    public Result resetPasswdByCode(@RequestParam @NotBlank(message = "手机号不能为空") String phoneNumber,
                                    @RequestParam @NotBlank(message = "验证码不能为空") String code,
                                    @RequestParam @NotBlank(message = "密码不能为空")String password) {
        return userService.resetPasswdByCode(phoneNumber, code, password);
    }

}
