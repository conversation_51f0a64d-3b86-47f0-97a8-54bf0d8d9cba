package com.bto.system.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.converter.vo.ElectricityInfoDTOMapper;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.*;
import com.bto.commons.pojo.vo.*;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.utils.PlantStatisticsExcelCustomizeColumnWidth;
import com.bto.commons.utils.ReflectUtil;
import com.bto.system.utils.GlobalParamUtil;
import com.bto.system.service.AlarmManageService;
import com.bto.system.service.StatisticService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR> by zhb on 2024/5/9.
 */
@Api(tags = "统计信息模块")
@RestController
@RequestMapping("/statistics")
public class StatisticController {

    @Autowired
    private GlobalParamUtil globalParamUtil;

    @Autowired
    private StatisticService statisticService;

    @Autowired
    private AlarmManageService alarmManageService;

    @Autowired
    private ElectricityInfoDTOMapper electricityInfoDTOMapper;

    @GetMapping("getPlantNumInfo")
    @ApiOperation(value = "获取电站统计数量信息",response = NumInfoDTO.class)
    @PreAuthorize(value = "hasAuthority('stats:plantNum:info')")
    public Result<NumInfoDTO> getPlantNumInfo() throws NoSuchFieldException, IllegalAccessException {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        NumInfoDTO numInfo = statisticService.getPlantNumInfo(userInfo);
        return Result.success(numInfo);
    }

    @GetMapping("getPlantElectricityInfo")
    @ApiOperation(value = "获取电站发电量与工作效率数据：包括电站统计数据与最大工作效率电站")
    @PreAuthorize(value = "hasAuthority('stats:plantElectricity:info')")
    public Result<PlantElectricityVO> getPlantElectricityInfo() {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        PlantElectricityVO plantElectricityInfo = statisticService.getPlantElectricityInfo(userInfo);
        return Result.success(plantElectricityInfo);
    }

    @GetMapping("getInverterNumInfo")
    @ApiOperation(value = "获取逆变器统计数量信息")
    @PreAuthorize(value = "hasAuthority('stats:inverterNum:info')")
    public Result<NumInfoDTO> getInverterNumInfo() throws NoSuchFieldException, IllegalAccessException {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        NumInfoDTO numInfo = statisticService.getInverterNumInfo(userInfo);
        return Result.success(numInfo);
    }

    @GetMapping("getElectricityBySixMonth")
    @ApiOperation(value = "获取近六个月发电量数据")
    public Result getElectricityBySixMonth() {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        List<ChartElectricityInfoVO> electricityBySixMonth = statisticService.getElectricityBySixMonth(userInfo);
        return Result.success(electricityBySixMonth);
    }

    @GetMapping("getEveryHourElectricityInfo")
    @ApiOperation(value = "查询所有电站当日每小时发电量统计")
    public Result getEveryHourElectricityInfo() { //todo 留待改造
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        List<ChartElectricityInfoVO> everyHourElectricityInfo = statisticService.getEveryHourElectricityInfo(userInfo);
        return Result.success(everyHourElectricityInfo);
    }

    @PostMapping("getElectricityStatisticsInfo")
    @ApiOperation(value = "能效收益统计-区域/发电统计")
    public Result<PageInfo<ElectricityStaticsInfoVO>> getElectricityStatisticsInfo(@RequestBody ElectricityStatisticsQueryDTO query) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        PageInfo<ElectricityStaticsInfoVO> electricityStatisticsInfo = statisticService.getElectricityStatisticsInfo(query,userInfo);
        return Result.success(electricityStatisticsInfo);
    }

    @PostMapping("exportElectricityStatisticsInfo")
    @ApiOperation(value = "导出能效收益统计-区域/发电统计--Excel表格--hjw")
    public void exportExcelElectricityStatisticsInfo(@RequestBody ElectricityStatisticsExportFileDTO query, HttpServletResponse response) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        PageInfo<ElectricityStaticsInfoVO> pageInfo = statisticService.exportExcelElectricityStatisticsInfo(query, userInfo);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + "能效收益统计" + ".xlsx");
        List<ElectricityStaticsInfoVO> electricityStaticsInfoVOList = pageInfo.getList();
        ArrayList<ElectricityInfoWithPlantNameAndUidVO> electricityInfoWithPlantNameAndUidVOList = new ArrayList<>();
        for (ElectricityStaticsInfoVO electricityStaticsInfoVO : electricityStaticsInfoVOList) {
            List<ElectricityInfoVO> electricityList = electricityStaticsInfoVO.getElectricityList();
            // 将electricityStaticsInfoDTO的成员变量electricityList数据扁平化处理,ElectricityInfoDTO类 转成 ElectricityInfoWithPlantNameAndUidDTO 类
            for (ElectricityInfoVO electricityInfoVO : electricityList) {
                ElectricityInfoWithPlantNameAndUidVO electricityInfoWithPlantNameAndUidVO = electricityInfoDTOMapper.electricityInfoDTO2ElectricityInfoWithPlantNameAndUidDTO(electricityInfoVO);
                electricityInfoWithPlantNameAndUidVO.setPlantName(electricityStaticsInfoVO.getPlantName());
                electricityInfoWithPlantNameAndUidVO.setPlantUid(electricityStaticsInfoVO.getPlantUid());
                electricityInfoWithPlantNameAndUidVOList.add(electricityInfoWithPlantNameAndUidVO);
            }
        }
        try {
            EasyExcel.write(response.getOutputStream(), ElectricityInfoWithPlantNameAndUidVO.class).sheet("sheet1").doWrite(electricityInfoWithPlantNameAndUidVOList);
        } catch (Exception e) {
            throw new BusinessException(ResultEnum.SYSTEM_RUNTIME_FAILED.getCode(), ResultEnum.SYSTEM_RUNTIME_FAILED.getMessage());
        }
    }

    @PostMapping("getPlantStatisticsInfo")
    @ApiOperation(value = "获取电站统计数据信息")
    public Result<Page<PlantStatisticsInfoVO>> getPlantStatisticsInfo(@RequestBody PlantStatisticsQueryDTO query) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        Page<PlantStatisticsInfoVO> result = statisticService.getPlantStatisticsInfo(query,userInfo);
        return Result.success(result);
    }

    @PostMapping(value = "exportPlantStatisticsInfo", produces = "application/octet-stream")
    @ApiOperation(value = "电站统计-Excel报表导出")
    public void exportExcelPlantStatistics(@RequestBody PlantStatisticsExportFileDTO query, HttpServletResponse response) throws IOException, IllegalAccessException {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        Page<PlantStatisticsInfoVO> page = statisticService.exportExcelPlantStatistics(userInfo, query);
        String fileName = "电站统计" + query.getPowerStartTime() + "~" + query.getPowerEndTime();
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        try {
            // 内容策略
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            // 设置 水平居中
            contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(null, contentWriteCellStyle);
            EasyExcel.write(response.getOutputStream(), PlantStatisticsInfoVO.class).sheet("电站统计")
                    .registerWriteHandler(new PlantStatisticsExcelCustomizeColumnWidth())
                    .head(head(fileName))
                    .registerWriteHandler(horizontalCellStyleStrategy)
                    .doWrite(page.getRecords());
        } catch (Exception e) {
            throw new BusinessException(ResultEnum.SYSTEM_RUNTIME_FAILED.getCode(), ResultEnum.SYSTEM_RUNTIME_FAILED.getMessage());
        }
    }

    private List<List<String>> head(String fileName) {
        List<String> columnList = ReflectUtil.getFieldNameByObj(new PlantStatisticsInfoVO());
        List<List<String>> list = new ArrayList<>();
        for (String columnName : columnList) {
            List<String> head = new ArrayList<>();
            head.add(fileName);
            head.add(columnName);
            list.add(head);
        }
        return list;
    }

    @PostMapping("integrativeStatisticChart")
    @ApiOperation("综合统计图形数据查询")
    public Result<List<IntegrativeStatisticChartVO>> integrativeStatisticChart(@RequestBody IntegrativeStatisticQueryDTO query) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        List<IntegrativeStatisticChartVO> result = statisticService.integrativeStatisticChart(userInfo, query);
        return Result.success(result);
    }
    @PostMapping("integrativeStatisticSheet")
    @ApiOperation("综合统计表格数据查询")
    public Result<IntegrativeStatisticSheetVO> integrativeStatisticSheet(@RequestBody IntegrativeStatisticQueryDTO query) throws Exception {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        IntegrativeStatisticSheetVO result = statisticService.integrativeStatisticSheet(userInfo, query);
        return Result.success(result);
    }

    @GetMapping("getElectricityByNumDay/{dayNums}")
    @ApiOperation(value = "按天数获取发电量数据")
    public Result getElectricityByNumDay(@PathVariable Integer dayNums) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        List<ChartElectricityInfoVO> getElectricityByNumDay = statisticService.getElectricityByNumDay(userInfo,dayNums);
        return Result.success(getElectricityByNumDay);

    }

    @GetMapping("getAlarmNumInfo")
    @ApiOperation(value = "获取告警统计数量信息")
    @PreAuthorize(value = "hasAuthority('stats:alarmNum:info')")
    public Result getAlarmNumInfo(){
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        HashMap<String, String> alarmNumInfo = alarmManageService.getAlarmNumInfo(userInfo);
        return Result.success(alarmNumInfo);
    }

    @GetMapping("getDeviceNumInfo")
    @ApiOperation(value = "获取设备统计数量信息")
    @PreAuthorize(value = "hasAuthority('stats:deviceNum:info')")
    public Result<NumInfoDTO> getDeviceNumInfo() throws NoSuchFieldException, IllegalAccessException {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        NumInfoDTO deviceNumInfo = statisticService.getDeviceNumInfo(userInfo);
        return Result.success(deviceNumInfo);
    }

}
