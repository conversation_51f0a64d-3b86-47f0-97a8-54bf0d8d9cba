package com.bto.system.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.core.annotation.Order;
import springfox.bean.validators.configuration.BeanValidatorPluginsConfiguration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2WebMvc;

/**
 * swagger配置类
 * <AUTHOR>
 * @date 2023/5/17 15:37
 */
@Configuration
@EnableSwagger2WebMvc
@Import(BeanValidatorPluginsConfiguration.class)
public class SwaggerConfiguration {

    @Bean(value = "userApi")
    @Order(value = 1)
    public Docket groupRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(groupApiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.bto"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(groupApiInfo());

    }

    private ApiInfo groupApiInfo() {
        return new ApiInfoBuilder()
                .title("博通-光伏运维平台2.0-系统管理服务-接口文档")
                .description("<div style='font-size:14px;color:red;'>Bto-Photovoltaic operation and maintenance platform-2.0-Device-APIs</div>")
                .termsOfServiceUrl("https://www.btosolarman.com/")
                .version("2.0")
                .license("Bto")
//                .contact("<EMAIL>")
                .build();
    }
}