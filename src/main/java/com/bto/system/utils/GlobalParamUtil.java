package com.bto.system.utils;

import cn.hutool.core.collection.CollUtil;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.bto.commons.enums.SystemEnum;
import com.bto.commons.enums.UserEnum;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.response.ResultEnum;
import com.bto.system.service.ProjectService;
import com.bto.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

import static com.bto.commons.constant.OauthConstants.*;

/**
 * <AUTHOR>
 * @date 2023/5/30 17:45
 */
@Component
public class GlobalParamUtil {

    @Autowired
    private UserService userService;

    @Autowired
    private ProjectService projectService;

    public RequireParamsDTO getUserInfo() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        HttpServletRequest request1 = attributes.getRequest();
        String authorization = request1.getHeader("Authorization");
        String jwtToken = authorization.replace("bearer", "").trim();
        JWTVerifier jwtVerifier = JWT.require((Algorithm.HMAC256(SystemEnum.SIGN_KEY.getName()))).build();
        // 解析指定的token
        DecodedJWT decodedJWT = jwtVerifier.verify(jwtToken);
        Claim claim = decodedJWT.getClaim(USER_INFO);
        Map<String, Object> userInfo = claim.asMap();
        RequireParamsDTO requireParams = new RequireParamsDTO();
        for (String key : userInfo.keySet()) {
            if (key.equals("userUid")) {
                requireParams.setUserUid(userInfo.get(key).toString());
            } else if (key.equals("userType")) {
                requireParams.setUserType(userInfo.get(key).toString());
            } else if (key.equals("roleID")) {
                requireParams.setRoleID(userInfo.get(key).toString());
            } else if (key.equals("projectID") || key.equals("projectId")) {
                requireParams.setProjectID(userInfo.get(key).toString());
            }
        }
        if (UserEnum.USER_OF_INDIVIDUAL.getCode().equals(requireParams.getUserType())) {
            RequestContextHolder.setRequestAttributes(requestAttributes);
            // Result<List<String>> plantUidListByUser = systemServiceClient.getPlantUidListByUser(requireParams.getUserUid());
            // List<String> data = plantUidListByUser.getData();
            List<String> data = userService.getPlantUidList(requireParams.getUserUid());
            requireParams.setPlantList(data);
        } else {
            // 获取请求头中项目id
            String projectId = request1.getHeader("projectId");
            // 判断是否带有请求头
            RequestContextHolder.setRequestAttributes(requestAttributes);
            // List<String> data = systemServiceClient.getProjectIDListByPid(requireParams.getProjectID()).getData();
            List<String> data = projectService.getProjectIDListByPid(requireParams.getProjectID());
            if (projectId != null && data.contains(projectId)) {
                RequestContextHolder.setRequestAttributes(requestAttributes);
                // data = systemServiceClient.getProjectIDListByPid(projectId).getData();
                data = projectService.getProjectIDListByPid(projectId);
                requireParams.setProjectList(data);
            } else {
                requireParams.setProjectList(data);
            }
        }
        if (CollUtil.isNotEmpty(requireParams.getProjectList()) || CollUtil.isNotEmpty(requireParams.getPlantList())) {
            return requireParams;
        } else {
            throw new BusinessException(ResultEnum.USER_ACCOUNT_DATA_EXCEPTION);
        }
    }

}
