package com.bto.oauth.config;

import com.bto.commons.enums.SystemEnum;
import com.bto.oauth.custom.CustomAccessTokenConverter;
import com.bto.oauth.enhancer.JWTokenEnhancer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;
import org.springframework.security.oauth2.provider.token.store.JwtTokenStore;

/**
 * JWTokenConfig配置类
 * <AUTHOR>
 * @date 2023/3/29 10:35
 */
@Configuration
public class JWTokenConfig {

    @Bean
    public TokenStore jwtTokenStore() {

        return new JwtTokenStore(jwtAccessTokenConverter());
    }

    @Bean
    public JwtAccessTokenConverter jwtAccessTokenConverter() {
        JwtAccessTokenConverter jwtTokenConverter = new JwtAccessTokenConverter();
        jwtTokenConverter.setAccessTokenConverter(new CustomAccessTokenConverter());
        // 签名密钥
        jwtTokenConverter.setSigningKey(SystemEnum.SIGN_KEY.getName());
        return jwtTokenConverter;
    }

    @Bean
    public TokenEnhancer tokenEnhancer() {
        return new JWTokenEnhancer();
    }
}