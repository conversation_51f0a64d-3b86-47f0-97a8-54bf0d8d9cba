package com.bto.oauth.config;

import com.bto.oauth.custom.CustomExceptionTranslator;
import com.bto.oauth.handle.MyAuthenticationFailureHandler;
import com.bto.oauth.handle.MyAuthenticationSucessHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configurers.ResourceServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.error.OAuth2AuthenticationEntryPoint;

import javax.annotation.Resource;
import java.util.List;

/**
 * 资源服务器配置
 * 使用一个特殊的过滤器来检查请求中的承载令牌，以便通过OAuth2对请求进行认证
 *
 * <AUTHOR>
 * @date 2023/3/29 10:27
 */
@Configuration
@EnableResourceServer
public class ResourceServerConfig extends ResourceServerConfigurerAdapter {
    @Autowired
    private PermitResource permitResource;
    @Resource
    private MyAuthenticationSucessHandler authenticationSucessHandler;
    @Resource
    private MyAuthenticationFailureHandler authenticationFailureHandler;


    /**
     * 对资源oauth2.0客户端的http请求进行资源服务配置
     * 并加入一些基本的Spring Security配置
     *
     * @param http the current http filter configuration
     * @throws Exception
     */
    @Override
    public void configure(HttpSecurity http) throws Exception {
        // 忽略授权的地址列表
        List<String> permitList = permitResource.getPermitList();
        String[] permits = permitList.toArray(new String[0]);
        http
                // 表单登录
                .formLogin()
                // 处理表单登录 URL
                .loginProcessingUrl("/login")
                // 处理登录成功
                .successHandler(authenticationSucessHandler)
                // 处理登录失败
                .failureHandler(authenticationFailureHandler)
                .and()
                // 授权配置
                .authorizeRequests()
                .antMatchers("/login", "/doc.html", "system/v2/api-docs"
                        , "/security/v2/api-docs", "/plant/v2/api-docs", "/device/v2/api-docs"
                        , "/alarm/v2/api-docs", "/alarm/v2/api-docs", "/oauth/login","/oauth/userLogin","/oauth/getAccessToken").permitAll()
                .antMatchers(permits).permitAll()
                .antMatchers(HttpMethod.OPTIONS).permitAll()
                // 所有请求
                .anyRequest()
                // 都需要认证
                .authenticated()
                .and()
                .csrf().disable();
    }

    @Override
    public void configure(ResourceServerSecurityConfigurer resources) throws Exception {
        // 自定义token失效/错误返回信息
        OAuth2AuthenticationEntryPoint authenticationEntryPoint = new OAuth2AuthenticationEntryPoint();
        authenticationEntryPoint.setExceptionTranslator(new CustomExceptionTranslator());
        resources.authenticationEntryPoint(authenticationEntryPoint);
    }
}