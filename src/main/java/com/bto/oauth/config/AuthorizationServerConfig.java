package com.bto.oauth.config;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bto.commons.enums.UserEnum;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.utils.PropertyUtils;
import com.bto.oauth.custom.CustomAccessTokenConverter;
import com.bto.oauth.dao.ClientDetailsMapper;
import com.bto.oauth.service.CustomUserDetailsService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.common.exceptions.InvalidClientException;
import org.springframework.security.oauth2.config.annotation.configurers.ClientDetailsServiceConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configuration.AuthorizationServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableAuthorizationServer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerEndpointsConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.ClientDetails;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.NoSuchClientException;
import org.springframework.security.oauth2.provider.client.BaseClientDetails;
import org.springframework.security.oauth2.provider.client.JdbcClientDetailsService;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;
import org.springframework.security.oauth2.provider.token.TokenEnhancerChain;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;
import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStore;

import javax.sql.DataSource;
import java.util.*;
import static com.bto.commons.constant.OauthConstants.*;

/**
 * <AUTHOR>
 * @date 2023/3/28 18:06
 * 授权服务器配置
 */
@Configuration
@EnableAuthorizationServer
public class AuthorizationServerConfig extends AuthorizationServerConfigurerAdapter {
    @Autowired
    private TokenStore jwtTokenStore;
    @Autowired
    private RedisTokenStore redisTokenStore;
    @Autowired
    private JwtAccessTokenConverter jwtAccessTokenConverter;
    @Autowired
    private CustomAccessTokenConverter customAccessTokenConverter;
    @Autowired
    private AuthenticationManager authenticationManager;
    @Autowired
    private TokenEnhancer tokenEnhancer;
    @Autowired
    private CustomUserDetailsService userDetailService;
    @Autowired
    @Qualifier("dataSource")
    private DataSource dataSource;
    @Autowired
    private ClientDetailsMapper clientDetailsMapper;

    /**
     * 对Jwt签名时，增加一个密钥     * JwtAccessTokenConverter：对Jwt来进行编码以及解码的类
     */
    @Bean
    public JwtAccessTokenConverter accessTokenConverter() {
        JwtAccessTokenConverter converter = new JwtAccessTokenConverter();
        converter.setAccessTokenConverter(customAccessTokenConverter);
        converter.setSigningKey("bto-dev");
        return converter;
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    /**
     * 配置授权（authorization）以及令牌（token）的访问端点和令牌服务(token services)
     * 使用JWT替换默认令牌（只需要指定TokenStore为JwtTokenStore即可）
     *
     * @param endpoints the endpoints configurer
     */
    @Override
    public void configure(AuthorizationServerEndpointsConfigurer endpoints) {
        TokenEnhancerChain enhancerChain = new TokenEnhancerChain();
        List<TokenEnhancer> enhancers = new ArrayList<>();
        enhancers.add(tokenEnhancer);
        enhancers.add(jwtAccessTokenConverter);
        enhancerChain.setTokenEnhancers(enhancers);
        endpoints.authenticationManager(authenticationManager)
                .tokenStore(jwtTokenStore)
                .tokenStore(redisTokenStore)
                .accessTokenConverter(jwtAccessTokenConverter)
                .accessTokenConverter(customAccessTokenConverter)
                .reuseRefreshTokens(false)
                .userDetailsService(userDetailService)
                .tokenEnhancer(enhancerChain)
                .allowedTokenEndpointRequestMethods(HttpMethod.GET, HttpMethod.POST, HttpMethod.PUT, HttpMethod.DELETE, HttpMethod.OPTIONS);
    }

    /**
     * 配置OAuth2客户端
     * 注入ClientDetailsService实例对象，能够使用内存或者JDBC来实现客户端详情服务，
     * 默认提供了2个实现类JdbcClientDetailsService、InMemoryClientDetailsService
     *
     * @param clients the client details configurer
     * @throws Exception
     */
    @Override
    public void configure(ClientDetailsServiceConfigurer clients) throws Exception {
        clients.withClientDetails(myclientDetailsService());
    }

    public ClientDetailsService myclientDetailsService() {
        return new JdbcClientDetailsService(dataSource) {
            @Override
            public ClientDetails loadClientByClientId(String clientId) throws InvalidClientException {
                BaseClientDetails clientDetails = new BaseClientDetails();
                com.bto.oauth.entity.ClientDetails details = new com.bto.oauth.entity.ClientDetails();
                QueryWrapper<com.bto.oauth.entity.ClientDetails> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq(CLIENT_ID, clientId);
                try {
                    details = clientDetailsMapper.selectOne(queryWrapper);
                    if (ObjectUtil.isNull(details)) {
                        throw new BusinessException(ResultEnum.NONE_CLIENT_INFO);
                    }
                    String[] nullPropertyNames = PropertyUtils.getNullPropertyNames(details);
                    BeanUtils.copyProperties(details, clientDetails, nullPropertyNames);
                    String scope = details.getScope();
                    String grantTypes = details.getAuthorizedGrantTypes();
                    String[] scopeArray = scope.split(",");
                    String[] grantTypesArray = grantTypes.split(",");
                    clientDetails.setScope(Arrays.asList(scopeArray));
                    clientDetails.setAuthorizedGrantTypes(Arrays.asList(grantTypesArray));
                    clientDetails.setAccessTokenValiditySeconds(details.getAccessTokenValidity());
                    clientDetails.setRefreshTokenValiditySeconds(details.getRefreshTokenValidity());
                } catch (EmptyResultDataAccessException e) {
                    throw new NoSuchClientException("No client with requested id: " + clientId);
                }
                if (UserEnum.USER_OF_API.getCode().equals(details.getUserType())) {
                    Set<String> authorities = clientDetailsMapper.selectAuthorities(clientId);
                    List<GrantedAuthority> authorityList = new ArrayList<>();
                    for (String authority : authorities) {
                        authorityList.add(new SimpleGrantedAuthority(authority));
                    }
                    clientDetails.setAuthorities(authorityList);
                }
                return clientDetails;
            }
        };
    }

    @Override
    public void configure(AuthorizationServerSecurityConfigurer security) {
        security.tokenKeyAccess("permitAll()").checkTokenAccess("isAuthenticated()");
        security.allowFormAuthenticationForClients();
        // 获取密钥需要身份认证
        security.tokenKeyAccess("isAuthenticated()");
    }
}