package com.bto.oauth.config;

import com.bto.oauth.handle.MyAuthenticationAccessDeniedHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.BeanIds;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;

/**
 * 认证服务器在继承了AuthorizationServerConfigurerAdapter适配器后，
 * 需要重写configure(AuthorizationServerEndpointsConfigurer endpoints)方法，
 * 以及指定 AuthenticationManager和UserDetailService。
 * 创建一个新的配置类SecurityConfig
 * <AUTHOR>
 * @date 2023/3/29 10:40
 */
@Configuration
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class SecurityConfig extends WebSecurityConfigurerAdapter {

    @Autowired
    private MyAuthenticationAccessDeniedHandler authenticationAccessDeniedHandler;

    /**
     * 此处注册我们需要的AuthenticationManagerBean
     * @return
     * @throws Exception
     */
    @Bean(name = BeanIds.AUTHENTICATION_MANAGER)
    @Override
    public AuthenticationManager authenticationManagerBean() throws Exception {
        return super.authenticationManagerBean();
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.exceptionHandling().accessDeniedHandler(authenticationAccessDeniedHandler);
    }

}