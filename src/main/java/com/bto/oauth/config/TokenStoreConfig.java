package com.bto.oauth.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.security.oauth2.provider.token.store.JdbcTokenStore;
import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStore;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @date 2023/3/31 16:15
 *   令牌存储策略配置类
 */
@Configuration
public class TokenStoreConfig {

    @Autowired
    private RedisConnectionFactory redisConnectionFactory;
    @Autowired
    private DataSource dataSource;

     @Bean
     @Primary
     public RedisTokenStore redisTokenStore(){
         return new RedisTokenStore(redisConnectionFactory);
     }

     @Bean
    public JdbcTokenStore jdbcTokenStore(){
         return new JdbcTokenStore(dataSource);
     }
}