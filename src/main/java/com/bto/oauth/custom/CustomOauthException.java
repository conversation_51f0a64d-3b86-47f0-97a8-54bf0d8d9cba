package com.bto.oauth.custom;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.springframework.security.oauth2.common.exceptions.OAuth2Exception;

/**
 * <AUTHOR>
 * 2023/3/31 14:40
 */
@JsonSerialize(using = CustomOauthExceptionSerializer.class)
public class CustomOauthException extends OAuth2Exception {
    private String code;
    private String mesg;

    public CustomOauthException(String msg, String code, String mesg){
        super(msg);
        this.code = code;
        this.mesg = mesg;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMasg() {
        return mesg;
    }

    public void setMasg(String mesg) {
        this.mesg = mesg;
    }

    @Override
    public String toString() {
        return "CustomOauthException{" +
                "code='" + code + '\'' +
                ", mesg='" + mesg + '\'' +
                '}';
    }
}