package com.bto.oauth.custom;

import com.bto.commons.enums.SystemEnum;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;

import java.io.IOException;

import static com.bto.commons.response.ResultEnum.*;

/**
 * 自定义Oauth异常结果
 * <AUTHOR>
 * 2023/3/31 11:40
 */
public class CustomOauthExceptionSerializer extends StdSerializer {

    private static final long serialVersionUID = -1111852940631273050L;

    public CustomOauthExceptionSerializer() {
        super(CustomOauthException.class);
 
    }
    @Override
    public void serialize(Object value, JsonGenerator gen, SerializerProvider provider) throws IOException {
        CustomOauthException oauthException = (CustomOauthException) value;
        if (oauthException.getCode().equals(SystemEnum.invalid_token.getName())){
            gen.writeStartObject();
            gen.writeObjectField("status",USER_LOGIN_EXPIRED.getCode());
            gen.writeObjectField("message",USER_LOGIN_EXPIRED.getMessage());
            gen.writeEndObject();
        } else if (oauthException.getCode().equals(SystemEnum.unauthorized_token.getName())) {
            gen.writeStartObject();
            gen.writeObjectField("status",ACCESS_AUTHORIZATION_FAILED.getCode());
            gen.writeObjectField("message",ACCESS_AUTHORIZATION_FAILED.getMessage());
            gen.writeEndObject();
        }else {
            gen.writeStartObject();
            gen.writeObjectField("status",SYSTEM_RUNTIME_FAILED.getCode());
            gen.writeObjectField("message",SYSTEM_RUNTIME_FAILED.getMessage());
            gen.writeEndObject();
        }
    }
}