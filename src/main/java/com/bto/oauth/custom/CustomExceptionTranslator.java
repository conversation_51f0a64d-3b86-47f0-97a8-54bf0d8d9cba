package com.bto.oauth.custom;

import org.springframework.http.ResponseEntity;
import org.springframework.security.oauth2.common.exceptions.OAuth2Exception;
import org.springframework.security.oauth2.provider.error.DefaultWebResponseExceptionTranslator;

/**
 * <AUTHOR>
 * 2023/3/31 10:40
 */
public class CustomExceptionTranslator extends DefaultWebResponseExceptionTranslator {
    @Override
    public ResponseEntity translate(Exception e) throws Exception {
        ResponseEntity translate = super.translate(e);
        OAuth2Exception body = (OAuth2Exception) translate.getBody();
        CustomOauthException customOauthException = new CustomOauthException(body.getMessage(), body.getOAuth2ErrorCode(), String.valueOf(body.getHttpErrorCode()));
        ResponseEntity response = new ResponseEntity<>(customOauthException, translate.getHeaders(), translate.getStatusCode());
        return response;
    }
}