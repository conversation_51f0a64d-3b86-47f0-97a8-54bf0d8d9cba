package com.bto.oauth.custom;

import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.OAuth2Request;
import org.springframework.security.oauth2.provider.token.DefaultAccessTokenConverter;
import org.springframework.security.oauth2.provider.token.UserAuthenticationConverter;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import static com.bto.commons.constant.OauthConstants.*;

/**
 * 自定义Token转换类
 * <AUTHOR>
 * @date 2023/08/07
 */
@Component
public class CustomAccessTokenConverter extends DefaultAccessTokenConverter {
    private UserAuthenticationConverter userTokenConverter = new CustomUserAuthenticationConverter();

    @Override
    public Map<String, ?> convertAccessToken(OAuth2AccessToken token, OAuth2Authentication authentication) {
        Map<String, Object> response = new HashMap();
        OAuth2Request clientToken = authentication.getOAuth2Request();
        if (!authentication.isClientOnly()) {
            response.putAll(this.userTokenConverter.convertUserAuthentication(authentication.getUserAuthentication()));
        }
        if (token.getScope() != null) {
            response.put(SCOPE, token.getScope());
        }
        if (token.getAdditionalInformation().containsKey(JWT_ID)) {
            response.put(JWT_ID, token.getAdditionalInformation().get(JWT_ID));
        }
        if (token.getExpiration() != null) {
            response.put(EXPIRATION, token.getExpiration().getTime() / 1000L);
        }
        if (authentication.getOAuth2Request().getGrantType() != null) {
            response.put(GRANT_TYPE, authentication.getOAuth2Request().getGrantType());
        }
        if (clientToken.getResourceIds() != null && !clientToken.getResourceIds().isEmpty()) {
            response.put(RESOURCE_IDS, clientToken.getResourceIds());
        }
        response.put(CLIENT_ID, clientToken.getClientId());
        response.putAll(token.getAdditionalInformation());
        return response;
    }
}