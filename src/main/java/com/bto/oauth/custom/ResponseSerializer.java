package com.bto.oauth.custom;

import java.io.IOException;
import com.bto.commons.response.ResultEnum;
import com.bto.oauth.entity.OauthResponse;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import static com.bto.commons.constant.Constant.*;
import static com.bto.commons.constant.OauthConstants.*;

/**
 * <AUTHOR>
 * @date 2023/3/31 15:20
 */
public class ResponseSerializer extends StdSerializer<OauthResponse> {

    public ResponseSerializer() {
        super(OauthResponse.class);
    }

    @Override
    public void serialize(OauthResponse value, JsonGenerator gen, SerializerProvider provider) throws IOException {
        OAuth2AccessToken oAuth2AccessToken = (OAuth2AccessToken) value.getData();
        gen.writeStartObject();
        if (oAuth2AccessToken != null) {
            gen.writeStringField(STATUS, value.getStatus());
            gen.writeStringField(MESSAGE, value.getMessage());
            gen.writeObjectFieldStart(DATA);
            gen.writeStringField(TOKEN_OF_ACCESS, oAuth2AccessToken.getValue());
            if (oAuth2AccessToken.getRefreshToken() != null) {
                gen.writeStringField(TOKEN_OF_REFRESH, oAuth2AccessToken.getRefreshToken().toString());
            }
            gen.writeStringField(TOKEN_TYPE, oAuth2AccessToken.getTokenType());
            gen.writeObjectField(SCOPE, oAuth2AccessToken.getScope());
            gen.writeNumberField(EXPIRATION, oAuth2AccessToken.getExpiresIn());
            if (value.getUserInfo() != null) {
                gen.writeObjectField(USERINFO, value.getUserInfo());
            }
            if (value.getMenuList() != null) {
                gen.writeObjectField(MENU_LIST, value.getMenuList());
            }
            gen.writeEndObject();
        } else {
            ResultEnum.throwExceptionByCode(value.getStatus());
        }
    }
}