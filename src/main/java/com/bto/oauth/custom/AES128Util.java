package com.bto.oauth.custom;

import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.Security;
import java.util.Random;

/**
 * <AUTHOR>
 * 2023/3/31 10:40
 */
public class AES128Util {
 
	public static void main(String[] args) throws Exception {
		/*
		 * 加密用的Key 可以用26个字母和数字组成，最好不要用保留字符，虽然不会错，至于怎么裁决，个人看情况而定
		 * 此处使用AES-128-CBC加密模式，key需要为16位。
		 */
		String iv = getRandomIVHexString();
 
		String cKey = "T5DFG58SFETGD2ET";
		// 需要加密的字串
		String cSrc = "q";
		System.out.println(cSrc);
		System.out.println(iv);
		// 加密
 
		String enString = Encrypt(cSrc, cKey, iv);
		System.out.println("加密后的字串是：" + enString);
 
		// 解密
		String DeString = Decrypt(enString, cKey, iv);
		System.out.println("解密后的字串是：" + DeString);
	}
 
	/**
	 * 128安全随机数（16位偏移量）
	 * 
	 * @return
	 * @throws Exception
	 */
	public static String getRandomIVHexString() throws Exception {
		String base = "0123456789abcdef";
		Random random = new Random();
		StringBuffer code = new StringBuffer();
		for (int i = 0; i < 16; i++) {
			int number = random.nextInt(base.length());
			code.append(base.charAt(number));
		}
		return code.toString();
	}
 
	/**
	 * 加密：获得一个24位密文
	 * 
	 * @param sSrc
	 *            加密字符串
	 * @param sKey
	 *            密钥
	 * @param sIv
	 *            加密字符偏移量
	 * @return
	 * @throws Exception
	 */
	public static String Encrypt(String sKey, String sSrc, String sIv) throws Exception {
		byte[] raw = sKey.getBytes();
		byte[] ivbyte = sIv.getBytes();
		SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
		Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
		Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding");// "算法/模式/补码方式"
		// IvParameterSpec iv = new
		// IvParameterSpec("0102030405060708".getBytes());
		// 使用CBC模式，需要一个向量iv，可增加加密算法的强度
		cipher.init(Cipher.ENCRYPT_MODE, skeySpec, new IvParameterSpec(ivbyte));
		byte[] encrypted = cipher.doFinal(sSrc.getBytes());
 
		return new BASE64Encoder().encode(encrypted);// 此处使用BASE64做转码功能，同时能起到2次加密的作用。
	}
 
	/**
	 * 解密
	 * 
	 * @param sSrc
	 * @param sKey
	 * @return
	 * @throws Exception
	 */
	public static String Decrypt(String sSrc, String sKey, String sIv) throws Exception {
		try {
			byte[] raw = sKey.getBytes("ASCII");
			byte[] ivbyte = sIv.getBytes();
			SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
			Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
			Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding");
 
			cipher.init(Cipher.DECRYPT_MODE, skeySpec, new IvParameterSpec(ivbyte));
			byte[] encrypted1 = new BASE64Decoder().decodeBuffer(sSrc);// 先用base64解密
			try {
				byte[] original = cipher.doFinal(encrypted1);
				String originalString = new String(original);
				return originalString;
			} catch (Exception e) {
				System.out.println(e.toString());
				return null;
			}
		} catch (Exception ex) {
			System.out.println(ex.toString());
			return null;
		}
	}
 
}