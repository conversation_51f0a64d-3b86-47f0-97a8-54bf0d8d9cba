package com.bto.oauth.enhancer;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bto.commons.constant.OauthConstants;
import com.bto.oauth.dao.ClientDetailsMapper;
import com.bto.oauth.entity.ClientDetails;
import com.bto.oauth.entity.UserInfo;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.oauth2.common.DefaultOAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.OAuth2Request;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import static com.bto.commons.constant.OauthConstants.*;

/**
 * JWT Token增强器:
 * 在令牌存储之前，对令牌进行增强处理，只声明了一个enhance方法
 *
 * <AUTHOR>
 * @date 2023/3/29 10:36
 */
@Component
public class JWTokenEnhancer implements TokenEnhancer {

    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private ClientDetailsMapper clientDetailsMapper;

    /**
     * 重写enhance方法--设置附加信息
     *
     * @param oAuth2AccessToken    当前访问令牌及其过期和刷新令牌
     * @param oAuth2Authentication 包括客户端和用户详细信息的当前身份验证
     * @return
     */
    @Override
    public OAuth2AccessToken enhance(OAuth2AccessToken oAuth2AccessToken, OAuth2Authentication oAuth2Authentication) {
        OAuth2Request oAuth2Request = oAuth2Authentication.getOAuth2Request();
        final Map<String, Object> additionalInfo = new HashMap<>();
        if (!OauthConstants.CLIENT_CREDENTIALS.equals(oAuth2Request.getGrantType())) {
            UserInfo userInfo = (UserInfo) oAuth2Authentication.getPrincipal();
            try {
                String str = objectMapper.writeValueAsString(userInfo);
                Map map = objectMapper.readValue(str, Map.class);
                map.remove(PASSWORD);
                map.remove(AUTHORITIES);
                map.remove(ACCOUNT_NON_EXPIRED);
                map.remove(ACCOUNT_NON_LOCKED);
                map.remove(CREDENTIALS_NON_EXPIRED);
                map.remove(ENABLED);
                map.remove(AUTHORITY_SET);
                additionalInfo.put(USER_INFO, map);
                ((DefaultOAuth2AccessToken) oAuth2AccessToken).setAdditionalInformation(additionalInfo);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        } else {
            String clientId = oAuth2Request.getClientId();
            HashMap<String, Object> map = new HashMap<>();
            try {
                QueryWrapper<ClientDetails> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq(CLIENT_ID, clientId);
                ClientDetails clientDetails = clientDetailsMapper.selectOne(queryWrapper);
                map.put(PROJECT_ID, clientDetails.getProjectId());
                map.put(USER_TYPE, clientDetails.getUserType());
                map.put(CLIENT_ID, clientId);
            } catch (Exception e) {
                e.printStackTrace();
            }
            additionalInfo.put(USER_INFO, map);
            ((DefaultOAuth2AccessToken) oAuth2AccessToken).setAdditionalInformation(additionalInfo);
        }
        return oAuth2AccessToken;
    }
}