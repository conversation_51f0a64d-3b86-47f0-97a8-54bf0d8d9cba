package com.bto.oauth.service;

import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

/**
 * 继承原来的UserDetailsService新增自定义方法
 * <AUTHOR>
 */
@Service
public interface CustomUserDetailsService extends UserDetailsService {

    /**
     * 用户名、密码登录
     * @param adminLoginToken
     * @return
     * @throws UsernameNotFoundException
     */
    UserDetails loadUserByUsernameAndPassword(UsernamePasswordAuthenticationToken adminLoginToken) throws UsernameNotFoundException;

}