package com.bto.oauth.service.impl;

import cn.hutool.core.codec.Base64;
import com.bto.commons.constant.OauthConstants;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.response.ResultEnum;
import com.bto.oauth.service.CustomClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.provider.ClientDetails;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @date 2023/7/19 15:53
 */
@Service
public class CustomClientServiceImpl implements CustomClientService {

    @Autowired
    private ClientDetailsService clientDetailsService;
    @Autowired
    private PasswordEncoder passwordEncoder;
    @Override
    public User checkClientInfo(ServletRequestAttributes requestAttributes) {
        HttpServletRequest request = requestAttributes.getRequest();
        // 1. 从请求头中获取 ClientId
        String header = request.getHeader(OauthConstants.HEAD_TYPE_AUTHORIZATION);
        if (header == null || !header.startsWith(OauthConstants.AUTH_TYPE_BASIC)) {
            throw new BusinessException(ResultEnum.NONE_CLIENT_INFO);
        }
        String[] tokens = this.extractAndDecodeHeader(header, request);
        String clientId = tokens[0];
        String clientSecret = tokens[1];
        // 2. 通过 ClientDetailsService 获取 ClientDetails
        ClientDetails clientDetails = clientDetailsService.loadClientByClientId(clientId);
        // 3. 校验 ClientId和 ClientSecret的正确性
        if (clientDetails == null) {
            throw new BusinessException(ResultEnum.NONE_CLIENT_INFO);
        } else if (!passwordEncoder.matches(clientSecret, clientDetails.getClientSecret())) {
            throw new BusinessException(ResultEnum.CLIENT_SECRET_ERROR);
        }
        return new User(clientId, clientSecret, new ArrayList<>());
    }

    /**
     * 提取并解析请求头
     * @param header
     * @param request
     * @return
     */
    private String[] extractAndDecodeHeader(String header, HttpServletRequest request) {
        String[] headers = header.split(" ");
        String clientToken;
        try {
            clientToken = Base64.decodeStr(headers[1].trim());
        } catch (IllegalArgumentException e) {
            throw new BusinessException(ResultEnum.SYSTEM_RUNTIME_FAILED
                    ,"无法解码Client基本身份验证令牌");
        }catch (Exception e){
            throw new BusinessException(ResultEnum.INVALID_CLIENT_INFO);
        }
        int delim = clientToken.indexOf(":");
        if (delim == -1) {
            throw new BusinessException(ResultEnum.INVALID_CLIENT_INFO);
        } else {
            return new String[]{clientToken.substring(0, delim), clientToken.substring(delim + 1)};
        }
    }
}