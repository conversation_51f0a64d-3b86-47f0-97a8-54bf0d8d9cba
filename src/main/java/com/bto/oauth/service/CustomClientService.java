package com.bto.oauth.service;

import org.springframework.security.core.userdetails.User;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * 自定义客户端服务
 * <AUTHOR>
 * @date 2023/7/19 15:52
 */
@Service
public interface CustomClientService {
    /**
     * 客户端信息校验方法
     * @param requestAttributes
     * @return
     */
    User checkClientInfo(ServletRequestAttributes requestAttributes);
}