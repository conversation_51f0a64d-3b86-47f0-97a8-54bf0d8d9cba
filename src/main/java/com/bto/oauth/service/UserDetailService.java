package com.bto.oauth.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bto.commons.enums.UserEnum;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.utils.PropertyUtils;
import com.bto.oauth.dao.ClientDetailsMapper;
import com.bto.oauth.dao.UserDetailMapper;
import com.bto.oauth.entity.UserInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.provider.ClientDetails;
import org.springframework.security.oauth2.provider.ClientRegistrationException;
import org.springframework.security.oauth2.provider.NoSuchClientException;
import org.springframework.security.oauth2.provider.client.BaseClientDetails;

import java.util.*;

import static com.bto.commons.constant.OauthConstants.CLIENT_ID;

/**
 * 加载用户特定数据的核心接口实现类
 * 它作为用户DAO在整个框架中使用，并且是DaoAuthenticationProvider使用的策略。
 *
 * <AUTHOR>
 * @date 2023/3/29 15:30
 */
@Configuration
public class UserDetailService implements CustomUserDetailsService {
    @Autowired
    private PasswordEncoder passwordEncoder;
    @Autowired
    private UserDetailMapper userDetailMapper;
    @Autowired
    private ClientDetailsMapper clientDetailsMapper;

    /**
     * 根据用户名获取用户 - 用户的角色、权限等信息
     */
    @Override
    public UserInfo loadUserByUsername(String username) throws UsernameNotFoundException {
        QueryWrapper<com.bto.oauth.entity.User> userQueryWrapper = new QueryWrapper<com.bto.oauth.entity.User>();
        userQueryWrapper.eq("user_name", username);
        com.bto.oauth.entity.User user = userDetailMapper.selectOne(userQueryWrapper);
        user.setPassword(this.passwordEncoder.encode(user.getPassword()));
        //查询用户角色对应的权限
        List<String> authorityList = userDetailMapper.selectAuthorityList(user.getRoleID());
        // 用户权限列表
        Set<String> permsSet = new HashSet<>();
        for (String authority : authorityList) {
            if (StrUtil.isBlank(authority)) {
                continue;
            }
            permsSet.addAll(Arrays.asList(authority.trim().split(",")));
        }
        UserInfo userInfo = new UserInfo(user.getUsername(), user.getPassword(), user.isEnabled(),
                user.isAccountNonExpired(), user.isCredentialsNonExpired(),
                user.isAccountNonLocked(), AuthorityUtils.commaSeparatedStringToAuthorityList(""));
        userInfo.setAuthoritySet(permsSet);
        BeanUtils.copyProperties(user, userInfo, PropertyUtils.getNullPropertyNames(user));
        return userInfo;
    }

    public UserInfo  loadUserByUsernameAndPassword(UsernamePasswordAuthenticationToken adminLoginToken) throws UsernameNotFoundException {
        QueryWrapper<com.bto.oauth.entity.User> userQueryWrapper = new QueryWrapper<com.bto.oauth.entity.User>();
        userQueryWrapper.eq("user_name", adminLoginToken.getName());
        userQueryWrapper.eq("user_password", adminLoginToken.getCredentials());
        com.bto.oauth.entity.User user = userDetailMapper.selectOne(userQueryWrapper);
        if (user == null) {
            throw new BusinessException(ResultEnum.IDENTITY_VERIFICATION_FAILED);
        } else if (UserEnum.USER_STATUS_DISENABLE.getCode().equals(user.getUserStatus().toString())) {
            throw new BusinessException(ResultEnum.USER_ACCOUNT_NOT_ENABLE);
        }
        //查询用户角色对应的权限
        List<String> authorityList = userDetailMapper.selectAuthorityList(user.getRoleID());
        // 用户权限列表
        Set<String> permsSet = new HashSet<>();
        for (String authority : authorityList) {
            if (StrUtil.isBlank(authority)) {
                continue;
            }
            permsSet.addAll(Arrays.asList(authority.trim().split(",")));
        }
        user.setPassword(this.passwordEncoder.encode(user.getPassword()));
        UserInfo userInfo = new UserInfo(user.getUsername(), user.getPassword(), user.isEnabled(),
                user.isAccountNonExpired(), user.isCredentialsNonExpired(),
                user.isAccountNonLocked(), AuthorityUtils.commaSeparatedStringToAuthorityList("user:add"));
        userInfo.setAuthoritySet(permsSet);
        BeanUtils.copyProperties(user, userInfo, PropertyUtils.getNullPropertyNames(user));
        return userInfo;
    }

    public ClientDetails loadClientByUserUid(String userUid, String clientId) throws ClientRegistrationException {
        BaseClientDetails clientDetails = new BaseClientDetails();
        com.bto.oauth.entity.ClientDetails details = new com.bto.oauth.entity.ClientDetails();
        QueryWrapper<com.bto.oauth.entity.ClientDetails> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CLIENT_ID, clientId);
        try {
            details = clientDetailsMapper.selectOne(queryWrapper);
            if (ObjectUtil.isNull(details)) {
                throw new BusinessException(ResultEnum.NONE_CLIENT_INFO);
            }
            String[] nullPropertyNames = PropertyUtils.getNullPropertyNames(details);
            BeanUtils.copyProperties(details, clientDetails, nullPropertyNames);
            String scope = details.getScope();
            String grantTypes = details.getAuthorizedGrantTypes();
            String[] scopeArray = scope.split(",");
            String[] grantTypesArray = grantTypes.split(",");
            clientDetails.setScope(Arrays.asList(scopeArray));
            clientDetails.setAuthorizedGrantTypes(Arrays.asList(grantTypesArray));
            clientDetails.setAccessTokenValiditySeconds(details.getAccessTokenValidity());
            clientDetails.setRefreshTokenValiditySeconds(details.getRefreshTokenValidity());
        } catch (EmptyResultDataAccessException e) {
            throw new NoSuchClientException("查询不到用户ID" + userUid);
        }
        if (UserEnum.USER_OF_API.getCode().equals(details.getUserType())) {
            Set<String> authorities = clientDetailsMapper.selectAuthorityList(userUid);
            List<GrantedAuthority> authorityList = new ArrayList<>();
            for (String authority : authorities) {
                authorityList.add(new SimpleGrantedAuthority(authority));
            }
            clientDetails.setAuthorities(authorityList);
        }
        return clientDetails;
    }

}