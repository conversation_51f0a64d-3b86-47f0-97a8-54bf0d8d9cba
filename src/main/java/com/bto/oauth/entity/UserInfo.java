package com.bto.oauth.entity;

import com.bto.commons.pojo.vo.MenuInfoVO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/24 11:37
 */

@EqualsAndHashCode(callSuper = true)
@ApiModel("登录查询用户信息")
public class UserInfo extends User implements Serializable {
    private static final long serialVersionUID = -4788644254942504791L;
    @ApiModelProperty("用户名")
    private String username;
    @ApiModelProperty("用户编号")
    private String userUid;
    @ApiModelProperty("用户类型")
    private String userType;
    @ApiModelProperty("角色编号")
    private String roleID;
    @ApiModelProperty("项目编号")
    private String projectID;
    @ApiModelProperty("菜单列表")
    private List<MenuInfoVO> menuList;
    /**
     * 拥有权限集合
     */
    private Set<String> authoritySet;

    @Override
    @JsonIgnore
    public Collection<GrantedAuthority> getAuthorities() {
        if (authoritySet == null) {
            return null;
        }
        return authoritySet.stream().map(SimpleGrantedAuthority::new).collect(Collectors.toSet());
    }

    public Set<String> getAuthoritySet() {
        return authoritySet;
    }

    public void setAuthoritySet(Set<String> authoritySet) {
        this.authoritySet = authoritySet;
    }

    @Override
    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getUserUid() {
        return userUid;
    }

    public void setUserUid(String userUid) {
        this.userUid = userUid;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getRoleID() {
        return roleID;
    }

    public void setRoleID(String roleID) {
        this.roleID = roleID;
    }

    public String getProjectID() {
        return projectID;
    }

    public void setProjectID(String projectID) {
        this.projectID = projectID;
    }

    public List<MenuInfoVO> getMenuList() {
        return menuList;
    }

    public void setMenuList(List<MenuInfoVO> menuList) {
        this.menuList = menuList;
    }

    public UserInfo(String username, String password, Collection<? extends GrantedAuthority> authorities) {
        super(username, password, authorities);
    }

    public UserInfo(String username, String password, boolean enabled, boolean accountNonExpired, boolean credentialsNonExpired, boolean accountNonLocked, Collection<? extends GrantedAuthority> authorities) {
        super(username, password, enabled, accountNonExpired, credentialsNonExpired, accountNonLocked, authorities);
    }

}