package com.bto.oauth.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * 用户基本表-checked
 *
 * @TableName bto_user
 */
@Data
@ApiModel("用户基本表")
@TableName("bto_user")
public class User implements Serializable {

    private static final long serialVersionUID = 403504944725644525L;
    /**
     * 用户Uid
     */
    @Size(max = 70, message = "编码长度不能超过70")
    @ApiModelProperty("用户Uid")
    @Length(max = 70, message = "编码长度不能超过70")
    @TableField("user_uid")
    private String userUid;

    /**
     * 用户类型
     */
    @ApiModelProperty("用户Uid")
    @Length(max = 70, message = "编码长度不能超过70")
    @TableField("user_type")
    private String userType;

    /**
     * 用户名
     */
    @Size(max = 50, message = "编码长度不能超过50")
    @ApiModelProperty("用户名")
    @Length(max = 50, message = "编码长度不能超过50")
    @TableField("user_name")
    private String username;
    /**
     * 手机号码
     */
    @Size(max = 15, message = "编码长度不能超过15")
    @ApiModelProperty("手机号码")
    @Length(max = 15, message = "编码长度不能超过15")
    @TableField("user_phone")
    private String userPhone;
    /**
     * 用户邮箱
     */
    @Size(max = 255, message = "编码长度不能超过255")
    @ApiModelProperty("用户邮箱")
    @Length(max = 255, message = "编码长度不能超过255")
    @TableField("user_email")
    private String userEmail;
    /**
     * 用户密码
     */
    @Size(max = 255, message = "编码长度不能超过255")
    @ApiModelProperty("用户密码")
    @Length(max = 255, message = "编码长度不能超过255")
    @TableField("user_password")
    private String password;
    /**
     * 用户账号状态(0:正常，1：异常)
     */
    @NotNull(message = "[用户账号状态(0:正常，1：异常)]不能为空")
    @ApiModelProperty("用户账号状态(0:正常，1：异常)")
    @TableField("user_status")
    private Integer userStatus;
    /**
     * 角色id
     */
    @Size(max = 125, message = "编码长度不能超过125")
    @ApiModelProperty("角色id")
    @Length(max = 125, message = "编码长度不能超过125")
    @TableField("role_id")
    private String roleID;
    /**
     * 项目专项(1:户用，2：整县-河源)
     */
    @NotNull(message = "[项目专项(1:户用，2：整县-河源)]不能为空")
    @ApiModelProperty("项目专项(1:户用，2：整县-河源)")
    @TableField("project_special")
    private String projectID;
    /**
     * 创建者
     */
    @Size(max = 70, message = "编码长度不能超过70")
    @ApiModelProperty("创建者")
    @Length(max = 70, message = "编码长度不能超过70")
    @TableField("creator")
    private String creator;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间 ")
    @TableField("create_time")
    private Date createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField("update_time")
    private Date updateTime;
    /**
     * 更新者
     */
    @Size(max = 70, message = "编码长度不能超过70")
    @ApiModelProperty("更新者")
    @Length(max = 70, message = "编码长度不能超过70")
    @TableField("updater")
    private String updater;
    /**
     * 逻辑删除（0：正常，1：已删除）
     */
    @NotNull(message = "[逻辑删除（0：正常，1：已删除）]不能为空")
    @ApiModelProperty("逻辑删除（0：正常，1：已删除）")
    @TableField("is_deleted")
    private Integer isDeleted;
    /**
     * 合同id
     */
    @ApiModelProperty("合同id")
    @TableField("contract_id")
    private String contractId;
    @TableField(exist = false)
    private boolean accountNonExpired = true;
    @TableField(exist = false)
    private boolean accountNonLocked = true;
    @TableField(exist = false)
    private boolean credentialsNonExpired = true;
    @TableField(exist = false)
    private boolean enabled = true;
}