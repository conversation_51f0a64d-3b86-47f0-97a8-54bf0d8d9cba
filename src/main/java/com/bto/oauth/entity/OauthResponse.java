package com.bto.oauth.entity;

import com.bto.commons.pojo.vo.MenuInfoVO;
import com.bto.commons.pojo.dto.UserDTO;
import com.bto.oauth.custom.ResponseSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/05/31
 */
@EqualsAndHashCode(callSuper = true)
@JsonSerialize(using = ResponseSerializer.class)
@Data
public class OauthResponse extends OauthResult {
    private Object data;
    private UserDTO userInfo;
    private List<MenuInfoVO> menuList;
}