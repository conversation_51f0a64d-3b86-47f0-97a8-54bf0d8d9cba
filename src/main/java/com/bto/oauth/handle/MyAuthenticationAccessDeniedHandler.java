package com.bto.oauth.handle;

import com.bto.commons.response.ResultEnum;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 身份验证访问被拒绝处理程序
 * <AUTHOR>
 * @date 2023-07-27
 */
@Component
public class MyAuthenticationAccessDeniedHandler implements AccessDeniedHandler {

    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response, AccessDeniedException accessDeniedException) throws IOException, IOException {
        response.setStatus(Integer.parseInt(ResultEnum.ACCESS_REFUSED_NO_AUTHORITY.getCode()));
        response.setContentType("application/json;charset=utf-8");
        response.getWriter().write(ResultEnum.ACCESS_REFUSED_NO_AUTHORITY.getMessage());
    }
}