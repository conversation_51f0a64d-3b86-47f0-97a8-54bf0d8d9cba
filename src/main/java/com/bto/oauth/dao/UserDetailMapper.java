package com.bto.oauth.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bto.oauth.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/29 17:37
 */
@Mapper
public interface UserDetailMapper extends BaseMapper<User> {

    /**
     * 查询角色权限列表
     * @param roleID
     * @return List<String>
     */
    List<String> selectAuthorityList(@Param("roleID") String roleID);
}