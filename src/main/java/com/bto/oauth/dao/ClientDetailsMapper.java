package com.bto.oauth.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bto.oauth.entity.ClientDetails;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/8/3 10:05
 */
@Mapper
public interface ClientDetailsMapper extends BaseMapper<ClientDetails> {

    /**
     * 查询客户端信息
     * @param clientId
     * @return
     */
    HashMap<String,String> selectClientInfo(@Param("clientId") String clientId);

    /**
     * 查询客户端权限
     * @param clientId
     * @return
     */
    Set<String> selectAuthorities(@Param("clientId") String clientId);

    /**
     * 查询用户权限
     * @param userUid
     * @return
     */
    Set<String> selectAuthorityList(String userUid);
}
