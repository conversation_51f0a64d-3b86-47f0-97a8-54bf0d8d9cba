package com.bto.oauth.aspect;

import com.alibaba.fastjson.JSON;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.response.ResultEnum;
import com.bto.oauth.entity.OauthResponse;
import com.bto.oauth.entity.UserLogin;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.http.ResponseEntity;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.stereotype.Component;

import static com.bto.commons.constant.Constant.*;
import static com.bto.commons.response.ResultEnum.ACCESS_AUTHORIZATION_FAILED;

/**
 * <AUTHOR>
 * @date 2023/3/31 9:53
 */
@Component
@Aspect
@Slf4j
public class AuthTokenAspect {

    @Around("execution(* org.springframework.security.oauth2.provider.endpoint.TokenEndpoint.postAccessToken(..))")
    public Object handleControllerMethod(ProceedingJoinPoint pjp) throws Throwable {
          final String invalidAccessToken = "No Auth";
          final String invalidRefreshToken = "Invalid refresh token";
        // 放行
        OauthResponse response = new OauthResponse();
        Object[] args = pjp.getArgs();
        UserLogin userLogin= JSON.parseObject(JSON.toJSONString(args[1]), UserLogin.class);
        try {
            Object proceed = pjp.proceed();
            if (proceed != null) {
                ResponseEntity<OAuth2AccessToken> responseEntity = (ResponseEntity<OAuth2AccessToken>) proceed;
                OAuth2AccessToken body = responseEntity.getBody();
                if (responseEntity.getStatusCode().is2xxSuccessful() && PASSWORD_MODE.equals(userLogin.getGrantType())) {
                    response.setStatus(ResultEnum.LOGIN_SUCCESS.getCode());
                    response.setMessage(ResultEnum.LOGIN_SUCCESS.getMessage());
                    response.setData(body);
                } else if (responseEntity.getStatusCode().is2xxSuccessful() && REFRESH_MODE.equals(userLogin.getGrantType())) {
                    response.setStatus(ResultEnum.REFRESH_TOKEN_SUCCESS.getCode());
                    response.setMessage(ResultEnum.REFRESH_TOKEN_SUCCESS.getMessage());
                    response.setData(body);
                }else if (responseEntity.getStatusCode().is2xxSuccessful() && CLIENT_MODE.equals(userLogin.getGrantType())) {
                    response.setStatus(ResultEnum.LOGIN_SUCCESS.getCode());
                    response.setMessage(ResultEnum.LOGIN_SUCCESS.getMessage());
                    response.setData(body);
                }else {
                    log.error("error:{}", responseEntity.getStatusCode());
                    response.setStatus(ACCESS_AUTHORIZATION_FAILED.getCode());
                    response.setMessage(ACCESS_AUTHORIZATION_FAILED.getMessage());
                    throw new BusinessException(ACCESS_AUTHORIZATION_FAILED);

                }
            }
        } catch (RuntimeException e) {
            e.printStackTrace();
            if (e.getMessage() == null) {
                throw new BusinessException(ResultEnum.USER_NON_EXISTENT);
            } else if (e.getMessage().contains(invalidRefreshToken)) {
                throw new BusinessException(ResultEnum.USER_NON_EXISTENT);
            } else if (e.getMessage().contains(invalidAccessToken)) {
                throw new BusinessException(ResultEnum.USER_NON_EXISTENT);
            } else if (e instanceof BusinessException) {
                throw e;
            } else  {
                throw new BusinessException(ResultEnum.SYSTEM_RUNTIME_FAILED);
            }
        }
        return ResponseEntity
                .status(200)
                .body(response);
    }

}