package com.bto;

import com.github.jeffreyning.mybatisplus.conf.EnableMPP;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2023/5/17 16:35
 */


@Configuration
@EnableMPP
@SpringBootApplication(scanBasePackages = {"com.bto.*"})
@MapperScan(basePackages ={ "com.bto.*.dao"})
public class BtoLocalBusinessApplication {

    public static void main(String[] args) {
        SpringApplication springApplication = new SpringApplication(BtoLocalBusinessApplication.class);
        springApplication.run(args);
    }

}
