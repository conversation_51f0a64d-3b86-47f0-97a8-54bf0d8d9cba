<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bto.system.dao.OperatorMapper">
    <sql id="userInfo">
        <if test="userInfo.plantList!=null and userInfo.plantList.size()>0">
            and bpb.plant_uid in
            <foreach collection="userInfo.plantList" item="plantUid" open="(" close=")" separator=",">
                #{plantUid}
            </foreach>
        </if>
        <if test="userInfo.projectList!=null and userInfo.projectList.size()>0">
            and bpb.project_special in
            <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
                #{projectID}
            </foreach>
        </if>
    </sql>
    <resultMap id="edgeServerSIMCardMap" type="com.bto.commons.pojo.entity.EdgeServerSIMCard">
        <result column="plant_uid" property="plantUid"/>
        <result column="plant_name" property="plantName"/>
        <result column="imei" property="IMEI"/>
        <result column="user_name" property="userName"/>
        <result column="device_id" property="operatorSN"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="signal_strength" property="signalStrength"/>
    </resultMap>
    <select id="getEdgeServerSIMCards" resultMap="edgeServerSIMCardMap">
        select vup.plant_uid,
        vup.plant_name,
        bd.imei,
        vup.user_name,
        bd.device_id,
        bd.start_time,
        bd.end_time,
        bil.signal_strength
        from v_user_plant vup
        left join bto_device bd on vup.plant_uid = bd.plant_uid
        left join bto_inverter_latest bil on vup.plant_uid = bil.plant_uid
        <where>
            and vup.is_deleted='0'
            and bd.is_deleted='0'
            and bd.imei != ''
            and bd.device_type = '2'
            <if test="plantUid!= null and plantUid!=''">
                and vup.plant_uid = #{plantUid}
            </if>
        </where>
        group by bd.imei
    </select>
    <select id="getOperatorInfo" resultType="com.bto.commons.pojo.vo.OperatorListVO">
        SELECT
        besr.plant_uid plantUid,
        bpb.plant_name plantName,
        bd.imei,
        bd.iccid,
        bd.device_id operatorSn,
        besr.collect_date collectDate,
        besr.generation_electricity generationElectricity,
        besr.use_electricity useElectricity,
        besr.deprecated_generate_electricity selfUseElectricity,
        besr.buy_electricity buyElectricity,
        besr.sell_electricity sellelectricity,
        bd.device_id operatorSn,
        besr.apv,
        besr.bpv,
        besr.cpv,
        besr.dpv,
        besr.impep,
        besr.expep,
        besr.state,
        bd.device_id operatorSn,
        bd.create_time createTime,
        bd.update_time updateTime,
        bd.enable,
        bd.enable enableStr,
        bd.`status`,
        bd.`status` statusStr
        FROM bto_edge_server_realtime besr
        LEFT JOIN bto_device bd ON besr.plant_uid= bd.plant_uid
        LEFT JOIN bto_plant_base bpb ON besr.plant_uid= bpb.plant_uid
        <where>
            <include refid="userInfo"/>
            AND bd.device_type = 2
            AND bd.is_deleted = 0
            <if test="query.collectDate !=null and query.collectDate!='' ">
                AND besr.collect_date = #{query.collectDate}
            </if>
            <if test="query.plantName !=null and query.plantName!='' ">
                AND bpb.plant_name like concat('%',#{query.plantName},'%')
            </if>
            <if test="query.imei !=null and query.imei != '' ">
                AND bd.imei = #{query.imei}
            </if>
            <if test="query.operatorSn !='' and query.operatorSn !=null">
                AND bd.device_id LIKE concat('%',#{query.operatorSn})
            </if>
            <if test="query.plantUid != null and query.plantUid !=''">
                AND bpb.plant_uid = #{query.plantUid}
            </if>
            <if test="query.status != null and query.status !='' ">
                AND bd.status = #{query.status}
            </if>
            <if test="query.enable !=null and query.enable !='' ">
                AND bd.enable = #{query.enable}
            </if>
        </where>
        ORDER BY ${query.order}
        <choose>
            <when test="query.isAsc">
                asc
            </when>
            <otherwise>
                desc
            </otherwise>
        </choose>
    </select>
    <select id="getIotCardByPage" resultType="com.bto.commons.pojo.vo.IotCardInfoVO">
        WITH
        cimi_date AS(
        SELECT plant_uid,
        imei,
        cimi,
        iccid,
        start_time,
        DATE(end_time) endTime,
        IF(DATE(end_time) &lt;= curdate(),0,1) cardStatus,
        IF(DATE(end_time) &lt;= curdate(),0,1) cardStatusStr,
        TIMESTAMPDIFF(day,curdate(),end_time) as remainingDays
        FROM bto_device WHERE is_deleted = 0 AND ((device_type = 2 AND manufacturer = 'ZBO') OR (device_type = 1 AND
        imei = '')))
        SELECT t1.*,
        t2.user_name,
        t2.user_phone phoneNumber,
        t2.plant_name,
        t2.create_time
        FROM cimi_date t1 LEFT JOIN v_user_plant t2 ON t1.plant_uid = t2.plant_uid
        <where>
            <if test="userInfo.plantList!=null and userInfo.plantList.size()>0">
                and t2.plant_uid in
                <foreach collection="userInfo.plantList" item="plantUid" open="(" close=")" separator=",">
                    #{plantUid}
                </foreach>
            </if>
            <if test="userInfo.projectList!=null and userInfo.projectList.size()>0">
                and t2.project_special in
                <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
                    #{projectID}
                </foreach>
            </if>
            <if test="query.plantName != null and query.plantName != '' ">
                AND t2.plant_name LIKE concat('%',#{query.plantName},'%')
            </if>
            <if test="query.plantUid != null and query.plantUid != '' ">
                AND t2.plant_uid = #{query.plantUid}
            </if>
            <if test="query.iccid != null and query.iccid != '' ">
                AND iccid = #{query.iccid}
            </if>
            <if test="query.cimi != null and query.cimi != '' ">
                AND cimi = #{query.cimi}
            </if>
            <if test="query.cardStatus != null and query.cardStatus != '' ">
                AND cardStatus = #{query.cardStatus}
            </if>
            <if test="query.remainingDays != null and query.remainingDays != '' ">
                AND remainingDays &lt;= #{query.remainingDays}
            </if>
            <if test="query.phoneNumber != null and query.phoneNumber != '' ">
                AND user_phone LIKE concat('%',#{query.phoneNumber}, '%')
            </if>
        </where>
        ORDER BY ${query.order}
        <choose>
            <when test="query.isAsc">
                asc
            </when>
            <otherwise>
                desc
            </otherwise>
        </choose>
    </select>
    <select id="getSmsInfo" resultType="com.bto.commons.pojo.dto.SmsInfoDTO">
        select bd.cimi, v.user_name, v.user_phone
        from bto_device bd
        LEFT JOIN v_user_plant v on bd.plant_uid = v.plant_uid
        where bd.cimi in
        <foreach collection="cimiList" separator="," open="(" close=")" item="cimi">
            #{cimi}
        </foreach>
    </select>
</mapper>