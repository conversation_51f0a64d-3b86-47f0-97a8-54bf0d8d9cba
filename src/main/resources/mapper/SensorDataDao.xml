<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bto.system.dao.SensorDataDao">

    <resultMap type="com.bto.commons.pojo.entity.SensorDataEntity" id="sensorDataMap">
        <result property="plantUid" column="plant_uid"/>
        <result property="sensorId" column="sensor_id"/>
        <result property="smokeConcentr" column="smoke_concentr"/>
        <result property="temp" column="temp"/>
        <result property="humidity" column="humidity"/>
        <result property="alarmStatus" column="alarm_status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <select id="selectLatest" resultType="com.bto.commons.pojo.entity.SensorDataEntity">
        SELECT sensor_id,
        MAX(update_time) AS update_time,
        plant_uid,
        smoke_concentr,
        temp,
        humidity,
        alarm_status,
        create_time
        FROM sensor_data
        <where>
            <if test="deviceIds!=null and deviceIds.size()>0">
                and sensor_id in
                <foreach collection="deviceIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY sensor_id
        ORDER BY update_time DESC;
    </select>

</mapper>