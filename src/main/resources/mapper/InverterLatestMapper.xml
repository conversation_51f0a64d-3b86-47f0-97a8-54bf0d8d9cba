<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bto.system.dao.InverterLatestMapper">
    <sql id="userInfo">
        <if test="userInfo.plantList!=null and userInfo.plantList.size()>0">
            and bpb.plant_uid in
            <foreach collection="userInfo.plantList" item="plantUid" open="(" close=")" separator=",">
                #{plantUid}
            </foreach>
        </if>
        <if test="userInfo.projectList!=null and userInfo.projectList.size()>0">
            and bpb.project_special in
            <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
                #{projectID}
            </foreach>
        </if>
    </sql>
    <resultMap id="InverterInfoVOMap" type="com.bto.commons.pojo.dto.InverterInfoDTO">
        <id column="plant_uid" property="plantUid"/>
        <result column="inverter_sn" property="deviceId"/>
        <result column="module" property="module"/>
        <result column="project_special" property="projectSpecial"/>
        <result column="device_address" property="deviceAddress"/>
        <result column="device_pc" property="devicePc"/>
        <result column="cimi" property="cimi"/>
        <result column="iccid" property="iccid"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="software_version" property="softwareVersion"/>
        <result column="display_version" property="displayVersion"/>
        <result column="control_version" property="controlVersion"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="receive_type" property="receiveType"/>
        <result column="plant_name" property="plantName"/>
        <result column="inverter_status" property="inverterStatus"/>
        <result column="power" property="power"/>
        <result column="today_electricity" property="todayElectricity"/>
        <result column="month_electricity" property="monthElectricity"/>
        <result column="year_electricity" property="yearElectricity"/>
        <result column="total_electricity" property="totalElectricity"/>
    </resultMap>

    <!--查询逆变器列表-->
    <select id="getInverters" resultMap="InverterInfoVOMap">
        select *
        from v_inverter_electricity bil
        left join bto_plant_base bpb on bil.plant_uid = bpb.plant_uid
        <where>
            <trim suffixOverrides="AND | OR">
                bpb.is_deleted = 0 and bil.is_deleted != 1
                <include refid="userInfo" />
                <if test="inverterInfoDTO.plantName!='' and inverterInfoDTO.plantName != null">
                    and bpb.plant_name like concat('%',#{inverterInfoDTO.plantName},'%')
                </if>
                <if test="inverterInfoDTO.plantUid!='' and inverterInfoDTO.plantUid!=null">
                    and bpb.plant_uid=#{inverterInfoDTO.plantUid}
                </if>
                <if test="inverterInfoDTO.deviceId!='' and inverterInfoDTO.deviceId!=null">
                    and bil.inverter_sn like concat('%',#{inverterInfoDTO.deviceId},'%')
                </if>
                <if test="inverterInfoDTO.multiInverterStatus!=null and inverterInfoDTO.multiInverterStatus.size()>0">
                    and bil.inverter_status in
                    <foreach collection="inverterInfoDTO.multiInverterStatus" item="inverterStatusEnum" index="index"
                             open="(" close=")" separator="," >
                        #{inverterStatusEnum}
                    </foreach>
                </if>
            </trim>
            order by bpb.create_time desc
        </where>
    </select>

    <select id="getInverterRealTimeData" resultType="com.bto.commons.pojo.vo.InverterRealTimeInfoVO">
        SELECT
            t1.inverter_sn inverterSN,
            t1.init_time initTime,
            t1.ipv1,
            t1.ipv2,
            t1.ipv3,
            t1.ipv4,
            t1.ipv5,
            t1.ipv6,
            t1.ipv7,
            t1.ipv8,
            t1.ipv9,
            t1.ipv10,
            t1.ipv11,
            t1.ipv12,
            t1.vpv1,
            t1.vpv2,
            t1.vpv3,
            t1.vpv4,
            t1.vpv5,
            t1.vpv6,
            t1.vpv7,
            t1.vpv8,
            t1.vpv9,
            t1.vpv10,
            t1.vpv11,
            t1.vpv12,
            t1.iac1,
            t1.iac2,
            t1.iac3,
            t1.vac1,
            t1.vac2,
            t1.vac3,
            t1.fac1,
            t1.fac2,
            t1.fac3,
            t1.power,
            concat( t2.pv1_dc1, ' - ', t2.pv1_dc2, ' - ', t2.pv1_dc3, ' - ', t2.pv1_dc4 ) pv1,
            concat( t2.pv2_dc1, ' - ', t2.pv2_dc2, ' - ', t2.pv2_dc3, ' - ', t2.pv2_dc4 ) pv2,
            concat( t2.pv3_dc1, ' - ', t2.pv3_dc2, ' - ', t2.pv3_dc3, ' - ', t2.pv3_dc4 ) pv3,
            concat( t2.pv4_dc1, ' - ', t2.pv4_dc2, ' - ', t2.pv4_dc3, ' - ', t2.pv4_dc4 ) pv4,
            concat( t2.pv5_dc1, ' - ', t2.pv5_dc2, ' - ', t2.pv5_dc3, ' - ', t2.pv5_dc4 ) pv5,
            concat( t2.pv6_dc1, ' - ', t2.pv6_dc2, ' - ', t2.pv6_dc3, ' - ', t2.pv6_dc4 ) pv6,
            concat( t2.pv7_dc1, ' - ', t2.pv7_dc2, ' - ', t2.pv7_dc3, ' - ', t2.pv7_dc4 ) pv7,
            concat( t2.pv8_dc1, ' - ', t2.pv8_dc2, ' - ', t2.pv8_dc3, ' - ', t2.pv8_dc4 ) pv8,
            concat( t2.pv9_dc1, ' - ', t2.pv9_dc2, ' - ', t2.pv9_dc3, ' - ', t2.pv9_dc4 ) pv9,
            concat( t2.pv10_dc1, ' - ',t2.pv10_dc2, ' - ', t2.pv10_dc3, ' - ', t2.pv10_dc4 ) pv10,
            concat( t2.pv11_dc1, ' - ',t2.pv11_dc2, ' - ', t2.pv11_dc3, ' - ', t2.pv11_dc4 ) pv11,
            concat( t2.pv12_dc1, ' - ',t2.pv12_dc2, ' - ', t2.pv12_dc3, ' - ', t2.pv12_dc4 ) pv12,
            t1.today_electricity todayElectricity,
            t1.total_electricity totalElectricity,
            t1.update_time updateTime
        FROM
            bto_inverter_${tableSuffix} t1
                LEFT JOIN bto_inverter_component t2 ON t1.inverter_sn = t2.inverter_sn
        <where>
            <if test="query.inverterSN != ''">
                AND t1.inverter_sn = #{query.inverterSN}
            </if>
            <if test="query.date !='' and query.date != null">
                AND DATE(t1.init_time) = #{query.date}
            </if>
        </where>

        ORDER BY
            t1.init_time DESC
    </select>
    <select id="selectPvNum" resultType="java.lang.Integer">
        SELECT pv pvNum
        FROM v_inverter_pv WHERE device_id = #{inverterSN}
    </select>
</mapper>