<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bto.system.dao.MenuMapper">

    <select id="getMenuList" parameterType="com.bto.commons.pojo.vo.RequireParamsDTO"
            resultType="com.bto.commons.pojo.vo.MenuInfoVO">
        SELECT
            id,
            pid,
            menu_name label,
            menu_url path,
            auth,
            type,
            open_style openStyle,
            icon,
            creator,
            create_time createTime,
            sort,
            hidden,
            title,
            breadcrumb,
            affix,
            always_show alwaysShow,
            active_menu activeMenu,
            keep_alive keepAlive,
            version,
            component,
            redirect
        FROM bto_menu
        <where>
            is_deleted = 0
            AND id IN (
            SELECT menu_id
            FROM bto_role_has_menu
            WHERE role_id = #{roleID})
        </where>
    </select>
    <select id="getMenUidList" resultType="java.lang.Long">
        SELECT menu_id
        FROM bto_role_has_menu
        <where>
            <if test="deletedField">
                is_deleted = 0
            </if>
            and role_id = #{roleId}
        </where>
    </select>
    <select id="getDelMenUidList" resultType="java.lang.Long">
        SELECT menu_id
        FROM bto_role_has_menu
        WHERE is_deleted = 1
          and role_id = #{roleId}
    </select>
    <select id="getMenu" resultType="com.bto.commons.pojo.entity.Menu">
        SELECT id
        FROM bto_menu
        WHERE pid = #{pid}
        ORDER BY id DESC
        LIMIT 1
    </select>
    <select id="getMenuListByRole" resultType="com.bto.commons.pojo.vo.MenuInfoVO">
        SELECT id,
        pid,
        menu_name label,
        menu_url path,
        auth,
        type,
        open_style openStyle,
        icon,
        creator,
        create_time createTime,
        sort,
        hidden,
        title,
        breadcrumb,
        affix,
        always_show alwaysShow,
        active_menu activeMenu,
        keep_alive keepAlive,
        version,
        component,
        redirect
        FROM bto_menu
        <where>
            is_deleted = 0 AND type &lt;&gt; 2
            AND
            id IN
            <foreach collection="menuArray" item="menUid" open="(" separator="," close=")">
                #{menUid}
            </foreach>
        </where>
    </select>
</mapper>