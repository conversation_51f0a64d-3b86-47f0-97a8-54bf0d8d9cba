<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bto.system.dao.AlarmManageMapper">
    <sql id="userInfo">
        <if test="userInfo.plantList!=null and userInfo.plantList.size()>0">
            and plant_uid in
            <foreach collection="userInfo.plantList" item="plantUid" open="(" close=")" separator=",">
                #{plantUid}
            </foreach>
        </if>
        <if test="userInfo.projectList!=null and userInfo.projectList.size()>0">
            and project_special in
            <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
                #{projectID}
            </foreach>
        </if>
    </sql>
    <select id="getPlantAlarmInfoList" resultType="com.bto.commons.pojo.entity.ViewPlantAlarm">
        SELECT * FROM(
        SELECT id alarm_id,
        `source`,
        plant_uid plantUid,
        plant_name plantName,
        device_id deviceId,
        device_type deviceType,
        start_time startTime,
        alarm_mean alarmMean,
        `status`,
        project_special projectSpecial,
        project_special projectName
        FROM v_plant_alarm
        <where>
            <include refid="userInfo"/>
        </where>
        GROUP BY device_id,alarm_mean,start_time
        <trim prefix="having" prefixOverrides="and">
            <if test="query.deviceId!=null and query.deviceId!=''">
                AND device_id LIKE concat('%', #{query.deviceId}, '%')
            </if>
            <if test="query.plantName!=null and query.plantName!=''">
                AND plant_name LIKE concat('%', #{query.plantName}, '%')
            </if>
            <if test="query.deviceType!=null and query.deviceType!=''">
                AND device_type = #{query.deviceType}
            </if>
            <if test="query.startAlarmBeginTime != null and query.startAlarmBeginTime!=''">
                AND start_time LIKE concat('%', #{query.startAlarmBeginTime}, '%')
            </if>
            <if test="query.alarmInfo!=null and query.alarmInfo!=''">
                AND alarm_mean LIKE concat('%', #{query.alarmInfo}, '%')
            </if>
            <if test="query.source!=null and query.source!=''">
                AND `source` LIKE concat('%', #{query.source}, '%')
            </if>
        </trim>
        ORDER BY ${query.order}
        <choose>
            <when test="query.isAsc">
                asc
            </when>
            <otherwise>
                desc
            </otherwise>
        </choose>
        ) t
        <if test="query.isDistinct">
            GROUP BY t.plantUid
        </if>
    </select>

    <select id="getAlarmInfoNum" resultType="java.lang.Integer">
        -- SELECT COUNT(1)
        -- FROM bto_inverter_alarm t1
        -- where status = '0' AND alarm_info NOT LIKE'PV%'
        -- AND
        -- EXISTS
        -- (SELECT device_id
        -- FROM bto_device
        -- WHERE is_deleted = 0
        -- AND device_type = 1
        -- AND device_id = t1.inverter_sn
        SELECT count(*) from v_plant_alarm
        <where>
            <include refid="userInfo"/>
        </where>
    </select>
    <select id="getAlarmPlantNum" resultType="java.lang.Integer">
        SELECT count(1) FROM bto_plant_base
        <where>
            is_deleted = '0' and plant_status = '2'
            <include refid="userInfo"/>
        </where>
    </select>


</mapper>