<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bto.system.dao.InverterMapper">

    <sql id="userInfo">
        <if test="userInfo.plantList!=null and userInfo.plantList.size()>0">
            and bpb.plant_uid in
            <foreach collection="userInfo.plantList" item="plantUid" open="(" close=")" separator=",">
                #{plantUid}
            </foreach>
        </if>
        <if test="userInfo.projectList!=null and userInfo.projectList.size()>0">
            and bpb.project_special in
            <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
                #{projectID}
            </foreach>
        </if>
    </sql>
    <select id="getInverterDataByDay" resultType="com.bto.commons.pojo.vo.InverterRealTimeInfoVO">
        SELECT inverter_sn       inverterSN,
               init_time         initTime,
               ipv1,
               ipv2,
               ipv3,
               ipv4,
               ipv5,
               ipv6,
               ipv7,
               ipv8,
               ipv9,
               ipv10,
               ipv11,
               ipv12,
               vpv1,
               vpv2,
               vpv3,
               vpv4,
               vpv5,
               vpv6,
               vpv7,
               vpv8,
               vpv9,
               vpv10,
               vpv11,
               vpv12,
               iac1,
               iac2,
               iac3,
               vac1,
               vac2,
               vac3,
               fac1,
               fac2,
               fac3,
               power,
               pv1_power power1,
               pv2_power power2,
               pv3_power power3,
               pv4_power power4,
               pv5_power power5,
               pv6_power power6,
               pv7_power power7,
               pv8_power power8,
               pv9_power power9,
               pv10_power power10,
               pv11_power power11,
               pv12_power power12,
               today_electricity todayElectricity,
               total_electricity totalElectricity,
               update_time       updateTime,
               temp temp
        FROM ${tableName}
        WHERE inverter_sn = #{inverterSN}
        order by initTime desc
    </select>
    <select id="getElectricityList" resultType="com.bto.commons.pojo.vo.ChartElectricityInfoVO">
        SELECT
        SUM(electricity) electricity
        <if test="date.length()==0">
            ,LEFT(collect,4) collectDate
        </if>
        <if test="date.length()==4">
            ,LEFT(collect,7) collectDate
        </if>
        <if test="date.length()==7">
            ,LEFT(collect,10) collectDate
        </if>
        FROM bto_plant_day
        <where>
            plant_uid = #{plantUid}
            <if test="date.length()==4">
                AND LEFT(collect,4) = #{date}
            </if>
            <if test="date.length()==7">
                AND LEFT(collect,7) = #{date}
            </if>
        </where>
        <if test="date==null or date==''.toString">
            GROUP BY LEFT(collect,4)
        </if>
        <if test="date.length()==4">
            GROUP BY LEFT(collect,7)
        </if>
        <if test="date.length()==7">
            GROUP BY LEFT(collect,10)
        </if>
    </select>
    <select id="getPvModuleFields" resultType="java.lang.String">
        SELECT fields FROM bto_inverter_circuit_info WHERE pv &lt;= (
            SELECT pv FROM v_inverter_pv WHERE device_id = #{inverterSN}
        ) ORDER BY fields ASC
    </select>
    <select id="getInverterInfoChart" resultType="java.util.LinkedHashMap">
        SELECT
        inverter_sn inverterSN,
        power,
        today_electricity todayElectricity,
        month_electricity monthElectricity,
        year_electricity yearElectricity,
        total_electricity totalElectricity,
        temp temperature,
        init_time initTime,
        <foreach collection="pvModuleFields" item="field" separator=",">
            ${field}/100 ${field}
        </foreach>
        FROM ${tableName}
        <where>
            inverter_sn = #{inverterSN}
        </where>
    </select>
    <select id="getInverterDetails" resultType="com.bto.commons.pojo.vo.InverterDetailsVO">
        SELECT
            v1.device_id inverterSN,
            v1.plant_name plantName,
            v1.manufacturer,
            v1.module,
            v1.software_version softwareVersion,
            v1.display_version displayVersion,
            v1.control_version controlVersion,
            v1.create_time createTime,
            v1.device_address address,
            v2.inverter_status inverterStatus,
            v2.`power`,
            v2.signal_strength signalStrength,
            v2.today_electricity todayElectricity,
            v2.month_electricity monthElectricity,
            v2.year_electricity yearElectricity,
            v2.total_electricity totalElectricity,
            v2.update_time updateTime
        FROM v_plant_inverter v1
                 LEFT JOIN v_inverter_electricity v2 ON v1.device_id = v2.inverter_sn  AND v2.deleted = 0
        WHERE v1.device_id = #{inverterSN}
    </select>
    <select id="getTotalPowerList" resultType="com.bto.commons.pojo.vo.PlantPowerVO">
        SELECT SUM(power) power,
               init_time  initTime,
               'total'    inverterSN
        FROM ${tableName} t1 WHERE EXISTS(
                                           SELECT inverter_sn FROM bto_inverter_latest
                                           WHERE plant_uid = #{plantUid}
                                             AND inverter_sn = t1.inverter_sn)
        GROUP BY init_time
        ORDER BY init_time DESC
    </select>
    <select id="getPlantMaxPower" resultType="java.lang.String">
        SELECT max(t.sumPower)
        FROM (SELECT init_time, sum(power) sumPower
        FROM ${tableName} WHERE
        inverter_sn IN
        <foreach collection="inverterSnList" item="inverterSn" open="(" close=")" separator=",">
            #{inverterSn}
        </foreach>
        GROUP BY init_time) t
    </select>
    <select id="selectInverterInfoList" resultType="com.bto.commons.pojo.vo.InverterInfoVO">
        SELECT
        bil.plant_uid plantUid,
        bil.inverter_sn inverterSn,
        bil.power,
        bil.today_electricity todayElectricity,
        bil.month_electricity monthElectricity,
        bil.year_electricity yearElectricity,
        bil.total_electricity totalElectricity,
        bil.inverter_status inverterStatus,
        bil.project_special projectSpecial,
        bpb.plant_name plantName,
        bpb.plant_capacity plantCapacity,
        bpb.user_uid userUid,
        bpb.create_time createTime,
        bpb.update_time updateTime
        FROM v_inverter_electricity bil
        LEFT JOIN bto_plant_base bpb on bil.plant_uid = bpb.plant_uid
        <where>
            bpb.is_deleted = 0 and bil.is_deleted != 1
            <include refid="userInfo" />
            <if test="query.plantName!='' and query.plantName != null">
                and bpb.plant_name like concat('%',#{query.plantName},'%')
            </if>
            <if test="query.plantUid !='' and query.plantUid !=null">
                and bpb.plant_uid=#{query.plantUid}
            </if>
            <if test="query.deviceId !='' and query.deviceId !=null">
                and bil.inverter_sn like concat('%',#{query.deviceId},'%')
            </if>
            <if test="query.multiInverterStatus !=null and query.multiInverterStatus.size()>0">
                and bil.inverter_status in
                <foreach collection="query.multiInverterStatus" item="inverterStatusEnum" index="index"
                         open="(" close=")" separator="," >
                    #{inverterStatusEnum}
                </foreach>
            </if>
            ORDER BY ${query.order}
            <choose>
                <when test="query.isAsc">
                    asc
                </when>
                <otherwise>
                    desc
                </otherwise>
            </choose>
        </where>
    </select>
    <select id="getInverterRatedCapacityByModel" resultType="java.lang.Integer">
        SELECT device_capacity
        FROM bto_device_type
        WHERE device_type = '1'
          AND model = #{model}
        GROUP BY 'model'
    </select>
</mapper>