<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bto.oauth.dao.UserDetailMapper">

    <select id="selectAuthorityList" resultType="java.lang.String">
        select auth
        from bto_menu t1
        left join bto_role_has_menu t2 on t1.id = t2.menu_id
        where t2 .role_id = #{roleID}
          and t1.is_deleted = 0 and t2.is_deleted = 0
    </select>
</mapper>