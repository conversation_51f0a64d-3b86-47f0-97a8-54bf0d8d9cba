<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bto.system.dao.FunctionalInstrumentDao">

    <resultMap type="com.bto.commons.pojo.entity.FunctionalInstrumentEntity" id="functionalInstrumentMap">
        <result property="deviceId" column="device_id"/>
        <result property="electricity" column="electricity"/>
        <result property="initTime" column="init_time"/>
        <result property="apv" column="apv"/>
        <result property="bpv" column="bpv"/>
        <result property="cpv" column="cpv"/>
        <result property="aac" column="aac"/>
        <result property="bac" column="bac"/>
        <result property="cac" column="cac"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <select id="page" resultType="com.bto.commons.pojo.entity.FunctionalInstrumentEntity">
        WITH t_rank AS (SELECT *,
        RANK() OVER ( PARTITION BY device_id ORDER BY create_time DESC ) AS `rank`
        FROM bto_functional_instrument)
        SELECT *
        FROM t_rank
        <where>
            AND `rank` = 1
            <if test="query.deviceId!=null and query.deviceId.size()>0">
                and device_id IN
                <foreach collection="query.deviceId" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY create_time desc
    </select>

</mapper>