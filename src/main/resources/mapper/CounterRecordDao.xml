<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bto.system.dao.CounterRecordDao">

    <resultMap type="com.bto.commons.pojo.entity.CounterRecordEntity" id="counterMap">
        <result property="id" column="id"/>
        <result property="counterId" column="counter_id"/>
        <result property="initTime" column="init_time"/>
        <result property="overtension" column="overtension"/>
        <result property="undervoltage" column="undervoltage"/>
        <result property="drainGrade" column="drain_grade"/>
        <result property="lr1Sv" column="lr1_sv"/>
        <result property="lr1Time" column="lr1_time"/>
        <result property="lr2Sv" column="lr2_sv"/>
        <result property="lr2Time" column="lr2_time"/>
        <result property="lr3Sv" column="lr3_sv"/>
        <result property="electrifyStatus" column="electrify_status"/>
        <result property="alarmType" column="alarm_type"/>
        <result property="alarmTime" column="alarm_time"/>
        <result property="deviceType" column="device_type"/>
        <result property="status" column="status"/>
        <result property="alarmId" column="alarm_id"/>
        <result property="vac1" column="vac1"/>
        <result property="vac2" column="vac2"/>
        <result property="vac3" column="vac3"/>
        <result property="iac1" column="iac1"/>
        <result property="iac2" column="iac2"/>
        <result property="iac3" column="iac3"/>
        <result property="drainCurrent" column="drain_current"/>
        <result property="currentN" column="current_n"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>



    <select id="getLatestPage" resultType="com.bto.commons.pojo.vo.CounterRecordVO">
        WITH LatestData AS
        (SELECT *,ROW_NUMBER() OVER (PARTITION BY counter_id ORDER BY init_time DESC) AS row_num FROM bto_counter)
        SELECT t1.*,t2.device_name AS counter_name
        FROM LatestData t1
        left join bto_device t2 on t1.counter_id = t2.device_id and t2.is_deleted = 0
        <where>
          row_num = 1
            <if test="query.counterName !=null and query.counterName != ''">
                and t2.device_name like concat('%',#{query.counterName},'%')
            </if>
        </where>


    </select>

</mapper>