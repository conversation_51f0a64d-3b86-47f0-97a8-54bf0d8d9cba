<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bto.system.dao.PdArchivesDao">

    <resultMap type="com.bto.commons.pojo.entity.PdArchivesEntity" id="pdArchivesMap">
        <result property="pdId" column="pd_id"/>
        <result property="pdName" column="pd_name"/>
        <result property="pdPhotoUrl" column="pd_photo_url"/>
        <result property="pdType" column="pd_type"/>
        <result property="pdModule" column="pd_module"/>
        <result property="pdAddress" column="pd_address"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="userInfo">
        <if test="userInfo.plantList!=null and userInfo.plantList.size()>0">
            and bpb.plant_uid in
            <foreach collection="userInfo.plantList" item="plantUid" open="(" close=")" separator=",">
                #{plantUid}
            </foreach>
        </if>
        <if test="userInfo.projectList!=null and userInfo.projectList.size()>0">
            and bpb.project_special in
            <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
                #{projectID}
            </foreach>
        </if>
    </sql>

    <select id="tree" resultType="com.bto.commons.pojo.vo.PlantWithArchiveVO">
        SELECT
        t1.*,
        t2.plant_uid,
        t2.plant_name
        FROM
        bto_device bpb
        LEFT JOIN pd_archives t1 ON bpb.device_id = t1.pd_id
        LEFT JOIN bto_plant_base t2 ON bpb.plant_uid = t2.plant_uid
        <where>
            device_type = 11
            <include refid="userInfo"></include>
        </where>

    </select>

</mapper>