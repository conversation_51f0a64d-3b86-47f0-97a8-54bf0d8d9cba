<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bto.system.dao.ProjectMapper">

    <select id="getProjectSpecialInfoList" parameterType="com.bto.commons.pojo.vo.RequireParamsDTO"
            resultType="com.bto.commons.pojo.vo.ProjectInfoVO">
        SELECT id,
               pid,
               img_url imgUrl,
               screen_logo,
               name        projectName,
               create_time createTime
        FROM bto_project_category
        <where>
            is_deleted = '0'
            <if test="projectID!='0'.toString">
                and id LIKE CONCAT(#{projectID},'%')
            </if>
        </where>
    </select>
    <select id="selectProject" resultType="com.bto.commons.pojo.entity.Project">
        SELECT id
        FROM bto_project_category
        WHERE pid = #{pid}
        ORDER BY id DESC
        LIMIT 1
    </select>
    <select id="getProjectIDListByPid" resultType="com.bto.commons.pojo.vo.ProjectInfoVO">
        SELECT id,
               pid,
               name        projectName,
               create_time createTime
        FROM bto_project_category
        <where>
            <if test="projectId!='0'.toString">
                    id LIKE concat(#{projectId},'%')
            </if>
        </where>
    </select>
</mapper>