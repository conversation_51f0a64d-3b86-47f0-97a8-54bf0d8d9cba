<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--mapper类全包名-->
<mapper namespace="com.bto.system.dao.DeviceMapper">
    <sql id="userInfo">
        <if test="userInfo.plantList!=null and userInfo.plantList.size()>0">
            and bpb.plant_uid in
            <foreach collection="userInfo.plantList" item="plantUid" open="(" close=")" separator=",">
                #{plantUid}
            </foreach>
        </if>
        <if test="userInfo.projectList!=null and userInfo.projectList.size()>0">
            and bpb.project_special in
            <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
                #{projectID}
            </foreach>
        </if>
    </sql>

    <select id="selectListByType" resultType="com.bto.commons.pojo.vo.DeviceVO">
        select * from bto_device bpb
        <where>
            bpb.is_deleted = 0
            <if test="type !=null and type != ''">
                AND bpb.device_type = #{type}
            </if>
            <if test="plantUid !=null and plantUid != ''">
                AND plant_uid = #{plantUid}
            </if>
            <include refid="userInfo"/>
        </where>
    </select>
    <select id="getCounterPage" resultType="com.bto.commons.pojo.vo.CounterVO">
        select bpb.*,
        t1.plant_name,
        bd.device_name as counter_name
        from bto_device bpb
        LEFT JOIN bto_plant_base t1 on t1.plant_uid = bpb.plant_uid
        LEFT JOIN bto_device bd ON bd.device_id = bpb.pid AND bd.is_deleted = 0
        <where>
            bpb.is_deleted = 0
            <if test="query.deviceType !=null and query.deviceType != ''">
                and bpb.device_type = #{query.deviceType}
            </if>
            <if test="query.plantUid !=null and query.plantUid != ''">
                and bpb.plant_uid = #{query.plantUid}
            </if>
            <if test="query.deviceId !=null and query.deviceId != ''">
                and bpb.device_id = #{query.deviceId}
            </if>
            <if test="query.plantName !=null and query.plantName != ''">
                and t1.plant_name like concat('%',#{query.plantName},'%')
            </if>
            <if test="query.counterName !=null and query.counterName != ''">
                and bd.counter_name like concat('%',#{query.counterName},'%')
            </if>
            <include refid="userInfo"></include>
        </where>
    </select>
    <select id="counterMonitor" resultType="com.bto.commons.pojo.vo.DeviceVO">
        select * from bto_device
        <where>
            is_deleted = 0
            <if test="typeList!=null and typeList.size()>0">
                and device_type in
                <foreach collection="typeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

</mapper>