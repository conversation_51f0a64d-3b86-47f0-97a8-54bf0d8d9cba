<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bto.system.dao.UserMapper">
    <sql id="userInfo">
        <if test="userInfo.projectList!=null and userInfo.projectList.size()>0">
            t1.project_special in
            <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
                #{projectID}
            </foreach>
        </if>
    </sql>
    <select id="getUserList" resultType="com.bto.commons.pojo.vo.UserInfoVO">
        SELECT t1.user_uid userUid,
        t1.user_name userName,
        t1.avatar userAvatar,
        t1.user_phone userPhone,
        t1.user_email userEmail,
        t1.user_status userStatus,
        t1.user_type userType,
        t1.creator,
        t1.create_time createTime,
        t2.role_name roleName,
        t1.role_id roleID,
        t1.project_special projectID,
        t3.name projectName
        FROM bto_user t1
        LEFT JOIN bto_role t2 ON t1.role_id = t2.id
        LEFT JOIN bto_project_category t3 ON t1.project_special = t3.id
        <where>
            t1.is_deleted = 0 AND
            <include refid="userInfo"/>
            <if test="query.userName!=null">
                AND t1.user_name LIKE CONCAT('%',#{query.userName},'%')
            </if>
            <if test="query.roleId != null and query.roleId!=''">
                AND t1.role_id = #{query.roleId}
            </if>
            <if test="query.userType!=null and query.userType!=''">
                AND t1.user_type = #{query.userType}
            </if>
            <if test="query.userStatus!=null and query.userStatus!=''">
                AND t1.user_status = #{query.userStatus}
            </if>
            <if test="query.userPhone !=null and query.userPhone !=''">
                AND t1.user_phone LIKE CONCAT('%',#{query.userPhone},'%')
            </if>
            <if test="query.projectSpecial !=null and query.projectSpecial.size() > 0">
                AND t1.project_special in
                <foreach collection="query.projectSpecial" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY ${query.order}
        <choose>
            <when test="query.isAsc">
                asc
            </when>
            <otherwise>
                desc
            </otherwise>
        </choose>
    </select>
    <select id="selectUserByUserInfo" parameterType="com.bto.oauth.entity.UserLogin"
            resultType="com.bto.commons.pojo.dto.UserDTO">
        SELECT user_uid        userUid,
               user_name       username,
               user_name       username,
               user_type       userType,
               role_id         roleID,
               project_special projectID,
               role_name       roleName,
               project_name    projectName,
               title           projectTitle,
               img_url         imgUrl,
               layout          layout,
               screen_logo     screenLogo,
               avatar
        FROM v_user_role_project
        WHERE user_name = #{username}
          AND user_password = #{password}
    </select>
    <select id="getPlantUidList" resultType="java.lang.String">
        select plant_uid
        from bto_plant_base
        where is_deleted = 0
          and user_uid = #{userUid}
    </select>
</mapper>