<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bto.system.dao.StatisticsMapper">
    <sql id="userInfo">
        <if test="userInfo.plantList!=null and userInfo.plantList.size()>0">
            and plant_uid in
            <foreach collection="userInfo.plantList" item="plantUid" open="(" close=")" separator=",">
                #{plantUid}
            </foreach>
        </if>
        <if test="userInfo.projectList!=null and userInfo.projectList.size()>0">
            and project_special in
            <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
                #{projectID}
            </foreach>
        </if>
    </sql>
    <sql id="getInfoByUser">
        <if test="userInfo.plantList!=null and userInfo.plantList.size()>0">
            and bto_plant_base.plant_uid in
            <foreach collection="userInfo.plantList" item="plantUid" open="(" close=")" separator=",">
                #{plantUid}
            </foreach>
        </if>
        <if test="userInfo.projectList!=null and userInfo.projectList.size()>0">
            and bto_plant_base.project_special in
            <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
                #{projectID}
            </foreach>
        </if>
    </sql>
    <sql id="getEveryHourElectricityInfoByUser">
        SELECT sum(today_electricity) electricity, concat(LEFT(init_time, 14), '00:00') data_time
        FROM bto_inverter_${tableSuffix} t1
        <where>
            EXISTS(SELECT device_id
            FROM bto_device
            <where>
                is_deleted = 0
                AND device_type = 1
                AND
                plant_uid IN
                <foreach collection="userInfo.plantList" item="plantUid" open="(" close=")" separator=",">
                    #{plantUid}
                </foreach>
                AND device_id = t1.inverter_sn)
            </where>
            <if test="date!=null and date!=''">
                AND DATE(t1.init_time) = #{date}
            </if>
            GROUP BY data_time
        </where>
    </sql>
    <sql id="getEveryHourElectricityInfoByEnterprise">
        SELECT data_time, SUM(electricity) electricity
        FROM `bto_curdate_electricity`
        <where>
            project_special IN
            <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
                #{projectID}
            </foreach>
        </where>
        GROUP BY data_time
    </sql>

    <resultMap id="ElectricityStatisticsInfoMap" type="com.bto.commons.pojo.vo.ElectricityStaticsInfoVO">
        <id property="plantUid" column="plantUid" jdbcType="VARCHAR"/>
        <result property="plantName" column="plantName" jdbcType="VARCHAR"/>
        <collection property="electricityList" ofType="com.bto.commons.pojo.vo.ElectricityInfoVO"
                    javaType="java.util.ArrayList">
            <result property="electricity" column="electricity" javaType="String"/>
            <result property="dataTime" column="dataTime" javaType="String"/>
            <result property="electricityEfficiency" column="electricityEfficiency" javaType="String"/>
            <result property="plantPrice" column="plantPrice" javaType="String"/>
            <result property="electricityEfficiency" column="electricityEfficiency" javaType="String"/>
            <result property="plantCapacity" column="plantCapacity" javaType="String"/>
        </collection>
    </resultMap>

    <select id="getPlantNumInfo" resultType="com.bto.commons.pojo.vo.NumInfoVO">
        SELECT plant_status status, count(*) statusNum
        FROM bto_plant_base
        <where>is_deleted = '0'
            <include refid="userInfo"/>
        </where>
        GROUP BY plant_status
    </select>

    <select id="getPlantElectricityInfo" resultType="com.bto.commons.pojo.vo.PlantElectricityVO">
        SELECT SUM(power) totalPower,
        SUM(today_electricity) todayElectricity,
        SUM(month_electricity) monthElectricity,
        SUM(year_electricity) yearElectricity,
        SUM(total_electricity) totalElectricity,
        SUM(plant_capacity) plantCapacity,
        SUM(today_electricity) todayCo2,
        SUM(total_electricity) totalCocal,
        SUM(total_electricity) totalCo2,
        SUM(total_electricity) treeNum
        FROM bto_plant_base
        <where>
            is_deleted = '0'
            <include refid="userInfo"/>
        </where>
    </select>

    <select id="getWorkEfficiencyInfo" resultType="com.bto.commons.pojo.vo.PlantElectricityVO">
        SELECT plant_name plantName,
        (POWER / plant_capacity) * 100 maxEfficiency,
        (SELECT avg(workEfficiency) * 100 averageEfficiency
        FROM (
        SELECT plant_name, POWER / plant_capacity workEfficiency
        FROM bto_plant_base
        <where>
            is_deleted = 0
            <include refid="userInfo"/>
        </where>
        ORDER BY workEfficiency DESC) t1
        ) averageEfficiency
        FROM bto_plant_base
        <where>
            is_deleted = 0
            <include refid="userInfo"/>
        </where>
        ORDER BY maxEfficiency DESC
        LIMIT 1;
    </select>

    <select id="getPlantMassByProjectId" resultType="string">
        SELECT
        SUM( plant_abnormal_num ) / SUM( plant_num ) AS failureRate
        FROM
        bto_project_day
        WHERE
        project_special IN
        <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
            #{projectID}
        </foreach>
        AND collect = #{date}
    </select>

    <select id="getPlantMassByPlantList" resultType="java.lang.Integer">
        SELECT
        count(*) as statusNum
        FROM
        bto_device
        <where>
            is_deleted = 0
            AND `status` = '2'
            <include refid="userInfo"/>
        </where>
        GROUP BY
        `status`;
    </select>

    <select id="getInverterNumInfo" resultType="com.bto.commons.pojo.vo.NumInfoVO">
        SELECT `status`,
        count(*) statusNum
        FROM bto_device
        <where>
            is_deleted = 0 and device_type = 1
            <include refid="userInfo"/>
        </where>
        group by status
    </select>

    <!--获取近六个月发电量数据-->
    <select id="getElectricityBySixMonth" resultType="com.bto.commons.pojo.vo.ChartElectricityInfoVO">
        SELECT SUM(electricity) electricity, LEFT(collect, 7) collectDate
        FROM
        <if test="userInfo.projectList!=null and userInfo.projectList.size()>0">
            bto_project_day
        </if>
        <if test="userInfo.plantList!=null and userInfo.plantList.size()>0">
            bto_plant_day
        </if>
        <where>
            <include refid="userInfo"/>
        </where>
        GROUP BY collectDate
        ORDER BY collectDate DESC
        LIMIT 6;
    </select>

    <select id="getEveryHourElectricityInfo" resultType="com.bto.commons.pojo.vo.ChartElectricityInfoVO">
        SELECT ABS(IFNULL(electricity - LAG(electricity) OVER (ORDER BY data_time), 0)) AS electricity,
        data_time collectDate
        FROM (
        <if test="userInfo.projectList!=null and userInfo.projectList.size()>0">
            <include refid="getEveryHourElectricityInfoByEnterprise"/>
        </if>
        <if test="userInfo.plantList!=null and userInfo.plantList.size()>0">
            <include refid="getEveryHourElectricityInfoByUser"/>
        </if>
        ) t
    </select>

    <select id="selectPlantUidList" resultType="com.bto.commons.pojo.vo.PlantStatisticsInfoVO">
        SELECT
        bto_plant_base.plant_uid plantUid
        FROM
        bto_plant_base
        LEFT JOIN v_user_plant ON bto_plant_base.plant_uid = v_user_plant.plant_uid
        <where>
            bto_plant_base.is_deleted = '0'
            <include refid="getInfoByUser"/>
            <if test="query.plantName!=null and query.plantName!='' ">
                AND bto_plant_base.plant_name LIKE concat('%',#{query.plantName},'%')
            </if>
            <if test="query.plantStatus!=null and query.plantStatus.size() > 0">
                AND v_user_plant.plant_status IN
                <foreach collection="query.plantStatus" separator="," open="(" close=")" index="index" item="plantUid">
                    #{plantUid}
                </foreach>
            </if>
            <if test="query.powerDistributor!=null and query.powerDistributor!='' ">
                AND v_user_plant.power_distributor = #{query.powerDistributor}
            </if>
            <if test="query.address!=null and query.address!='' ">
                AND bto_plant_base.address LIKE concat('%',#{query.address},'%')
            </if>
            <if test="query.createStartTime!=null and query.createStartTime!=''
            and query.createEndTime!=null and query.createEndTime!='' ">
                AND bto_plant_base.create_time BETWEEN #{query.createStartTime} AND
                date_add(#{query.createEndTime},interval 1 day)
            </if>
            <if test="query.powerStartTime!=null and query.powerStartTime!=''
            and query.powerEndTime!=null and query.powerEndTime!='' ">
                <if test="dateType !=null and dateType == 'day' ">
                    AND left(bto_plant_base.create_time,10) &lt;= #{query.powerEndTime}
                </if>
                <if test="dateType !=null and dateType == 'month' ">
                    AND left(bto_plant_base.create_time,7) &lt;= #{query.powerEndTime}
                </if>
                <if test="dateType !=null and dateType == 'year' ">
                    AND left(bto_plant_base.create_time,4) &lt;= #{query.powerEndTime}
                </if>

            </if>
        </where>
        ORDER BY bto_plant_base.create_time DESC
    </select>

    <select id="getElectricityStatisticsInfoByMonth" resultMap="ElectricityStatisticsInfoMap">
        SELECT t1.plant_uid plantUid,
        t1.collect dataTime,
        SUM(t1.electricity) electricity,
        SUM(t1.electricity)/(t2.plant_capacity*3.5) electricityEfficiency,
        t2.plant_name plantName,
        t2.sale_price plantPrice,
        t2.plant_capacity plantCapacity
        FROM bto_plant_day t1
        LEFT JOIN bto_plant_base t2 ON t1.plant_uid = t2.plant_uid
        <where>
            LEFT(collect,7) = #{query.date} AND
            <if test="plantUidArray.size()>0 and plantUidArray!=null">
                t1.plant_uid IN
                <foreach collection="plantUidArray" separator="," open="(" close=")" index="index" item="plantUid">
                    #{plantUid}
                </foreach>
            </if>
        </where>
        GROUP BY t1.plant_uid,t1.collect
    </select>

    <select id="getElectricityStatisticsInfoByDay"
            parameterType="com.bto.commons.pojo.dto.ElectricityStatisticsQueryDTO"
            resultMap="ElectricityStatisticsInfoMap">
        SELECT
        t1.plant_uid plantUid,
        t1.plant_name plantName,
        SUM(t2.today_electricity)/(t1.plant_capacity*3.5) electricityEfficiency,
        t2.init_time dataTime,
        SUM(t2.today_electricity) electricity,
        t1.sale_price plantPrice,
        t1.plant_capacity plantCapacity
        FROM v_plant_inverter t1
        LEFT JOIN ${tableName} t2 ON t2.inverter_sn = t1.device_id
        <where>
            <if test="plantUidArray.size()>0 and plantUidArray!=null">
                t1.plant_uid in
                <foreach collection="plantUidArray" separator="," open="(" close=")" index="index" item="plantUid">
                    #{plantUid}
                </foreach>
            </if>
        </where>
        GROUP BY t1.plant_uid,t2.init_time
    </select>

    <select id="getElectricityStatisticsInfoByYear"
            resultMap="ElectricityStatisticsInfoMap">
        SELECT t1.plant_uid plantUid,
        LEFT(t1.collect,7) dataTime,
        SUM(t1.electricity) electricity,
        SUM(t1.electricity)/(t2.plant_capacity*3.5) electricityEfficiency,
        t2.plant_name plantName,
        t2.sale_price plantPrice,
        t2.plant_capacity plantCapacity
        FROM bto_plant_day t1
        LEFT JOIN bto_plant_base t2 ON t1.plant_uid = t2.plant_uid
        <where>
            LEFT(collect,4) = #{query.date} AND
            <if test="plantUidArray.size()>0 and plantUidArray!=null">
                t1.plant_uid IN
                <foreach collection="plantUidArray" separator="," open="(" close=")" index="index" item="plantUid">
                    #{plantUid}
                </foreach>
            </if>
        </where>
        GROUP BY t1.plant_uid,dataTime
    </select>

    <select id="getPlantBaseInfo" parameterType="com.bto.commons.pojo.dto.PlantStatisticsQueryDTO"
            resultType="com.bto.commons.pojo.vo.PlantStatisticsInfoVO">
        SELECT
        v.meter_id meterID,
        v.plant_uid plantUid,
        v.plant_name plantName,
        v.user_name userName,
        v.plant_capacity plantCapacity,
        v.total_electricity totalElectricity,
        v.address,
        v.inverter_num inverterNum,
        v.create_time createTime,
        v.receive_time updateTime,
        v.plant_status STATUS,
        v.sale_price electricityPrice,
        SUM( t.electricity ) electricity,
        SUM( t.electricity ) totalReduceCo2,
        SUM( t.electricity ) totalPlantTreeNum
        FROM
        v_user_plant v
        LEFT JOIN bto_plant_day t ON t.plant_uid = v.plant_uid
        <where>
            <if test="dateType!='' and dateType =='year'">
                (LEFT(t.collect,4) BETWEEN #{query.powerStartTime} AND #{query.powerEndTime} )
            </if>
            <if test="dateType!='' and dateType =='month'">
                (LEFT(t.collect,7) BETWEEN #{query.powerStartTime} AND #{query.powerEndTime} )
            </if>
            <if test="dateType!='' and dateType =='day'">
                (LEFT(t.collect,10) BETWEEN #{query.powerStartTime} AND #{query.powerEndTime} )
            </if>
            <if test="plantUidArray !=null and plantUidArray.size()>0 ">
                AND t.plant_uid IN
                <foreach collection="plantUidArray" open="(" close=")" separator="," item="plantUid">
                    #{plantUid}
                </foreach>
            </if>
            <if test="query.projectId != null and query.projectId != '0' and query.projectId != ''">
                AND v.project_special LIKE CONCAT(#{query.projectId}, '%')
            </if>
        </where>
        GROUP BY v.plant_uid
        ORDER BY v.create_time DESC
    </select>
    <select id="getPlantAlarmNum" resultType="com.bto.commons.pojo.vo.PlantStatisticsInfoVO">
        SELECT
        t1.plant_uid plantUid,
        count(t2.inverter_sn ) alarmNum
        FROM
        bto_inverter_latest t1 LEFT JOIN bto_plant_base t ON t.plant_uid = t1.plant_uid
        LEFT JOIN bto_inverter_alarm t2 ON t1.inverter_sn = t2.inverter_sn
        <where>
            AND t.inverter_num &lt;&gt; 0
            <if test="dateType!='' and dateType =='year'">
                AND (LEFT(t2.alarm_date,4) BETWEEN #{query.powerStartTime} AND #{query.powerEndTime} )
            </if>
            <if test="dateType!='' and dateType =='month'">
                AND (LEFT(t2.alarm_date,7) BETWEEN #{query.powerStartTime} AND #{query.powerEndTime} )
            </if>
            <if test="dateType!='' and dateType =='day'">
                AND (LEFT(t2.alarm_date,10) BETWEEN #{query.powerStartTime} AND #{query.powerEndTime} )
            </if>
            <if test="plantUidArray.size()>0">
                AND t1.plant_uid IN
                <foreach collection="plantUidArray" open="(" close=")" separator="," item="plantUid">
                    #{plantUid}
                </foreach>
            </if>
        </where>
        GROUP BY t1.plant_uid
        ORDER BY t.create_time DESC
    </select>

    <select id="getPlantStatusInfo" resultType="com.bto.commons.pojo.vo.PlantInfoVO">
        SELECT
        plant_uid plantUid,
        plant_status plantStatus,
        plant_type_id plantType,
        power_distributor powerDistributor,
        LEFT ( project_special, 1 ) projectName
        FROM
        v_user_plant
        WHERE
        plant_uid IN
        <foreach collection="plantUid" open="(" close=")" separator="," item="plantUid">
            #{plantUid}
        </foreach>
    </select>

    <select id="getIntegrativeStatisticSheet"
            resultType="com.bto.commons.pojo.vo.IntegrativeStatisticSheetVO">
        SELECT SUM(plant_capacity) plantCapacity,
        SUM(power) power,
        COUNT(*) plantNum,
        SUM(total_electricity) electricity,
        SUM(total_electricity) income,
        SUM(total_electricity) reduceCo2,
        SUM(total_electricity) reduceCoal,
        SUM(total_electricity) treeNum,
        NOW() statisticalTime
        FROM bto_plant_base
        <where>
            is_deleted = 0
            AND project_special IN
            <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
                #{projectID}
            </foreach>
            <if test="query.createStartTime!=null and query.createEndTime!=null and query.createStartTime!='' and query.createEndTime!=''">
                AND (create_time
                BETWEEN #{query.createStartTime} AND date_add(#{query.createEndTime},interval 1 day))
            </if>
        </where>
    </select>

    <select id="getDeviceNumInfo" resultType="java.util.HashMap">
        SELECT COUNT(*) statusNum,
        `status` plantStatus
        FROM bto_photovoltaic.bto_device t1
        LEFT JOIN bto_plant_base t2 ON t1.plant_uid = t2.plant_uid
        <where>
            t1.is_deleted = 0 AND t2.is_deleted = 0
            <if test="query.createStartTime!=null and query.createEndTime!=null and query.createStartTime!='' and query.createEndTime!=''">
                AND (t2.create_time BETWEEN #{query.createStartTime} AND #{query.createEndTime})
            </if>
            AND t2.project_special IN
            <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
                #{projectID}
            </foreach>
            GROUP BY plantStatus
        </where>
    </select>
    <select id="getPeriodElectricity" resultType="java.lang.String">
        SELECT sum(electricity)
        FROM bto_plant_day t1
        <where>
            <if test="query.dataStartTime!=null and query.dataEndTime!=null and query.dataStartTime!='' and query.dataEndTime!=''">
                <choose>
                    <!-- 如果传递的是月份，例如 '2024-02' -->
                    <when test="query.dataStartTime.length() == 7 and query.dataEndTime.length() == 7">
                        LEFT(t1.collect,7) BETWEEN #{query.dataStartTime} AND #{query.dataEndTime}
                    </when>
                    <!-- 如果传递的是天，例如 '2024-02-29' -->
                    <otherwise>
                        t1.collect BETWEEN #{query.dataStartTime} AND #{query.dataEndTime}
                    </otherwise>
                </choose>
            </if>
            AND
            EXISTS (
            SELECT plant_uid FROM bto_plant_base
            <where>
                <if test="query.createStartTime!=null and query.createEndTime!=null and query.createStartTime!='' and query.createEndTime!=''">
                    AND (date(create_time) BETWEEN #{query.createStartTime} AND #{query.createEndTime})
                </if>
                AND project_special IN
                <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
                    #{projectID}
                </foreach>
                AND plant_uid = t1.plant_uid
            </where>
            )
        </where>
    </select>

    <select id="getIntegrativeStatisticChart"
            resultType="com.bto.commons.pojo.vo.IntegrativeStatisticChartVO">
        SELECT
        SUM(electricity) electricity,
        SUM(plant_capacity) plantCapacity,
        SUM(electricity)/ SUM(plant_capacity) income,
        SUM(plant_num) plantNum,
        SUM(plant_abnormal_num) abnormalPlantNum,
        SUM(plant_num)-SUM(plant_abnormal_num) AS normalPlantNum,
        <if test="isMonth">
            LEFT(collect, 7) collectDate
        </if>
        <if test="!isMonth">
            LEFT(collect, 10) collectDate
        </if>
        FROM bto_project_day
        <where>
            <include refid="userInfo"/>
            AND collect between #{query.dataStartTime} and #{query.dataEndTime}
        </where>
        GROUP BY collectDate
        ORDER BY collectDate DESC
    </select>

    <!--按天数获取发电量数据-->
    <select id="getElectricityByNumDay" parameterType="com.bto.commons.pojo.vo.RequireParamsDTO"
            resultType="com.bto.commons.pojo.vo.ChartElectricityInfoVO">
        SELECT SUM(electricity) electricity, collect collectDate
        FROM
        <if test="userInfo.projectList!=null and userInfo.projectList.size()>0">
            bto_project_day
        </if>
        <if test="userInfo.plantList!=null and userInfo.plantList.size()>0">
            bto_plant_day
        </if>
        <where>
            <include refid="userInfo"/>
        </where>
        GROUP BY collectDate
        ORDER BY collectDate DESC
        LIMIT #{nums}
    </select>

    <select id="getDeviceStatusNumInfo" resultType="com.bto.commons.pojo.vo.NumInfoVO">
        SELECT `status`,
        count(*) statusNum
        FROM bto_device
        <where>
            is_deleted = 0
            <include refid="userInfo"/>
        </where>
        GROUP BY `status`
    </select>

</mapper>