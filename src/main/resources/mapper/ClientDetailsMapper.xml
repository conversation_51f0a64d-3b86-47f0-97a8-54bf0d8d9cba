<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bto.oauth.dao.ClientDetailsMapper">

    <select id="selectClientInfo" resultType="java.util.HashMap">
        SELECT project_id
        FROM oauth_client_details
        <where>
            client_id = #{clientId}
        </where>
    </select>
    <select id="selectAuthorities" resultType="java.lang.String">
        select t3.auth
        from oauth_client_details t1
                 left join bto_role_has_menu t2 on t1.role_id = t2.role_id
                 left join bto_menu t3 on t3.id = t2.menu_id
        where t1.client_id = #{clientId} and t3.auth != ''
    </select>
    <select id="selectAuthorityList" resultType="java.lang.String">
        SELECT
            t3.auth
        FROM
            bto_user t1
                LEFT JOIN bto_role_has_menu t2 ON t1.role_id = t2.role_id
                LEFT JOIN bto_menu t3 ON t3.id = t2.menu_id
        WHERE
            t1.user_uid = #{userUid}
          AND t3.auth != ''
    </select>
</mapper>