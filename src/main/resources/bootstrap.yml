server:
#  port: 48000
  port: 52000
  servlet:
    multipart:
      enabled: true
      max-file-size: 50MB
      max-request-size: 50MB
    session:
      cookie:
        name: OAUTH2-CLIENT
    context-path: /system
security:
  basic:
    enabled: false
  oauth2:
    client:
      client-id: admin
      client-secret: btoadmin
spring:
  profiles:
    active: prod
  application:
    name: bto-xhl-server
  redis:
    database: 1
    host: 127.0.0.1
    port: 6379
#    password: xhl123..
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************************************************
    username: root
    password: 123
#    password: bto_xhl++

    hikari:
      connection-timeout: 30000
      minimum-idle: 5
      maximum-pool-size: 5
      auto-commit: true
      idle-timeout: 600000
      max-lifetime: 1800000
      connection-test-query: SELECT 1
  mvc:
    hiddenmethod:
      filter:
        enabled: true
  session:
    store-type: redis
mybatis-plus:
  pagehelper:
    dialect: mysql
    page:
      size: -1L
      mybatis-plus:
    mapper-locations: classpath:/mapper/*.xml
  configuration:
    plugins:
      - com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  type-handlers-package: com.bto.commons.handler
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql
storage:
  enabled: true
  config:
    type: local
    domain: http://localhost:41000/system
  local:
    path: C://upload
logging:
  level:
    root: info