import{l as s}from"./quasar-b3f06d8a.js";import"./vue-5bfa3a54.js";import t from"./inexistAlarm-73b185db.js";import o from"./existAlarm-00b7be57.js";import{h as i,az as r,a9 as e,o as m,c as p,a,x as j,b as l,H as n,l as u}from"./@vue-5e5cdef9.js";import"./@babel-f3c0a00c.js";import"./MyTable-27fb4664.js";import"./index-8cc8d4b8.js";import"./element-plus-d975be09.js";import"./lodash-es-ea7deab5.js";import"./@vueuse-af86c621.js";import"./vue-router-6159329f.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./dayjs-d60cc07f.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./xe-utils-fe99d42a.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-7bbbb435.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./lodash-6d99edc3.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./naive-ui-0ee0b8c3.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./date-fns-tz-0cc073c8.js";import"./pagination-c4d8e88e.js";import"./MyForm-5e5c0ec8.js";import"./formUtil-a2e6828b.js";import"./menuStore-26f8ddd8.js";import"./icons-95011f8c.js";import"./@vicons-f32a0bdb.js";import"./api-b858041e.js";import"./notification-950a5f80.js";import"./date-33a67ff0.js";import"./element-china-area-data-0e3c7f8a.js";import"./formatTableData-0442e1d7.js";import"./pageUtil-3bb2e07a.js";import"./proxyUtil-6f30f7ef.js";import"./dataAnalysisApi-c10bbebe.js";const d={class:"tw-h-full tw-w-full tw-bg-gray-100"},c={class:"tw-bg-gray"},v={key:0,class:"tw-bg--200"},g={key:1},b={__name:"faultPrediction",setup(b){const f=i(1);return(i,b)=>{const y=s,x=r("y");return e((m(),p("div",d,[a("header",c,[j(y,{"toggle-color":"green",modelValue:l(f),"onUpdate:modelValue":b[0]||(b[0]=s=>n(f)?f.value=s:null),spread:"","no-caps":"",style:{width:"23%"},class:"tw-rounded tw-ml-2 tw-mt-1","text-color":"black",options:[{label:"无告警",value:1},{label:"有告警",value:2}]},null,8,["modelValue"])]),1==l(f)?(m(),p("section",v,[j(t)])):u("",!0),2==l(f)?(m(),p("section",g,[j(o)])):u("",!0)])),[[x,[1,20]]])}}};export{b as default};
