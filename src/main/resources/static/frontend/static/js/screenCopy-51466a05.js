import{c as t,_ as e,a as s}from"./mapUtils-1e5311e9.js";import{W as l,X as a}from"./element-plus-d975be09.js";import"./vue-5bfa3a54.js";import{_ as i,u as n}from"./index-8cc8d4b8.js";import{x as r}from"./homeStore-b8fb7ab6.js";import{S as o,a as c,A as m}from"./swiper-7f939876.js";import{B as p}from"./bignumber.js-a537a5ca.js";import{s as u}from"./startEndSlide-8c1800d1.js";import{C as f,G as d,H as x,F as j,J as w}from"./@vueuse-af86c621.js";import{h as v}from"./homeApi-54bb989e.js";import{S as y}from"./@vicons-f32a0bdb.js";import{j as h,h as g,m as z,v as b,az as I,o as k,c as S,a as D,x as C,a8 as E,t as N,b as _,f as F,l as T,a9 as L,aa as M,F as R,k as A,C as W,D as U}from"./@vue-5e5cdef9.js";import{_ as q}from"./naive-ui-0ee0b8c3.js";import"./three-59a86278.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./chartResize-3e3d11d7.js";import"./element-resize-detector-0d37a2ab.js";import"./@babel-f3c0a00c.js";import"./batch-processor-06abf2b4.js";import"./lodash-6d99edc3.js";import"./echartsInit-0067e609.js";import"./menuStore-26f8ddd8.js";import"./vue-router-6159329f.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./dayjs-d60cc07f.js";import"./icons-95011f8c.js";import"./chartXY-a0399c4a.js";import"./api-b858041e.js";import"./notification-950a5f80.js";import"./axios-84f1a956.js";import"./quasar-b3f06d8a.js";import"./countUtil-c51cdcf8.js";import"./alarmAnalysisApi-e3a5f201.js";import"./lodash-es-ea7deab5.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./xe-utils-fe99d42a.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-7bbbb435.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./taskUitls-36951a34.js";import"./index-15186f59.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./date-fns-tz-0cc073c8.js";const B=t=>(W("data-v-69a986e9"),t=t(),U(),t),H={class:"progress-left u-flex-center-no u-gap-8"},P=B((()=>D("article",{class:"small-title font-small-text-size"}," 电站在线率 ",-1))),Y={class:"tw-flex-1 light-blue"},O={class:"tw-text-xl tw-text-[#fff] tw-flex tw-items-center"},X={class:"supplementary-text font-small-text-size"},Z={class:"title u-flex-x-center item-start u-gap-8"},G={class:"main-title font-title-size"},J={class:"date-time"},Q={class:"progress-right u-flex-center-no u-gap-8"},V=B((()=>D("article",{class:"small-title font-small-text-size"}," 逆变器在线率 ",-1))),$={class:"tw-flex-1 light-blue"},K={class:"tw-text-xl tw-text-[#fff] tw-flex tw-items-center"},tt={class:"supplementary-text font-small-text-size"},et={class:"ElecDaily u-flex-column"},st=B((()=>D("div",{class:""},[D("article",{class:"regular-title sub-title title-border font-sub-title-size"},"发电汇总")],-1))),lt={class:"u-flex-1 u-flex-column plant-card-content"},at={class:"u-flex-1 u-flex-y-center justify-between"},it=B((()=>D("article",{class:"small-title font-text-size"}," 总发电量 ",-1))),nt={class:"u-flex-1 u-flex-y-center justify-end"},rt={class:"body-text font-text-size"},ot={class:"supplementary-text font-small-text-size2"},ct={class:"u-flex-1 u-flex-y-center justify-between"},mt=B((()=>D("article",{class:"small-title font-text-size"}," 月发电量 ",-1))),pt={class:"u-flex-1 u-flex-y-center justify-end"},ut={class:"body-text font-text-size"},ft={class:"supplementary-text font-small-text-size2"},dt={class:"u-flex-1 u-flex-y-center justify-between"},xt=B((()=>D("article",{class:"small-title font-text-size"}," 日发电量 ",-1))),jt={class:"u-flex-1 u-flex-y-center justify-end"},wt={class:"body-text font-text-size"},vt={class:"supplementary-text font-small-text-size2"},yt={class:"Assets u-flex-center-no"},ht={class:"u-flex-1 u-flex-column u-gap-10"},gt={class:"u-flex-1 u-flex-column"},zt=B((()=>D("span",{class:"small-title text-center font-sub-title-size"}," 总装机容量 ",-1))),bt={class:"u-flex-center-no"},It={class:"body-text font-text-size"},kt={class:"supplementary-text font-small-text-size2"},St={class:"u-flex-1 u-flex-column u-gap-10"},Dt={class:"u-flex-1 u-flex-column"},Ct=B((()=>D("span",{class:"small-title text-center font-sub-title-size"},"总电站数量",-1))),Et={class:"body-text text-center font-text-size"},Nt={class:"AlarmStatics u-flex-column"},_t=B((()=>D("div",{class:""},[D("article",{class:"regular-title sub-title title-border font-sub-title-size"},"实时告警")],-1))),Ft={class:"u-flex-1 u-flex-center-no"},Tt={class:"u-flex-1 h-full u-flex-column item-center justify-center"},Lt={class:"body-text text-center font-text-size"},Mt={class:"small-title text-center font-text-size"},Rt={class:"u-flex-1 h-full u-flex-column item-center justify-center"},At={class:"body-text text-center font-text-size"},Wt={class:"small-title text-center font-text-size"},Ut={class:"Map"},qt={class:"map-info u-flex-column items-center"},Bt={class:"area u-flex-y-center u-gap-6"},Ht=["onClick"],Pt={key:0,class:"weather u-flex-center u-gap-6"},Yt={class:"u-flex-center"},Ot=B((()=>D("i",{class:"font-small-text-size"},"温度：",-1))),Xt={class:"font-small-text-size weather-font-color"},Zt={class:"u-flex-center"},Gt=B((()=>D("i",{class:"font-small-text-size"},"风向：",-1))),Jt={class:"font-small-text-size weather-font-color"},Qt={class:"u-flex-center"},Vt=B((()=>D("i",{class:"font-small-text-size"},"风速：",-1))),$t={class:"font-small-text-size weather-font-color"},Kt={class:"u-flex-center weather-font-color"},te=B((()=>D("i",{class:"font-small-text-size"},"湿度：",-1))),ee={class:"font-small-text-size"},se={class:"u-flex-center u-gap-6 font-small-text-size weather-font-color"},le={class:"weather-font-color"},ae={class:"ElecChart"},ie={class:"SavingEnergy"},ne={class:"se-item-1"},re=B((()=>D("article",{class:"small-title text-center font-text-size"},"节约标准煤 (万吨)",-1))),oe={class:"body-text text-left font-small-text-size"},ce={class:"se-item-2"},me=B((()=>D("article",{class:"small-title text-center font-text-size"},"日CO2减排 (吨)",-1))),pe={class:"body-text text-right font-small-text-size"},ue={class:"se-item-3"},fe={class:"body-text text-left font-small-text-size"},de=B((()=>D("article",{class:"small-title text-center font-text-size"},"CO2减排 (万吨)",-1))),xe={class:"se-item-4"},je={class:"body-text text-right font-small-text-size"},we=B((()=>D("article",{class:"small-title text-center font-text-size"},"等效植树 (棵)",-1))),ve={class:"RotationalSeeding"},ye=B((()=>D("div",{class:"rs-title"},[D("article",{class:"regular-title sub-title title-border font-sub-title-size"},"电站效率")],-1))),he={class:"u-flex-1 rs-content"},ge={class:"tw-flex tw-items-center tw-justify-around text-ellipsis font-small-text-size"},ze={class:"tw-w-[40%]"},be={class:"inline-flex tw-w-[30%]"},Ie={class:"tw-text-[#F49A26] tw-w-[25%]"},ke={key:0,class:"rs-loading u-flex-center-no"},Se=i(Object.assign({name:"B2HomeScreen"},{__name:"screenCopy",props:{iframe:{type:Boolean,default:!1}},setup(i){const W=i,U=h({start:null,end:!1,index:{startIndex:0,endIndex:50},total:0,displayData:[],originData:[]}),B={scrollTimer:null,retryTimer:null,dateTimer:null,elecChartTimer:null,resultTimer:null},Se=[m],De=new u(10,20,10,100),[Ce]=f(),Ee=r(),Ne=n();Ne.getUser();const _e=g([]),Fe=h({curDate:d(x(),"HH:MM YYYY/MM/DD"),map:{alarmList:[],nodeStatus:"",weatherData:null,parentIdList:[]}}),Te=h({map:j("mapDom")}),Le=g(null),{isFullscreen:Me,enter:Re,exit:Ae,toggle:We}=w(Le);async function Ue(){De.total<=10||(De.total<=20?_e.value.push(..._e.value):_e.value.length>=10&&_e.value.splice(Ce.value?De.step:0,De.step,...await async function(){const t=await v(...De.next());return"00000"==t.status&&De.resetTotal(t.data.total),t.data.records}()))}return z((async()=>{W.iframe&&document.querySelector(".screen-box").classList.remove("screenfull-content"),await Ee.getRankInfo(),Fe.map=new t(Te.map),await Fe.map.setChart();const e=await v(0,20);"00000"==(null==e?void 0:e.status)&&(De.resetTotal(e.data.total),_e.value=e.data.records),top!=self&&(document.querySelector("ul").style.display="none",document.querySelector("header").classList.add("tw-hidden"),document.querySelector("html").classList.add("tw-min-h-0"))})),b((()=>{var t,e,s,l;for(let a in B)B[a]&&(l=B)[s=a]&&(clearInterval(l[s]),l[s]=null);Fe.map.timer&&(clearInterval(Fe.map.timer),Fe.map.timer=null,Te.map=null,Fe.map=null,null==(e=null==(t=Fe.map)?void 0:t.chart)||e.clear())})),(t,i)=>{var n,r,m,u,f,d,x;const j=l,w=a,v=e,h=s,g=q,z=I("go");return k(),S("div",{class:"screen-box bg screenfull-content",ref_key:"mainRef",ref:Le},[D("div",{class:"Header",onDblclick:i[0]||(i[0]=t=>_(We)())},[D("div",H,[P,D("div",Y,[C(j,{"stroke-width":10,percentage:new(_(p))(null==(n=_(Ee).plantNumInfo)?void 0:n.onlineRate).times(100).decimalPlaces(2).toNumber()||0},{default:E((()=>{var t;return[D("article",O,[D("span",X,N(new(_(p))(null==(t=_(Ee).plantNumInfo)?void 0:t.onlineRate).times(100).decimalPlaces(2).toString())+"% ",1)])]})),_:1},8,["percentage"])])]),D("div",Z,[_(Ne).userInfo.screenLogo?(k(),F(w,{key:0,src:_(Ne).userInfo.screenLogo,fit:"contain",class:"image"},null,8,["src"])):T("",!0),D("h1",G,N(_(Ne).userInfo.projectTitle),1),D("div",J,N(_(Fe).curDate),1)]),D("div",Q,[V,D("div",$,[C(j,{"stroke-width":12,percentage:new(_(p))(null==(r=_(Ee).inverterNumInfo)?void 0:r.onlineRate).times(100).decimalPlaces(2).toNumber()||0},{default:E((()=>{var t;return[D("article",K,[D("span",tt,N(new(_(p))(null==(t=_(Ee).inverterNumInfo)?void 0:t.onlineRate).times(100).decimalPlaces(2).toString())+"% ",1)])]})),_:1},8,["percentage"])])])],32),D("div",et,[st,D("div",lt,[D("div",at,[C(_(y),{class:"tw-w-[30px] tw-mr-1 tw-text-[#F8B62D]"}),it,D("article",nt,[D("span",rt,N((String(_(Ee).plantInfo.totalElectricity)||"000000.00").split("").join(" ")),1),D("span",ot,N(_(Ee).plantInfo.totalElectricityThousandUnit?"kWh":"MWh"),1)])]),D("div",ct,[C(_(y),{class:"tw-w-[30px] tw-mr-1 tw-text-[#F8B62D]"}),mt,D("article",pt,[D("span",ut,N((String(_(Ee).plantInfo.monthElectricity)||"000000.00").split("").join(" ")),1),D("span",ft,N(_(Ee).plantInfo.monthElectricityThousandUnit?"kWh":"MWh"),1)])]),D("div",dt,[C(_(y),{class:"tw-w-[30px] tw-mr-1 tw-text-[#F8B62D]"}),xt,D("article",jt,[D("span",wt,N((String(_(Ee).plantInfo.todayElectricity)||"000000.00").split("").join(" ")),1),D("span",vt,N(_(Ee).plantInfo.todayElectricityThousandUnit?"kWh":"MWh"),1)])])])]),D("div",yt,[D("div",ht,[C(w,{class:"as-image",src:"https://www.btosolarman.com/assets/btosolar/picture/screen/computer.png",fit:"contain"}),D("p",gt,[zt,D("span",bt,[D("i",It,N((_(Ee).plantInfo.plantCapacity+""||"000000.00").split("").join("")),1),D("i",kt,N(_(Ee).plantInfo.plantCapacityThousandUnit?"kWp":"MWp"),1)])])]),D("div",St,[C(w,{class:"as-image",src:"https://www.btosolarman.com/assets/btosolar/picture/screen/net.png",fit:"contain"}),D("p",Dt,[Ct,D("span",Et,N(_(Ee).plantNumInfo.totalNum),1)])])]),D("div",Nt,[_t,D("div",Ft,[D("div",Tt,[D("article",Lt,N(_(Ee).alarmNumInfo.alarmPlantNum),1),L((k(),S("article",Mt,[M(" 告警电站 ")])),[[z,"/plantManage/plantList?status=3"]])]),D("div",Rt,[D("article",At,N(_(Ee).alarmNumInfo.alarmInfoNum),1),L((k(),S("article",Wt,[M(" 告警 ")])),[[z,"/alarmAnalysis/alarmList?status=0"]])])])]),D("div",Ut,[D("div",qt,[D("p",Bt,[D("span",{class:"font-small-text-size",onClick:i[1]||(i[1]=t=>_(Fe).map.titleEnter())},"全国"),(k(!0),S(R,null,A(_(Fe).map.parentIdList,(t=>(k(),S("span",{class:"font-small-text-size",key:t.name,onClick:e=>_(Fe).map.titleEnter(t)},N(t.name),9,Ht)))),128))]),(null==(m=_(Fe).map.weatherData)?void 0:m.city)&&_(Fe).map.parentIdList?(k(),S("p",Pt,[D("span",Yt,[Ot,D("i",Xt,N(null==(u=_(Fe).map.weatherData)?void 0:u.temperature)+"℃ ",1)]),D("span",Zt,[Gt,D("i",Jt,N(null==(f=_(Fe).map.weatherData)?void 0:f.wind),1)]),D("span",Qt,[Vt,D("i",$t,N(null==(d=_(Fe).map.weatherData)?void 0:d.windspeed),1)]),D("span",Kt,[te,D("i",ee,N(null==(x=_(Fe).map.weatherData)?void 0:x.humidity),1)])])):T("",!0),D("p",se,[D("span",null,N(_(Fe).map.plantName),1),D("span",le,N(_(Fe).map.nodeStatus),1)])]),D("figure",{id:"mapChart",class:"u-wh-full",ref:"mapDom",onClick:i[2]||(i[2]=(...e)=>t.exitHandle&&t.exitHandle(...e))},null,512)]),D("div",ae,[C(v)]),D("div",ie,[D("div",ne,[re,D("article",oe,N(_(Ee).plantInfo.totalCocal),1)]),D("div",ce,[me,D("article",pe,N(_(Ee).plantInfo.todayCo2),1)]),D("div",ue,[D("article",fe,N(_(Ee).plantInfo.totalCo2),1),de]),D("div",xe,[D("article",je,N(_(Ee).plantInfo.treeNum),1),we]),C(h)]),D("div",ve,[ye,D("div",he,[_(_e).length?(k(),F(_(c),{key:0,id:"swiperlist",ref:"swiperRef",autoplay:{delay:2e3,disableOnInteraction:!0},loop:!0,modules:Se,"slides-per-view":10,"space-between":0,speed:300,class:"tw-h-full swiper-no-swiping swipe tw-rounded-br-xl tw-rounded-bl-xl",direction:"vertical",onReachEnd:Ue},{default:E((()=>[(k(!0),S(R,null,A(_(_e),((t,e)=>(k(),F(_(o),{key:e,class:"tw-w-full"},{default:E((()=>[D("tr",ge,[D("td",ze,[C(g,{class:"tw-w-[100%] tw-text-white"},{default:E((()=>[M(N(t.plantName),1)])),_:2},1024)]),D("td",be,[C(j,{percentage:parseFloat((100*parseFloat(t.plantEfficiency)).toFixed(2)),"show-text":!1,"stroke-width":10,class:"tw-inline",color:"#F49A26",status:"success"},null,8,["percentage"])]),D("td",Ie,N((100*parseFloat(t.plantEfficiency)).toFixed(2))+" % ",1)])])),_:2},1024)))),128))])),_:1},512)):T("",!0)]),_(U).end?(k(),S("p",ke,"加载中...")):T("",!0)])],512)}}}),[["__scopeId","data-v-69a986e9"]]);export{Se as default};
