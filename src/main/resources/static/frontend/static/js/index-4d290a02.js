import{f as e,B as a}from"./element-plus-d975be09.js";import"./vue-5bfa3a54.js";import{_ as t,g as l,l as s,b as i}from"./index-8cc8d4b8.js";import{s as n}from"./pinia-c7531a5f.js";import{m as r,J as o}from"./vue3-drr-grid-layout-3f9cba0a.js";import{c,d as u,i as m}from"./chartResize-3e3d11d7.js";import{X as d}from"./xe-utils-fe99d42a.js";import{B as p}from"./bignumber.js-a537a5ca.js";import{a as v}from"./vxe-table-3a25f2d2.js";import{i as f,p as g,a as h,b as y,c as x,d as b,e as w,l as j,g as C,f as N,h as k,j as E,k as P,m as S}from"./index-6788ffa0.js";import{g as I}from"./imgImport-3dead1a5.js";import{h as _,j as z,w as q,e as D,m as $,v as M,as as O,o as W,c as L,b as R,a9 as T,l as U,a as F,F as J,k as A,y as B,t as V,aa as G,x as Z,a8 as H,f as K,a6 as X,C as Y,D as Q}from"./@vue-5e5cdef9.js";import"./lodash-es-ea7deab5.js";import"./@vueuse-af86c621.js";import"./vue-router-6159329f.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./dayjs-d60cc07f.js";import"./@babel-f3c0a00c.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./nprogress-e136a1b4.js";import"./quasar-b3f06d8a.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-7bbbb435.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./lodash-6d99edc3.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./vue-demi-01e7384c.js";import"./dom-zindex-5f662ad1.js";import"./element-resize-detector-0d37a2ab.js";import"./batch-processor-06abf2b4.js";import"./index-15186f59.js";import"./sass-ac65759c.js";import"./immutable-901ee85f.js";import"./exceljs-b3a0e81d.js";const ee=e=>(Y("data-v-3512cd63"),e=e(),Q(),e),ae={key:0,class:"screen-loading u-wh-full","element-loading-text":"正在获取数据"},te={class:"u-wh-full info-statics u-flex-column u-border"},le=ee((()=>F("div",{class:"title regular-title u-flex-y-center"},[F("p",{class:"square"}),F("p",null,"整体概况")],-1))),se={class:"info-statics-content u-flex-1 w-full"},ie=["onClick"],ne={class:"card-icon u-flex-center-no"},re=["src","alt"],oe={class:"u-flex-1 u-flex-column justify-center"},ce={class:"text-left small-title"},ue={class:"text-left digtal"},me={class:"u-wh-full info-statics u-flex-column u-border"},de=ee((()=>F("div",{class:"title regular-title u-flex-y-center"},[F("p",{class:"square"}),F("p",null,"配电房数据")],-1))),pe={class:"info-statics-content u-flex-1 w-full"},ve={class:"card-icon u-flex-center-no"},fe=["src","alt"],ge={class:"u-flex-1 u-flex-column justify-center"},he={class:"text-left small-title"},ye={class:"text-left digtal"},xe={class:"grid grid-cols-2 gap-4"},be={class:"u-wh-full station-statics u-flex-column u-border"},we=ee((()=>F("div",{class:"title regular-title u-flex-y-center"},[F("p",{class:"square"}),F("p",null,"电站实时状态")],-1))),je={class:"station-statics-content u-flex-1 w-full u-flex-center-no"},Ce={class:"index u-flex-1 station-statics-content-item"},Ne=["onClick"],ke={class:"w-full u-flex-center-no u-gap-6"},Ee=["xlink:href"],Pe=["src"],Se={class:"small-title text-center"},Ie={class:"digtal text-center u-flex-center-no"},_e={class:"u-wh-full inverter-statics u-flex-column u-border"},ze=ee((()=>F("div",{class:"title regular-title u-flex-y-center"},[F("p",{class:"square"}),F("p",null,"逆变器实时状态")],-1))),qe={class:"inverter-statics-content u-flex-1 w-full u-flex-center-no"},De={class:"index u-flex-1 inverter-statics-content-item"},$e=["onClick"],Me={class:"w-full u-flex-center-no u-gap-6"},Oe=["xlink:href"],We=["src"],Le={class:"small-title text-center"},Re={class:"digtal text-center u-flex-center-no"},Te={class:"u-wh-full elec-chart u-flex-column u-border"},Ue=ee((()=>F("div",{class:"title regular-title u-flex-y-center"},[F("p",{class:"square"}),F("p",null,"近期发电")],-1))),Fe={class:"elec-chart-content u-flex-1 u-flex-center-no u-gap-18"},Je={class:"elec-chart-content-chart u-flex-1 h-full u-flex-column"},Ae={class:"u-flex-center title1"},Be=ee((()=>F("span",null,"今日发电",-1))),Ve=ee((()=>F("figure",{class:"u-flex-1 w-full",id:"dayElecChart"},null,-1))),Ge=ee((()=>F("div",{class:"elec-chart-content-chart u-flex-1 h-full u-flex-column"},[F("p",{class:"text-center title0"},"近6月发电趋势"),F("figure",{class:"u-flex-1 w-full",id:"monthElecChart"})],-1))),Ze={class:"grid-layout u-wh-full"},He={class:"grid-layout-box u-flex-center"},Ke={class:"u-wh-full u-flex-center"},Xe={class:"grid-save-btns u-flex-y-center justify-end"},Ye=t({__name:"index",setup(t){const Y=l(),{projectId:Q}=n(Y),ee=_();_();const Ye=_(),Qe=_(!0);let ea=null;const aa=z({loading:!1,rowHeight:400,layout:[{x:0,y:0,w:1,h:1,i:1,name:"infoStatics",template:f},{x:1,y:0,w:1,h:1,i:2,name:"plantStatus",template:g},{x:0,y:1,w:1,h:1,i:3,name:"plantElec",template:h},{x:1,y:1,w:1,h:1,i:4,name:"inverterStatus",template:y}]}),ta=z({visible:!1,submitLoading:!1,layout:[{x:0,y:0,w:1,h:1,i:1,name:"infoStatics",text:"电站概况"},{x:1,y:0,w:1,h:1,i:2,name:"plantStatus",text:"电站状态"},{x:0,y:1,w:1,h:1,i:3,name:"plantElec",text:"发电趋势"},{x:1,y:1,w:1,h:1,i:4,name:"inverterStatus",text:"逆变器状态"}]}),la=_(""),sa=z({totalNum:{label:"电站数量",value:0,unit:null,icon:"icon-Union",bgColor:"bg-fff4e7",color:"font-ffa828",picName:"plant_num.png",event:()=>pa()},InverterNum:{label:"逆变器数",value:0,unit:null,icon:"icon-nibianqi2",bgColor:"bg-eaf4ff",color:"font-3a8fea",picName:"inverter_num.png",event:()=>va()},plantCapacity:{label:"装机容量",value:0,icon:"icon-zhuangjirongliang-mianicon",unit:"(KWp)",bgColor:"bg-eefbff",picName:"capacity.png",color:"font-0facd5"},totalElectricity:{label:"总发电量",value:0,icon:"icon-leijifadianliang",unit:"(kWh)",bgColor:"bg-ebfefd",picName:"total_elec.png",color:"font-36cfc9"}}),ia=z({totalNum:{label:"配电房数",value:2,unit:null,icon:"icon-Union",bgColor:"bg-fff4e7",color:"font-ffa828",picName:"配电房.png",event:()=>pa()},InverterNum:{label:"并网柜数",value:4,unit:null,icon:"icon-nibianqi2",bgColor:"bg-eaf4ff",color:"font-3a8fea",picName:"并网柜.png",event:()=>va()},plantCapacity:{label:"烟感正常数",value:2,icon:"icon-zhuangjirongliang-mianicon",unit:"",bgColor:"bg-eefbff",picName:"正常烟感.png",color:"font-0facd5"},totalElectricity:{label:"烟感异常数",value:0,icon:"icon-leijifadianliang",unit:"",bgColor:"bg-ffecec",picName:"告警烟感.png",color:"font-36cfc9"}}),na=z({visible:!1,filterText:"",sel:"",selLabel:"",data:[]}),ra=z({offlineNum:{label:"光精灵离线",value:0,color:"color-outline",icon:"#icon-RemoveFilled",event:()=>pa(0)},alarmNum:{label:"告警",value:0,color:"color-alarm",icon:"#icon-jinggaozhuangtai",event:()=>da("")},inverterShutdownNum:{label:"夜间模式",value:0,color:"color-night",icon:"#icon-a-zu2837",event:()=>pa(4)},selfCheckNum:{label:"自检提示",value:0,color:"color-self",icon:"#icon-jinggaozhuangtai",event:()=>pa(5)}}),oa=z({offlineNum:{label:"离线",value:0,color:"color-outline",icon:"#icon-RemoveFilled",event:()=>va("13")},alarmNum:{label:"告警",value:0,color:"color-alarm",icon:"icon-alarmNew",event:()=>va("2")},inverterShutdownNum:{label:"夜间模式",value:0,color:"color-night",icon:"#icon-a-zu2837",event:()=>va("4")},selfCheckNum:{label:"自检提示",value:0,color:"color-self",icon:"#icon-jinggaozhuangtai",event:()=>va("3")}}),ca={plantProgressChart:null,inverterProgressChart:null,dayElecChart:null,monthElecChart:null},ua=z({plantProgressChart:null,inverterProgressChart:null,dayElecChart:null,monthElecChart:null}),ma=z({plantProgressChart:0,inverterProgressChart:0,dayElecChart:{xData:[],data:[]},monthElecChart:{xData:[],data:[]}}),da=e=>{s.push({path:"/plantManage/alarmManage",query:{deviceType:e}})},pa=e=>{s.push({path:"statementManage/plant",query:{status:e}})},va=e=>{s.push({path:"statementManage/inverterList",query:{multiInverterStatus:e}})},fa=(e,a)=>{let t;switch(ua[e]=!0,e){case"dayElecChart":t=d.clone(b,!0);let e=d.clone(j,!0);e.data=ma.dayElecChart.data,e.name="发电量(MWh)",t.series[0]=e,t.xAxis.data=ma.dayElecChart.xData;break;case"monthElecChart":t=d.clone(b,!0);let a=d.clone(w,!0);a.data=ma.monthElecChart.data,a.name="发电量(MWh)",t.series[0]=a,t.yAxis[0].splitLine.lineStyle.color=["rgba(248, 182, 72, .6)"],t.xAxis.data=ma.monthElecChart.xData;break;case"plantProgressChart":t=d.clone(x,!0),t.graphic[0].style.text=`${new p(ma.plantProgressChart).times(100).decimalPlaces(2).toNumber()}%`;let l=new p(ma.plantProgressChart).times(100).decimalPlaces(2).toNumber();t.series[0].data[0].value=l,t.series[0].data[1].value=new p(100).minus(l).decimalPlaces(2).toNumber();break;case"inverterProgressChart":t=d.clone(x,!0),t.graphic[0].style.text=`${new p(ma.inverterProgressChart).times(100).decimalPlaces(2).toNumber()}%`,t.graphic[1].style.text="逆变器正常运行率";let s=new p(ma.inverterProgressChart).times(100).decimalPlaces(2).toNumber();t.series[0].data[0].value=s,t.series[0].data[1].value=new p(100).minus(s).decimalPlaces(2).toNumber()}a[e]&&a[e].dispose(),a[e]=m(e,t),ua[e]=!1,c(ee.value,a)},ga=async e=>{try{const a=await C(e);if("00000"==a.status){for(let e in ra)a.data[e]&&(ra[e].value=a.data[e]);sa.totalNum.value=a.data.totalNum,ma.plantProgressChart=a.data.normalRate,aa.loading&&fa("plantProgressChart",ca)}}catch(a){}},ha=async e=>{try{const a=await N(e);if("00000"==a.status){let e=a.data.plantCapacity;e.length>9?(sa.plantCapacity.value=new p(e).div(1e3).decimalPlaces(2).toString(),sa.plantCapacity.unit="(MWp)"):(sa.plantCapacity.value=new p(e).decimalPlaces(2).toString(),sa.plantCapacity.unit="(kWp)");let t=a.data.totalElectricity;t.length>9?(sa.totalElectricity.value=new p(t).div(1e3).decimalPlaces(2).toString(),sa.totalElectricity.unit="(MWh)"):(sa.totalElectricity.value=new p(t).decimalPlaces(2).toString(),sa.totalElectricity.unit="(kWh)"),la.value=`${a.data.todayElectricity}kWh`}}catch(a){}},ya=async e=>{try{const a=await k(e);if("00000"==a.status){for(let e in oa)a.data[e]?oa[e].value=a.data[e]:oa[e].value="NaN";sa.InverterNum.value=a.data.totalNum,ma.inverterProgressChart=a.data.normalRate,aa.loading&&fa("inverterProgressChart",ca)}}catch(a){}},xa=async e=>{try{const a=await E(e);"00000"==a.status&&(ma.monthElecChart={xData:[],data:[]},a.data.forEach((e=>{ma.monthElecChart.xData.push(`${e.collectDate.substr(5,2)}月`),ma.monthElecChart.data.push(new p(e.electricity).div(1e3).decimalPlaces(2).toNumber())})),aa.loading&&fa("monthElecChart",ca))}catch(a){}},ba=async e=>{try{const a=await P(e);"00000"==a.status&&(ma.dayElecChart={xData:[],data:[]},a.data.forEach((e=>{ma.dayElecChart.xData.push(`${e.collectDate.substr(11,5)}时`),ma.dayElecChart.data.push(new p(e.electricity).div(1e3).decimalPlaces(2).toNumber())})),aa.loading&&fa("dayElecChart",ca))}catch(a){}},wa=()=>{ta.visible=!1},ja=async()=>{ta.submitLoading=!0;try{const e=await S(JSON.stringify(ta.layout));ta.submitLoading=!1,"00000"==e.status?(localStorage.setItem("plantOverviewGrid",encodeURIComponent(JSON.stringify(ta.layout))),ta.visible=!1,aa.loading=!1,Qe.value=!0,Ca()):v.modal.message({content:"保存布局异常，请稍后再试",status:"info"})}catch(e){ta.submitLoading=!1}},Ca=()=>{let e=JSON.parse(decodeURIComponent(localStorage.getItem("plantOverviewGrid")));e.layout&&(e=e.layout),ta.layout=e;let a={};e.forEach((e=>{a[e.name]={x:e.x,y:e.y,w:e.w,h:e.h}}));for(let t in aa.layout)aa.layout[t]={...aa.layout[t],...a[aa.layout[t].name]};setTimeout((()=>{aa.loading=!0,Qe.value=!1}),1e3)};q((()=>aa.loading),(e=>{e&&setTimeout((async()=>{await fa("dayElecChart",ca),await fa("monthElecChart",ca),await fa("plantProgressChart",ca),await fa("inverterProgressChart",ca)}),300)})),q((()=>na.filterText),((e,a)=>{Ye.value.filter(e)})),q(Q,((e,a)=>{na.sel=e,(async()=>{Qe.value=!0,ea&&(clearInterval(ea),ea=null),await ga(na.sel),await ha(na.sel),await ya(na.sel),await xa(na.sel),await ba(na.sel),ea||(ea=setInterval((async()=>{await ga(na.sel),await ha(na.sel),await ya(na.sel),await xa(na.sel),await ba(na.sel)}),3e5)),Qe.value=!1})()}));const Na=i(),{isFullScreen:ka}=n(Na),Ea=D((()=>!Na.sidebar.opened));q(Ea,(e=>{c(ee.value,ca)}));let Pa=null;return q(ka,(e=>{Qe.value=!0,aa.loading=!1,Pa&&(clearTimeout(Pa),Pa=null),Pa=setTimeout((()=>{aa.loading=!0,Qe.value=!1}),300)})),$((async()=>{Y.getProjectId(),na.sel=Y.projectId,aa.loading=!0,Qe.value=!0,await ga(na.sel),await ha(na.sel),await ya(na.sel),await xa(na.sel),await ba(na.sel),aa.loading=!1,Qe.value=!1,ea=setInterval((async()=>{await ga(na.sel),await ha(na.sel),await ya(na.sel),await xa(na.sel),await ba(na.sel)}),3e5)})),M((()=>{u(ee.value),ea&&(clearInterval(ea),ea=null)})),(t,l)=>{const s=e,i=O("vxe-modal"),n=a;return W(),L("div",{class:"app-container plantOverview-margin u-flex-column",ref_key:"overviewRef",ref:ee},[R(Qe)?T((W(),L("div",ae,null,512)),[[n,R(Qe)]]):U("",!0),F("div",te,[le,F("div",se,[(W(!0),L(J,null,A(R(sa),((e,a)=>(W(),L("p",{key:a,class:B(["card-style",e.event?`cursor-pointer ${e.bgColor}`:`${e.bgColor}`]),onClick:e.event},[F("span",ne,[F("img",{src:R(I)("plantOverview",e.picName),class:"card-icon-img",alt:e.picName},null,8,re)]),F("span",oe,[F("i",ce,V(e.label)+" "+V(e.unit),1),F("i",ue,V(e.value),1)])],10,ie)))),128))])]),F("div",me,[de,F("div",pe,[(W(!0),L(J,null,A(R(ia),((e,a)=>(W(),L("p",{key:a,class:B(["card-style",e.event?`cursor-pointer ${e.bgColor}`:`${e.bgColor}`])},[F("span",ve,[F("img",{src:R(I)("plantOverview",e.picName),class:"card-icon-img",alt:e.picName},null,8,fe)]),F("span",ge,[F("i",he,V(e.label)+" "+V(e.unit),1),F("i",ye,V(e.value),1)])],2)))),128))])]),F("div",xe,[F("div",be,[we,F("div",je,[F("div",Ce,[(W(!0),L(J,null,A(R(ra),((e,a)=>(W(),L("p",{key:a,class:B(["card-style2",`card-${e.color}`]),onClick:e.event},[F("span",ke,["告警"!==e.label?(W(),L("svg",{key:0,class:B(["icon status-icon",e.color]),"aria-hidden":"true"},[F("use",{"xlink:href":e.icon},null,8,Ee)],2)):(W(),L("img",{key:1,src:R(I)("plantOverview","icon_alarm.png"),class:"icon-alarmNew",alt:"icon_alarm"},null,8,Pe)),F("i",Se,V(e.label),1)]),F("span",Ie,V(e.value),1)],10,Ne)))),128))])])]),F("div",_e,[ze,F("div",qe,[F("div",De,[(W(!0),L(J,null,A(R(oa),((e,a)=>(W(),L("p",{key:a,class:B(["card-style2",`card-${e.color}`]),onClick:e.event},[F("span",Me,["告警"!==e.label?(W(),L("svg",{key:0,class:B(["icon status-icon",e.color]),"aria-hidden":"true"},[F("use",{"xlink:href":e.icon},null,8,Oe)],2)):(W(),L("img",{key:1,src:R(I)("plantOverview","icon_alarm.png"),class:"icon-alarmNew",alt:"icon_alarm"},null,8,We)),F("i",Le,V(e.label),1)]),F("span",Re,V(e.value),1)],10,$e)))),128))])])])]),F("div",Te,[Ue,F("div",Fe,[F("div",Je,[F("p",Ae,[Be,G("  "),F("span",null,V(R(la)),1)]),Ve]),Ge])]),Z(i,{modelValue:R(ta).visible,"onUpdate:modelValue":l[1]||(l[1]=e=>R(ta).visible=e),title:"自定义布局",width:"1200","min-width":"400","min-height":"400",height:"600",loading:R(ta).submitLoading,resize:"","destroy-on-close":"",zIndex:2008},{default:H((()=>[F("div",Ze,[F("div",He,[Z(R(r),{layout:R(ta).layout,"onUpdate:layout":l[0]||(l[0]=e=>R(ta).layout=e),"col-num":2,"row-height":200,"max-rows":10,"is-resizable":!1,style:{width:"100%",height:"100%"}},{default:H((({gridItemProps:e})=>[(W(!0),L(J,null,A(R(ta).layout,(a=>(W(),K(R(o),X({key:a.i},e,{x:a.x,y:a.y,w:a.w,h:a.h,i:a.i}),{default:H((()=>[F("div",Ke,[F("p",null,V(a.text),1)])])),_:2},1040,["x","y","w","h","i"])))),128))])),_:1},8,["layout"])]),F("div",Xe,[Z(s,{onClick:wa},{default:H((()=>[G("取消")])),_:1}),Z(s,{type:"primary",onClick:ja},{default:H((()=>[G("保存")])),_:1})])])])),_:1},8,["modelValue","loading"])],512)}}},[["__scopeId","data-v-3512cd63"]]);export{Ye as default};
