import"./vue-5bfa3a54.js";import{S as t,a as s,A as e}from"./swiper-7f939876.js";import{c as r}from"./icons-95011f8c.js";import{s as l}from"./startEndSlide-8c1800d1.js";import{h as i}from"./homeApi-54bb989e.js";import{g as o}from"./api-b858041e.js";import{_ as a}from"./index-8cc8d4b8.js";import{u as p,h as m,m as n,o as u,c as d,f as j,a8 as c,F as v,k as f,b as h,a as w,t as b,r as y,q as x,l as g,C as k,D as C}from"./@vue-5e5cdef9.js";import{C as S}from"./@vueuse-af86c621.js";import{n as z}from"./@vicons-f32a0bdb.js";import"./@babel-f3c0a00c.js";import"./naive-ui-0ee0b8c3.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./lodash-es-ea7deab5.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./date-fns-tz-0cc073c8.js";import"./async-validator-cf877c1f.js";import"./index-15186f59.js";import"./dayjs-d60cc07f.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./menuStore-26f8ddd8.js";import"./vue-router-6159329f.js";import"./lodash-6d99edc3.js";import"./notification-950a5f80.js";import"./axios-84f1a956.js";import"./quasar-b3f06d8a.js";import"./element-plus-d975be09.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./xe-utils-fe99d42a.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-7bbbb435.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";const _={class:"tw-w-full tw-rounded main"},R=(t=>(k("data-v-2f4b30d0"),t=t(),C(),t))((()=>w("tr",{class:"table-title tw-grid tw-h-[70px] tw-rounded-tr-xl tw-rounded-tl-xl"},[w("th",null,"序号"),w("th",null,"电站名称"),w("th",null,"所有人"),w("th",null,"市"),w("th",null,"当前效率（W）"),w("th",null,"电站容量(KWp)"),w("th",null,"当日发电量(KWh)"),w("th",null,"累计发电量(KWh)"),w("th",null,"CO2减排(t)"),w("th",null,"创建时间"),w("th",null,"运行状态"),w("th",null,"离线时间")],-1))),W={class:"tw-grid text-ellipsis"},E={class:"tw-inline-flex tw-items-center tw-justify-center"},q=a({__name:"tableWrapper - 副本",setup(a){p((t=>({"69a2d2ea":I.value})));const k=z,C=r,q=[e],N=new l(10,20,10,100),T=m([]),[A,K]=S(),I=m([1,3,2,1,2,2,2,2,2,2,2,2].join("fr ")+"fr"),O={"正常运行":"CheckmarkCircleSharp","离线":"RemoveCircleSharp","告警运行":"RemoveCircleSharp","自检提示":"RemoveCircleSharp","未初始化":"CloseCircleSharp","电站状态异常":"RemoveCircleSharp"},Z={"正常运行":"#1aa034","离线":"#8d8d8d","告警运行":"#ff0000","自检提示":"#ffaa00","未初始化":"#a0a0a0","电站状态异常":"#ffff00"};async function B(){T.value.length>=20&&T.value.splice(A.value?N.step:0,N.step,...await async function(){const t=await i(...N.next()),s=o(t);return N.resetTotal(s.data.total),K(),s.data.records}())}return n((async()=>{const t=await i(0,20),s=o(t);T.value=s.data.records})),(e,r)=>(u(),d("div",_,[R,T.value.length?(u(),j(h(s),{key:0,id:"swiperlist","slides-per-view":10,autoplay:{delay:1e3,disableOnInteraction:!0},speed:300,"space-between":0,direction:"vertical",scrollbar:{draggable:!1},loop:!0,modules:q,onReachEnd:B,class:"tw-h-full tw-rounded-bl-xl tw-rounded-br-xl"},{default:c((()=>[(u(!0),d(v,null,f(T.value,((s,e)=>(u(),j(h(t),{key:e,class:"tw-w-full table-content"},{default:c((()=>[w("tr",W,[w("td",null,b(s.index),1),w("td",null,b(s.plantName),1),w("td",null,b(s.userName),1),w("td",null,b(s.city),1),w("td",null,b(s.power),1),w("td",null,b(s.plantCapacity),1),w("td",null,b(s.todayElectricity),1),w("td",null,b(s.totalElectricity),1),w("td",null,b(s.co2),1),w("td",null,b(s.createTime.slice(0,10)),1),w("td",E,["正常运行"==s.status?(u(),j(y(h(C)("https://www.btosolarman.com/assets/btosolar/picture/carouselNormal.png")),{key:0,class:"tw-w-4",style:x({color:Z[s.status]})},null,8,["style"])):(u(),j(y(h(k)[O[s.status]]),{key:1,class:"tw-w-4",style:x({color:Z[s.status]})},null,8,["style"]))]),w("td",null,b(s.offineTime),1)])])),_:2},1024)))),128))])),_:1})):g("",!0)]))}},[["__scopeId","data-v-2f4b30d0"]]);export{q as default};
