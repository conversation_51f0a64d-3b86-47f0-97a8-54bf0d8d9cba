import{b as e,a,D as t,F as l,K as o,r as s,x as r,C as i,f as u,G as d,L as n,A as p}from"./element-plus-d975be09.js";import"./vue-5bfa3a54.js";import{V as c}from"./vxe-table-3a25f2d2.js";import{u as m,a as f,b as g,c as j,d as b,e as h,i as v,g as y}from"./index-5f1b0ee7.js";import{u as _,a as w}from"./user-cde6e84b.js";import{m as V}from"./md5-4d9c0b64.js";import{T as S}from"./@element-plus-4c34063a.js";import{n as P}from"./@vicons-f32a0bdb.js";import{l as k}from"./lodash-6d99edc3.js";import{p as D,_ as x}from"./index-8cc8d4b8.js";import{h as T,j as U,w as C,m as O,as as z,o as q,c as N,x as I,a8 as R,a as F,aa as B,b as E,F as A,k as L,f as M,t as G,q as K,a6 as $,l as H,C as W,D as Z}from"./@vue-5e5cdef9.js";import"./lodash-es-ea7deab5.js";import"./@vueuse-af86c621.js";import"./vue-router-6159329f.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./dayjs-d60cc07f.js";import"./@babel-f3c0a00c.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./xe-utils-fe99d42a.js";import"./dom-zindex-5f662ad1.js";import"./crypt-d45ab006.js";import"./charenc-ab2e9fee.js";import"./is-buffer-9e0e61f6.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./nprogress-e136a1b4.js";import"./quasar-b3f06d8a.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-7bbbb435.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";const J={class:"form"},Q=(e=>(W("data-v-fb40a926"),e=e(),Z(),e))((()=>F("p",null,"高级筛选",-1))),X={class:"u-flex-y-center justify-end"},Y={class:"table-btn"},ee={class:"flex justify-center items-center"},ae={class:"flex"},te={key:0},le={key:1},oe=x(Object.assign({name:"user"},{__name:"index",setup(x){const W=T(),Z=T(),oe=P,se=T(),re=T(),ie=T(!0),ue=T(!1),de=T(!1);T(!1);const ne=U({condition:{userName:"",userPhone:"",userStatus:"",userType:"",projectSpecial:"",projectProps:{value:"id",multiple:!0,label:"projectName",checkStrictly:!1}},tablePage:{totalResult:0,currentPage:1,pageSize:15},modelData:{userTitle:"新增用户",projectProps:{value:"id",label:"projectName",emitPath:!1,checkStrictly:!0},selectOptions:{value:"id",label:"projectName"},projectStatus:!0,userName:"",userStatus:"",projectID:"30",userType:"",roleID:"",userPhone:"",userEmail:"",userPassword:""}}),pe=k._.omit(k._.cloneDeep(ne.condition),[]),ce=k._.omit(k._.cloneDeep(ne.modelData),["projectProps"]);k._.curry(D)("/deviceMonitor/plantDetail?plantUid=");const me=U({userName:[{required:!0,message:"该输入框不能为空",trigger:"blur"}],userType:[{required:!0,message:"该选择器不能为空",trigger:"blur"}],userStatus:[{required:!0,message:"该选择不能为空",trigger:"blur"}],projectID:[{required:!0,message:"该选择不能为空",trigger:"blur"}],roleID:[{required:!0,message:"该选择不能为空",trigger:"blur"}],userPhone:[{required:!0,message:"该输入框不能为空",trigger:"blur"}],roleName:[{required:!0,message:"该输入框不能为空",trigger:"blur"}],label:[{required:!0,message:"该输入框不能为空",trigger:"blur"}],type:[{required:!0,message:"该输入框不能为空",trigger:"blur"}],path:[{required:!0,message:"该输入框不能为空",trigger:"blur"}],projectName:[{required:!0,message:"该输入框不能为空",trigger:"blur"}],userPassword:[{required:!0,message:"该输入框不能为空",trigger:"blur"}]}),fe=U({border:"full",showFooter:!1,loading:!1,minHeight:600,height:"auto",autoResize:!0,columnConfig:{resizable:!0},customConfig:{storage:{visible:!0,fixed:!0},checkMethod:({column:e})=>!["seq"].includes(e.field)},editConfig:{trigger:"click",mode:"cell"},rowConfig:{},sortConfig:{remote:!0},data:[],toolbarConfig:{custom:{allowFixed:!1},slots:{buttons:"toolbar_buttons",tools:"toolbar_tools"}},columns:[{field:"seq",type:"seq",width:50},{field:"userName",title:"用户名",width:"150"},{field:"userType",title:"用户类型",width:"150"},{field:"userStatus",title:"用户状态",width:"150",slots:{default:"row-userStatus"}},{field:"projectName",title:"项目专项",width:"280"},{field:"roleName",title:"用户角色",width:"150"},{field:"userPhone",title:"手机号",width:"150"},{field:"userEmail",title:"邮箱",width:"auto"},{field:"createTime",title:"创建时间",width:"auto"},{field:"operations",title:"操作",fixed:"right",showOverflow:!1,slots:{default:"row-operate"},width:250}]}),ge=({row:e,_rowIndex:a,column:t,visibleData:l,columnIndex:o})=>{const s=e[t.field];if("合计："===s&&"plantType"===t.field)return{rowspan:1,colspan:3};if("合计："===s&&"city"===t.field)return{rowspan:1,colspan:0};if("合计："===s&&"projectId"===t.field)return{rowspan:1,colspan:0};if(s&&["plantType","projectId"].includes(t.field)){const e=l[a-1];let o=l[a+1];if(e&&e[t.field]===s)return{rowspan:0,colspan:0};{let e=1;for(;o&&o[t.field]===s;)o=l[++e+a];if(e>1)return{rowspan:e,colspan:1}}}},je=U({userStatusOptions:_,userTypeOptions:w,userRoleOptions:[],projectOptions:[]}),be=({field:e,order:a})=>{ne.condition.order=e,null!==a?ne.condition.isAsc="asc"===a:(ne.condition.order="",ne.condition.isAsc=""),Se()},he=(e,a)=>{},ve=async()=>{de.value=!0;let e="";try{"新增用户"===ne.modelData.userTitle?e=await m({...k._.omit(ne.modelData,["projectProps","userPassword"])}):"编辑用户"===ne.modelData.userTitle?e=await f({...k._.omit(ne.modelData,["projectProps","userStatus","userPassword"])}):(ne.modelData.userPassword=V(ne.modelData.userPassword),e=await f({...k._.pick(ne.modelData,["userUid","userPassword"])}))}catch(a){de.value=!1}de.value=!1,"00000"===e.status&&await Se(),ue.value=!1},ye=e=>{Object.assign(ne.modelData,ce),ne.modelData.userTitle="新增用户",ue.value=!0};const _e=e=>{Se(e.currentPage,e.pageSize)},we=()=>{Object.assign(ne.condition,pe)};const Ve=async e=>{try{const{file:e}=await c.readFile({types:["xlsx","xls"]});await v(e);await Se()}catch(a){}},Se=async(e=1,a=15)=>{fe.loading=!0,ne.tablePage.currentPage=e,ne.tablePage.pageSize=a,ne.condition.projectSpecial=k._.uniq(k._.flattenDeep(ne.condition.projectSpecial));const t=await y({...ne.condition,...ne.tablePage});fe.loading=!1,fe.data=t.data.records,ne.tablePage.totalResult=t.data.total};return C((()=>ne.modelData.userType),((e,a)=>{"0"==e||"个人用户"==e?(ne.modelData.projectStatus=!1,ne.modelData.projectProps.checkStrictly=!1):"1"==e||"企业用户"==e?(ne.modelData.projectStatus=!1,ne.modelData.projectProps.checkStrictly=!0):ne.modelData.projectStatus=!0}),{immediate:!0,deep:!0}),O((()=>{Se(),async function(){try{const e=await b();je.projectOptions=e.data[0].children}catch(e){}}(),async function(){try{const e=await h(1,100,"");je.userRoleOptions=e.data.records}catch(e){}}()})),(c,m)=>{const f=z("vxe-input"),b=z("vxe-form-item"),h=z("vxe-button"),v=t,y=l,_=o,w=z("vxe-form"),V=s,P=r,k=i,D=u,x=d,T=z("vxe-pager"),U=z("vxe-grid"),C=n,O=p,pe=z("vxe-modal");return q(),N("div",{ref_key:"appContainerRef",ref:se,class:"app-container"},[I(U,$({id:"userTable",ref_key:"xGrid",ref:re,"scroll-y":{enabled:!1},"span-method":ge,class:"my-grid66"},fe,{onCustom:he,onSortChange:be}),{form:R((()=>[F("div",J,[I(w,{ref_key:"ordinaryForm",ref:Z,collapseStatus:ie.value,"onUpdate:collapseStatus":m[5]||(m[5]=e=>ie.value=e),data:ne.condition},{default:R((()=>[I(b,{field:"userName",title:"用户名"},{default:R((({data:e})=>[I(f,{modelValue:e.userName,"onUpdate:modelValue":a=>e.userName=a,placeholder:"请输入用户名"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),I(b,null,{default:R((()=>[I(h,{status:"danger",onClick:m[0]||(m[0]=e=>we())},{default:R((()=>[B("重置")])),_:1})])),_:1}),I(b,null,{default:R((()=>[I(h,{status:"primary",onClick:m[1]||(m[1]=e=>Se(1))},{default:R((()=>[B("查询")])),_:1})])),_:1}),I(b,null,{default:R((()=>[I(P,{visible:ne.detailVis,width:500,placement:"bottom",trigger:"click"},{reference:R((()=>[I(V,{class:"align-middle cursor-pointer",size:"22",onClick:m[4]||(m[4]=e=>ne.detailVis=!ne.detailVis)},{default:R((()=>[I(E(S))])),_:1})])),default:R((()=>[Q,F("div",X,[I(w,{ref_key:"seniorForm",ref:W,data:ne.condition},{default:R((()=>[I(b,{field:"userPhone",title:"手机号"},{default:R((({data:e})=>[I(f,{modelValue:e.userPhone,"onUpdate:modelValue":a=>e.userPhone=a,placeholder:"请输入手机号"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),I(b,{field:"userStatus",title:"用户状态"},{default:R((({data:e})=>[I(y,{modelValue:e.userStatus,"onUpdate:modelValue":a=>e.userStatus=a,style:{width:"180px"},clearable:"",placeholder:"用户状态"},{default:R((()=>[(q(!0),N(A,null,L(je.userStatusOptions,(e=>(q(),M(v,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue"])])),_:1}),I(b,{field:"userType",title:"用户类型"},{default:R((({data:e})=>[I(y,{modelValue:e.userType,"onUpdate:modelValue":a=>e.userType=a,style:{width:"180px"},clearable:"",placeholder:"用户类型"},{default:R((()=>[(q(!0),N(A,null,L(je.userTypeOptions,(e=>(q(),M(v,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue"])])),_:1}),I(b,{field:"projectSpecial",title:"项目专项"},{default:R((({data:e})=>[I(_,{modelValue:e.projectSpecial,"onUpdate:modelValue":a=>e.projectSpecial=a,checkStrictly:!1,options:je.projectOptions,props:ne.condition.projectProps,class:"tw-w-full",clearable:"","collapse-tags":"","collapse-tags-tooltip":"",placeholder:"项目专项"},null,8,["modelValue","onUpdate:modelValue","options","props"])])),_:1}),I(b,{span:"24"},{default:R((()=>[I(h,{status:"danger",onClick:m[2]||(m[2]=e=>we())},{default:R((()=>[B("重置")])),_:1}),I(h,{status:"primary",onClick:m[3]||(m[3]=e=>Se(1))},{default:R((()=>[B("查询")])),_:1})])),_:1})])),_:1},8,["data"])])])),_:1},8,["visible"])])),_:1})])),_:1},8,["collapseStatus","data"])])])),toolbar_buttons:R((()=>[])),toolbar_tools:R((()=>[F("div",Y,[I(h,{status:"primary",onClick:ye},{default:R((()=>[B("新增")])),_:1}),I(h,{status:"primary",onClick:Ve},{default:R((()=>[B("导入")])),_:1})])])),top:R((()=>[])),"row-plantStatus":R((({row:e})=>[I(k,{type:"正常"===e.plantStatus?"success":"warning"},{default:R((()=>[B(G(e.plantStatus),1)])),_:2},1032,["type"])])),"row-userStatus":R((({row:e})=>[F("div",ee,[F("div",{style:K({background:"启用"===e.userStatus?"#24b276":"grey"}),class:"circle"},null,4),F("span",null,G(e.userStatus),1)])])),"row-operate":R((({row:t})=>[F("div",ae,[I(x,{content:"修改",effect:"dark",placement:"top"},{default:R((()=>[I(D,{icon:E(oe).PencilSharp,plain:"",onClick:e=>(e=>{Object.assign(ne.modelData,e),ne.modelData.userType="个人用户"===ne.modelData.userType?"0":"1",ne.modelData.userTitle="编辑用户",ue.value=!0})(t)},null,8,["icon","onClick"])])),_:2},1024),I(x,{content:"启用"===t.userStatus?"禁用":"启用",effect:"dark",placement:"top"},{default:R((()=>[I(D,{icon:"启用"===t.userStatus?E(oe).BanSharp:E(oe).PlayCircleSharp,type:"启用"===t.userStatus?"primary":"success",plain:"",onClick:l=>function(t,l){const o="启用"===t?"禁用":"启用";e.confirm(`是否要${o}该用户`,{confirmButtonText:"确定",cancelButtonText:"取消"}).then((async()=>{try{const e=await g("启用"===t?"0":"1",l);"00000"===e.status?(a.success("状态修改成功"),Se()):a.error(e.message)}catch(e){}})).catch((()=>{}))}(t.userStatus,t.userUid)},null,8,["icon","type","onClick"])])),_:2},1032,["content"]),I(x,{content:"重置密码",effect:"dark",placement:"top"},{default:R((()=>[I(D,{icon:E(oe).LockClosedSharp,color:"#626aef",plain:"",onClick:e=>{return a=t.userUid,Object.assign(ne.modelData,{userUid:a}),ne.modelData.userTitle="修改密码",void(ue.value=!0);var a}},null,8,["icon","onClick"])])),_:2},1024),I(x,{content:"删除",effect:"dark",placement:"top"},{default:R((()=>[I(D,{icon:E(oe).TrashSharp,plain:"",type:"danger",onClick:l=>{return o=t.userUid,void e.confirm("是否要删除该用户？(该删除操作不可逆)",{confirmButtonText:"确定",cancelButtonText:"取消"}).then((async()=>{try{const e=await j(o);"00000"===e.status?(a.success("用户删除成功"),Se()):a.error(e.message)}catch(e){}})).catch((()=>{}));var o}},null,8,["icon","onClick"])])),_:2},1024)])])),bottom:R((()=>[])),pager:R((()=>[I(T,{"current-page":ne.tablePage.currentPage,"onUpdate:currentPage":m[6]||(m[6]=e=>ne.tablePage.currentPage=e),"page-size":ne.tablePage.pageSize,"onUpdate:pageSize":m[7]||(m[7]=e=>ne.tablePage.pageSize=e),"page-sizes":[10,15,20],total:ne.tablePage.totalResult,perfect:"",onPageChange:_e},null,8,["current-page","page-size","total"])])),_:1},16),I(pe,{modelValue:ue.value,"onUpdate:modelValue":m[10]||(m[10]=e=>ue.value=e),loading:de.value,title:ne.modelData.userTitle,"destroy-on-close":"",escClosable:"","min-height":"300","min-width":"600",resize:"",width:"800"},{default:R((()=>[I(w,{data:ne.modelData,rules:me,"title-align":"right","title-width":"100",onSubmit:ve},{default:R((()=>["新增用户"===ne.modelData.userTitle||"编辑用户"===ne.modelData.userTitle?(q(),N("div",te,[I(b,{"item-render":{},span:12,field:"userName",title:"用户名"},{default:R((({data:e})=>[I(f,{modelValue:e.userName,"onUpdate:modelValue":a=>e.userName=a,placeholder:"请输入用户名"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),I(b,{"item-render":{},span:12,field:"userType",title:"用户类型"},{default:R((({data:e})=>[I(y,{modelValue:e.userType,"onUpdate:modelValue":a=>e.userType=a,class:"tw-w-full",clearable:"",placeholder:"用户类型","popper-class":"Pc"},{default:R((()=>[(q(!0),N(A,null,L(je.userTypeOptions,(e=>(q(),M(v,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue"])])),_:1}),"新增用户"===ne.modelData.userTitle?(q(),M(b,{key:0,"item-render":{},span:12,field:"projectID",title:"用户状态"},{default:R((({data:e})=>[I(O,{modelValue:e.userStatus,"onUpdate:modelValue":a=>e.userStatus=a},{default:R((()=>[(q(!0),N(A,null,L(je.userStatusOptions,(e=>(q(),M(C,{label:e.value,size:"large"},{default:R((()=>[B(G(e.label),1)])),_:2},1032,["label"])))),256))])),_:2},1032,["modelValue","onUpdate:modelValue"])])),_:1})):H("",!0),I(b,{"item-render":{},span:12,field:"projectID",title:"项目专项"},{default:R((({data:e})=>[I(_,{modelValue:e.projectID,"onUpdate:modelValue":a=>e.projectID=a,disabled:ne.modelData.projectStatus,options:je.projectOptions,props:ne.modelData.projectProps,class:"tw-w-full",clearable:"",placeholder:"项目专项","popper-class":"Pc"},null,8,["modelValue","onUpdate:modelValue","disabled","options","props"])])),_:1}),I(b,{"item-render":{},span:12,field:"roleID",title:"用户角色"},{default:R((({data:e})=>[I(y,{modelValue:e.roleID,"onUpdate:modelValue":a=>e.roleID=a,class:"tw-w-full",clearable:"",placeholder:"用户角色","popper-class":"Pc"},{default:R((()=>[(q(!0),N(A,null,L(je.userRoleOptions,(e=>(q(),M(v,{key:e.roleID,label:e.roleName,value:e.roleID},null,8,["label","value"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue"])])),_:1}),I(b,{"item-render":{},span:12,field:"userPhone",title:"手机号"},{default:R((({data:e})=>[I(f,{modelValue:e.userPhone,"onUpdate:modelValue":a=>e.userPhone=a,placeholder:"请输入手机号"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),I(b,{"item-render":{},span:12,field:"userEmail",title:"邮箱"},{default:R((({data:e})=>[I(f,{modelValue:e.userEmail,"onUpdate:modelValue":a=>e.userEmail=a,placeholder:"请输入邮箱"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),I(b,{span:24,align:"center","title-align":"left"},{default:R((()=>[I(h,{type:"submit"},{default:R((()=>[B("提交")])),_:1}),I(h,{type:"cancel",onClick:m[8]||(m[8]=e=>ue.value=!1)},{default:R((()=>[B("取消")])),_:1})])),_:1})])):(q(),N("div",le,[I(b,{"item-render":{},span:24,field:"userPassword",title:"新密码"},{default:R((({data:e})=>[I(f,{modelValue:e.userPassword,"onUpdate:modelValue":a=>e.userPassword=a,placeholder:"请输入新密码"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),I(b,{span:24,align:"center","title-align":"left"},{default:R((()=>[I(h,{type:"submit"},{default:R((()=>[B("提交")])),_:1}),I(h,{type:"cancel",onClick:m[9]||(m[9]=e=>ue.value=!1)},{default:R((()=>[B("取消")])),_:1})])),_:1})]))])),_:1},8,["data","rules"])])),_:1},8,["modelValue","loading","title"])],512)}}}),[["__scopeId","data-v-fb40a926"]]);export{oe as default};
