import{_ as e,u as t,a as s}from"./index-8cc8d4b8.js";import{_ as i}from"./index-f2383b94.js";import{X as l}from"./element-plus-d975be09.js";import"./vue-5bfa3a54.js";import{g as n,a,b as r}from"./index-791aa09c.js";import{g as o}from"./imgImport-3dead1a5.js";import"./fabric-8dd10b04.js";import{K as c}from"./konva-5c630d74.js";import{g as d}from"./gsap-91da67c7.js";import{x as p}from"./xe-utils-fe99d42a.js";import{d as u}from"./dayjs-d60cc07f.js";import{l as m}from"./lodash-6d99edc3.js";import{d as f}from"./@vueuse-af86c621.js";import{h as v,j as g,m as h,v as y,o as x,c as j,a as w,t as b,b as _,f as k,l as A,x as z,F as I,k as R,y as N}from"./@vue-5e5cdef9.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./vue-router-6159329f.js";import"./bignumber.js-a537a5ca.js";import"./js-cookie-8253c38e.js";import"./axios-84f1a956.js";import"./lodash-es-ea7deab5.js";import"./spark-md5-022b35d0.js";import"./@babel-f3c0a00c.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";import"./quasar-b3f06d8a.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./@element-plus-4c34063a.js";import"./vxe-table-plugin-element-7bbbb435.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./screenfull-c82f2093.js";const S={class:"header"},C={class:"date font-small-text-size2 normal regular-title"},Y={class:"title"},L={class:"main-title font-title-size text"},D={class:"screen-full"},E={class:"left-sensor"},G={class:"item-title-bg font-title-size"},T=["src"],X={class:"sensor-box"},M={key:0,class:"circle-img"},W=["src"],q=["src"],$={key:1,class:"circle-img"},O=["src"],H={class:"u-flex-column"},U={class:"font-small-text-size2"},B={key:0},F={key:1},J={class:"smokeValue font-small-text-size1"},K={class:"smokeUnit font-small-text-size3"},P={class:"right-sensor"},V={class:"item-title-bg font-title-size"},Z=["src"],Q={class:"sensor-box"},ee={key:0,class:"circle-img"},te=["src"],se=["src"],ie={key:1,class:"circle-img"},le=["src"],ne={class:"u-flex-column"},ae={class:"font-small-text-size2"},re={key:0},oe={key:1},ce={class:"smokeValue font-small-text-size1"},de={class:"smokeUnit font-small-text-size3"},pe={class:"title"},ue={class:"item-title-bg font-title-size"},me=["src"],fe={class:"right-wiringDiagram"},ve={class:"title"},ge={class:"item-title-bg font-title-size"},he=["src"],ye=e({__name:"gridConnectedCabinet",setup(e){const ye=v([]),xe=v({}),je=v({}),we=t(),be=v(),_e=g({data:u().format("YYYY-MM-DD HH:mm:ss")});v(),v(),v(),v(),v(),v(),v(),v(),v(),v();const ke=g([]),Ae={alarmStatus:{icon:"",label:"故障状态",value:"报警",unit:"",picName:"warn.png",end:!1},smokeConcentr:{icon:"mist",label:"烟雾浓度",value:0,unit:"PPM",picName:"",end:!1},temp:{icon:"temp",label:"当前温度",value:0,unit:"°C",picName:"",end:!1},humidity:{icon:"humidity",label:"当前湿度",value:0,unit:"%RH",picName:"",end:!0}},ze=g([]),Ie=v(null),Re=v(null),Ne=v(null),Se=g({width:1200,height:900});let Ce=null,Ye={canvasStage:null,layer:null};const Le={canvasStage:null,layer:null};(new window.Image).src=o("bingwang","1.png");const De=(e,t,s,i,l,n)=>{const a=new c.Rect({id:"promptRect",x:s,y:i,stroke:"#555",strokeWidth:5,fill:"#ddd",width:200,height:200,shadowColor:"black",shadowBlur:10,shadowOffsetX:10,shadowOffsetY:10,shadowOpacity:.2,cornerRadius:10});let r=new c.Text({id:"promptText",x:s,y:i,text:`${(null==l?void 0:l.name)||""}\n\n设备id: ${l.deviceId}\n\n运行状态:${1===l.status?"正常运行":"异常"}\n\n今日发电量: ${l.todayEle}`,fontSize:16,fontFamily:"Calibri",fill:"#555",width:200,padding:20,align:"center"});void 0!==Ye.layer.find("#promptRect")[0]?(Ye.layer.find("#promptRect").forEach((e=>{e.destroy()})),Ye.layer.find("#promptText").forEach((e=>{e.destroy()}))):void 0!==Le.layer.find("#promptRect")[0]&&(Le.layer.find("#promptRect").forEach((e=>{e.destroy()})),Le.layer.find("#promptText").forEach((e=>{e.destroy()}))),n||(t.add(a),t.add(r))},Ee=v([]),Ge=(e,t,s,i,l)=>{for(let r=0;r<s.length;r++){const e=[0,2,4,1,3],t=[5.7,7.7,9.7,8.7,6.7];let l=0;s[r].index=r;for(let n=0;n<i.length;n++)0===r&&i[n].pid===s[r].deviceId?(i[n].index=e[l],l++):1===r&&i[n].pid===s[r].deviceId&&(i[n].index=t[l],l++)}Ce&&Ce.clear(),Ce=new c.Stage({container:e.value,width:940,height:860,scale:t});const n=new c.Layer;"left"===l?(Ye.canvasStage=Ce,Ye.layer=n):(Le.canvasStage=Ce,Le.layer=n),Ce.add(n),Ce.on("click",(e=>{De(0,n,m._,m._,m._,!0)})),s.forEach((e=>{c.Image.fromURL(o("bingwang","1.png"),(function(s){switch(e.index){case 0:case 1:"left"===l&&s.setAttrs({name:e.index+1}),"right"===l&&s.setAttrs({name:e.index+3})}s.setAttrs({id:e.deviceId,x:200+450*e.index,y:300,scaleX:.4,scaleY:.4}),s.on("click",(async s=>{await r(e),De(0,n,s.evt.offsetX/t.x,s.evt.offsetY/t.y,e)})),Ee.value.push(s),n.add(s),n.batchDraw()}))})),i.forEach((e=>{c.Image.fromURL(o("bingwang","R6-30-50K.png"),(function(s){s.setAttrs({id:e.deviceId,x:80*e.index+40,y:0,scaleX:.2,scaleY:.2}),s.on("click",(async s=>{const{data:i}=await r(e);9===Math.floor(e.index)?(t.x<1&&De(0,n,s.evt.offsetX-80,s.evt.offsetY,e),t.x>=1&&De(0,n,s.evt.offsetX-160,s.evt.offsetY,e)):De(0,n,s.evt.offsetX/t.x,s.evt.offsetY/t.y,e)})),n.add(s),n.batchDraw()}))}));let a={line1:[100,90,100,350,200,350],line2:[180,90,180,330,200,330],line3:[260,90,260,350],line4:[345,90,345,330,300,330],line5:[420,90,420,350,300,350],line6:[555,90,555,350,650,350],line7:[635,90,635,330,655,330],line8:[715,90,715,350],line9:[795,90,795,330,765,330],line10:[875,90,875,350,750,350]};const p=Object.keys(a);let u=[];i.forEach((e=>{u.push(`line${Math.floor(e.index)+1}`)}));const f=p.filter((e=>u.includes(e))).reduce(((e,t)=>(e[t]=a[t],e)),{});for(let r in f){const e=r.split("line")[1],t=i.find((t=>Math.floor(t.index)+1==e));f[r]=new c.Line({points:f[r],stroke:"green",strokeWidth:5,lineCap:"round",lineJoin:"round"}),n.add(f[r]);const s=new c.Rect({id:r,x:f[r].attrs.points[0]-5,y:f[r].attrs.points[1],width:10,height:10,fill:0!=t.status?"blue":"red"});n.add(s);const l=f[r].attrs.points[0]-5,a=f[r].attrs.points[1],o=f[r].attrs.points.at(-2),p=f[r].attrs.points.at(-1)-5;if(0!=t.status){const e=d.timeline({paused:!0});e.to(s,{y:p,duration:1}).to(s,{x:o,duration:1},"<+=1"),e.to(s,{x:l,duration:0}).to(s,{y:a,duration:0}).addLabel("Done","+=0"),e.add((()=>e.seek(0)),"Done+=0.01"),e.play()}}const v={line_1:[220,500,220,650,180,650,180,700],point_1:[180,700],group_1:new c.Group,line_2:[250,500,250,700],point_2:[250,700],group_2:new c.Group,line_3:[270,500,270,700],point_3:[270,700],group_3:new c.Group,line_4:[300,500,300,650,340,650,340,700],point_4:[340,700],group_4:new c.Group,line_5:[670,500,670,650,630,650,630,700],point_5:[630,700],group_5:new c.Group,line_6:[700,500,700,700],point_6:[700,700],group_6:new c.Group,line_7:[730,500,730,650,730,650,730,700],point_7:[730,700],group_7:new c.Group,line_8:[760,500,760,650,800,650,800,700],point_8:[800,700],group_8:new c.Group};for(let r in v)r.includes("line")?v[r]=new c.Line({points:v[r],stroke:"green",fill:"blue",strokeWidth:5,lineCap:"round",lineJoin:"round"}):r.includes("point")?v[r]=new c.Circle({x:v[r][0],y:v[r][1],radius:5,fill:"red",stroke:"red",strokeWidth:4}):(v[r].add(v["line_"+r.split("_")[1]]),v[r].add(v["point_"+r.split("_")[1]]),n.add(v[r]))},Te={x:1,y:1},Xe=p.debounce((()=>Ge(Re,Te,xe.value.bwgArr,xe.value.nbqArr,"left")),500),Me=p.debounce((()=>Ge(Ne,Te,je.value.bwgArr,je.value.nbqArr,"right")),500);return h((()=>{(async()=>{const{data:e}=await n();for(let t=0;t<e.length;t++){const s=p.clone(Ae,!0),i=Object.keys(s);ke.push(e[t].deviceName);for(let l=0;l<i.length;l++)"alarmStatus"===i[l]&&0==e[t].children[0].alarmStatus?s[i[l]].value="正常":"alarmStatus"===i[l]&&1==e[t].children[0].alarmStatus?s[i[l]].value="告警":s[i[l]].value=e[t].children[0][i[l]];ze.push(s)}})(),(async()=>{const e=[],t=[],s=[],i=[],{data:l}=await a();ye.value=l,ye.value.forEach((l=>{10==l.deviceType?e.push(l):11==l.deviceType?t.push(l):1==l.deviceType?s.push(l):12==l.deviceType&&i.push(l)}));for(let n=0;n<e.length;n++){if(e[0]){xe.value.pdfArr=e[0],xe.value.bwgArr=t.filter((t=>t.pid===e[0].deviceId)),xe.value.wdArr=i.filter((t=>t.pid===e[0].deviceId));const l=xe.value.bwgArr.map((e=>e.deviceId));xe.value.nbqArr=s.filter((e=>l.includes(e.pid)))}if(e[1]){je.value.pdfArr=e[1],je.value.bwgArr=t.filter((t=>t.pid===e[1].deviceId)),je.value.wdArr=i.filter((t=>t.pid===e[1].deviceId));const l=je.value.bwgArr.map((e=>e.deviceId));je.value.nbqArr=s.filter((e=>l.includes(e.pid)))}}})(),f(Ie,(e=>{Se.width=Ie.value.offsetWidth,Se.height=Ie.value.offsetHeight,Te.x=Se.width/940,Te.y=Se.height/860,Xe(),Me(),Ee.value=[]}))})),y((()=>{})),(e,t)=>{var n,a,r,c,d,p,u,m;const f=l,v=i,g=s;return x(),j("div",{class:"screenfull-content tw-h-full tw-w-full bwg-bg screen-box",ref_key:"screenRef",ref:be},[w("div",S,[w("div",C,b(_(_e).data),1),w("div",Y,[_(we).userInfo.screenLogo?(x(),k(f,{key:0,src:_(we).userInfo.screenLogo,fit:"contain",class:"image"},null,8,["src"])):A("",!0),w("h1",L,b(_(we).userInfo.projectTitle),1)]),w("div",D,[z(v,{class:"setting-item",type:"font",element:_(be)},null,8,["element"])])]),w("div",E,[w("div",null,[w("p",G,[w("img",{src:_(o)("screen","title_icon.png"),class:"title-icon"},null,8,T),w("span",null,b(null==(n=_(ke))?void 0:n[0]),1)])]),w("div",X,[(x(!0),j(I,null,R(null==(a=_(ze))?void 0:a[0],((e,t)=>(x(),j("div",{key:t,class:"u-flex-1 h-full u-flex-center-no asset-content-item"},[""!==e.picName?(x(),j("p",M,[w("img",{src:_(o)("screen","circle.png"),class:"title-icon0"},null,8,W),w("img",{src:_(o)("screen",e.picName),class:"title-icon1"},null,8,q)])):(x(),j("p",$,[w("img",{src:_(o)("screen","circle.png"),class:"title-icon0"},null,8,O),z(g,{name:e.icon,class:"title-icon1"},null,8,["name"])])),w("p",H,[w("span",U,b(e.label),1),"故障状态"===e.label?(x(),j("span",B,[w("i",{class:N(["font-small-text-size1",!0===e.value?"normal":"abnormal"])},b(e.value),3)])):(x(),j("span",F,[w("i",J,b(e.value),1),w("i",K,b(e.unit),1)]))])])))),128))])]),w("div",P,[w("p",V,[w("img",{src:_(o)("screen","title_icon.png"),class:"title-icon"},null,8,Z),w("span",null,b(null==(r=_(ke))?void 0:r[1]),1)]),w("div",Q,[(x(!0),j(I,null,R(null==(c=_(ze))?void 0:c[1],((e,t)=>(x(),j("div",{key:t,class:"u-flex-1 h-full u-flex-center-no asset-content-item"},[""!==e.picName?(x(),j("p",ee,[w("img",{src:_(o)("screen","circle.png"),class:"title-icon0"},null,8,te),w("img",{src:_(o)("screen",e.picName),class:"title-icon1"},null,8,se)])):(x(),j("p",ie,[w("img",{src:_(o)("screen","circle.png"),class:"title-icon0"},null,8,le),z(g,{name:e.icon,class:"title-icon1"},null,8,["name"])])),w("p",ne,[w("span",ae,b(e.label),1),"故障状态"===e.label?(x(),j("span",re,[w("i",{class:N(["font-small-text-size1",!0===e.value?"normal":"abnormal"])},b(e.value),3)])):(x(),j("span",oe,[w("i",ce,b(e.value),1),w("i",de,b(e.unit),1)]))])])))),128))])]),w("div",{class:"left-wiringDiagram",ref_key:"leftWiringDiagramRef",ref:Ie},[w("div",pe,[w("p",ue,[w("img",{src:_(o)("screen","title_icon.png"),class:"title-icon"},null,8,me),w("span",null,b(null==(p=null==(d=_(xe))?void 0:d.pdfArr)?void 0:p.deviceName),1)])]),w("div",{ref_key:"leftCanvasRef",ref:Re,class:"canvasStyle"},null,512)],512),w("div",fe,[w("div",ve,[w("p",ge,[w("img",{src:_(o)("screen","title_icon.png"),class:"title-icon"},null,8,he),w("span",null,b(null==(m=null==(u=_(je))?void 0:u.pdfArr)?void 0:m.deviceName),1)])]),w("div",{ref_key:"rightCanvasRef",ref:Ne,class:"canvasStyle"},null,512)])],512)}}},[["__scopeId","data-v-3964c796"]]);export{ye as default};
