var t="delete",e=5,r=1<<e,n=r-1,i={};function o(t){t&&(t.value=!0)}function u(){}function s(t){return void 0===t.size&&(t.size=t.__iterate(c)),t.size}function a(t,e){if("number"!=typeof e){var r=e>>>0;if(""+r!==e||4294967295===r)return NaN;e=r}return e<0?s(t)+e:e}function c(){return!0}function f(t,e,r){return(0===t&&!l(t)||void 0!==r&&t<=-r)&&(void 0===e||void 0!==r&&e>=r)}function h(t,e){return _(t,e,0)}function p(t,e){return _(t,e,e)}function _(t,e,r){return void 0===t?r:l(t)?e===1/0?e:0|Math.max(0,e+t):void 0===e||e===t?t:0|Math.min(e,t)}function l(t){return t<0||0===t&&1/t==-1/0}var v="@@__IMMUTABLE_ITERABLE__@@";function y(t){return Boolean(t&&t[v])}var d="@@__IMMUTABLE_KEYED__@@";function g(t){return Boolean(t&&t[d])}var w="@@__IMMUTABLE_INDEXED__@@";function m(t){return Boolean(t&&t[w])}function S(t){return g(t)||m(t)}var z=function(t){return y(t)?t:Q(t)},b=function(t){function e(t){return g(t)?t:X(t)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e}(z),I=function(t){function e(t){return m(t)?t:F(t)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e}(z),O=function(t){function e(t){return y(t)&&!S(t)?t:G(t)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e}(z);z.Keyed=b,z.Indexed=I,z.Set=O;var E="@@__IMMUTABLE_SEQ__@@";function j(t){return Boolean(t&&t[E])}var q="@@__IMMUTABLE_RECORD__@@";function M(t){return Boolean(t&&t[q])}function D(t){return y(t)||M(t)}var A="@@__IMMUTABLE_ORDERED__@@";function x(t){return Boolean(t&&t[A])}var k=0,R=1,U=2,K="function"==typeof Symbol&&Symbol.iterator,T="@@iterator",B=K||T,L=function(t){this.next=t};function C(t,e,r,n){var i=0===t?e:1===t?r:[e,r];return n?n.value=i:n={value:i,done:!1},n}function P(){return{value:void 0,done:!0}}function W(t){return!!Array.isArray(t)||!!J(t)}function N(t){return t&&"function"==typeof t.next}function H(t){var e=J(t);return e&&e.call(t)}function J(t){var e=t&&(K&&t[K]||t[T]);if("function"==typeof e)return e}L.prototype.toString=function(){return"[Iterator]"},L.KEYS=k,L.VALUES=R,L.ENTRIES=U,L.prototype.inspect=L.prototype.toSource=function(){return this.toString()},L.prototype[B]=function(){return this};var V=Object.prototype.hasOwnProperty;function Y(t){return!(!Array.isArray(t)&&"string"!=typeof t)||t&&"object"==typeof t&&Number.isInteger(t.length)&&t.length>=0&&(0===t.length?1===Object.keys(t).length:t.hasOwnProperty(t.length-1))}var Q=function(t){function e(t){return null==t?rt():D(t)?t.toSeq():function(t){var e=ot(t);if(e)return(n=J(r=t))&&n===r.entries?e.fromEntrySeq():function(t){var e=J(t);return e&&e===t.keys}(t)?e.toSetSeq():e;var r,n;if("object"==typeof t)return new $(t);throw new TypeError("Expected Array or collection object of values, or keyed object: "+t)}(t)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.toSeq=function(){return this},e.prototype.toString=function(){return this.__toString("Seq {","}")},e.prototype.cacheResult=function(){return!this._cache&&this.__iterateUncached&&(this._cache=this.entrySeq().toArray(),this.size=this._cache.length),this},e.prototype.__iterate=function(t,e){var r=this._cache;if(r){for(var n=r.length,i=0;i!==n;){var o=r[e?n-++i:i++];if(!1===t(o[1],o[0],this))break}return i}return this.__iterateUncached(t,e)},e.prototype.__iterator=function(t,e){var r=this._cache;if(r){var n=r.length,i=0;return new L((function(){if(i===n)return{value:void 0,done:!0};var o=r[e?n-++i:i++];return C(t,o[0],o[1])}))}return this.__iteratorUncached(t,e)},e}(z),X=function(t){function e(t){return null==t?rt().toKeyedSeq():y(t)?g(t)?t.toSeq():t.fromEntrySeq():M(t)?t.toSeq():nt(t)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.toKeyedSeq=function(){return this},e}(Q),F=function(t){function e(t){return null==t?rt():y(t)?g(t)?t.entrySeq():t.toIndexedSeq():M(t)?t.toSeq().entrySeq():it(t)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.of=function(){return e(arguments)},e.prototype.toIndexedSeq=function(){return this},e.prototype.toString=function(){return this.__toString("Seq [","]")},e}(Q),G=function(t){function e(t){return(y(t)&&!S(t)?t:F(t)).toSetSeq()}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.of=function(){return e(arguments)},e.prototype.toSetSeq=function(){return this},e}(Q);Q.isSeq=j,Q.Keyed=X,Q.Set=G,Q.Indexed=F,Q.prototype[E]=!0;var Z=function(t){function e(t){this._array=t,this.size=t.length}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.get=function(t,e){return this.has(t)?this._array[a(this,t)]:e},e.prototype.__iterate=function(t,e){for(var r=this._array,n=r.length,i=0;i!==n;){var o=e?n-++i:i++;if(!1===t(r[o],o,this))break}return i},e.prototype.__iterator=function(t,e){var r=this._array,n=r.length,i=0;return new L((function(){if(i===n)return{value:void 0,done:!0};var o=e?n-++i:i++;return C(t,o,r[o])}))},e}(F),$=function(t){function e(t){var e=Object.keys(t).concat(Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(t):[]);this._object=t,this._keys=e,this.size=e.length}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.get=function(t,e){return void 0===e||this.has(t)?this._object[t]:e},e.prototype.has=function(t){return V.call(this._object,t)},e.prototype.__iterate=function(t,e){for(var r=this._object,n=this._keys,i=n.length,o=0;o!==i;){var u=n[e?i-++o:o++];if(!1===t(r[u],u,this))break}return o},e.prototype.__iterator=function(t,e){var r=this._object,n=this._keys,i=n.length,o=0;return new L((function(){if(o===i)return{value:void 0,done:!0};var u=n[e?i-++o:o++];return C(t,u,r[u])}))},e}(X);$.prototype[A]=!0;var tt,et=function(t){function e(t){this._collection=t,this.size=t.length||t.size}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.__iterateUncached=function(t,e){if(e)return this.cacheResult().__iterate(t,e);var r=H(this._collection),n=0;if(N(r))for(var i;!(i=r.next()).done&&!1!==t(i.value,n++,this););return n},e.prototype.__iteratorUncached=function(t,e){if(e)return this.cacheResult().__iterator(t,e);var r=H(this._collection);if(!N(r))return new L(P);var n=0;return new L((function(){var e=r.next();return e.done?e:C(t,n++,e.value)}))},e}(F);function rt(){return tt||(tt=new Z([]))}function nt(t){var e=ot(t);if(e)return e.fromEntrySeq();if("object"==typeof t)return new $(t);throw new TypeError("Expected Array or collection object of [k, v] entries, or keyed object: "+t)}function it(t){var e=ot(t);if(e)return e;throw new TypeError("Expected Array or collection object of values: "+t)}function ot(t){return Y(t)?new Z(t):W(t)?new et(t):void 0}var ut="@@__IMMUTABLE_MAP__@@";function st(t){return Boolean(t&&t[ut])}function at(t){return st(t)&&x(t)}function ct(t){return Boolean(t&&"function"==typeof t.equals&&"function"==typeof t.hashCode)}function ft(t,e){if(t===e||t!=t&&e!=e)return!0;if(!t||!e)return!1;if("function"==typeof t.valueOf&&"function"==typeof e.valueOf){if((t=t.valueOf())===(e=e.valueOf())||t!=t&&e!=e)return!0;if(!t||!e)return!1}return!!(ct(t)&&ct(e)&&t.equals(e))}var ht="function"==typeof Math.imul&&-2===Math.imul(4294967295,2)?Math.imul:function(t,e){var r=65535&(t|=0),n=65535&(e|=0);return r*n+((t>>>16)*n+r*(e>>>16)<<16>>>0)|0};function pt(t){return t>>>1&1073741824|3221225471&t}var _t=Object.prototype.valueOf;function lt(t){if(null==t)return vt(t);if("function"==typeof t.hashCode)return pt(t.hashCode(t));var e,r=(e=t).valueOf!==_t&&"function"==typeof e.valueOf?e.valueOf(e):e;if(null==r)return vt(r);switch(typeof r){case"boolean":return r?1108378657:1108378656;case"number":return function(t){if(t!=t||t===1/0)return 0;var e=0|t;e!==t&&(e^=4294967295*t);for(;t>4294967295;)e^=t/=4294967295;return pt(e)}(r);case"string":return r.length>Ot?function(t){var e=qt[t];void 0===e&&(e=yt(t),jt===Et&&(jt=0,qt={}),jt++,qt[t]=e);return e}(r):yt(r);case"object":case"function":return function(t){var e;if(St&&void 0!==(e=mt.get(t)))return e;if(e=t[It],void 0!==e)return e;if(!gt){if(void 0!==(e=t.propertyIsEnumerable&&t.propertyIsEnumerable[It]))return e;if(void 0!==(e=function(t){if(t&&t.nodeType>0)switch(t.nodeType){case 1:return t.uniqueID;case 9:return t.documentElement&&t.documentElement.uniqueID}}(t)))return e}if(e=wt(),St)mt.set(t,e);else{if(void 0!==dt&&!1===dt(t))throw new Error("Non-extensible objects are not allowed as keys.");if(gt)Object.defineProperty(t,It,{enumerable:!1,configurable:!1,writable:!1,value:e});else if(void 0!==t.propertyIsEnumerable&&t.propertyIsEnumerable===t.constructor.prototype.propertyIsEnumerable)t.propertyIsEnumerable=function(){return this.constructor.prototype.propertyIsEnumerable.apply(this,arguments)},t.propertyIsEnumerable[It]=e;else{if(void 0===t.nodeType)throw new Error("Unable to set a non-enumerable property on object.");t[It]=e}}return e}(r);case"symbol":return function(t){var e=zt[t];if(void 0!==e)return e;return e=wt(),zt[t]=e,e}(r);default:if("function"==typeof r.toString)return yt(r.toString());throw new Error("Value type "+typeof r+" cannot be hashed.")}}function vt(t){return null===t?1108378658:1108378659}function yt(t){for(var e=0,r=0;r<t.length;r++)e=31*e+t.charCodeAt(r)|0;return pt(e)}var dt=Object.isExtensible,gt=function(){try{return Object.defineProperty({},"@",{}),!0}catch(t){return!1}}();function wt(){var t=++bt;return 1073741824&bt&&(bt=0),t}var mt,St="function"==typeof WeakMap;St&&(mt=new WeakMap);var zt=Object.create(null),bt=0,It="__immutablehash__";"function"==typeof Symbol&&(It=Symbol(It));var Ot=16,Et=255,jt=0,qt={},Mt=function(t){function e(t,e){this._iter=t,this._useKeys=e,this.size=t.size}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.get=function(t,e){return this._iter.get(t,e)},e.prototype.has=function(t){return this._iter.has(t)},e.prototype.valueSeq=function(){return this._iter.valueSeq()},e.prototype.reverse=function(){var t=this,e=Ut(this,!0);return this._useKeys||(e.valueSeq=function(){return t._iter.toSeq().reverse()}),e},e.prototype.map=function(t,e){var r=this,n=Rt(this,t,e);return this._useKeys||(n.valueSeq=function(){return r._iter.toSeq().map(t,e)}),n},e.prototype.__iterate=function(t,e){var r=this;return this._iter.__iterate((function(e,n){return t(e,n,r)}),e)},e.prototype.__iterator=function(t,e){return this._iter.__iterator(t,e)},e}(X);Mt.prototype[A]=!0;var Dt=function(t){function e(t){this._iter=t,this.size=t.size}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.includes=function(t){return this._iter.includes(t)},e.prototype.__iterate=function(t,e){var r=this,n=0;return e&&s(this),this._iter.__iterate((function(i){return t(i,e?r.size-++n:n++,r)}),e)},e.prototype.__iterator=function(t,e){var r=this,n=this._iter.__iterator(R,e),i=0;return e&&s(this),new L((function(){var o=n.next();return o.done?o:C(t,e?r.size-++i:i++,o.value,o)}))},e}(F),At=function(t){function e(t){this._iter=t,this.size=t.size}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.has=function(t){return this._iter.includes(t)},e.prototype.__iterate=function(t,e){var r=this;return this._iter.__iterate((function(e){return t(e,e,r)}),e)},e.prototype.__iterator=function(t,e){var r=this._iter.__iterator(R,e);return new L((function(){var e=r.next();return e.done?e:C(t,e.value,e.value,e)}))},e}(G),xt=function(t){function e(t){this._iter=t,this.size=t.size}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.entrySeq=function(){return this._iter.toSeq()},e.prototype.__iterate=function(t,e){var r=this;return this._iter.__iterate((function(e){if(e){Jt(e);var n=y(e);return t(n?e.get(1):e[1],n?e.get(0):e[0],r)}}),e)},e.prototype.__iterator=function(t,e){var r=this._iter.__iterator(R,e);return new L((function(){for(;;){var e=r.next();if(e.done)return e;var n=e.value;if(n){Jt(n);var i=y(n);return C(t,i?n.get(0):n[0],i?n.get(1):n[1],e)}}}))},e}(X);function kt(t){var e=Yt(t);return e._iter=t,e.size=t.size,e.flip=function(){return t},e.reverse=function(){var e=t.reverse.apply(this);return e.flip=function(){return t.reverse()},e},e.has=function(e){return t.includes(e)},e.includes=function(e){return t.has(e)},e.cacheResult=Qt,e.__iterateUncached=function(e,r){var n=this;return t.__iterate((function(t,r){return!1!==e(r,t,n)}),r)},e.__iteratorUncached=function(e,r){if(e===U){var n=t.__iterator(e,r);return new L((function(){var t=n.next();if(!t.done){var e=t.value[0];t.value[0]=t.value[1],t.value[1]=e}return t}))}return t.__iterator(e===R?k:R,r)},e}function Rt(t,e,r){var n=Yt(t);return n.size=t.size,n.has=function(e){return t.has(e)},n.get=function(n,o){var u=t.get(n,i);return u===i?o:e.call(r,u,n,t)},n.__iterateUncached=function(n,i){var o=this;return t.__iterate((function(t,i,u){return!1!==n(e.call(r,t,i,u),i,o)}),i)},n.__iteratorUncached=function(n,i){var o=t.__iterator(U,i);return new L((function(){var i=o.next();if(i.done)return i;var u=i.value,s=u[0];return C(n,s,e.call(r,u[1],s,t),i)}))},n}function Ut(t,e){var r=this,n=Yt(t);return n._iter=t,n.size=t.size,n.reverse=function(){return t},t.flip&&(n.flip=function(){var e=kt(t);return e.reverse=function(){return t.flip()},e}),n.get=function(r,n){return t.get(e?r:-1-r,n)},n.has=function(r){return t.has(e?r:-1-r)},n.includes=function(e){return t.includes(e)},n.cacheResult=Qt,n.__iterate=function(r,n){var i=this,o=0;return n&&s(t),t.__iterate((function(t,u){return r(t,e?u:n?i.size-++o:o++,i)}),!n)},n.__iterator=function(n,i){var o=0;i&&s(t);var u=t.__iterator(U,!i);return new L((function(){var t=u.next();if(t.done)return t;var s=t.value;return C(n,e?s[0]:i?r.size-++o:o++,s[1],t)}))},n}function Kt(t,e,r,n){var o=Yt(t);return n&&(o.has=function(n){var o=t.get(n,i);return o!==i&&!!e.call(r,o,n,t)},o.get=function(n,o){var u=t.get(n,i);return u!==i&&e.call(r,u,n,t)?u:o}),o.__iterateUncached=function(i,o){var u=this,s=0;return t.__iterate((function(t,o,a){if(e.call(r,t,o,a))return s++,i(t,n?o:s-1,u)}),o),s},o.__iteratorUncached=function(i,o){var u=t.__iterator(U,o),s=0;return new L((function(){for(;;){var o=u.next();if(o.done)return o;var a=o.value,c=a[0],f=a[1];if(e.call(r,f,c,t))return C(i,n?c:s++,f,o)}}))},o}function Tt(t,e,r,n){var i=t.size;if(f(e,r,i))return t;var o=h(e,i),u=p(r,i);if(o!=o||u!=u)return Tt(t.toSeq().cacheResult(),e,r,n);var s,c=u-o;c==c&&(s=c<0?0:c);var _=Yt(t);return _.size=0===s?s:t.size&&s||void 0,!n&&j(t)&&s>=0&&(_.get=function(e,r){return(e=a(this,e))>=0&&e<s?t.get(e+o,r):r}),_.__iterateUncached=function(e,r){var i=this;if(0===s)return 0;if(r)return this.cacheResult().__iterate(e,r);var u=0,a=!0,c=0;return t.__iterate((function(t,r){if(!a||!(a=u++<o))return c++,!1!==e(t,n?r:c-1,i)&&c!==s})),c},_.__iteratorUncached=function(e,r){if(0!==s&&r)return this.cacheResult().__iterator(e,r);if(0===s)return new L(P);var i=t.__iterator(e,r),u=0,a=0;return new L((function(){for(;u++<o;)i.next();if(++a>s)return{value:void 0,done:!0};var t=i.next();return n||e===R||t.done?t:C(e,a-1,e===k?void 0:t.value[1],t)}))},_}function Bt(t,e,r,n){var i=Yt(t);return i.__iterateUncached=function(i,o){var u=this;if(o)return this.cacheResult().__iterate(i,o);var s=!0,a=0;return t.__iterate((function(t,o,c){if(!s||!(s=e.call(r,t,o,c)))return a++,i(t,n?o:a-1,u)})),a},i.__iteratorUncached=function(i,o){var u=this;if(o)return this.cacheResult().__iterator(i,o);var s=t.__iterator(U,o),a=!0,c=0;return new L((function(){var t,o,f;do{if((t=s.next()).done)return n||i===R?t:C(i,c++,i===k?void 0:t.value[1],t);var h=t.value;o=h[0],f=h[1],a&&(a=e.call(r,f,o,u))}while(a);return i===U?t:C(i,o,f,t)}))},i}function Lt(t,e,r){var n=Yt(t);return n.__iterateUncached=function(i,o){if(o)return this.cacheResult().__iterate(i,o);var u=0,s=!1;return function t(a,c){a.__iterate((function(o,a){return(!e||c<e)&&y(o)?t(o,c+1):(u++,!1===i(o,r?a:u-1,n)&&(s=!0)),!s}),o)}(t,0),u},n.__iteratorUncached=function(n,i){if(i)return this.cacheResult().__iterator(n,i);var o=t.__iterator(n,i),u=[],s=0;return new L((function(){for(;o;){var t=o.next();if(!1===t.done){var a=t.value;if(n===U&&(a=a[1]),e&&!(u.length<e)||!y(a))return r?t:C(n,s++,a,t);u.push(o),o=a.__iterator(n,i)}else o=u.pop()}return{value:void 0,done:!0}}))},n}function Ct(t,e,r){e||(e=Xt);var n=g(t),i=0,o=t.toSeq().map((function(e,n){return[n,e,i++,r?r(e,n,t):e]})).valueSeq().toArray();return o.sort((function(t,r){return e(t[3],r[3])||t[2]-r[2]})).forEach(n?function(t,e){o[e].length=2}:function(t,e){o[e]=t[1]}),n?X(o):m(t)?F(o):G(o)}function Pt(t,e,r){if(e||(e=Xt),r){var n=t.toSeq().map((function(e,n){return[e,r(e,n,t)]})).reduce((function(t,r){return Wt(e,t[1],r[1])?r:t}));return n&&n[0]}return t.reduce((function(t,r){return Wt(e,t,r)?r:t}))}function Wt(t,e,r){var n=t(r,e);return 0===n&&r!==e&&(null==r||r!=r)||n>0}function Nt(t,e,r,n){var i=Yt(t),o=new Z(r).map((function(t){return t.size}));return i.size=n?o.max():o.min(),i.__iterate=function(t,e){for(var r,n=this.__iterator(R,e),i=0;!(r=n.next()).done&&!1!==t(r.value,i++,this););return i},i.__iteratorUncached=function(t,i){var o=r.map((function(t){return t=z(t),H(i?t.reverse():t)})),u=0,s=!1;return new L((function(){var r;return s||(r=o.map((function(t){return t.next()})),s=n?r.every((function(t){return t.done})):r.some((function(t){return t.done}))),s?{value:void 0,done:!0}:C(t,u++,e.apply(null,r.map((function(t){return t.value}))))}))},i}function Ht(t,e){return t===e?t:j(t)?e:t.constructor(e)}function Jt(t){if(t!==Object(t))throw new TypeError("Expected [K, V] tuple: "+t)}function Vt(t){return g(t)?b:m(t)?I:O}function Yt(t){return Object.create((g(t)?X:m(t)?F:G).prototype)}function Qt(){return this._iter.cacheResult?(this._iter.cacheResult(),this.size=this._iter.size,this):Q.prototype.cacheResult.call(this)}function Xt(t,e){return void 0===t&&void 0===e?0:void 0===t?1:void 0===e?-1:t>e?1:t<e?-1:0}function Ft(t,e){e=e||0;for(var r=Math.max(0,t.length-e),n=new Array(r),i=0;i<r;i++)n[i]=t[i+e];return n}function Gt(t,e){if(!t)throw new Error(e)}function Zt(t){Gt(t!==1/0,"Cannot perform this action with an infinite size.")}function $t(t){if(Y(t)&&"string"!=typeof t)return t;if(x(t))return t.toArray();throw new TypeError("Invalid keyPath: expected Ordered Collection or Array: "+t)}Dt.prototype.cacheResult=Mt.prototype.cacheResult=At.prototype.cacheResult=xt.prototype.cacheResult=Qt;var te=Object.prototype.toString;function ee(t){if(!t||"object"!=typeof t||"[object Object]"!==te.call(t))return!1;var e=Object.getPrototypeOf(t);if(null===e)return!0;for(var r=e,n=Object.getPrototypeOf(e);null!==n;)r=n,n=Object.getPrototypeOf(r);return r===e}function re(t){return"object"==typeof t&&(D(t)||Array.isArray(t)||ee(t))}function ne(t){try{return"string"==typeof t?JSON.stringify(t):String(t)}catch(e){return JSON.stringify(t)}}function ie(t,e){return D(t)?t.has(e):re(t)&&V.call(t,e)}function oe(t,e,r){return D(t)?t.get(e,r):ie(t,e)?"function"==typeof t.get?t.get(e):t[e]:r}function ue(t){if(Array.isArray(t))return Ft(t);var e={};for(var r in t)V.call(t,r)&&(e[r]=t[r]);return e}function se(t,e){if(!re(t))throw new TypeError("Cannot update non-data-structure value: "+t);if(D(t)){if(!t.remove)throw new TypeError("Cannot update immutable value without .remove() method: "+t);return t.remove(e)}if(!V.call(t,e))return t;var r=ue(t);return Array.isArray(r)?r.splice(e,1):delete r[e],r}function ae(t,e,r){if(!re(t))throw new TypeError("Cannot update non-data-structure value: "+t);if(D(t)){if(!t.set)throw new TypeError("Cannot update immutable value without .set() method: "+t);return t.set(e,r)}if(V.call(t,e)&&r===t[e])return t;var n=ue(t);return n[e]=r,n}function ce(t,e,r,n){n||(n=r,r=void 0);var o=fe(D(t),t,$t(e),0,r,n);return o===i?r:o}function fe(t,e,r,n,o,u){var s=e===i;if(n===r.length){var a=s?o:e,c=u(a);return c===a?e:c}if(!s&&!re(e))throw new TypeError("Cannot update within non-data-structure value in path ["+r.slice(0,n).map(ne)+"]: "+e);var f=r[n],h=s?i:oe(e,f,i),p=fe(h===i?t:D(h),h,r,n+1,o,u);return p===h?e:p===i?se(e,f):ae(s?t?Ye():{}:e,f,p)}function he(t,e,r){return ce(t,e,i,(function(){return r}))}function pe(t,e){return he(this,t,e)}function _e(t,e){return ce(t,e,(function(){return i}))}function le(t){return _e(this,t)}function ve(t,e,r,n){return ce(t,[e],r,n)}function ye(t,e,r){return 1===arguments.length?t(this):ve(this,t,e,r)}function de(t,e,r){return ce(this,t,e,r)}function ge(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];return me(this,t)}function we(t){for(var e=[],r=arguments.length-1;r-- >0;)e[r]=arguments[r+1];if("function"!=typeof t)throw new TypeError("Invalid merger function: "+t);return me(this,e,t)}function me(t,e,r){for(var n=[],o=0;o<e.length;o++){var u=b(e[o]);0!==u.size&&n.push(u)}return 0===n.length?t:0!==t.toSeq().size||t.__ownerID||1!==n.length?t.withMutations((function(t){for(var e=r?function(e,n){ve(t,n,i,(function(t){return t===i?e:r(t,e,n)}))}:function(e,r){t.set(r,e)},o=0;o<n.length;o++)n[o].forEach(e)})):t.constructor(n[0])}function Se(t){for(var e=[],r=arguments.length-1;r-- >0;)e[r]=arguments[r+1];return Ee(t,e)}function ze(t,e){for(var r=[],n=arguments.length-2;n-- >0;)r[n]=arguments[n+2];return Ee(e,r,t)}function be(t){for(var e=[],r=arguments.length-1;r-- >0;)e[r]=arguments[r+1];return Oe(t,e)}function Ie(t,e){for(var r=[],n=arguments.length-2;n-- >0;)r[n]=arguments[n+2];return Oe(e,r,t)}function Oe(t,e,r){return Ee(t,e,function(t){function e(r,n,i){return re(r)&&re(n)&&(o=n,u=Q(r),s=Q(o),m(u)===m(s)&&g(u)===g(s))?Ee(r,[n],e):t?t(r,n,i):n;var o,u,s}return e}(r))}function Ee(t,e,r){if(!re(t))throw new TypeError("Cannot merge into non-data-structure value: "+t);if(D(t))return"function"==typeof r&&t.mergeWith?t.mergeWith.apply(t,[r].concat(e)):t.merge?t.merge.apply(t,e):t.concat.apply(t,e);for(var n=Array.isArray(t),i=t,o=n?I:b,u=n?function(e){i===t&&(i=ue(i)),i.push(e)}:function(e,n){var o=V.call(i,n),u=o&&r?r(i[n],e,n):e;o&&u===i[n]||(i===t&&(i=ue(i)),i[n]=u)},s=0;s<e.length;s++)o(e[s]).forEach(u);return i}function je(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];return Oe(this,t)}function qe(t){for(var e=[],r=arguments.length-1;r-- >0;)e[r]=arguments[r+1];return Oe(this,e,t)}function Me(t){for(var e=[],r=arguments.length-1;r-- >0;)e[r]=arguments[r+1];return ce(this,t,Ye(),(function(t){return Ee(t,e)}))}function De(t){for(var e=[],r=arguments.length-1;r-- >0;)e[r]=arguments[r+1];return ce(this,t,Ye(),(function(t){return Oe(t,e)}))}function Ae(t){var e=this.asMutable();return t(e),e.wasAltered()?e.__ensureOwner(this.__ownerID):this}function xe(){return this.__ownerID?this:this.__ensureOwner(new u)}function ke(){return this.__ensureOwner()}function Re(){return this.__altered}var Ue=function(t){function e(e){return null==e?Ye():st(e)&&!x(e)?e:Ye().withMutations((function(r){var n=t(e);Zt(n.size),n.forEach((function(t,e){return r.set(e,t)}))}))}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.of=function(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];return Ye().withMutations((function(e){for(var r=0;r<t.length;r+=2){if(r+1>=t.length)throw new Error("Missing value for key: "+t[r]);e.set(t[r],t[r+1])}}))},e.prototype.toString=function(){return this.__toString("Map {","}")},e.prototype.get=function(t,e){return this._root?this._root.get(0,void 0,t,e):e},e.prototype.set=function(t,e){return Qe(this,t,e)},e.prototype.remove=function(t){return Qe(this,t,i)},e.prototype.deleteAll=function(t){var e=z(t);return 0===e.size?this:this.withMutations((function(t){e.forEach((function(e){return t.remove(e)}))}))},e.prototype.clear=function(){return 0===this.size?this:this.__ownerID?(this.size=0,this._root=null,this.__hash=void 0,this.__altered=!0,this):Ye()},e.prototype.sort=function(t){return wr(Ct(this,t))},e.prototype.sortBy=function(t,e){return wr(Ct(this,e,t))},e.prototype.map=function(t,e){var r=this;return this.withMutations((function(n){n.forEach((function(i,o){n.set(o,t.call(e,i,o,r))}))}))},e.prototype.__iterator=function(t,e){return new Ne(this,t,e)},e.prototype.__iterate=function(t,e){var r=this,n=0;return this._root&&this._root.iterate((function(e){return n++,t(e[1],e[0],r)}),e),n},e.prototype.__ensureOwner=function(t){return t===this.__ownerID?this:t?Ve(this.size,this._root,t,this.__hash):0===this.size?Ye():(this.__ownerID=t,this.__altered=!1,this)},e}(b);Ue.isMap=st;var Ke=Ue.prototype;Ke[ut]=!0,Ke[t]=Ke.remove,Ke.removeAll=Ke.deleteAll,Ke.setIn=pe,Ke.removeIn=Ke.deleteIn=le,Ke.update=ye,Ke.updateIn=de,Ke.merge=Ke.concat=ge,Ke.mergeWith=we,Ke.mergeDeep=je,Ke.mergeDeepWith=qe,Ke.mergeIn=Me,Ke.mergeDeepIn=De,Ke.withMutations=Ae,Ke.wasAltered=Re,Ke.asImmutable=ke,Ke["@@transducer/init"]=Ke.asMutable=xe,Ke["@@transducer/step"]=function(t,e){return t.set(e[0],e[1])},Ke["@@transducer/result"]=function(t){return t.asImmutable()};var Te=function(t,e){this.ownerID=t,this.entries=e};Te.prototype.get=function(t,e,r,n){for(var i=this.entries,o=0,u=i.length;o<u;o++)if(ft(r,i[o][0]))return i[o][1];return n},Te.prototype.update=function(t,e,r,n,s,a,c){for(var f=s===i,h=this.entries,p=0,_=h.length;p<_&&!ft(n,h[p][0]);p++);var l=p<_;if(l?h[p][1]===s:f)return this;if(o(c),(f||!l)&&o(a),!f||1!==h.length){if(!l&&!f&&h.length>=tr)return function(t,e,r,n){t||(t=new u);for(var i=new Pe(t,lt(r),[r,n]),o=0;o<e.length;o++){var s=e[o];i=i.update(t,0,void 0,s[0],s[1])}return i}(t,h,n,s);var v=t&&t===this.ownerID,y=v?h:Ft(h);return l?f?p===_-1?y.pop():y[p]=y.pop():y[p]=[n,s]:y.push([n,s]),v?(this.entries=y,this):new Te(t,y)}};var Be=function(t,e,r){this.ownerID=t,this.bitmap=e,this.nodes=r};Be.prototype.get=function(t,r,i,o){void 0===r&&(r=lt(i));var u=1<<((0===t?r:r>>>t)&n),s=this.bitmap;return s&u?this.nodes[Ze(s&u-1)].get(t+e,r,i,o):o},Be.prototype.update=function(t,o,u,s,a,c,f){void 0===u&&(u=lt(s));var h=(0===o?u:u>>>o)&n,p=1<<h,_=this.bitmap,l=!!(_&p);if(!l&&a===i)return this;var v=Ze(_&p-1),y=this.nodes,d=l?y[v]:void 0,g=Xe(d,t,o+e,u,s,a,c,f);if(g===d)return this;if(!l&&g&&y.length>=er)return function(t,e,n,i,o){for(var u=0,s=new Array(r),a=0;0!==n;a++,n>>>=1)s[a]=1&n?e[u++]:void 0;return s[i]=o,new Le(t,u+1,s)}(t,y,_,h,g);if(l&&!g&&2===y.length&&Fe(y[1^v]))return y[1^v];if(l&&g&&1===y.length&&Fe(g))return g;var w=t&&t===this.ownerID,m=l?g?_:_^p:_|p,S=l?g?$e(y,v,g,w):function(t,e,r){var n=t.length-1;if(r&&e===n)return t.pop(),t;for(var i=new Array(n),o=0,u=0;u<n;u++)u===e&&(o=1),i[u]=t[u+o];return i}(y,v,w):function(t,e,r,n){var i=t.length+1;if(n&&e+1===i)return t[e]=r,t;for(var o=new Array(i),u=0,s=0;s<i;s++)s===e?(o[s]=r,u=-1):o[s]=t[s+u];return o}(y,v,g,w);return w?(this.bitmap=m,this.nodes=S,this):new Be(t,m,S)};var Le=function(t,e,r){this.ownerID=t,this.count=e,this.nodes=r};Le.prototype.get=function(t,r,i,o){void 0===r&&(r=lt(i));var u=(0===t?r:r>>>t)&n,s=this.nodes[u];return s?s.get(t+e,r,i,o):o},Le.prototype.update=function(t,r,o,u,s,a,c){void 0===o&&(o=lt(u));var f=(0===r?o:o>>>r)&n,h=s===i,p=this.nodes,_=p[f];if(h&&!_)return this;var l=Xe(_,t,r+e,o,u,s,a,c);if(l===_)return this;var v=this.count;if(_){if(!l&&--v<rr)return function(t,e,r,n){for(var i=0,o=0,u=new Array(r),s=0,a=1,c=e.length;s<c;s++,a<<=1){var f=e[s];void 0!==f&&s!==n&&(i|=a,u[o++]=f)}return new Be(t,i,u)}(t,p,v,f)}else v++;var y=t&&t===this.ownerID,d=$e(p,f,l,y);return y?(this.count=v,this.nodes=d,this):new Le(t,v,d)};var Ce=function(t,e,r){this.ownerID=t,this.keyHash=e,this.entries=r};Ce.prototype.get=function(t,e,r,n){for(var i=this.entries,o=0,u=i.length;o<u;o++)if(ft(r,i[o][0]))return i[o][1];return n},Ce.prototype.update=function(t,e,r,n,u,s,a){void 0===r&&(r=lt(n));var c=u===i;if(r!==this.keyHash)return c?this:(o(a),o(s),Ge(this,t,e,r,[n,u]));for(var f=this.entries,h=0,p=f.length;h<p&&!ft(n,f[h][0]);h++);var _=h<p;if(_?f[h][1]===u:c)return this;if(o(a),(c||!_)&&o(s),c&&2===p)return new Pe(t,this.keyHash,f[1^h]);var l=t&&t===this.ownerID,v=l?f:Ft(f);return _?c?h===p-1?v.pop():v[h]=v.pop():v[h]=[n,u]:v.push([n,u]),l?(this.entries=v,this):new Ce(t,this.keyHash,v)};var Pe=function(t,e,r){this.ownerID=t,this.keyHash=e,this.entry=r};Pe.prototype.get=function(t,e,r,n){return ft(r,this.entry[0])?this.entry[1]:n},Pe.prototype.update=function(t,e,r,n,u,s,a){var c=u===i,f=ft(n,this.entry[0]);return(f?u===this.entry[1]:c)?this:(o(a),c?void o(s):f?t&&t===this.ownerID?(this.entry[1]=u,this):new Pe(t,this.keyHash,[n,u]):(o(s),Ge(this,t,e,lt(n),[n,u])))},Te.prototype.iterate=Ce.prototype.iterate=function(t,e){for(var r=this.entries,n=0,i=r.length-1;n<=i;n++)if(!1===t(r[e?i-n:n]))return!1},Be.prototype.iterate=Le.prototype.iterate=function(t,e){for(var r=this.nodes,n=0,i=r.length-1;n<=i;n++){var o=r[e?i-n:n];if(o&&!1===o.iterate(t,e))return!1}},Pe.prototype.iterate=function(t,e){return t(this.entry)};var We,Ne=function(t){function e(t,e,r){this._type=e,this._reverse=r,this._stack=t._root&&Je(t._root)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.next=function(){for(var t=this._type,e=this._stack;e;){var r=e.node,n=e.index++,i=void 0;if(r.entry){if(0===n)return He(t,r.entry)}else if(r.entries){if(n<=(i=r.entries.length-1))return He(t,r.entries[this._reverse?i-n:n])}else if(n<=(i=r.nodes.length-1)){var o=r.nodes[this._reverse?i-n:n];if(o){if(o.entry)return He(t,o.entry);e=this._stack=Je(o,e)}continue}e=this._stack=this._stack.__prev}return{value:void 0,done:!0}},e}(L);function He(t,e){return C(t,e[0],e[1])}function Je(t,e){return{node:t,index:0,__prev:e}}function Ve(t,e,r,n){var i=Object.create(Ke);return i.size=t,i._root=e,i.__ownerID=r,i.__hash=n,i.__altered=!1,i}function Ye(){return We||(We=Ve(0))}function Qe(t,e,r){var n,o;if(t._root){var u={value:!1},s={value:!1};if(n=Xe(t._root,t.__ownerID,0,void 0,e,r,u,s),!s.value)return t;o=t.size+(u.value?r===i?-1:1:0)}else{if(r===i)return t;o=1,n=new Te(t.__ownerID,[[e,r]])}return t.__ownerID?(t.size=o,t._root=n,t.__hash=void 0,t.__altered=!0,t):n?Ve(o,n):Ye()}function Xe(t,e,r,n,u,s,a,c){return t?t.update(e,r,n,u,s,a,c):s===i?t:(o(c),o(a),new Pe(e,n,[u,s]))}function Fe(t){return t.constructor===Pe||t.constructor===Ce}function Ge(t,r,i,o,u){if(t.keyHash===o)return new Ce(r,o,[t.entry,u]);var s,a=(0===i?t.keyHash:t.keyHash>>>i)&n,c=(0===i?o:o>>>i)&n,f=a===c?[Ge(t,r,i+e,o,u)]:(s=new Pe(r,o,u),a<c?[t,s]:[s,t]);return new Be(r,1<<a|1<<c,f)}function Ze(t){return t=(t=(858993459&(t-=t>>1&1431655765))+(t>>2&858993459))+(t>>4)&252645135,t+=t>>8,127&(t+=t>>16)}function $e(t,e,r,n){var i=n?t:Ft(t);return i[e]=r,i}var tr=r/4,er=r/2,rr=r/4,nr="@@__IMMUTABLE_LIST__@@";function ir(t){return Boolean(t&&t[nr])}var or=function(t){function i(n){var i=pr();if(null==n)return i;if(ir(n))return n;var o=t(n),u=o.size;return 0===u?i:(Zt(u),u>0&&u<r?hr(0,u,e,null,new sr(o.toArray())):i.withMutations((function(t){t.setSize(u),o.forEach((function(e,r){return t.set(r,e)}))})))}return t&&(i.__proto__=t),i.prototype=Object.create(t&&t.prototype),i.prototype.constructor=i,i.of=function(){return this(arguments)},i.prototype.toString=function(){return this.__toString("List [","]")},i.prototype.get=function(t,e){if((t=a(this,t))>=0&&t<this.size){var r=vr(this,t+=this._origin);return r&&r.array[t&n]}return e},i.prototype.set=function(t,e){return function(t,e,r){if(e=a(t,e),e!=e)return t;if(e>=t.size||e<0)return t.withMutations((function(t){e<0?yr(t,e).set(0,r):yr(t,0,e+1).set(e,r)}));e+=t._origin;var n=t._tail,i=t._root,o={value:!1};e>=dr(t._capacity)?n=_r(n,t.__ownerID,0,e,r,o):i=_r(i,t.__ownerID,t._level,e,r,o);if(!o.value)return t;if(t.__ownerID)return t._root=i,t._tail=n,t.__hash=void 0,t.__altered=!0,t;return hr(t._origin,t._capacity,t._level,i,n)}(this,t,e)},i.prototype.remove=function(t){return this.has(t)?0===t?this.shift():t===this.size-1?this.pop():this.splice(t,1):this},i.prototype.insert=function(t,e){return this.splice(t,0,e)},i.prototype.clear=function(){return 0===this.size?this:this.__ownerID?(this.size=this._origin=this._capacity=0,this._level=e,this._root=this._tail=this.__hash=void 0,this.__altered=!0,this):pr()},i.prototype.push=function(){var t=arguments,e=this.size;return this.withMutations((function(r){yr(r,0,e+t.length);for(var n=0;n<t.length;n++)r.set(e+n,t[n])}))},i.prototype.pop=function(){return yr(this,0,-1)},i.prototype.unshift=function(){var t=arguments;return this.withMutations((function(e){yr(e,-t.length);for(var r=0;r<t.length;r++)e.set(r,t[r])}))},i.prototype.shift=function(){return yr(this,1)},i.prototype.concat=function(){for(var e=arguments,r=[],n=0;n<arguments.length;n++){var i=e[n],o=t("string"!=typeof i&&W(i)?i:[i]);0!==o.size&&r.push(o)}return 0===r.length?this:0!==this.size||this.__ownerID||1!==r.length?this.withMutations((function(t){r.forEach((function(e){return e.forEach((function(e){return t.push(e)}))}))})):this.constructor(r[0])},i.prototype.setSize=function(t){return yr(this,0,t)},i.prototype.map=function(t,e){var r=this;return this.withMutations((function(n){for(var i=0;i<r.size;i++)n.set(i,t.call(e,n.get(i),i,r))}))},i.prototype.slice=function(t,e){var r=this.size;return f(t,e,r)?this:yr(this,h(t,r),p(e,r))},i.prototype.__iterator=function(t,e){var r=e?this.size:0,n=fr(this,e);return new L((function(){var i=n();return i===cr?{value:void 0,done:!0}:C(t,e?--r:r++,i)}))},i.prototype.__iterate=function(t,e){for(var r,n=e?this.size:0,i=fr(this,e);(r=i())!==cr&&!1!==t(r,e?--n:n++,this););return n},i.prototype.__ensureOwner=function(t){return t===this.__ownerID?this:t?hr(this._origin,this._capacity,this._level,this._root,this._tail,t,this.__hash):0===this.size?pr():(this.__ownerID=t,this.__altered=!1,this)},i}(I);or.isList=ir;var ur=or.prototype;ur[nr]=!0,ur[t]=ur.remove,ur.merge=ur.concat,ur.setIn=pe,ur.deleteIn=ur.removeIn=le,ur.update=ye,ur.updateIn=de,ur.mergeIn=Me,ur.mergeDeepIn=De,ur.withMutations=Ae,ur.wasAltered=Re,ur.asImmutable=ke,ur["@@transducer/init"]=ur.asMutable=xe,ur["@@transducer/step"]=function(t,e){return t.push(e)},ur["@@transducer/result"]=function(t){return t.asImmutable()};var sr=function(t,e){this.array=t,this.ownerID=e};sr.prototype.removeBefore=function(t,r,i){if(i===r?1<<r:0===this.array.length)return this;var o=i>>>r&n;if(o>=this.array.length)return new sr([],t);var u,s=0===o;if(r>0){var a=this.array[o];if((u=a&&a.removeBefore(t,r-e,i))===a&&s)return this}if(s&&!u)return this;var c=lr(this,t);if(!s)for(var f=0;f<o;f++)c.array[f]=void 0;return u&&(c.array[o]=u),c},sr.prototype.removeAfter=function(t,r,i){if(i===(r?1<<r:0)||0===this.array.length)return this;var o,u=i-1>>>r&n;if(u>=this.array.length)return this;if(r>0){var s=this.array[u];if((o=s&&s.removeAfter(t,r-e,i))===s&&u===this.array.length-1)return this}var a=lr(this,t);return a.array.splice(u+1),o&&(a.array[u]=o),a};var ar,cr={};function fr(t,n){var i=t._origin,o=t._capacity,u=dr(o),s=t._tail;return a(t._root,t._level,0);function a(t,c,f){return 0===c?function(t,e){var a=e===u?s&&s.array:t&&t.array,c=e>i?0:i-e,f=o-e;f>r&&(f=r);return function(){if(c===f)return cr;var t=n?--f:c++;return a&&a[t]}}(t,f):function(t,u,s){var c,f=t&&t.array,h=s>i?0:i-s>>u,p=1+(o-s>>u);p>r&&(p=r);return function(){for(;;){if(c){var t=c();if(t!==cr)return t;c=null}if(h===p)return cr;var r=n?--p:h++;c=a(f&&f[r],u-e,s+(r<<u))}}}(t,c,f)}}function hr(t,e,r,n,i,o,u){var s=Object.create(ur);return s.size=e-t,s._origin=t,s._capacity=e,s._level=r,s._root=n,s._tail=i,s.__ownerID=o,s.__hash=u,s.__altered=!1,s}function pr(){return ar||(ar=hr(0,0,e))}function _r(t,r,i,u,s,a){var c,f=u>>>i&n,h=t&&f<t.array.length;if(!h&&void 0===s)return t;if(i>0){var p=t&&t.array[f],_=_r(p,r,i-e,u,s,a);return _===p?t:((c=lr(t,r)).array[f]=_,c)}return h&&t.array[f]===s?t:(a&&o(a),c=lr(t,r),void 0===s&&f===c.array.length-1?c.array.pop():c.array[f]=s,c)}function lr(t,e){return e&&t&&e===t.ownerID?t:new sr(t?t.array.slice():[],e)}function vr(t,r){if(r>=dr(t._capacity))return t._tail;if(r<1<<t._level+e){for(var i=t._root,o=t._level;i&&o>0;)i=i.array[r>>>o&n],o-=e;return i}}function yr(t,r,i){void 0!==r&&(r|=0),void 0!==i&&(i|=0);var o=t.__ownerID||new u,s=t._origin,a=t._capacity,c=s+r,f=void 0===i?a:i<0?a+i:s+i;if(c===s&&f===a)return t;if(c>=f)return t.clear();for(var h=t._level,p=t._root,_=0;c+_<0;)p=new sr(p&&p.array.length?[void 0,p]:[],o),_+=1<<(h+=e);_&&(c+=_,s+=_,f+=_,a+=_);for(var l=dr(a),v=dr(f);v>=1<<h+e;)p=new sr(p&&p.array.length?[p]:[],o),h+=e;var y=t._tail,d=v<l?vr(t,f-1):v>l?new sr([],o):y;if(y&&v>l&&c<a&&y.array.length){for(var g=p=lr(p,o),w=h;w>e;w-=e){var m=l>>>w&n;g=g.array[m]=lr(g.array[m],o)}g.array[l>>>e&n]=y}if(f<a&&(d=d&&d.removeAfter(o,0,f)),c>=v)c-=v,f-=v,h=e,p=null,d=d&&d.removeBefore(o,0,c);else if(c>s||v<l){for(_=0;p;){var S=c>>>h&n;if(S!==v>>>h&n)break;S&&(_+=(1<<h)*S),h-=e,p=p.array[S]}p&&c>s&&(p=p.removeBefore(o,h,c-_)),p&&v<l&&(p=p.removeAfter(o,h,v-_)),_&&(c-=_,f-=_)}return t.__ownerID?(t.size=f-c,t._origin=c,t._capacity=f,t._level=h,t._root=p,t._tail=d,t.__hash=void 0,t.__altered=!0,t):hr(c,f,h,p,d)}function dr(t){return t<r?0:t-1>>>e<<e}var gr,wr=function(t){function e(t){return null==t?Sr():at(t)?t:Sr().withMutations((function(e){var r=b(t);Zt(r.size),r.forEach((function(t,r){return e.set(r,t)}))}))}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.of=function(){return this(arguments)},e.prototype.toString=function(){return this.__toString("OrderedMap {","}")},e.prototype.get=function(t,e){var r=this._map.get(t);return void 0!==r?this._list.get(r)[1]:e},e.prototype.clear=function(){return 0===this.size?this:this.__ownerID?(this.size=0,this._map.clear(),this._list.clear(),this.__altered=!0,this):Sr()},e.prototype.set=function(t,e){return zr(this,t,e)},e.prototype.remove=function(t){return zr(this,t,i)},e.prototype.__iterate=function(t,e){var r=this;return this._list.__iterate((function(e){return e&&t(e[1],e[0],r)}),e)},e.prototype.__iterator=function(t,e){return this._list.fromEntrySeq().__iterator(t,e)},e.prototype.__ensureOwner=function(t){if(t===this.__ownerID)return this;var e=this._map.__ensureOwner(t),r=this._list.__ensureOwner(t);return t?mr(e,r,t,this.__hash):0===this.size?Sr():(this.__ownerID=t,this.__altered=!1,this._map=e,this._list=r,this)},e}(Ue);function mr(t,e,r,n){var i=Object.create(wr.prototype);return i.size=t?t.size:0,i._map=t,i._list=e,i.__ownerID=r,i.__hash=n,i.__altered=!1,i}function Sr(){return gr||(gr=mr(Ye(),pr()))}function zr(t,e,n){var o,u,s=t._map,a=t._list,c=s.get(e),f=void 0!==c;if(n===i){if(!f)return t;a.size>=r&&a.size>=2*s.size?(o=(u=a.filter((function(t,e){return void 0!==t&&c!==e}))).toKeyedSeq().map((function(t){return t[0]})).flip().toMap(),t.__ownerID&&(o.__ownerID=u.__ownerID=t.__ownerID)):(o=s.remove(e),u=c===a.size-1?a.pop():a.set(c,void 0))}else if(f){if(n===a.get(c)[1])return t;o=s,u=a.set(c,[e,n])}else o=s.set(e,a.size),u=a.set(a.size,[e,n]);return t.__ownerID?(t.size=o.size,t._map=o,t._list=u,t.__hash=void 0,t.__altered=!0,t):mr(o,u)}wr.isOrderedMap=at,wr.prototype[A]=!0,wr.prototype[t]=wr.prototype.remove;var br="@@__IMMUTABLE_STACK__@@";function Ir(t){return Boolean(t&&t[br])}var Or=function(t){function e(t){return null==t?Mr():Ir(t)?t:Mr().pushAll(t)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.of=function(){return this(arguments)},e.prototype.toString=function(){return this.__toString("Stack [","]")},e.prototype.get=function(t,e){var r=this._head;for(t=a(this,t);r&&t--;)r=r.next;return r?r.value:e},e.prototype.peek=function(){return this._head&&this._head.value},e.prototype.push=function(){var t=arguments;if(0===arguments.length)return this;for(var e=this.size+arguments.length,r=this._head,n=arguments.length-1;n>=0;n--)r={value:t[n],next:r};return this.__ownerID?(this.size=e,this._head=r,this.__hash=void 0,this.__altered=!0,this):qr(e,r)},e.prototype.pushAll=function(e){if(0===(e=t(e)).size)return this;if(0===this.size&&Ir(e))return e;Zt(e.size);var r=this.size,n=this._head;return e.__iterate((function(t){r++,n={value:t,next:n}}),!0),this.__ownerID?(this.size=r,this._head=n,this.__hash=void 0,this.__altered=!0,this):qr(r,n)},e.prototype.pop=function(){return this.slice(1)},e.prototype.clear=function(){return 0===this.size?this:this.__ownerID?(this.size=0,this._head=void 0,this.__hash=void 0,this.__altered=!0,this):Mr()},e.prototype.slice=function(e,r){if(f(e,r,this.size))return this;var n=h(e,this.size);if(p(r,this.size)!==this.size)return t.prototype.slice.call(this,e,r);for(var i=this.size-n,o=this._head;n--;)o=o.next;return this.__ownerID?(this.size=i,this._head=o,this.__hash=void 0,this.__altered=!0,this):qr(i,o)},e.prototype.__ensureOwner=function(t){return t===this.__ownerID?this:t?qr(this.size,this._head,t,this.__hash):0===this.size?Mr():(this.__ownerID=t,this.__altered=!1,this)},e.prototype.__iterate=function(t,e){var r=this;if(e)return new Z(this.toArray()).__iterate((function(e,n){return t(e,n,r)}),e);for(var n=0,i=this._head;i&&!1!==t(i.value,n++,this);)i=i.next;return n},e.prototype.__iterator=function(t,e){if(e)return new Z(this.toArray()).__iterator(t,e);var r=0,n=this._head;return new L((function(){if(n){var e=n.value;return n=n.next,C(t,r++,e)}return{value:void 0,done:!0}}))},e}(I);Or.isStack=Ir;var Er,jr=Or.prototype;function qr(t,e,r,n){var i=Object.create(jr);return i.size=t,i._head=e,i.__ownerID=r,i.__hash=n,i.__altered=!1,i}function Mr(){return Er||(Er=qr(0))}jr[br]=!0,jr.shift=jr.pop,jr.unshift=jr.push,jr.unshiftAll=jr.pushAll,jr.withMutations=Ae,jr.wasAltered=Re,jr.asImmutable=ke,jr["@@transducer/init"]=jr.asMutable=xe,jr["@@transducer/step"]=function(t,e){return t.unshift(e)},jr["@@transducer/result"]=function(t){return t.asImmutable()};var Dr="@@__IMMUTABLE_SET__@@";function Ar(t){return Boolean(t&&t[Dr])}function xr(t){return Ar(t)&&x(t)}function kr(t,e){if(t===e)return!0;if(!y(e)||void 0!==t.size&&void 0!==e.size&&t.size!==e.size||void 0!==t.__hash&&void 0!==e.__hash&&t.__hash!==e.__hash||g(t)!==g(e)||m(t)!==m(e)||x(t)!==x(e))return!1;if(0===t.size&&0===e.size)return!0;var r=!S(t);if(x(t)){var n=t.entries();return e.every((function(t,e){var i=n.next().value;return i&&ft(i[1],t)&&(r||ft(i[0],e))}))&&n.next().done}var o=!1;if(void 0===t.size)if(void 0===e.size)"function"==typeof t.cacheResult&&t.cacheResult();else{o=!0;var u=t;t=e,e=u}var s=!0,a=e.__iterate((function(e,n){if(r?!t.has(e):o?!ft(e,t.get(n,i)):!ft(t.get(n,i),e))return s=!1,!1}));return s&&t.size===a}function Rr(t,e){var r=function(r){t.prototype[r]=e[r]};return Object.keys(e).forEach(r),Object.getOwnPropertySymbols&&Object.getOwnPropertySymbols(e).forEach(r),t}function Ur(t){if(!t||"object"!=typeof t)return t;if(!y(t)){if(!re(t))return t;t=Q(t)}if(g(t)){var e={};return t.__iterate((function(t,r){e[r]=Ur(t)})),e}var r=[];return t.__iterate((function(t){r.push(Ur(t))})),r}var Kr=function(t){function e(e){return null==e?Pr():Ar(e)&&!x(e)?e:Pr().withMutations((function(r){var n=t(e);Zt(n.size),n.forEach((function(t){return r.add(t)}))}))}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.of=function(){return this(arguments)},e.fromKeys=function(t){return this(b(t).keySeq())},e.intersect=function(t){return(t=z(t).toArray()).length?Br.intersect.apply(e(t.pop()),t):Pr()},e.union=function(t){return(t=z(t).toArray()).length?Br.union.apply(e(t.pop()),t):Pr()},e.prototype.toString=function(){return this.__toString("Set {","}")},e.prototype.has=function(t){return this._map.has(t)},e.prototype.add=function(t){return Lr(this,this._map.set(t,t))},e.prototype.remove=function(t){return Lr(this,this._map.remove(t))},e.prototype.clear=function(){return Lr(this,this._map.clear())},e.prototype.map=function(t,e){var r=this,n=!1,i=Lr(this,this._map.mapEntries((function(i){var o=i[1],u=t.call(e,o,o,r);return u!==o&&(n=!0),[u,u]}),e));return n?i:this},e.prototype.union=function(){for(var e=[],r=arguments.length;r--;)e[r]=arguments[r];return 0===(e=e.filter((function(t){return 0!==t.size}))).length?this:0!==this.size||this.__ownerID||1!==e.length?this.withMutations((function(r){for(var n=0;n<e.length;n++)"string"==typeof e[n]?r.add(e[n]):t(e[n]).forEach((function(t){return r.add(t)}))})):this.constructor(e[0])},e.prototype.intersect=function(){for(var e=[],r=arguments.length;r--;)e[r]=arguments[r];if(0===e.length)return this;e=e.map((function(e){return t(e)}));var n=[];return this.forEach((function(t){e.every((function(e){return e.includes(t)}))||n.push(t)})),this.withMutations((function(t){n.forEach((function(e){t.remove(e)}))}))},e.prototype.subtract=function(){for(var e=[],r=arguments.length;r--;)e[r]=arguments[r];if(0===e.length)return this;e=e.map((function(e){return t(e)}));var n=[];return this.forEach((function(t){e.some((function(e){return e.includes(t)}))&&n.push(t)})),this.withMutations((function(t){n.forEach((function(e){t.remove(e)}))}))},e.prototype.sort=function(t){return sn(Ct(this,t))},e.prototype.sortBy=function(t,e){return sn(Ct(this,e,t))},e.prototype.wasAltered=function(){return this._map.wasAltered()},e.prototype.__iterate=function(t,e){var r=this;return this._map.__iterate((function(e){return t(e,e,r)}),e)},e.prototype.__iterator=function(t,e){return this._map.__iterator(t,e)},e.prototype.__ensureOwner=function(t){if(t===this.__ownerID)return this;var e=this._map.__ensureOwner(t);return t?this.__make(e,t):0===this.size?this.__empty():(this.__ownerID=t,this._map=e,this)},e}(O);Kr.isSet=Ar;var Tr,Br=Kr.prototype;function Lr(t,e){return t.__ownerID?(t.size=e.size,t._map=e,t):e===t._map?t:0===e.size?t.__empty():t.__make(e)}function Cr(t,e){var r=Object.create(Br);return r.size=t?t.size:0,r._map=t,r.__ownerID=e,r}function Pr(){return Tr||(Tr=Cr(Ye()))}Br[Dr]=!0,Br[t]=Br.remove,Br.merge=Br.concat=Br.union,Br.withMutations=Ae,Br.asImmutable=ke,Br["@@transducer/init"]=Br.asMutable=xe,Br["@@transducer/step"]=function(t,e){return t.add(e)},Br["@@transducer/result"]=function(t){return t.asImmutable()},Br.__empty=Pr,Br.__make=Cr;var Wr,Nr=function(t){function e(t,r,n){if(!(this instanceof e))return new e(t,r,n);if(Gt(0!==n,"Cannot step a Range by 0"),t=t||0,void 0===r&&(r=1/0),n=void 0===n?1:Math.abs(n),r<t&&(n=-n),this._start=t,this._end=r,this._step=n,this.size=Math.max(0,Math.ceil((r-t)/n-1)+1),0===this.size){if(Wr)return Wr;Wr=this}}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.toString=function(){return 0===this.size?"Range []":"Range [ "+this._start+"..."+this._end+(1!==this._step?" by "+this._step:"")+" ]"},e.prototype.get=function(t,e){return this.has(t)?this._start+a(this,t)*this._step:e},e.prototype.includes=function(t){var e=(t-this._start)/this._step;return e>=0&&e<this.size&&e===Math.floor(e)},e.prototype.slice=function(t,r){return f(t,r,this.size)?this:(t=h(t,this.size),(r=p(r,this.size))<=t?new e(0,0):new e(this.get(t,this._end),this.get(r,this._end),this._step))},e.prototype.indexOf=function(t){var e=t-this._start;if(e%this._step==0){var r=e/this._step;if(r>=0&&r<this.size)return r}return-1},e.prototype.lastIndexOf=function(t){return this.indexOf(t)},e.prototype.__iterate=function(t,e){for(var r=this.size,n=this._step,i=e?this._start+(r-1)*n:this._start,o=0;o!==r&&!1!==t(i,e?r-++o:o++,this);)i+=e?-n:n;return o},e.prototype.__iterator=function(t,e){var r=this.size,n=this._step,i=e?this._start+(r-1)*n:this._start,o=0;return new L((function(){if(o===r)return{value:void 0,done:!0};var u=i;return i+=e?-n:n,C(t,e?r-++o:o++,u)}))},e.prototype.equals=function(t){return t instanceof e?this._start===t._start&&this._end===t._end&&this._step===t._step:kr(this,t)},e}(F);function Hr(t,e,r){for(var n=$t(e),o=0;o!==n.length;)if((t=oe(t,n[o++],i))===i)return r;return t}function Jr(t,e){return Hr(this,t,e)}function Vr(t,e){return Hr(t,e,i)!==i}function Yr(){Zt(this.size);var t={};return this.__iterate((function(e,r){t[r]=e})),t}z.isIterable=y,z.isKeyed=g,z.isIndexed=m,z.isAssociative=S,z.isOrdered=x,z.Iterator=L,Rr(z,{toArray:function(){Zt(this.size);var t=new Array(this.size||0),e=g(this),r=0;return this.__iterate((function(n,i){t[r++]=e?[i,n]:n})),t},toIndexedSeq:function(){return new Dt(this)},toJS:function(){return Ur(this)},toKeyedSeq:function(){return new Mt(this,!0)},toMap:function(){return Ue(this.toKeyedSeq())},toObject:Yr,toOrderedMap:function(){return wr(this.toKeyedSeq())},toOrderedSet:function(){return sn(g(this)?this.valueSeq():this)},toSet:function(){return Kr(g(this)?this.valueSeq():this)},toSetSeq:function(){return new At(this)},toSeq:function(){return m(this)?this.toIndexedSeq():g(this)?this.toKeyedSeq():this.toSetSeq()},toStack:function(){return Or(g(this)?this.valueSeq():this)},toList:function(){return or(g(this)?this.valueSeq():this)},toString:function(){return"[Collection]"},__toString:function(t,e){return 0===this.size?t+e:t+" "+this.toSeq().map(this.__toStringMapper).join(", ")+" "+e},concat:function(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];return Ht(this,function(t,e){var r=g(t),n=[t].concat(e).map((function(t){return y(t)?r&&(t=b(t)):t=r?nt(t):it(Array.isArray(t)?t:[t]),t})).filter((function(t){return 0!==t.size}));if(0===n.length)return t;if(1===n.length){var i=n[0];if(i===t||r&&g(i)||m(t)&&m(i))return i}var o=new Z(n);return r?o=o.toKeyedSeq():m(t)||(o=o.toSetSeq()),(o=o.flatten(!0)).size=n.reduce((function(t,e){if(void 0!==t){var r=e.size;if(void 0!==r)return t+r}}),0),o}(this,t))},includes:function(t){return this.some((function(e){return ft(e,t)}))},entries:function(){return this.__iterator(U)},every:function(t,e){Zt(this.size);var r=!0;return this.__iterate((function(n,i,o){if(!t.call(e,n,i,o))return r=!1,!1})),r},filter:function(t,e){return Ht(this,Kt(this,t,e,!0))},partition:function(t,e){return function(t,e,r){var n=g(t),i=[[],[]];t.__iterate((function(o,u){i[e.call(r,o,u,t)?1:0].push(n?[u,o]:o)}));var o=Vt(t);return i.map((function(e){return Ht(t,o(e))}))}(this,t,e)},find:function(t,e,r){var n=this.findEntry(t,e);return n?n[1]:r},forEach:function(t,e){return Zt(this.size),this.__iterate(e?t.bind(e):t)},join:function(t){Zt(this.size),t=void 0!==t?""+t:",";var e="",r=!0;return this.__iterate((function(n){r?r=!1:e+=t,e+=null!=n?n.toString():""})),e},keys:function(){return this.__iterator(k)},map:function(t,e){return Ht(this,Rt(this,t,e))},reduce:function(t,e,r){return Zr(this,t,e,r,arguments.length<2,!1)},reduceRight:function(t,e,r){return Zr(this,t,e,r,arguments.length<2,!0)},reverse:function(){return Ht(this,Ut(this,!0))},slice:function(t,e){return Ht(this,Tt(this,t,e,!0))},some:function(t,e){Zt(this.size);var r=!1;return this.__iterate((function(n,i,o){if(t.call(e,n,i,o))return r=!0,!1})),r},sort:function(t){return Ht(this,Ct(this,t))},values:function(){return this.__iterator(R)},butLast:function(){return this.slice(0,-1)},isEmpty:function(){return void 0!==this.size?0===this.size:!this.some((function(){return!0}))},count:function(t,e){return s(t?this.toSeq().filter(t,e):this)},countBy:function(t,e){return function(t,e,r){var n=Ue().asMutable();return t.__iterate((function(i,o){n.update(e.call(r,i,o,t),0,(function(t){return t+1}))})),n.asImmutable()}(this,t,e)},equals:function(t){return kr(this,t)},entrySeq:function(){var t=this;if(t._cache)return new Z(t._cache);var e=t.toSeq().map(tn).toIndexedSeq();return e.fromEntrySeq=function(){return t.toSeq()},e},filterNot:function(t,e){return this.filter(en(t),e)},findEntry:function(t,e,r){var n=r;return this.__iterate((function(r,i,o){if(t.call(e,r,i,o))return n=[i,r],!1})),n},findKey:function(t,e){var r=this.findEntry(t,e);return r&&r[0]},findLast:function(t,e,r){return this.toKeyedSeq().reverse().find(t,e,r)},findLastEntry:function(t,e,r){return this.toKeyedSeq().reverse().findEntry(t,e,r)},findLastKey:function(t,e){return this.toKeyedSeq().reverse().findKey(t,e)},first:function(t){return this.find(c,null,t)},flatMap:function(t,e){return Ht(this,function(t,e,r){var n=Vt(t);return t.toSeq().map((function(i,o){return n(e.call(r,i,o,t))})).flatten(!0)}(this,t,e))},flatten:function(t){return Ht(this,Lt(this,t,!0))},fromEntrySeq:function(){return new xt(this)},get:function(t,e){return this.find((function(e,r){return ft(r,t)}),void 0,e)},getIn:Jr,groupBy:function(t,e){return function(t,e,r){var n=g(t),i=(x(t)?wr():Ue()).asMutable();t.__iterate((function(o,u){i.update(e.call(r,o,u,t),(function(t){return(t=t||[]).push(n?[u,o]:o),t}))}));var o=Vt(t);return i.map((function(e){return Ht(t,o(e))})).asImmutable()}(this,t,e)},has:function(t){return this.get(t,i)!==i},hasIn:function(t){return Vr(this,t)},isSubset:function(t){return t="function"==typeof t.includes?t:z(t),this.every((function(e){return t.includes(e)}))},isSuperset:function(t){return(t="function"==typeof t.isSubset?t:z(t)).isSubset(this)},keyOf:function(t){return this.findKey((function(e){return ft(e,t)}))},keySeq:function(){return this.toSeq().map($r).toIndexedSeq()},last:function(t){return this.toSeq().reverse().first(t)},lastKeyOf:function(t){return this.toKeyedSeq().reverse().keyOf(t)},max:function(t){return Pt(this,t)},maxBy:function(t,e){return Pt(this,e,t)},min:function(t){return Pt(this,t?rn(t):on)},minBy:function(t,e){return Pt(this,e?rn(e):on,t)},rest:function(){return this.slice(1)},skip:function(t){return 0===t?this:this.slice(Math.max(0,t))},skipLast:function(t){return 0===t?this:this.slice(0,-Math.max(0,t))},skipWhile:function(t,e){return Ht(this,Bt(this,t,e,!0))},skipUntil:function(t,e){return this.skipWhile(en(t),e)},sortBy:function(t,e){return Ht(this,Ct(this,e,t))},take:function(t){return this.slice(0,Math.max(0,t))},takeLast:function(t){return this.slice(-Math.max(0,t))},takeWhile:function(t,e){return Ht(this,function(t,e,r){var n=Yt(t);return n.__iterateUncached=function(n,i){var o=this;if(i)return this.cacheResult().__iterate(n,i);var u=0;return t.__iterate((function(t,i,s){return e.call(r,t,i,s)&&++u&&n(t,i,o)})),u},n.__iteratorUncached=function(n,i){var o=this;if(i)return this.cacheResult().__iterator(n,i);var u=t.__iterator(U,i),s=!0;return new L((function(){if(!s)return{value:void 0,done:!0};var t=u.next();if(t.done)return t;var i=t.value,a=i[0],c=i[1];return e.call(r,c,a,o)?n===U?t:C(n,a,c,t):(s=!1,{value:void 0,done:!0})}))},n}(this,t,e))},takeUntil:function(t,e){return this.takeWhile(en(t),e)},update:function(t){return t(this)},valueSeq:function(){return this.toIndexedSeq()},hashCode:function(){return this.__hash||(this.__hash=function(t){if(t.size===1/0)return 0;var e=x(t),r=g(t),n=e?1:0;return function(t,e){return e=ht(e,3432918353),e=ht(e<<15|e>>>-15,461845907),e=ht(e<<13|e>>>-13,5),e=e+3864292196^t,e=ht(e^e>>>16,2246822507),e=ht(e^e>>>13,3266489909),e=pt(e^e>>>16),e}(t.__iterate(r?e?function(t,e){n=31*n+un(lt(t),lt(e))|0}:function(t,e){n=n+un(lt(t),lt(e))|0}:e?function(t){n=31*n+lt(t)|0}:function(t){n=n+lt(t)|0}),n)}(this))}});var Qr=z.prototype;Qr[v]=!0,Qr[B]=Qr.values,Qr.toJSON=Qr.toArray,Qr.__toStringMapper=ne,Qr.inspect=Qr.toSource=function(){return this.toString()},Qr.chain=Qr.flatMap,Qr.contains=Qr.includes,Rr(b,{flip:function(){return Ht(this,kt(this))},mapEntries:function(t,e){var r=this,n=0;return Ht(this,this.toSeq().map((function(i,o){return t.call(e,[o,i],n++,r)})).fromEntrySeq())},mapKeys:function(t,e){var r=this;return Ht(this,this.toSeq().flip().map((function(n,i){return t.call(e,n,i,r)})).flip())}});var Xr=b.prototype;Xr[d]=!0,Xr[B]=Qr.entries,Xr.toJSON=Yr,Xr.__toStringMapper=function(t,e){return ne(e)+": "+ne(t)},Rr(I,{toKeyedSeq:function(){return new Mt(this,!1)},filter:function(t,e){return Ht(this,Kt(this,t,e,!1))},findIndex:function(t,e){var r=this.findEntry(t,e);return r?r[0]:-1},indexOf:function(t){var e=this.keyOf(t);return void 0===e?-1:e},lastIndexOf:function(t){var e=this.lastKeyOf(t);return void 0===e?-1:e},reverse:function(){return Ht(this,Ut(this,!1))},slice:function(t,e){return Ht(this,Tt(this,t,e,!1))},splice:function(t,e){var r=arguments.length;if(e=Math.max(e||0,0),0===r||2===r&&!e)return this;t=h(t,t<0?this.count():this.size);var n=this.slice(0,t);return Ht(this,1===r?n:n.concat(Ft(arguments,2),this.slice(t+e)))},findLastIndex:function(t,e){var r=this.findLastEntry(t,e);return r?r[0]:-1},first:function(t){return this.get(0,t)},flatten:function(t){return Ht(this,Lt(this,t,!1))},get:function(t,e){return(t=a(this,t))<0||this.size===1/0||void 0!==this.size&&t>this.size?e:this.find((function(e,r){return r===t}),void 0,e)},has:function(t){return(t=a(this,t))>=0&&(void 0!==this.size?this.size===1/0||t<this.size:-1!==this.indexOf(t))},interpose:function(t){return Ht(this,function(t,e){var r=Yt(t);return r.size=t.size&&2*t.size-1,r.__iterateUncached=function(r,n){var i=this,o=0;return t.__iterate((function(t){return(!o||!1!==r(e,o++,i))&&!1!==r(t,o++,i)}),n),o},r.__iteratorUncached=function(r,n){var i,o=t.__iterator(R,n),u=0;return new L((function(){return(!i||u%2)&&(i=o.next()).done?i:u%2?C(r,u++,e):C(r,u++,i.value,i)}))},r}(this,t))},interleave:function(){var t=[this].concat(Ft(arguments)),e=Nt(this.toSeq(),F.of,t),r=e.flatten(!0);return e.size&&(r.size=e.size*t.length),Ht(this,r)},keySeq:function(){return Nr(0,this.size)},last:function(t){return this.get(-1,t)},skipWhile:function(t,e){return Ht(this,Bt(this,t,e,!1))},zip:function(){return Ht(this,Nt(this,nn,[this].concat(Ft(arguments))))},zipAll:function(){return Ht(this,Nt(this,nn,[this].concat(Ft(arguments)),!0))},zipWith:function(t){var e=Ft(arguments);return e[0]=this,Ht(this,Nt(this,t,e))}});var Fr=I.prototype;Fr[w]=!0,Fr[A]=!0,Rr(O,{get:function(t,e){return this.has(t)?t:e},includes:function(t){return this.has(t)},keySeq:function(){return this.valueSeq()}});var Gr=O.prototype;function Zr(t,e,r,n,i,o){return Zt(t.size),t.__iterate((function(t,o,u){i?(i=!1,r=t):r=e.call(n,r,t,o,u)}),o),r}function $r(t,e){return e}function tn(t,e){return[e,t]}function en(t){return function(){return!t.apply(this,arguments)}}function rn(t){return function(){return-t.apply(this,arguments)}}function nn(){return Ft(arguments)}function on(t,e){return t<e?1:t>e?-1:0}function un(t,e){return t^e+2654435769+(t<<6)+(t>>2)}Gr.has=Qr.includes,Gr.contains=Gr.includes,Gr.keys=Gr.values,Rr(X,Xr),Rr(F,Fr),Rr(G,Gr);var sn=function(t){function e(t){return null==t?hn():xr(t)?t:hn().withMutations((function(e){var r=O(t);Zt(r.size),r.forEach((function(t){return e.add(t)}))}))}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.of=function(){return this(arguments)},e.fromKeys=function(t){return this(b(t).keySeq())},e.prototype.toString=function(){return this.__toString("OrderedSet {","}")},e}(Kr);sn.isOrderedSet=xr;var an,cn=sn.prototype;function fn(t,e){var r=Object.create(cn);return r.size=t?t.size:0,r._map=t,r.__ownerID=e,r}function hn(){return an||(an=fn(Sr()))}cn[A]=!0,cn.zip=Fr.zip,cn.zipWith=Fr.zipWith,cn.zipAll=Fr.zipAll,cn.__empty=hn,cn.__make=fn;var pn={LeftThenRight:-1,RightThenLeft:1};var _n=function(t,e){var r;!function(t){if(M(t))throw new Error("Can not call `Record` with an immutable Record as default values. Use a plain javascript object instead.");if(D(t))throw new Error("Can not call `Record` with an immutable Collection as default values. Use a plain javascript object instead.");if(null===t||"object"!=typeof t)throw new Error("Can not call `Record` with a non-object as default values. Use a plain javascript object instead.")}(t);var n=function(o){var u=this;if(o instanceof n)return o;if(!(this instanceof n))return new n(o);if(!r){r=!0;var s=Object.keys(t),a=i._indices={};i._name=e,i._keys=s,i._defaultValues=t;for(var c=0;c<s.length;c++){var f=s[c];a[f]=c,i[f]?"object"==typeof console&&console.warn:gn(i,f)}}return this.__ownerID=void 0,this._values=or().withMutations((function(t){t.setSize(u._keys.length),b(o).forEach((function(e,r){t.set(u._indices[r],e===u._defaultValues[r]?void 0:e)}))})),this},i=n.prototype=Object.create(ln);return i.constructor=n,e&&(n.displayName=e),n};_n.prototype.toString=function(){for(var t,e=yn(this)+" { ",r=this._keys,n=0,i=r.length;n!==i;n++)e+=(n?", ":"")+(t=r[n])+": "+ne(this.get(t));return e+" }"},_n.prototype.equals=function(t){return this===t||M(t)&&dn(this).equals(dn(t))},_n.prototype.hashCode=function(){return dn(this).hashCode()},_n.prototype.has=function(t){return this._indices.hasOwnProperty(t)},_n.prototype.get=function(t,e){if(!this.has(t))return e;var r=this._indices[t],n=this._values.get(r);return void 0===n?this._defaultValues[t]:n},_n.prototype.set=function(t,e){if(this.has(t)){var r=this._values.set(this._indices[t],e===this._defaultValues[t]?void 0:e);if(r!==this._values&&!this.__ownerID)return vn(this,r)}return this},_n.prototype.remove=function(t){return this.set(t)},_n.prototype.clear=function(){var t=this._values.clear().setSize(this._keys.length);return this.__ownerID?this:vn(this,t)},_n.prototype.wasAltered=function(){return this._values.wasAltered()},_n.prototype.toSeq=function(){return dn(this)},_n.prototype.toJS=function(){return Ur(this)},_n.prototype.entries=function(){return this.__iterator(U)},_n.prototype.__iterator=function(t,e){return dn(this).__iterator(t,e)},_n.prototype.__iterate=function(t,e){return dn(this).__iterate(t,e)},_n.prototype.__ensureOwner=function(t){if(t===this.__ownerID)return this;var e=this._values.__ensureOwner(t);return t?vn(this,e,t):(this.__ownerID=t,this._values=e,this)},_n.isRecord=M,_n.getDescriptiveName=yn;var ln=_n.prototype;function vn(t,e,r){var n=Object.create(Object.getPrototypeOf(t));return n._values=e,n.__ownerID=r,n}function yn(t){return t.constructor.displayName||t.constructor.name||"Record"}function dn(t){return nt(t._keys.map((function(e){return[e,t.get(e)]})))}function gn(t,e){try{Object.defineProperty(t,e,{get:function(){return this.get(e)},set:function(t){Gt(this.__ownerID,"Cannot set on an immutable record."),this.set(e,t)}})}catch(r){}}ln[q]=!0,ln[t]=ln.remove,ln.deleteIn=ln.removeIn=le,ln.getIn=Jr,ln.hasIn=Qr.hasIn,ln.merge=ge,ln.mergeWith=we,ln.mergeIn=Me,ln.mergeDeep=je,ln.mergeDeepWith=qe,ln.mergeDeepIn=De,ln.setIn=pe,ln.update=ye,ln.updateIn=de,ln.withMutations=Ae,ln.asMutable=xe,ln.asImmutable=ke,ln[B]=ln.entries,ln.toJSON=ln.toObject=Qr.toObject,ln.inspect=ln.toSource=function(){return this.toString()};var wn,mn=function(t){function e(t,r){if(!(this instanceof e))return new e(t,r);if(this._value=t,this.size=void 0===r?1/0:Math.max(0,r),0===this.size){if(wn)return wn;wn=this}}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.toString=function(){return 0===this.size?"Repeat []":"Repeat [ "+this._value+" "+this.size+" times ]"},e.prototype.get=function(t,e){return this.has(t)?this._value:e},e.prototype.includes=function(t){return ft(this._value,t)},e.prototype.slice=function(t,r){var n=this.size;return f(t,r,n)?this:new e(this._value,p(r,n)-h(t,n))},e.prototype.reverse=function(){return this},e.prototype.indexOf=function(t){return ft(this._value,t)?0:-1},e.prototype.lastIndexOf=function(t){return ft(this._value,t)?this.size:-1},e.prototype.__iterate=function(t,e){for(var r=this.size,n=0;n!==r&&!1!==t(this._value,e?r-++n:n++,this););return n},e.prototype.__iterator=function(t,e){var r=this,n=this.size,i=0;return new L((function(){return i===n?{value:void 0,done:!0}:C(t,e?n-++i:i++,r._value)}))},e.prototype.equals=function(t){return t instanceof e?ft(this._value,t._value):kr(t)},e}(F);function Sn(t,e){return zn([],e||bn,t,"",e&&e.length>2?[]:void 0,{"":t})}function zn(t,e,r,n,i,o){if("string"!=typeof r&&!D(r)&&(Y(r)||W(r)||ee(r))){if(~t.indexOf(r))throw new TypeError("Cannot convert circular structure to Immutable");t.push(r),i&&""!==n&&i.push(n);var u=e.call(o,n,Q(r).map((function(n,o){return zn(t,e,n,o,i,r)})),i&&i.slice());return t.pop(),i&&i.pop(),u}return r}function bn(t,e){return m(e)?e.toList():g(e)?e.toMap():e.toSet()}var In="4.3.5",On={version:In,Collection:z,Iterable:z,Seq:Q,Map:Ue,OrderedMap:wr,List:or,Stack:Or,Set:Kr,OrderedSet:sn,PairSorting:pn,Record:_n,Range:Nr,Repeat:mn,is:ft,fromJS:Sn,hash:lt,isImmutable:D,isCollection:y,isKeyed:g,isIndexed:m,isAssociative:S,isOrdered:x,isValueObject:ct,isPlainObject:ee,isSeq:j,isList:ir,isMap:st,isOrderedMap:at,isStack:Ir,isSet:Ar,isOrderedSet:xr,isRecord:M,get:oe,getIn:Hr,has:ie,hasIn:Vr,merge:Se,mergeDeep:be,mergeWith:ze,mergeDeepWith:Ie,remove:se,removeIn:_e,set:ae,setIn:he,update:ve,updateIn:ce},En=z;const jn=Object.freeze(Object.defineProperty({__proto__:null,Collection:z,Iterable:En,List:or,Map:Ue,OrderedMap:wr,OrderedSet:sn,PairSorting:pn,Range:Nr,Record:_n,Repeat:mn,Seq:Q,Set:Kr,Stack:Or,default:On,fromJS:Sn,get:oe,getIn:Hr,has:ie,hasIn:Vr,hash:lt,is:ft,isAssociative:S,isCollection:y,isImmutable:D,isIndexed:m,isKeyed:g,isList:ir,isMap:st,isOrdered:x,isOrderedMap:at,isOrderedSet:xr,isPlainObject:ee,isRecord:M,isSeq:j,isSet:Ar,isStack:Ir,isValueObject:ct,merge:Se,mergeDeep:be,mergeDeepWith:Ie,mergeWith:ze,remove:se,removeIn:_e,set:ae,setIn:he,update:ve,updateIn:ce,version:In},Symbol.toStringTag,{value:"Module"}));export{jn as i};
