import"./vue-5bfa3a54.js";import{h as e,K as t,w as n,e as o,s as a,m as i,p as c,a4 as r,j as s}from"./@vue-5e5cdef9.js";import{o as u,a as l}from"./evtd-12f38dac.js";function d(o){const a=e(!!o.value);if(a.value)return t(a);const i=n(o,(e=>{e&&(a.value=!0,i())}));return t(a)}function f(t){const a=o(t),i=e(a.value);return n(a,(e=>{i.value=e})),"function"==typeof t?i:{__v_isRef:!0,get value(){return i.value},set value(e){t.set(e)}}}function v(){return null!==a()}const m="undefined"!=typeof window;let h,w;var p,k;function y(e){if(w)return;let t=!1;i((()=>{w||null==h||h.then((()=>{t||e()}))})),c((()=>{t=!0}))}h=m?null===(k=null===(p=document)||void 0===p?void 0:p.fonts)||void 0===k?void 0:k.ready:void 0,w=!1,void 0!==h?h.then((()=>{w=!0})):w=!0;const g=e(null);function b(e){if(e.clientX>0||e.clientY>0)g.value={x:e.clientX,y:e.clientY};else{const{target:t}=e;if(t instanceof Element){const{left:e,top:n,width:o,height:a}=t.getBoundingClientRect();g.value=e>0||n>0?{x:e+o/2,y:n+a/2}:{x:0,y:0}}else g.value=null}}let E=0,L=!0;function x(){if(!m)return t(e(null));0===E&&u("click",document,b,!0);const n=()=>{E+=1};return L&&(L=v())?(r(n),c((()=>{E-=1,0===E&&l("click",document,b,!0)}))):n(),t(g)}const M=e(void 0);let j=0;function P(){M.value=Date.now()}let S=!0;function T(n){if(!m)return t(e(!1));const o=e(!1);let a=null;function i(){null!==a&&window.clearTimeout(a)}function s(){i(),o.value=!0,a=window.setTimeout((()=>{o.value=!1}),n)}0===j&&u("click",window,P,!0);const d=()=>{j+=1,u("click",window,s,!0)};return S&&(S=v())?(r(d),c((()=>{j-=1,0===j&&l("click",window,P,!0),l("click",window,s,!0),i()}))):d(),t(o)}let C=0;const D="undefined"!=typeof window&&void 0!==window.matchMedia,O=e(null);let _,q;function R(e){e.matches&&(O.value="dark")}function X(e){e.matches&&(O.value="light")}let Y=!0;function B(){return D?(0===C&&(_=window.matchMedia("(prefers-color-scheme: dark)"),q=window.matchMedia("(prefers-color-scheme: light)"),_.matches?O.value="dark":q.matches?O.value="light":O.value=null,_.addEventListener?(_.addEventListener("change",R),q.addEventListener("change",X)):_.addListener&&(_.addListener(R),q.addListener(X))),Y&&(Y=v())&&(r((()=>{C+=1})),c((()=>{C-=1,0===C&&("removeEventListener"in _?(_.removeEventListener("change",R),q.removeEventListener("change",X)):"removeListener"in _&&(_.removeListener(R),q.removeListener(X)),_=void 0,q=void 0)}))),t(O)):t(O)}function I(e,t){return n(e,(e=>{void 0!==e&&(t.value=e)})),o((()=>void 0===e.value?t.value:e.value))}function K(){const n=e(!1);return i((()=>{n.value=!0})),t(n)}function $(e,t){return o((()=>{for(const n of t)if(void 0!==e[n])return e[n];return e[t[t.length-1]]}))}const z="undefined"!=typeof window&&(/iPad|iPhone|iPod/.test(navigator.platform)||"MacIntel"===navigator.platform&&navigator.maxTouchPoints>1)&&!window.MSStream;function A(){return z}const F={xs:0,s:640,m:1024,l:1280,xl:1536,"2xl":1920};const G={};function H(t=F){if(!m)return o((()=>[]));if("function"!=typeof window.matchMedia)return o((()=>[]));const n=e({}),a=Object.keys(t),i=(e,t)=>{e.matches?n.value[t]=!0:n.value[t]=!1};return a.forEach((e=>{const n=t[e];let o,a;void 0===G[n]?(o=window.matchMedia(`(min-width: ${n}px)`),o.addEventListener?o.addEventListener("change",(t=>{a.forEach((n=>{n(t,e)}))})):o.addListener&&o.addListener((t=>{a.forEach((n=>{n(t,e)}))})),a=new Set,G[n]={mql:o,cbs:a}):(o=G[n].mql,a=G[n].cbs),a.add(i),o.matches&&a.forEach((t=>{t(o,e)}))})),c((()=>{a.forEach((e=>{const{cbs:n}=G[t[e]];n.has(i)&&n.delete(i)}))})),o((()=>{const{value:e}=n;return a.filter((t=>e[t]))}))}function J(e={},o){const a=s({ctrl:!1,command:!1,win:!1,shift:!1,tab:!1}),{keydown:i,keyup:d}=e,f=e=>{switch(e.key){case"Control":a.ctrl=!0;break;case"Meta":a.command=!0,a.win=!0;break;case"Shift":a.shift=!0;break;case"Tab":a.tab=!0}void 0!==i&&Object.keys(i).forEach((t=>{if(t!==e.key)return;const n=i[t];if("function"==typeof n)n(e);else{const{stop:t=!1,prevent:o=!1}=n;t&&e.stopPropagation(),o&&e.preventDefault(),n.handler(e)}}))},m=e=>{switch(e.key){case"Control":a.ctrl=!1;break;case"Meta":a.command=!1,a.win=!1;break;case"Shift":a.shift=!1;break;case"Tab":a.tab=!1}void 0!==d&&Object.keys(d).forEach((t=>{if(t!==e.key)return;const n=d[t];if("function"==typeof n)n(e);else{const{stop:t=!1,prevent:o=!1}=n;t&&e.stopPropagation(),o&&e.preventDefault(),n.handler(e)}}))},h=()=>{(void 0===o||o.value)&&(u("keydown",document,f),u("keyup",document,m)),void 0!==o&&n(o,(e=>{e?(u("keydown",document,f),u("keyup",document,m)):(l("keydown",document,f),l("keyup",document,m))}))};return v()?(r(h),c((()=>{(void 0===o||o.value)&&(l("keydown",document,f),l("keyup",document,m))}))):h(),t(a)}export{A as a,I as b,$ as c,d,J as e,T as f,x as g,H as h,K as i,B as j,y as o,f as u};
