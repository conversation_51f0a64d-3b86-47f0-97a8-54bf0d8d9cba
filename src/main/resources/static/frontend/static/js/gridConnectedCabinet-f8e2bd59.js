import{_ as e,u as s,a as t}from"./index-a5df0f75.js";import{_ as i}from"./index-04837d66.js";import{X as l}from"./element-plus-95e0b914.js";import"./vue-5bfa3a54.js";import{g as n}from"./index-05697651.js";import{g as a}from"./imgImport-cfe60b78.js";import"./fabric-8dd10b04.js";import{K as o}from"./konva-5c630d74.js";import{g as r}from"./gsap-91da67c7.js";import{d as c}from"./dayjs-67f8ddef.js";import{x as m}from"./xe-utils-fe99d42a.js";import{d as p}from"./@vueuse-5227c686.js";import{h as u,j as d,m as f,v as g,o as v,c as h,a as j,t as w,b as x,f as b,l as y,x as _,F as k,k as z,y as C,C as R,D}from"./@vue-5e5cdef9.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./vue-router-6159329f.js";import"./bignumber.js-a537a5ca.js";import"./js-cookie-8253c38e.js";import"./axios-84f1a956.js";import"./lodash-es-ea7deab5.js";import"./spark-md5-022b35d0.js";import"./@babel-f3c0a00c.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";import"./quasar-df1bac18.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./@element-plus-4c34063a.js";import"./vxe-table-plugin-element-be30dbb6.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./lodash-6d99edc3.js";import"./vue-konva-ae2c3fae.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./screenfull-c82f2093.js";import"./chartResize-3e3d11d7.js";import"./element-resize-detector-0d37a2ab.js";import"./batch-processor-06abf2b4.js";const I=e=>(R("data-v-ad5ff0c1"),e=e(),D(),e),L={class:"header"},N={class:"date font-small-text-size2 normal regular-title"},G={class:"title"},S={class:"main-title font-title-size text"},Y={class:"screen-full"},T={class:"left-sensor"},W={class:"item-title-bg font-title-size"},X=["src"],H={class:"sensor-box"},M={key:0,class:"circle-img"},O=["src"],A=["src"],E={key:1,class:"circle-img"},U=["src"],J={class:"u-flex-column"},P={class:"font-small-text-size2"},q={key:0},B={key:1},F={class:"smokeValue font-small-text-size1"},K={class:"smokeUnit font-small-text-size3"},V={class:"right-sensor"},Z={class:"item-title-bg font-title-size"},$=["src"],Q={class:"sensor-box"},ee={key:0,class:"circle-img"},se=["src"],te=["src"],ie={key:1,class:"circle-img"},le=["src"],ne={class:"u-flex-column"},ae={class:"font-small-text-size2"},oe={key:0},re={key:1},ce={class:"smokeValue font-small-text-size1"},me={class:"smokeUnit font-small-text-size3"},pe={class:"title"},ue={class:"item-title-bg font-title-size"},de=["src"],fe=I((()=>j("span",null,"配电房1",-1))),ge={class:"right-wiringDiagram"},ve={class:"title"},he={class:"item-title-bg font-title-size"},je=["src"],we=I((()=>j("span",null,"配电房2",-1))),xe=e({__name:"gridConnectedCabinet",setup(e){const R=s(),D=u(),I=d({data:c().format("YYYY-MM-DD HH:mm:ss")});u(),u(),u(),u(),u(),u(),u(),u(),u(),u(!1),u();const xe=d([]),be={alarmStatus:{icon:"",label:"故障状态",value:"报警",unit:"",picName:"warn.png",end:!1},smokeConcentr:{icon:"mist",label:"烟雾浓度",value:0,unit:"PPM",picName:"",end:!1},temp:{icon:"temp",label:"当前温度",value:0,unit:"°C",picName:"",end:!1},humidity:{icon:"humidity",label:"当前湿度",value:0,unit:"%RH",picName:"",end:!0}},ye=d([]),_e=u(null),ke=u(null),ze=u(null),Ce=d({width:1200,height:900});let Re=!1,De=null;(new window.Image).src=a("bingwang","1.png");const Ie=(e,s)=>{De&&De.clear(),De=new o.Stage({container:e.value,width:940,height:860,scale:s});const t=new o.Layer;De.add(t),[0,1,2,3,4,5].forEach((e=>{o.Image.fromURL(a("bingwang","R6-30-50K.png"),(function(s){s.setAttrs({x:150*e,y:0,scaleX:.3,scaleY:.3}),t.add(s),t.batchDraw()}))})),[0,1].forEach((e=>{o.Image.fromURL(a("bingwang","1.png"),(function(s){s.setAttrs({x:200+450*e,y:300,scaleX:.4,scaleY:.4}),s.on("click",(e=>{Re=!Re,((e,s,t,i,l,n)=>{const a=new o.Rect({id:"promptRect",x:t,y:i,stroke:"#555",strokeWidth:5,fill:"#ddd",width:200,height:200,shadowColor:"black",shadowBlur:10,shadowOffsetX:10,shadowOffsetY:10,shadowOpacity:.2,cornerRadius:10});let r=new o.Text({id:"promptText",x:t,y:i,text:"COMPLEX TEXT\n\nAll the world's a stage, and all the men and women merely players. They have their exits and their entrances.",fontSize:18,fontFamily:"Calibri",fill:"#555",width:200,padding:20,align:"center"});if(!l)return s.find("#promptRect")[0].destroy(),void s.find("#promptText")[0].destroy();s.add(a),s.add(r)})(0,t,e.evt.offsetX,e.evt.offsetY,Re)})),t.add(s),t.batchDraw()}))}));const i={line1:[100,130,100,350,200,350],line2:[250,130,250,350],line3:[400,130,400,350,300,350],line4:[550,130,550,350,650,350],line5:[700,130,700,350],line6:[850,130,850,350,750,350]};for(let n in i){i[n]=new o.Line({points:i[n],stroke:"green",strokeWidth:5,lineCap:"round",lineJoin:"round"}),t.add(i[n]);const e=new o.Rect({x:i[n].attrs.points[0]-5,y:i[n].attrs.points[1],width:10,height:10,fill:"blue"});t.add(e);const s=i[n].attrs.points[0]-5,l=i[n].attrs.points[1],a=i[n].attrs.points.at(-2),c=i[n].attrs.points.at(-1)-5,m=r.timeline({paused:!0});m.to(e,{y:c,duration:1}).to(e,{x:a,duration:1},"<+=1"),m.to(e,{x:s,duration:1}).to(e,{y:l,duration:1},"<+=1").addLabel("Done","+=0"),m.add((()=>m.seek(0)),"Done+=0.01"),m.play()}const l={line_1:[220,500,220,650,180,650,180,700],point_1:[180,700],group_1:new o.Group,line_2:[250,500,250,700],point_2:[250,700],group_2:new o.Group,line_3:[270,500,270,700],point_3:[270,700],group_3:new o.Group,line_4:[300,500,300,650,340,650,340,700],point_4:[340,700],group_4:new o.Group,line_5:[670,500,670,650,630,650,630,700],point_5:[630,700],group_5:new o.Group,line_6:[700,500,700,700],point_6:[700,700],group_6:new o.Group,line_7:[730,500,730,650,730,650,730,700],point_7:[730,700],group_7:new o.Group,line_8:[760,500,760,650,800,650,800,700],point_8:[800,700],group_8:new o.Group};for(let n in l)n.includes("line")?l[n]=new o.Line({points:l[n],stroke:"green",fill:"blue",strokeWidth:5,lineCap:"round",lineJoin:"round"}):n.includes("point")?l[n]=new o.Circle({x:l[n][0],y:l[n][1],radius:5,fill:"red",stroke:"red",strokeWidth:4}):(l[n].add(l["line_"+n.split("_")[1]]),l[n].add(l["point_"+n.split("_")[1]]),t.add(l[n]))},Le=m.debounce((()=>{const e={x:1,y:1};p(_e,(s=>{Ce.width=_e.value.offsetWidth,Ce.height=_e.value.offsetHeight,e.x=Ce.width/940,e.y=Ce.height/860,Ie(ke,e),Ie(ze,e)}))}),500);return f((()=>{(async()=>{const{data:e}=await n();for(let s=0;s<e.length;s++){const t=m.clone(be,!0),i=Object.keys(t);xe.push(e[s].deviceName);for(let l=0;l<i.length;l++)"alarmStatus"===i[l]&&0==e[s].children[0].alarmStatus?t[i[l]].value="正常":"alarmStatus"===i[l]&&1==e[s].children[0].alarmStatus?t[i[l]].value="告警":t[i[l]].value=e[s].children[0][i[l]];ye.push(t)}})(),Ie(ke),Ie(ze),Le()})),g((()=>{})),(e,s)=>{const n=l,o=i,r=t;return v(),h("div",{class:"screenfull-content tw-h-full tw-w-full bwg-bg screen-box",ref_key:"screenRef",ref:D},[j("div",L,[j("div",N,w(x(I).data),1),j("div",G,[x(R).userInfo.screenLogo?(v(),b(n,{key:0,src:x(R).userInfo.screenLogo,fit:"contain",class:"image"},null,8,["src"])):y("",!0),j("h1",S,w(x(R).userInfo.projectTitle),1)]),j("div",Y,[_(o,{class:"setting-item",type:"font",element:x(D)},null,8,["element"])])]),j("div",T,[j("div",null,[j("p",W,[j("img",{src:x(a)("screen","title_icon.png"),class:"title-icon"},null,8,X),j("span",null,w(x(xe)[0]),1)])]),j("div",H,[(v(!0),h(k,null,z(x(ye)[0],((e,s)=>(v(),h("div",{key:s,class:"u-flex-1 h-full u-flex-center-no asset-content-item"},[""!==e.picName?(v(),h("p",M,[j("img",{src:x(a)("screen","circle.png"),class:"title-icon0"},null,8,O),j("img",{src:x(a)("screen",e.picName),class:"title-icon1"},null,8,A)])):(v(),h("p",E,[j("img",{src:x(a)("screen","circle.png"),class:"title-icon0"},null,8,U),_(r,{name:e.icon,class:"title-icon1"},null,8,["name"])])),j("p",J,[j("span",P,w(e.label),1),"故障状态"===e.label?(v(),h("span",q,[j("i",{class:C(["font-small-text-size1",!0===e.value?"normal":"abnormal"])},w(e.value),3)])):(v(),h("span",B,[j("i",F,w(e.value),1),j("i",K,w(e.unit),1)]))])])))),128))])]),j("div",V,[j("p",Z,[j("img",{src:x(a)("screen","title_icon.png"),class:"title-icon"},null,8,$),j("span",null,w(x(xe)[1]),1)]),j("div",Q,[(v(!0),h(k,null,z(x(ye)[1],((e,s)=>(v(),h("div",{key:s,class:"u-flex-1 h-full u-flex-center-no asset-content-item"},[""!==e.picName?(v(),h("p",ee,[j("img",{src:x(a)("screen","circle.png"),class:"title-icon0"},null,8,se),j("img",{src:x(a)("screen",e.picName),class:"title-icon1"},null,8,te)])):(v(),h("p",ie,[j("img",{src:x(a)("screen","circle.png"),class:"title-icon0"},null,8,le),_(r,{name:e.icon,class:"title-icon1"},null,8,["name"])])),j("p",ne,[j("span",ae,w(e.label),1),"故障状态"===e.label?(v(),h("span",oe,[j("i",{class:C(["font-small-text-size1",!0===e.value?"normal":"abnormal"])},w(e.value),3)])):(v(),h("span",re,[j("i",ce,w(e.value),1),j("i",me,w(e.unit),1)]))])])))),128))])]),j("div",{class:"left-wiringDiagram",ref_key:"leftWiringDiagramRef",ref:_e},[j("div",pe,[j("p",ue,[j("img",{src:x(a)("screen","title_icon.png"),class:"title-icon"},null,8,de),fe])]),j("div",{ref_key:"leftCanvasRef",ref:ke,class:"canvasStyle"},null,512)],512),j("div",ge,[j("div",ve,[j("p",he,[j("img",{src:x(a)("screen","title_icon.png"),class:"title-icon"},null,8,je),we])]),j("div",{ref_key:"rightCanvasRef",ref:ze,class:"canvasStyle"},null,512)])],512)}}},[["__scopeId","data-v-ad5ff0c1"]]);export{xe as default};
