import{g as e,C as t,d as a,F as l,o as s,$ as i,j as o}from"./quasar-b3f06d8a.js";import{_ as r}from"./MyTable-27fb4664.js";import{_ as p}from"./pagination-c4d8e88e.js";import{_ as n}from"./MyForm-5e5c0ec8.js";import"./vue-5bfa3a54.js";import{f as m}from"./formatTableData-0442e1d7.js";import{j as u,h as c,m as d,az as v,o as j,c as f,x as b,a8 as g,b as y,a as w,t as h,aa as x,a9 as _,H as k,C as N,D as V}from"./@vue-5e5cdef9.js";import{l as C}from"./lodash-6d99edc3.js";import{_ as S,p as U}from"./index-8cc8d4b8.js";import{c as T}from"./pageUtil-3bb2e07a.js";import{f as z}from"./formUtil-a2e6828b.js";import{a as D,e as W}from"./plantManageApi-ea9fcaaf.js";import{g as q}from"./api-b858041e.js";import{e as I}from"./index-fec80322.js";import{e as E}from"./exportFile-7631667a.js";import{X as K}from"./statisticReportApi-dc9fa149.js";import{g as F}from"./naive-ui-0ee0b8c3.js";import"./@vueuse-af86c621.js";import"./vue-router-6159329f.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./@babel-f3c0a00c.js";import"./element-plus-d975be09.js";import"./lodash-es-ea7deab5.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./dayjs-d60cc07f.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./xe-utils-fe99d42a.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-7bbbb435.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./proxyUtil-6f30f7ef.js";import"./menuStore-26f8ddd8.js";import"./icons-95011f8c.js";import"./@vicons-f32a0bdb.js";import"./notification-950a5f80.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./date-fns-tz-0cc073c8.js";const H={plantStatus:"运行状态",projectName:"项目类型",city:"地址",plantName:"站点名称",createTime:"建站日期",plantCapacity:"装机容量:KWp",powerDistributor:"配电箱状态",inverterNum:"逆变器数量",plantType:"站点类型",power:"实时功率:W",todayElectricity:"当日发电量:KWh",dailyEfficiencyPerHour:"日等效小时",yearlyEfficiencyPerHour:"年等效小时",totalElectricity:"累计发电量:KWh",projectCompany:"项目公司",edit:"编辑"},M=Object.keys(H),L=m(H);L.find((e=>"plantName"==e.field)).slot="plantName",L.find((e=>"edit"==e.field)).slot="edit",u([{label:"运行状态",field:"plantStatus",align:"center",search:c("")},{label:"站点名称",field:"plantName",align:"center",slot:"plantName",search:c("")},{label:"test",field:"plantUid",align:"center",search:c("")},{label:"电厂编号",field:"meterId",align:"center",search:c("")},{label:"所属用户",field:"userName",align:"center",search:c("")},{label:"建站日期",field:"createTime",align:"center",search:c("")},{label:"装机容量:KWp",field:"plantCapacity",align:"center",search:c("")},{label:"配电箱状态",field:"powerDistributor",align:"center",search:c("")},{label:"逆变器数量",field:"inverNums",align:"center",search:c("")},{label:"站点类型",field:"plantTypeId",align:"center",search:c("")},{label:"实时功率:W",field:"power",align:"center",search:c("")},{label:"当日发电量:KWh",field:"todayElectricity",align:"center",search:c("")},{label:"日等效小时",field:"equivalentUseHour",align:"center",search:c("")},{label:"年等效小时",field:"equivalentUseHour",align:"center",search:c("")},{label:"累计发电量:KWh",field:"totalElectricity",align:"center",search:c("")},{label:"地址",field:"address",align:"center",search:c("")}]);const R=c([{label:"光精灵离线",value:"0"},{label:"光精灵在线",value:"1"},{label:"正常",value:"2"},{label:"告警",value:"3"},{label:"逆变器夜间离线",value:"4"},{label:"自检提示",value:"5"}]),A={0:[0],1:[1,2,3,4],2:[1,3],3:[2],4:[4],5:[3]},Q=c([{label:"正常",value:1},{label:"配电箱开关故障",value:2},{label:"电表箱开关故障",value:3},{label:"市电停电",value:4},{label:"电表箱开关采样异常",value:5},{label:"市电采样异常",value:6},{label:"市电、电表箱开关采样异常",value:7},{label:"过流开关故障",value:8},{label:"失压开关故障",value:9},{label:"失压开关采样异常",value:10},{label:"市电、失压开关采样异常",value:11}]),P=e=>(N("data-v-342915e0"),e=e(),V(),e),$={class:"app-container"},B=["onClick"],O=P((()=>w("div",{class:"tw-text-2xl"},"编辑电站信息",-1))),X=P((()=>w("span",{class:"tw-text-lg"},"站点名称",-1))),Z=P((()=>w("span",{class:"tw-text-lg"},"电站容量",-1))),G=P((()=>w("span",{class:"tw-text-lg"},"电表编号",-1))),J=S({__name:"plantList",setup(m){const N=C._.curry(U)("/deviceMonitor/plantDetail?plantUid=");let V=c([]);c();const S=T(Y),H=u([{formType:"input",label:"站点名称",prop:"plantName",value:""},{formType:"select",label:"项目名称",prop:"projectName",value:"",keyField:"id",labelField:"projectName",multiple:!1,class:"tw-w-[400px]",options:[]},{formType:"select",label:"电站状态",prop:"plantStatus",value:[],multiple:!0,options:R},{formType:"select",label:"配电箱状态",prop:"powerDistributorStatus",value:"",multiple:!1,options:Q},{formType:"button",label:"查询",value:!1,prop:"check",invoke:Y},{formType:"space"},{formType:"button",label:"重置",value:!1,prop:"reset",invoke:()=>{S.page=1,S.pageSize=10}},{formType:"button",label:"导出",value:!1,prop:"export",invoke:async function(e=z.getValue(H)){var t;const a=await I(e.plantName,null==(t=e.plantStatus)?void 0:t.map((e=>A[e])).flat(),e.powerDistributorStatus,L.map((e=>e.field))),l=await z.exportFile(a,H,"export");E(l,"电站列表")}}]),P=c(!1);let J=c({plantName:"",meterId:"",plantCapacity:""});async function Y(e=z.getValue(H),t,a){var l;let s=await D(S.page,S.pageSize,e.plantName,null==(l=e.plantStatus)?void 0:l.map((e=>A[e])).flat(),e.powerDistributorStatus,e.projectName??"",0);s.response.value.data.list=s.response.value.data.records,z.tableResponse(s,V,S,H,"check",t,a)}async function ee(e){if(e.isTrusted){const e=await W(J.value.plantUid,J.value.meterId,J.value.plantCapacity,J.value.plantName),t=q(e);"00000"==(null==t?void 0:t.status)&&(await Y(),P.value=!1)}else J.value=C._.pick(e,["plantName","meterId","plantCapacity","plantUid"]),P.value=!0}return d((async()=>{const e=z.getQuery();z.setValue(H,"plantStatus",e.status?e.status.split(""):[]),z.setValue(H,"powerDistributorStatus","");const t=await K();let a=q(t).data;!function e(t){var a;for(const l of t)(null==(a=null==l?void 0:l.children)?void 0:a.length)?e(l.children):delete l.children}(a),H[1].options=a,S.page=1})),d((async()=>{const e=z.getQuery();z.setValue(H,"plantStatus",e.status?e.status.split(""):[]),z.setValue(H,"powerDistributorStatus","");const t=await K();let a=q(t).data;!function e(t){var a;for(const l of t)(null==(a=null==l?void 0:l.children)?void 0:a.length)?e(l.children):delete l.children}(a),H[1].options=a,S.page=1})),(m,u)=>{const c=n,d=p,C=F,U=r,T=e,z=t,D=a,W=l,q=s,I=i,E=o,K=v("close-popup");return j(),f("div",$,[b(U,{rowKey:"plantUid",rows:y(V),columns:y(L),visibleColumns:y(M)},{top:g((()=>[b(c,{page:y(S),title:"",formList:y(H)},null,8,["page","formList"])])),bottom:g((()=>[b(d,{page:y(S)},null,8,["page"])])),plantName:g((({col:e,props:t})=>[w("span",{onClick:e=>y(N)(t.row.plantUid),class:"hover:tw-text-blue-600 tw-cursor-pointer"},h(t.row[e.field]),9,B)])),edit:g((({col:e,props:t})=>[b(C,{type:"info",class:"tw-text-white",onClick:e=>ee(t.row)},{default:g((()=>[x(" 编辑 ")])),_:2},1032,["onClick"])])),default:g((()=>[x(" & ")])),_:1},8,["rows","columns","visibleColumns"]),b(E,{modelValue:y(P),"onUpdate:modelValue":u[3]||(u[3]=e=>k(P)?P.value=e:null),persistent:""},{default:g((()=>[b(I,{style:{width:"700px","max-width":"80vw"}},{default:g((()=>[b(T,null,{default:g((()=>[O])),_:1}),b(T,{class:"q-pt-none"},{default:g((()=>[b(D,{class:"tw-text-base",outlined:"",modelValue:y(J).plantName,"onUpdate:modelValue":u[0]||(u[0]=e=>y(J).plantName=e)},{prepend:g((()=>[X,b(z,{vertical:"",inset:"",class:"tw-ml-2"})])),_:1},8,["modelValue"])])),_:1}),b(T,{class:"q-pt-none"},{default:g((()=>[b(D,{class:"tw-text-base",outlined:"",modelValue:y(J).plantCapacity,"onUpdate:modelValue":u[1]||(u[1]=e=>y(J).plantCapacity=e)},{prepend:g((()=>[Z,b(z,{vertical:"",inset:"",class:"tw-ml-2"})])),_:1},8,["modelValue"])])),_:1}),b(T,{class:"q-pt-none"},{default:g((()=>[b(D,{class:"tw-text-base",outlined:"",modelValue:y(J).meterId,"onUpdate:modelValue":u[2]||(u[2]=e=>y(J).meterId=e)},{prepend:g((()=>[G,b(z,{vertical:"",inset:"",class:"tw-ml-2"})])),_:1},8,["modelValue"])])),_:1}),b(q,{align:"right",class:"bg-white text-blue"},{default:g((()=>[_(b(W,{flat:"",label:"取消"},null,512),[[K]]),b(W,{flat:"",label:"确认",onClick:ee})])),_:1})])),_:1})])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-342915e0"]]);export{J as default};
