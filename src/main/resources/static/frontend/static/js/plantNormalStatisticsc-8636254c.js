import{F as e,W as t}from"./quasar-b3f06d8a.js";import{_ as a}from"./date-33a67ff0.js";import"./vue-5bfa3a54.js";import"./echarts-f30da64f.js";import{V as o}from"./zrender-c058db04.js";import{F as l}from"./@vueuse-af86c621.js";import{X as s,c as r,d as i}from"./statisticReportApi-dc9fa149.js";import{g as n}from"./api-b858041e.js";import{d as c}from"./dayjs-d60cc07f.js";import{m as d}from"./notification-950a5f80.js";import{e as m}from"./echartsInit-0067e609.js";import{j as p,h as w,m as u,az as x,o as h,c as f,a as v,x as y,b as g,a8 as b,aa as j,a9 as D,t as S}from"./@vue-5e5cdef9.js";import{e as L,v as F}from"./naive-ui-0ee0b8c3.js";import"./lodash-6d99edc3.js";import"./@babel-f3c0a00c.js";import"./index-8cc8d4b8.js";import"./element-plus-d975be09.js";import"./lodash-es-ea7deab5.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./vue-router-6159329f.js";import"./bignumber.js-a537a5ca.js";import"./xe-utils-fe99d42a.js";import"./js-cookie-8253c38e.js";import"./axios-84f1a956.js";import"./spark-md5-022b35d0.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-7bbbb435.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./tslib-a4e99503.js";import"./@vicons-f32a0bdb.js";import"./menuStore-26f8ddd8.js";import"./icons-95011f8c.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./date-fns-tz-0cc073c8.js";let E,C,z,A,k;{const e=["#d50000","#0091ea","#0090FF","#36CE9E","#FFC005","#FF515A","#8B5CFF","#00CA69"];let t=[{name:"1",value1:100,value2:233},{name:"2",value1:138,value2:233},{name:"3",value1:350,value2:200},{name:"4",value1:173,value2:180},{name:"5",value1:180,value2:199},{name:"6",value1:150,value2:233},{name:"7",value1:180,value2:210},{name:"8",value1:230,value2:180}],a=t.map((e=>e.name)),l=t.map((e=>e.value1)),s=t.map((e=>e.value2));const r=(e,t)=>{let a="";return/^#[\da-f]{6}$/i.test(e)&&(a=`rgba(${parseInt("0x"+e.slice(1,3))},${parseInt("0x"+e.slice(3,5))},${parseInt("0x"+e.slice(5,7))},${t})`),a};E={backgroundColor:"transparent",color:e,legend:{right:10},tooltip:{trigger:"axis"},grid:{top:"30px",bottom:"30px",left:"40px",right:"10px"},xAxis:[{type:"category",boundaryGap:!1,axisLabel:{formatter:"{value}日",color:"#333"},axisLine:{lineStyle:{color:"#D9D9D9"}},data:a}],yAxis:[{type:"value",axisLabel:{color:"#666"},nameTextStyle:{color:"#666",fontSize:12,lineHeight:40},splitLine:{lineStyle:{type:"dashed",color:"#E9E9E9"}},axisLine:{show:!1},axisTick:{show:!1}}],series:[{name:"异常电站数",type:"line",smooth:!0,symbolSize:8,zlevel:3,lineStyle:{normal:{color:e[0],shadowBlur:3,shadowColor:r(e[0],.5),shadowOffsetY:8}},areaStyle:{normal:{color:new o(0,0,0,1,[{offset:0,color:r(e[0],.3)},{offset:1,color:r(e[0],.1)}],!1),shadowColor:r(e[0],.1),shadowBlur:10}},data:l},{name:"正常电站数",type:"line",smooth:!0,symbolSize:8,zlevel:3,lineStyle:{normal:{color:"#0091ea",shadowBlur:3,shadowColor:r("#0091ea",.5),shadowOffsetY:8}},areaStyle:{normal:{color:new o(0,0,0,1,[{offset:0,color:r(e[1],.3)},{offset:1,color:r(e[1],.1)}],!1),shadowColor:r(e[1],.1),shadowBlur:10}},data:s}]}}{const e=(e,t)=>{let a="";return/^#[\da-f]{6}$/i.test(e)&&(a=`rgba(${parseInt("0x"+e.slice(1,3))},${parseInt("0x"+e.slice(3,5))},${parseInt("0x"+e.slice(5,7))},${t})`),a};z={backgroundColor:"transparent",color:["#29def5"],grid:{top:"40px",bottom:"30px",left:"10%",right:"10%"},tooltip:{trigger:"axis",axisPointer:{type:"shadow",label:{show:!0}}},legend:{data:["发电量","收益"],right:"40%",textStyle:{color:"#333"}},xAxis:[{data:[],axisLine:{lineStyle:{color:"#D9D9D9"}},axisLabel:{show:!0,formatter:"{value}月",color:"#333"}}],yAxis:[{type:"value",name:"发电量 (MWh)",nameTextStyle:{color:"#333"},splitLine:{show:!1},lineStyle:{type:"dashed",color:"#E9E9E9"},axisTick:{show:!1},axisLine:{show:!1},axisLabel:{show:!0,color:"#333"}},{type:"value",name:"收益(万)",nameTextStyle:{color:"#333"},position:"right",splitLine:{show:!1},lineStyle:{type:"dashed",color:"#E9E9E9"},axisTick:{show:!1},axisLine:{show:!1},axisLabel:{show:!0,color:"#333"}}],series:[{name:"发电量",type:"bar",barWidth:15,itemStyle:{normal:{color:"#7a99de"}},data:[]},{name:"收益",type:"line",yAxisIndex:1,smooth:!0,showAllSymbol:!0,symbolSize:10,symbolSize:8,zlevel:3,lineStyle:{normal:{color:"#29def5",shadowBlur:3,shadowColor:e("#29def5",.5),shadowOffsetY:8}},areaStyle:{normal:{color:new o(0,0,0,1,[{offset:0,color:e("#29def5",.3)},{offset:1,color:e("#29def5",.1)}],!1),shadowColor:e("#29def5",.1),shadowBlur:10}},data:[]}]}}{const e=["#d50000","#0091ea","#0090FF","#36CE9E","#FFC005","#FF515A","#8B5CFF","#00CA69"],t=(e,t)=>{let a="";return/^#[\da-f]{6}$/i.test(e)&&(a=`rgba(${parseInt("0x"+e.slice(1,3))},${parseInt("0x"+e.slice(3,5))},${parseInt("0x"+e.slice(5,7))},${t})`),a};C={backgroundColor:"transparent",color:["#0091ea"],legend:{right:10},tooltip:{trigger:"axis"},grid:{top:"30px",bottom:"30px",left:"40px",right:"10px"},xAxis:[{type:"category",boundaryGap:!1,axisLabel:{formatter:"{value}日",color:"#333"},axisLine:{lineStyle:{color:"#D9D9D9"}},data:[]}],yAxis:[{type:"value",axisLabel:{color:"#666"},nameTextStyle:{color:"#666",fontSize:12,lineHeight:40},splitLine:{lineStyle:{type:"dashed",color:"#E9E9E9"}},axisLine:{show:!1},axisTick:{show:!1}}],series:[{name:"日等效小时数",type:"line",smooth:!0,symbolSize:8,zlevel:3,lineStyle:{normal:{color:e[2],shadowBlur:3,shadowColor:t(e[1],.5),shadowOffsetY:8}},areaStyle:{normal:{color:new o(0,0,0,1,[{offset:0,color:t(e[1],.3)},{offset:1,color:t(e[1],.1)}],!1),shadowColor:t(e[1],.1),shadowBlur:10}},data:[]}]}}{const e=(e,t)=>{let a="";return/^#[\da-f]{6}$/i.test(e)&&(a=`rgba(${parseInt("0x"+e.slice(1,3))},${parseInt("0x"+e.slice(3,5))},${parseInt("0x"+e.slice(5,7))},${t})`),a};A={backgroundColor:"transparent",color:["#29def5"],grid:{top:"40px",bottom:"30px",left:"10%",right:"10%"},tooltip:{trigger:"axis",axisPointer:{type:"shadow",label:{show:!0}}},legend:{data:["发电量","收益"],right:"40%",textStyle:{color:"#333"}},xAxis:[{data:[],axisLabel:{formatter:"{value}日",color:"#333"},axisLine:{lineStyle:{color:"#D9D9D9"}}}],yAxis:[{type:"value",name:"发电量 (MWh)",nameTextStyle:{color:"#333"},splitLine:{show:!1},lineStyle:{type:"dashed",color:"#E9E9E9"},axisTick:{show:!1},axisLine:{show:!1},axisLabel:{show:!0,color:"#333"}},{type:"value",name:"收益(万)",nameTextStyle:{color:"#333"},position:"right",splitLine:{show:!1},lineStyle:{type:"dashed",color:"#E9E9E9"},axisTick:{show:!1},axisLine:{show:!1},axisLabel:{show:!0,color:"#333"}}],series:[{name:"发电量",type:"bar",barMixWidth:10,barMaxWidth:20,itemStyle:{normal:{color:"#5087ec"}},data:[]},{name:"收益",type:"line",yAxisIndex:1,smooth:!0,showAllSymbol:!0,symbolSize:10,symbolSize:8,zlevel:3,lineStyle:{normal:{color:"#29def5",shadowBlur:3,shadowColor:e("#29def5",.5),shadowOffsetY:8}},areaStyle:{normal:{color:new o(0,0,0,1,[{offset:0,color:e("#29def5",.3)},{offset:1,color:e("#29def5",.1)}],!1),shadowColor:e("#29def5",.1),shadowBlur:10}},data:[]}]}}{let e=["#0E7CE2","#FF8352","#E271DE","#F8456B","#00FFFF","#4AEAB0"],t=function(e){return e.toString().replace(/(?=(\B)(\d{3})+$)/g,",")};k={backgroundColor:"transparent",color:e,title:[{text:"设备统计",top:"center",left:"center",textStyle:{rich:{name:{fontSize:34,fontWeight:"normal",padding:[10,0]},val:{fontSize:32,fontWeight:"bold"}}}}],series:[{type:"pie",radius:["45%","60%"],center:["50%","50%"],data:[],color:e,hoverAnimation:!1,itemStyle:{normal:{borderColor:"#fff",borderWidth:2}},labelLine:{normal:{length:20,length2:120}},label:{normal:{formatter:e=>"{icon|●}{name|"+e.name+"}{value|"+t(e.value)+"}",padding:[0,-100,15,-100],rich:{icon:{fontSize:16},name:{fontSize:14,padding:[0,10,0,4]},value:{fontSize:16,fontWeight:"bold"}}}}}]}}const I={class:"tw-h-full tw-w-full tw-px-3 tw-py-2 tw-flex tw-flex-col",ref:"mainDom"},$={class:"tw-h-[50px] tw-bg-[#2ba242] tw-rounded-sm tw-flex tw-items-center tw-justify-between"},Y={class:"tw-flex"},B={class:"tw-flex"},M={class:"tw-w-full tw-flex tw-items-center"},P=v("p",{class:"tw-m-0 tw-text-xl tw-mr-[130px]"},"汇总统计",-1),T=v("thead",null,[v("tr",null,[v("th",{class:"text-center"},"总电站数"),v("th",{class:"text-center"},"总装机容量 MWp"),v("th",{class:"text-center"},"总功率 MW"),v("th",{class:"text-center"},"当日发电量 MWh"),v("th",{class:"text-center"},"当日收益 万"),v("th",{class:"text-center"},[j(" CO"),v("sub",{class:"tw-text-xs"},"2"),j("累计减排 wt ")]),v("th",{class:"text-center"},"节约标准煤 wt"),v("th",{class:"text-center"},"等效植树 棵"),v("th",{class:"text-center"},"统计时间")])],-1),W={class:"text-center"},N={class:"text-center"},O={class:"text-center"},R={class:"text-center"},_={class:"text-center"},U={class:"text-center"},q={class:"text-center"},H={class:"text-center"},G={class:"text-center"},V={class:"tw-flex tw-flex-col tw-h-[80%] tw-flex-wrap tw-pt-1"},Q={class:"tw-w-1/3 tw-h-1/2"},X=v("header",{class:"tw-text-lg tw-h-[5%] tw-w-full tw-table"},[v("div",{class:"tw-table-cell tw-w-1/2 tw-align-middle"},"异常率")],-1),Z={class:"tw-h-[90%]",ref:"abnormalRateDom"},J={class:"tw-w-1/3 tw-h-1/2 tw-pt-2"},K=v("header",{class:"tw-text-lg tw-h-[5%] tw-w-full tw-table"},[v("div",{class:"tw-table-cell tw-w-1/2 tw-align-middle"}," 等效时间 ")],-1),ee={class:"tw-h-[90%]",ref:"dayDom"},te={class:"tw-w-1/3 tw-h-full",ref:"totalDom"},ae={class:"tw-h-[80%]",ref:"totalDom"},oe={class:"tw-w-1/3 tw-h-1/2"},le=v("header",{class:"tw-text-lg tw-h-[5%] tw-w-full tw-table"},[v("div",{class:"tw-table-cell tw-w-1/2 tw-align-middle"}," 发电量 ")],-1),se={class:"tw-h-[90%]",ref:"monthDom"},re={class:"tw-w-1/3 tw-h-1/2 tw-pt-2"},ie=v("header",{class:"tw-text-lg tw-h-[50px] tw-w-full tw-table"},[v("div",{class:"tw-table-cell tw-w-1/2 tw-align-middle"}," 发电量收益 ")],-1),ne={class:"tw-h-[90%]",ref:"dayEarnDom"},ce={__name:"plantNormalStatisticsc",setup(o){const ce=p({projectId:void 0,itemList:[],electricityPrice:.45,abnormalRate:"",month:"",day:"",earn:""});let de=w({});const me={createDateDom:l("createDateDom"),dayDom:l("dayDom"),monthDom:l("monthDom"),abnormalRateDom:l("abnormalRateDom"),dayEarnDom:l("dayEarnDom"),totalDom:l("totalDom"),mainDom:l("mainDom")},pe=p({searchLoading:!1,resetLoading:!1,totalLoading:!1});async function we(){}async function ue(){pe.resetLoading=!0,pe.totalLoading=!0,me.createDateDom.value.resetDate([c("2018-01-01").valueOf(),c().valueOf()]);const e=me.createDateDom.value.date,t=e&&e.map((e=>c(e).format("YYYY-MM")));Promise.all([xe(e),he(e),he(t)]).then((e=>{De(e[0].data),de.value=e[0].data||{},ye(e[1]),be(e[1]),je(e[1]),ge(e[2]),pe.resetLoading=!1,pe.totalLoading=!1})).catch((e=>{throw pe.resetLoading=!1,pe.totalLoading=!1,d.error("查询数据失败"),e}))}w(!0);const xe=async e=>{try{const t=await r(ce.projectId,ce.electricityPrice,...e);return n(t)}catch(t){return t}},he=async e=>{try{const t=await i(ce.projectId,ce.electricityPrice,...e);return n(t)}catch(t){return t}};async function fe(){pe.searchLoading=!0,pe.totalLoading=!0;const e=me.createDateDom.value.date,t=e&&e.map((e=>c(e).format("YYYY-MM")));Promise.all([xe(e),he(e),he(t)]).then((e=>{De(e[0].data),de.value=e[0].data||{},ye(e[1]),be(e[1]),je(e[1]),ge(e[2]),pe.searchLoading=!1,pe.totalLoading=!1})).catch((e=>{throw pe.searchLoading=!1,pe.totalLoading=!1,d.error("查询数据失败"),e}))}const ve=async e=>{const t=e&&e.map((e=>c(e).format("YYYY-MM")));integrativeStatisticChartApi(ce.projectId,ce.electricityPrice,...e).then((e=>{const t=n(e);ye(t),be(t),je(t)})).catch((e=>(d.error("获取图表数据失败"),e))),i(ce.projectId,ce.electricityPrice,...t).then((e=>{const t=n(e);ge(t)})).catch((e=>(d.error("获取图表数据失败"),e))),r(ce.projectId,ce.electricityPrice,...me.createDateDom.value.date).then((e=>{const t=n(e);De(t.data),de.value=t.data||{}})).catch((e=>{d.error("获取图表数据失败")}))},ye=async(e=[])=>{const t=E;t.xAxis[0].data=e.data.map((e=>e.collectDate)).reverse()||[],t.series[0].data=e.data.map((e=>e.abnormalPlantNum)).reverse()||[],t.series[1].data=e.data.map((e=>e.normalPlantNum)).reverse()||[],await m(me.abnormalRateDom,t)},ge=async(e=[])=>{const t=z;t.xAxis[0].data=e.data.map((e=>e.collectDate)).reverse()||[],t.series[1].data=e.data.map((e=>(1*e.income/1e4).toFixed(2))).reverse()||[],t.series[0].data=e.data.map((e=>(1*e.electricity/1e3).toFixed(2))).reverse()||[],await m(me.monthDom,t)},be=async(e=[])=>{const t=C;t.xAxis[0].data=e.data.map((e=>e.collectDate)).reverse()||[],t.series[0].data=e.data.map((e=>e.dailyEfficiencyPerHour)).reverse()||[],await m(me.dayDom,t)},je=async(e=[])=>{const t=A;t.xAxis[0].data=e.data.map((e=>e.collectDate)).reverse()||[],t.series[1].data=e.data.map((e=>(1*e.income/1e4).toFixed(2))).reverse()||[],t.series[0].data=e.data.map((e=>(1*e.electricity/1e3).toFixed(2))).reverse()||[],await m(me.dayEarnDom,t)},De=async(e=[])=>{const t=k;let{normalDeviceNum:a,alarmDeviceNum:o,offlineDeviceNum:l}=e,s=["#0E7CE2","#FF8352","#E271DE","#F8456B","#00FFFF","#4AEAB0"];t.series[0].data=[{label:{color:s[0]},name:"正常",value:a},{label:{color:s[1]},name:"告警",value:o},{label:{color:s[2]},name:"离线",value:l}],await m(me.totalDom,t)};return u((async()=>{await async function(){const e=await s();!function e(t){var a;for(const o of t)(null==(a=null==o?void 0:o.children)?void 0:a.length)?e(o.children):delete o.children}(n(e).data),ce.itemList=n(e).data,ce.projectId=ce.itemList[0].projectId}(),ve(["2018-01-01",c().format("YYYY-MM-DD")])})),(o,l)=>{const s=L,r=F,i=e,n=a,c=t,d=x("skeleton-item");return h(),f("div",I,[v("div",$,[v("div",Y,[y(s,{class:"tw-w-[300px] tw-mx-2",options:g(ce).itemList,"key-field":"id","label-field":"projectName",value:g(ce).projectId,"onUpdate:value":l[0]||(l[0]=e=>g(ce).projectId=e),placeholder:"项目名称"},null,8,["options","value"]),y(r,{class:"tw-w-[300px] tw-mx-2",step:"0.01",value:g(ce).electricityPrice,"onUpdate:value":l[1]||(l[1]=e=>g(ce).electricityPrice=e)},{prefix:b((()=>[j(" 电价: ")])),suffix:b((()=>[j(" ￥ ")])),_:1},8,["value"])]),v("div",B,[D(y(i,{class:"tw-bg-yellow-500 tw-text-white tw-w-[80px]",loading:g(pe).resetLoading,label:"重置","no-caps":"",onClick:ue},null,8,["loading"]),[[d]]),D(y(i,{class:"tw-bg-teal-500 tw-text-white tw-w-[80px] tw-mx-2",label:"查询","no-caps":"",onClick:fe,loading:g(pe).searchLoading},null,8,["loading"]),[[d]]),D(y(i,{class:"tw-bg-green-500 tw-text-white tw-w-[80px] tw-mr-2",label:"下载","no-caps":"",onClick:we},null,512),[[d]])])]),v("div",null,[v("div",M,[P,y(n,{ref:"createDateDom",disabled:g(pe).totalLoading,size:"small",tabs:"日",init:["2018-01-01",Date.now()],onUpdateDate:ve},null,8,["disabled","init"])]),y(c,{separator:"cell",class:"tw-rounded-none"},{default:b((()=>{var e,t,a,o;return[T,v("tbody",null,[v("tr",null,[v("td",W,S(g(de).plantNum||"0"),1),v("td",N,S((((null==(e=g(de))?void 0:e.plantCapacity)||0)/1e3/1e3).toFixed(3)||"0"),1),v("td",O,S((((null==(t=g(de))?void 0:t.power)||0)/1e3/1e3).toFixed(3)||"0"),1),v("td",R,S((((null==(a=g(de))?void 0:a.electricity)||0)/1e3/100).toFixed(3)||"0"),1),v("td",_,S((((null==(o=g(de))?void 0:o.income)||0)/1e4).toFixed(3)||"0"),1),v("td",U,S(g(de).reduceCo2||"0"),1),v("td",q,S(g(de).reduceCoal||"0"),1),v("td",H,S(g(de).treeNum||"0"),1),v("td",G,S(g(de).statisticalTime||"-"),1)])])]})),_:1})]),v("section",V,[v("div",Q,[X,v("figure",Z,null,512)]),v("div",J,[K,v("figure",ee,null,512)]),v("div",te,[v("figure",ae,null,512)],512),v("div",oe,[le,v("figure",se,null,512)]),v("div",re,[ie,v("figure",ne,null,512)])])],512)}}};export{ce as default};
