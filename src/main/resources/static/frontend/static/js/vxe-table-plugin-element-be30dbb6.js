import{h as e}from"./@babel-f3c0a00c.js";import{r as t}from"./vue-5bfa3a54.js";import{x as n}from"./xe-utils-fe99d42a.js";import{r}from"./dayjs-67f8ddef.js";var o={};!function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.VXETablePluginElement=void 0;var o=t,a=u(n),l=u(r());function u(e){return e&&e.__esModule?e:{default:e}}function i(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?i(Object(n),!0).forEach((function(t){f(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):i(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function f(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function c(e){return null==e||""===e}function p(e){return"on"+e.substring(0,1).toLocaleUpperCase()+e.substring(1)}function s(e,t){return(0,l.default)(e).format(t)}function v(e,t){return e&&t.valueFormat?function(e,t){return(0,l.default)(e,t).date}(e,t.valueFormat):e}function m(e,t,n){return e?s(v(e,t),t.format||n):e}function b(e,t,n,r){return a.default.map(e,(function(e){return m(e,t,r)})).join(n)}function h(e,t,n,r){return(e=m(e,n,r))>=m(t[0],n,r)&&e<=m(t[1],n,r)}function g(e,t,n,r){return a.default.assign({},r,e.props,f({},"modelValue",n))}function w(e,t,n,r){return a.default.assign({},r,e.props,f({},"modelValue",n))}function Y(e){return""+(c(e)?"":e)}function C(e,t,n){var r=e.placeholder;return[(0,o.h)("span",{class:"vxe-cell--label"},r&&c(n)?[(0,o.h)("span",{class:"vxe-cell--placeholder"},Y(undefined._t(r)))]:Y(n))]}function E(e,t,n,r){var o=e.events,l="update:modelValue",u=function(e){var t="change";switch(e.name){case"ElAutocomplete":t="select";break;case"ElInput":case"ElInputNumber":t="input"}return t}(e),i=u===l,d={};return a.default.objectEach(o,(function(e,n){d[p(n)]=function(){for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];e.apply(void 0,[t].concat(r))}})),n&&(d[p(l)]=function(e){n(e),o&&o[l]&&o[l](t,e),i&&r&&r(e)}),!i&&r&&(d[p(u)]=function(){for(var e=arguments.length,n=new Array(e),a=0;a<e;a++)n[a]=arguments[a];r.apply(void 0,n),o&&o[u]&&o[u].apply(o,[t].concat(n))}),d}function y(e,t){var n=t.$table,r=t.row,o=t.column;return E(e,t,(function(e){a.default.set(r,o.field,e)}),(function(){n.updateStatus(t)}))}function M(e,t,n,r){return E(e,t,(function(e){n.data=e}),r)}function D(e,t){var n=t.$form,r=t.data,o=t.field;return E(e,t,(function(e){a.default.set(r,o,e)}),(function(){n.updateStatus(t)}))}function k(e,t,n,r){var o=n[e];t&&n.length>e&&a.default.each(t,(function(t){t.value===o&&(r.push(t.label),k(++e,t.children,n,r))}))}function j(e,t){var n,r=e.options,o=void 0===r?[]:r,l=e.optionGroups,u=e.props,i=void 0===u?{}:u,d=e.optionProps,f=void 0===d?{}:d,p=e.optionGroupProps,s=void 0===p?{}:p,v=t.$table,m=t.rowid,b=t.row,h=t.column,g=i.filterable,w=i.multiple,Y=f.label||"label",C=f.value||"value",E=s.options||"options",y=a.default.get(b,h.field),M=h.id;if(g){var D=v.internalData.fullAllDataRowIdData[m];if(D&&((n=D.cellData)||(n=D.cellData={})),D&&n[M]&&n[M].value===y)return n[M].label}if(!c(y)){var k=a.default.map(w?y:[y],l?function(e){for(var t,n=0;n<l.length&&!(t=a.default.find(l[n][E],(function(t){return t[C]===e})));n++);return t?t[Y]:e}:function(e){var t=a.default.find(o,(function(t){return t[C]===e}));return t?t[Y]:e}).join(", ");return n&&o&&o.length&&(n[M]={value:y,label:k}),k}return""}function O(e,t){var n=e.props,r=void 0===n?{}:n,o=t.row,l=t.column,u=a.default.get(o,l.field)||[],i=[];return k(0,r.options,u,i),(!1===r.showAllLevels?i.slice(i.length-1,i.length):i).join(" ".concat(r.separator||"/"," "))}function P(e,t){var n=e.props,r=void 0===n?{}:n,o=t.row,l=t.column,u=r.rangeSeparator,i=void 0===u?"-":u,d=a.default.get(o,l.field);switch(r.type){case"week":d=m(d,r,"YYYYwWW");break;case"month":d=m(d,r,"YYYY-MM");break;case"year":d=m(d,r,"YYYY");break;case"dates":d=b(d,r,", ","YYYY-MM-DD");break;case"daterange":d=b(d,r," ".concat(i," "),"YYYY-MM-DD");break;case"datetimerange":d=b(d,r," ".concat(i," "),"YYYY-MM-DD HH:ss:mm");break;case"monthrange":d=b(d,r," ".concat(i," "),"YYYY-MM");break;default:d=m(d,r,"YYYY-MM-DD")}return d}function I(e,t){var n=e.props,r=void 0===n?{}:n,o=t.row,l=t.column,u=r.isRange,i=r.format,d=void 0===i?"hh:mm:ss":i,f=r.rangeSeparator,c=void 0===f?"-":f,p=a.default.get(o,l.field);return p&&u&&(p=a.default.map(p,(function(e){return s(v(e,r),d)})).join(" ".concat(c," "))),s(v(p,r),d)}function F(e){return function(t,n){var r=n.row,l=n.column,u=t.name,i=t.attrs,f=a.default.get(r,l.field);return[(0,o.h)((0,o.resolveComponent)(u),d(d(d({},i),g(t,0,f,e)),y(t,n)))]}}function x(e,t){var n=e.attrs;return[(0,o.h)((0,o.resolveComponent)("el-button"),d(d(d({},n),g(e,0,null)),E(e,t)),R(e.content))]}function _(e,t){var n=e.children;return n?n.map((function(e){return x(e,t)[0]})):[]}function S(e){return function(t,n){var r=n.column,a=t.name,l=t.attrs;return[(0,o.h)("div",{class:"vxe-table--filter-element-wrapper"},r.filters.map((function(r,u){var i=r.data;return(0,o.h)((0,o.resolveComponent)(a),d(d(d({key:u},l),g(t,0,i,e)),M(t,n,r,(function(){A(n,!!r.data,r)}))))})))]}}function A(e,t,n){e.$panel.changeOption(null,t,n)}function V(e){var t=e.option,n=e.row,r=e.column,o=t.data,l=a.default.get(n,r.field);return a.default.toValueString(l).indexOf(o)>-1}function G(e){var t=e.option,n=e.row,r=e.column,o=t.data;return a.default.get(n,r.field)===o}function N(e,t){var n=t.label||"label",r=t.value||"value";return a.default.map(e,(function(e,t){return(0,o.h)((0,o.resolveComponent)("el-option"),{key:t,value:e[r],label:e[n],disabled:e.disabled})}))}function R(e){return[Y(e)]}function T(e){return function(t,n){var r=n.data,l=n.field,u=t.name,i=t.attrs,f=a.default.get(r,l);return[(0,o.h)((0,o.resolveComponent)(u),d(d(d({},i),w(t,0,f,e)),D(t,n)))]}}function X(e,t){var n=e.attrs,r=w(e,0,null);return[(0,o.h)((0,o.resolveComponent)("el-button"),d(d(d({},n),r),E(e,t)),{default:function(){return R(e.content||r.content)}})]}function $(e,t){var n=e.children;return n?n.map((function(e){return X(e,t)[0]})):[]}function H(e){return function(t){var n=t.row,r=t.column,o=t.options;return o&&o.original?a.default.get(n,r.field):e(r.editRender||r.cellRender,t)}}function B(){return function(e,t){var n=e.name,r=e.options,l=void 0===r?[]:r,u=e.optionProps,i=void 0===u?{}:u,f=e.attrs,c=t.data,p=t.field,s=i.label||"label",v=i.value||"value",m=a.default.get(c,p);return[(0,o.h)((0,o.resolveComponent)("".concat(n,"Group")),d(d(d({},f),w(e,0,m)),D(e,t)),{default:function(){return l.map((function(e,t){return(0,o.h)((0,o.resolveComponent)(n),{key:t,label:e[v],disabled:e.disabled},{default:function(){return R(e[s])}})}))}})]}}function q(e,t,n){for(var r,o=e.target;o&&o.nodeType&&o!==document;){if(n&&o.className&&o.className.split&&o.className.split(" ").indexOf(n)>-1)r=o;else if(o===t)return{flag:!n||!!r,container:t,targetElem:r};o=o.parentNode}return{flag:!1}}function L(e){var t=e.$event,n=document.body;if(q(t,n,"el-autocomplete-suggestion").flag||q(t,n,"el-select-dropdown").flag||q(t,n,"el-cascader__dropdown").flag||q(t,n,"el-cascader-menus").flag||q(t,n,"el-time-panel").flag||q(t,n,"el-picker-panel").flag||q(t,n,"el-color-dropdown").flag)return!1}var W={install:function(e){!/^(4)\./.test(e.version)&&/v4/i.test(e.v),e.renderer.mixin({ElAutocomplete:{autofocus:"input.el-input__inner",renderDefault:F(),renderEdit:F(),renderFilter:S(),defaultFilterMethod:G,renderItemContent:T()},ElInput:{autofocus:"input.el-input__inner",renderDefault:F(),renderEdit:F(),renderFilter:S(),defaultFilterMethod:V,renderItemContent:T()},ElInputNumber:{autofocus:"input.el-input__inner",renderDefault:F(),renderEdit:F(),renderFilter:S(),defaultFilterMethod:V,renderItemContent:T()},ElSelect:{renderEdit:function(e,t){var n=e.options,r=void 0===n?[]:n,l=e.optionGroups,u=e.optionProps,i=void 0===u?{}:u,f=e.optionGroupProps,c=void 0===f?{}:f,p=t.row,s=t.column,v=e.attrs,m=g(e,0,a.default.get(p,s.field)),b=y(e,t);if(l){var h=c.options||"options",w=c.label||"label";return[(0,o.h)((0,o.resolveComponent)("el-select"),d(d(d({},v),m),b),{default:function(){return a.default.map(l,(function(e,t){return(0,o.h)((0,o.resolveComponent)("el-option-group"),{key:t,label:e[w]},{default:function(){return N(e[h],i)}})}))}})]}return[(0,o.h)((0,o.resolveComponent)("el-select"),d(d(d({},m),v),b),{default:function(){return N(r,i)}})]},renderCell:function(e,t){return C(e,0,j(e,t))},renderFilter:function(e,t){var n=e.options,r=void 0===n?[]:n,l=e.optionGroups,u=e.optionProps,i=void 0===u?{}:u,f=e.optionGroupProps,c=void 0===f?{}:f,p=c.options||"options",s=c.label||"label",v=t.column,m=e.attrs;return[(0,o.h)("div",{class:"vxe-table--filter-element-wrapper"},l?v.filters.map((function(n,r){var u=n.data,f=g(e,0,u);return(0,o.h)((0,o.resolveComponent)("el-select"),d(d(d({key:r},m),f),M(e,t,n,(function(){A(t,f.multiple?n.data&&n.data.length>0:!a.default.eqNull(n.data),n)}))),{default:function(){return a.default.map(l,(function(e,t){return(0,o.h)((0,o.resolveComponent)("el-option-group"),{key:t,label:e[s]},{default:function(){return N(e[p],i)}})}))}})})):v.filters.map((function(n,l){var u=n.data,f=g(e,0,u);return(0,o.h)((0,o.resolveComponent)("el-select"),d(d(d({key:l},m),f),M(e,t,n,(function(){A(t,f.multiple?n.data&&n.data.length>0:!a.default.eqNull(n.data),n)}))),{default:function(){return N(r,i)}})})))]},defaultFilterMethod:function(e){var t=e.option,n=e.row,r=e.column,o=t.data,l=r.field,u=r.filterRender.props,i=void 0===u?{}:u,d=a.default.get(n,l);return i.multiple?a.default.isArray(d)?a.default.includeArrays(d,o):o.indexOf(d)>-1:d==o},renderItemContent:function(e,t){var n=e.options,r=void 0===n?[]:n,l=e.optionGroups,u=e.optionProps,i=void 0===u?{}:u,f=e.optionGroupProps,c=void 0===f?{}:f,p=t.data,s=t.field,v=e.attrs,m=w(e,0,a.default.get(p,s)),b=D(e,t);if(l){var h=c.options||"options",g=c.label||"label";return[(0,o.h)((0,o.resolveComponent)("el-select"),d(d(d({},v),m),b),{default:function(){return a.default.map(l,(function(e,t){return(0,o.h)((0,o.resolveComponent)("el-option-group"),{label:e[g],key:t},{default:function(){return N(e[h],i)}})}))}})]}return[(0,o.h)((0,o.resolveComponent)("el-select"),d(d(d({},v),m),b),{default:function(){return N(r,i)}})]},exportMethod:H(j)},ElCascader:{renderEdit:F(),renderCell:function(e,t){return C(e,0,O(e,t))},renderItemContent:T(),exportMethod:H(O)},ElDatePicker:{renderEdit:F(),renderCell:function(e,t){return C(e,0,P(e,t))},renderFilter:function(e,t){var n=t.column,r=e.name,a=e.attrs;return[(0,o.h)("div",{class:"vxe-table--filter-element-wrapper"},n.filters.map((function(n,l){var u=n.data;return(0,o.h)((0,o.resolveComponent)(r),d(d(d({key:l},a),g(e,0,u)),M(e,t,n,(function(){A(t,!!n.data,n)}))))})))]},defaultFilterMethod:function(e){var t=e.option,n=e.row,r=e.column,o=t.data,l=r.filterRender.props,u=void 0===l?{}:l,i=a.default.get(n,r.field);if(o)switch(u.type){case"daterange":return h(i,o,u,"YYYY-MM-DD");case"datetimerange":return h(i,o,u,"YYYY-MM-DD HH:ss:mm");case"monthrange":return h(i,o,u,"YYYY-MM");default:return i===o}return!1},renderItemContent:T(),exportMethod:H(P)},ElTimePicker:{renderEdit:F(),renderCell:function(e,t){return C(e,0,I(e,t))},renderItemContent:T(),exportMethod:H(I)},ElTimeSelect:{renderEdit:F(),renderItemContent:T()},ElRate:{renderDefault:F(),renderEdit:F(),renderFilter:S(),defaultFilterMethod:G,renderItemContent:T()},ElSwitch:{renderDefault:F(),renderEdit:F(),renderFilter:function(e,t){var n=t.column,r=e.name,l=e.attrs;return[(0,o.h)("div",{class:"vxe-table--filter-element-wrapper"},n.filters.map((function(n,u){var i=n.data;return(0,o.h)((0,o.resolveComponent)(r),d(d(d({key:u},l),g(e,0,i)),M(e,t,n,(function(){A(t,a.default.isBoolean(n.data),n)}))))})))]},defaultFilterMethod:G,renderItemContent:T()},ElSlider:{renderDefault:F(),renderEdit:F(),renderFilter:S(),defaultFilterMethod:G,renderItemContent:T()},ElRadio:{renderItemContent:B()},ElCheckbox:{renderItemContent:B()},ElButton:{renderDefault:x,renderItemContent:X},ElButtons:{renderDefault:_,renderItemContent:$}}),e.interceptor.add("event.clearFilter",L),e.interceptor.add("event.clearActived",L),e.interceptor.add("event.clearAreas",L)}};e.VXETablePluginElement=W,"undefined"!=typeof window&&window.VXETable&&window.VXETable.use&&window.VXETable.use(W);var U=W;e.default=U}(o);const a=e(o);export{a as V};
