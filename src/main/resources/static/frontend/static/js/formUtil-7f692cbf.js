import{x as t}from"./menuStore-30bf76d3.js";import{g as e}from"./api-360ec627.js";import{l as r}from"./lodash-6d99edc3.js";import{m as a}from"./notification-950a5f80.js";import{m as o}from"./index-a5df0f75.js";import"./vue-5bfa3a54.js";import{b as s,e as l}from"./naive-ui-0ee0b8c3.js";import{A as i}from"./@vue-5e5cdef9.js";class n{constructor(t){this.formList=t}static getValue(t){return t.reduce(((t,e)=>(e.prop&&(t[e.prop]=e.value),t)),{})}static setValue(t,e,r){t.find((t=>t.prop==e)).value=r}static async tableResponse(o,s,l,i,u,c,p){var f,d;const v=r._.find(i,{prop:u});if(v.value=!0,l.history=p,"check"==(null==c?void 0:c.prop))return void(l.page=1);let m,y;try{m=o,y=e(m),"00204"==y.status?(s.value=[],l.total=0,a.info(y.message)):(s.value=(null==(f=y.data)?void 0:f.list)||(null==(d=y.data)?void 0:d.records),l.total=y.data.total)}catch(h){const e=t();a.error(e.value+((null==m?void 0:m.message)??"请求中断")),l.history.undo()}finally{n.setValue(i,u,!1),v.value=!1,v.type="info"}return y}static async tableResponse(o,s,l,i,u,c,p){var f,d;const v=r._.find(i,{prop:u});if(v.value=!0,l.history=p,"check"==(null==c?void 0:c.prop))return void(l.page=1);let m,y;try{m=o,y=e(m),"00204"==y.status?(s.value=[],l.total=0,a.info(y.message)):(s.value=(null==(f=y.data)?void 0:f.list)||(null==(d=y.data)?void 0:d.records)||y.data,l.total=y.data.total)}catch(h){const e=t();a.error(e.value+((null==m?void 0:m.message)??"请求中断")),l.history.undo()}finally{n.setValue(i,u,!1),v.value=!1,v.type="info"}return y}static async exportFile(t,o,s){const l=r._.find(o,{prop:s});let i;l.value=!0;try{i=t}catch(u){a.error("导出失败!!")}finally{n.setValue(o,s,!1),l.value=!1}return e(i)}static async reset(t,e,a){t.forEach((t=>{const a=e[t.prop];t&&!r._.isUndefined(a)&&(t.value=a)}));r._.find(t,{prop:"reset"}).invoke(),a.value=!1}static getQuery(){return o(location.hash)}static isObjEqual(t,e){return u(t,e)}static createColumns(t,e,r){const a={"电站容量":"tw-w-[100px] tw-mx-1","地址":"tw-w-[300px] tw-mx-1"};return Object.entries(t).map((([t,r])=>({title:r,key:t,render:(o,n)=>"projectId"!=t?i(s,{value:o[t],class:a[r],onUpdateValue(e){o[t]=e}}):i(l,{value:o[t],options:e.excelForm.options,class:"tw-w-[200px] tw-mx-1",keyField:"id",labelField:"projectName",onUpdateValue(e){o[t]=e}})})))}}function u(t,e){const r=t=>null!==t&&"object"==typeof t&&!Array.isArray(t);if(!r(t)||!r(e))return t===e;const a=Object.keys(t),o=Object.keys(e);if(a.length!==o.length)return!1;for(const s of a){const a=t[s],o=e[s];if(r(a)&&r(o)){if(!u(a,o))return!1}else if(Array.isArray(a)&&Array.isArray(o)){if(!c(a,o))return!1}else if(a!==o)return!1}return!0}function c(t,e){if(t.length!==e.length)return!1;const r=t.slice().sort(),a=e.slice().sort();for(let o=0;o<r.length;o++)if(!u(r[o],a[o]))return!1;return!0}export{n as f};
