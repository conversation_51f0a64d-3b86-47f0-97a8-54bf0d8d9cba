var t=null,e="z-index-manage",n={m:1e3,s:1e3};function r(){return t||"undefined"!=typeof document&&((t=document.getElementById(e))||((t=document.createElement("div")).id=e,t.style.display="none",document.body.appendChild(t),a(n.m),m(n.s))),t}function u(t){return function(e){if(e){e=Number(e),n[t]=e;var u=r();u&&(u.dataset?u.dataset[t]=e+"":u.setAttribute("data-"+t,e+""))}return n[t]}}var a=u("m");function d(t,e){return function(u){var a,d=r();if(d){var i=d.dataset?d.dataset[t]:d.getAttribute("data-"+t);i&&(a=Number(i))}return a||(a=n[t]),u?Number(u)<a?e():u:a}}var i=d("m",o);function o(){return a(i()+1)}var m=u("s"),s=d("s",f);function c(){return i()+s()}function f(){return m(s()+1),c()}var b={setCurrent:a,getCurrent:i,getNext:o,setSubCurrent:m,getSubCurrent:c,getSubNext:f};export{b as D};
