import"./vue-5bfa3a54.js";import{_ as t}from"./sun-9a09fd29.js";import{x as e,s}from"./homeStore-7900023d.js";import{r as i}from"./icons-95011f8c.js";import l from"./tableWrapper-2dedb322.js";import"./echarts-f30da64f.js";import{V as o}from"./zrender-c058db04.js";import{G as a,H as r}from"./@vueuse-5227c686.js";import{e as n}from"./echartsInit-2e16a3ff.js";import{s as p}from"./chartXY-a0399c4a.js";import{_ as c}from"./index-a5df0f75.js";import{r as m}from"./naive-ui-0ee0b8c3.js";import{h as w,e as f,m as d,o as x,c as u,a as h,t as j,b,aa as y,x as g,f as v,r as S,C as W,D as k}from"./@vue-5e5cdef9.js";import"./@babel-f3c0a00c.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./homeApi-030fb9ef.js";import"./index-092b8780.js";import"./api-360ec627.js";import"./menuStore-30bf76d3.js";import"./vue-router-6159329f.js";import"./dayjs-67f8ddef.js";import"./lodash-6d99edc3.js";import"./@vicons-f32a0bdb.js";import"./notification-950a5f80.js";import"./axios-84f1a956.js";import"./quasar-df1bac18.js";import"./taskUitls-36951a34.js";import"./element-plus-95e0b914.js";import"./lodash-es-ea7deab5.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./swiper-7f939876.js";import"./startEndSlide-8c1800d1.js";import"./tslib-a4e99503.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./xe-utils-fe99d42a.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-be30dbb6.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./date-fns-tz-0cc073c8.js";const I=e(),N={backgroundColor:"#3616a7",color:["#F8B62B","#EA5514"],legend:{icon:"square",top:"15%",right:"5%",textStyle:{color:"#fff"},itemWidth:10,itemHeight:10,data:["发电量"]},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(t){var e=t[0].value;return t[0].name+"<br />"+e+(I.plantInfo.monthElectricityThousandUnit?"kWh":"MWh")}},grid:{top:"10%",left:"8%",right:"0%",bottom:"13%"},xAxis:{type:"category",axisLine:{lineStyle:{type:"solid",color:"#2d4b83",width:"0.5"}},axisLabel:{interval:0,rotate:0,color:"#b8b8b8"}},yAxis:[{nameTextStyle:{fontSize:14,color:"#fff"},type:"value",axisLine:{lineStyle:{show:!1,type:"solid",color:"transparent",width:"0.5"}},axisLabel:{color:"#b8b8b8"},splitLine:{lineStyle:{color:["#2d4b83"],width:.5,type:"solid"}}}],series:[{type:"bar",barWidth:"35%",barMaxWidth:50,barGap:"10%",label:{show:!0,position:"top",distance:5,formatter(t,e){let s="";return s=t.dataIndex<3?"{a|} ":"{b|} ",s},rich:{a:{align:"center",padding:[0,0,2,0],width:15,height:15,color:"#fff"},b:{align:"center",padding:[0,0,2,0],width:15,height:15,color:"#fff"}}},itemStyle:{normal:{color:new o(0,0,0,1,[{offset:0,color:"#F8B62B"},{offset:1,color:"#EA5514"}],!1),shadowColor:"rgba(0,255,225,1)",shadowBlur:3}},data:[]}]},A={backgroundColor:"#3616a7",color:["#F8B62B","#EA5514"],legend:{icon:"square",top:"15%",right:"5%",textStyle:{color:"#fff"},itemWidth:10,itemHeight:10,data:["发电量"]},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(t){var e=t[0].value;return t[0].name+"月<br />"+e+" MWh"}},grid:{top:"10%",left:"10%",right:"0%",bottom:"13%"},xAxis:{type:"category",axisLine:{lineStyle:{type:"solid",color:"#2d4b83",width:"0.5"}},axisLabel:{formatter:"{value}月",interval:0,rotate:0,color:"#b8b8b8"}},yAxis:[{nameTextStyle:{fontSize:14,color:"#fff"},type:"value",axisLine:{lineStyle:{show:!1,type:"solid",color:"transparent",width:"0.5"}},axisLabel:{color:"#b8b8b8"},splitLine:{lineStyle:{color:["#2d4b83"],width:.5,type:"solid"}}}],series:[{type:"bar",barWidth:"35%",barMaxWidth:20,barGap:"10%",label:{show:!0,position:"top",distance:5,formatter(t,e){let s="";return s=t.dataIndex<3?"{a|} ":"{b|} ",s},rich:{a:{align:"center",padding:[0,0,2,0],width:15,height:15,color:"#fff"},b:{align:"center",padding:[0,0,2,0],width:15,height:15,color:"#fff"}}},itemStyle:{normal:{color:new o(0,0,0,1,[{offset:0,color:"#F8B62B"},{offset:1,color:"#EA5514"}],!1),shadowColor:"rgba(0,255,225,1)",shadowBlur:3}},data:[]}]},B=t=>(W("data-v-83837601"),t=t(),k(),t),E={class:"screenfull-content tw-h-full tw-w-full tw-bg-[#3616a7] tw-p-[30px] tw-flex"},M={class:"tw-w-[439px] tw-mr-[45px] tw-h-full"},H={class:"tw-flex tw-flex-col tw-w-full tw-h-full"},T={class:"tw-rounded-lg bg tw-h-[180px]"},C=B((()=>h("article",{class:"tw-text-white tw-text-[20px] tw-h-[30%] tw-font-semibold tw-flex tw-items-center tw-justify-center",style:{"font-family":"SourceHanSansCN-Bold"}}," 博 通 光 云 智 能 监 控 中 心 ",-1))),D={class:"tw-text-white tw-text-[50px] tw-font-bold tw-h-[40%] tw-flex tw-items-center tw-justify-center",style:{"font-family":"SourceHanSansCN-Heavy"}},L={class:"small-title tw-text-[rgba(115, 212, 112, 1)] tw-h-[30%] tw-flex tw-items-center tw-justify-center",style:{"font-family":"SourceHanSansCN-Medium"}},z={class:"tw-flex-1 tw-mt-[20px] tw-flex tw-flex-col"},_={class:"tw-flex tw-justify-between"},P=B((()=>h("article",{class:"tw-text-[21px] tw-text-white"},[h("span",null,[h("img",{class:"tw-align-middle",src:t})]),y(" 总发电量 ")],-1))),q={class:"tw-text-[rgba(115, 212, 112, 1)] tw-text-[28px] tw-font-bold",style:{"font-style":"Alibaba-PuHuiTi-B"}},U={class:"tw-text-[#9f90d5] tw-text-[18px]"},Y={class:"tw-flex tw-justify-between tw-my-[40px]"},F=B((()=>h("article",{class:"tw-text-[21px] tw-text-white"},[h("img",{class:"tw-align-middle",src:t}),y(" 月发电量 ")],-1))),G={class:"tw-text-[rgba(115, 212, 112, 1)] tw-text-[28px] tw-font-bold",style:{"font-style":"Alibaba-PuHuiTi-B"}},O={class:"tw-text-[#9f90d5] tw-text-[18px]"},R={class:"tw-flex tw-justify-between"},V=B((()=>h("article",{class:"tw-text-[21px] tw-text-white"},[h("span",null,[h("img",{class:"tw-align-middle",src:t})]),y(" 日发电量 ")],-1))),Q={class:"tw-text-[rgba(115, 212, 112, 1)] tw-text-[28px] tw-font-bold",style:{"font-style":"Alibaba-PuHuiTi-B"}},X={class:"tw-text-[#9f90d5] tw-text-[18px]"},Z={class:"tw-flex-1 tw-mt-[10px] tw-flex tw-flex-col"},$={class:"tw-text-white tw-text-[15px]"},J=B((()=>h("span",{class:"tw-text-[18px]"},"近7日发电量统计 ",-1))),K={class:"tw-text-[#9f90d5] tw-text-[8px]"},tt={class:"tw-flex-1 tw-mt-[10px] tw-flex tw-flex-col"},et=B((()=>h("article",{class:"tw-text-white tw-text-[15px]"},[h("span",{class:"tw-text-[18px]"},"近6月发电量统计 "),h("span",{class:"tw-text-[#9f90d5] tw-text-[8px]"},"MWh")],-1))),st={class:"tw-flex-1 tw-flex tw-flex-col"},it={class:"tw-h-[18%] tw-flex"},lt={class:"tw-p-[20px] tw-w-[40%] u-flex-column justify-between"},ot=B((()=>h("p",{class:"small-title tw-text-white"}," 正常电站 / 告警电站 / 离线电站 / 总数 ",-1))),at={class:"small-title tw-text-[rgba(115, 212, 112, 1)] tw-font-bold"},rt={class:"tw-p-[20px] tw-w-[40%] u-flex-column justify-between"},nt=B((()=>h("p",{class:"small-title tw-text-white"}," 正常设备 / 告警设备 / 离线设备 / 总数 ",-1))),pt={class:"small-title tw-text-[rgba(115, 212, 112, 1)] tw-font-bold"},ct={class:"tw-p-[20px] tw-w-[20%] u-flex-column justify-between"},mt={class:"small-title tw-text-white tw-m-1"},wt={class:"small-title tw-text-white tw-m-1"},ft={class:"small-title tw-text-white tw-m-1"},dt={class:"tw-h-[80%] tw-text-white"},xt=c({__name:"carousel",setup(t){const o=a(r(),"HH : mm : ss"),c=a(r(),"YYYY / MM / DD"),W=w(),k=w();let I=null,B=null;const xt=e();return f((()=>xt.deviceNumInfo.normalNum/xt.deviceNumInfo.totalNum*100)),f((()=>xt.plantNumInfo.normalNum/xt.plantNumInfo.totalNum*100)),d((async()=>{await xt.setData(),I=await n(W,N),B=await n(k,A),s((()=>{const t=xt.day.slice(-7);null==I||I.setOption(p(N,t.map((t=>0==t.collectDate.charAt(5)?t.collectDate.slice(6):t.collectDate.slice(5))),t.map((t=>t.electricity))),!0);const e=xt.month.slice(-6);null==B||B.setOption(p(A,e.map((t=>t.collectDate)),e.map((t=>t.electricity))),!0)}),6e4,!0)})),(t,e)=>{const s=m;return x(),u("div",E,[h("div",M,[h("div",H,[h("div",T,[C,h("article",D,j(b(o).split("").join(" ")),1),h("article",L,j(b(c).split("").join(" ")),1)]),h("div",z,[h("div",_,[P,h("article",q,[y(j(b(xt).plantInfo.totalElectricity.split("").join(" "))+" ",1),h("span",U,j(b(xt).plantInfo.totalElectricityThousandUnit?"kWh":"MWh"),1)])]),h("div",Y,[F,h("article",G,[y(j(b(xt).plantInfo.monthElectricity.split("").join(" "))+" ",1),h("span",O,j(b(xt).plantInfo.monthElectricityThousandUnit?"kWh":"MWh"),1)])]),h("div",R,[V,h("article",Q,[y(j(b(xt).plantInfo.todayElectricity.split("").join(" "))+" ",1),h("span",X,j(b(xt).plantInfo.todayElectricityThousandUnit?"kWh":"MWh"),1)])])]),h("div",Z,[h("article",$,[J,h("span",K,j(b(xt).plantInfo.monthElectricityThousandUnit?"kWh":"MWh"),1)]),h("figure",{class:"tw-m-0 tw-flex-1",ref_key:"sevenDaysDom",ref:W},null,512)]),h("div",tt,[et,h("figure",{class:"tw-m-0 tw-w-full tw-flex-1",ref_key:"pastSixMonthsDom",ref:k},null,512)])])]),h("div",st,[h("div",it,[h("div",lt,[ot,h("p",at,j(b(xt).plantNumComputed.split("").join(" ")),1),g(s,{type:"line",percentage:b(xt).normalEquipmentRatio,"indicator-text-color":"#3616a7","show-indicator":!1},null,8,["percentage"])]),h("div",rt,[nt,h("p",pt,j(b(xt).deviceNumComputed.split("").join(" ")),1),g(s,{type:"line",percentage:b(xt).normalPowerPlantProportion,"indicator-text-color":"#3616a7","show-indicator":!1},null,8,["percentage"])]),h("div",ct,[h("p",mt,[(x(),v(S(b(i)(void 0,"https://www.btosolarman.com/assets/btosolar/picture/carouselNormal.png",13,13)))),y(" 正常运行 ")]),h("p",wt,[(x(),v(S(b(i)(void 0,"https://www.btosolarman.com/assets/btosolar/picture/carouselAlarm.svg",16,16)))),y(" 告警运行 ")]),h("p",ft,[(x(),v(S(b(i)(void 0,"https://www.btosolarman.com/assets/btosolar/picture/carouselOffline.png",14,14)))),y(" 离线 ")])])]),h("div",dt,[g(l,{class:"tw-h-full tw-rounded"})])])])}}},[["__scopeId","data-v-83837601"]]);export{xt as default};
