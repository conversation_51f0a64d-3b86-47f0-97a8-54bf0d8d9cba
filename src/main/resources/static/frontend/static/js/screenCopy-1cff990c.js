import{c as t,_ as e,a as s}from"./mapUtils-bebbcec2.js";import{W as l,X as a}from"./element-plus-95e0b914.js";import"./vue-5bfa3a54.js";import{_ as i,u as n}from"./index-a5df0f75.js";import{x as r}from"./homeStore-7900023d.js";import{S as o,a as c,A as m}from"./swiper-7f939876.js";import{B as p}from"./bignumber.js-a537a5ca.js";import{s as u}from"./startEndSlide-8c1800d1.js";import{C as f,G as d,H as x,F as j,J as w}from"./@vueuse-5227c686.js";import{h as v}from"./homeApi-030fb9ef.js";import{S as y}from"./@vicons-f32a0bdb.js";import{j as h,h as g,m as z,v as b,az as I,o as k,c as D,a as S,x as C,a8 as N,t as E,b as T,f as _,l as F,a9 as L,aa as M,F as R,k as A,C as W,D as U}from"./@vue-5e5cdef9.js";import{_ as q}from"./naive-ui-0ee0b8c3.js";import"./three-59a86278.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./chartResize-3e3d11d7.js";import"./element-resize-detector-0d37a2ab.js";import"./@babel-f3c0a00c.js";import"./batch-processor-06abf2b4.js";import"./lodash-6d99edc3.js";import"./echartsInit-2e16a3ff.js";import"./menuStore-30bf76d3.js";import"./vue-router-6159329f.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./dayjs-67f8ddef.js";import"./icons-95011f8c.js";import"./chartXY-a0399c4a.js";import"./api-360ec627.js";import"./notification-950a5f80.js";import"./axios-84f1a956.js";import"./quasar-df1bac18.js";import"./countUtil-d7099b62.js";import"./alarmAnalysisApi-0364d01e.js";import"./lodash-es-ea7deab5.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./xe-utils-fe99d42a.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-be30dbb6.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./taskUitls-36951a34.js";import"./index-092b8780.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./date-fns-tz-0cc073c8.js";const B=t=>(W("data-v-69a986e9"),t=t(),U(),t),H={class:"progress-left u-flex-center-no u-gap-8"},P=B((()=>S("article",{class:"small-title font-small-text-size"}," 电站在线率 ",-1))),Y={class:"tw-flex-1 light-blue"},O={class:"tw-text-xl tw-text-[#fff] tw-flex tw-items-center"},X={class:"supplementary-text font-small-text-size"},G={class:"title u-flex-x-center item-start u-gap-8"},J={class:"main-title font-title-size"},Q={class:"date-time"},V={class:"progress-right u-flex-center-no u-gap-8"},Z=B((()=>S("article",{class:"small-title font-small-text-size"}," 逆变器在线率 ",-1))),$={class:"tw-flex-1 light-blue"},K={class:"tw-text-xl tw-text-[#fff] tw-flex tw-items-center"},tt={class:"supplementary-text font-small-text-size"},et={class:"ElecDaily u-flex-column"},st=B((()=>S("div",{class:""},[S("article",{class:"regular-title sub-title title-border font-sub-title-size"},"发电汇总")],-1))),lt={class:"u-flex-1 u-flex-column plant-card-content"},at={class:"u-flex-1 u-flex-y-center justify-between"},it=B((()=>S("article",{class:"small-title font-text-size"}," 总发电量 ",-1))),nt={class:"u-flex-1 u-flex-y-center justify-end"},rt={class:"body-text font-text-size"},ot={class:"supplementary-text font-small-text-size2"},ct={class:"u-flex-1 u-flex-y-center justify-between"},mt=B((()=>S("article",{class:"small-title font-text-size"}," 月发电量 ",-1))),pt={class:"u-flex-1 u-flex-y-center justify-end"},ut={class:"body-text font-text-size"},ft={class:"supplementary-text font-small-text-size2"},dt={class:"u-flex-1 u-flex-y-center justify-between"},xt=B((()=>S("article",{class:"small-title font-text-size"}," 日发电量 ",-1))),jt={class:"u-flex-1 u-flex-y-center justify-end"},wt={class:"body-text font-text-size"},vt={class:"supplementary-text font-small-text-size2"},yt={class:"Assets u-flex-center-no"},ht={class:"u-flex-1 u-flex-column u-gap-10"},gt={class:"u-flex-1 u-flex-column"},zt=B((()=>S("span",{class:"small-title text-center font-sub-title-size"}," 总装机容量 ",-1))),bt={class:"u-flex-center-no"},It={class:"body-text font-text-size"},kt={class:"supplementary-text font-small-text-size2"},Dt={class:"u-flex-1 u-flex-column u-gap-10"},St={class:"u-flex-1 u-flex-column"},Ct=B((()=>S("span",{class:"small-title text-center font-sub-title-size"},"总电站数量",-1))),Nt={class:"body-text text-center font-text-size"},Et={class:"AlarmStatics u-flex-column"},Tt=B((()=>S("div",{class:""},[S("article",{class:"regular-title sub-title title-border font-sub-title-size"},"实时告警")],-1))),_t={class:"u-flex-1 u-flex-center-no"},Ft={class:"u-flex-1 h-full u-flex-column item-center justify-center"},Lt={class:"body-text text-center font-text-size"},Mt={class:"small-title text-center font-text-size"},Rt={class:"u-flex-1 h-full u-flex-column item-center justify-center"},At={class:"body-text text-center font-text-size"},Wt={class:"small-title text-center font-text-size"},Ut={class:"Map"},qt={class:"map-info u-flex-column items-center"},Bt={class:"area u-flex-y-center u-gap-6"},Ht=["onClick"],Pt={key:0,class:"weather u-flex-center u-gap-6"},Yt={class:"u-flex-center"},Ot=B((()=>S("i",{class:"font-small-text-size"},"温度：",-1))),Xt={class:"font-small-text-size weather-font-color"},Gt={class:"u-flex-center"},Jt=B((()=>S("i",{class:"font-small-text-size"},"风向：",-1))),Qt={class:"font-small-text-size weather-font-color"},Vt={class:"u-flex-center"},Zt=B((()=>S("i",{class:"font-small-text-size"},"风速：",-1))),$t={class:"font-small-text-size weather-font-color"},Kt={class:"u-flex-center weather-font-color"},te=B((()=>S("i",{class:"font-small-text-size"},"湿度：",-1))),ee={class:"font-small-text-size"},se={class:"u-flex-center u-gap-6 font-small-text-size weather-font-color"},le={class:"weather-font-color"},ae={class:"ElecChart"},ie={class:"SavingEnergy"},ne={class:"se-item-1"},re=B((()=>S("article",{class:"small-title text-center font-text-size"},"节约标准煤 (万吨)",-1))),oe={class:"body-text text-left font-small-text-size"},ce={class:"se-item-2"},me=B((()=>S("article",{class:"small-title text-center font-text-size"},"日CO2减排 (吨)",-1))),pe={class:"body-text text-right font-small-text-size"},ue={class:"se-item-3"},fe={class:"body-text text-left font-small-text-size"},de=B((()=>S("article",{class:"small-title text-center font-text-size"},"CO2减排 (万吨)",-1))),xe={class:"se-item-4"},je={class:"body-text text-right font-small-text-size"},we=B((()=>S("article",{class:"small-title text-center font-text-size"},"等效植树 (棵)",-1))),ve={class:"RotationalSeeding"},ye=B((()=>S("div",{class:"rs-title"},[S("article",{class:"regular-title sub-title title-border font-sub-title-size"},"电站效率")],-1))),he={class:"u-flex-1 rs-content"},ge={class:"tw-flex tw-items-center tw-justify-around text-ellipsis font-small-text-size"},ze={class:"tw-w-[40%]"},be={class:"inline-flex tw-w-[30%]"},Ie={class:"tw-text-[#F49A26] tw-w-[25%]"},ke={key:0,class:"rs-loading u-flex-center-no"},De=i(Object.assign({name:"B2HomeScreen"},{__name:"screenCopy",props:{iframe:{type:Boolean,default:!1}},setup(i){const W=i,U=h({start:null,end:!1,index:{startIndex:0,endIndex:50},total:0,displayData:[],originData:[]}),B={scrollTimer:null,retryTimer:null,dateTimer:null,elecChartTimer:null,resultTimer:null},De=[m],Se=new u(10,20,10,100),[Ce]=f(),Ne=r(),Ee=n();Ee.getUser();const Te=g([]),_e=h({curDate:d(x(),"HH:MM YYYY/MM/DD"),map:{alarmList:[],nodeStatus:"",weatherData:null,parentIdList:[]}}),Fe=h({map:j("mapDom")}),Le=g(null),{isFullscreen:Me,enter:Re,exit:Ae,toggle:We}=w(Le);async function Ue(){Se.total<=10||(Se.total<=20?Te.value.push(...Te.value):Te.value.length>=10&&Te.value.splice(Ce.value?Se.step:0,Se.step,...await async function(){const t=await v(...Se.next());return"00000"==t.status&&Se.resetTotal(t.data.total),t.data.records}()))}return z((async()=>{W.iframe&&document.querySelector(".screen-box").classList.remove("screenfull-content"),await Ne.getRankInfo(),_e.map=new t(Fe.map),await _e.map.setChart();const e=await v(0,20);"00000"==(null==e?void 0:e.status)&&(Se.resetTotal(e.data.total),Te.value=e.data.records),top!=self&&(document.querySelector("ul").style.display="none",document.querySelector("header").classList.add("tw-hidden"),document.querySelector("html").classList.add("tw-min-h-0"))})),b((()=>{var t,e,s,l;for(let a in B)B[a]&&(l=B)[s=a]&&(clearInterval(l[s]),l[s]=null);_e.map.timer&&(clearInterval(_e.map.timer),_e.map.timer=null,Fe.map=null,_e.map=null,null==(e=null==(t=_e.map)?void 0:t.chart)||e.clear())})),(t,i)=>{var n,r,m,u,f,d,x;const j=l,w=a,v=e,h=s,g=q,z=I("go");return k(),D("div",{class:"screen-box bg screenfull-content",ref_key:"mainRef",ref:Le},[S("div",{class:"Header",onDblclick:i[0]||(i[0]=t=>T(We)())},[S("div",H,[P,S("div",Y,[C(j,{"stroke-width":10,percentage:new(T(p))(null==(n=T(Ne).plantNumInfo)?void 0:n.onlineRate).times(100).decimalPlaces(2).toNumber()||0},{default:N((()=>{var t;return[S("article",O,[S("span",X,E(new(T(p))(null==(t=T(Ne).plantNumInfo)?void 0:t.onlineRate).times(100).decimalPlaces(2).toString())+"% ",1)])]})),_:1},8,["percentage"])])]),S("div",G,[T(Ee).userInfo.screenLogo?(k(),_(w,{key:0,src:T(Ee).userInfo.screenLogo,fit:"contain",class:"image"},null,8,["src"])):F("",!0),S("h1",J,E(T(Ee).userInfo.projectTitle),1),S("div",Q,E(T(_e).curDate),1)]),S("div",V,[Z,S("div",$,[C(j,{"stroke-width":12,percentage:new(T(p))(null==(r=T(Ne).inverterNumInfo)?void 0:r.onlineRate).times(100).decimalPlaces(2).toNumber()||0},{default:N((()=>{var t;return[S("article",K,[S("span",tt,E(new(T(p))(null==(t=T(Ne).inverterNumInfo)?void 0:t.onlineRate).times(100).decimalPlaces(2).toString())+"% ",1)])]})),_:1},8,["percentage"])])])],32),S("div",et,[st,S("div",lt,[S("div",at,[C(T(y),{class:"tw-w-[30px] tw-mr-1 tw-text-[#F8B62D]"}),it,S("article",nt,[S("span",rt,E((String(T(Ne).plantInfo.totalElectricity)||"000000.00").split("").join(" ")),1),S("span",ot,E(T(Ne).plantInfo.totalElectricityThousandUnit?"kWh":"MWh"),1)])]),S("div",ct,[C(T(y),{class:"tw-w-[30px] tw-mr-1 tw-text-[#F8B62D]"}),mt,S("article",pt,[S("span",ut,E((String(T(Ne).plantInfo.monthElectricity)||"000000.00").split("").join(" ")),1),S("span",ft,E(T(Ne).plantInfo.monthElectricityThousandUnit?"kWh":"MWh"),1)])]),S("div",dt,[C(T(y),{class:"tw-w-[30px] tw-mr-1 tw-text-[#F8B62D]"}),xt,S("article",jt,[S("span",wt,E((String(T(Ne).plantInfo.todayElectricity)||"000000.00").split("").join(" ")),1),S("span",vt,E(T(Ne).plantInfo.todayElectricityThousandUnit?"kWh":"MWh"),1)])])])]),S("div",yt,[S("div",ht,[C(w,{class:"as-image",src:"https://www.btosolarman.com/assets/btosolar/picture/screen/computer.png",fit:"contain"}),S("p",gt,[zt,S("span",bt,[S("i",It,E((T(Ne).plantInfo.plantCapacity+""||"000000.00").split("").join("")),1),S("i",kt,E(T(Ne).plantInfo.plantCapacityThousandUnit?"kWp":"MWp"),1)])])]),S("div",Dt,[C(w,{class:"as-image",src:"https://www.btosolarman.com/assets/btosolar/picture/screen/net.png",fit:"contain"}),S("p",St,[Ct,S("span",Nt,E(T(Ne).plantNumInfo.totalNum),1)])])]),S("div",Et,[Tt,S("div",_t,[S("div",Ft,[S("article",Lt,E(T(Ne).alarmNumInfo.alarmPlantNum),1),L((k(),D("article",Mt,[M(" 告警电站 ")])),[[z,"/plantManage/plantList?status=3"]])]),S("div",Rt,[S("article",At,E(T(Ne).alarmNumInfo.alarmInfoNum),1),L((k(),D("article",Wt,[M(" 告警 ")])),[[z,"/alarmAnalysis/alarmList?status=0"]])])])]),S("div",Ut,[S("div",qt,[S("p",Bt,[S("span",{class:"font-small-text-size",onClick:i[1]||(i[1]=t=>T(_e).map.titleEnter())},"全国"),(k(!0),D(R,null,A(T(_e).map.parentIdList,(t=>(k(),D("span",{class:"font-small-text-size",key:t.name,onClick:e=>T(_e).map.titleEnter(t)},E(t.name),9,Ht)))),128))]),(null==(m=T(_e).map.weatherData)?void 0:m.city)&&T(_e).map.parentIdList?(k(),D("p",Pt,[S("span",Yt,[Ot,S("i",Xt,E(null==(u=T(_e).map.weatherData)?void 0:u.temperature)+"℃ ",1)]),S("span",Gt,[Jt,S("i",Qt,E(null==(f=T(_e).map.weatherData)?void 0:f.wind),1)]),S("span",Vt,[Zt,S("i",$t,E(null==(d=T(_e).map.weatherData)?void 0:d.windspeed),1)]),S("span",Kt,[te,S("i",ee,E(null==(x=T(_e).map.weatherData)?void 0:x.humidity),1)])])):F("",!0),S("p",se,[S("span",null,E(T(_e).map.plantName),1),S("span",le,E(T(_e).map.nodeStatus),1)])]),S("figure",{id:"mapChart",class:"u-wh-full",ref:"mapDom",onClick:i[2]||(i[2]=(...e)=>t.exitHandle&&t.exitHandle(...e))},null,512)]),S("div",ae,[C(v)]),S("div",ie,[S("div",ne,[re,S("article",oe,E(T(Ne).plantInfo.totalCocal),1)]),S("div",ce,[me,S("article",pe,E(T(Ne).plantInfo.todayCo2),1)]),S("div",ue,[S("article",fe,E(T(Ne).plantInfo.totalCo2),1),de]),S("div",xe,[S("article",je,E(T(Ne).plantInfo.treeNum),1),we]),C(h)]),S("div",ve,[ye,S("div",he,[T(Te).length?(k(),_(T(c),{key:0,id:"swiperlist",ref:"swiperRef",autoplay:{delay:2e3,disableOnInteraction:!0},loop:!0,modules:De,"slides-per-view":10,"space-between":0,speed:300,class:"tw-h-full swiper-no-swiping swipe tw-rounded-br-xl tw-rounded-bl-xl",direction:"vertical",onReachEnd:Ue},{default:N((()=>[(k(!0),D(R,null,A(T(Te),((t,e)=>(k(),_(T(o),{key:e,class:"tw-w-full"},{default:N((()=>[S("tr",ge,[S("td",ze,[C(g,{class:"tw-w-[100%] tw-text-white"},{default:N((()=>[M(E(t.plantName),1)])),_:2},1024)]),S("td",be,[C(j,{percentage:parseFloat((100*parseFloat(t.plantEfficiency)).toFixed(2)),"show-text":!1,"stroke-width":10,class:"tw-inline",color:"#F49A26",status:"success"},null,8,["percentage"])]),S("td",Ie,E((100*parseFloat(t.plantEfficiency)).toFixed(2))+" % ",1)])])),_:2},1024)))),128))])),_:1},512)):F("",!0)]),T(U).end?(k(),D("p",ke,"加载中...")):F("",!0)])],512)}}}),[["__scopeId","data-v-69a986e9"]]);export{De as default};
