import"./vue-5bfa3a54.js";import{j as e,P as t,Q as n,R as s,S as i,q as o,A as a,T as l,F as r,y as d}from"./@vue-5e5cdef9.js";const c="skeleton",u=`x-${c}`,f=`${u}-item`,v=`${f}-ref`,g=new WeakMap,b=new ResizeObserver(((e,t)=>{for(let n=0;n<e.length;n++){const s=e[n],i=g.get(s.target);null==i||i.forEach((e=>e(s,t)))}}));var p={observer(e,t){if(t){const n=g.get(e);n?n.add(t):g.set(e,new Set([t]))}b.observe(e)},dispose(e,t){var n;t?null===(n=g.get(e))||void 0===n||n.delete(t):(g.delete(e),b.unobserve(e))},unobserve(e){b.unobserve(e)}};const m=e=>"object"==typeof e.value?e.value.loading:e.value,h=e=>d([u,`${u}-animated`,e.value.loadingClass]),x=(e,t,c)=>{const g=n({el:e,binding:t,fragment:e.appendChild(document.createElement("div")),targets:n([]),vnode:c});e=null,t=null,c=null;const b=s((()=>{var e;const{el:t,binding:n,vnode:s}=g;m(n)?(((e,t,n)=>{t=t.split(" ");const s=(n=n.split(" ")).filter((e=>!t.includes(e))),i=t;s.forEach((t=>t&&e.classList.remove(t))),i.forEach((t=>t&&e.classList.add(t)))})(t,(e=>{if(!m(e))return"";const t=(e=>e.modifiers.animated)(e);return d([u,{[`${u}-animated`]:t},e.value.loadingClass])})(n),h(n)),t.style.cssText=i(o([t.style.cssText,n.value.loadingStyle]))):(t.classList.remove(...h(n).split(" ")),t.style.cssText=i(null===(e=s.props)||void 0===e?void 0:e.style))})),x=s((()=>{const{binding:e,fragment:t}=g,n=g.targets,s=m(e);if(s?p.observer(g.el):p.unobserve(g.el),s?n.forEach((e=>e.el.classList.add(v))):n.forEach((e=>e.el.classList.remove(v))),s){const e=n.map((e=>{var t,n;return a("div",{class:[g.binding.value.class,null===(t=e.binding.value)||void 0===t?void 0:t.class,f],style:[g.binding.value.style,null===(n=e.binding.value)||void 0===n?void 0:n.style,e.state.style]})}));l(a(r,e),t)}else l(null,t)}));return g.unwatch=()=>{b(),x()},g},y=[],C={created(e,t,n){y.push(x(e,t,n))},beforeMount(e,t,n){const s=y.find((t=>t.el===e));s.binding=t,s.vnode=n},beforeUpdate(e,t,n){const s=y.find((t=>t.el===e));s.vnode=n,s.binding=t},beforeUnmount(e){const t=y.findIndex((t=>t.el===e));if(t<0)return;const n=y.splice(t,1)[0];n.unwatch(),n.fragment.parentNode.removeChild(n.fragment),p.dispose(e)}},k={mounted(e,t){const n=y.findLast((t=>t.el.contains(e)));if(!n)return;const s=E(n,e,t);n.targets.push(s)},beforeUnmount(e){const t=y.findLast((t=>t.el.contains(e)));if(!t)return;const n=t.targets.findIndex((t=>t.el===e));if(n<0)return;const s=t.targets[n];t.targets.splice(n,1),p.dispose(t.el,s.resizeCb)}},E=(n,s,i)=>{s.classList.add(`${f}-ref`);const{el:o}=n,a={el:s,binding:i,state:e({}),resizeCb:()=>{const e=o.getBoundingClientRect(),n=getComputedStyle(o),i=getComputedStyle(s),l=s.getBoundingClientRect(),r={position:"absolute"};["top","left"].forEach((s=>{const i=parseInt(n[t(`border-${s}-width`)]);r[s]=Math.abs(l[s]-e[s])-i+"px"})),["width","height"].forEach((e=>r[e]=Math.ceil(l[e])+"px")),r.borderRadius=i.borderRadius,r.zIndex=+i.zIndex+1,a.state.style=r}};return a.resizeCb(),p.observer(o,a.resizeCb),a};!function(e,t){void 0===t&&(t={});var n=t.insertAt;if(e&&"undefined"!=typeof document){var s=document.head||document.getElementsByTagName("head")[0],i=document.createElement("style");i.type="text/css","top"===n&&s.firstChild?s.insertBefore(i,s.firstChild):s.appendChild(i),i.styleSheet?i.styleSheet.cssText=e:i.appendChild(document.createTextNode(e))}}(".x-skeleton{position:relative}.x-skeleton-item{background-color:#e6e6e6;box-sizing:border-box}.x-skeleton-item-ref{opacity:0;pointer-events:none;user-select:none}.x-skeleton-animated .x-skeleton-item{animation:x-skeleton-loading 1.4s ease infinite;background:linear-gradient(90deg,#f2f2f2 25%,#e6e6e6 37%,#f2f2f2 63%);background-size:400% 100%}@keyframes x-skeleton-loading{0%{background-position:100% 50%}to{background-position:0 50%}}");var z={install(e){e.directive(c,C),e.directive(`${c}-item`,k)}};export{z as i};
