import{v as e}from"./@vueuse-5227c686.js";import{r as t,f as a,v as i,k as l,u as s,d as o,B as n}from"./element-plus-95e0b914.js";import"./vue-5bfa3a54.js";import{M as r}from"./@element-plus-4c34063a.js";import{u as d}from"./vue-router-6159329f.js";import{a as c}from"./vxe-table-3a25f2d2.js";import{X as p}from"./xe-utils-fe99d42a.js";import{i as m,_ as u}from"./index-a5df0f75.js";import{f,d as h,c as v}from"./chartResize-3e3d11d7.js";import{d as g}from"./dayjs-67f8ddef.js";import{i as y}from"./echarts-f30da64f.js";import{h as b,j as x,m as w,p as L,as as j,o as V,c as P,a as k,t as S,b as $,x as C,a8 as N,aa as z,F as A,k as E,y as D,f as _,a6 as T,a9 as O,ak as U,l as W,B as M,C as I,D as H}from"./@vue-5e5cdef9.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./lodash-es-ea7deab5.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./@babel-f3c0a00c.js";import"./dom-zindex-5f662ad1.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./nprogress-e136a1b4.js";import"./quasar-df1bac18.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-be30dbb6.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./lodash-6d99edc3.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./element-resize-detector-0d37a2ab.js";import"./batch-processor-06abf2b4.js";const R=e=>(I("data-v-edcf7bfd"),e=e(),H(),e),Y={class:"station-sel flex items-center justify-start u-gap-10"},q=R((()=>k("span",{class:"u-flex-y-center"},[k("i",{class:"square"}),k("i",{class:"small-title"},"当前电站")],-1))),B={class:"body-text"},F={class:"small-title u-flex-1 text-right"},G={class:"inverter-list"},J={class:"scrollbar-flex-content"},X=["onClick"],K={class:"table-btn"},Z={key:1,class:"chart-box","element-loading-text":"正在生成图表"},Q={class:"table-btn"},ee=M('<div class="u-flex-column chart-power-item" data-v-edcf7bfd><p class="chart-title small-title u-flex-y-center" data-v-edcf7bfd>日功率</p><figure id="powerChart" class="u-flex-1" data-v-edcf7bfd></figure></div><div class="u-flex-column chart-today-item" data-v-edcf7bfd><p class="chart-title small-title u-flex-y-center" data-v-edcf7bfd>日发电量</p><figure id="todayChart" class="u-flex-1" data-v-edcf7bfd></figure></div>',2),te={class:"u-flex-column chart-PV-item"},ae={class:"chart-title small-title u-flex-y-center justify-between"},ie=R((()=>k("span",null,"直流输入",-1))),le=R((()=>k("figure",{id:"pvChart",class:"u-flex-1"},null,-1))),se={class:"u-flex-column chart-L-item"},oe={class:"chart-title small-title u-flex-y-center justify-between"},ne=R((()=>k("span",null,"交流输出",-1))),re=R((()=>k("figure",{id:"LChart",class:"u-flex-1"},null,-1))),de={class:"station-list"},ce={class:"infinite-list",style:{overflow:"auto"}},pe={key:0,class:"infinite-list-empty supplementary-text text-center"},me=["onClick"],ue=u(Object.assign({name:"realtime"},{__name:"index",setup(u){const M=d(),I=b(),H=b(),R=b();b(!0);const ue=b("选择电站"),fe=b(!1),he=x({show:!1,loading:!0,opt:{title:{show:!1,text:"",textStyle:{fontSize:f(16)}},legend:{show:!0,bottom:"2%",selected:{}},tooltip:{show:!0,trigger:"axis",confine:!0,formatter:null},grid:{left:"10%",right:"10%",top:"20%",bottom:"25%"},xAxis:{data:[]},yAxis:{type:"value",axisLine:{show:!0},axisTick:{show:!0},splitLine:{show:!1}},series:[]},originData:{},PVSel:[],LSel:[],chartTem:{time:[],PV:{pv1:{name:"直流输入",data:[]}},L:{L1:{name:"交流输出",data:[]}},power:{name:"功率(W)",data:[]},todayElec:{name:"日发电量(kWh)",data:[]},totalElec:{name:"总发电量(kWh)",data:[]}},pvSelected:[],Lselected:[],pvOpt:{},LOpt:{}}),ve={powerChart:null,todayChart:null,pvChart:null,LChart:null},ge=x({first:!0,plantUid:"",data:[],page:{currentPage:1,pageSize:50}}),ye=x({type:"station",visible:!1,stationData:[],submitLoading:!1,list:{plantUid:"",plantName:"",pageSize:15,currentPage:1}}),be=x({condition:{inverterSN:"",date:g().format("YYYY-MM-DD")},modelData:{},tablePage:{totalResult:0,currentPage:1,pageSize:15}}),xe=x({id:"realTime",border:!0,showFooter:!1,minHeight:500,height:"96%",maxHeight:860,loading:!1,autoResize:!0,editConfig:{trigger:"click",mode:"cell"},scrollX:{enabled:!1},scrollY:{enabled:!1},sortConfig:{sortMethod:({sortList:e})=>{let t={};e.forEach((e=>{t[e.field]=e.order}))}},data:[],customConfig:{storage:{visible:!0,fixed:!0}},toolbarConfig:{custom:!0,slots:{buttons:"toolbar_buttons",tools:"toolbar_tools"}},columns:[{title:" 时间",field:"initTime",width:120,fixed:"left",slots:{default:"time"}},{title:" 直流输入",children:[{title:"模式",field:"pvType",width:100},{title:"通道",field:"pvNum",width:100},{title:"电压",field:"vpv",width:100},{title:"电流",field:"ipv",width:100},{title:"组串",field:"pv",width:200}]},{title:"交流输出",children:[{title:"相位",field:"acNum",width:100},{title:"电压",field:"vac",width:100},{title:"电流",field:"iac",width:100},{title:"频率",field:"fac",width:100}]},{title:"功率(W)",field:"power"},{title:"当日发电量(kWh)",field:"todayElectricity"},{title:"总发电量(kWh)",field:"totalElectricity"}],spanMethod:({row:e,_rowIndex:t,column:a,visibleData:i})=>{const l=e[a.field];if(l&&["initTime","power","totalElectricity","todayElectricity"].includes(a.field)){const e=i[t-1];let s=i[t+1];if(e&&e[a.field]===l)return{rowspan:0,colspan:0};{let e=1;for(;s&&s[a.field]===l;)s=i[++e+t];if(e>1)return{rowspan:e,colspan:1}}}}}),we=async e=>{if(be.condition.inverterSN=e,fe.value=!0,xe.loading)c.modal.message({content:"正在请求数据中，请勿频繁点击",status:"info"});else{xe.loading=!0;try{const e=await(t={...be.tablePage,...be.condition},m({url:"/system/deviceManage/deviceId/realTime",method:"post",data:{...t}}));if("00000"==e.status){if(he.show)he.originData=((e,t)=>{let a={time:[],PV:{},L:{},power:{name:"功率(W)",type:"line",smooth:!0,data:[],lineStyle:{color:"#f8b62d"},itemStyle:{color:"#f8b62d",normal:{color:"#f8b62d"}},areaStyle:{color:"#f8b62d"}},todayElec:{name:"日发电量(kWh)",type:"bar",data:[],lineStyle:{color:"#f8b62d"},itemStyle:{color:"#f8b62d",normal:{color:"#f8b62d"}},areaStyle:{color:"#f8b62d"}},totalElec:{name:"总发电量(kWh)",type:"bar",data:[],lineStyle:{color:"#f8b62d"},itemStyle:{color:"#f8b62d",normal:{color:"#f8b62d"}},areaStyle:{color:"#f8b62d"}}};return e.forEach((e=>{a.time.push(e.initTime.substr(11,5)),a.power.data.push(e.power),a.todayElec.data.push(e.todayElectricity),a.totalElec.data.push(e.totalElectricity);for(let i=1;i<t+1;i++)a.PV[`PV${i}电压(V)`]||(he.PVSel.length<=t&&he.PVSel.push({name:[`PV${i}`],value:`PV${i}`,children:[{name:`PV${i}电压(V)`,value:`PV${i}电压(V)`},{name:`PV${i}电流(A)`,value:`PV${i}电流(A)`}]}),a.PV[`PV${i}电压(V)`]={name:`PV${i}电压(V)`,type:"line",smooth:!0,data:[]},a.PV[`PV${i}电流(A)`]={name:`PV${i}电流(A)`,type:"line",data:[],smooth:!0,yAxisIndex:1}),a.L[`L${i}电压(V)`]||(he.LSel.length<=t&&he.LSel.push({name:[`L${i}`],value:`L${i}`,children:[{name:`L${i}电压(V)`,value:`L${i}电压(V)`},{name:`L${i}电流(A)`,value:`L${i}电流(A)`},{name:`L${i}频率(Hz)`,value:`L${i}频率(Hz)`}]}),a.L[`L${i}电压(V)`]={name:`L${i}电压(V)`,type:"line",smooth:!0,data:[]},a.L[`L${i}电流(A)`]={name:`L${i}电流(A)`,type:"line",smooth:!0,data:[],yAxisIndex:1},a.L[`L${i}频率(Hz)`]={name:`L${i}频率(Hz)`,type:"line",smooth:!0,data:[],yAxisIndex:2}),a.PV[`PV${i}电压(V)`].data.push(e[`vpv${i}`]),a.PV[`PV${i}电流(A)`].data.push(e[`ipv${i}`]),a.L[`L${i}电压(V)`].data.push(e[`vac${i}`]),a.L[`L${i}电流(A)`].data.push(e[`iac${i}`]),a.L[`L${i}频率(Hz)`].data.push(e[`fac${i}`])})),a})(e.data.records.reverse(),e.data.pvNum),Ne(he.originData,ve);else{be.tablePage.totalResult=e.data.total;let t=p.clone(e.data.records,!0);xe.data=((e,t)=>{let a=[];return e.forEach((e=>{for(let i=1;i<t+1;i++){let t={};t.initTime=e.initTime,t.pvType="独立模式",t.pvNum=`PV${i}`,t.vpv=e[`vpv${i}`],t.ipv=e[`ipv${i}`],t.pv=e[`pv${i}`],t.acNum=`L${i}`,t.vac=e[`vac${i}`],t.iac=e[`iac${i}`],t.fac=e[`fac${i}`],t.power=e.power,t.todayElectricity=e.todayElectricity,t.totalElectricity=e.totalElectricity,a.push(t)}})),a})(t,e.data.pvNum)}xe.loading=!1}else xe.data=[],be.tablePage.totalResult=0,xe.loading=!1,he.show&&(he.loading=!1,Ne(he.chartTem,ve))}catch(a){xe.data=[],be.tablePage.totalResult=0,xe.loading=!1}finally{fe.value=!1}var t}},Le=async(e,t)=>{ye.submitLoading=!0;try{const i=await(a={plantUid:e,...ge.page},m({url:"/system/deviceManage/deviceManage/inverterInfoList",method:"post",data:{...a}}));ye.submitLoading=!1,ue.value=t,ye.visible=!1,"00000"==i.status&&(be.tablePage={totalResult:0,currentPage:1,pageSize:20},ge.data=i.data.records,be.condition.inverterSN=i.data.records[0].inverterSN,we(be.condition.inverterSN))}catch(i){ye.submitLoading=!1}var a},je=()=>{ye.list.currentPage+=1,Ve("next")},Ve=async(e,t)=>{ye.list.plantUid="","reset"==e?ye.list.currentPage=1:"page"==e&&(ye.list.plantUid=t),ye.submitLoading=!0;try{const t=await(a=ye.list,m({url:"/system/plantManage/getPlantList",method:"post",data:{...a}}));ye.submitLoading=!1,"00000"==t.status?(1==ye.list.currentPage?ye.stationData=t.data.records:ye.stationData=[...ye.stationData,...t.data.records],ge.first&&(ue.value=ye.stationData[0].plantName,Le(ye.stationData[0].plantUid,ye.stationData[0].plantName)),"page"==e&&(ye.list.plantName=ye.stationData[0].plantName)):ye.stationData=[]}catch(i){ye.submitLoading=!1}var a},Pe=()=>{we(be.condition.inverterSN)},ke=async()=>{Pe()},Se=e=>e.getTime()>Date.now(),$e=()=>{},Ce=(e,t)=>{let a=document.getElementById(e),i=y(a);return i.setOption(t),i},Ne=(e,t)=>{let a;he.loading=!0;for(let i in t){switch(a=JSON.parse(JSON.stringify(he.opt)),a.xAxis.data=e.time,i){case"todayChart":a.title.text="日发电量(kWh)",a.yAxis.name="kWh",a.series.push(e.todayElec);break;case"totalChart":a.title.text="总发电量(kWh)",a.yAxis.name="kWh",a.series.push(e.totalElec);break;case"powerChart":a.title.text="日功率(W)",a.yAxis.name="W",a.series.push(e.power);break;case"pvChart":a.title.text="直流输入",he.pvSelected=[],Object.keys(e.PV).forEach((e=>{e.indexOf("V1电")>-1?(a.legend.selected[`${e}`]=!0,he.pvSelected.push(e)):a.legend.selected[`${e}`]=!1})),a.yAxis=[{type:"value",name:"电压(V)",axisLine:{show:!0},axisTick:{show:!0},splitLine:{show:!1}},{type:"value",name:"电流(A)",axisLine:{show:!0},axisTick:{show:!0},splitLine:{show:!1}}],a.series=Object.values(e.PV),a.legend.show=!1,he.pvOpt=p.clone(a,!0);break;case"LChart":a.title.text="交流输出",he.Lselected=[],Object.keys(e.L).forEach((e=>{e.indexOf("L1电")>-1||e.indexOf("L1频")>-1?(a.legend.selected[`${e}`]=!0,he.Lselected.push(e)):a.legend.selected[`${e}`]=!1})),a.yAxis=[{type:"value",name:"电压(V)",axisLine:{show:!0},axisTick:{show:!0},splitLine:{show:!1}},{type:"value",name:"电流(A)",axisLine:{show:!0},axisTick:{show:!0},splitLine:{show:!1}},{type:"value",name:"频率(Hz)",offset:f(40),axisLine:{show:!0},axisTick:{show:!0},splitLine:{show:!1}}],a.series=Object.values(e.L),a.legend.show=!1,he.LOpt=p.clone(a,!0)}t[i]&&(t[i].dispose(),h(R.value)),t[i]=Ce(i,a)}setTimeout((()=>{he.loading=!1}),1e3),v(R.value,t)},ze=e=>{for(let t in e)e[t]&&e[t].dispose()},Ae=async e=>{"chart"==e?(he.show=!0,be.tablePage={currentPage:1,pageSize:100},await we(be.condition.inverterSN)):(he.PVSel=[],he.LSel=[],he.pvSelected=[],he.Lselected=[],ze(ve),be.tablePage={currentPage:1,pageSize:15},h(R.value),he.loading=!0,he.show=!1,await we(be.condition.inverterSN))},Ee=(e,t)=>{if(!t)if("PV"==e){let e=p.clone(he.pvOpt,!0),t=he.pvSelected.join("-");for(let a in e.legend.selected)t.indexOf(a)>-1?e.legend.selected[a]=!0:e.legend.selected[a]=!1;ve.pvChart.dispose(),ve.pvChart=Ce("pvChart",e)}else{let e=p.clone(he.LOpt,!0),t=he.Lselected.join("-");for(let a in e.legend.selected)t.indexOf(a)>-1?e.legend.selected[a]=!0:e.legend.selected[a]=!1;ve.LChart.dispose(),ve.LChart=Ce("LChart",e)}},De=async e=>{await we(be.condition.inverterSN)};return w((async()=>{M.query.plantUid&&""!=M.query.plantUid?await Ve("page",M.query.plantUid):await Ve("reset"),ge.first=!1})),L((()=>{h(R.value),ze(ve)})),(d,c)=>{const p=j("CaretBottom"),m=t,u=a,f=i,h=l,v=j("vxe-button"),g=j("vxe-pager"),y=j("vxe-grid"),b=s,x=o,w=j("vxe-modal"),L=n,M=e;return V(),P("div",{class:"app-container",ref_key:"appContainerRef",ref:I},[k("div",Y,[k("p",{class:"plant-sel h-full u-flex-center u-gap-10 shadow-md body-text justify-between",onClick:c[0]||(c[0]=e=>{return t="station",ye.type=t,void(ye.visible=!0);var t})},[q,k("span",B,S($(ue)),1),C(m,{class:"sel-btn",size:20},{default:N((()=>[C(p)])),_:1})]),k("span",F,[C(u,{type:"primary",onClick:ke,loading:$(fe)},{default:N((()=>[z("刷新")])),_:1},8,["loading"]),z("  "),C(f,{modelValue:$(be).condition.date,"onUpdate:modelValue":c[1]||(c[1]=e=>$(be).condition.date=e),disabled:$(xe).loading,type:"date","value-format":"YYYY-MM-DD",onChange:Pe,"disabled-date":Se},null,8,["modelValue","disabled"])])]),k("div",G,[C(h,null,{default:N((()=>[k("div",J,[(V(!0),P(A,null,E($(ge).data,((e,t)=>(V(),P("p",{class:D(["inverter-list-item h-full body-text u-flex-center",$(be).condition.inverterSN==e.inverterSN?"is-active":""]),onClick:t=>we(e.inverterSN)},S(e.inverterSN),11,X)))),256))])])),_:1})]),$(he).show?O((V(),P("div",Z,[k("div",Q,[C(v,{status:"primary",onClick:c[5]||(c[5]=e=>Ae("table"))},{default:N((()=>[z("表格")])),_:1})]),k("div",{ref_key:"gatherChart",ref:R,id:"chartGather"},[ee,k("div",te,[k("p",ae,[ie,C(b,{modelValue:$(he).pvSelected,"onUpdate:modelValue":c[6]||(c[6]=e=>$(he).pvSelected=e),data:$(he).PVSel,"render-after-expand":!1,"show-checkbox":"",multiple:"","collapse-tags":"",onVisibleChange:c[7]||(c[7]=e=>Ee("PV",e))},null,8,["modelValue","data"])]),le]),k("div",se,[k("p",oe,[ne,C(b,{modelValue:$(he).Lselected,"onUpdate:modelValue":c[8]||(c[8]=e=>$(he).Lselected=e),data:$(he).LSel,"render-after-expand":!1,"show-checkbox":"",multiple:"","collapse-tags":"",onVisibleChange:c[9]||(c[9]=e=>Ee("L",e))},null,8,["modelValue","data"])]),re])],512)])),[[L,$(he).loading]]):(V(),_(y,T({key:0,ref_key:"xGrid",ref:H,class:"my-grid66"},$(xe)),{toolbar_buttons:N((()=>[])),toolbar_tools:N((()=>[k("div",K,[C(v,{status:"warning",icon:"vxe-icon-chart-line",onClick:c[2]||(c[2]=e=>Ae("chart"))})])])),time:N((({row:e})=>[k("span",null,S(e.initTime.substr(11,5)),1)])),"row-operate":N((({row:e})=>[C(u,{link:"",type:"primary",onClick:t=>d.seeTableItem(e)},{default:N((()=>[z("查看详情")])),_:2},1032,["onClick"])])),bottom:N((()=>[])),pager:N((()=>[C(g,{perfect:"","current-page":$(be).tablePage.currentPage,"onUpdate:currentPage":c[3]||(c[3]=e=>$(be).tablePage.currentPage=e),"page-size":$(be).tablePage.pageSize,"onUpdate:pageSize":c[4]||(c[4]=e=>$(be).tablePage.pageSize=e),total:$(be).tablePage.totalResult,onPageChange:De},null,8,["current-page","page-size","total"])])),_:1},16)),C(w,{modelValue:$(ye).visible,"onUpdate:modelValue":c[13]||(c[13]=e=>$(ye).visible=e),title:"电站选择",width:"500","min-width":"400","min-height":"100",loading:$(ye).submitLoading,resize:"","destroy-on-close":"",onHide:$e},{default:N((()=>[k("div",de,[C(x,{modelValue:$(ye).list.plantName,"onUpdate:modelValue":c[11]||(c[11]=e=>$(ye).list.plantName=e),clearable:"",onKeyup:c[12]||(c[12]=U((e=>Ve("reset")),["enter"]))},{append:N((()=>[C(u,{icon:$(r),onClick:c[10]||(c[10]=e=>Ve("reset"))},null,8,["icon"])])),_:1},8,["modelValue"]),O((V(),P("ul",ce,[0==$(ye).stationData.length?(V(),P("li",pe,"暂无数据")):W("",!0),(V(!0),P(A,null,E($(ye).stationData,(e=>(V(),P("li",{class:"infinite-list-item body-text",key:e.plantUid,onClick:t=>Le(e.plantUid,e.plantName)},S(e.plantName),9,me)))),128))])),[[M,je]])])])),_:1},8,["modelValue","loading"])],512)}}}),[["__scopeId","data-v-edcf7bfd"]]);export{ue as default};
