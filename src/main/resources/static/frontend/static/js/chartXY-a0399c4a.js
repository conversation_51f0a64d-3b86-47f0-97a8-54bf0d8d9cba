import{l as e}from"./lodash-6d99edc3.js";const t={legend:{show:!0,position:[10,10],width:800,textStyle:{color:"rgba(255, 255, 255, 1)"}},tooltip:{trigger:"axis",formatter:e=>{let t=e.at(0).axisValue+"<br>";for(const i of e){const e=s(i);t+=i.marker+i.seriesName+':<span style="display:inline-block;margin-right:20px"></span>'+i.data+e+"<br />"}return t}},grid:{top:"10px",bottom:"40px",right:"120px",left:"60px"},xAxis:{type:"category",data:[],axisLabel:{show:!0,color:"#fff"},axisLine:{show:!0,lineStyle:{color:"#fff",width:0,type:"solid"}},splitLine:{show:!0,lineStyle:{color:["#fff"]}}},yAxis:{type:"value",name:"单",nameLocation:"end",axisLabel:{color:"#fff",margin:15},splitLine:{lineStyle:{color:"#fff"}}},series:[{data:[],type:"line",smooth:!0,itemStyle:{}}]};function i(e=[]){let t={monthElectricity:"月发电量",todayElectricity:"日发电量",yearElectricity:"年发电量",totalElectricity:"总发电量",temperature:"温度",power:"总功率"},i={fac:"频率",iac:"输出电流",ipv:"输入电流",vac:"输出电压",vpv:"输入电压",_power:"功率"};const s=Object.keys(t),n=Object.keys(i),r=e.length-1,a=e.reduce(((e,t,i)=>(Object.entries(t).forEach((([t,s])=>{null==e[t]?e[t]="":e[t]+=i!=r?`${s},`:`${s}`})),e)),{}),o={};let l=0;return Object.keys(a).map((e=>{if(s.includes(e))return void(o[t[e]]=a[e].split(","));const r=n.filter((t=>e.includes(t)));r.length>0&&(l=e.match(/\d+/g)[0],o[`${i[r[0]]}${l}`]=a[e].split(","))})),[a.initTime.split(","),o,Object.keys(o),1*l]}function s(e){return e.seriesName.includes("电压")?"V":e.seriesName.includes("电流")?"A":e.seriesName.includes("电量")?"KWh":e.seriesName.includes("温度")?"℃":e.seriesName.includes("频率")?"Hz":e.seriesName.includes("功率")?"W":""}function n(i,s,n,r){const a=["#76ff03","red","yellow","#18ffff","#ff3d00","#b2ff59","pink","purple"];if(r){let o=function(e){const t=["输出","输入","日","月","年","总"];for(const i of t)e=e.replace(i,"");for(let i=1;i<1*n[3]+1;i++)e=e.replace(i+"","");return e};if(!r.length)return t;i.xAxis.data=s.map((e=>({value:e,textStyle:{fontSize:13}})));const l=["功率","电压","电流","发电量","频率","温度"];i.yAxis=l.map(((e,t)=>({type:"value",name:e,position:"left",alignTicks:!t,show:t==l.findIndex((e=>e==r[0])),axisLine:{show:!t,lineStyle:{color:"white"}}})));const c=e._.transform(n[1],((e,t,i)=>{r.some((e=>i.includes(e)))&&(e[i]=t)}),{});let f=0;const p=e._.transform(c,((i,s,n)=>{const r=e._.cloneDeep(t.series.at(0));r.name=n,r.data=s.map((e=>e||"0.00")),r.yAxisIndex=l.findIndex((e=>e==o(n))),r.itemStyle.color=a.at(++f%a.length),i.push(r)}),[]);return i.series=p,i.legend={icon:"roundRect",orient:"vertical",right:20,top:0,textStyle:{lineHeight:15,color:"rgba(255, 255, 255, 1)"},padding:0,data:e._.keys(c)},i}{i.series.at(0).data=n;const t=s.map((e=>({value:e})));return e._.isArray(i.xAxis)?i.xAxis[0].data=t:i.xAxis.data=t,i}}export{i as g,t as i,n as s};
