import"./vue-5bfa3a54.js";import{s as e}from"./vue-resize-6b3cb84f.js";import{O as t}from"./vue-observe-visibility-e7e33a1f.js";import{O as i,Q as s,A as r,as as l,az as o,a9 as n,o as a,c,g as d,l as h,f as u,a8 as m,F as p,k as f,r as v,a6 as y,aw as $,q as z,y as g,x as S,an as _,ao as b}from"./@vue-5e5cdef9.js";var w={itemsLimit:1e3},T=/(auto|scroll)/;function D(e,t){return null===e.parentNode?t:D(e.parentNode,t.concat([e]))}var x=function(e,t){return getComputedStyle(e,null).getPropertyValue(t)},I=function(e){return T.test(function(e){return x(e,"overflow")+x(e,"overflow-y")+x(e,"overflow-x")}(e))};function R(e){if(e instanceof HTMLElement||e instanceof SVGElement){for(var t=D(e.parentNode,[]),i=0;i<t.length;i+=1)if(I(t[i]))return t[i];return document.scrollingElement||document.documentElement}}function V(e){return(V="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var M={items:{type:Array,required:!0},keyField:{type:String,default:"id"},direction:{type:String,default:"vertical",validator:function(e){return["vertical","horizontal"].includes(e)}},listTag:{type:String,default:"div"},itemTag:{type:String,default:"div"}};function k(){return this.items.length&&"object"!==V(this.items[0])}var O=!1;if("undefined"!=typeof window){O=!1;try{var A=Object.defineProperty({},"passive",{get:function(){O=!0}});window.addEventListener("test",null,A)}catch(C){}}let P=0;var U={name:"RecycleScroller",components:{ResizeObserver:e},directives:{ObserveVisibility:t},props:{...M,itemSize:{type:Number,default:null},gridItems:{type:Number,default:void 0},itemSecondarySize:{type:Number,default:void 0},minItemSize:{type:[Number,String],default:null},sizeField:{type:String,default:"size"},typeField:{type:String,default:"type"},buffer:{type:Number,default:200},pageMode:{type:Boolean,default:!1},prerender:{type:Number,default:0},emitUpdate:{type:Boolean,default:!1},updateInterval:{type:Number,default:0},skipHover:{type:Boolean,default:!1},listTag:{type:String,default:"div"},itemTag:{type:String,default:"div"},listClass:{type:[String,Object,Array],default:""},itemClass:{type:[String,Object,Array],default:""}},emits:["resize","visible","hidden","update","scroll-start","scroll-end"],data:()=>({pool:[],totalSize:0,ready:!1,hoverKey:null}),computed:{sizes(){if(null===this.itemSize){const e={"-1":{accumulator:0}},t=this.items,i=this.sizeField,s=this.minItemSize;let r,l=1e4,o=0;for(let n=0,a=t.length;n<a;n++)r=t[n][i]||s,r<l&&(l=r),o+=r,e[n]={accumulator:o,size:r};return this.$_computedMinItemSize=l,e}return[]},simpleArray:k,itemIndexByKey(){const{keyField:e,items:t}=this,i={};for(let s=0,r=t.length;s<r;s++)i[t[s][e]]=s;return i}},watch:{items(){this.updateVisibleItems(!0)},pageMode(){this.applyPageMode(),this.updateVisibleItems(!1)},sizes:{handler(){this.updateVisibleItems(!1)},deep:!0},gridItems(){this.updateVisibleItems(!0)},itemSecondarySize(){this.updateVisibleItems(!0)}},created(){this.$_startIndex=0,this.$_endIndex=0,this.$_views=new Map,this.$_unusedViews=new Map,this.$_scrollDirty=!1,this.$_lastUpdateScrollPosition=0,this.prerender&&(this.$_prerender=!0,this.updateVisibleItems(!1)),this.gridItems&&this.itemSize},mounted(){this.applyPageMode(),this.$nextTick((()=>{this.$_prerender=!1,this.updateVisibleItems(!0),this.ready=!0}))},activated(){const e=this.$_lastUpdateScrollPosition;"number"==typeof e&&this.$nextTick((()=>{this.scrollToPosition(e)}))},beforeUnmount(){this.removeListeners()},methods:{addView(e,t,r,l,o){const n=i({id:P++,index:t,used:!0,key:l,type:o}),a=s({item:r,position:0,nr:n});return e.push(a),a},unuseView(e,t=!1){const i=this.$_unusedViews,s=e.nr.type;let r=i.get(s);r||(r=[],i.set(s,r)),r.push(e),t||(e.nr.used=!1,e.position=-9999)},handleResize(){this.$emit("resize"),this.ready&&this.updateVisibleItems(!1)},handleScroll(e){if(!this.$_scrollDirty){if(this.$_scrollDirty=!0,this.$_updateTimeout)return;const e=()=>requestAnimationFrame((()=>{this.$_scrollDirty=!1;const{continuous:e}=this.updateVisibleItems(!1,!0);e||(clearTimeout(this.$_refreshTimout),this.$_refreshTimout=setTimeout(this.handleScroll,this.updateInterval+100))}));e(),this.updateInterval&&(this.$_updateTimeout=setTimeout((()=>{this.$_updateTimeout=0,this.$_scrollDirty&&e()}),this.updateInterval))}},handleVisibilityChange(e,t){this.ready&&(e||0!==t.boundingClientRect.width||0!==t.boundingClientRect.height?(this.$emit("visible"),requestAnimationFrame((()=>{this.updateVisibleItems(!1)}))):this.$emit("hidden"))},updateVisibleItems(e,t=!1){const i=this.itemSize,s=this.gridItems||1,r=this.itemSecondarySize||i,l=this.$_computedMinItemSize,o=this.typeField,n=this.simpleArray?null:this.keyField,a=this.items,c=a.length,d=this.sizes,h=this.$_views,u=this.$_unusedViews,m=this.pool,p=this.itemIndexByKey;let f,v,y,$,z,g;if(c)if(this.$_prerender)f=$=0,v=z=Math.min(this.prerender,a.length),y=null;else{const e=this.getScroll();if(t){let t=e.start-this.$_lastUpdateScrollPosition;if(t<0&&(t=-t),null===i&&t<l||t<i)return{continuous:!0}}this.$_lastUpdateScrollPosition=e.start;const r=this.buffer;e.start-=r,e.end+=r;let o=0;if(this.$refs.before&&(o=this.$refs.before.scrollHeight,e.start-=o),this.$refs.after){const t=this.$refs.after.scrollHeight;e.end+=t}if(null===i){let t,i,s=0,r=c-1,l=~~(c/2);do{i=l,t=d[l].accumulator,t<e.start?s=l:l<c-1&&d[l+1].accumulator>e.start&&(r=l),l=~~((s+r)/2)}while(l!==i);for(l<0&&(l=0),f=l,y=d[c-1].accumulator,v=l;v<c&&d[v].accumulator<e.end;v++);for(-1===v?v=a.length-1:(v++,v>c&&(v=c)),$=f;$<c&&o+d[$].accumulator<e.start;$++);for(z=$;z<c&&o+d[z].accumulator<e.end;z++);}else{f=~~(e.start/i*s);f-=f%s,v=Math.ceil(e.end/i*s),$=Math.max(0,Math.floor((e.start-o)/i*s)),z=Math.floor((e.end-o)/i*s),f<0&&(f=0),v>c&&(v=c),$<0&&($=0),z>c&&(z=c),y=Math.ceil(c/s)*i}}else f=v=$=z=y=0;v-f>w.itemsLimit&&this.itemsLimitError(),this.totalSize=y;const S=f<=this.$_endIndex&&v>=this.$_startIndex;if(S)for(let w=0,x=m.length;w<x;w++)g=m[w],g.nr.used&&(e&&(g.nr.index=p[g.item[n]]),(null==g.nr.index||g.nr.index<f||g.nr.index>=v)&&this.unuseView(g));const _=S?null:new Map;let b,T,D;for(let w=f;w<v;w++){b=a[w];const e=n?b[n]:b;if(null==e)throw new Error(`Key is ${e} on item (keyField is '${n}')`);if(g=h.get(e),!i&&!d[w].size){g&&this.unuseView(g);continue}T=b[o];let t=u.get(T),l=!1;if(g){if(!g.nr.used&&(g.nr.used=!0,l=!0,t)){const e=t.indexOf(g);-1!==e&&t.splice(e,1)}}else S?g=t&&t.length?t.pop():this.addView(m,w,b,e,T):(D=_.get(T)||0,(!t||D>=t.length)&&(g=this.addView(m,w,b,e,T),this.unuseView(g,!0),t=u.get(T)),g=t[D],_.set(T,D+1)),h.delete(g.nr.key),g.nr.used=!0,g.nr.index=w,g.nr.key=e,g.nr.type=T,h.set(e,g),l=!0;g.item=b,l&&(w===a.length-1&&this.$emit("scroll-end"),0===w&&this.$emit("scroll-start")),null===i?(g.position=d[w-1].accumulator,g.offset=0):(g.position=Math.floor(w/s)*i,g.offset=w%s*r)}return this.$_startIndex=f,this.$_endIndex=v,this.emitUpdate&&this.$emit("update",f,v,$,z),clearTimeout(this.$_sortTimer),this.$_sortTimer=setTimeout(this.sortViews,this.updateInterval+300),{continuous:S}},getListenerTarget(){let e=R(this.$el);return!window.document||e!==window.document.documentElement&&e!==window.document.body||(e=window),e},getScroll(){const{$el:e,direction:t}=this,i="vertical"===t;let s;if(this.pageMode){const t=e.getBoundingClientRect(),r=i?t.height:t.width;let l=-(i?t.top:t.left),o=i?window.innerHeight:window.innerWidth;l<0&&(o+=l,l=0),l+o>r&&(o=r-l),s={start:l,end:l+o}}else s=i?{start:e.scrollTop,end:e.scrollTop+e.clientHeight}:{start:e.scrollLeft,end:e.scrollLeft+e.clientWidth};return s},applyPageMode(){this.pageMode?this.addListeners():this.removeListeners()},addListeners(){this.listenerTarget=this.getListenerTarget(),this.listenerTarget.addEventListener("scroll",this.handleScroll,!!O&&{passive:!0}),this.listenerTarget.addEventListener("resize",this.handleResize)},removeListeners(){this.listenerTarget&&(this.listenerTarget.removeEventListener("scroll",this.handleScroll),this.listenerTarget.removeEventListener("resize",this.handleResize),this.listenerTarget=null)},scrollToItem(e){let t;const i=this.gridItems||1;t=null===this.itemSize?e>0?this.sizes[e-1].accumulator:0:Math.floor(e/i)*this.itemSize,this.scrollToPosition(t)},scrollToPosition(e){const t="vertical"===this.direction?{scroll:"scrollTop",start:"top"}:{scroll:"scrollLeft",start:"left"};let i,s,r;if(this.pageMode){const l=R(this.$el),o="HTML"===l.tagName?0:l[t.scroll],n=l.getBoundingClientRect(),a=this.$el.getBoundingClientRect()[t.start]-n[t.start];i=l,s=t.scroll,r=e+o+a}else i=this.$el,s=t.scroll,r=e;i[s]=r},itemsLimitError(){throw setTimeout((()=>{})),new Error("Rendered items limit reached")},sortViews(){this.pool.sort(((e,t)=>e.nr.index-t.nr.index))}}};const F={key:0,ref:"before",class:"vue-recycle-scroller__slot"},L={key:1,ref:"after",class:"vue-recycle-scroller__slot"};U.render=function(e,t,i,s,r,_){const b=l("ResizeObserver"),w=o("observe-visibility");return n((a(),c("div",{class:g(["vue-recycle-scroller",{ready:r.ready,"page-mode":i.pageMode,[`direction-${e.direction}`]:!0}]),onScrollPassive:t[0]||(t[0]=(...e)=>_.handleScroll&&_.handleScroll(...e))},[e.$slots.before?(a(),c("div",F,[d(e.$slots,"before")],512)):h("v-if",!0),(a(),u(v(i.listTag),{ref:"wrapper",style:z({["vertical"===e.direction?"minHeight":"minWidth"]:r.totalSize+"px"}),class:g(["vue-recycle-scroller__item-wrapper",i.listClass])},{default:m((()=>[(a(!0),c(p,null,f(r.pool,(t=>(a(),u(v(i.itemTag),y({key:t.nr.id,style:r.ready?{transform:`translate${"vertical"===e.direction?"Y":"X"}(${t.position}px) translate${"vertical"===e.direction?"X":"Y"}(${t.offset}px)`,width:i.gridItems?`${"vertical"===e.direction&&i.itemSecondarySize||i.itemSize}px`:void 0,height:i.gridItems?`${"horizontal"===e.direction&&i.itemSecondarySize||i.itemSize}px`:void 0}:null,class:["vue-recycle-scroller__item-view",[i.itemClass,{hover:!i.skipHover&&r.hoverKey===t.nr.key}]]},$(i.skipHover?{}:{mouseenter:()=>{r.hoverKey=t.nr.key},mouseleave:()=>{r.hoverKey=null}})),{default:m((()=>[d(e.$slots,"default",{item:t.item,index:t.nr.index,active:t.nr.used})])),_:2},1040,["style","class"])))),128)),d(e.$slots,"empty")])),_:3},8,["style","class"])),e.$slots.after?(a(),c("div",L,[d(e.$slots,"after")],512)):h("v-if",!0),S(b,{onNotify:_.handleResize},null,8,["onNotify"])],34)),[[w,_.handleVisibilityChange]])},U.__file="src/components/RecycleScroller.vue";var N={name:"DynamicScroller",components:{RecycleScroller:U},provide(){return"undefined"!=typeof ResizeObserver&&(this.$_resizeObserver=new ResizeObserver((e=>{requestAnimationFrame((()=>{if(Array.isArray(e))for(const t of e)if(t.target&&t.target.$_vs_onResize){let e,i;if(t.borderBoxSize){const s=t.borderBoxSize[0];e=s.inlineSize,i=s.blockSize}else e=t.contentRect.width,i=t.contentRect.height;t.target.$_vs_onResize(t.target.$_vs_id,e,i)}}))}))),{vscrollData:this.vscrollData,vscrollParent:this,vscrollResizeObserver:this.$_resizeObserver}},inheritAttrs:!1,props:{...M,minItemSize:{type:[Number,String],required:!0}},emits:["resize","visible"],data(){return{vscrollData:{active:!0,sizes:{},keyField:this.keyField,simpleArray:!1}}},computed:{simpleArray:k,itemsWithSize(){const e=[],{items:t,keyField:i,simpleArray:s}=this,r=this.vscrollData.sizes,l=t.length;for(let o=0;o<l;o++){const l=t[o],n=s?o:l[i];let a=r[n];void 0!==a||this.$_undefinedMap[n]||(a=0),e.push({item:l,id:n,size:a})}return e}},watch:{items(){this.forceUpdate()},simpleArray:{handler(e){this.vscrollData.simpleArray=e},immediate:!0},direction(e){this.forceUpdate(!0)},itemsWithSize(e,t){const i=this.$el.scrollTop;let s=0,r=0;const l=Math.min(e.length,t.length);for(let n=0;n<l&&!(s>=i);n++)s+=t[n].size||this.minItemSize,r+=e[n].size||this.minItemSize;const o=r-s;0!==o&&(this.$el.scrollTop+=o)}},beforeCreate(){var e;this.$_updates=[],this.$_undefinedSizes=0,this.$_undefinedMap={},this.$_events={all:e=e||new Map,on:function(t,i){var s=e.get(t);s&&s.push(i)||e.set(t,[i])},off:function(t,i){var s=e.get(t);s&&s.splice(s.indexOf(i)>>>0,1)},emit:function(t,i){(e.get(t)||[]).slice().map((function(e){e(i)})),(e.get("*")||[]).slice().map((function(e){e(t,i)}))}}},activated(){this.vscrollData.active=!0},deactivated(){this.vscrollData.active=!1},unmounted(){this.$_events.all.clear()},methods:{onScrollerResize(){this.$refs.scroller&&this.forceUpdate(),this.$emit("resize")},onScrollerVisible(){this.$_events.emit("vscroll:update",{force:!1}),this.$emit("visible")},forceUpdate(e=!1){(e||this.simpleArray)&&(this.vscrollData.sizes={}),this.$_events.emit("vscroll:update",{force:!0})},scrollToItem(e){const t=this.$refs.scroller;t&&t.scrollToItem(e)},getItemSize(e,t=void 0){const i=this.simpleArray?null!=t?t:this.items.indexOf(e):e[this.keyField];return this.vscrollData.sizes[i]||0},scrollToBottom(){if(this.$_scrollingToBottom)return;this.$_scrollingToBottom=!0;const e=this.$el;this.$nextTick((()=>{e.scrollTop=e.scrollHeight+5e3;const t=()=>{e.scrollTop=e.scrollHeight+5e3,requestAnimationFrame((()=>{e.scrollTop=e.scrollHeight+5e3,0===this.$_undefinedSizes?this.$_scrollingToBottom=!1:requestAnimationFrame(t)}))};requestAnimationFrame(t)}))}}};N.render=function(e,t,i,s,r,o){const n=l("RecycleScroller");return a(),u(n,y({ref:"scroller",items:o.itemsWithSize,"min-item-size":i.minItemSize,direction:e.direction,"key-field":"id","list-tag":e.listTag,"item-tag":e.itemTag},e.$attrs,{onResize:o.onScrollerResize,onVisible:o.onScrollerVisible}),{default:m((({item:t,index:i,active:s})=>[d(e.$slots,"default",_(b({item:t.item,index:i,active:s,itemWithSize:t})))])),before:m((()=>[d(e.$slots,"before")])),after:m((()=>[d(e.$slots,"after")])),empty:m((()=>[d(e.$slots,"empty")])),_:3},16,["items","min-item-size","direction","list-tag","item-tag","onResize","onVisible"])},N.__file="src/components/DynamicScroller.vue";var B={name:"DynamicScrollerItem",inject:["vscrollData","vscrollParent","vscrollResizeObserver"],props:{item:{required:!0},watchData:{type:Boolean,default:!1},active:{type:Boolean,required:!0},index:{type:Number,default:void 0},sizeDependencies:{type:[Array,Object],default:null},emitResize:{type:Boolean,default:!1},tag:{type:String,default:"div"}},emits:["resize"],computed:{id(){if(this.vscrollData.simpleArray)return this.index;if(this.vscrollData.keyField in this.item)return this.item[this.vscrollData.keyField];throw new Error(`keyField '${this.vscrollData.keyField}' not found in your item. You should set a valid keyField prop on your Scroller`)},size(){return this.vscrollData.sizes[this.id]||0},finalActive(){return this.active&&this.vscrollData.active}},watch:{watchData:"updateWatchData",id(e,t){if(this.$el.$_vs_id=this.id,this.size||this.onDataUpdate(),this.$_sizeObserved){const i=this.vscrollData.sizes[t],s=this.vscrollData.sizes[e];null!=i&&i!==s&&this.applySize(i)}},finalActive(e){this.size||(e?this.vscrollParent.$_undefinedMap[this.id]||(this.vscrollParent.$_undefinedSizes++,this.vscrollParent.$_undefinedMap[this.id]=!0):this.vscrollParent.$_undefinedMap[this.id]&&(this.vscrollParent.$_undefinedSizes--,this.vscrollParent.$_undefinedMap[this.id]=!1)),this.vscrollResizeObserver?e?this.observeSize():this.unobserveSize():e&&this.$_pendingVScrollUpdate===this.id&&this.updateSize()}},created(){if(!this.$isServer&&(this.$_forceNextVScrollUpdate=null,this.updateWatchData(),!this.vscrollResizeObserver)){for(const e in this.sizeDependencies)this.$watch((()=>this.sizeDependencies[e]),this.onDataUpdate);this.vscrollParent.$_events.on("vscroll:update",this.onVscrollUpdate)}},mounted(){this.finalActive&&(this.updateSize(),this.observeSize())},beforeUnmount(){this.vscrollParent.$_events.off("vscroll:update",this.onVscrollUpdate),this.unobserveSize()},methods:{updateSize(){this.finalActive?this.$_pendingSizeUpdate!==this.id&&(this.$_pendingSizeUpdate=this.id,this.$_forceNextVScrollUpdate=null,this.$_pendingVScrollUpdate=null,this.computeSize(this.id)):this.$_forceNextVScrollUpdate=this.id},updateWatchData(){this.watchData&&!this.vscrollResizeObserver?this.$_watchData=this.$watch("item",(()=>{this.onDataUpdate()}),{deep:!0}):this.$_watchData&&(this.$_watchData(),this.$_watchData=null)},onVscrollUpdate({force:e}){!this.finalActive&&e&&(this.$_pendingVScrollUpdate=this.id),this.$_forceNextVScrollUpdate!==this.id&&!e&&this.size||this.updateSize()},onDataUpdate(){this.updateSize()},computeSize(e){this.$nextTick((()=>{if(this.id===e){const e=this.$el.offsetWidth,t=this.$el.offsetHeight;this.applyWidthHeight(e,t)}this.$_pendingSizeUpdate=null}))},applyWidthHeight(e,t){const i=~~("vertical"===this.vscrollParent.direction?t:e);i&&this.size!==i&&this.applySize(i)},applySize(e){this.vscrollParent.$_undefinedMap[this.id]&&(this.vscrollParent.$_undefinedSizes--,this.vscrollParent.$_undefinedMap[this.id]=void 0),this.vscrollData.sizes[this.id]=e,this.emitResize&&this.$emit("resize",this.id)},observeSize(){this.vscrollResizeObserver&&(this.$_sizeObserved||(this.vscrollResizeObserver.observe(this.$el),this.$el.$_vs_id=this.id,this.$el.$_vs_onResize=this.onResize,this.$_sizeObserved=!0))},unobserveSize(){this.vscrollResizeObserver&&this.$_sizeObserved&&(this.vscrollResizeObserver.unobserve(this.$el),this.$el.$_vs_onResize=void 0,this.$_sizeObserved=!1)},onResize(e,t,i){this.id===e&&this.applyWidthHeight(t,i)}},render(){return r(this.tag,this.$slots.default())}};B.__file="src/components/DynamicScrollerItem.vue";var H={version:"2.0.0-beta.8",install:function(e,t){var i=Object.assign({},{installComponents:!0,componentsPrefix:""},t);for(var s in i)void 0!==i[s]&&(w[s]=i[s]);i.installComponents&&function(e,t){e.component("".concat(t,"recycle-scroller"),U),e.component("".concat(t,"RecycleScroller"),U),e.component("".concat(t,"dynamic-scroller"),N),e.component("".concat(t,"DynamicScroller"),N),e.component("".concat(t,"dynamic-scroller-item"),B),e.component("".concat(t,"DynamicScrollerItem"),B)}(e,i.componentsPrefix)}};export{H as p};
