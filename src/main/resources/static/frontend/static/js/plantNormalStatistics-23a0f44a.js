import{_ as e}from"./MyForm-5e5c0ec8.js";import{C as t,i as o,N as s}from"./quasar-b3f06d8a.js";import{c as a,_ as r}from"./dateUtil-77b84bd5.js";import"./vue-5bfa3a54.js";import i from"./summary-7819ddff.js";import p from"./alarm-a364ff72.js";import l from"./electricityIncome-90cd5036.js";import{d as m}from"./dayjs-d60cc07f.js";import{F as n}from"./@vueuse-af86c621.js";import{X as c}from"./statisticReportApi-dc9fa149.js";import{g as u}from"./api-b858041e.js";import{f as j}from"./formUtil-a2e6828b.js";import{_ as v}from"./index-8cc8d4b8.js";import{e as d,v as f}from"./naive-ui-0ee0b8c3.js";import{h as w,j as b,L as x,m as y,o as g,c as h,a as k,x as D,a8 as U,aa as _,b as T,f as V,r as z,C as F,D as S}from"./@vue-5e5cdef9.js";import"./lodash-6d99edc3.js";import"./@babel-f3c0a00c.js";import"./@vicons-f32a0bdb.js";import"./notification-950a5f80.js";import"./proxyUtil-6f30f7ef.js";import"./xlsx-c1bdd32b.js";import"./exportEchart-130b148d.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./exceljs-b3a0e81d.js";import"./file-saver-8735aaf5.js";import"./echartsInit-0067e609.js";import"./menuStore-26f8ddd8.js";import"./vue-router-6159329f.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./icons-95011f8c.js";import"./taskUitls-36951a34.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./element-plus-d975be09.js";import"./lodash-es-ea7deab5.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./xe-utils-fe99d42a.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-7bbbb435.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./date-fns-tz-0cc073c8.js";const C=e=>(F("data-v-c6a0a5b8"),e=e(),S(),e),E={class:"tw-h-full tw-w-full tw-p-2 tw-flex tw-flex-col tw-min-w-[1280]"},L={class:"tw-flex tw-items-center tw-bg-green-600"},N=C((()=>k("span",{class:"tw-text-[#fff] tw-mr-2 tw-text-center"}," 建站 ",-1))),q=C((()=>k("span",{class:"tw-text-[#fff] tw-mr-2 tw-text-center"}," 发电 ",-1))),I=v({__name:"plantNormalStatistics",setup(v){w("汇总统计");const F=b({"汇总统计":x(i),"发电量收益":x(l),"设备统计":x(p)}),S=b([{formType:"slot",label:"",prop:"id",list:[],value:w("")},{formType:"slot",label:"",prop:"electricityPrice",value:.45},{formType:"slot",label:"",prop:"date",value:a([m().subtract(1,"year").valueOf(),Date.now()])},{formType:"slot",label:"",prop:"power",value:a([m().subtract(1,"month").valueOf(),Date.now()])},{formType:"button",label:"查询",value:!1,prop:"check",invoke:I},{formType:"space"},{formType:"button",label:"重置",value:!1,prop:"reset",invoke:()=>{page.page=1,page.pageSize=10}},{formType:"button",label:"导出",value:!1,prop:"export",invoke:async function(){const e=j.getValue(S);return e.date=e.date.date,e.power=e.power.date,await C.componentDom.value.exportFile(e),void j.setValue(S,"export",!1)}},{formType:"slot",prop:"tabs",value:"汇总统计"}]),C={createDateDom:n("createDateDom"),powerDateDom:n("powerDateDom"),componentDom:n("componentDom")};async function I(){const e=j.getValue(S);e.date=e.date.date,e.power=e.power.date,j.setValue(S,"check",!0),await C.componentDom.value.getData(e),j.setValue(S,"check",!1)}return y((async()=>{await async function(){const e=await c();!function e(t){var o;for(const s of t)(null==(o=null==s?void 0:s.children)?void 0:o.length)?e(s.children):delete s.children}(u(e).data),S[0].list=u(e).data,S[0].value=S[0].list[0].id}(),await I()})),(a,i)=>{const p=d,l=f,m=r,n=t,c=o,u=s,j=e;return g(),h("div",E,[k("div",L,[D(j,{title:"",formList:T(S),page:{}},{id:U((e=>[D(p,{class:"tw-w-[200px] tw-mx-1",options:e.scope.list,"key-field":"id","label-field":"projectName",value:e.scope.value,"onUpdate:value":t=>e.scope.value=t,placeholder:"项目名称"},null,8,["options","value","onUpdate:value"])])),electricityPrice:U((e=>[D(l,{class:"tw-w-[200px] tw-mx-2",step:"0.01",value:e.scope.value,"onUpdate:value":t=>e.scope.value=t,min:.01,precision:2},{prefix:U((()=>[_(" 电价(￥): ")])),_:2},1032,["value","onUpdate:value"])])),date:U((e=>[N,D(m,{date:e.scope.value,class:"tw-w-[300px] tw-mr-2"},null,8,["date"])])),power:U((e=>[q,D(m,{date:e.scope.value,class:"tw-w-[300px] tw-mr-2"},null,8,["date"])])),tabs:U((e=>[D(u,{modelValue:e.scope.value,"onUpdate:modelValue":t=>e.scope.value=t,shrink:"",stretch:"",class:"tw-text-white",dense:"","active-color":"black","active-bg-color":"white","indicator-color":"white",onClick:I},{default:U((()=>[D(n,{vertical:""}),D(c,{name:"汇总统计",label:"汇总统计"}),D(n,{vertical:""}),D(c,{name:"发电量收益",label:"发电量收益"}),D(n,{vertical:""}),D(c,{name:"设备统计",label:"设备统计"}),D(n,{vertical:""})])),_:2},1032,["modelValue","onUpdate:modelValue"])])),_:1},8,["formList"])]),(g(),V(z(T(F)[T(S).at(-1).value]),{ref:"componentDom"},null,512))])}}},[["__scopeId","data-v-c6a0a5b8"]]);export{I as default};
