import{j as t}from"./quasar-b3f06d8a.js";import{r as e,h as s,g as o,j as r}from"./element-plus-d975be09.js";import"./vue-5bfa3a54.js";import{_ as i}from"./index-8cc8d4b8.js";import{as as l,o as a,c as n,k as p,F as u,f as m,a8 as c,x as d,r as j,a as f,t as w,a9 as v,ab as h,b as g,h as b,e as x,m as k,az as _,y,q as C,aH as $,H as z,C as S,D as q}from"./@vue-5e5cdef9.js";import{x as I}from"./menuStore-26f8ddd8.js";import{e as R,f as V,P as M,L as U}from"./@vicons-f32a0bdb.js";import{x as A}from"./homeStore-b8fb7ab6.js";import{x as W,b as D}from"./api-b858041e.js";import{I as F,J as H}from"./@vueuse-af86c621.js";import L from"./operaTip-c045533b.js";import{m as P}from"./notification-950a5f80.js";import{u as T}from"./naive-ui-0ee0b8c3.js";import"./lodash-es-ea7deab5.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./dayjs-d60cc07f.js";import"./@babel-f3c0a00c.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./vue-router-6159329f.js";import"./bignumber.js-a537a5ca.js";import"./xe-utils-fe99d42a.js";import"./js-cookie-8253c38e.js";import"./axios-84f1a956.js";import"./spark-md5-022b35d0.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-7bbbb435.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./lodash-6d99edc3.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./icons-95011f8c.js";import"./homeApi-54bb989e.js";import"./index-15186f59.js";import"./taskUitls-36951a34.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./date-fns-tz-0cc073c8.js";const Z={class:"tw-inline-block tw-w-[150px]"},B={class:"tw-inline-block tw-w-[150px] hover:tw-text-[#ff6d00]"},E=i({__name:"subMenu",props:{options:{type:Array,default:[]}},setup(t){const r=t;return(t,i)=>{const g=e,b=l("subMenu",!0),x=s,k=o;return a(!0),n(u,null,p(r.options,(t=>(a(),n(u,null,[t.children&&t.children.length>0?(a(),m(x,{key:t.label,index:t.label},{title:c((()=>[d(g,null,{default:c((()=>[(a(),m(j(t.icon)))])),_:2},1024),f("span",Z,w(t.label),1)])),default:c((()=>[d(b,{options:t.children},null,8,["options"])])),_:2},1032,["index"])):v((a(),m(k,{key:t.path,index:t.path,onClick:e=>function(t){t.path.startsWith("https://")?window.open(t.path,"_blank"):router.push(t.path)}(t)},{title:c((()=>[d(g,null,{default:c((()=>[(a(),m(j(t.icon),{class:"hover:tw-text-[#ff6d00]"}))])),_:2},1024),f("span",B,w(t.key),1)])),_:2},1032,["index","onClick"])),[[h,t.show]])],64)))),256)}}},[["__scopeId","data-v-30f221d1"]]),J=i({__name:"myMenu",setup(t){const e=I();return(t,s)=>{const i=o,l=E,n=r;return a(),m(n,{collapse:g(e).collapsed,"active-text-color":"#ff6d00","default-active":t.$router.currentRoute.value.path,"unique-opened":!0},{default:c((()=>[d(i,{onClick:s[0]||(s[0]=t=>g(e).collapsed=!g(e).collapsed)},{default:c((()=>[d(g(R),{class:"tw-w-[30px] tw-mx-auto"})])),_:1}),d(l,{options:g(e).menuOptions},null,8,["options"])])),_:1},8,["collapse","default-active"])}}},[["__scopeId","data-v-a12bd00f"]]),N=t=>(S("data-v-5f91db58"),t=t(),q(),t),O={class:"tw-w-full tw-h-full min-w-1200 tw-bg-blue-400 tw-flex"},Q={class:"tw-flex-1 tw-h-full tw-w-[80%]"},G={class:"tw-text-base tw-font-bold tw--ml-[20%]"},K={class:"tw-h-full"},X=N((()=>f("span",null,"全屏",-1))),Y=N((()=>f("span",null,"退出登录",-1))),tt=i({__name:"index",setup(e){function s(t){if(t.route.fullPath.includes("home"))return!1;return!["电站详情"].includes(t.route.name)}b(null);const o=b();F(o,{initialValue:{x:innerWidth/4.2,y:80},preventDefault:!0});const r=I(),i=W(),p=b(),u=b(),h=b(!1),S=A(),{isFullscreen:q,enter:R,exit:Z,toggle:B}=H(u);async function E(){await router.replace("/login"),W().$reset(),A().$reset(),D().$reset(),I().$reset(),sessionStorage.clear(),location.origin.includes("man")||router.go(),P.success("退出登录")}r.menuRef=p;const N=x((()=>"综合大屏"==router.currentRoute.value.name?{header:"tw-h-0",section:"tw-h-full"}:{header:"tw-h-1/12 tw-pt-1",section:"tw-h-11/12"}));return k((()=>{r.collapsed=!1,S.init()})),(e,o)=>{const p=J,b=T,x=l("router-view"),k=t,S=_("sunken");return a(),n("div",O,[d(p),f("section",Q,[f("header",{class:y(["tw-w-full tw-shadow-2xl tw-flex tw-justify-between",g(N).header])},[v((a(),n("div",null,[f("div",{class:"img",onClick:o[0]||(o[0]=(...t)=>e.$router.back&&e.$router.back(...t)),style:C({backgroundImage:`url('${g(i).info.imgUrl}')`})},null,4)])),[[S]]),f("div",G,w(["电站详情","逆变器详情","实时数据"].includes(g(r).value)?g(r).title:""),1),f("div",K,[d(b,{trigger:"hover"},{trigger:c((()=>[d(g(V),{onClick:g(B),class:"tw-text-yellow-500 tw-mr-2 tw-h-full tw-pb-1 hover:tw-bg-gray-200"},null,8,["onClick"])])),default:c((()=>[X])),_:1}),d(b,{trigger:"hover"},{trigger:c((()=>[d(g(M),{class:"tw-text-yellow-500 tw-mr-2 tw-h-full tw-pb-1 hover:tw-bg-gray-200"})])),default:c((()=>[f("span",null," 账号： "+w(g(i).info.username),1)])),_:1}),d(b,{trigger:"hover"},{trigger:c((()=>[d(g(U),{onClick:E,class:"tw-text-yellow-500 tw-mr-2 tw-h-full tw-pb-1 hover:tw-bg-gray-200"})])),default:c((()=>[Y])),_:1})])],2),f("section",{class:y(["tw-bg-white tw-w-full",g(N).section]),ref_key:"fullScreenRef",ref:u},[d(x,{class:"tw-h-full tw-w-full"},{default:c((t=>[s(t)?(a(),m($,{key:0},[(a(),m(j(t.Component)))],1024)):(a(),m(j(t.Component),{key:1}))])),_:1})],2)]),d(k,{modelValue:g(h),"onUpdate:modelValue":o[1]||(o[1]=t=>z(h)?h.value=t:null),persistent:""},{default:c((()=>[d(L)])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-5f91db58"]]);export{tt as default};
