import"./vue-5bfa3a54.js";import{d as t,h as e,i as n,j as o,e as i,w as r,p as s,m as a,o as l,c,g as u,b as p,y as d,l as h,q as f,a5 as g,n as m,a4 as v,a as y,a9 as b,ab as x,x as w,a6 as E,F as S,k as T,f as _,a8 as z}from"./@vue-5e5cdef9.js";var O=Object.defineProperty,I=(t,e,n)=>(((t,e,n)=>{e in t?O(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n})(t,"symbol"!=typeof e?e+"":e,n),n);const P=Symbol("$emitter");var M=(t=>(t.DOWN="DOWN",t.LEFT="LEFT",t.RIGHT="RIGHT",t.UP="UP",t))(M||{});const D=t=>{const e=Array(t.length);for(let n=0;n<t.length;n++)e[n]=k(t[n]);return e},k=t=>JSON.parse(JSON.stringify(t)),A=(t,e)=>!(t===e||t.x+t.w<=e.x||t.x>=e.x+e.w||t.y+t.h<=e.y||t.y>=e.y+e.h),C=(t,e)=>{if(!t)return;const n=L(t),o=B(t),i=Array(t.length);for(let r=0;r<o.length;r++){let s=o[r];s.static||(s=R(n,s,e),n.push(s)),i[t.indexOf(s)]=s,s.moved=!1}return i},R=(t,e,n)=>{if(n)for(;e.y>0&&!j(t,e);)e.y--;let o;for(;o=j(t,e);)e.y=o.y+o.h;return e},N=(t,e)=>t.filter((t=>A(t,e))),j=(t,e)=>{for(let n=0,o=t.length;n<o;n++)if(A(t[n],e))return t[n]},H=(t,e)=>t.filter((t=>t.i===e))[0],L=t=>t.filter((t=>t.static)),F=(t,e,n,o,i,r,s)=>{var a;if(e.static)return t;const l=e.x,c=e.y,u={DOWN:c<o,LEFT:l>n,RIGHT:l<n,UP:c>o};e.x=n,e.y=o,e.moved=!0;let p=B(t);u.UP&&(p=p.reverse());const d=N(p,e);if(s&&d.length)return e.x=l,e.y=c,e.moved=!1,t;for(let h=0;h<d.length;h++){const n=d[h];if(n.moved||e.y>n.y&&e.y-n.y>n.h/4)continue;const o=null==(a=Object.keys(u).filter((t=>u[t])))?void 0:a[0];t=n.static?W(t,n,e,i,o,r):W(t,e,n,i,o,r)}return t},W=(t,e,n,o,i,r)=>{if(o){const i={h:n.h,i:-1,w:n.w,x:n.x,y:Math.max(e.y-n.h,0)};if(!j(t,i))return F(t,n,i.x,i.y,o,r,!1)}const s={$default:{x:n.x,y:n.y+1},[M.LEFT]:[n.x+e.w,e.y],[M.RIGHT]:[n.x-e.w,e.y],[M.UP]:[n.x,n.y+e.h],[M.DOWN]:[n.x,n.y-e.h]};if(r){const t=i===M.LEFT||i===M.RIGHT;if(i in s&&!(t&&e.w<n.w&&e.x!==n.x)){const[t,e]=s[i];s.$default.x=t,s.$default.y=e}}return F(t,n,s.$default.x,s.$default.y,r,!1)},B=t=>[...t].sort(((t,e)=>t.y===e.y&&t.x===e.x?0:t.y>e.y||t.y===e.y&&t.x>e.x?1:-1)),$=(t,e,n,o,i,r,s)=>{if(Object.prototype.hasOwnProperty.call(e,o))return D(e[o]||[]);let a=t;const l=X(n),c=l.slice(l.indexOf(o));for(let u=0;u<c.length;u++){const t=c[u];if(Object.prototype.hasOwnProperty.call(e,t)){a=e[t];break}}return a=D(a||[]),C(((t,e)=>{const n=L(t);for(let o=0;o<t.length;o++){const i=t[o];if(i.x+i.w>e.cols&&(i.x=e.cols-i.w),i.x<0&&(i.x=0,i.w=e.cols),i.static)for(;j(n,i);)i.y++;else n.push(i)}return t})(a,{cols:r}),s)},q=(t,e)=>{var n;return null!=(n=e[t])?n:void 0},X=t=>Object.keys(t).sort(((e,n)=>{var o,i;return(null!=(o=t[e])?o:1)-(null!=(i=t[n])?i:1)})),Y=t=>!(!t||!t.Window)&&t instanceof t.Window;let V,G;function U(t){V=t;const e=t.document.createTextNode("");e.ownerDocument!==t.document&&"function"==typeof t.wrap&&t.wrap(e)===e&&(t=t.wrap(t)),G=t}function K(t){return Y(t)?t:(t.ownerDocument||t).defaultView||G.window}typeof window<"u"&&window&&U(window);const J=t=>!!t&&"object"==typeof t,Q=t=>"function"==typeof t,Z={window:t=>t===G||Y(t),docFrag:t=>J(t)&&11===t.nodeType,object:J,func:Q,number:t=>"number"==typeof t,bool:t=>"boolean"==typeof t,string:t=>"string"==typeof t,element:t=>{if(!t||"object"!=typeof t)return!1;const e=K(t)||G;return/object|function/.test(typeof Element)?t instanceof Element||t instanceof e.Element:1===t.nodeType&&"string"==typeof t.nodeName},plainObject:t=>J(t)&&!!t.constructor&&/function Object\b/.test(t.constructor.toString()),array:t=>J(t)&&typeof t.length<"u"&&Q(t.splice)};function tt({interaction:t}){if("drag"!==t.prepared.name)return;const e=t.prepared.axis;"x"===e?(t.coords.cur.page.y=t.coords.start.page.y,t.coords.cur.client.y=t.coords.start.client.y,t.coords.velocity.client.y=0,t.coords.velocity.page.y=0):"y"===e&&(t.coords.cur.page.x=t.coords.start.page.x,t.coords.cur.client.x=t.coords.start.client.x,t.coords.velocity.client.x=0,t.coords.velocity.page.x=0)}function et({iEvent:t,interaction:e}){if("drag"!==e.prepared.name)return;const n=e.prepared.axis;if("x"===n||"y"===n){const o="x"===n?"y":"x";t.page[o]=e.coords.start.page[o],t.client[o]=e.coords.start.client[o],t.delta[o]=0}}const nt={id:"actions/drag",install:function(t){const{actions:e,Interactable:n,defaults:o}=t;n.prototype.draggable=nt.draggable,e.map.drag=nt,e.methodDict.drag="draggable",o.actions.drag=nt.defaults},listeners:{"interactions:before-action-move":tt,"interactions:action-resume":tt,"interactions:action-move":et,"auto-start:check":t=>{const{interaction:e,interactable:n,buttons:o}=t,i=n.options.drag;if(i&&i.enabled&&(!e.pointerIsDown||!/mouse|pointer/.test(e.pointerType)||o&n.options.drag.mouseButtons))return t.action={name:"drag",axis:"start"===i.lockAxis?i.startAxis:i.lockAxis},!1}},draggable:function(t){return Z.object(t)?(this.options.drag.enabled=!1!==t.enabled,this.setPerAction("drag",t),this.setOnEvents("drag",t),/^(xy|x|y|start)$/.test(t.lockAxis)&&(this.options.drag.lockAxis=t.lockAxis),/^(xy|x|y)$/.test(t.startAxis)&&(this.options.drag.startAxis=t.startAxis),this):Z.bool(t)?(this.options.drag.enabled=t,this):this.options.drag},beforeMove:tt,move:et,defaults:{startAxis:"xy",lockAxis:"xy"},getCursor:()=>"move"},ot=nt,it={init:function(t){const e=t;it.document=e.document,it.DocumentFragment=e.DocumentFragment||rt,it.SVGElement=e.SVGElement||rt,it.SVGSVGElement=e.SVGSVGElement||rt,it.SVGElementInstance=e.SVGElementInstance||rt,it.Element=e.Element||rt,it.HTMLElement=e.HTMLElement||it.Element,it.Event=e.Event,it.Touch=e.Touch||rt,it.PointerEvent=e.PointerEvent||e.MSPointerEvent},document:null,DocumentFragment:null,SVGElement:null,SVGSVGElement:null,SVGElementInstance:null,Element:null,HTMLElement:null,Event:null,Touch:null,PointerEvent:null};function rt(){}const st=it;const at={init:function(t){const e=st.Element,n=t.navigator||{};at.supportsTouch="ontouchstart"in t||Z.func(t.DocumentTouch)&&st.document instanceof t.DocumentTouch,at.supportsPointerEvent=!1!==n.pointerEnabled&&!!st.PointerEvent,at.isIOS=/iP(hone|od|ad)/.test(n.platform),at.isIOS7=/iP(hone|od|ad)/.test(n.platform)&&/OS 7[^\d]/.test(n.appVersion),at.isIe9=/MSIE 9/.test(n.userAgent),at.isOperaMobile="Opera"===n.appName&&at.supportsTouch&&/Presto/.test(n.userAgent),at.prefixedMatchesSelector="matches"in e.prototype?"matches":"webkitMatchesSelector"in e.prototype?"webkitMatchesSelector":"mozMatchesSelector"in e.prototype?"mozMatchesSelector":"oMatchesSelector"in e.prototype?"oMatchesSelector":"msMatchesSelector",at.pEventTypes=at.supportsPointerEvent?st.PointerEvent===t.MSPointerEvent?{up:"MSPointerUp",down:"MSPointerDown",over:"mouseover",out:"mouseout",move:"MSPointerMove",cancel:"MSPointerCancel"}:{up:"pointerup",down:"pointerdown",over:"pointerover",out:"pointerout",move:"pointermove",cancel:"pointercancel"}:null,at.wheelEvent=st.document&&"onmousewheel"in st.document?"mousewheel":"wheel"},supportsTouch:null,supportsPointerEvent:null,isIOS7:null,isIOS:null,isIe9:null,isOperaMobile:null,prefixedMatchesSelector:null,pEventTypes:null,wheelEvent:null};const lt=at;function ct(t,e){if(t.contains)return t.contains(e);for(;e;){if(e===t)return!0;e=e.parentNode}return!1}function ut(t,e){for(;Z.element(t);){if(dt(t,e))return t;t=pt(t)}return null}function pt(t){let e=t.parentNode;if(Z.docFrag(e)){for(;(e=e.host)&&Z.docFrag(e););return e}return e}function dt(t,e){return G!==V&&(e=e.replace(/\/deep\//g," ")),t[lt.prefixedMatchesSelector](e)}const ht=t=>t.parentNode||t.host;function ft(t,e){const n=[];let o,i=t;for(;(o=ht(i))&&i!==e&&o!==i.ownerDocument;)n.unshift(i),i=o;return n}function gt(t,e){return(parseInt(K(t).getComputedStyle(t).zIndex,10)||0)>=(parseInt(K(e).getComputedStyle(e).zIndex,10)||0)}function mt(t,e,n){for(;Z.element(t);){if(dt(t,e))return!0;if((t=pt(t))===n)return dt(t,e)}return!1}function vt(t){return t.correspondingUseElement||t}function yt(t){const e=t instanceof st.SVGElement?t.getBoundingClientRect():t.getClientRects()[0];return e&&{left:e.left,right:e.right,top:e.top,bottom:e.bottom,width:e.width||e.right-e.left,height:e.height||e.bottom-e.top}}function bt(t){const e=yt(t);if(!lt.isIOS7&&e){const n=function(t){return{x:(t=t||G).scrollX||t.document.documentElement.scrollLeft,y:t.scrollY||t.document.documentElement.scrollTop}}(K(t));e.left+=n.x,e.right+=n.x,e.top+=n.y,e.bottom+=n.y}return e}function xt(t){const e=[];for(;t;)e.push(t),t=pt(t);return e}function wt(t){return!!Z.string(t)&&(st.document.querySelector(t),!0)}function Et(t,e){for(const n in e)t[n]=e[n];return t}function St(t,e,n){return"parent"===t?pt(n):"self"===t?e.getRect(n):ut(n,t)}function Tt(t,e,n,o){let i=t;return Z.string(i)?i=St(i,e,n):Z.func(i)&&(i=i(...o)),Z.element(i)&&(i=bt(i)),i}function _t(t){return t&&{x:"x"in t?t.x:t.left,y:"y"in t?t.y:t.top}}function zt(t){return t&&!("x"in t&&"y"in t)&&((t=Et({},t)).x=t.left||0,t.y=t.top||0,t.width=t.width||(t.right||0)-t.x,t.height=t.height||(t.bottom||0)-t.y),t}function Ot(t,e,n){t.left&&(e.left+=n.x),t.right&&(e.right+=n.x),t.top&&(e.top+=n.y),t.bottom&&(e.bottom+=n.y),e.width=e.right-e.left,e.height=e.bottom-e.top}function It(t,e,n){const o=t.options[n];return _t(Tt(o&&o.origin||t.options.origin,t,e,[t&&e]))||{x:0,y:0}}function Pt(t,e,n){if(n=n||{},Z.string(t)&&-1!==t.search(" ")&&(t=Mt(t)),Z.array(t))return t.reduce(((t,o)=>Et(t,Pt(o,e,n))),n);if(Z.object(t)&&(e=t,t=""),Z.func(e))n[t]=n[t]||[],n[t].push(e);else if(Z.array(e))for(const o of e)Pt(t,o,n);else if(Z.object(e))for(const o in e){Pt(Mt(o).map((e=>`${t}${e}`)),e[o],n)}return n}function Mt(t){return t.trim().split(/ +/)}const Dt=(t,e)=>Math.sqrt(t*t+e*e);function kt(t,e){t.__set||(t.__set={});for(const n in e)"function"!=typeof t[n]&&"__set"!==n&&Object.defineProperty(t,n,{get:()=>n in t.__set?t.__set[n]:t.__set[n]=e[n],set(e){t.__set[n]=e},configurable:!0});return t}function At(t,e){t.page=t.page||{},t.page.x=e.page.x,t.page.y=e.page.y,t.client=t.client||{},t.client.x=e.client.x,t.client.y=e.client.y,t.timeStamp=e.timeStamp}function Ct(t){t.page.x=0,t.page.y=0,t.client.x=0,t.client.y=0}function Rt(t){return t instanceof st.Event||t instanceof st.Touch}function Nt(t,e,n){return t=t||"page",(n=n||{}).x=e[t+"X"],n.y=e[t+"Y"],n}function jt(t,e){return e=e||{x:0,y:0},lt.isOperaMobile&&Rt(t)?(Nt("screen",t,e),e.x+=window.scrollX,e.y+=window.scrollY):Nt("page",t,e),e}function Ht(t){return Z.number(t.pointerId)?t.pointerId:t.identifier}function Lt(t,e,n){const o=e.length>1?Wt(e):e[0];jt(o,t.page),function(t,e){e=e||{},lt.isOperaMobile&&Rt(t)?Nt("screen",t,e):Nt("client",t,e)}(o,t.client),t.timeStamp=n}function Ft(t){const e=[];return Z.array(t)?(e[0]=t[0],e[1]=t[1]):"touchend"===t.type?1===t.touches.length?(e[0]=t.touches[0],e[1]=t.changedTouches[0]):0===t.touches.length&&(e[0]=t.changedTouches[0],e[1]=t.changedTouches[1]):(e[0]=t.touches[0],e[1]=t.touches[1]),e}function Wt(t){const e={pageX:0,pageY:0,clientX:0,clientY:0,screenX:0,screenY:0};for(const n of t)for(const t in e)e[t]+=n[t];for(const n in e)e[n]/=t.length;return e}function Bt(t){if(!t.length)return null;const e=Ft(t),n=Math.min(e[0].pageX,e[1].pageX),o=Math.min(e[0].pageY,e[1].pageY),i=Math.max(e[0].pageX,e[1].pageX),r=Math.max(e[0].pageY,e[1].pageY);return{x:n,y:o,left:n,top:o,right:i,bottom:r,width:i-n,height:r-o}}function $t(t,e){const n=e+"X",o=e+"Y",i=Ft(t),r=i[0][n]-i[1][n],s=i[0][o]-i[1][o];return Dt(r,s)}function qt(t,e){const n=e+"X",o=e+"Y",i=Ft(t),r=i[1][n]-i[0][n],s=i[1][o]-i[0][o];return 180*Math.atan2(s,r)/Math.PI}function Xt(t){return Z.string(t.pointerType)?t.pointerType:Z.number(t.pointerType)?[void 0,void 0,"touch","pen","mouse"][t.pointerType]:/touch/.test(t.type||"")||t instanceof st.Touch?"touch":"mouse"}function Yt(t){const e=Z.func(t.composedPath)?t.composedPath():t.path;return[vt(e?e[0]:t.target),vt(t.currentTarget)]}function Vt(t){return{coords:t,get page(){return this.coords.page},get client(){return this.coords.client},get timeStamp(){return this.coords.timeStamp},get pageX(){return this.coords.page.x},get pageY(){return this.coords.page.y},get clientX(){return this.coords.client.x},get clientY(){return this.coords.client.y},get pointerId(){return this.coords.pointerId},get target(){return this.coords.target},get type(){return this.coords.type},get pointerType(){return this.coords.pointerType},get buttons(){return this.coords.buttons},preventDefault(){}}}class Gt{constructor(t){I(this,"immediatePropagationStopped",!1),I(this,"propagationStopped",!1),this._interaction=t}preventDefault(){}stopPropagation(){this.propagationStopped=!0}stopImmediatePropagation(){this.immediatePropagationStopped=this.propagationStopped=!0}}Object.defineProperty(Gt.prototype,"interaction",{get(){return this._interaction._proxy},set(){}});const Ut=(t,e)=>{for(const n of e)t.push(n);return t},Kt=t=>Ut([],t),Jt=(t,e)=>{for(let n=0;n<t.length;n++)if(e(t[n],n,t))return n;return-1},Qt=(t,e)=>t[Jt(t,e)];class Zt extends Gt{constructor(t,e,n){super(e._interaction),I(this,"dropzone"),I(this,"dragEvent"),I(this,"relatedTarget"),I(this,"draggable"),I(this,"propagationStopped",!1),I(this,"immediatePropagationStopped",!1);const{element:o,dropzone:i}="dragleave"===n?t.prev:t.cur;this.type=n,this.target=o,this.currentTarget=o,this.dropzone=i,this.dragEvent=e,this.relatedTarget=e.target,this.draggable=e.interactable,this.timeStamp=e.timeStamp}reject(){const{dropState:t}=this._interaction;if("dropactivate"===this.type||this.dropzone&&t.cur.dropzone===this.dropzone&&t.cur.element===this.target)if(t.prev.dropzone=this.dropzone,t.prev.element=this.target,t.rejected=!0,t.events.enter=null,this.stopImmediatePropagation(),"dropactivate"===this.type){const e=t.activeDrops,n=Jt(e,(({dropzone:t,element:e})=>t===this.dropzone&&e===this.target));t.activeDrops.splice(n,1);const o=new Zt(t,this.dragEvent,"dropdeactivate");o.dropzone=this.dropzone,o.target=this.target,this.dropzone.fire(o)}else this.dropzone.fire(new Zt(t,this.dragEvent,"dragleave"))}preventDefault(){}stopPropagation(){this.propagationStopped=!0}stopImmediatePropagation(){this.immediatePropagationStopped=this.propagationStopped=!0}}function te(t,e){for(const{dropzone:n,element:o}of t.slice())e.dropzone=n,e.target=o,n.fire(e),e.propagationStopped=e.immediatePropagationStopped=!1}function ee(t,e){const n=function({interactables:t},e){const n=[];for(const o of t.list){if(!o.options.drop.enabled)continue;const t=o.options.drop.accept;if(Z.element(t)&&t!==e||Z.string(t)&&!dt(e,t)||Z.func(t)&&!t({dropzone:o,draggableElement:e}))continue;const i=Z.string(o.target)?o._context.querySelectorAll(o.target):Z.array(o.target)?o.target:[o.target];for(const r of i)r!==e&&n.push({dropzone:o,element:r,rect:o.getRect(r)})}return n}(t,e);for(const o of n)o.rect=o.dropzone.getRect(o.element);return n}function ne({dropState:t,interactable:e,element:n},o,i){const r=[];for(const{dropzone:a,element:l,rect:c}of t.activeDrops)r.push(a.dropCheck(o,i,e,n,l,c)?l:null);const s=function(t){let e,n=[];for(let o=0;o<t.length;o++){const i=t[o],r=t[e];if(!i||o===e)continue;if(!r){e=o;continue}const s=ht(i),a=ht(r);if(s===i.ownerDocument)continue;if(a===i.ownerDocument){e=o;continue}if(s===a){gt(i,r)&&(e=o);continue}let l;if(n=n.length?n:ft(r),r instanceof st.HTMLElement&&i instanceof st.SVGElement&&!(i instanceof st.SVGSVGElement)){if(i===a)continue;l=i.ownerSVGElement}else l=i;const c=ft(l,r.ownerDocument);let u=0;for(;c[u]&&c[u]===n[u];)u++;const p=[c[u-1],c[u],n[u]];if(p[0]){let t=p[0].lastChild;for(;t;){if(t===p[1]){e=o,n=c;break}if(t===p[2])break;t=t.previousSibling}}}return e}(r);return t.activeDrops[s]||null}function oe(t,e,n){const{dropState:o}=t,i={enter:null,leave:null,activate:null,deactivate:null,move:null,drop:null};return"dragstart"===n.type&&(i.activate=new Zt(o,n,"dropactivate"),i.activate.target=null,i.activate.dropzone=null),"dragend"===n.type&&(i.deactivate=new Zt(o,n,"dropdeactivate"),i.deactivate.target=null,i.deactivate.dropzone=null),o.rejected||(o.cur.element!==o.prev.element&&(o.prev.dropzone&&(i.leave=new Zt(o,n,"dragleave"),n.dragLeave=i.leave.target=o.prev.element,n.prevDropzone=i.leave.dropzone=o.prev.dropzone),o.cur.dropzone&&(i.enter=new Zt(o,n,"dragenter"),n.dragEnter=o.cur.element,n.dropzone=o.cur.dropzone)),"dragend"===n.type&&o.cur.dropzone&&(i.drop=new Zt(o,n,"drop"),n.dropzone=o.cur.dropzone,n.relatedTarget=o.cur.element),"dragmove"===n.type&&o.cur.dropzone&&(i.move=new Zt(o,n,"dropmove"),i.move.dragmove=n,n.dropzone=o.cur.dropzone)),i}function ie(t,e){const{dropState:n}=t,{activeDrops:o,cur:i,prev:r}=n;e.leave&&r.dropzone.fire(e.leave),e.enter&&i.dropzone.fire(e.enter),e.move&&i.dropzone.fire(e.move),e.drop&&i.dropzone.fire(e.drop),e.deactivate&&te(o,e.deactivate),n.prev.dropzone=i.dropzone,n.prev.element=i.element}function re({interaction:t,iEvent:e,event:n},o){if("dragmove"!==e.type&&"dragend"!==e.type)return;const{dropState:i}=t;o.dynamicDrop&&(i.activeDrops=ee(o,t.element));const r=e,s=ne(t,r,n);i.rejected=i.rejected&&!!s&&s.dropzone===i.cur.dropzone&&s.element===i.cur.element,i.cur.dropzone=s&&s.dropzone,i.cur.element=s&&s.element,i.events=oe(t,0,r)}const se={id:"actions/drop",install:function(t){const{actions:e,interactStatic:n,Interactable:o,defaults:i}=t;t.usePlugin(ot),o.prototype.dropzone=function(t){return function(t,e){if(Z.object(e)){if(t.options.drop.enabled=!1!==e.enabled,e.listeners){const n=Pt(e.listeners),o=Object.keys(n).reduce(((t,e)=>(t[/^(enter|leave)/.test(e)?`drag${e}`:/^(activate|deactivate|move)/.test(e)?`drop${e}`:e]=n[e],t)),{});t.off(t.options.drop.listeners),t.on(o),t.options.drop.listeners=o}return Z.func(e.ondrop)&&t.on("drop",e.ondrop),Z.func(e.ondropactivate)&&t.on("dropactivate",e.ondropactivate),Z.func(e.ondropdeactivate)&&t.on("dropdeactivate",e.ondropdeactivate),Z.func(e.ondragenter)&&t.on("dragenter",e.ondragenter),Z.func(e.ondragleave)&&t.on("dragleave",e.ondragleave),Z.func(e.ondropmove)&&t.on("dropmove",e.ondropmove),/^(pointer|center)$/.test(e.overlap)?t.options.drop.overlap=e.overlap:Z.number(e.overlap)&&(t.options.drop.overlap=Math.max(Math.min(1,e.overlap),0)),"accept"in e&&(t.options.drop.accept=e.accept),"checker"in e&&(t.options.drop.checker=e.checker),t}return Z.bool(e)?(t.options.drop.enabled=e,t):t.options.drop}(this,t)},o.prototype.dropCheck=function(t,e,n,o,i,r){return function(t,e,n,o,i,r,s){let a=!1;if(!(s=s||t.getRect(r)))return!!t.options.drop.checker&&t.options.drop.checker(e,n,a,t,r,o,i);const l=t.options.drop.overlap;if("pointer"===l){const t=It(o,i,"drag"),n=jt(e);n.x+=t.x,n.y+=t.y;const r=n.x>s.left&&n.x<s.right,l=n.y>s.top&&n.y<s.bottom;a=r&&l}const c=o.getRect(i);if(c&&"center"===l){const t=c.left+c.width/2,e=c.top+c.height/2;a=t>=s.left&&t<=s.right&&e>=s.top&&e<=s.bottom}return c&&Z.number(l)&&(a=Math.max(0,Math.min(s.right,c.right)-Math.max(s.left,c.left))*Math.max(0,Math.min(s.bottom,c.bottom)-Math.max(s.top,c.top))/(c.width*c.height)>=l),t.options.drop.checker&&(a=t.options.drop.checker(e,n,a,t,r,o,i)),a}(this,t,e,n,o,i,r)},n.dynamicDrop=function(e){return Z.bool(e)?(t.dynamicDrop=e,n):t.dynamicDrop},Et(e.phaselessTypes,{dragenter:!0,dragleave:!0,dropactivate:!0,dropdeactivate:!0,dropmove:!0,drop:!0}),e.methodDict.drop="dropzone",t.dynamicDrop=!1,i.actions.drop=se.defaults},listeners:{"interactions:before-action-start":({interaction:t})=>{"drag"===t.prepared.name&&(t.dropState={cur:{dropzone:null,element:null},prev:{dropzone:null,element:null},rejected:null,events:null,activeDrops:[]})},"interactions:after-action-start":({interaction:t,event:e,iEvent:n},o)=>{if("drag"!==t.prepared.name)return;const{dropState:i}=t;i.activeDrops=null,i.events=null,i.activeDrops=ee(o,t.element),i.events=oe(t,0,n),i.events.activate&&(te(i.activeDrops,i.events.activate),o.fire("actions/drop:start",{interaction:t,dragEvent:n}))},"interactions:action-move":re,"interactions:after-action-move":({interaction:t,iEvent:e},n)=>{"drag"===t.prepared.name&&(ie(t,t.dropState.events),n.fire("actions/drop:move",{interaction:t,dragEvent:e}),t.dropState.events={})},"interactions:action-end":(t,e)=>{if("drag"!==t.interaction.prepared.name)return;const{interaction:n,iEvent:o}=t;re(t,e),ie(n,n.dropState.events),e.fire("actions/drop:end",{interaction:n,dragEvent:o})},"interactions:stop":({interaction:t})=>{if("drag"!==t.prepared.name)return;const{dropState:e}=t;e&&(e.activeDrops=null,e.events=null,e.cur.dropzone=null,e.cur.element=null,e.prev.dropzone=null,e.prev.element=null,e.rejected=!1)}},getActiveDrops:ee,getDrop:ne,getDropEvents:oe,fireDropEvents:ie,defaults:{enabled:!1,accept:null,overlap:"pointer"}},ae=se;function le({interaction:t,iEvent:e,phase:n}){if("gesture"!==t.prepared.name)return;const o=t.pointers.map((t=>t.pointer)),i="start"===n,r="end"===n,s=t.interactable.options.deltaSource;if(e.touches=[o[0],o[1]],i)e.distance=$t(o,s),e.box=Bt(o),e.scale=1,e.ds=0,e.angle=qt(o,s),e.da=0,t.gesture.startDistance=e.distance,t.gesture.startAngle=e.angle;else if(r){const n=t.prevEvent;e.distance=n.distance,e.box=n.box,e.scale=n.scale,e.ds=0,e.angle=n.angle,e.da=0}else e.distance=$t(o,s),e.box=Bt(o),e.scale=e.distance/t.gesture.startDistance,e.angle=qt(o,s),e.ds=e.scale-t.gesture.scale,e.da=e.angle-t.gesture.angle;t.gesture.distance=e.distance,t.gesture.angle=e.angle,Z.number(e.scale)&&e.scale!==1/0&&!isNaN(e.scale)&&(t.gesture.scale=e.scale)}const ce={id:"actions/gesture",before:["actions/drag","actions/resize"],install:function(t){const{actions:e,Interactable:n,defaults:o}=t;n.prototype.gesturable=function(t){return Z.object(t)?(this.options.gesture.enabled=!1!==t.enabled,this.setPerAction("gesture",t),this.setOnEvents("gesture",t),this):Z.bool(t)?(this.options.gesture.enabled=t,this):this.options.gesture},e.map.gesture=ce,e.methodDict.gesture="gesturable",o.actions.gesture=ce.defaults},listeners:{"interactions:action-start":le,"interactions:action-move":le,"interactions:action-end":le,"interactions:new":({interaction:t})=>{t.gesture={angle:0,distance:0,scale:1,startAngle:0,startDistance:0}},"auto-start:check":t=>{if(t.interaction.pointers.length<2)return;const e=t.interactable.options.gesture;return e&&e.enabled?(t.action={name:"gesture"},!1):void 0}},defaults:{},getCursor:()=>""},ue=ce;function pe(t,e,n,o,i,r,s){if(!e)return!1;if(!0===e){const e=Z.number(r.width)?r.width:r.right-r.left,o=Z.number(r.height)?r.height:r.bottom-r.top;if(s=Math.min(s,Math.abs(("left"===t||"right"===t?e:o)/2)),e<0&&("left"===t?t="right":"right"===t&&(t="left")),o<0&&("top"===t?t="bottom":"bottom"===t&&(t="top")),"left"===t){const t=e>=0?r.left:r.right;return n.x<t+s}if("top"===t){const t=o>=0?r.top:r.bottom;return n.y<t+s}if("right"===t)return n.x>(e>=0?r.right:r.left)-s;if("bottom"===t)return n.y>(o>=0?r.bottom:r.top)-s}return!!Z.element(o)&&(Z.element(e)?e===o:mt(o,e,i))}function de({iEvent:t,interaction:e}){if("resize"!==e.prepared.name||!e.resizeAxes)return;const n=t;e.interactable.options.resize.square?("y"===e.resizeAxes?n.delta.x=n.delta.y:n.delta.y=n.delta.x,n.axes="xy"):(n.axes=e.resizeAxes,"x"===e.resizeAxes?n.delta.y=0:"y"===e.resizeAxes&&(n.delta.x=0))}const he={id:"actions/resize",before:["actions/drag"],install:function(t){const{actions:e,browser:n,Interactable:o,defaults:i}=t;he.cursors=function(t){return t.isIe9?{x:"e-resize",y:"s-resize",xy:"se-resize",top:"n-resize",left:"w-resize",bottom:"s-resize",right:"e-resize",topleft:"se-resize",bottomright:"se-resize",topright:"ne-resize",bottomleft:"ne-resize"}:{x:"ew-resize",y:"ns-resize",xy:"nwse-resize",top:"ns-resize",left:"ew-resize",bottom:"ns-resize",right:"ew-resize",topleft:"nwse-resize",bottomright:"nwse-resize",topright:"nesw-resize",bottomleft:"nesw-resize"}}(n),he.defaultMargin=n.supportsTouch||n.supportsPointerEvent?20:10,o.prototype.resizable=function(e){return function(t,e,n){return Z.object(e)?(t.options.resize.enabled=!1!==e.enabled,t.setPerAction("resize",e),t.setOnEvents("resize",e),Z.string(e.axis)&&/^x$|^y$|^xy$/.test(e.axis)?t.options.resize.axis=e.axis:null===e.axis&&(t.options.resize.axis=n.defaults.actions.resize.axis),Z.bool(e.preserveAspectRatio)?t.options.resize.preserveAspectRatio=e.preserveAspectRatio:Z.bool(e.square)&&(t.options.resize.square=e.square),t):Z.bool(e)?(t.options.resize.enabled=e,t):t.options.resize}(this,e,t)},e.map.resize=he,e.methodDict.resize="resizable",i.actions.resize=he.defaults},listeners:{"interactions:new":({interaction:t})=>{t.resizeAxes="xy"},"interactions:action-start":t=>{(function({iEvent:t,interaction:e}){if("resize"!==e.prepared.name||!e.prepared.edges)return;const n=t,o=e.rect;e._rects={start:Et({},o),corrected:Et({},o),previous:Et({},o),delta:{left:0,right:0,width:0,top:0,bottom:0,height:0}},n.edges=e.prepared.edges,n.rect=e._rects.corrected,n.deltaRect=e._rects.delta})(t),de(t)},"interactions:action-move":t=>{(function({iEvent:t,interaction:e}){if("resize"!==e.prepared.name||!e.prepared.edges)return;const n=t,o=e.interactable.options.resize.invert,i="reposition"===o||"negate"===o,r=e.rect,{start:s,corrected:a,delta:l,previous:c}=e._rects;if(Et(c,a),i){if(Et(a,r),"reposition"===o){if(a.top>a.bottom){const t=a.top;a.top=a.bottom,a.bottom=t}if(a.left>a.right){const t=a.left;a.left=a.right,a.right=t}}}else a.top=Math.min(r.top,s.bottom),a.bottom=Math.max(r.bottom,s.top),a.left=Math.min(r.left,s.right),a.right=Math.max(r.right,s.left);a.width=a.right-a.left,a.height=a.bottom-a.top;for(const u in a)l[u]=a[u]-c[u];n.edges=e.prepared.edges,n.rect=a,n.deltaRect=l})(t),de(t)},"interactions:action-end":function({iEvent:t,interaction:e}){if("resize"!==e.prepared.name||!e.prepared.edges)return;const n=t;n.edges=e.prepared.edges,n.rect=e._rects.corrected,n.deltaRect=e._rects.delta},"auto-start:check":function(t){const{interaction:e,interactable:n,element:o,rect:i,buttons:r}=t;if(!i)return;const s=Et({},e.coords.cur.page),a=n.options.resize;if(a&&a.enabled&&(!e.pointerIsDown||!/mouse|pointer/.test(e.pointerType)||r&a.mouseButtons)){if(Z.object(a.edges)){const n={left:!1,right:!1,top:!1,bottom:!1};for(const t in n)n[t]=pe(t,a.edges[t],s,e._latestPointer.eventTarget,o,i,a.margin||he.defaultMargin);n.left=n.left&&!n.right,n.top=n.top&&!n.bottom,(n.left||n.right||n.top||n.bottom)&&(t.action={name:"resize",edges:n})}else{const e="y"!==a.axis&&s.x>i.right-he.defaultMargin,n="x"!==a.axis&&s.y>i.bottom-he.defaultMargin;(e||n)&&(t.action={name:"resize",axes:(e?"x":"")+(n?"y":"")})}return!t.action&&void 0}}},defaults:{square:!1,preserveAspectRatio:!1,axis:"xy",margin:NaN,edges:null,invert:"none"},cursors:null,getCursor({edges:t,axis:e,name:n}){const o=he.cursors;let i=null;if(e)i=o[n+e];else if(t){let e="";for(const n of["top","bottom","left","right"])t[n]&&(e+=n);i=o[e]}return i},defaultMargin:null},fe=he,ge={id:"actions",install(t){t.usePlugin(ue),t.usePlugin(fe),t.usePlugin(ot),t.usePlugin(ae)}};let me,ve,ye=0;const be={request:t=>me(t),cancel:t=>ve(t),init:function(t){if(me=t.requestAnimationFrame,ve=t.cancelAnimationFrame,!me){const e=["ms","moz","webkit","o"];for(const n of e)me=t[`${n}RequestAnimationFrame`],ve=t[`${n}CancelAnimationFrame`]||t[`${n}CancelRequestAnimationFrame`]}me=me&&me.bind(t),ve=ve&&ve.bind(t),me||(me=e=>{const n=Date.now(),o=Math.max(0,16-(n-ye)),i=t.setTimeout((()=>{e(n+o)}),o);return ye=n+o,i},ve=t=>clearTimeout(t))}};const xe={defaults:{enabled:!1,margin:60,container:null,speed:300},now:Date.now,interaction:null,i:0,x:0,y:0,isScrolling:!1,prevTime:0,margin:0,speed:0,start(t){xe.isScrolling=!0,be.cancel(xe.i),t.autoScroll=xe,xe.interaction=t,xe.prevTime=xe.now(),xe.i=be.request(xe.scroll)},stop(){xe.isScrolling=!1,xe.interaction&&(xe.interaction.autoScroll=null),be.cancel(xe.i)},scroll(){const{interaction:t}=xe,{interactable:e,element:n}=t,o=t.prepared.name,i=e.options[o].autoScroll,r=we(i.container,e,n),s=xe.now(),a=(s-xe.prevTime)/1e3,l=i.speed*a;if(l>=1){const o={x:xe.x*l,y:xe.y*l};if(o.x||o.y){const i=Ee(r);Z.window(r)?r.scrollBy(o.x,o.y):r&&(r.scrollLeft+=o.x,r.scrollTop+=o.y);const s=Ee(r),a={x:s.x-i.x,y:s.y-i.y};(a.x||a.y)&&e.fire({type:"autoscroll",target:n,interactable:e,delta:a,interaction:t,container:r})}xe.prevTime=s}xe.isScrolling&&(be.cancel(xe.i),xe.i=be.request(xe.scroll))},check(t,e){var n;return null==(n=t.options[e].autoScroll)?void 0:n.enabled},onInteractionMove({interaction:t,pointer:e}){if(!t.interacting()||!xe.check(t.interactable,t.prepared.name))return;if(t.simulation)return void(xe.x=xe.y=0);let n,o,i,r;const{interactable:s,element:a}=t,l=t.prepared.name,c=s.options[l].autoScroll,u=we(c.container,s,a);if(Z.window(u))r=e.clientX<xe.margin,n=e.clientY<xe.margin,o=e.clientX>u.innerWidth-xe.margin,i=e.clientY>u.innerHeight-xe.margin;else{const t=yt(u);r=e.clientX<t.left+xe.margin,n=e.clientY<t.top+xe.margin,o=e.clientX>t.right-xe.margin,i=e.clientY>t.bottom-xe.margin}xe.x=o?1:r?-1:0,xe.y=i?1:n?-1:0,xe.isScrolling||(xe.margin=c.margin,xe.speed=c.speed,xe.start(t))}};function we(t,e,n){return(Z.string(t)?St(t,e,n):t)||K(n)}function Ee(t){return Z.window(t)&&(t=window.document.body),{x:t.scrollLeft,y:t.scrollTop}}const Se={id:"auto-scroll",install:function(t){const{defaults:e,actions:n}=t;t.autoScroll=xe,xe.now=()=>t.now(),n.phaselessTypes.autoscroll=!0,e.perAction.autoScroll=xe.defaults},listeners:{"interactions:new":({interaction:t})=>{t.autoScroll=null},"interactions:destroy":({interaction:t})=>{t.autoScroll=null,xe.stop(),xe.interaction&&(xe.interaction=null)},"interactions:stop":xe.stop,"interactions:action-move":t=>xe.onInteractionMove(t)}},Te=Se;function _e(t,e){let n=!1;return function(){return n||(G.console.warn(e),n=!0),t.apply(this,arguments)}}function ze(t,e){return t.name=e.name,t.axis=e.axis,t.edges=e.edges,t}function Oe(t){return Z.bool(t)?(this.options.styleCursor=t,this):null===t?(delete this.options.styleCursor,this):this.options.styleCursor}function Ie(t){return Z.func(t)?(this.options.actionChecker=t,this):null===t?(delete this.options.actionChecker,this):this.options.actionChecker}const Pe={id:"auto-start/interactableMethods",install:function(t){const{Interactable:e}=t;e.prototype.getAction=function(e,n,o,i){const r=function(t,e,n,o,i){const r=t.getRect(o),s=e.buttons||{0:1,1:4,3:8,4:16}[e.button],a={action:null,interactable:t,interaction:n,element:o,rect:r,buttons:s};return i.fire("auto-start:check",a),a.action}(this,n,o,i,t);return this.options.actionChecker?this.options.actionChecker(e,n,r,this,i,o):r},e.prototype.ignoreFrom=_e((function(t){return this._backCompatOption("ignoreFrom",t)}),"Interactable.ignoreFrom() has been deprecated. Use Interactble.draggable({ignoreFrom: newValue})."),e.prototype.allowFrom=_e((function(t){return this._backCompatOption("allowFrom",t)}),"Interactable.allowFrom() has been deprecated. Use Interactble.draggable({allowFrom: newValue})."),e.prototype.actionChecker=Ie,e.prototype.styleCursor=Oe}};function Me(t,e,n,o,i){return e.testIgnoreAllow(e.options[t.name],n,o)&&e.options[t.name].enabled&&Ce(e,n,t,i)?t:null}function De(t,e,n,o,i,r,s){for(let a=0,l=o.length;a<l;a++){const l=o[a],c=i[a],u=l.getAction(e,n,t,c);if(!u)continue;const p=Me(u,l,c,r,s);if(p)return{action:p,interactable:l,element:c}}return{action:null,interactable:null,element:null}}function ke(t,e,n,o,i){let r=[],s=[],a=o;function l(t){r.push(t),s.push(a)}for(;Z.element(a);){r=[],s=[],i.interactables.forEachMatch(a,l);const c=De(t,e,n,r,s,o,i);if(c.action&&!c.interactable.options[c.action.name].manualStart)return c;a=pt(a)}return{action:null,interactable:null,element:null}}function Ae(t,{action:e,interactable:n,element:o},i){e=e||{name:null},t.interactable=n,t.element=o,ze(t.prepared,e),t.rect=n&&e.name?n.getRect(o):null,je(t,i),i.fire("autoStart:prepared",{interaction:t})}function Ce(t,e,n,o){const i=t.options,r=i[n.name].max,s=i[n.name].maxPerElement,a=o.autoStart.maxInteractions;let l=0,c=0,u=0;if(!(r&&s&&a))return!1;for(const p of o.interactions.list){const o=p.prepared.name;if(p.interacting()){if(l++,l>=a)return!1;if(p.interactable===t&&(c+=o===n.name?1:0,c>=r||p.element===e&&(u++,o===n.name&&u>=s)))return!1}}return a>0}function Re(t,e){return Z.number(t)?(e.autoStart.maxInteractions=t,this):e.autoStart.maxInteractions}function Ne(t,e,n){const{cursorElement:o}=n.autoStart;o&&o!==t&&(o.style.cursor=""),t.ownerDocument.documentElement.style.cursor=e,t.style.cursor=e,n.autoStart.cursorElement=e?t:null}function je(t,e){const{interactable:n,element:o,prepared:i}=t;if("mouse"!==t.pointerType||!n||!n.options.styleCursor)return void(e.autoStart.cursorElement&&Ne(e.autoStart.cursorElement,"",e));let r="";if(i.name){const s=n.options[i.name].cursorChecker;r=Z.func(s)?s(i,n,o,t._interacting):e.actions.map[i.name].getCursor(i)}Ne(t.element,r||"",e)}const He={id:"auto-start/base",before:["actions"],install:function(t){const{interactStatic:e,defaults:n}=t;t.usePlugin(Pe),n.base.actionChecker=null,n.base.styleCursor=!0,Et(n.perAction,{manualStart:!1,max:1/0,maxPerElement:1,allowFrom:null,ignoreFrom:null,mouseButtons:1}),e.maxInteractions=e=>Re(e,t),t.autoStart={maxInteractions:1/0,withinInteractionLimit:Ce,cursorElement:null}},listeners:{"interactions:down":function({interaction:t,pointer:e,event:n,eventTarget:o},i){if(t.interacting())return;Ae(t,ke(t,e,n,o,i),i)},"interactions:move":(t,e)=>{(function({interaction:t,pointer:e,event:n,eventTarget:o},i){if("mouse"!==t.pointerType||t.pointerIsDown||t.interacting())return;Ae(t,ke(t,e,n,o,i),i)})(t,e),function(t,e){const{interaction:n}=t;if(!n.pointerIsDown||n.interacting()||!n.pointerWasMoved||!n.prepared.name)return;e.fire("autoStart:before-start",t);const{interactable:o}=n,i=n.prepared.name;i&&o&&(o.options[i].manualStart||!Ce(o,n.element,n.prepared,e)?n.stop():(n.start(n.prepared,o,n.element),je(n,e)))}(t,e)},"interactions:stop":function({interaction:t},e){const{interactable:n}=t;n&&n.options.styleCursor&&Ne(t.element,"",e)}},maxInteractions:Re,withinInteractionLimit:Ce,validateAction:Me},Le=He;const Fe={id:"auto-start/dragAxis",listeners:{"autoStart:before-start":function({interaction:t,eventTarget:e,dx:n,dy:o},i){if("drag"!==t.prepared.name)return;const r=Math.abs(n),s=Math.abs(o),a=t.interactable.options.drag,l=a.startAxis,c=r>s?"x":r<s?"y":"xy";if(t.prepared.axis="start"===a.lockAxis?c[0]:a.lockAxis,"xy"!==c&&"xy"!==l&&l!==c){t.prepared.name=null;let n=e;const o=function(o){if(o===t.interactable)return;const r=t.interactable.options.drag;if(!r.manualStart&&o.testIgnoreAllow(r,n,e)){const r=o.getAction(t.downPointer,t.downEvent,t,n);if(r&&"drag"===r.name&&function(t,e){if(!e)return!1;const n=e.options.drag.startAxis;return"xy"===t||"xy"===n||n===t}(c,o)&&Le.validateAction(r,o,n,e,i))return o}};for(;Z.element(n);){const e=i.interactables.forEachMatch(n,o);if(e){t.prepared.name="drag",t.interactable=e,t.element=n;break}n=pt(n)}}}}};function We(t){const e=t.prepared&&t.prepared.name;if(!e)return null;const n=t.interactable.options;return n[e].hold||n[e].delay}const Be={id:"auto-start/hold",install:function(t){const{defaults:e}=t;t.usePlugin(Le),e.perAction.hold=0,e.perAction.delay=0},listeners:{"interactions:new":({interaction:t})=>{t.autoStartHoldTimer=null},"autoStart:prepared":({interaction:t})=>{const e=We(t);e>0&&(t.autoStartHoldTimer=setTimeout((()=>{t.start(t.prepared,t.interactable,t.element)}),e))},"interactions:move":({interaction:t,duplicate:e})=>{t.autoStartHoldTimer&&t.pointerWasMoved&&!e&&(clearTimeout(t.autoStartHoldTimer),t.autoStartHoldTimer=null)},"autoStart:before-start":({interaction:t})=>{We(t)>0&&(t.prepared.name=null)}},getHoldDuration:We},$e=Be,qe={id:"auto-start",install(t){t.usePlugin(Le),t.usePlugin($e),t.usePlugin(Fe)}};function Xe(t){return/^(always|never|auto)$/.test(t)?(this.options.preventDefault=t,this):Z.bool(t)?(this.options.preventDefault=t?"always":"never",this):this.options.preventDefault}function Ye({interaction:t,event:e}){t.interactable&&t.interactable.checkAndPreventDefault(e)}const Ve={id:"core/interactablePreventDefault",install:function(t){const{Interactable:e}=t;e.prototype.preventDefault=Xe,e.prototype.checkAndPreventDefault=function(e){return function(t,e,n){const o=t.options.preventDefault;if("never"!==o){if("always"===o)return void n.preventDefault();if(e.events.supportsPassive&&/^touch(start|move)$/.test(n.type)){const t=K(n.target).document,o=e.getDocOptions(t);if(!o||!o.events||!1!==o.events.passive)return}/^(mouse|pointer|touch)*(down|start)/i.test(n.type)||Z.element(n.target)&&dt(n.target,"input,select,textarea,[contenteditable=true],[contenteditable=true] *")||n.preventDefault()}}(this,t,e)},t.interactions.docEvents.push({type:"dragstart",listener(e){for(const n of t.interactions.list)if(n.element&&(n.element===e.target||ct(n.element,e.target)))return void n.interactable.checkAndPreventDefault(e)}})},listeners:["down","move","up","cancel"].reduce(((t,e)=>(t[`interactions:${e}`]=Ye,t)),{})},Ge={};var Ue,Ke;(Ke=Ue||(Ue={})).touchAction="touchAction",Ke.boxSizing="boxSizing",Ke.noListeners="noListeners";const Je="[interact.js] ",Qe={touchAction:"https://developer.mozilla.org/en-US/docs/Web/CSS/touch-action",boxSizing:"https://developer.mozilla.org/en-US/docs/Web/CSS/box-sizing"};const Ze=[{name:Ue.touchAction,perform:({element:t})=>!function(t,e,n){let o=t;for(;Z.element(o);){if(tn(o,e,n))return!0;o=pt(o)}return!1}(t,"touchAction",/pan-|pinch|none/),getInfo:({element:t})=>[t,Qe.touchAction],text:'Consider adding CSS "touch-action: none" to this element\n'},{name:Ue.boxSizing,perform(t){const{element:e}=t;return"resize"===t.prepared.name&&e instanceof st.HTMLElement&&!tn(e,"boxSizing",/border-box/)},text:'Consider adding CSS "box-sizing: border-box" to this resizable element',getInfo:({element:t})=>[t,Qe.boxSizing]},{name:Ue.noListeners,perform(t){const e=t.prepared.name;return!(t.interactable.events.types[`${e}move`]||[]).length},getInfo:t=>[t.prepared.name,t.interactable],text:"There are no listeners set for this action"}];function tn(t,e,n){const o=t.style[e]||G.getComputedStyle(t)[e];return n.test((o||"").toString())}const en={id:"dev-tools",install:function(t,{logger:e}={}){const{Interactable:n,defaults:o}=t;t.logger=e||console,o.base.devTools={ignore:{}},n.prototype.devTools=function(t){return t?(Et(this.options.devTools,t),this):this.options.devTools},t.usePlugin(Ge)},listeners:{"interactions:action-start":({interaction:t},e)=>{for(const n of Ze){const o=t.interactable&&t.interactable.options;!(o&&o.devTools&&o.devTools.ignore[n.name])&&n.perform(t)&&e.logger.warn(Je+n.text,...n.getInfo(t))}}},checks:Ze,CheckName:Ue,links:Qe,prefix:Je},nn=en;function on(t){const e={};for(const n in t){const o=t[n];Z.plainObject(o)?e[n]=on(o):Z.array(o)?e[n]=Kt(o):e[n]=o}return e}class rn{constructor(t){I(this,"states",[]),I(this,"startOffset",{left:0,right:0,top:0,bottom:0}),I(this,"startDelta"),I(this,"result"),I(this,"endResult"),I(this,"edges"),I(this,"interaction"),this.interaction=t,this.result=sn()}start({phase:t},e){const{interaction:n}=this,o=function(t){const e=t.interactable.options[t.prepared.name],n=e.modifiers;return n&&n.length?n:["snap","snapSize","snapEdges","restrict","restrictEdges","restrictSize"].map((t=>{const n=e[t];return n&&n.enabled&&{options:n,methods:n._methods}})).filter((t=>!!t))}(n);this.prepareStates(o),this.edges=Et({},n.edges),this.startOffset=function(t,e){return t?{left:e.x-t.left,top:e.y-t.top,right:t.right-e.x,bottom:t.bottom-e.y}:{left:0,top:0,right:0,bottom:0}}(n.rect,e),this.startDelta={x:0,y:0};const i=this.fillArg({phase:t,pageCoords:e,preEnd:!1});return this.result=sn(),this.startAll(i),this.result=this.setAll(i)}fillArg(t){const{interaction:e}=this;return t.interaction=e,t.interactable=e.interactable,t.element=e.element,t.rect=t.rect||e.rect,t.edges=this.edges,t.startOffset=this.startOffset,t}startAll(t){for(const e of this.states)e.methods.start&&(t.state=e,e.methods.start(t))}setAll(t){const{phase:e,preEnd:n,skipModifiers:o,rect:i}=t;t.coords=Et({},t.pageCoords),t.rect=Et({},i);const r=o?this.states.slice(o):this.states,s=sn(t.coords,t.rect);for(const u of r){var a;const{options:o}=u,i=Et({},t.coords);let r=null;null!=(a=u.methods)&&a.set&&this.shouldDo(o,n,e)&&(t.state=u,r=u.methods.set(t),Ot(this.interaction.edges,t.rect,{x:t.coords.x-i.x,y:t.coords.y-i.y})),s.eventProps.push(r)}s.delta.x=t.coords.x-t.pageCoords.x,s.delta.y=t.coords.y-t.pageCoords.y,s.rectDelta.left=t.rect.left-i.left,s.rectDelta.right=t.rect.right-i.right,s.rectDelta.top=t.rect.top-i.top,s.rectDelta.bottom=t.rect.bottom-i.bottom;const l=this.result.coords,c=this.result.rect;if(l&&c){const t=s.rect.left!==c.left||s.rect.right!==c.right||s.rect.top!==c.top||s.rect.bottom!==c.bottom;s.changed=t||l.x!==s.coords.x||l.y!==s.coords.y}return s}applyToInteraction(t){const{interaction:e}=this,{phase:n}=t,o=e.coords.cur,i=e.coords.start,{result:r,startDelta:s}=this,a=r.delta;"start"===n&&Et(this.startDelta,r.delta);for(const[u,p]of[[i,s],[o,a]])u.page.x+=p.x,u.page.y+=p.y,u.client.x+=p.x,u.client.y+=p.y;const{rectDelta:l}=this.result,c=t.rect||e.rect;c.left+=l.left,c.right+=l.right,c.top+=l.top,c.bottom+=l.bottom,c.width=c.right-c.left,c.height=c.bottom-c.top}setAndApply(t){const{interaction:e}=this,{phase:n,preEnd:o,skipModifiers:i}=t,r=this.setAll(this.fillArg({preEnd:o,phase:n,pageCoords:t.modifiedCoords||e.coords.cur.page}));if(this.result=r,!r.changed&&(!i||i<this.states.length)&&e.interacting())return!1;if(t.modifiedCoords){const{page:n}=e.coords.cur,o={x:t.modifiedCoords.x-n.x,y:t.modifiedCoords.y-n.y};r.coords.x+=o.x,r.coords.y+=o.y,r.delta.x+=o.x,r.delta.y+=o.y}this.applyToInteraction(t)}beforeEnd(t){const{interaction:e,event:n}=t,o=this.states;if(!o||!o.length)return;let i=!1;for(const r of o){t.state=r;const{options:e,methods:n}=r,o=n.beforeEnd&&n.beforeEnd(t);if(o)return this.endResult=o,!1;i=i||!i&&this.shouldDo(e,!0,t.phase,!0)}i&&e.move({event:n,preEnd:!0})}stop(t){const{interaction:e}=t;if(!this.states||!this.states.length)return;const n=Et({states:this.states,interactable:e.interactable,element:e.element,rect:null},t);this.fillArg(n);for(const o of this.states)n.state=o,o.methods.stop&&o.methods.stop(n);this.states=null,this.endResult=null}prepareStates(t){this.states=[];for(let e=0;e<t.length;e++){const{options:n,methods:o,name:i}=t[e];this.states.push({options:n,methods:o,index:e,name:i})}return this.states}restoreInteractionCoords({interaction:{coords:t,rect:e,modification:n}}){if(!n.result)return;const{startDelta:o}=n,{delta:i,rectDelta:r}=n.result,s=[[t.start,o],[t.cur,i]];for(const[a,l]of s)a.page.x-=l.x,a.page.y-=l.y,a.client.x-=l.x,a.client.y-=l.y;e.left-=r.left,e.right-=r.right,e.top-=r.top,e.bottom-=r.bottom}shouldDo(t,e,n,o){return!(!t||!1===t.enabled||o&&!t.endOnly||t.endOnly&&!e||"start"===n&&!t.setStart)}copyFrom(t){this.startOffset=t.startOffset,this.startDelta=t.startDelta,this.edges=t.edges,this.states=t.states.map((t=>on(t))),this.result=sn(Et({},t.result.coords),Et({},t.result.rect))}destroy(){for(const t in this)this[t]=null}}function sn(t,e){return{rect:e,coords:t,delta:{x:0,y:0},rectDelta:{left:0,right:0,top:0,bottom:0},eventProps:[],changed:!0}}function an(t,e){const{defaults:n}=t,o={start:t.start,set:t.set,beforeEnd:t.beforeEnd,stop:t.stop},i=t=>{const i=t||{};i.enabled=!1!==i.enabled;for(const e in n)e in i||(i[e]=n[e]);const r={options:i,methods:o,name:e,enable:()=>(i.enabled=!0,r),disable:()=>(i.enabled=!1,r)};return r};return e&&"string"==typeof e&&(i._defaults=n,i._methods=o),i}function ln({iEvent:t,interaction:e}){const n=e.modification.result;n&&(t.modifiers=n.eventProps)}const cn={id:"modifiers/base",before:["actions"],install:t=>{t.defaults.perAction.modifiers=[]},listeners:{"interactions:new":({interaction:t})=>{t.modification=new rn(t)},"interactions:before-action-start":t=>{const e=t.interaction.modification;e.start(t,t.interaction.coords.start.page),t.interaction.edges=e.edges,e.applyToInteraction(t)},"interactions:before-action-move":t=>t.interaction.modification.setAndApply(t),"interactions:before-action-end":t=>t.interaction.modification.beforeEnd(t),"interactions:action-start":ln,"interactions:action-move":ln,"interactions:action-end":ln,"interactions:after-action-start":t=>t.interaction.modification.restoreInteractionCoords(t),"interactions:after-action-move":t=>t.interaction.modification.restoreInteractionCoords(t),"interactions:stop":t=>t.interaction.modification.stop(t)}},un=cn,pn={base:{preventDefault:"auto",deltaSource:"page"},perAction:{enabled:!1,origin:{x:0,y:0}},actions:{}};class dn extends Gt{constructor(t,e,n,o,i,r,s){super(t),I(this,"relatedTarget",null),I(this,"screenX"),I(this,"screenY"),I(this,"button"),I(this,"buttons"),I(this,"ctrlKey"),I(this,"shiftKey"),I(this,"altKey"),I(this,"metaKey"),I(this,"page"),I(this,"client"),I(this,"delta"),I(this,"rect"),I(this,"x0"),I(this,"y0"),I(this,"t0"),I(this,"dt"),I(this,"duration"),I(this,"clientX0"),I(this,"clientY0"),I(this,"velocity"),I(this,"speed"),I(this,"swipe"),I(this,"axes"),I(this,"preEnd"),i=i||t.element;const a=t.interactable,l=(a&&a.options||pn).deltaSource,c=It(a,i,n),u="start"===o,p="end"===o,d=u?this:t.prevEvent,h=u?t.coords.start:p?{page:d.page,client:d.client,timeStamp:t.coords.cur.timeStamp}:t.coords.cur;this.page=Et({},h.page),this.client=Et({},h.client),this.rect=Et({},t.rect),this.timeStamp=h.timeStamp,p||(this.page.x-=c.x,this.page.y-=c.y,this.client.x-=c.x,this.client.y-=c.y),this.ctrlKey=e.ctrlKey,this.altKey=e.altKey,this.shiftKey=e.shiftKey,this.metaKey=e.metaKey,this.button=e.button,this.buttons=e.buttons,this.target=i,this.currentTarget=i,this.preEnd=r,this.type=s||n+(o||""),this.interactable=a,this.t0=u?t.pointers[t.pointers.length-1].downTime:d.t0,this.x0=t.coords.start.page.x-c.x,this.y0=t.coords.start.page.y-c.y,this.clientX0=t.coords.start.client.x-c.x,this.clientY0=t.coords.start.client.y-c.y,this.delta=u||p?{x:0,y:0}:{x:this[l].x-d[l].x,y:this[l].y-d[l].y},this.dt=t.coords.delta.timeStamp,this.duration=this.timeStamp-this.t0,this.velocity=Et({},t.coords.velocity[l]),this.speed=Dt(this.velocity.x,this.velocity.y),this.swipe=p||"inertiastart"===o?this.getSwipe():null}getSwipe(){const t=this._interaction;if(t.prevEvent.speed<600||this.timeStamp-t.prevEvent.timeStamp>150)return null;let e=180*Math.atan2(t.prevEvent.velocityY,t.prevEvent.velocityX)/Math.PI;e<0&&(e+=360);const n=112.5<=e&&e<247.5,o=202.5<=e&&e<337.5;return{up:o,down:!o&&22.5<=e&&e<157.5,left:n,right:!n&&(292.5<=e||e<67.5),angle:e,speed:t.prevEvent.speed,velocity:{x:t.prevEvent.velocityX,y:t.prevEvent.velocityY}}}preventDefault(){}stopImmediatePropagation(){this.immediatePropagationStopped=this.propagationStopped=!0}stopPropagation(){this.propagationStopped=!0}}Object.defineProperties(dn.prototype,{pageX:{get(){return this.page.x},set(t){this.page.x=t}},pageY:{get(){return this.page.y},set(t){this.page.y=t}},clientX:{get(){return this.client.x},set(t){this.client.x=t}},clientY:{get(){return this.client.y},set(t){this.client.y=t}},dx:{get(){return this.delta.x},set(t){this.delta.x=t}},dy:{get(){return this.delta.y},set(t){this.delta.y=t}},velocityX:{get(){return this.velocity.x},set(t){this.velocity.x=t}},velocityY:{get(){return this.velocity.y},set(t){this.velocity.y=t}}});class hn{constructor(t,e,n,o,i){I(this,"id"),I(this,"pointer"),I(this,"event"),I(this,"downTime"),I(this,"downTarget"),this.id=t,this.pointer=e,this.event=n,this.downTime=o,this.downTarget=i}}let fn,gn;!function(t){t.interactable="",t.element="",t.prepared="",t.pointerIsDown="",t.pointerWasMoved="",t._proxy=""}(fn||(fn={})),function(t){t.start="",t.move="",t.end="",t.stop="",t.interacting=""}(gn||(gn={}));let mn=0;class vn{constructor({pointerType:t,scopeFire:e}){I(this,"interactable",null),I(this,"element",null),I(this,"rect",null),I(this,"_rects"),I(this,"edges",null),I(this,"_scopeFire"),I(this,"prepared",{name:null,axis:null,edges:null}),I(this,"pointerType"),I(this,"pointers",[]),I(this,"downEvent",null),I(this,"downPointer",{}),I(this,"_latestPointer",{pointer:null,event:null,eventTarget:null}),I(this,"prevEvent",null),I(this,"pointerIsDown",!1),I(this,"pointerWasMoved",!1),I(this,"_interacting",!1),I(this,"_ending",!1),I(this,"_stopped",!0),I(this,"_proxy",null),I(this,"simulation",null),I(this,"doMove",_e((function(t){this.move(t)}),"The interaction.doMove() method has been renamed to interaction.move()")),I(this,"coords",{start:{page:{x:0,y:0},client:{x:0,y:0},timeStamp:0},prev:{page:{x:0,y:0},client:{x:0,y:0},timeStamp:0},cur:{page:{x:0,y:0},client:{x:0,y:0},timeStamp:0},delta:{page:{x:0,y:0},client:{x:0,y:0},timeStamp:0},velocity:{page:{x:0,y:0},client:{x:0,y:0},timeStamp:0}}),I(this,"_id",mn++),this._scopeFire=e,this.pointerType=t;const n=this;this._proxy={};for(const o in fn)Object.defineProperty(this._proxy,o,{get:()=>n[o]});for(const o in gn)Object.defineProperty(this._proxy,o,{value:(...t)=>n[o](...t)});this._scopeFire("interactions:new",{interaction:this})}get pointerMoveTolerance(){return 1}pointerDown(t,e,n){const o=this.updatePointer(t,e,n,!0),i=this.pointers[o];this._scopeFire("interactions:down",{pointer:t,event:e,eventTarget:n,pointerIndex:o,pointerInfo:i,type:"down",interaction:this})}start(t,e,n){return!(this.interacting()||!this.pointerIsDown||this.pointers.length<("gesture"===t.name?2:1)||!e.options[t.name].enabled)&&(ze(this.prepared,t),this.interactable=e,this.element=n,this.rect=e.getRect(n),this.edges=this.prepared.edges?Et({},this.prepared.edges):{left:!0,right:!0,top:!0,bottom:!0},this._stopped=!1,this._interacting=this._doPhase({interaction:this,event:this.downEvent,phase:"start"})&&!this._stopped,this._interacting)}pointerMove(t,e,n){!this.simulation&&(!this.modification||!this.modification.endResult)&&this.updatePointer(t,e,n,!1);const o=this.coords.cur.page.x===this.coords.prev.page.x&&this.coords.cur.page.y===this.coords.prev.page.y&&this.coords.cur.client.x===this.coords.prev.client.x&&this.coords.cur.client.y===this.coords.prev.client.y;let i,r;this.pointerIsDown&&!this.pointerWasMoved&&(i=this.coords.cur.client.x-this.coords.start.client.x,r=this.coords.cur.client.y-this.coords.start.client.y,this.pointerWasMoved=Dt(i,r)>this.pointerMoveTolerance);const s=this.getPointerIndex(t),a={pointer:t,pointerIndex:s,pointerInfo:this.pointers[s],event:e,type:"move",eventTarget:n,dx:i,dy:r,duplicate:o,interaction:this};o||function(t,e){const n=Math.max(e.timeStamp/1e3,.001);t.page.x=e.page.x/n,t.page.y=e.page.y/n,t.client.x=e.client.x/n,t.client.y=e.client.y/n,t.timeStamp=n}(this.coords.velocity,this.coords.delta),this._scopeFire("interactions:move",a),!o&&!this.simulation&&(this.interacting()&&(a.type=null,this.move(a)),this.pointerWasMoved&&At(this.coords.prev,this.coords.cur))}move(t){(!t||!t.event)&&Ct(this.coords.delta),(t=Et({pointer:this._latestPointer.pointer,event:this._latestPointer.event,eventTarget:this._latestPointer.eventTarget,interaction:this},t||{})).phase="move",this._doPhase(t)}pointerUp(t,e,n,o){let i=this.getPointerIndex(t);-1===i&&(i=this.updatePointer(t,e,n,!1));const r=/cancel$/i.test(e.type)?"cancel":"up";this._scopeFire(`interactions:${r}`,{pointer:t,pointerIndex:i,pointerInfo:this.pointers[i],event:e,eventTarget:n,type:r,curEventTarget:o,interaction:this}),this.simulation||this.end(e),this.removePointer(t,e)}documentBlur(t){this.end(t),this._scopeFire("interactions:blur",{event:t,type:"blur",interaction:this})}end(t){let e;this._ending=!0,t=t||this._latestPointer.event,this.interacting()&&(e=this._doPhase({event:t,interaction:this,phase:"end"})),this._ending=!1,!0===e&&this.stop()}currentAction(){return this._interacting?this.prepared.name:null}interacting(){return this._interacting}stop(){this._scopeFire("interactions:stop",{interaction:this}),this.interactable=this.element=null,this._interacting=!1,this._stopped=!0,this.prepared.name=this.prevEvent=null}getPointerIndex(t){const e=Ht(t);return"mouse"===this.pointerType||"pen"===this.pointerType?this.pointers.length-1:Jt(this.pointers,(t=>t.id===e))}getPointerInfo(t){return this.pointers[this.getPointerIndex(t)]}updatePointer(t,e,n,o){const i=Ht(t);let r=this.getPointerIndex(t),s=this.pointers[r];return o=!1!==o&&(o||/(down|start)$/i.test(e.type)),s?s.pointer=t:(s=new hn(i,t,e,null,null),r=this.pointers.length,this.pointers.push(s)),Lt(this.coords.cur,this.pointers.map((t=>t.pointer)),this._now()),function(t,e,n){t.page.x=n.page.x-e.page.x,t.page.y=n.page.y-e.page.y,t.client.x=n.client.x-e.client.x,t.client.y=n.client.y-e.client.y,t.timeStamp=n.timeStamp-e.timeStamp}(this.coords.delta,this.coords.prev,this.coords.cur),o&&(this.pointerIsDown=!0,s.downTime=this.coords.cur.timeStamp,s.downTarget=n,kt(this.downPointer,t),this.interacting()||(At(this.coords.start,this.coords.cur),At(this.coords.prev,this.coords.cur),this.downEvent=e,this.pointerWasMoved=!1)),this._updateLatestPointer(t,e,n),this._scopeFire("interactions:update-pointer",{pointer:t,event:e,eventTarget:n,down:o,pointerInfo:s,pointerIndex:r,interaction:this}),r}removePointer(t,e){const n=this.getPointerIndex(t);if(-1===n)return;const o=this.pointers[n];this._scopeFire("interactions:remove-pointer",{pointer:t,event:e,eventTarget:null,pointerIndex:n,pointerInfo:o,interaction:this}),this.pointers.splice(n,1),this.pointerIsDown=!1}_updateLatestPointer(t,e,n){this._latestPointer.pointer=t,this._latestPointer.event=e,this._latestPointer.eventTarget=n}destroy(){this._latestPointer.pointer=null,this._latestPointer.event=null,this._latestPointer.eventTarget=null}_createPreparedEvent(t,e,n,o){return new dn(this,t,this.prepared.name,e,this.element,n,o)}_fireEvent(t){var e;null==(e=this.interactable)||e.fire(t),(!this.prevEvent||t.timeStamp>=this.prevEvent.timeStamp)&&(this.prevEvent=t)}_doPhase(t){const{event:e,phase:n,preEnd:o,type:i}=t,{rect:r}=this;if(r&&"move"===n&&(Ot(this.edges,r,this.coords.delta[this.interactable.options.deltaSource]),r.width=r.right-r.left,r.height=r.bottom-r.top),!1===this._scopeFire(`interactions:before-action-${n}`,t))return!1;const s=t.iEvent=this._createPreparedEvent(e,n,o,i);return this._scopeFire(`interactions:action-${n}`,t),"start"===n&&(this.prevEvent=s),this._fireEvent(s),this._scopeFire(`interactions:after-action-${n}`,t),!0}_now(){return Date.now()}}function yn({interaction:t}){bn(t)}function bn(t){if(!function(t){return!(!t.offset.pending.x&&!t.offset.pending.y)}(t))return!1;const{pending:e}=t.offset;return wn(t.coords.cur,e),wn(t.coords.delta,e),Ot(t.edges,t.rect,e),e.x=0,e.y=0,!0}function xn({x:t,y:e}){this.offset.pending.x+=t,this.offset.pending.y+=e,this.offset.total.x+=t,this.offset.total.y+=e}function wn({page:t,client:e},{x:n,y:o}){t.x+=n,t.y+=o,e.x+=n,e.y+=o}gn.offsetBy="";const En={id:"offset",before:["modifiers","pointer-events","actions","inertia"],install(t){t.Interaction.prototype.offsetBy=xn},listeners:{"interactions:new":({interaction:t})=>{t.offset={total:{x:0,y:0},pending:{x:0,y:0}}},"interactions:update-pointer":({interaction:t})=>function(t){!t.pointerIsDown||(wn(t.coords.cur,t.offset.total),t.offset.pending.x=0,t.offset.pending.y=0)}(t),"interactions:before-action-start":yn,"interactions:before-action-move":yn,"interactions:before-action-end":function({interaction:t}){if(bn(t))return t.move({offset:!0}),t.end(),!1},"interactions:stop":function({interaction:t}){t.offset.total.x=0,t.offset.total.y=0,t.offset.pending.x=0,t.offset.pending.y=0}}},Sn=En;class Tn{constructor(t){I(this,"active",!1),I(this,"isModified",!1),I(this,"smoothEnd",!1),I(this,"allowResume",!1),I(this,"modification"),I(this,"modifierCount",0),I(this,"modifierArg"),I(this,"startCoords"),I(this,"t0",0),I(this,"v0",0),I(this,"te",0),I(this,"targetOffset"),I(this,"modifiedOffset"),I(this,"currentOffset"),I(this,"lambda_v0",0),I(this,"one_ve_v0",0),I(this,"timeout"),I(this,"interaction"),this.interaction=t}start(t){const{interaction:e}=this,n=_n(e);if(!n||!n.enabled)return!1;const{client:o}=e.coords.velocity,i=Dt(o.x,o.y),r=this.modification||(this.modification=new rn(e));if(r.copyFrom(e.modification),this.t0=e._now(),this.allowResume=n.allowResume,this.v0=i,this.currentOffset={x:0,y:0},this.startCoords=e.coords.cur.page,this.modifierArg=r.fillArg({pageCoords:this.startCoords,preEnd:!0,phase:"inertiastart"}),this.t0-e.coords.cur.timeStamp<50&&i>n.minSpeed&&i>n.endSpeed)this.startInertia();else{if(r.result=r.setAll(this.modifierArg),!r.result.changed)return!1;this.startSmoothEnd()}return e.modification.result.rect=null,e.offsetBy(this.targetOffset),e._doPhase({interaction:e,event:t,phase:"inertiastart"}),e.offsetBy({x:-this.targetOffset.x,y:-this.targetOffset.y}),e.modification.result.rect=null,this.active=!0,e.simulation=this,!0}startInertia(){const t=this.interaction.coords.velocity.client,e=_n(this.interaction),n=e.resistance,o=-Math.log(e.endSpeed/this.v0)/n;this.targetOffset={x:(t.x-o)/n,y:(t.y-o)/n},this.te=o,this.lambda_v0=n/this.v0,this.one_ve_v0=1-e.endSpeed/this.v0;const{modification:i,modifierArg:r}=this;r.pageCoords={x:this.startCoords.x+this.targetOffset.x,y:this.startCoords.y+this.targetOffset.y},i.result=i.setAll(r),i.result.changed&&(this.isModified=!0,this.modifiedOffset={x:this.targetOffset.x+i.result.delta.x,y:this.targetOffset.y+i.result.delta.y}),this.onNextFrame((()=>this.inertiaTick()))}startSmoothEnd(){this.smoothEnd=!0,this.isModified=!0,this.targetOffset={x:this.modification.result.delta.x,y:this.modification.result.delta.y},this.onNextFrame((()=>this.smoothEndTick()))}onNextFrame(t){this.timeout=be.request((()=>{this.active&&t()}))}inertiaTick(){const{interaction:t}=this,e=_n(t).resistance,n=(t._now()-this.t0)/1e3;if(n<this.te){const o=1-(Math.exp(-e*n)-this.lambda_v0)/this.one_ve_v0;let i;i=this.isModified?function(t,e,n,o,i,r,s){return{x:On(s,t,n,i),y:On(s,e,o,r)}}(0,0,this.targetOffset.x,this.targetOffset.y,this.modifiedOffset.x,this.modifiedOffset.y,o):{x:this.targetOffset.x*o,y:this.targetOffset.y*o};const r={x:i.x-this.currentOffset.x,y:i.y-this.currentOffset.y};this.currentOffset.x+=r.x,this.currentOffset.y+=r.y,t.offsetBy(r),t.move(),this.onNextFrame((()=>this.inertiaTick()))}else t.offsetBy({x:this.modifiedOffset.x-this.currentOffset.x,y:this.modifiedOffset.y-this.currentOffset.y}),this.end()}smoothEndTick(){const{interaction:t}=this,e=t._now()-this.t0,{smoothEndDuration:n}=_n(t);if(e<n){const o={x:In(e,0,this.targetOffset.x,n),y:In(e,0,this.targetOffset.y,n)},i={x:o.x-this.currentOffset.x,y:o.y-this.currentOffset.y};this.currentOffset.x+=i.x,this.currentOffset.y+=i.y,t.offsetBy(i),t.move({skipModifiers:this.modifierCount}),this.onNextFrame((()=>this.smoothEndTick()))}else t.offsetBy({x:this.targetOffset.x-this.currentOffset.x,y:this.targetOffset.y-this.currentOffset.y}),this.end()}resume({pointer:t,event:e,eventTarget:n}){const{interaction:o}=this;o.offsetBy({x:-this.currentOffset.x,y:-this.currentOffset.y}),o.updatePointer(t,e,n,!0),o._doPhase({interaction:o,event:e,phase:"resume"}),At(o.coords.prev,o.coords.cur),this.stop()}end(){this.interaction.move(),this.interaction.end(),this.stop()}stop(){this.active=this.smoothEnd=!1,this.interaction.simulation=null,be.cancel(this.timeout)}}function _n({interactable:t,prepared:e}){return t&&t.options&&e.name&&t.options[e.name].inertia}const zn={id:"inertia",before:["modifiers","actions"],install:function(t){const{defaults:e}=t;t.usePlugin(Sn),t.usePlugin(un),t.actions.phases.inertiastart=!0,t.actions.phases.resume=!0,e.perAction.inertia={enabled:!1,resistance:10,minSpeed:100,endSpeed:10,allowResume:!0,smoothEndDuration:300}},listeners:{"interactions:new":({interaction:t})=>{t.inertia=new Tn(t)},"interactions:before-action-end":function({interaction:t,event:e}){return(!t._interacting||t.simulation||!t.inertia.start(e))&&null},"interactions:down":function(t){const{interaction:e,eventTarget:n}=t,o=e.inertia;if(!o.active)return;let i=n;for(;Z.element(i);){if(i===e.element){o.resume(t);break}i=pt(i)}},"interactions:stop":function({interaction:t}){const e=t.inertia;e.active&&e.stop()},"interactions:before-action-resume":t=>{const{modification:e}=t.interaction;e.stop(t),e.start(t,t.interaction.coords.cur.page),e.applyToInteraction(t)},"interactions:before-action-inertiastart":t=>t.interaction.modification.setAndApply(t),"interactions:action-resume":ln,"interactions:action-inertiastart":ln,"interactions:after-action-inertiastart":t=>t.interaction.modification.restoreInteractionCoords(t),"interactions:after-action-resume":t=>t.interaction.modification.restoreInteractionCoords(t)}};function On(t,e,n,o){const i=1-t;return i*i*e+2*i*t*n+t*t*o}function In(t,e,n,o){return-n*(t/=o)*(t-2)+e}const Pn=zn;function Mn(t,e){for(const n of e){if(t.immediatePropagationStopped)break;n(t)}}class Dn{constructor(t){I(this,"options"),I(this,"types",{}),I(this,"propagationStopped",!1),I(this,"immediatePropagationStopped",!1),I(this,"global"),this.options=Et({},t||{})}fire(t){let e;const n=this.global;(e=this.types[t.type])&&Mn(t,e),!t.propagationStopped&&n&&(e=n[t.type])&&Mn(t,e)}on(t,e){const n=Pt(t,e);for(t in n)this.types[t]=Ut(this.types[t]||[],n[t])}off(t,e){const n=Pt(t,e);for(t in n){const e=this.types[t];if(e&&e.length)for(const o of n[t]){const t=e.indexOf(o);-1!==t&&e.splice(t,1)}}}getRect(t){return null}}function kn(t,e){if(e.phaselessTypes[t])return!0;for(const n in e.map)if(0===t.indexOf(n)&&t.substr(n.length)in e.phases)return!0;return!1}class An{constructor(t,e,n,o){I(this,"options"),I(this,"_actions"),I(this,"target"),I(this,"events",new Dn),I(this,"_context"),I(this,"_win"),I(this,"_doc"),I(this,"_scopeEvents"),I(this,"_rectChecker"),this._actions=e.actions,this.target=t,this._context=e.context||n,this._win=K(wt(t)?this._context:t),this._doc=this._win.document,this._scopeEvents=o,this.set(e)}get _defaults(){return{base:{},perAction:{},actions:{}}}setOnEvents(t,e){return Z.func(e.onstart)&&this.on(`${t}start`,e.onstart),Z.func(e.onmove)&&this.on(`${t}move`,e.onmove),Z.func(e.onend)&&this.on(`${t}end`,e.onend),Z.func(e.oninertiastart)&&this.on(`${t}inertiastart`,e.oninertiastart),this}updatePerActionListeners(t,e,n){(Z.array(e)||Z.object(e))&&this.off(t,e),(Z.array(n)||Z.object(n))&&this.on(t,n)}setPerAction(t,e){const n=this._defaults;for(const o in e){const i=o,r=this.options[t],s=e[i];"listeners"===i&&this.updatePerActionListeners(t,r.listeners,s),Z.array(s)?r[i]=Kt(s):Z.plainObject(s)?(r[i]=Et(r[i]||{},on(s)),Z.object(n.perAction[i])&&"enabled"in n.perAction[i]&&(r[i].enabled=!1!==s.enabled)):Z.bool(s)&&Z.object(n.perAction[i])?r[i].enabled=s:r[i]=s}}getRect(t){return t=t||(Z.element(this.target)?this.target:null),Z.string(this.target)&&(t=t||this._context.querySelector(this.target)),bt(t)}rectChecker(t){return Z.func(t)?(this._rectChecker=t,this.getRect=t=>{const e=Et({},this._rectChecker(t));return"width"in e||(e.width=e.right-e.left,e.height=e.bottom-e.top),e},this):null===t?(delete this.getRect,delete this._rectChecker,this):this.getRect}_backCompatOption(t,e){if(wt(e)||Z.object(e)){this.options[t]=e;for(const n in this._actions.map)this.options[n][t]=e;return this}return this.options[t]}origin(t){return this._backCompatOption("origin",t)}deltaSource(t){return"page"===t||"client"===t?(this.options.deltaSource=t,this):this.options.deltaSource}context(){return this._context}inContext(t){return this._context===t.ownerDocument||ct(this._context,t)}testIgnoreAllow(t,e,n){return!this.testIgnore(t.ignoreFrom,e,n)&&this.testAllow(t.allowFrom,e,n)}testAllow(t,e,n){return!t||!!Z.element(n)&&(Z.string(t)?mt(n,t,e):!!Z.element(t)&&ct(t,n))}testIgnore(t,e,n){return!(!t||!Z.element(n))&&(Z.string(t)?mt(n,t,e):!!Z.element(t)&&ct(t,n))}fire(t){return this.events.fire(t),this}_onOff(t,e,n,o){Z.object(e)&&!Z.array(e)&&(o=n,n=null);const i="on"===t?"add":"remove",r=Pt(e,n);for(let s in r){"wheel"===s&&(s=lt.wheelEvent);for(const e of r[s])kn(s,this._actions)?this.events[t](s,e):Z.string(this.target)?this._scopeEvents[`${i}Delegate`](this.target,this._context,s,e,o):this._scopeEvents[i](this.target,s,e,o)}return this}on(t,e,n){return this._onOff("on",t,e,n)}off(t,e,n){return this._onOff("off",t,e,n)}set(t){const e=this._defaults;Z.object(t)||(t={}),this.options=on(e.base);for(const n in this._actions.methodDict){const o=n,i=this._actions.methodDict[o];this.options[o]={},this.setPerAction(o,Et(Et({},e.perAction),e.actions[o])),this[i](t[o])}for(const n in t)Z.func(this[n])&&this[n](t[n]);return this}unset(){if(Z.string(this.target))for(const t in this._scopeEvents.delegatedEvents){const e=this._scopeEvents.delegatedEvents[t];for(let n=e.length-1;n>=0;n--){const{selector:o,context:i,listeners:r}=e[n];o===this.target&&i===this._context&&e.splice(n,1);for(let e=r.length-1;e>=0;e--)this._scopeEvents.removeDelegate(this.target,this._context,t,r[e][0],r[e][1])}}else this._scopeEvents.remove(this.target,"all")}}class Cn{constructor(t){I(this,"list",[]),I(this,"selectorMap",{}),I(this,"scope"),this.scope=t,t.addListeners({"interactable:unset":({interactable:t})=>{const{target:e,_context:n}=t,o=Z.string(e)?this.selectorMap[e]:e[this.scope.id],i=Jt(o,(t=>t.context===n));o[i]&&(o[i].context=null,o[i].interactable=null),o.splice(i,1)}})}new(t,e){e=Et(e||{},{actions:this.scope.actions});const n=new this.scope.Interactable(t,e,this.scope.document,this.scope.events),o={context:n._context,interactable:n};return this.scope.addDocument(n._doc),this.list.push(n),Z.string(t)?(this.selectorMap[t]||(this.selectorMap[t]=[]),this.selectorMap[t].push(o)):(n.target[this.scope.id]||Object.defineProperty(t,this.scope.id,{value:[],configurable:!0}),t[this.scope.id].push(o)),this.scope.fire("interactable:new",{target:t,options:e,interactable:n,win:this.scope._win}),n}get(t,e){const n=e&&e.context||this.scope.document,o=Z.string(t),i=o?this.selectorMap[t]:t[this.scope.id];if(!i)return null;const r=Qt(i,(e=>e.context===n&&(o||e.interactable.inContext(t))));return r&&r.interactable}forEachMatch(t,e){for(const n of this.list){let o;if((Z.string(n.target)?Z.element(t)&&dt(t,n.target):t===n.target)&&n.inContext(t)&&(o=e(n)),void 0!==o)return o}}}class Rn{constructor(t){I(this,"currentTarget"),I(this,"originalEvent"),I(this,"type"),this.originalEvent=t,kt(this,t)}preventOriginalDefault(){this.originalEvent.preventDefault()}stopPropagation(){this.originalEvent.stopPropagation()}stopImmediatePropagation(){this.originalEvent.stopImmediatePropagation()}}function Nn(t){if(!Z.object(t))return{capture:!!t,passive:!1};const e=Et({},t);return e.capture=!!t.capture,e.passive=!!t.passive,e}const jn={id:"events",install:function(t){var e;const n=[],o={},i=[],r={add:s,remove:a,addDelegate:function(t,e,n,r,a){const u=Nn(a);if(!o[n]){o[n]=[];for(const t of i)s(t,n,l),s(t,n,c,!0)}const p=o[n];let d=Qt(p,(n=>n.selector===t&&n.context===e));d||(d={selector:t,context:e,listeners:[]},p.push(d)),d.listeners.push([r,u])},removeDelegate:function(t,e,n,i,r){const s=Nn(r),u=o[n];let p,d=!1;if(u)for(p=u.length-1;p>=0;p--){const o=u[p];if(o.selector===t&&o.context===e){const{listeners:t}=o;for(let o=t.length-1;o>=0;o--){const[r,{capture:h,passive:f}]=t[o];if(r===i&&h===s.capture&&f===s.passive){t.splice(o,1),t.length||(u.splice(p,1),a(e,n,l),a(e,n,c,!0)),d=!0;break}}if(d)break}}},delegateListener:l,delegateUseCapture:c,delegatedEvents:o,documents:i,targets:n,supportsOptions:!1,supportsPassive:!1};function s(t,e,o,i){const s=Nn(i);let a=Qt(n,(e=>e.eventTarget===t));a||(a={eventTarget:t,events:{}},n.push(a)),a.events[e]||(a.events[e]=[]),t.addEventListener&&!((t,e)=>-1!==t.indexOf(e))(a.events[e],o)&&(t.addEventListener(e,o,r.supportsOptions?s:s.capture),a.events[e].push(o))}function a(t,e,o,i){const s=Nn(i),l=Jt(n,(e=>e.eventTarget===t)),c=n[l];if(!c||!c.events)return;if("all"===e){for(e in c.events)c.events.hasOwnProperty(e)&&a(t,e,"all");return}let u=!1;const p=c.events[e];if(p){if("all"===o){for(let n=p.length-1;n>=0;n--)a(t,e,p[n],s);return}for(let n=0;n<p.length;n++)if(p[n]===o){t.removeEventListener(e,o,r.supportsOptions?s:s.capture),p.splice(n,1),0===p.length&&(delete c.events[e],u=!0);break}}u&&!Object.keys(c.events).length&&n.splice(l,1)}function l(t,e){const n=Nn(e),i=new Rn(t),r=o[t.type],[s]=Yt(t);let a=s;for(;Z.element(a);){for(let t=0;t<r.length;t++){const e=r[t],{selector:o,context:l}=e;if(dt(a,o)&&ct(l,s)&&ct(l,a)){const{listeners:t}=e;i.currentTarget=a;for(const[e,{capture:o,passive:r}]of t)o===n.capture&&r===n.passive&&e(i)}}a=pt(a)}}function c(t){return l.call(this,t,!0)}return null==(e=t.document)||e.createElement("div").addEventListener("test",null,{get capture(){return r.supportsOptions=!0},get passive(){return r.supportsPassive=!0}}),t.events=r,r}},Hn={methodOrder:["simulationResume","mouseOrPen","hasPointer","idle"],search(t){for(const e of Hn.methodOrder){const n=Hn[e](t);if(n)return n}return null},simulationResume({pointerType:t,eventType:e,eventTarget:n,scope:o}){if(!/down|start/i.test(e))return null;for(const i of o.interactions.list){let e=n;if(i.simulation&&i.simulation.allowResume&&i.pointerType===t)for(;e;){if(e===i.element)return i;e=pt(e)}}return null},mouseOrPen({pointerId:t,pointerType:e,eventType:n,scope:o}){if("mouse"!==e&&"pen"!==e)return null;let i;for(const r of o.interactions.list)if(r.pointerType===e){if(r.simulation&&!Ln(r,t))continue;if(r.interacting())return r;i||(i=r)}if(i)return i;for(const r of o.interactions.list)if(!(r.pointerType!==e||/down/i.test(n)&&r.simulation))return r;return null},hasPointer({pointerId:t,scope:e}){for(const n of e.interactions.list)if(Ln(n,t))return n;return null},idle({pointerType:t,scope:e}){for(const n of e.interactions.list){if(1===n.pointers.length){const t=n.interactable;if(t&&(!t.options.gesture||!t.options.gesture.enabled))continue}else if(n.pointers.length>=2)continue;if(!n.interacting()&&t===n.pointerType)return n}return null}};function Ln(t,e){return t.pointers.some((({id:t})=>t===e))}const Fn=Hn,Wn=["pointerDown","pointerMove","pointerUp","updatePointer","removePointer","windowBlur"];function Bn(t,e){return function(n){const o=e.interactions.list,i=Xt(n),[r,s]=Yt(n),a=[];if(/^touch/.test(n.type)){e.prevTouchTime=e.now();for(const t of n.changedTouches){const o={pointer:t,pointerId:Ht(t),pointerType:i,eventType:n.type,eventTarget:r,curEventTarget:s,scope:e},l=$n(o);a.push([o.pointer,o.eventTarget,o.curEventTarget,l])}}else{let t=!1;if(!lt.supportsPointerEvent&&/mouse/.test(n.type)){for(let e=0;e<o.length&&!t;e++)t="mouse"!==o[e].pointerType&&o[e].pointerIsDown;t=t||e.now()-e.prevTouchTime<500||0===n.timeStamp}if(!t){const t={pointer:n,pointerId:Ht(n),pointerType:i,eventType:n.type,curEventTarget:s,eventTarget:r,scope:e},o=$n(t);a.push([t.pointer,t.eventTarget,t.curEventTarget,o])}}for(const[e,l,c,u]of a)u[t](e,n,l,c)}}function $n(t){const{pointerType:e,scope:n}=t,o={interaction:Fn.search(t),searchDetails:t};return n.fire("interactions:find",o),o.interaction||n.interactions.new({pointerType:e})}function qn({doc:t,scope:e,options:n},o){const{interactions:{docEvents:i},events:r}=e,s=r[o];e.browser.isIOS&&!n.events&&(n.events={passive:!1});for(const l in r.delegatedEvents)s(t,l,r.delegateListener),s(t,l,r.delegateUseCapture,!0);const a=n&&n.events;for(const{type:l,listener:c}of i)s(t,l,c,a)}const Xn={id:"core/interactions",install:function(t){const e={};for(const r of Wn)e[r]=Bn(r,t);const n=lt.pEventTypes;let o;function i(){for(const e of t.interactions.list)if(e.pointerIsDown&&"touch"===e.pointerType&&!e._interacting)for(const n of e.pointers)t.documents.some((({doc:t})=>ct(t,n.downTarget)))||e.removePointer(n.pointer,n.event)}o=st.PointerEvent?[{type:n.down,listener:i},{type:n.down,listener:e.pointerDown},{type:n.move,listener:e.pointerMove},{type:n.up,listener:e.pointerUp},{type:n.cancel,listener:e.pointerUp}]:[{type:"mousedown",listener:e.pointerDown},{type:"mousemove",listener:e.pointerMove},{type:"mouseup",listener:e.pointerUp},{type:"touchstart",listener:i},{type:"touchstart",listener:e.pointerDown},{type:"touchmove",listener:e.pointerMove},{type:"touchend",listener:e.pointerUp},{type:"touchcancel",listener:e.pointerUp}],o.push({type:"blur",listener(e){for(const n of t.interactions.list)n.documentBlur(e)}}),t.prevTouchTime=0,t.Interaction=class extends vn{get pointerMoveTolerance(){return t.interactions.pointerMoveTolerance}set pointerMoveTolerance(e){t.interactions.pointerMoveTolerance=e}_now(){return t.now()}},t.interactions={list:[],new(e){e.scopeFire=(e,n)=>t.fire(e,n);const n=new t.Interaction(e);return t.interactions.list.push(n),n},listeners:e,docEvents:o,pointerMoveTolerance:1},t.usePlugin(Ve)},listeners:{"scope:add-document":t=>qn(t,"add"),"scope:remove-document":t=>qn(t,"remove"),"interactable:unset":({interactable:t},e)=>{for(let n=e.interactions.list.length-1;n>=0;n--){const o=e.interactions.list[n];o.interactable===t&&(o.stop(),e.fire("interactions:destroy",{interaction:o}),o.destroy(),e.interactions.list.length>2&&e.interactions.list.splice(n,1))}}},onDocSignal:qn,doOnInteractions:Bn,methodNames:Wn},Yn=Xn;function Vn(t){return t&&t.replace(/\/.*$/,"")}const Gn=new class{constructor(){I(this,"id",`__interact_scope_${Math.floor(100*Math.random())}`),I(this,"isInitialized",!1),I(this,"listenerMaps",[]),I(this,"browser",lt),I(this,"defaults",on(pn)),I(this,"Eventable",Dn),I(this,"actions",{map:{},phases:{start:!0,move:!0,end:!0},methodDict:{},phaselessTypes:{}}),I(this,"interactStatic",function(t){const e=(n,o)=>{let i=t.interactables.get(n,o);return i||(i=t.interactables.new(n,o),i.events.global=e.globalEvents),i};return e.getPointerAverage=Wt,e.getTouchBBox=Bt,e.getTouchDistance=$t,e.getTouchAngle=qt,e.getElementRect=bt,e.getElementClientRect=yt,e.matchesSelector=dt,e.closest=ut,e.globalEvents={},e.version="1.10.17",e.scope=t,e.use=function(t,e){return this.scope.usePlugin(t,e),this},e.isSet=function(t,e){return!!this.scope.interactables.get(t,e&&e.context)},e.on=_e((function(t,e,n){if(Z.string(t)&&-1!==t.search(" ")&&(t=t.trim().split(/ +/)),Z.array(t)){for(const o of t)this.on(o,e,n);return this}if(Z.object(t)){for(const n in t)this.on(n,t[n],e);return this}return kn(t,this.scope.actions)?this.globalEvents[t]?this.globalEvents[t].push(e):this.globalEvents[t]=[e]:this.scope.events.add(this.scope.document,t,e,{options:n}),this}),"The interact.on() method is being deprecated"),e.off=_e((function(t,e,n){if(Z.string(t)&&-1!==t.search(" ")&&(t=t.trim().split(/ +/)),Z.array(t)){for(const o of t)this.off(o,e,n);return this}if(Z.object(t)){for(const n in t)this.off(n,t[n],e);return this}if(kn(t,this.scope.actions)){let n;t in this.globalEvents&&-1!==(n=this.globalEvents[t].indexOf(e))&&this.globalEvents[t].splice(n,1)}else this.scope.events.remove(this.scope.document,t,e,n);return this}),"The interact.off() method is being deprecated"),e.debug=function(){return this.scope},e.supportsTouch=function(){return lt.supportsTouch},e.supportsPointerEvent=function(){return lt.supportsPointerEvent},e.stop=function(){for(const t of this.scope.interactions.list)t.stop();return this},e.pointerMoveTolerance=function(t){return Z.number(t)?(this.scope.interactions.pointerMoveTolerance=t,this):this.scope.interactions.pointerMoveTolerance},e.addDocument=function(t,e){this.scope.addDocument(t,e)},e.removeDocument=function(t){this.scope.removeDocument(t)},e}(this)),I(this,"InteractEvent",dn),I(this,"Interactable"),I(this,"interactables",new Cn(this)),I(this,"_win"),I(this,"document"),I(this,"window"),I(this,"documents",[]),I(this,"_plugins",{list:[],map:{}}),I(this,"onWindowUnload",(t=>this.removeDocument(t.target)));const t=this;this.Interactable=class extends An{get _defaults(){return t.defaults}set(e){return super.set(e),t.fire("interactable:set",{options:e,interactable:this}),this}unset(){super.unset();const e=t.interactables.list.indexOf(this);e<0||(super.unset(),t.interactables.list.splice(e,1),t.fire("interactable:unset",{interactable:this}))}}}addListeners(t,e){this.listenerMaps.push({id:e,map:t})}fire(t,e){for(const{map:{[t]:n}}of this.listenerMaps)if(n&&!1===n(e,this,t))return!1}init(t){return this.isInitialized?this:function(t,e){return t.isInitialized=!0,Z.window(e)&&U(e),st.init(e),lt.init(e),be.init(e),t.window=e,t.document=e.document,t.usePlugin(Yn),t.usePlugin(jn),t}(this,t)}pluginIsInstalled(t){return this._plugins.map[t.id]||-1!==this._plugins.list.indexOf(t)}usePlugin(t,e){if(!this.isInitialized)return this;if(this.pluginIsInstalled(t))return this;if(t.id&&(this._plugins.map[t.id]=t),this._plugins.list.push(t),t.install&&t.install(this,e),t.listeners&&t.before){let e=0;const n=this.listenerMaps.length,o=t.before.reduce(((t,e)=>(t[e]=!0,t[Vn(e)]=!0,t)),{});for(;e<n;e++){const t=this.listenerMaps[e].id;if(o[t]||o[Vn(t)])break}this.listenerMaps.splice(e,0,{id:t.id,map:t.listeners})}else t.listeners&&this.listenerMaps.push({id:t.id,map:t.listeners});return this}addDocument(t,e){if(-1!==this.getDocIndex(t))return!1;const n=K(t);e=e?Et({},e):{},this.documents.push({doc:t,options:e}),this.events.documents.push(t),t!==this.document&&this.events.add(n,"unload",this.onWindowUnload),this.fire("scope:add-document",{doc:t,window:n,scope:this,options:e})}removeDocument(t){const e=this.getDocIndex(t),n=K(t),o=this.documents[e].options;this.events.remove(n,"unload",this.onWindowUnload),this.documents.splice(e,1),this.events.documents.splice(e,1),this.fire("scope:remove-document",{doc:t,window:n,scope:this,options:o})}getDocIndex(t){for(let e=0;e<this.documents.length;e++)if(this.documents[e].doc===t)return e;return-1}getDocOptions(t){const e=this.getDocIndex(t);return-1===e?null:this.documents[e].options}now(){return(this.window.Date||Date).now()}},Un=Gn.interactStatic,Kn=typeof globalThis<"u"?globalThis:typeof window<"u"?window:globalThis;Gn.init(Kn);const Jn=Object.freeze(Object.defineProperty({__proto__:null,edgeTarget:()=>{},elements:()=>{},grid:t=>{const e=[["x","y"],["left","top"],["right","bottom"],["width","height"]].filter((([e,n])=>e in t||n in t)),n=(n,o)=>{const{range:i,limits:r={left:-1/0,right:1/0,top:-1/0,bottom:1/0},offset:s={x:0,y:0}}=t,a={range:i,grid:t,x:null,y:null};for(const[l,c]of e){const e=Math.round((n-s.x)/t[l]),i=Math.round((o-s.y)/t[c]);a[l]=Math.max(r.left,Math.min(r.right,e*t[l]+s.x)),a[c]=Math.max(r.top,Math.min(r.bottom,i*t[c]+s.y))}return a};return n.grid=t,n.coordFields=e,n}},Symbol.toStringTag,{value:"Module"})),Qn={id:"snappers",install(t){const{interactStatic:e}=t;e.snappers=Et(e.snappers||{},Jn),e.createSnapGrid=e.snappers.grid}},Zn=Qn,to={start(t){const{state:e,rect:n,edges:o,pageCoords:i}=t;let{ratio:r}=e.options;const{equalDelta:s,modifiers:a}=e.options;"preserve"===r&&(r=n.width/n.height),e.startCoords=Et({},i),e.startRect=Et({},n),e.ratio=r,e.equalDelta=s;const l=e.linkedEdges={top:o.top||o.left&&!o.bottom,left:o.left||o.top&&!o.right,bottom:o.bottom||o.right&&!o.top,right:o.right||o.bottom&&!o.left};if(e.xIsPrimaryAxis=!(!o.left&&!o.right),e.equalDelta){const t=(l.left?1:-1)*(l.top?1:-1);e.edgeSign={x:t,y:t}}else e.edgeSign={x:l.left?-1:1,y:l.top?-1:1};if(Et(t.edges,l),!a||!a.length)return;const c=new rn(t.interaction);c.copyFrom(t.interaction.modification),c.prepareStates(a),e.subModification=c,c.startAll({...t})},set(t){const{state:e,rect:n,coords:o}=t,i=Et({},o),r=e.equalDelta?eo:no;if(r(e,e.xIsPrimaryAxis,o,n),!e.subModification)return null;const s=Et({},n);Ot(e.linkedEdges,s,{x:o.x-i.x,y:o.y-i.y});const a=e.subModification.setAll({...t,rect:s,edges:e.linkedEdges,pageCoords:o,prevCoords:o,prevRect:s}),{delta:l}=a;if(a.changed){r(e,Math.abs(l.x)>Math.abs(l.y),a.coords,a.rect),Et(o,a.coords)}return a.eventProps},defaults:{ratio:"preserve",equalDelta:!1,modifiers:[],enabled:!1}};function eo({startCoords:t,edgeSign:e},n,o){n?o.y=t.y+(o.x-t.x)*e.y:o.x=t.x+(o.y-t.y)*e.x}function no({startRect:t,startCoords:e,ratio:n,edgeSign:o},i,r,s){if(i){const i=s.width/n;r.y=e.y+(i-t.height)*o.y}else{const i=s.height*n;r.x=e.x+(i-t.width)*o.x}}const oo=an(to,"aspectRatio"),io=()=>{};io._defaults={};const ro=io;function so(t,e,n){return Z.func(t)?Tt(t,e.interactable,e.element,[n.x,n.y,e]):Tt(t,e.interactable,e.element)}const ao={start:function({rect:t,startOffset:e,state:n,interaction:o,pageCoords:i}){const{options:r}=n,{elementRect:s}=r,a=Et({left:0,top:0,right:0,bottom:0},r.offset||{});if(t&&s){const n=so(r.restriction,o,i);if(n){const e=n.right-n.left-t.width,o=n.bottom-n.top-t.height;e<0&&(a.left+=e,a.right+=e),o<0&&(a.top+=o,a.bottom+=o)}a.left+=e.left-t.width*s.left,a.top+=e.top-t.height*s.top,a.right+=e.right-t.width*(1-s.right),a.bottom+=e.bottom-t.height*(1-s.bottom)}n.offset=a},set:function({coords:t,interaction:e,state:n}){const{options:o,offset:i}=n,r=so(o.restriction,e,t);if(!r)return;const s=function(t){return t&&!("left"in t&&"top"in t)&&((t=Et({},t)).left=t.x||0,t.top=t.y||0,t.right=t.right||t.left+t.width,t.bottom=t.bottom||t.top+t.height),t}(r);t.x=Math.max(Math.min(s.right-i.right,t.x),s.left+i.left),t.y=Math.max(Math.min(s.bottom-i.bottom,t.y),s.top+i.top)},defaults:{restriction:null,elementRect:null,offset:null,endOnly:!1,enabled:!1}},lo=an(ao,"restrict"),co={top:1/0,left:1/0,bottom:-1/0,right:-1/0},uo={top:-1/0,left:-1/0,bottom:1/0,right:1/0};function po(t,e){for(const n of["top","left","bottom","right"])n in t||(t[n]=e[n]);return t}const ho={noInner:co,noOuter:uo,start:function({interaction:t,startOffset:e,state:n}){const{options:o}=n;let i;if(o){i=_t(so(o.offset,t,t.coords.start.page))}i=i||{x:0,y:0},n.offset={top:i.y+e.top,left:i.x+e.left,bottom:i.y-e.bottom,right:i.x-e.right}},set:function({coords:t,edges:e,interaction:n,state:o}){const{offset:i,options:r}=o;if(!e)return;const s=Et({},t),a=so(r.inner,n,s)||{},l=so(r.outer,n,s)||{};po(a,co),po(l,uo),e.top?t.y=Math.min(Math.max(l.top+i.top,s.y),a.top+i.top):e.bottom&&(t.y=Math.max(Math.min(l.bottom+i.bottom,s.y),a.bottom+i.bottom)),e.left?t.x=Math.min(Math.max(l.left+i.left,s.x),a.left+i.left):e.right&&(t.x=Math.max(Math.min(l.right+i.right,s.x),a.right+i.right))},defaults:{inner:null,outer:null,offset:null,endOnly:!1,enabled:!1}},fo=an(ho,"restrictEdges"),go=Et({get elementRect(){return{top:0,left:0,bottom:1,right:1}},set elementRect(t){}},ao.defaults),mo=an({start:ao.start,set:ao.set,defaults:go},"restrictRect"),vo={width:-1/0,height:-1/0},yo={width:1/0,height:1/0};const bo={start:function(t){return ho.start(t)},set:function(t){const{interaction:e,state:n,rect:o,edges:i}=t,{options:r}=n;if(!i)return;const s=zt(so(r.min,e,t.coords))||vo,a=zt(so(r.max,e,t.coords))||yo;n.options={endOnly:r.endOnly,inner:Et({},ho.noInner),outer:Et({},ho.noOuter)},i.top?(n.options.inner.top=o.bottom-s.height,n.options.outer.top=o.bottom-a.height):i.bottom&&(n.options.inner.bottom=o.top+s.height,n.options.outer.bottom=o.top+a.height),i.left?(n.options.inner.left=o.right-s.width,n.options.outer.left=o.right-a.width):i.right&&(n.options.inner.right=o.left+s.width,n.options.outer.right=o.left+a.width),ho.set(t),n.options=r},defaults:{min:null,max:null,endOnly:!1,enabled:!1}},xo=an(bo,"restrictSize");const wo={start:function(t){const{interaction:e,interactable:n,element:o,rect:i,state:r,startOffset:s}=t,{options:a}=r,l=a.offsetWithOrigin?function(t){const{element:e}=t.interaction;return _t(Tt(t.state.options.origin,null,null,[e]))||It(t.interactable,e,t.interaction.prepared.name)}(t):{x:0,y:0};let c;if("startCoords"===a.offset)c={x:e.coords.start.page.x,y:e.coords.start.page.y};else{const t=Tt(a.offset,n,o,[e]);c=_t(t)||{x:0,y:0},c.x+=l.x,c.y+=l.y}const{relativePoints:u}=a;r.offsets=i&&u&&u.length?u.map(((t,e)=>({index:e,relativePoint:t,x:s.left-i.width*t.x+c.x,y:s.top-i.height*t.y+c.y}))):[{index:0,relativePoint:null,x:c.x,y:c.y}]},set:function(t){const{interaction:e,coords:n,state:o}=t,{options:i,offsets:r}=o,s=It(e.interactable,e.element,e.prepared.name),a=Et({},n),l=[];i.offsetWithOrigin||(a.x-=s.x,a.y-=s.y);for(const u of r){const t=a.x-u.x,n=a.y-u.y;for(let o=0,r=i.targets.length;o<r;o++){const r=i.targets[o];let s;s=Z.func(r)?r(t,n,e._proxy,u,o):r,s&&l.push({x:(Z.number(s.x)?s.x:t)+u.x,y:(Z.number(s.y)?s.y:n)+u.y,range:Z.number(s.range)?s.range:i.range,source:r,index:o,offset:u})}}const c={target:null,inRange:!1,distance:0,range:0,delta:{x:0,y:0}};for(const u of l){const t=u.range,e=u.x-a.x,n=u.y-a.y,o=Dt(e,n);let i=o<=t;t===1/0&&c.inRange&&c.range!==1/0&&(i=!1),(!c.target||(i?c.inRange&&t!==1/0?o/t<c.distance/c.range:t===1/0&&c.range!==1/0||o<c.distance:!c.inRange&&o<c.distance))&&(c.target=u,c.distance=o,c.range=t,c.inRange=i,c.delta.x=e,c.delta.y=n)}return c.inRange&&(n.x=c.target.x,n.y=c.target.y),o.closest=c,c},defaults:{range:1/0,targets:null,offset:null,offsetWithOrigin:!0,origin:null,relativePoints:null,endOnly:!1,enabled:!1}},Eo=an(wo,"snap");const So={start:function(t){const{state:e,edges:n}=t,{options:o}=e;if(!n)return null;t.state={options:{targets:null,relativePoints:[{x:n.left?0:1,y:n.top?0:1}],offset:o.offset||"self",origin:{x:0,y:0},range:o.range}},e.targetFields=e.targetFields||[["width","height"],["x","y"]],wo.start(t),e.offsets=t.state.offsets,t.state=e},set:function(t){const{interaction:e,state:n,coords:o}=t,{options:i,offsets:r}=n,s={x:o.x-r[0].x,y:o.y-r[0].y};n.options=Et({},i),n.options.targets=[];for(const l of i.targets||[]){let t;if(t=Z.func(l)?l(s.x,s.y,e):l,t){for(const[e,o]of n.targetFields)if(e in t||o in t){t.x=t[e],t.y=t[o];break}n.options.targets.push(t)}}const a=wo.set(t);return n.options=i,a},defaults:{range:1/0,targets:null,offset:null,endOnly:!1,enabled:!1}},To=an(So,"snapSize");const _o={start:function(t){const{edges:e}=t;return e?(t.state.targetFields=t.state.targetFields||[[e.left?"left":"right",e.top?"top":"bottom"]],So.start(t)):null},set:So.set,defaults:Et(on(So.defaults),{targets:null,range:null,offset:{x:0,y:0}})},zo={aspectRatio:oo,restrictEdges:fo,restrict:lo,restrictRect:mo,restrictSize:xo,snapEdges:an(_o,"snapEdges"),snap:Eo,snapSize:To,spring:ro,avoid:ro,transform:ro,rubberband:ro},Oo={id:"modifiers",install(t){const{interactStatic:e}=t;t.usePlugin(un),t.usePlugin(Zn),e.modifiers=zo;for(const n in zo){const{_defaults:e,_methods:o}=zo[n];e._methods=o,t.defaults.perAction[n]=e}}},Io=Oo;class Po extends Gt{constructor(t,e,n,o,i,r){if(super(i),kt(this,n),n!==e&&kt(this,e),this.timeStamp=r,this.originalEvent=n,this.type=t,this.pointerId=Ht(e),this.pointerType=Xt(e),this.target=o,this.currentTarget=null,"tap"===t){const t=i.getPointerIndex(e);this.dt=this.timeStamp-i.pointers[t].downTime;const n=this.timeStamp-i.tapTime;this.double=!!i.prevTap&&"doubletap"!==i.prevTap.type&&i.prevTap.target===this.target&&n<500}else"doubletap"===t&&(this.dt=e.timeStamp-i.tapTime,this.double=!0)}_subtractOrigin({x:t,y:e}){return this.pageX-=t,this.pageY-=e,this.clientX-=t,this.clientY-=e,this}_addOrigin({x:t,y:e}){return this.pageX+=t,this.pageY+=e,this.clientX+=t,this.clientY+=e,this}preventDefault(){this.originalEvent.preventDefault()}}const Mo={id:"pointer-events/base",before:["inertia","modifiers","auto-start","actions"],install:function(t){t.pointerEvents=Mo,t.defaults.actions.pointerEvents=Mo.defaults,Et(t.actions.phaselessTypes,Mo.types)},listeners:{"interactions:new":function({interaction:t}){t.prevTap=null,t.tapTime=0},"interactions:update-pointer":function({down:t,pointerInfo:e}){!t&&e.hold||(e.hold={duration:1/0,timeout:null})},"interactions:move":function(t,e){const{interaction:n,pointer:o,event:i,eventTarget:r,duplicate:s}=t;!s&&(!n.pointerIsDown||n.pointerWasMoved)&&(n.pointerIsDown&&Ao(t),Do({interaction:n,pointer:o,event:i,eventTarget:r,type:"move"},e))},"interactions:down":(t,e)=>{(function({interaction:t,pointer:e,event:n,eventTarget:o,pointerIndex:i},r){const s=t.pointers[i].hold,a=xt(o),l={interaction:t,pointer:e,event:n,eventTarget:o,type:"hold",targets:[],path:a,node:null};for(const u of a)l.node=u,r.fire("pointerEvents:collect-targets",l);if(!l.targets.length)return;let c=1/0;for(const u of l.targets){const t=u.eventable.options.holdDuration;t<c&&(c=t)}s.duration=c,s.timeout=setTimeout((()=>{Do({interaction:t,eventTarget:o,pointer:e,event:n,type:"hold"},r)}),c)})(t,e),Do(t,e)},"interactions:up":(t,e)=>{Ao(t),Do(t,e),function({interaction:t,pointer:e,event:n,eventTarget:o},i){t.pointerWasMoved||Do({interaction:t,eventTarget:o,pointer:e,event:n,type:"tap"},i)}(t,e)},"interactions:cancel":(t,e)=>{Ao(t),Do(t,e)}},PointerEvent:Po,fire:Do,collectEventTargets:ko,defaults:{holdDuration:600,ignoreFrom:null,allowFrom:null,origin:{x:0,y:0}},types:{down:!0,move:!0,up:!0,cancel:!0,tap:!0,doubletap:!0,hold:!0}};function Do(t,e){const{interaction:n,pointer:o,event:i,eventTarget:r,type:s,targets:a=ko(t,e)}=t,l=new Po(s,o,i,r,n,e.now());e.fire("pointerEvents:new",{pointerEvent:l});const c={interaction:n,pointer:o,event:i,eventTarget:r,targets:a,type:s,pointerEvent:l};for(let u=0;u<a.length;u++){const t=a[u];for(const n in t.props||{})l[n]=t.props[n];const e=It(t.eventable,t.node);if(l._subtractOrigin(e),l.eventable=t.eventable,l.currentTarget=t.node,t.eventable.fire(l),l._addOrigin(e),l.immediatePropagationStopped||l.propagationStopped&&u+1<a.length&&a[u+1].node!==l.currentTarget)break}if(e.fire("pointerEvents:fired",c),"tap"===s){const t=l.double?Do({interaction:n,pointer:o,event:i,eventTarget:r,type:"doubletap"},e):l;n.prevTap=t,n.tapTime=t.timeStamp}return l}function ko({interaction:t,pointer:e,event:n,eventTarget:o,type:i},r){const s=t.getPointerIndex(e),a=t.pointers[s];if("tap"===i&&(t.pointerWasMoved||!a||a.downTarget!==o))return[];const l=xt(o),c={interaction:t,pointer:e,event:n,eventTarget:o,type:i,path:l,targets:[],node:null};for(const u of l)c.node=u,r.fire("pointerEvents:collect-targets",c);return"hold"===i&&(c.targets=c.targets.filter((e=>{var n;return e.eventable.options.holdDuration===(null==(n=t.pointers[s])?void 0:n.hold.duration)}))),c.targets}function Ao({interaction:t,pointerIndex:e}){const n=t.pointers[e].hold;n&&n.timeout&&(clearTimeout(n.timeout),n.timeout=null)}const Co=Object.freeze(Object.defineProperty({__proto__:null,default:Mo},Symbol.toStringTag,{value:"Module"}));function Ro({interaction:t}){t.holdIntervalHandle&&(clearInterval(t.holdIntervalHandle),t.holdIntervalHandle=null)}const No={id:"pointer-events/holdRepeat",install:function(t){t.usePlugin(Mo);const{pointerEvents:e}=t;e.defaults.holdRepeatInterval=0,e.types.holdrepeat=t.actions.phaselessTypes.holdrepeat=!0},listeners:["move","up","cancel","endall"].reduce(((t,e)=>(t[`pointerEvents:${e}`]=Ro,t)),{"pointerEvents:new":function({pointerEvent:t}){"hold"===t.type&&(t.count=(t.count||0)+1)},"pointerEvents:fired":function({interaction:t,pointerEvent:e,eventTarget:n,targets:o},i){if("hold"!==e.type||!o.length)return;const r=o[0].eventable.options.holdRepeatInterval;r<=0||(t.holdIntervalHandle=setTimeout((()=>{i.pointerEvents.fire({interaction:t,eventTarget:n,type:"hold",pointer:e,event:e},i)}),r))}})},jo=No;function Ho(t){return Et(this.events.options,t),this}const Lo={id:"pointer-events/interactableTargets",install:function(t){const{Interactable:e}=t;e.prototype.pointerEvents=Ho;const n=e.prototype._backCompatOption;e.prototype._backCompatOption=function(t,e){const o=n.call(this,t,e);return o===this&&(this.events.options[t]=e),o}},listeners:{"pointerEvents:collect-targets":({targets:t,node:e,type:n,eventTarget:o},i)=>{i.interactables.forEachMatch(e,(i=>{const r=i.events,s=r.options;r.types[n]&&r.types[n].length&&i.testIgnoreAllow(s,e,o)&&t.push({node:e,eventable:r,props:{interactable:i}})}))},"interactable:new":({interactable:t})=>{t.events.getRect=function(e){return t.getRect(e)}},"interactable:set":({interactable:t,options:e},n)=>{Et(t.events.options,n.pointerEvents.defaults),Et(t.events.options,e.pointerEvents||{})}}},Fo=Lo,Wo={id:"pointer-events",install(t){t.usePlugin(Co),t.usePlugin(jo),t.usePlugin(Fo)}},Bo=Wo;function $o(t,e,n,o,i){const r=t.interactions.new({pointerType:"reflow"}),s={interaction:r,event:i,pointer:i,eventTarget:n,phase:"reflow"};r.interactable=e,r.element=n,r.prevEvent=i,r.updatePointer(i,i,n,!0),Ct(r.coords.delta),ze(r.prepared,o),r._doPhase(s);const{Promise:a}=t.window,l=a?new a((t=>{r._reflowResolve=t})):void 0;return r._reflowPromise=l,r.start(o,e,n),r._interacting?(r.move(s),r.end(i)):(r.stop(),r._reflowResolve()),r.removePointer(i,i),l}const qo={id:"reflow",install:function(t){const{Interactable:e}=t;t.actions.phases.reflow=!0,e.prototype.reflow=function(e){return function(t,e,n){const o=Z.string(t.target)?Kt(t._context.querySelectorAll(t.target)):[t.target],i=n.window.Promise,r=i?[]:null;for(const s of o){const o=t.getRect(s);if(!o)break;const a=Qt(n.interactions.list,(n=>n.interacting()&&n.interactable===t&&n.element===s&&n.prepared.name===e.name));let l;if(a)a.move(),r&&(l=a._reflowPromise||new i((t=>{a._reflowResolve=t})));else{const i=zt(o),r=Vt({page:{x:i.x,y:i.y},client:{x:i.x,y:i.y},timeStamp:n.now()});l=$o(n,t,s,e,r)}r&&r.push(l)}return r&&i.all(r).then((()=>t))}(this,e,t)}},listeners:{"interactions:stop":({interaction:t},e)=>{"reflow"===t.pointerType&&(t._reflowResolve&&t._reflowResolve(),((t,e)=>{t.splice(t.indexOf(e),1)})(e.interactions.list,t))}}},Xo=qo;if(Un.use(Ve),Un.use(Sn),Un.use(Bo),Un.use(Pn),Un.use(Io),Un.use(qe),Un.use(ge),Un.use(Te),Un.use(Xo),Un.use(nn),"object"==typeof module&&module)try{module.exports=Un}catch{}Un.default=Un;const Yo=(t,e,n,o)=>(t=>!isNaN(t))(t)?{deltaX:n-t,deltaY:o-e,lastX:t,lastY:e,x:n,y:o}:{deltaX:0,deltaY:0,lastX:n,lastY:o,x:n,y:o},Vo=t=>(t=>{const e=t.target.offsetParent||document.body,n=t.offsetParent===document.body?{left:0,top:0}:e.getBoundingClientRect();return{x:t.clientX+e.scrollLeft-n.left,y:t.clientY+e.scrollTop-n.top}})(t),Go=t({__name:"GridItem",props:{breakpointCols:{required:!0,type:Object},colNum:{required:!0,type:Number},containerWidth:{required:!0,type:Number},h:{required:!0,type:Number},i:{required:!0,type:Number},isDraggable:{required:!0,type:Boolean},isResizable:{required:!0,type:Boolean},lastBreakpoint:{required:!0,type:String},margin:{required:!0,type:Array},maxH:{default:1/0,type:Number},maxRows:{required:!0,type:Number},maxW:{default:1/0,type:Number},minH:{default:1,type:Number},minW:{default:1,type:Number},observer:{default:void 0,type:[IntersectionObserver,void 0]},rowHeight:{required:!0,type:Number},static:{default:!1,type:Boolean},useCssTransforms:{required:!0,type:Boolean},w:{required:!0,type:Number},x:{required:!0,type:Number},y:{required:!0,type:Number}},emits:["container-resized","resize","resized","move","moved","drag-event","resize-event"],setup(t,{emit:g}){const m=t,v=e(null),y=n(P),b="vue-resizable-handle",x=e(m.colNum),w=e(!1),E=e({}),S=e({h:m.h,w:m.w,x:m.x,y:m.y}),T=e(null),_=e(!1),z=e(!1),O=e({h:NaN,w:NaN,x:NaN,y:NaN}),I=e({h:NaN,w:NaN,x:NaN,y:NaN}),M=e(!1),D=e(null),k=o({props:{}}),A=i((()=>({"css-transforms":m.useCssTransforms,"disable-user-select":_.value,"no-touch":C.value,resizing:z.value,static:m.static,"vue-draggable-dragging":_.value,"vue-resizable":R.value}))),C=i((()=>{const t=(m.isDraggable||m.isResizable)&&!m.static,e=-1!==navigator.userAgent.toLowerCase().indexOf("android");return t&&e})),R=i((()=>m.isResizable&&!m.static));r((()=>m.observer),(()=>{m.observer&&v.value&&(m.observer.observe(v.value),v.value.__INTERSECTION_OBSERVER_INDEX__=m.i)})),r((()=>x.value),(()=>{X(),L()})),r((()=>m.containerWidth),(()=>{X(),L()})),r((()=>m.h),(t=>{S.value.h=t,L()})),r((()=>m.isDraggable),(()=>{$()})),r((()=>m.isResizable),(()=>{X()})),r((()=>m.maxH),(()=>{X()})),r((()=>m.maxW),(()=>{X()})),r((()=>m.minH),(()=>{X()})),r((()=>m.minW),(()=>{X()})),r((()=>m.rowHeight),(()=>{L()})),r((()=>m.static),(()=>{X(),$()})),r((()=>m.w),(t=>{S.value.w=t,H()})),r((()=>m.x),(t=>{S.value.x=t,H()})),r((()=>m.y),(t=>{S.value.y=t,H()}));const N=()=>{const[t]=m.margin;return(m.containerWidth-t*(x.value+1))/x.value},j=(t,e,n,o)=>{const i=N(),[r,s]=m.margin;return{height:o===1/0?o:Math.round(m.rowHeight*o+Math.max(0,o-1)*s),left:Math.round(i*t+(t+1)*s),top:Math.round(m.rowHeight*e+(e+1)*s),width:n===1/0?n:Math.round(i*n+Math.max(0,n-1)*r)}},H=()=>{var t,e,n,o,i,r;const s=j(S.value.x,S.value.y,S.value.w,S.value.h);m.x+m.w>x.value?(S.value.x=0,S.value.w=m.w>x.value?x.value:m.w):(S.value.x=m.x,S.value.w=m.w),_.value&&(s.top=null!=(t=E.value.top)?t:0,s.left=null!=(e=E.value.left)?e:0),z.value&&(s.width=null!=(o=null==(n=null==D?void 0:D.value)?void 0:n.width)?o:0,s.height=null!=(r=null==(i=null==D?void 0:D.value)?void 0:i.height)?r:0),k.props=m.useCssTransforms?((t,e,n,o)=>({height:`${o}px`,position:"absolute",transform:`translate3d(${e}px,${t}px, 0)`,width:`${n}px`}))(s.top,s.left,s.width,s.height):((t,e,n,o)=>({height:`${o}px`,left:`${e}px`,position:"absolute",top:`${t}px`,width:`${n}px`}))(s.top,s.left,s.width,s.height)},L=()=>{H();const t={};for(const e of["width","height"]){const n=k.props[e],o=null==n?void 0:n.toString().match(/^(\d+)px$/);if(!o)return;t[e]=+o[1]}g("container-resized",{h:m.h,height:t.height,i:m.i,w:m.w,width:t.width})},F=t=>{var e,n,o,i;if(m.static||z.value)return;const r=Vo(t);if(!r)return;const{x:s,y:a}=r,l={left:0,top:0};switch(t.type){case"dragstart":{I.value.x=S.value.x,I.value.y=S.value.y;const e=t.target.offsetParent.getBoundingClientRect(),n=t.target.getBoundingClientRect();l.left=n.left-e.left,l.top=n.top-e.top,E.value=l,_.value=!0;break}case"dragend":{if(!_.value)return;const e=t.target.offsetParent.getBoundingClientRect(),n=t.target.getBoundingClientRect();l.left=n.left-e.left,l.top=n.top-e.top,E.value={},_.value=!1;break}case"dragmove":{const t=Yo(O.value.x,O.value.y,s,a);l.left=(null!=(n=null==(e=null==E?void 0:E.value)?void 0:e.left)?n:0)+t.deltaX,l.top=(null!=(i=null==(o=null==E?void 0:E.value)?void 0:o.top)?i:0)+t.deltaY,E.value=l;break}}const c=((t,e)=>{const n=N(),[o,i]=m.margin,r=Math.round((e-o)/(n+o)),s=Math.round((t-i)/(m.rowHeight+i));return{x:Math.max(Math.min(r,x.value-S.value.w),0),y:Math.max(Math.min(s,m.maxRows-S.value.h),0)}})(l.top,l.left);O.value.x=s,O.value.y=a,(S.value.x!==c.x||S.value.y!==c.y)&&g("move",m.i,c.x,c.y),"dragend"===t.type&&(I.value.x!==S.value.x||I.value.y!==S.value.y)&&g("moved",m.i,c.x,c.y),null==y||y.emit("drag-event",[t.type,m.i,c.x,c.y,S.value.h,S.value.w])},W=t=>{var e,n,o,i;if(m.static)return;const r=Vo(t);if(!r)return;const{x:s,y:a}=r,l={height:0,width:0};switch(t.type){case"resizestart":{I.value.w=S.value.w,I.value.h=S.value.h;const{height:t,width:e}=j(S.value.x,S.value.y,S.value.w,S.value.h);l.width=e,l.height=t,D.value=l,z.value=!0;break}case"resizemove":{const t=Yo(O.value.x,O.value.h,s,a);l.width=(null!=(n=null==(e=null==D?void 0:D.value)?void 0:e.width)?n:0)+t.deltaX,l.height=(null!=(i=null==(o=null==D?void 0:D.value)?void 0:o.height)?i:0)+t.deltaY,D.value=l,z.value=!0;break}case"resizeend":{const{height:t,width:e}=j(S.value.x,S.value.y,S.value.w,S.value.h);l.width=e,l.height=t,D.value=null,z.value=!1;break}}const c=((t,e)=>{const n=N(),[o,i]=m.margin,r=Math.round((e+o)/(n+o)),s=Math.round((t+i)/(m.rowHeight+i));return{h:Math.max(Math.min(s,m.maxRows-S.value.y),0),w:Math.max(Math.min(r,x.value-S.value.x),0)}})(l.height,l.width);c.w<m.minW&&(c.w=m.minW),c.w>m.maxW&&(c.w=m.maxW),c.h<m.minH&&(c.h=m.minH),c.h>m.maxH&&(c.h=m.maxH),c.h<1&&(c.h=1),c.w<1&&(c.w=1),O.value.x=s,O.value.h=a,(S.value.w!==c.w||S.value.h!==c.h)&&g("resize",m.i,c.h,c.w,l.height,l.width),"resizeend"===t.type&&(I.value.w!==S.value.w||I.value.h!==S.value.h)&&g("resized",m.i,c.h,c.w,l.height,l.width),null==y||y.emit("resize-event",[t.type,m.i,S.value.x,S.value.y,c.h,c.w])},B=t=>{x.value=t},$=()=>{!T.value&&v.value&&(T.value=Un(v.value)),m.isDraggable&&!m.static?(T.value.draggable({ignoreFrom:"a, button"}),w.value||(w.value=!0,T.value.on("dragstart dragmove dragend",F))):T.value.draggable({enabled:!1})},X=()=>{if(!T.value&&v.value&&(T.value=Un(v.value)),m.isResizable&&!m.static){const t=`.${((t,e,n)=>t.trim().replace(e,n))(b," ",".")}`,e=j(0,0,m.maxW,m.maxH),n=j(0,0,m.minW,m.minH),o={edges:{bottom:t,left:!1,right:t,top:!1},ignoreFrom:"a, button",restrictSize:{max:{height:e.height,width:e.width},min:{height:n.height,width:n.width}}};T.value.resizable(o),M.value||(M.value=!0,T.value.on("resizestart resizemove resizeend",W))}else T.value.resizable({enabled:!1})};return null==y||y.on("recalculate-styles",H),null==y||y.on("set-col-num",B),s((()=>{null==y||y.off("recalculate-styles",H),null==y||y.off("set-col-num",B),T.value&&T.value.unset(),m.observer&&m.observer.unobserve(v.value)})),a((()=>{m.lastBreakpoint&&(x.value=q(m.lastBreakpoint,m.breakpointCols)),X(),$(),H()})),(t,e)=>(l(),c("div",{ref_key:"item",ref:v,class:d(["vue-grid-item",p(A)]),style:f(k.props)},[u(t.$slots,"default"),p(R)?(l(),c("span",{key:0,class:d(b)})):h("",!0)],6))}});var Uo={exports:{}};(Uo.exports={}).forEach=function(t,e){for(var n=0;n<t.length;n++){var o=e(t[n]);if(o)return o}};var Ko={exports:{}},Jo=Ko.exports={};Jo.isIE=function(t){return(-1!==(e=navigator.userAgent.toLowerCase()).indexOf("msie")||-1!==e.indexOf("trident")||-1!==e.indexOf(" edge/"))&&(!t||t===function(){var t=3,e=document.createElement("div"),n=e.getElementsByTagName("i");do{e.innerHTML="\x3c!--[if gt IE "+ ++t+"]><i></i><![endif]--\x3e"}while(n[0]);return t>4?t:undefined}());var e},Jo.isLegacyOpera=function(){return!!window.opera};var Qo={exports:{}};(Qo.exports={}).getOption=function(t,e,n){var o=t[e];return null==o&&void 0!==n?n:o};var Zo=Qo.exports;function ti(){var t={},e=0,n=0,o=0;return{add:function(i,r){r||(r=i,i=0),i>n?n=i:i<o&&(o=i),t[i]||(t[i]=[]),t[i].push(r),e++},process:function(){for(var e=o;e<=n;e++)for(var i=t[e],r=0;r<i.length;r++){(0,i[r])()}},size:function(){return e}}}var ei="_erd";function ni(t){return t[ei]}var oi={initState:function(t){return t[ei]={},ni(t)},getState:ni,cleanState:function(t){delete t[ei]}},ii=Ko.exports,ri=Uo.exports.forEach,si=Uo.exports.forEach,ai=function(t){var e=t.stateHandler.getState;return{isDetectable:function(t){var n=e(t);return n&&!!n.isDetectable},markAsDetectable:function(t){e(t).isDetectable=!0},isBusy:function(t){return!!e(t).busy},markBusy:function(t,n){e(t).busy=!!n}}},li=function(t){var e={};function n(n){var o=t.get(n);return void 0===o?[]:e[o]||[]}return{get:n,add:function(n,o){var i=t.get(n);e[i]||(e[i]=[]),e[i].push(o)},removeListener:function(t,e){for(var o=n(t),i=0,r=o.length;i<r;++i)if(o[i]===e){o.splice(i,1);break}},removeAllListeners:function(t){var e=n(t);!e||(e.length=0)}}},ci=function(){var t=1;return{generate:function(){return t++}}},ui=function(t){var e=t.idGenerator,n=t.stateHandler.getState;return{get:function(t){var e=n(t);return e&&void 0!==e.id?e.id:null},set:function(t){var o=n(t);if(!o)throw new Error("setId required the element to have a resize detection state.");var i=e.generate();return o.id=i,i}}},pi=function(t){function e(){}var n={log:e,warn:e,error:e};if(!t&&window.console){var o=function(t,e){t[e]=function(){var t=console[e];if(t.apply)t.apply(console,arguments);else for(var n=0;n<arguments.length;n++)t(arguments[n])}};o(n,"log"),o(n,"warn"),o(n,"error")}return n},di=Ko.exports,hi=function(t){var e=(t=t||{}).reporter,n=Zo.getOption(t,"async",!0),o=Zo.getOption(t,"auto",!0);o&&!n&&(e&&e.warn("Invalid options combination. auto=true and async=false is invalid. Setting async=true."),n=!0);var i,r=ti(),s=!1;function a(){for(s=!0;r.size();){var t=r;r=ti(),t.process()}s=!1}function l(){var t;t=a,i=setTimeout(t,0)}return{add:function(t,e){!s&&o&&n&&0===r.size()&&l(),r.add(t,e)},force:function(t){s||(void 0===t&&(t=n),i&&(function(t){clearTimeout(t)}(i),i=null),t?l():a())}}},fi=oi,gi=function(t){var e=(t=t||{}).reporter,n=t.batchProcessor,o=t.stateHandler.getState;if(!e)throw new Error("Missing required dependency: reporter.");function i(t){return o(t).object}return{makeDetectable:function(i,r,s){s||(s=r,r=i,i=null),(i=i||{}).debug,ii.isIE(8)?s(r):function(r,s){var a=function(e){var n=t.important?" !important; ":"; ";return(e.join(n)+n).trim()}(["display: block","position: absolute","top: 0","left: 0","width: 100%","height: 100%","border: none","padding: 0","margin: 0","opacity: 0","z-index: -1000","pointer-events: none"]),l=!1,c=window.getComputedStyle(r),u=r.offsetWidth,p=r.offsetHeight;function d(){function t(){if("static"===c.position){r.style.setProperty("position","relative",i.important?"important":"");var t=function(t,e,n,o){var r=n[o];"auto"!==r&&"0"!==r.replace(/[^-\d\.]/g,"")&&(t.warn("An element that is positioned static has style."+o+"="+r+" which is ignored due to the static positioning. The element will need to be positioned relative, so the style."+o+" will be set to 0. Element: ",e),e.style.setProperty(o,"0",i.important?"important":""))};t(e,r,c,"top"),t(e,r,c,"right"),t(e,r,c,"bottom"),t(e,r,c,"left")}}""!==c.position&&(t(),l=!0);var n=document.createElement("object");n.style.cssText=a,n.tabIndex=-1,n.type="text/html",n.setAttribute("aria-hidden","true"),n.onload=function(){l||t(),function t(e,n){if(!e.contentDocument){var i=o(e);return i.checkForObjectDocumentTimeoutId&&window.clearTimeout(i.checkForObjectDocumentTimeoutId),void(i.checkForObjectDocumentTimeoutId=setTimeout((function(){i.checkForObjectDocumentTimeoutId=0,t(e,n)}),100))}n(e.contentDocument)}(this,(function(t){s(r)}))},ii.isIE()||(n.data="about:blank"),o(r)&&(r.appendChild(n),o(r).object=n,ii.isIE()&&(n.data="about:blank"))}o(r).startSize={width:u,height:p},n?n.add(d):d()}(r,s)},addListener:function(t,e){function n(){e(t)}if(ii.isIE(8))o(t).object={proxy:n},t.attachEvent("onresize",n);else{var r=i(t);if(!r)throw new Error("Element is not detectable by this strategy.");r.contentDocument.defaultView.addEventListener("resize",n)}},uninstall:function(t){if(o(t)){var e=i(t);!e||(ii.isIE(8)?t.detachEvent("onresize",e.proxy):t.removeChild(e),o(t).checkForObjectDocumentTimeoutId&&window.clearTimeout(o(t).checkForObjectDocumentTimeoutId),delete o(t).object)}}}},mi=function(t){var e=(t=t||{}).reporter,n=t.batchProcessor,o=t.stateHandler.getState;t.stateHandler.hasState;var i=t.idHandler;if(!n)throw new Error("Missing required dependency: batchProcessor");if(!e)throw new Error("Missing required dependency: reporter.");var r=function(){var t=500,e=500,n=document.createElement("div");n.style.cssText=l(["position: absolute","width: 1000px","height: 1000px","visibility: hidden","margin: 0","padding: 0"]);var o=document.createElement("div");o.style.cssText=l(["position: absolute","width: 500px","height: 500px","overflow: scroll","visibility: none","top: -1500px","left: -1500px","visibility: hidden","margin: 0","padding: 0"]),o.appendChild(n),document.body.insertBefore(o,document.body.firstChild);var i=t-o.clientWidth,r=e-o.clientHeight;return document.body.removeChild(o),{width:i,height:r}}(),s="erd_scroll_detection_container";function a(t){!function(t,e,n){function o(n,o){o=o||function(e){t.head.appendChild(e)};var i=t.createElement("style");return i.innerHTML=n,i.id=e,o(i),i}if(!t.getElementById(e)){var i=n+"_animation",r=n+"_animation_active",s="/* Created by the element-resize-detector library. */\n";s+="."+n+" > div::-webkit-scrollbar { "+l(["display: none"])+" }\n\n",s+="."+r+" { "+l(["-webkit-animation-duration: 0.1s","animation-duration: 0.1s","-webkit-animation-name: "+i,"animation-name: "+i])+" }\n",s+="@-webkit-keyframes "+i+" { 0% { opacity: 1; } 50% { opacity: 0; } 100% { opacity: 1; } }\n",o(s+="@keyframes "+i+" { 0% { opacity: 1; } 50% { opacity: 0; } 100% { opacity: 1; } }")}}(t,"erd_scroll_detection_scrollbar_style",s)}function l(e){var n=t.important?" !important; ":"; ";return(e.join(n)+n).trim()}function c(t,n,o){if(t.addEventListener)t.addEventListener(n,o);else{if(!t.attachEvent)return e.error("[scroll] Don't know how to add event listeners.");t.attachEvent("on"+n,o)}}function u(t,n,o){if(t.removeEventListener)t.removeEventListener(n,o);else{if(!t.detachEvent)return e.error("[scroll] Don't know how to remove event listeners.");t.detachEvent("on"+n,o)}}function p(t){return o(t).container.childNodes[0].childNodes[0].childNodes[0]}function d(t){return o(t).container.childNodes[0].childNodes[0].childNodes[1]}return a(window.document),{makeDetectable:function(t,a,u){function h(){if(t.debug){var n=Array.prototype.slice.call(arguments);if(n.unshift(i.get(a),"Scroll: "),e.log.apply)e.log.apply(null,n);else for(var o=0;o<n.length;o++)e.log(n[o])}}function f(t){var e=o(t).container.childNodes[0],n=window.getComputedStyle(e);return!n.width||-1===n.width.indexOf("px")}function g(){var t=window.getComputedStyle(a),e={};return e.position=t.position,e.width=a.offsetWidth,e.height=a.offsetHeight,e.top=t.top,e.right=t.right,e.bottom=t.bottom,e.left=t.left,e.widthCSS=t.width,e.heightCSS=t.height,e}function m(){if(h("storeStyle invoked."),o(a)){var t=g();o(a).style=t}else h("Aborting because element has been uninstalled")}function v(t,e,n){o(t).lastWidth=e,o(t).lastHeight=n}function y(){return 2*r.width+1}function b(){return 2*r.height+1}function x(t){return t+10+y()}function w(t){return t+10+b()}function E(t,e,n){var o=p(t),i=d(t),r=x(e),s=w(n),a=function(t){return 2*t+y()}(e),l=function(t){return 2*t+b()}(n);o.scrollLeft=r,o.scrollTop=s,i.scrollLeft=a,i.scrollTop=l}function S(){var t=o(a).container;if(!t){(t=document.createElement("div")).className=s,t.style.cssText=l(["visibility: hidden","display: inline","width: 0px","height: 0px","z-index: -1","overflow: hidden","margin: 0","padding: 0"]),o(a).container=t,function(t){t.className+=" "+s+"_animation_active"}(t),a.appendChild(t);var e=function(){o(a).onRendered&&o(a).onRendered()};c(t,"animationstart",e),o(a).onAnimationStart=e}return t}function T(){if(h("Injecting elements"),o(a)){!function(){var n=o(a).style;if("static"===n.position){a.style.setProperty("position","relative",t.important?"important":"");var i=function(t,e,n,o){var i=n[o];"auto"!==i&&"0"!==i.replace(/[^-\d\.]/g,"")&&(t.warn("An element that is positioned static has style."+o+"="+i+" which is ignored due to the static positioning. The element will need to be positioned relative, so the style."+o+" will be set to 0. Element: ",e),e.style[o]=0)};i(e,a,n,"top"),i(e,a,n,"right"),i(e,a,n,"bottom"),i(e,a,n,"left")}}();var n=o(a).container;n||(n=S());var i,u,p,d,f=r.width,g=r.height,m=l(["position: absolute","flex: none","overflow: hidden","z-index: -1","visibility: hidden","width: 100%","height: 100%","left: 0px","top: 0px"]),v=l(["position: absolute","flex: none","overflow: hidden","z-index: -1","visibility: hidden"].concat(["left: "+(i=(i=-(1+f))?i+"px":"0"),"top: "+(u=(u=-(1+g))?u+"px":"0"),"right: "+(d=(d=-f)?d+"px":"0"),"bottom: "+(p=(p=-g)?p+"px":"0")])),y=l(["position: absolute","flex: none","overflow: scroll","z-index: -1","visibility: hidden","width: 100%","height: 100%"]),b=l(["position: absolute","flex: none","overflow: scroll","z-index: -1","visibility: hidden","width: 100%","height: 100%"]),x=l(["position: absolute","left: 0","top: 0"]),w=l(["position: absolute","width: 200%","height: 200%"]),E=document.createElement("div"),T=document.createElement("div"),_=document.createElement("div"),z=document.createElement("div"),O=document.createElement("div"),I=document.createElement("div");E.dir="ltr",E.style.cssText=m,E.className=s,T.className=s,T.style.cssText=v,_.style.cssText=y,z.style.cssText=x,O.style.cssText=b,I.style.cssText=w,_.appendChild(z),O.appendChild(I),T.appendChild(_),T.appendChild(O),E.appendChild(T),n.appendChild(E),c(_,"scroll",P),c(O,"scroll",M),o(a).onExpandScroll=P,o(a).onShrinkScroll=M}else h("Aborting because element has been uninstalled");function P(){var t=o(a);t&&t.onExpand?t.onExpand():h("Aborting expand scroll handler: element has been uninstalled")}function M(){var t=o(a);t&&t.onShrink?t.onShrink():h("Aborting shrink scroll handler: element has been uninstalled")}}function _(){function r(e,n,o){var i=function(t){return p(t).childNodes[0]}(e),r=x(n),s=w(o);i.style.setProperty("width",r+"px",t.important?"important":""),i.style.setProperty("height",s+"px",t.important?"important":"")}function s(s){var c=a.offsetWidth,u=a.offsetHeight,p=c!==o(a).lastWidth||u!==o(a).lastHeight;h("Storing current size",c,u),v(a,c,u),n.add(0,(function(){if(p){if(!o(a))return void h("Aborting because element has been uninstalled");if(!l())return void h("Aborting because element container has not been initialized");if(t.debug){var n=a.offsetWidth,s=a.offsetHeight;(n!==c||s!==u)&&e.warn(i.get(a),"Scroll: Size changed before updating detector elements.")}r(a,c,u)}})),n.add(1,(function(){o(a)?l()?E(a,c,u):h("Aborting because element container has not been initialized"):h("Aborting because element has been uninstalled")})),p&&s&&n.add(2,(function(){o(a)?l()?s():h("Aborting because element container has not been initialized"):h("Aborting because element has been uninstalled")}))}function l(){return!!o(a).container}function c(){h("notifyListenersIfNeeded invoked");var t=o(a);return void 0===o(a).lastNotifiedWidth&&t.lastWidth===t.startSize.width&&t.lastHeight===t.startSize.height?h("Not notifying: Size is the same as the start size, and there has been no notification yet."):t.lastWidth===t.lastNotifiedWidth&&t.lastHeight===t.lastNotifiedHeight?h("Not notifying: Size already notified"):(h("Current size not notified, notifying..."),t.lastNotifiedWidth=t.lastWidth,t.lastNotifiedHeight=t.lastHeight,void ri(o(a).listeners,(function(t){t(a)})))}function u(){h("Scroll detected."),f(a)?h("Scroll event fired while unrendered. Ignoring..."):s(c)}if(h("registerListenersAndPositionElements invoked."),o(a)){o(a).onRendered=function(){if(h("startanimation triggered."),f(a))h("Ignoring since element is still unrendered...");else{h("Element rendered.");var t=p(a),e=d(a);(0===t.scrollLeft||0===t.scrollTop||0===e.scrollLeft||0===e.scrollTop)&&(h("Scrollbars out of sync. Updating detector elements..."),s(c))}},o(a).onExpand=u,o(a).onShrink=u;var g=o(a).style;r(a,g.width,g.height)}else h("Aborting because element has been uninstalled")}function z(){if(h("finalizeDomMutation invoked."),o(a)){var t=o(a).style;v(a,t.width,t.height),E(a,t.width,t.height)}else h("Aborting because element has been uninstalled")}function O(){u(a)}function I(){var t;h("Installing..."),o(a).listeners=[],t=g(),o(a).startSize={width:t.width,height:t.height},h("Element start size",o(a).startSize),n.add(0,m),n.add(1,T),n.add(2,_),n.add(3,z),n.add(4,O)}var P,M,D;u||(u=a,a=t,t=null),t=t||{},h("Making detectable..."),D=(M=P=a).getRootNode&&M.getRootNode().contains(M),M!==M.ownerDocument.body&&!M.ownerDocument.body.contains(M)&&!D||null===window.getComputedStyle(P)?(h("Element is detached"),S(),h("Waiting until element is attached..."),o(a).onRendered=function(){h("Element is now attached"),I()}):I()},addListener:function(t,e){if(!o(t).listeners.push)throw new Error("Cannot add listener to an element that is not detectable.");o(t).listeners.push(e)},uninstall:function(t){var e=o(t);!e||(e.onExpandScroll&&u(p(t),"scroll",e.onExpandScroll),e.onShrinkScroll&&u(d(t),"scroll",e.onShrinkScroll),e.onAnimationStart&&u(e.container,"animationstart",e.onAnimationStart),e.container&&t.removeChild(e.container))},initDocument:a}};function vi(t){return Array.isArray(t)||void 0!==t.length}function yi(t){if(Array.isArray(t))return t;var e=[];return si(t,(function(t){e.push(t)})),e}function bi(t){return t&&1===t.nodeType}function xi(t,e,n){var o=t[e];return null==o&&void 0!==n?n:o}const wi=()=>typeof window<"u",Ei={breakpointsValidatorPayload:{invalidBreakpointsKeys1:{lg:0,md:0,sm:0,xs:0,xx:0},invalidBreakpointsKeys2:{lg:0,md:0,sm:0,xs:0},invalidBreakpointsTypes:{lg:"0",md:0,sm:0,xs:0,xx:0},validBreakpoints:{lg:0,md:0,sm:0,xs:0,xxs:0}},intersectionObserverConfig:{root:null,rootMargin:"8px",threshold:.4},keysValidatorPayload:{invalidKeys1:["lg","md","sm","xs","xxw"],invalidKeys2:["1","2","3","4","5"],validKeys:["lg","md","sm","xs","xxs"]},layoutValidatorPayload:{invalidOptionalLayout:{isDraggable:!0,isResizable:!1,maxH:0,maxW:"0",minH:0,minW:0,moved:!0,static:!1},invalidRequiredLayout:{h:0,i:"string",w:0,x:0,y:0},validOptionalLayout:{isDraggable:!0,isResizable:!1,maxH:0,maxW:0,minH:0,minW:0,moved:!0,static:!1},validRequiredLayout:{h:0,i:-1,w:0,x:0,y:0}},marginValidatorPayload:{invalidMargin1:[0,0,0],invalidMargin2:["0",0],validMargin:[0,0]}},{keysValidatorPayload:Si,layoutValidatorPayload:Ti}=Ei,_i=t=>{const e=Object.keys(t),n=e.map((e=>"number"==typeof t[e]));return zi(Si.validKeys,e)&&-1===n.indexOf(!1)},zi=(t,e)=>{const n=e.filter((e=>t.indexOf(e)>=0));return e.length>=t.length&&n.length===t.length},Oi=t=>{const{validOptionalLayout:e,validRequiredLayout:n}=Ti,o={...n,...e},i=Object.keys(n);return!t.map((t=>zi(i,Object.keys(t)))).includes(!1)&&!t.map((t=>Object.keys(t).map((e=>!o[e]||typeof t[e]==typeof o[e])).includes(!1))).includes(!0)},Ii=t({__name:"GridLayout",props:{autoSize:{default:!0,type:Boolean},breakpoints:{default:()=>({lg:1200,md:996,sm:768,xs:480,xxs:0}),type:Object,validator:_i},colNum:{required:!0,type:Number},cols:{default:()=>({lg:12,md:10,sm:6,xs:4,xxs:2}),type:Object,validator:_i},horizontalShift:{default:!1,type:Boolean},intersectionObserverConfig:{default:()=>({root:null,rootMargin:"8px",threshold:.4}),type:Object,validator:t=>{t={...Ei.intersectionObserverConfig,...t};const e=["root","rootMargin","threshold"];return!Object.keys(t).map((t=>e.includes(t))).includes(!1)}},isDraggable:{default:!0,type:Boolean},isResizable:{default:!0,type:Boolean},layout:{required:!0,type:Array,validator:Oi},margin:{default:()=>[10,10],type:Array,validator:t=>{const e=t.map((t=>"number"==typeof t)),n=2===t.length;return-1===e.indexOf(!1)&&n}},maxRows:{default:1/0,type:Number},preventCollision:{default:!1,type:Boolean},responsive:{default:!1,type:Boolean},responsiveLayouts:{default:()=>({}),type:Object,validator:t=>{const e=Object.keys(t);return!e.length||!e.map((e=>Oi(t[e]))).includes(!1)}},rowHeight:{default:150,type:Number},useCssTransforms:{default:!0,type:Boolean},useObserver:{default:!1,type:Boolean},verticalCompact:{default:!0,type:Boolean}},emits:["update:layout","layout-ready","update:breakpoint","layout-created","layout-before-mount","layout-mounted","container-resized","item-resize","item-resized","item-move","item-moved","intersection-observe","intersection-unobserve"],setup(t,{emit:n}){const o=t,d=function(t){return{all:t=t||new Map,on:function(e,n){var o=t.get(e);o?o.push(n):t.set(e,[n])},off:function(e,n){var o=t.get(e);o&&(n?o.splice(o.indexOf(n)>>>0,1):t.set(e,[]))},emit:function(e,n){var o=t.get(e);o&&o.slice().map((function(t){t(n)})),(o=t.get("*"))&&o.slice().map((function(t){t(e,n)}))}}}();g(P,d);const h={h:0,i:-1,w:0,x:0,y:0},O=["minW","minH","maxW","maxH","moved","static","isDraggable","isResizable"],I=e(function(t){var e;if((t=t||{}).idHandler)e={get:function(e){return t.idHandler.get(e,!0)},set:t.idHandler.set};else{var n=ci(),o=ui({idGenerator:n,stateHandler:fi});e=o}var i=t.reporter;i||(i=pi(!1===i));var r=xi(t,"batchProcessor",hi({reporter:i})),s={};s.callOnAdd=!!xi(t,"callOnAdd",!0),s.debug=!!xi(t,"debug",!1);var a,l=li(e),c=ai({stateHandler:fi}),u=xi(t,"strategy","object"),p=xi(t,"important",!1),d={reporter:i,batchProcessor:r,stateHandler:fi,idHandler:e,important:p};if("scroll"===u&&(di.isLegacyOpera()?(i.warn("Scroll strategy is not supported on legacy Opera. Changing to object strategy."),u="object"):di.isIE(9)&&(i.warn("Scroll strategy is not supported on IE9. Changing to object strategy."),u="object")),"scroll"===u)a=mi(d);else{if("object"!==u)throw new Error("Invalid strategy name: "+u);a=gi(d)}var h={};return{listenTo:function(t,n,o){function r(t){var e=l.get(t);si(e,(function(e){e(t)}))}function u(t,e,n){l.add(e,n),t&&n(e)}if(o||(o=n,n=t,t={}),!n)throw new Error("At least one element required.");if(!o)throw new Error("Listener required.");if(bi(n))n=[n];else{if(!vi(n))return i.error("Invalid arguments. Must be a DOM element or a collection of DOM elements.");n=yi(n)}var d=0,f=xi(t,"callOnAdd",s.callOnAdd),g=xi(t,"onReady",(function(){})),m=xi(t,"debug",s.debug);si(n,(function(t){fi.getState(t)||(fi.initState(t),e.set(t));var s=e.get(t);if(m&&i.log("Attaching listener to element",s,t),!c.isDetectable(t))return m&&i.log(s,"Not detectable."),c.isBusy(t)?(m&&i.log(s,"System busy making it detectable"),u(f,t,o),h[s]=h[s]||[],void h[s].push((function(){++d===n.length&&g()}))):(m&&i.log(s,"Making detectable..."),c.markBusy(t,!0),a.makeDetectable({debug:m,important:p},t,(function(t){if(m&&i.log(s,"onElementDetectable"),fi.getState(t)){c.markAsDetectable(t),c.markBusy(t,!1),a.addListener(t,r),u(f,t,o);var e=fi.getState(t);if(e&&e.startSize){var l=t.offsetWidth,p=t.offsetHeight;(e.startSize.width!==l||e.startSize.height!==p)&&r(t)}h[s]&&si(h[s],(function(t){t()}))}else m&&i.log(s,"Element uninstalled before being detectable.");delete h[s],++d===n.length&&g()})));m&&i.log(s,"Already detecable, adding listener."),u(f,t,o),d++})),d===n.length&&g()},removeListener:l.removeListener,removeAllListeners:l.removeAllListeners,uninstall:function(t){if(!t)return i.error("At least one element is required.");if(bi(t))t=[t];else{if(!vi(t))return i.error("Invalid arguments. Must be a DOM element or a collection of DOM elements.");t=yi(t)}si(t,(function(t){l.removeAllListeners(t),a.uninstall(t),fi.cleanState(t)}))},initDocument:function(t){a.initDocument&&a.initDocument(t)}}}({callOnAdd:!1,strategy:"scroll"})),M=e(!1),k=e(""),A=e(0),R=e({}),j=e({}),L=e(o.layout),W=e({h:0,i:-1,w:0,x:0,y:0}),B=e(0);let Y;const V=e(null),G=i((()=>({breakpointCols:o.cols,colNum:o.colNum,containerWidth:B.value,isDraggable:o.isDraggable,isResizable:o.isResizable,lastBreakpoint:k.value,margin:o.margin,maxRows:o.maxRows,responsive:o.responsive,rowHeight:o.rowHeight,useCssTransforms:o.useCssTransforms,width:B.value})));r((()=>o.colNum),(t=>{d.emit("set-col-num",t)})),r((()=>o.layout.length),(()=>{J(),C(o.layout,o.verticalCompact)})),r((()=>o.margin),(()=>{tt()})),r((()=>o.responsive),(t=>{t||(n("update:layout",L.value),d.emit("set-col-num",o.colNum)),nt()})),r((()=>B.value),((t,e)=>{m((()=>{0===e&&m((()=>{n("layout-ready",o.layout)})),o.responsive&&ot(),tt()}))})),r((()=>o.useObserver),(t=>{t?st():Y.disconnect()}));const U=t=>{const e={observe:[],unobserve:[]};t.forEach((({target:t,isIntersecting:n})=>{n?e.observe.push(t.__INTERSECTION_OBSERVER_INDEX__):e.unobserve.push(t.__INTERSECTION_OBSERVER_INDEX__)})),n("intersection-observe",e.observe),n("intersection-unobserve",e.unobserve)},K=t=>{const e=Object.keys(h);return Object.keys(t).reduce(((n,o)=>((O.includes(o)||e.includes(o))&&(n[o]=t[o]),n)),{})},J=()=>{if(o.layout&&L.value){if(o.layout.length!==L.value.length){const t=Q(o.layout,L.value);t.length>0&&(o.layout.length>L.value.length?L.value=L.value.concat(t):L.value=L.value.filter((e=>!t.some((t=>e.i===t.i))))),A.value=o.layout.length,Z()}C(o.layout,o.verticalCompact),tt(),n("update:layout",o.layout),d.emit("recalculate-styles")}},Q=(t,e)=>{const n=t.filter((t=>!e.some((e=>t.i===e.i)))),o=e.filter((e=>!t.some((t=>e.i===t.i))));return n.concat(o)},Z=()=>{R.value=Object.assign({},o.responsiveLayouts)},tt=()=>{const t=et();j.value={height:t}},et=()=>{if(!o.autoSize||!o.layout)return;const[,t]=o.margin;return`${(t=>{let e,n=0;for(let o=0;o<t.length;o++)e=t[o].y+t[o].h,e>n&&(n=e);return n})(o.layout)*(o.rowHeight+t)+t}px`},nt=()=>{V.value&&(B.value=V.value.offsetWidth)},ot=()=>{const t=((t,e)=>{var n;const o=X(t);let[i]=o;for(let r=1;r<o.length;r++){const s=o[r];e>(null!=(n=t[s])?n:1)&&(i=s)}return i})(o.breakpoints,B.value),e=q(t,o.cols);k.value&&!R.value[k.value]&&(R.value[k.value]=D(o.layout));const i=$(L.value,R.value,o.breakpoints,t,k.value,e,o.verticalCompact);R.value[t]=i,k.value!==t&&n("update:breakpoint",t,i),k.value=t,n("update:layout",i),d.emit("set-col-num",q(t,o.cols))},it=([t,e,i,r,s,a])=>{const l=H(o.layout,e),c=null!=l?l:{...h};let u;if(o.preventCollision){const t=N(o.layout,{...c,h:s,w:a}).filter((t=>t.i!==c.i));if(u=t.length>0,u){let e=1/0,n=1/0;t.forEach((t=>{t.x>c.x&&(e=Math.min(e,t.x)),t.y>c.y&&(n=Math.min(n,t.y))})),Number.isFinite(e)&&(c.w=e-c.x),Number.isFinite(n)&&(c.h=n-c.y)}}u||(c.w=a,c.h=s),"resizestart"===t||"resizemove"===t?(W.value.i=+e,W.value.x=i,W.value.y=r,W.value.w=c.w,W.value.h=c.h,m((()=>{M.value=!0}))):m((()=>{M.value=!1})),o.responsive&&ot(),C(o.layout,o.verticalCompact),d.emit("recalculate-styles"),tt(),"resizeend"===t&&n("update:layout",o.layout)},rt=([t,e,i,r,s,a])=>{const l=H(o.layout,e),c=null!=l?l:{...h};"dragmove"===t||"dragstart"===t?(W.value.i=+e,W.value.x=c.x,W.value.y=c.y,W.value.w=a,W.value.h=s,m((()=>{M.value=!0}))):m((()=>{M.value=!1})),n("update:layout",F(o.layout,c,i,r,!0,o.horizontalShift,o.preventCollision)),C(o.layout,o.verticalCompact),d.emit("recalculate-styles"),tt(),"dragend"===t&&(C(o.layout,o.verticalCompact),n("update:layout",o.layout))},st=()=>{Y=new IntersectionObserver(U,{root:null,rootMargin:"8px",threshold:.4,...o.intersectionObserverConfig})};return n("layout-created",o.layout),d.on("resize-event",it),d.on("drag-event",rt),s((()=>{((t,e)=>{!wi||window.removeEventListener(t,e)})("resize",nt),I.value&&V.value&&I.value.uninstall(V.value),d.off("resize-event",it),d.off("drag-event",rt)})),v((()=>{n("layout-before-mount",o.layout)})),a((()=>{n("layout-mounted",o.layout),m((()=>{L.value=o.layout,m((()=>{nt(),Z(),((t,e)=>{if(!wi)return e();window.addEventListener(t,e)})("resize",nt.bind(this)),C(o.layout,o.verticalCompact),n("update:layout",o.layout),tt(),V.value&&I.value.listenTo(V.value,nt),o.useObserver&&st()}))}))})),(e,o)=>(l(),c("div",null,[y("div",{ref_key:"wrapper",ref:V,class:"vue-grid-layout",style:f(j.value)},[b(w(Go,E({class:"vue-grid-placeholder"},{...p(G),...W.value}),null,16),[[x,M.value]]),u(e.$slots,"default",{gridItemProps:{...p(G),observer:p(Y)}},(()=>[(l(!0),c(S,null,T(t.layout,(t=>(l(),_(Go,E({key:t.i},{...p(G),...K(t)},{observer:p(Y),onContainerResized:o[0]||(o[0]=t=>n("container-resized",t)),onResize:o[1]||(o[1]=t=>n("item-resize",t)),onMove:o[2]||(o[2]=t=>n("item-move",t)),onMoved:o[3]||(o[3]=t=>n("item-moved",t))}),{default:z((()=>[u(e.$slots,"gridItemContent",{item:t})])),_:2},1040,["observer"])))),128))]))],4)]))}});export{Go as J,Ii as m};
