import{a as e}from"./api-360ec627.js";const r={userName:[{required:!0,message:"该输入框不能为空",trigger:"blur"}],userType:[{required:!0,message:"该选择器不能为空",trigger:"blur"}],userStatus:[{required:!0,message:"该选择不能为空",trigger:"blur"}],projectID:[{required:!0,message:"该选择不能为空",trigger:"blur"}],roleID:[{required:!0,message:"该选择不能为空",trigger:"blur"}],userPhone:[{required:!0,message:"该输入框不能为空",trigger:"blur"}],roleName:[{required:!0,message:"该输入框不能为空",trigger:"blur"}],label:[{required:!0,message:"该输入框不能为空",trigger:"blur"}],type:[{required:!0,message:"该输入框不能为空",trigger:"blur"}],path:[{required:!0,message:"该输入框不能为空",trigger:"blur"}],projectName:[{required:!0,message:"该输入框不能为空",trigger:"blur"}]},s=(r=1,s=10,t=[],a="",u="",o="",m="")=>e("/system/user/getUserList",{},{currentPage:r,pageSize:s,projectSpecial:t,userName:a,userPhone:u,userStatus:o,userType:m},"post"),t=(r="",s="",t="",a="",u="",o="",m="")=>e("/system/user/addUser",{},{projectID:r,roleID:s,userEmail:t,userName:a,userPhone:u,userStatus:o,userType:m},"post"),a=(r="",s="",t="",a="",u="",o="",m)=>e("/system/user/editUser",{},{projectID:r,roleID:s,userEmail:t,userName:a,userPhone:u,userType:o,userUID:m},"put"),u=(r="",s)=>e("/system/user/editUser",{},{userStatus:r,userUID:s},"put"),o=r=>e("/system/user/deleteUser/"+r,{},{},"post"),m=()=>e("/system/project/getProjectSpecialInfo",{},{},"get"),i=(r,s)=>e("/system/project/addProject",{},{pid:r,projectName:s},"post"),l=(r,s,t)=>e("/system/project/editProject",{},{id:r,pid:s,projectName:t},"put"),p=(r=1,s=10,t="")=>e("/system/role/getRoleList",{},{currentPage:r,pageSize:s,roleName:t},"post"),g=(r=[],s,t)=>e("/system/role/addRole",{},{menuList:r,roleName:s,roleRemark:t},"post"),d=(r,s,t,a)=>e("/system/role/editRole",{},{roleID:r,roleName:s,roleRemark:t,menuList:a},"put"),y=r=>e("/system/role/deleteRole/"+r,{},{},"delete"),n=()=>e("/system/menu/getMenuList",{},{},"get"),c=(r,s,t,a,u,o,m,i)=>e("/system/menu/addMenu",{},{pid:r,label:s,icon:t,path:a,openStyle:u,auth:o,sort:m,type:i},"post"),b=(r,s,t,a,u,o)=>e("/system/menu/editMenu",{},{id:r,pid:s,label:t,icon:a,path:u,type:o},"post"),j=r=>e("/system/menu/deleteMenu/"+r,{},{},"delete");export{j as X,r as a,n as b,c,b as d,m as e,i as f,l as g,y as h,p as i,g as j,d as k,u as l,o as m,s as n,t as o,a as p};
