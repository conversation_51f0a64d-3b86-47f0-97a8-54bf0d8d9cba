import{K as e}from"./quasar-df1bac18.js";import"./vue-5bfa3a54.js";import{m as a,w as t}from"./@vueuse-5227c686.js";import{f as s}from"./formUtil-7f692cbf.js";import{l}from"./lodash-6d99edc3.js";import{_ as o}from"./index-a5df0f75.js";import{b as i,d as p,e as r,f as m,g as n,h as u}from"./naive-ui-0ee0b8c3.js";import{e as f,w as c,m as d,v,o as w,f as y,a8 as x,a as g,t as k,c as b,F as _,k as j,ak as h,y as L,aa as T,l as U,x as F,g as V}from"./@vue-5e5cdef9.js";const q={class:"body-text tw-mr-2"},C=o({__name:"MyForm",props:["title","formList","page"],emits:["update:modelValue"],setup(o,{emit:C}){const K=o,z=a(K,"page",C),D=f((()=>s.getValue(K.formList))),E=l._.find(K.formList,{prop:"check"}),I=t(D),M=f((()=>I.history.value.at(0).snapshot));c([()=>M.value,()=>D.value],((e,a)=>{const t=e.map((e=>l._.omit(e,["check","export","reset"]))),o=s.isObjEqual(...t);E.type=o?"info":"warning"})),c([()=>K.page.page,()=>K.page.pageSize],(e=>{I.commit()}));let N=l._.cloneDeep(s.getValue(K.formList));async function O(e){"warning"==E.type&&E.invoke(D,E,I),"reset"==e.prop&&(e.value=!0,await s.reset(K.formList,N,e,z)),"export"==e.prop&&(e.value=!0,await e.invoke())}return d((()=>{I.commit()})),v((()=>{I.clear()})),(a,t)=>{const s=i,l=p,o=r,f=m,c=e,d=n,v=u;return w(),y(v,{class:"tw-inline-flex tw-w-full tw-text-xl tw-items-center n-form-box"},{default:x((()=>[g("span",q,k(K.title),1),(w(!0),b(_,null,j(K.formList,(e=>(w(),b(_,null,["input"==e.formType?(w(),y(s,{key:0,value:e.value,"onUpdate:value":a=>e.value=a,onKeydown:h(O,["enter"]),class:L(["tw-mr-2",e.class??"tw-w-[300px]"])},{prefix:x((()=>[T(k(e.label),1)])),_:2},1032,["value","onUpdate:value","class"])):U("",!0),"select"==e.formType?(w(),y(f,{key:1,class:L(["tw-mr-2",e.class??"tw-w-[300px]"])},{default:x((()=>[F(l,null,{default:x((()=>[T(k(e.label),1)])),_:2},1024),F(o,{class:L(e.class??"tw-w-[300px]"),multiple:e.multiple,clearable:"","max-tag-count":1,options:e.options,value:e.value,"onUpdate:value":a=>e.value=a,"key-field":e.keyField??"value","label-field":e.labelField??"label"},null,8,["class","multiple","options","value","onUpdate:value","key-field","label-field"])])),_:2},1032,["class"])):U("",!0),"slot"==e.formType?V(a.$slots,e.prop,{key:2,scope:e},void 0,!0):U("",!0),"space"==e.formType?(w(),y(c,{key:3})):U("",!0),"button"==e.formType?(w(),y(d,{key:4,onClick:a=>O(e),type:e.type??"info",loading:e.value,class:"tw-text-right tw-mr-2 tw-text-white"},{default:x((()=>[T(k(e.label),1)])),_:2},1032,["onClick","type","loading"])):U("",!0)],64)))),256))])),_:3})}}},[["__scopeId","data-v-36bfa8ed"]]);export{C as _};
