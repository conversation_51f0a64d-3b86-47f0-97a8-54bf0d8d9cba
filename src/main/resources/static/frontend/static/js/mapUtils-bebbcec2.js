import{O as e,j as t,T as a,k as o,G as s,W as i,P as r,c as l,A as n,l as h,V as c,m,n as p,C as d,f,o as u,N as w}from"./three-59a86278.js";import"./vue-5bfa3a54.js";import{_ as g}from"./index-a5df0f75.js";import{o as x,c as y,h as b,j as v,m as A,v as S,x as L,a8 as D,b as z,a as C}from"./@vue-5e5cdef9.js";import{x as I,s as B}from"./homeStore-7900023d.js";import{r as j,i as R}from"./echarts-f30da64f.js";import{V as E}from"./zrender-c058db04.js";import{S as O,A as T,a as W}from"./swiper-7f939876.js";import{c as N}from"./chartResize-3e3d11d7.js";import{F as H}from"./@vueuse-5227c686.js";import{l as k}from"./lodash-6d99edc3.js";import{a as M}from"./echartsInit-2e16a3ff.js";import{s as _}from"./chartXY-a0399c4a.js";import{m as U}from"./api-360ec627.js";import{i as P,j as Y,k as q}from"./homeApi-030fb9ef.js";import{c as Q}from"./countUtil-d7099b62.js";import{d as F}from"./dayjs-67f8ddef.js";import{X as G}from"./alarmAnalysisApi-0364d01e.js";const J={r:80,earthBg:new URL(""+new URL("../jpg/earth-a63875c3.jpg",import.meta.url).href,self.location).href,bump:new URL(""+new URL("../jpg/earthBump-88e1f1b5.jpg",import.meta.url).href,self.location).href,cloud:new URL(""+new URL("../png/clouds-013c98f6.png",import.meta.url).href,self.location).href,spec:new URL(""+new URL("../jpg/spec-ed14ff23.jpg",import.meta.url).href,self.location).href};let V={scene:null,camera:null,mapDom:null,mesh:null,renderer:null,orbitControls:null,object:new e,axisHelper:new t(50),textureLoader:new a,clock:new o,group:new s};const X={class:"map",ref:"map"};const Z=g({name:"ccMap",data:()=>({}),methods:{initTHREE(){V.renderer=new i({antialias:!0,alpha:!0}),V.mapDom=this.$refs.map,V.renderer.setSize(V.mapDom.clientWidth,V.mapDom.clientHeight),V.renderer.setClearColor(0,0),V.mapDom.appendChild(V.renderer.domElement)},initCamera(){V.camera=new r(45,V.mapDom.clientWidth/V.mapDom.clientHeight,1,2e3),V.camera.position.z=300,V.camera.up.set(0,1,0),V.camera.lookAt(0,0,0)},initScene(){V.scene=new l;const e=new n(4211780);V.scene.add(e)},initAxisHelper(){V.scene.add(V.axisHelper)},initLight(){const e=new n(16777215);V.scene.add(e)},initOrbitControls(){const e=new h(V.camera,V.renderer.domElement);e.target=new c(0,0,0),e.autoRotate=!0,e.enablePan=!1,e.maxDistance=1e3,e.minDistance=100,V.orbitControls=e},initBg(){const e=V.textureLoader.load(J.earthBg),t=V.textureLoader.load(J.bump),a=V.textureLoader.load(J.spec),o=new m(J.r,50,50);let s=new p;s.map=e,s.bumpMap=t,s.bumpScale=1,s.specularMap=a,s.specular=new d("#1a2948"),s.shininess=2,V.mesh=new f(o,s),V.group.name="earth",V.group.add(V.mesh),V.scene.add(V.group);var i=new u({color:16777215,blending:w,transparent:!0,depthTest:!1});let r=new m(J.r+2,50,50),l=V.textureLoader.load(J.cloud);i.map=l,i.needsUpdate=!0;var n=new f(r,i);n.name="cloud",V.group.add(n)},glRender(){let e=V.clock.getElapsedTime();if(V.group){V.group.position.set(4*Math.sin(e),0,4*Math.cos(e));var t=new c(0,1,0);V.group.rotateOnAxis(t,0),V.group.rotateOnAxis(t,.005)}requestAnimationFrame(this.glRender),V.renderer.render(V.scene,V.camera),this.resize()},resize(){V.camera.aspect=V.mapDom.clientWidth/V.mapDom.clientHeight,V.camera.updateProjectionMatrix(),V.renderer.setSize(V.mapDom.clientWidth,V.mapDom.clientHeight)}},mounted(){this.initTHREE(),this.initCamera(),this.initScene(),this.initAxisHelper(),this.initLight(),this.initOrbitControls(),this.initBg(),this.glRender()}},[["render",function(e,t,a,o,s,i){return x(),y("div",X,null,512)}],["__scopeId","data-v-3826a70f"]]);let K,$;K={color:["#f6c55e","#91cc75","#fac858","#ee6666","#73c0de","#3ba272","#fc8452"],backgroundColor:"transparent",title:{text:"日发电量 / KWh",textStyle:{fontSize:16,fontWeight:500,color:"#fff"},left:"center",bottom:"0"},tooltip:{trigger:"axis"},grid:{top:"10px",bottom:"25px",left:"10px",right:"10px",containLabel:!0},xAxis:[{type:"category",boundaryGap:!1,axisLabel:{formatter:"{value}",color:"#fff"},axisLine:{lineStyle:{color:"#D9D9D9"}},data:[]}],yAxis:[{type:"value",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{show:!1},splitLine:{show:!1}}],series:[{name:"日发电量",type:"line",smooth:!0,symbol:"circle",symbolSize:3,zlevel:3,data:[]}]};{let e=["#f6c55e","#ea5514","#91cc75","#fac858","#ee6666","#73c0de","#3ba272","#fc8452"];$={color:e,backgroundColor:"transparent",grid:{top:"10px",bottom:"25px",left:"5px",right:"15px",containLabel:!0},title:{text:"月发电量 / MWh",textStyle:{fontSize:16,fontWeight:500,color:"white"},left:"center",bottom:"0"},tooltip:{trigger:"axis"},xAxis:[{type:"category",boundaryGap:!0,axisLabel:{formatter:"{value}",color:"#fff"},axisLine:{lineStyle:{color:"#D9D9D9"}},data:[]}],yAxis:[{type:"value",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{show:!1},splitLine:{show:!1}}],series:[{name:"发电量",type:"bar",barMixWidth:10,barMaxWidth:20,itemStyle:{normal:{color:new E(0,0,0,1,[{offset:0,color:e[0]},{offset:1,color:e[1]}])}},data:[]}]}}new E(0,1,0,0,[{offset:0,color:"#48b153"},{offset:1,color:"#3fad50"}]),new E(0,1,0,0,[{offset:0,color:"#48b153"},{offset:1,color:"#3fad50"}]);const ee={ref:"echartDom1",class:"tw-m-0 tw-h-full tw-w-[80%] tw-mx-auto"},te={ref:"echartDom2",class:"tw-m-0 tw-h-full tw-w-[80%] tw-mx-auto"},ae={ref:"echartDom3",class:"tw-m-0 tw-h-full tw-w-[80%] tw-mx-auto"},oe={__name:"carouselEchart",setup(e){const t=b(),a=v({echartDom1:H("echartDom1"),echartDom2:H("echartDom2"),echartDom3:H("echartDom3"),swiper:H("swiper")}),o={echartDom1:null,echartDom2:null,echartDom3:null},s=I(),i=v({scheduleRequest:null});return A((async()=>{const e=k._.cloneDeep($);e.title.text="近七日发电量",k._.values(o).every((e=>null==e))&&(o.echartDom1=await M(a.echartDom1,K),o.echartDom2=await M(a.echartDom2,e),o.echartDom3=await M(a.echartDom3,$),N(t.value,o)),i.scheduleRequest=B((()=>{o.echartDom3.setOption(_(o.echartDom3.getOption(),s.month.map((e=>e.collectDate)),s.month.map((e=>e.electricity))),!0);const e=s.day.slice(-7);o.echartDom2.setOption(_(o.echartDom2.getOption(),e.map((e=>e.collectDate.slice(8,10))),e.map((e=>e.electricity))),!0),o.echartDom1.setOption(_(o.echartDom1.getOption(),s.hour.map((e=>e.collectDate.slice(11,16))),s.hour.map((e=>e.electricity))),!0)}),6e4,!0)})),S((()=>{clearTimeout(i.scheduleRequest),i.scheduleRequest=null})),(e,a)=>(x(),y("div",{class:"tw-h-full tw-w-full",ref_key:"xwChartRef",ref:t},[L(z(W),{ref:"swiper","slides-per-view":1,autoplay:{delay:5e3,disableOnInteraction:!1},loop:"",speed:1e3,pagination:{clickable:!0},modules:[z(T)],class:"tw-h-full tw-w-full"},{default:D((()=>[L(z(O),null,{default:D((()=>[C("figure",ee,null,512)])),_:1}),L(z(O),null,{default:D((()=>[C("figure",te,null,512)])),_:1}),L(z(O),null,{default:D((()=>[C("figure",ae,null,512)])),_:1})])),_:1},8,["modules"])],512))}};class se{constructor(e){this.chart=null,this.parentIdList=[],this.options=null,this.tasks=[["mousemove",this.mousemoveHandle],["click",this.enterHandle]],this.move=!0,this.mapJson=null,this.el=e,this.timer=null,this.plantName="",this.weatherData=null,this.area=null,this.nodeStatus=null,this.alarmList=[],this.isFirstLoad=!0,this.statusDic={"正常":0,"告警":1,"光精灵离线":2,"自检提示":3,"逆变器夜间离线":4}}async getMapJson(e,t,a){let o;return"龙川县"==e?(o=await U.get("/lcx.json"),o.data):"始兴县"==e?(o=await U.get("/sxx.json"),o.data):"廉江市"==e?(o=await U.get("/ljs.json"),o.data):(o="china"==t?await U.get("/china.json"):await U.get(`/${t}/${a}.json`),o.data)}pushTitle(e,t,a){var o;if(this.alarmList=[],this.plantName="",this.nodeStatus="",clearInterval(this.timer),this.timer=null,1==this.parentIdList.length&&(this.isFirstLoad=!1),e==(null==(o=this.parentIdList.at(-1))?void 0:o.name))return!1;const s=this.parentIdList.findIndex((t=>t.name==e));return-1!=s?(this.parentIdList=this.parentIdList.slice(0,s+1),!0):("龙川县"==e&&this.parentIdList.length<3||"始兴县"==e&&this.parentIdList.length<3||"廉江市"==e&&this.parentIdList.length<3||this.parentIdList.length<2)&&(this.parentIdList.push({level:t,adcode:a,name:e}),!0)}findCoor(e){const{level:t,adcode:a}=this.mapJson.features.find((t=>t.properties.name==e)).properties;return{level:t,adcode:a,name:e}}async setChart(e="china",t="china",a=0){this.mapJson=await this.getMapJson(e,t,a),this.options=((e,t)=>{const a="china"==e;return{title:{left:"center",top:20,text:"",textStyle:{color:"rgb(179, 239, 255)",fontSize:16}},toolbox:{feature:{restore:{show:!1},dataView:{show:!1},dataZoom:{show:!1},magicType:{show:!1}},iconStyle:{normal:{borderColor:"#1990DA"}},top:0,right:0},tooltip:{show:!1,backgroundColor:"#0c2766",textStyle:{fontSize:12,fontWeight:"bold",color:"#fff"}},geo:[{map:e,roam:!0,center:a?[104,34]:null,zoom:a?1.5:1,scaleLimit:{min:.9,max:2},label:{show:!0,color:"#fff",fontSize:9,emphasis:{color:"#fff"}},itemStyle:{normal:{areaColor:{type:"linear-gradient",x:0,y:0,x2:500,y2:0,colorStops:["#1fb4d4","#2E98CA","#1E62AC","#00afd4","#0c519c","#1976d2"].map(((e,t,a)=>({offset:(t+1)/a.length,color:e}))),global:!0},borderColor:"#00f7ff",borderWidth:1},emphasis:{areaColor:"#f98637",shadowColor:"#00f7ff"}}}],series:[{name:"正常运行",type:"scatter",coordinateSystem:"geo",effectType:"ripple",rippleEffect:{color:"#00c755",number:3,period:4,scale:5,brushType:"fill"},label:{show:!0,position:"right",fontSize:20,color:"#16c00e",fontWeight:1e3,formatter:(e,t,a)=>e.data.plantNum?e.data.plantNum:""},hoverAnimation:!0,z:2,itemStyle:{normal:{areaColor:"#405fef",borderColor:"#fff",borderWidth:.5,color:"#52c41a",shadowBlur:10,shadowColor:"#151d29"}},emphasis:{scale:1.2,itemStyle:{color:"#00fefe"}},symbolSize:a?9:7,zlevel:1,data:[]},{name:"告警运行",type:"effectScatter",effectType:"ripple",showEffectOn:"emphasis",coordinateSystem:"geo",rippleEffect:{number:5,period:3,scale:10,brushType:"fill"},label:{show:!0,position:"right",fontSize:20,color:"#00c755",fontWeight:1e3,formatter:(e,t,a)=>e.data.plantNum?e.data.plantNum:""},hoverAnimation:!0,z:2,itemStyle:{normal:{color:"red",shadowBlur:10,shadowColor:"#151d29"},label:{show:!1}},emphasis:{scale:2.2,itemStyle:{color:"red"}},symbolSize:a?4:7,zlevel:1,data:[]},{name:"离线",type:"effectScatter",coordinateSystem:"geo",effectType:"ripple",showEffectOn:"emphasis",rippleEffect:{number:5,period:3,scale:10,brushType:"fill"},label:{show:!0,position:"right",fontSize:20,color:"#00c755",fontWeight:1e3,formatter:(e,t,a)=>e.data.plantNum?e.data.plantNum:""},hoverAnimation:!0,z:2,itemStyle:{normal:{areaColor:"#405fef",shadowBlur:10,shadowColor:"#333",color:"white",shadowBlur:10,shadowColor:"#151d29"}},emphasis:{scale:2.2,itemStyle:{color:"white"}},symbolSize:a?4:7,zlevel:1,data:[]},{name:"自检提示",type:"effectScatter",coordinateSystem:"geo",effectType:"ripple",showEffectOn:"emphasis",rippleEffect:{number:5,period:3,scale:10,brushType:"fill"},label:{show:!0,position:"right",fontSize:20,color:"#00c755",fontWeight:1e3,formatter:(e,t,a)=>e.data.plantNum?e.data.plantNum:""},hoverAnimation:!0,z:2,itemStyle:{normal:{areaColor:"#405fef",shadowBlur:10,shadowColor:"#333",color:"orange",shadowBlur:10,shadowColor:"#151d29"}},emphasis:{scale:2.2,itemStyle:{color:"orange"}},symbolSize:a?4:7,zlevel:1,data:[]},{name:"未初始化",type:"effectScatter",coordinateSystem:"geo",effectType:"ripple",showEffectOn:"emphasis",rippleEffect:{number:5,period:3,scale:10,brushType:"fill"},label:{show:!0,position:"right",fontSize:20,color:"#00c755",fontWeight:1e3,formatter:(e,t,a)=>e.data.plantNum?e.data.plantNum:""},hoverAnimation:!0,z:2,itemStyle:{normal:{areaColor:"#405fef",shadowBlur:10,shadowColor:"#333",color:"#151d29",shadowBlur:10,shadowColor:"#151d29"}},emphasis:{scale:2.2,itemStyle:{color:"#151d29"}},symbolSize:a?4:7,zlevel:1,data:[]}]}})(e,this.mapJson),j(e,this.mapJson),"china"==e&&(this.chart=null,this.chart=R(this.el),window.onresize=()=>{var e;null==(e=this.chart)||e.resize()},this.chart.on("click",(e=>this.enterHandle(e)))),this.chart.clear(),this.chart.setOption(this.options,!0),await this.getPlantCoordinate()}async enterHandle(e){const{level:t,adcode:a,name:o}=this.findCoor(e.name);this.pushTitle(o,t,a)&&this.setChart(o,t,a)}async titleEnter(e){if(k._.isUndefined(e))this.parentIdList=[],await this.setChart();else{const{name:t,level:a,adcode:o}=e;this.pushTitle(t,a,o)&&this.setChart(t,a,o)}}async getPlantCoordinate(){var e,t;const a=null==(e=this.parentIdList.at(-1))?void 0:e.name,o={0:()=>P("","中国"),1:()=>P(a,""),2:()=>Y(a,""),3:()=>Y("",a)}[this.parentIdList.length],s=await o(),i=this.chart.getOption(),r=s;let l;"00000"==(null==r?void 0:r.status)&&(null==(t=null==r?void 0:r.data)?void 0:t.length)&&(this.parentIdList.length<2?(r.data.forEach((e=>delete e.plantNum)),l=r.data.map((e=>({...e,value:[1*e.longitude,1*e.latitude],name:e.area||e.plantName}))),i.series.at(0).data=l):(l=k._.groupBy(r.data,"plantStatus"),k._.forIn(l,((e,t)=>{i.series.at(this.statusDic[t]).data=e.map((e=>({...e,value:[1*e.longitude,1*e.latitude],name:e.area||e.plantName})))}))),this.isFirstLoad&&1==(null==l?void 0:l.length)&&this.enterHandle(l[0]),this.chart.setOption(i),this.loopPointer(l))}async loopPointer(e){if(this.chart.getOption(),1==this.parentIdList.length){const t=new Q(0,e.length);this.timer=setInterval((()=>{const a=e[t.get()],{area:o,plantName:s,name:i}=a;this.plantName=k._.isUndefined(i)?"":i,this.getWeather(o),t.loopInc()}),5e3)}if(this.parentIdList.length>1){this.getWeather(this.parentIdList[1].name);const t=["告警","光精灵离线","自检提示","逆变器夜间离线"].map((t=>{var a;return null==(a=e[t])?void 0:a.length})),a=new ie([0,...t]),o=async()=>{var t,o;this.chart.dispatchAction({type:"downplay"}),this.chart.dispatchAction({type:"highlight",seriesIndex:a.seriesIndex,dataIndex:a.dataIndex});const s=k._.invert(this.statusDic),i=null==(t=e[s[a.seriesIndex]])?void 0:t.at(a.dataIndex),{plantName:r,plantStatus:l}=i||{};this.plantName=k._.isUndefined(r)?"":r,this.nodeStatus=l;const n=F().format("YYYY-MM-DD"),h=await G({"告警":"alarm","自检提示":"selfCheck"}[l],1,100,n,n,r,"","",0);this.alarmList=null==(o=null==h?void 0:h.data)?void 0:o.list};a.isLoop()?(await o(),this.timer=setInterval((async()=>{await o(),a.next()}),6e4)):await o()}}async getWeather(e){const t=await q(e);this.weatherData=t.data.filter((e=>e.time.includes(F().format("HH")+":00:00")))[0]}shineColorPointer(e){const t=this.chart.getOption();t.series[e].emphasis.scale=1.8,t.series[e].emphasis.itemStyle={color:"#00fefe",borderColor:"#fff",borderWidth:2,symbolSize:20,symbol:"image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAATCAMAAABFjsb+AAAArlBMVEUAAACqqgD//wC/vwD//wDV1Sv//yvq6hX/6hX/6xTt7RL/7ST27Rr27hr37h737x338Bv48Br48B747R337x337x337x338Bz38Bz47hv48Bv27xv27x347x328B358Bz28Bz58Bz37hv58B337x358R337x337xz47xv27xv47xz37xv37xz58B358B358B348Bv58Bv38Bz48Rz38Bz38B337xv37xv48BzEer9hAAAAOHRSTlMAAwMEBAYGDAwNDg46Ozw+Q0RFR2BhYmRlaGhyc3N0dnd3e3t8fH2Ajo+QocvLzPDy8vf3+Pn8/ZxQyHYAAAABYktHRDnXAJVAAAAAnElEQVQY03WQxxKCUBAExywGzGLAiNqYEPP7/y/zQIFaYh+ntnZ3WpIkyx64C3dgW4rJtzwils1cFJUd3jglSSqO+GRYkNQB8IPwEh59gLZUXQH7mzHGmOse8CpqALu7ibhtAVt9IDAxB6CnKRAm2QlwtQGeSfYA1pr8zI1T99V/7tZS/4t7nM056aFs96tvN/PHS6q/yPOcWez5Bam3MAvM/3lWAAAAAElFTkSuQmCC"},t.series[e].emphasis.itemStyle.label={show:!0,backgroudColor:{image:"https://d33wubrfki0l68.cloudfront.net/2f6479d73bc25170dc532dd42e059166573bf478/61057/favicon.svg"},width:"20px",height:"20px"},this.chart.setOption(t)}}class ie{constructor(e){this.dataIndex=0,this.index=0,this.splitLen=e,this.seriesIndex=e.findIndex((e=>e)),this.max=k._.sum(e)}isLoop(){return this.splitLen.some((e=>e))&&k._.sum(this.splitLen.filter((e=>e)))>1}next(){this.index+1>this.max&&(this.index=0);let e=this.index;for(let t=0;t<this.splitLen.length;t++){if(!(e>=this.splitLen[t]))return this.index++,this.seriesIndex=t,this.dataIndex=e,{seriesIndex:t,dataIndex:this.dataIndex};e-=this.splitLen[t]}}}export{oe as _,Z as a,se as c};
