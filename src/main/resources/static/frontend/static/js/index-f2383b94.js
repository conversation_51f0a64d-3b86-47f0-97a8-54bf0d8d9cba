import{a as e,G as a,o as l,p as n,q as t}from"./element-plus-d975be09.js";import{b as s,a as o,_ as u}from"./index-8cc8d4b8.js";import"./vue-5bfa3a54.js";import{s as c}from"./screenfull-c82f2093.js";import{d as i,h as r,e as d,M as f,w as m,o as p,c as v,F as x,f as _,a8 as g,x as y,t as b,aa as k}from"./@vue-5e5cdef9.js";const j={class:"screenfull u-flex-center-no"},h=u(i({__name:"index",props:{element:{default:"html"},openTips:{default:"进入全屏"},exitTips:{default:"退出全屏"},content:{type:Boolean,default:!1},type:{default:"icon"}},setup(u){const i=s(),h=u,w=r(!1),C=d((()=>w.value?h.exitTips:h.openTips)),T=d((()=>w.value?"fullscreen-exit":"fullscreen")),q=d((()=>w.value?"退出全屏":"进入全屏")),E=()=>{if("string"==typeof h.element){const a=document.querySelector(h.element)||void 0;c.isEnabled?c.toggle(a):e.warning("您的浏览器无法工作")}else{if("退出全屏"===q.value)return void c.exit();c.isEnabled?c.request(h.element):e.warning("您的浏览器无法工作")}},S=()=>{w.value=c.isFullscreen};f((e=>{c.on("change",S),e((()=>{c.isEnabled&&c.off("change",S)}))})),m(w,(e=>{i.toggleScreenStatus(e)}));const F=r(!1),z=d((()=>F.value?"内容区复原":"内容区放大")),B=d((()=>F.value?"fullscreen-exit":"fullscreen")),G=()=>{document.body.className=F.value?"":"content-large",F.value=!F.value};return(e,s)=>{const u=o,c=a,i=l,r=n,d=t;return p(),v("div",j,[e.content?(p(),_(d,{key:1},{dropdown:g((()=>[y(r,null,{default:g((()=>[y(i,{onClick:G},{default:g((()=>[k(b(z.value),1)])),_:1}),y(i,{onClick:E,disabled:w.value},{default:g((()=>[k("内容区全屏")])),_:1},8,["disabled"])])),_:1})])),default:g((()=>[y(u,{name:B.value},null,8,["name"])])),_:1})):(p(),v(x,{key:0},["icon"==e.type?(p(),_(c,{key:0,effect:"dark",content:C.value,placement:"bottom"},{default:g((()=>[y(u,{name:T.value,onClick:E},null,8,["name"])])),_:1},8,["content"])):(p(),v("div",{key:1,onClick:E,class:"screen-text"},b(q.value),1))],64))])}}}),[["__scopeId","data-v-8d6d6eb1"]]);export{h as _};
