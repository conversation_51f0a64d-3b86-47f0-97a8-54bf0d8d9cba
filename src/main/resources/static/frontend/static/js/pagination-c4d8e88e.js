import"./vue-5bfa3a54.js";import{a as e}from"./naive-ui-0ee0b8c3.js";import{o as a,c as p,x as t}from"./@vue-5e5cdef9.js";const s={class:"tw-flex tw-justify-center tw-w-100"},i={__name:"pagination",props:["page"],setup(i){const o=i;return(i,g)=>{const r=e;return a(),p("main",s,[t(r,{page:o.page.page,"onUpdate:page":g[0]||(g[0]=e=>o.page.page=e),itemCount:o.page.total,"onUpdate:itemCount":g[1]||(g[1]=e=>o.page.total=e),"page-sizes":[10,20,30,40],displayOrder:["quick-jumper","pages","size-picker"],"page-size":o.page.pageSize,"onUpdate:pageSize":g[2]||(g[2]=e=>o.page.pageSize=e),"show-quick-jumper":"","show-size-picker":"",ref:"pageRef"},null,8,["page","itemCount","page-size"])])}}};export{i as _};
