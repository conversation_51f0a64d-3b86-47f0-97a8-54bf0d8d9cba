import{t as e,g as t,d as r,c as n}from"./date-fns-992c4b85.js";function a(e,t,r){var n=function(e,t,r){if(r&&!r.code)throw new Error("date-fns-tz error: Please set a language code on the locale object imported from date-fns, e.g. `locale.code = 'en-US'`");return new Intl.DateTimeFormat(r?[r.code,"en-US"]:void 0,{timeZone:t,timeZoneName:e})}(e,r.timeZone,r.locale);return n.formatToParts?function(e,t){for(var r=e.formatToParts(t),n=r.length-1;n>=0;--n)if("timeZoneName"===r[n].type)return r[n].value}(n,t):function(e,t){var r=e.format(t).replace(/\u200E/g,""),n=/ [\w-+ ]+$/.exec(r);return n?n[0].substr(1):""}(n,t)}function i(e,t){var r=function(e){if(!o[e]){var t=new Intl.DateTimeFormat("en-US",{hourCycle:"h23",timeZone:"America/New_York",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}).format(new Date("2014-06-25T04:00:00.123Z")),r="06/25/2014, 00:00:00"===t||"‎06‎/‎25‎/‎2014‎ ‎00‎:‎00‎:‎00"===t;o[e]=r?new Intl.DateTimeFormat("en-US",{hourCycle:"h23",timeZone:e,year:"numeric",month:"numeric",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}):new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:e,year:"numeric",month:"numeric",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}return o[e]}(t);return r.formatToParts?function(e,t){try{for(var r=e.formatToParts(t),n=[],a=0;a<r.length;a++){var i=u[r[a].type];i>=0&&(n[i]=parseInt(r[a].value,10))}return n}catch(o){if(o instanceof RangeError)return[NaN];throw o}}(r,e):function(e,t){var r=e.format(t),n=/(\d+)\/(\d+)\/(\d+),? (\d+):(\d+):(\d+)/.exec(r);return[n[3],n[1],n[2],n[4],n[5],n[6]]}(r,e)}var u={year:0,month:1,day:2,hour:3,minute:4,second:5};var o={};function c(e,t,r,n,a,i,u){var o=new Date(0);return o.setUTCFullYear(e,t,r),o.setUTCHours(n,a,i,u),o}var f=36e5,l=6e4,s={timezone:/([Z+-].*)$/,timezoneZ:/^(Z)$/,timezoneHH:/^([+-]\d{2})$/,timezoneHHMM:/^([+-])(\d{2}):?(\d{2})$/};function d(e,t,r){var n,a,i;if(!e)return 0;if(n=s.timezoneZ.exec(e))return 0;if(n=s.timezoneHH.exec(e))return g(i=parseInt(n[1],10))?-i*f:NaN;if(n=s.timezoneHHMM.exec(e)){i=parseInt(n[2],10);var u=parseInt(n[3],10);return g(i,u)?(a=Math.abs(i)*f+u*l,"+"===n[1]?-a:a):NaN}if(function(e){if(D[e])return!0;try{return new Intl.DateTimeFormat(void 0,{timeZone:e}),D[e]=!0,!0}catch(t){return!1}}(e)){t=new Date(t||Date.now());var o=r?t:function(e){return c(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds())}(t),d=m(o,e),v=r?d:function(e,t,r){var n=e.getTime(),a=n-t,i=m(new Date(a),r);if(t===i)return t;a-=i-t;var u=m(new Date(a),r);if(i===u)return i;return Math.max(i,u)}(t,d,e);return-v}return NaN}function m(e,t){var r=i(e,t),n=c(r[0],r[1]-1,r[2],r[3]%24,r[4],r[5],0).getTime(),a=e.getTime(),u=a%1e3;return n-(a-=u>=0?u:1e3+u)}function g(e,t){return-23<=e&&e<=23&&(null==t||0<=t&&t<=59)}var D={};function v(e,t){var r=e?d(e,t,!0)/6e4:t.getTimezoneOffset();if(Number.isNaN(r))throw new RangeError("Invalid time zone specified: "+e);return r}function w(e,t){for(var r=e<0?"-":"",n=Math.abs(e).toString();n.length<t;)n="0"+n;return r+n}function N(e,t){var r=t||"",n=e>0?"-":"+",a=Math.abs(e);return n+w(Math.floor(a/60),2)+r+w(Math.floor(a%60),2)}function h(e,t){return e%60==0?(e>0?"-":"+")+w(Math.abs(e)/60,2):N(e,t)}const p={X:function(e,t,r,n){var a=v(n.timeZone,e);if(0===a)return"Z";switch(t){case"X":return h(a);case"XXXX":case"XX":return N(a);default:return N(a,":")}},x:function(e,t,r,n){var a=v(n.timeZone,e);switch(t){case"x":return h(a);case"xxxx":case"xx":return N(a);default:return N(a,":")}},O:function(e,t,r,n){var a=v(n.timeZone,e);switch(t){case"O":case"OO":case"OOO":return"GMT"+function(e,t){var r=e>0?"-":"+",n=Math.abs(e),a=Math.floor(n/60),i=n%60;if(0===i)return r+String(a);var u=t||"";return r+String(a)+u+w(i,2)}(a,":");default:return"GMT"+N(a,":")}},z:function(e,t,r,n){switch(t){case"z":case"zz":case"zzz":return a("short",e,n);default:return a("long",e,n)}}};var T=36e5,Y=6e4,M=2,x={dateTimePattern:/^([0-9W+-]+)(T| )(.*)/,datePattern:/^([0-9W+-]+)(.*)/,plainTime:/:/,YY:/^(\d{2})$/,YYY:[/^([+-]\d{2})$/,/^([+-]\d{3})$/,/^([+-]\d{4})$/],YYYY:/^(\d{4})/,YYYYY:[/^([+-]\d{4})/,/^([+-]\d{5})/,/^([+-]\d{6})/],MM:/^-(\d{2})$/,DDD:/^-?(\d{3})$/,MMDD:/^-?(\d{2})-?(\d{2})$/,Www:/^-?W(\d{2})$/,WwwD:/^-?W(\d{2})-?(\d{1})$/,HH:/^(\d{2}([.,]\d*)?)$/,HHMM:/^(\d{2}):?(\d{2}([.,]\d*)?)$/,HHMMSS:/^(\d{2}):?(\d{2}):?(\d{2}([.,]\d*)?)$/,timeZone:/(Z|[+-]\d{2}(?::?\d{2})?| UTC| [a-zA-Z]+\/[a-zA-Z_]+(?:\/[a-zA-Z_]+)?)$/};function Z(r,n){if(arguments.length<1)throw new TypeError("1 argument required, but only "+arguments.length+" present");if(null===r)return new Date(NaN);var a=n||{},i=null==a.additionalDigits?M:e(a.additionalDigits);if(2!==i&&1!==i&&0!==i)throw new RangeError("additionalDigits must be 0, 1 or 2");if(r instanceof Date||"object"==typeof r&&"[object Date]"===Object.prototype.toString.call(r))return new Date(r.getTime());if("number"==typeof r||"[object Number]"===Object.prototype.toString.call(r))return new Date(r);if("string"!=typeof r&&"[object String]"!==Object.prototype.toString.call(r))return new Date(NaN);var u=function(e){var t,r={},n=x.dateTimePattern.exec(e);n?(r.date=n[1],t=n[3]):(n=x.datePattern.exec(e))?(r.date=n[1],t=n[2]):(r.date=null,t=e);if(t){var a=x.timeZone.exec(t);a?(r.time=t.replace(a[1],""),r.timeZone=a[1].trim()):r.time=t}return r}(r),o=function(e,t){var r,n=x.YYY[t],a=x.YYYYY[t];if(r=x.YYYY.exec(e)||a.exec(e)){var i=r[1];return{year:parseInt(i,10),restDateString:e.slice(i.length)}}if(r=x.YY.exec(e)||n.exec(e)){var u=r[1];return{year:100*parseInt(u,10),restDateString:e.slice(u.length)}}return{year:null}}(u.date,i),c=o.year,f=function(e,t){if(null===t)return null;var r,n,a,i;if(0===e.length)return(n=new Date(0)).setUTCFullYear(t),n;if(r=x.MM.exec(e))return n=new Date(0),S(t,a=parseInt(r[1],10)-1)?(n.setUTCFullYear(t,a),n):new Date(NaN);if(r=x.DDD.exec(e)){n=new Date(0);var u=parseInt(r[1],10);return function(e,t){if(t<1)return!1;var r=I(e);if(r&&t>366)return!1;if(!r&&t>365)return!1;return!0}(t,u)?(n.setUTCFullYear(t,0,u),n):new Date(NaN)}if(r=x.MMDD.exec(e)){n=new Date(0),a=parseInt(r[1],10)-1;var o=parseInt(r[2],10);return S(t,a,o)?(n.setUTCFullYear(t,a,o),n):new Date(NaN)}if(r=x.Www.exec(e))return b(t,i=parseInt(r[1],10)-1)?y(t,i):new Date(NaN);if(r=x.WwwD.exec(e)){i=parseInt(r[1],10)-1;var c=parseInt(r[2],10)-1;return b(t,i,c)?y(t,i,c):new Date(NaN)}return null}(o.restDateString,c);if(isNaN(f))return new Date(NaN);if(f){var l,s=f.getTime(),m=0;if(u.time&&(m=function(e){var t,r,n;if(t=x.HH.exec(e))return z(r=parseFloat(t[1].replace(",",".")))?r%24*T:NaN;if(t=x.HHMM.exec(e))return z(r=parseInt(t[1],10),n=parseFloat(t[2].replace(",",".")))?r%24*T+n*Y:NaN;if(t=x.HHMMSS.exec(e)){r=parseInt(t[1],10),n=parseInt(t[2],10);var a=parseFloat(t[3].replace(",","."));return z(r,n,a)?r%24*T+n*Y+1e3*a:NaN}return null}(u.time),isNaN(m)))return new Date(NaN);if(u.timeZone||a.timeZone){if(l=d(u.timeZone||a.timeZone,new Date(s+m)),isNaN(l))return new Date(NaN)}else l=t(new Date(s+m)),l=t(new Date(s+m+l));return new Date(s+m+l)}return new Date(NaN)}function y(e,t,r){t=t||0,r=r||0;var n=new Date(0);n.setUTCFullYear(e,0,4);var a=7*t+r+1-(n.getUTCDay()||7);return n.setUTCDate(n.getUTCDate()+a),n}var H=[31,28,31,30,31,30,31,31,30,31,30,31],U=[31,29,31,30,31,30,31,31,30,31,30,31];function I(e){return e%400==0||e%4==0&&e%100!=0}function S(e,t,r){if(t<0||t>11)return!1;if(null!=r){if(r<1)return!1;var n=I(e);if(n&&r>U[t])return!1;if(!n&&r>H[t])return!1}return!0}function b(e,t,r){return!(t<0||t>52)&&(null==r||!(r<0||r>6))}function z(e,t,r){return(null==e||!(e<0||e>=25))&&((null==t||!(t<0||t>=60))&&(null==r||!(r<0||r>=60)))}var C=/([xXOz]+)|''|'(''|[^'])+('|$)/g;function $(e,t,a,i){var u=n(i);return u.timeZone=t,u.originalDate=e,function(e,t,n){var a=String(t),i=n||{},u=a.match(C);if(u){var o=Z(i.originalDate||e,i);a=u.reduce((function(e,t){if("'"===t[0])return e;var r=e.indexOf(t),n="'"===e[r-1],a=e.replace(t,"'"+p[t[0]](o,t,null,i)+"'");return n?a.substring(0,r-1)+a.substring(r+1):a}),a)}return r(e,a,i)}(function(e,t,r){var n=Z(e,r),a=d(t,n,!0),i=new Date(n.getTime()-a),u=new Date(0);return u.setFullYear(i.getUTCFullYear(),i.getUTCMonth(),i.getUTCDate()),u.setHours(i.getUTCHours(),i.getUTCMinutes(),i.getUTCSeconds(),i.getUTCMilliseconds()),u}(e,t),a,u)}export{$ as f};
