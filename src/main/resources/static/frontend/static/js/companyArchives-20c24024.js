import{a as e,F as s,d as t,Y as a,K as o,U as i}from"./quasar-b3f06d8a.js";import{_ as r}from"./pagination-c4d8e88e.js";import"./vue-5bfa3a54.js";import{j as l,h as p,e as m,m as n,az as j,o as c,c as d,a9 as u,b as v,f as g,a8 as b,x as f,F as h,k as w,aa as x,t as y,C as k,D as _,a as z}from"./@vue-5e5cdef9.js";import{l as U}from"./labelValueToObj-fe73517d.js";import{L as V}from"./ui-d5488bf7.js";import{X as q}from"./archivesManageApi-eb99912e.js";import{B as C}from"./@vueuse-af86c621.js";import{m as D}from"./notification-950a5f80.js";import{_ as S}from"./index-8cc8d4b8.js";import"./naive-ui-0ee0b8c3.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./lodash-es-ea7deab5.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./@babel-f3c0a00c.js";import"./date-fns-tz-0cc073c8.js";import"./async-validator-cf877c1f.js";import"./lodash-6d99edc3.js";import"./@x-ui-vue3-df3ba55b.js";import"./api-b858041e.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./menuStore-26f8ddd8.js";import"./vue-router-6159329f.js";import"./dayjs-d60cc07f.js";import"./icons-95011f8c.js";import"./@vicons-f32a0bdb.js";import"./axios-84f1a956.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./element-plus-d975be09.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./xe-utils-fe99d42a.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";/* empty css                *//* empty css                    */import"./vxe-table-plugin-element-7bbbb435.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";const T=l([{label:"地区",field:"plantStatus",align:"center",search:p("")},{label:"企业编号",field:"plantName",align:"center",search:p("")},{label:"企业全称",field:"createTime",align:"center",search:p("")},{label:"单位类型",field:"plantCapacity",align:"center",search:p("")},{label:"企业性质",field:"status",align:"center",search:p("")},{label:"地址",field:"inverNums",align:"center",search:p("")},{label:"时间",field:"plantTypeId",align:"center",search:p("")}]),A=e=>(k("data-v-2164809c"),e=e(),_(),e),N={class:"q-pa-md"},B=A((()=>z("h6",{class:"tw-text-2xl"},"企业档案",-1))),F=A((()=>z("div",{class:"full-width row flex-center text-accent q-gutter-sm"},[z("span",null," 没有可用数据 ")],-1))),I=S({__name:"companyArchives",setup(l){let k=p([]);m((()=>U(T,"field","search")));const _=new V;async function z(){let e;_.set(!0);try{const s=await q();e=getResponseData(s),C(k,e.data)}catch(s){D.error(e.message)}_.set(!1)}return p([Date.now(),Date.now()]),n((async()=>{await z()})),(l,p)=>{const m=e,n=s,U=t,V=a,q=o,C=r,D=i,S=j("skeleton-item"),A=j("skeleton");return c(),d("div",N,[u((c(),g(D,{"row-key":"plantUid","virtual-scroll":"",separator:"cell","table-header-class":"","title-class":"tw-bg-blue-300","table-class":"tw-bg-gray-100 tw--h-100",rows:v(k),columns:v(T),"rows-per-page-options":[0],loading:v(_).value.loading,"table-style":{height:"780px"}},{top:b((()=>[B,f(m),u(f(n,{class:"tw-bg-yellow-400 tw-text-white tw-mr-3",label:"查询","no-caps":"",onClick:()=>{}},null,512),[[S]]),u(f(n,{class:"tw-bg-green-500 tw-text-white",label:"导出","no-caps":"",onClick:()=>{}},null,512),[[S]])])),"top-row":b((()=>[f(q,null,{default:b((()=>[(c(!0),d(h,null,w(v(T),(e=>(c(),g(V,{key:null==e?void 0:e.label,style:{"border-bottom-width":"1px"}},{default:b((()=>[u(f(U,{borderless:"",dense:"",modelValue:e.search,"onUpdate:modelValue":s=>e.search=s,placeholder:"🔍"},null,8,["modelValue","onUpdate:modelValue"]),[[S]])])),_:2},1024)))),128))])),_:1})])),"body-cell":b((e=>[f(V,{props:e},{default:b((()=>[x(y(e.value),1)])),_:2},1032,["props"])])),pagination:b((e=>[f(C,{onUpdatePagination:z,total:100})])),"no-data":b((({icon:e,message:s,filter:t})=>[F])),_:1},8,["rows","columns","loading"])),[[A,v(_).value,void 0,{animated:!0}]])])}}},[["__scopeId","data-v-2164809c"]]);export{I as default};
