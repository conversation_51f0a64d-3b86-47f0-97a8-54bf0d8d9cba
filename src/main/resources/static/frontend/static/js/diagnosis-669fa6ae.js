import"./vue-5bfa3a54.js";import"./echarts-f30da64f.js";import{l as t}from"./lodash-6d99edc3.js";import{V as e}from"./zrender-c058db04.js";import{X as s,a as i}from"./dataAnalysisApi-c10bbebe.js";import{g as a}from"./api-b858041e.js";import{e as o}from"./echartsInit-0067e609.js";import{s as l}from"./chartXY-a0399c4a.js";import{_ as r}from"./index-8cc8d4b8.js";import{S as n,T as m,R as p,b as c}from"./@vicons-f32a0bdb.js";import{h as w,m as d,az as u,a9 as f,o as h,c as b,a as x,x as y,b as j,t as v,H as g,C as S,D as k}from"./@vue-5e5cdef9.js";import{b as z}from"./naive-ui-0ee0b8c3.js";import"./@babel-f3c0a00c.js";import"./tslib-a4e99503.js";import"./dayjs-d60cc07f.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./@vueuse-af86c621.js";import"./vue-router-6159329f.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./menuStore-26f8ddd8.js";import"./icons-95011f8c.js";import"./notification-950a5f80.js";import"./quasar-b3f06d8a.js";import"./element-plus-d975be09.js";import"./lodash-es-ea7deab5.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./xe-utils-fe99d42a.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-7bbbb435.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./date-fns-tz-0cc073c8.js";const L={backgroundColor:"#fff",legend:{show:!1},title:{text:"今日故障统计",top:"5%",left:"3%",textStyle:{fontSize:_(20)}},grid:{left:"4%",right:"10%",bottom:"2%",top:"20%",containLabel:!0},xAxis:{type:"value",axisTick:{show:!1},axisLine:{show:!1},splitLine:{show:!1},axisLabel:{show:!1}},yAxis:[{type:"category",axisTick:{show:!1},axisLine:{show:!1,lineStyle:{color:"#000"}},axisLabel:{inside:!1,color:"#000",fontWeight:"normal",fontSize:_(20)},data:["一级","二级","三级"].reverse()},{type:"category",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{show:!1},splitArea:{show:!1},splitLine:{show:!1},data:["一级","二级","三级"].reverse()},{type:"category",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{show:!1},splitArea:{show:!1},splitLine:{show:!1},data:["一级","二级","三级"].reverse()}],series:[{name:"",type:"bar",stack:"1",yAxisIndex:0,data:[],color:["#6BF1BF","#C7F895","#E6D349","#F8A065","#FF6B5F"],barWidth:20,z:1},{name:"",type:"bar",yAxisIndex:2,data:[100,100,100],barWidth:20,itemStyle:{color:"#e0e0e0",barBorderRadius:12},label:{normal:{show:!0,color:"#000",fontSize:_(20),position:"right"}},z:0}]},A={title:[{text:"故障级别分布",top:"10px",left:"20px",textStyle:{fontSize:_(20)}},{text:"故障率",top:"center",left:"center",textStyle:{fontSize:_(25)}}],tooltip:{trigger:"item"},series:[{name:"",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,label:{show:!0,fontSize:_(20),formatter:t=>t.name+"\n"+t.percent+"%"},emphasis:{label:{show:!0,fontSize:_(25),fontWeight:"bold"}},itemStyle:{borderRadius:3,borderColor:"#fff",borderWidth:2},data:[{value:1048,name:"一级"},{value:735,name:"二级"},{value:580,name:"三级"}]}]},F={backgroundColor:"#fff",title:{text:"工作效率",subtext:"1090",textStyle:{fontSize:20,fontWeight:800,color:"#000",lineHeight:40},subtextStyle:{fontSize:_(40),fontWeight:700,color:"#000"},top:"2%",left:"5%"},tooltip:{trigger:"axis"},grid:{bottom:10,left:0,right:10},xAxis:{data:[],show:!1},yAxis:[{type:"value",show:!1}],series:[{name:"",type:"bar",data:[0,4,4,5,9],symbolSize:1,barMaxWidth:"30",symbol:"circle",smooth:!0,yAxisIndex:0,showSymbol:!1,lineStyle:{width:3,color:new e(0,1,0,0,[{offset:0,color:"#2196f3"},{offset:1,color:"#1577e7"}]),shadowColor:"rgba(158,135,255, 0.3)",shadowBlur:10,shadowOffsetY:15}}]},R={title:{text:"天气数据",left:"5%",top:"5%",textStyle:{fontSize:_(25)},textAlign:"center"},grid:{left:"5%",right:"8%",bottom:"9%",top:"18%"},tooltip:{trigger:"axis",axisPointer:{lineStyle:{color:"#ddd"}},backgroundColor:"rgba(255,255,255,1)",padding:[5,10],textStyle:{color:"#7588E4"},extraCssText:"box-shadow: 0 0 5px rgba(0,0,0,0.3)"},legend:{right:10,top:10,orient:"vertical"},xAxis:{type:"category",data:["00:00","2:00","4:00","6:00","8:00","10:00","12:00","14:00","16:00","18:00","20:00","22:00"],boundaryGap:!1,splitLine:{show:!0,interval:"auto",lineStyle:{color:["#D4DFF5"]}},axisTick:{show:!1},axisLine:{lineStyle:{color:"#609ee9"}},axisLabel:{margin:10,fontSize:14}},yAxis:{type:"value",splitLine:{lineStyle:{color:["#D4DFF5"]}},axisTick:{show:!1},axisLine:{lineStyle:{color:"#609ee9"}},axisLabel:{margin:10,fontSize:14}},series:[{name:"今日",type:"line",smooth:!0,showSymbol:!1,symbol:"circle",symbolSize:6,data:["1200","1400","1008","1411","1026","1288","1300","800","1100","1000","1118","1322"],areaStyle:{normal:{color:new e(0,0,0,1,[{offset:0,color:"rgba(199, 237, 250,0.5)"},{offset:1,color:"rgba(199, 237, 250,0.2)"}],!1)}},lineStyle:{normal:{width:3}}},{name:"昨日",type:"line",smooth:!0,showSymbol:!1,symbol:"circle",symbolSize:6,data:["1200","1400","808","811","626","488","1600","1100","500","300","1998","822"],areaStyle:{normal:{color:new e(0,0,0,1,[{offset:0,color:"rgba(216, 244, 247,1)"},{offset:1,color:"rgba(216, 244, 247,1)"}],!1)}},itemStyle:{normal:{color:"#58c8da"}},lineStyle:{normal:{width:3}}}]};function _(t){const e=window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth;return parseInt(t*((e>=1200?e:1200)/1920))}const W=t=>(S("data-v-5e74864f"),t=t(),k(),t),B={class:"tw-h-full tw-w-full tw-p-4 tw-bg-gray-200"},C={class:"tw-bg-white tw-pt-2"},D={class:"tw-pl-4 tw-flex tw-items-center"},T=W((()=>x("h6",{class:"tw-pb-2"},"天气",-1))),I={class:"tw-w-9 tw-h-9 tw-bg-orange-200 tw-rounded-md tw-justify-center tw-items-center tw-inline-flex"},E={class:"text-h4 tw-leading-10 tw-font-semibold tw-pl-2"},q={class:"tw-pl-4 tw-flex tw-items-center"},U=W((()=>x("h6",{class:"tw-pb-2"},"温度",-1))),H={class:"tw-w-9 tw-h-9 tw-bg-teal-200 tw-rounded-md tw-justify-center tw-items-center tw-inline-flex"},M={class:"text-h4 tw-leading-10 tw-font-semibold tw-pl-2"},O={class:"tw-pl-4 tw-flex tw-items-center"},V=W((()=>x("h6",{class:"tw-pb-2"},"风速",-1))),X={class:"tw-w-9 tw-h-9 tw-bg-blue-200 tw-rounded-md tw-justify-center tw-items-center tw-inline-flex"},Y={class:"text-h4 tw-leading-10 tw-font-semibold tw-pl-2"},G={class:"tw-pl-4 tw-flex tw-items-center"},N=W((()=>x("h6",{class:"tw-pb-2"},"云量",-1))),P={class:"tw-w-9 tw-h-9 tw-bg-purple-200 tw-rounded-md tw-justify-center tw-items-center tw-inline-flex"},Q={class:"text-h4 tw-leading-10 tw-font-semibold tw-pl-2"},Z={class:"tw=p"},$=W((()=>x("section",{class:"tw-bg-white"},null,-1))),J={class:"tw-bg-white tw-pt-2 tw-px-4"},K=W((()=>x("p",{class:"tw-text-xl tw-m-0 tw-pb-1"},"诊断结果",-1))),tt={class:"tw-bg-white tw-pt-2 tw-px-4"},et=W((()=>x("p",{class:"tw-text-xl tw-m-0 tw-pb-1"},"运维建议",-1))),st=r({__name:"diagnosis",setup(e){let r="";const S=w(),k=w(),_=w(),W=w(),st=w(),it=w(),at=w({weather:"无数据",temperature:"无数据",windspeed:"无数据",cloud:"无数据"}),ot=w(""),lt=w("");return d((async()=>{r=router.currentRoute.value.query.plantUid,await async function(){var e,n,m;{const t=await s(),i=a(t).data,l=["一级","二级","三级"],r=[(null==(e=i.find((t=>1==t.alarmLevel)))?void 0:e.nums)??"0",(null==(n=i.find((t=>2==t.alarmLevel)))?void 0:n.nums)??"0",(null==(m=i.find((t=>3==t.alarmLevel)))?void 0:m.nums)??"0"].map(((t,e)=>({value:t,itemStyle:{normal:{color:["#6BF1BF","#C7F895","#E6D349","#F8A065","#FF6B5F"][e],barBorderRadius:12}}})));L.series[0].data=r.reverse(),L.series[1].label.normal.formatter=t=>r[t.dataIndex].value,await o(S,L),await o(k,function(t,e){return t.series[0].data=e,t}(A,i.map((t=>{const e=l[t.alarmLevel-1];if(e)return{name:e,value:t.nums}})).filter((t=>t))))}{const e=await i(r),s=a(e).data,n=[s.electricityResult.map((t=>t.collectDate)),s.electricityResult.map((t=>t.electricity))],m=s.weatherResult;at.value=m.at(-1);let w=l(F,...n);p=w,c=n[1].at(-1),p.title.subtext=c,w=p,await o(it,function(e,s,i=R){i.xAxis.data=e;const a=t._.cloneDeep(i.series.at(0));return i.series=s.map((t=>({...a,...t}))),i}(m.map((t=>t.time.slice(11,16))),[{data:m.map((t=>t.cloud)),name:"云量"},{data:m.map((t=>t.pressure/100)),name:"气压"},{data:m.map((t=>t.windspeed)),name:"风速"},{data:m.map((t=>t.humidity)),name:"湿度"},{data:m.map((t=>t.precip)),name:"降雨"}])),await o(W,w),await o(st,function(t,e){return t.title.text=e,t}(w,"附近电站近七日发电量"))}var p,c}()})),(t,e)=>{const s=z,i=u("x"),a=u("y"),o=u("g");return f((h(),b("div",B,[f((h(),b("section",null,[f((h(),b("section",C,[f((h(),b("section",null,[x("section",D,[x("div",null,[T,x("div",I,[y(j(n),{class:"tw-w-4/6"})]),x("span",E,v(j(at).weather),1)])]),x("section",q,[x("div",null,[U,x("div",H,[y(j(m),{class:"tw-w-4/6"})]),x("span",M,v(j(at).temperature??"无数据"),1)])]),x("section",O,[x("div",null,[V,x("div",X,[y(j(p),{class:"tw-w-4/6"})]),x("span",Y,v(j(at).windspeed),1)])]),x("section",G,[x("div",null,[N,x("div",P,[y(j(c),{class:"tw-w-4/6"})]),x("span",Q,v(j(at).cloud),1)])])])),[[i,[1,1,1,1]]]),x("section",{ref_key:"weatherRef",ref:it},null,512)])),[[a,[1,4]]]),f((h(),b("section",null,[x("section",{ref_key:"lineRef",ref:S,class:"tw-bg-white tw-h-full"},null,512),x("section",{ref_key:"circleRef",ref:k,class:"tw-bg-white tw-h-full"},null,512)])),[[a,[4,5]],[o,20]])])),[[i,[2,1]],[o,20]]),f((h(),b("section",Z,[x("section",{class:"tw-bg-white",ref_key:"workRef",ref:_},null,512),$,x("section",{class:"tw-bg-white",ref_key:"dayRef",ref:W},null,512),x("section",{class:"tw-bg-white",ref_key:"nearDayRef",ref:st},null,512)])),[[i,[1,1,1,1]],[o,20]]),f((h(),b("section",null,[x("section",J,[K,y(s,{value:j(ot),"onUpdate:value":e[0]||(e[0]=t=>g(ot)?ot.value=t:null),type:"textarea",disabled:"",placeholder:"诊断结果",class:"tw-h-2/3 tw-text-base","show-count":""},null,8,["value"])]),x("section",tt,[et,y(s,{value:j(lt),"onUpdate:value":e[1]||(e[1]=t=>g(lt)?lt.value=t:null),type:"textarea",disabled:"",placeholder:"运维建议",class:"tw-h-2/3 tw-text-base","show-count":""},null,8,["value"])])])),[[i,[1,2]],[o,20]])])),[[a,[2,.9,.7]],[o,20]])}}},[["__scopeId","data-v-5e74864f"]]);export{st as default};
