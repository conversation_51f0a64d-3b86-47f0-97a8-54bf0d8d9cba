import{a as t}from"./api-b858041e.js";const e=(e=1,a=10,s="2023-01-01",i="",r=[])=>t("/system/statistics/getElectricityStatisticsInfo",{},{currentPage:e,pageSize:a,date:s,address:i,plantUIds:r},"post"),a=(e=1,a=10,s="",i="",r="",c="",m="",d="")=>t("/system/statistics/getPlantStatisticsInfo",{},{currentPage:e,pageSize:a,createStartTime:s,createEndTime:i,powerStartTime:r,powerEndTime:c,plantName:m,address:d},"post"),s=(e,a,s,i,r,c)=>t("/system/statistics/integrativeStatisticChart",{},{projectId:e,electricityPrice:a,dataStartTime:s,dataEndTime:i,createStartTime:r,createEndTime:c},"post"),i=(e,a,s,i,r,c)=>t("/system/statistics/integrativeStatisticSheet",{},{projectId:e,electricityPrice:a,createStartTime:s,createEndTime:i,dataStartTime:r,dataEndTime:c},"post"),r=()=>t("/system/project/getProjectSpecialInfo"),c=(e,a,s="sheetName")=>t("/system/statistics/exportElectricityStatisticsInfo",{},{date:e,address:a,sheetName:s},"post","blob");export{r as X,e as a,c as b,i as c,s as d,a as e};
