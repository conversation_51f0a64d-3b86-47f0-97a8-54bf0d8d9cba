import{f as e,B as a}from"./element-plus-d975be09.js";import"./vue-5bfa3a54.js";import{i as l}from"./echarts-f30da64f.js";import{d as t,c as d,f as i}from"./chartResize-3e3d11d7.js";import{h as o,j as n,p as s,as as m,o as r,c as u,a as c,x as p,a8 as f,aa as g,f as v,a9 as h,ab as x,a6 as b,F as k,k as V,B as j}from"./@vue-5e5cdef9.js";import{_}from"./index-8cc8d4b8.js";import"./lodash-es-ea7deab5.js";import"./@vueuse-af86c621.js";import"./vue-router-6159329f.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./dayjs-d60cc07f.js";import"./@babel-f3c0a00c.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./element-resize-detector-0d37a2ab.js";import"./batch-processor-06abf2b4.js";import"./lodash-6d99edc3.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./xe-utils-fe99d42a.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";import"./quasar-b3f06d8a.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-7bbbb435.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";const U={class:"form"},y={class:"table-btn"},T={key:1,class:"chart-gather","element-loading-text":"正在生成图表"},z={class:"table-btn"},C=j('<div class="supplementary-text" data-v-80a4fd68>补充文字</div><div class="body-small-text" data-v-80a4fd68>小文本</div><div class="body-text" data-v-80a4fd68>正常文字</div><div class="small-title" data-v-80a4fd68>小标题</div><div class="title" data-v-80a4fd68>正常标题</div><div class="main-title" data-v-80a4fd68>大标题</div>',6),w=_({__name:"index",setup(j){elementResizeDetectorMaker();const _=o(),w=o(),D=o(),L=o(!0),S=o(),P=o(),R=n({condition:{name:"",nickname:"",sex:"",age:""},modelData:{name:"",nickname:"",role:"",sex:"",age:0,num:0,checkedList:[],flag1:!1,date3:"",address:""},tablePage:{totalResult:15,currentPage:1,pageSize:10}}),B=o(!1),E=o(!1),q=o(),O=o(!1),X=o(!0),A={},J=o([{label:"女",value:"0"},{label:"男",value:"1"}]),N=n({name:[{required:!0,message:"请输入名称"},{min:3,max:5,message:"长度在 3 到 5 个字符"}],nickname:[{required:!0,message:"请输入昵称"}],sex:[{required:!0,message:"请选择性别"}]}),M=n({border:"inner",showFooter:!1,minHeight:600,height:800,maxHeight:900,autoResize:!0,columnConfig:{resizable:!0},editConfig:{trigger:"click",mode:"cell"},sortConfig:{sortMethod:({sortList:e})=>{let a={};e.forEach((e=>{a[e.field]=e.order}))}},data:[{id:10001,name:"Test1",nickname:"T1",role:"Develop",sex:"0",sex2:["0"],num:40,num1:40,age:28,flag1:!1,address:"Shenzhen",date3:"",date12:"",date13:"",checkedList:[]},{id:10002,name:"Test2",nickname:"T2",role:"Designer",sex:"1",sex2:["0","1"],num:40,num1:20,age:22,flag1:!1,address:"Guangzhou",date3:"",date12:"",date13:"2020-08-20",checkedList:[]},{id:10003,name:"Test3",nickname:"T3",role:"Test",sex:"0",sex2:["1"],num:4,num1:200,age:32,flag1:!0,address:"Shanghai",date3:"",date12:"2020-09-10",date13:"",checkedList:[]},{id:10004,name:"Test4",nickname:"T4",role:"Designer",sex:"1",sex2:["0"],num:3,num1:30,age:23,flag1:!1,address:"Shenzhen",date3:"",date12:"",date13:"2020-12-04",checkedList:[]},{id:10004,name:"Test5",nickname:"T5",role:"Designer",sex:"1",sex2:["1"],num:40,num1:30,age:26,flag1:!0,address:"Shenzhen",date3:"",date12:"",date13:"",checkedList:[]},{id:10004,name:"Test6",nickname:"T6",role:"Designer",sex:"1",sex2:["1"],num:5,num1:30,age:28,flag1:!1,address:"BeiJing",date3:"",date12:"",date13:"2020-09-04",checkedList:[]},{id:10004,name:"Test7",nickname:"T7",role:"Designer",sex:"1",sex2:["1"],num:40,num1:30,age:30,flag1:!1,address:"BeiJing",date3:"",date12:"",date13:"2020-04-10",checkedList:[]},{id:10004,name:"Test5",nickname:"T5",role:"Designer",sex:"1",sex2:["1"],num:40,num1:30,age:26,flag1:!0,address:"Shenzhen",date3:"",date12:"",date13:"",checkedList:[]},{id:10004,name:"Test6",nickname:"T6",role:"Designer",sex:"1",sex2:["1"],num:5,num1:30,age:28,flag1:!1,address:"BeiJing",date3:"",date12:"",date13:"2020-09-04",checkedList:[]},{id:10004,name:"Test7",nickname:"T7",role:"Designer",sex:"1",sex2:["1"],num:40,num1:30,age:30,flag1:!1,address:"BeiJing",date3:"",date12:"",date13:"2020-04-10",checkedList:[]},{id:10004,name:"Test5",nickname:"T5",role:"Designer",sex:"1",sex2:["1"],num:40,num1:30,age:26,flag1:!0,address:"Shenzhen",date3:"",date12:"",date13:"",checkedList:[]},{id:10004,name:"Test6",nickname:"T6",role:"Designer",sex:"1",sex2:["1"],num:5,num1:30,age:28,flag1:!1,address:"BeiJing",date3:"",date12:"",date13:"2020-09-04",checkedList:[]}],toolbarConfig:{custom:!0,slots:{buttons:"toolbar_buttons",tools:"toolbar_tools"}},columns:[{type:"checkbox",width:60,fixed:"left"},{type:"seq",width:50,fixed:"left"},{field:"name",title:"名字",fixed:"left"},{field:"sex",title:"性别"},{field:"address",title:"地址"},{field:"role",title:"角色"},{field:"age",title:"年龄",sortable:!0},{field:"nickname",title:"昵称"},{field:"operations",title:"操作",fixed:"right",showOverflow:!1,slots:{default:"row-operate"}}]}),F=()=>{VXETable.modal.alert("查询")},G=e=>{w.value},H=({data:e})=>"Y"===e.flag1,Y=()=>{Object.assign(R.modelData,{name:"",nickname:"",role:"",sex:"",age:0,num:0,checkedList:[],flag1:!1,date3:"",address:""}),q.value=null,E.value=!0},$=async()=>{const e=w.value;if(e){if(e.getCheckboxRecords().length>0){const e=await VXETable.modal.confirm("您确定要删除所选记录吗？");VXETable.modal.message({content:`点击了 ${e}`})}else VXETable.modal.message({content:"请至少选择一条记录",status:"info"})}},I=()=>{B.value=!0,setTimeout((()=>{const e=xTable.value;e&&(B.value=!1,E.value=!1,q.value?(VXETable.modal.message({content:"保存成功",status:"success"}),Object.assign(q.value,R)):(VXETable.modal.message({content:"新增成功",status:"success"}),e.insert({...R})))}),500)},W=e=>{},Z=e=>{"chart"==e?(O.value=!0,setTimeout((()=>{X.value=!1,(()=>{let e=l(S.value),a=l(P.value),t={title:{text:"ECharts 入门示例",textStyle:{fontSize:i(16)}},tooltip:{},xAxis:{data:["衬衫","羊毛衫","雪纺衫","裤子","高跟鞋","袜子"]},yAxis:{},series:[{name:"销量",type:"bar",data:[5,20,36,10,10,20]}]};e.setOption(t),a.setOption(t),A.myChart=e,A.myChart2=a})(),d(D.value,A)}),1e3)):(X.value=!0,O.value=!1,t(D.value))};return s((()=>{O.value&&t(D.value)})),(l,t)=>{const d=m("vxe-input"),i=m("vxe-form-item"),o=m("vxe-option"),n=m("vxe-select"),s=m("vxe-button"),j=m("vxe-form"),A=e,K=m("vxe-pager"),Q=m("vxe-grid"),ee=m("vxe-radio"),ae=m("vxe-radio-group"),le=m("vxe-checkbox"),te=m("vxe-checkbox-group"),de=m("vxe-textarea"),ie=m("vxe-modal"),oe=a;return r(),u("div",{class:"app-container",ref_key:"appContainerRef",ref:_},[c("div",U,[p(j,{data:R.condition,collapseStatus:L.value,"onUpdate:collapseStatus":t[0]||(t[0]=e=>L.value=e),onSubmit:F},{default:f((()=>[p(i,{title:"名称",field:"name"},{default:f((({data:e})=>[p(d,{modelValue:e.name,"onUpdate:modelValue":a=>e.name=a,placeholder:"请输入名称",clearable:""},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),p(i,{title:"昵称",field:"nickname"},{default:f((({data:e})=>[p(d,{modelValue:e.nickname,"onUpdate:modelValue":a=>e.nickname=a,placeholder:"请输入昵称",clearable:""},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),p(i,{title:"性别",field:"sex"},{default:f((({data:e})=>[p(n,{modelValue:e.sex,"onUpdate:modelValue":a=>e.sex=a,placeholder:"请选择性别",clearable:""},{default:f((()=>[p(o,{value:"1",label:"女"}),p(o,{value:"2",label:"男"})])),_:2},1032,["modelValue","onUpdate:modelValue"])])),_:1}),p(i,{title:"年龄",field:"age",folding:""},{default:f((({data:e})=>[p(d,{modelValue:e.nickname,"onUpdate:modelValue":a=>e.nickname=a,placeholder:"请输入年龄",clearable:""},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),p(i,{title:"年龄",field:"age",folding:""},{default:f((({data:e})=>[p(d,{modelValue:e.nickname,"onUpdate:modelValue":a=>e.nickname=a,placeholder:"请输入年龄",clearable:""},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),p(i,{title:"年龄",field:"age",folding:""},{default:f((({data:e})=>[p(d,{modelValue:e.nickname,"onUpdate:modelValue":a=>e.nickname=a,placeholder:"请输入年龄",clearable:""},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),p(i,{title:"年龄",field:"age",folding:""},{default:f((({data:e})=>[p(d,{modelValue:e.nickname,"onUpdate:modelValue":a=>e.nickname=a,placeholder:"请输入年龄",clearable:""},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),p(i,{title:"年龄",field:"age",folding:""},{default:f((({data:e})=>[p(d,{modelValue:e.nickname,"onUpdate:modelValue":a=>e.nickname=a,placeholder:"请输入年龄",clearable:""},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),p(i,{title:"年龄",field:"age",folding:""},{default:f((({data:e})=>[p(d,{modelValue:e.nickname,"onUpdate:modelValue":a=>e.nickname=a,placeholder:"请输入年龄",clearable:""},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),p(i,null,{default:f((()=>[p(s,{status:"danger"},{default:f((()=>[g("重置")])),_:1})])),_:1}),p(i,{"collapse-node":""},{default:f((()=>[p(s,{status:"primary"},{default:f((()=>[g("查询")])),_:1})])),_:1})])),_:1},8,["data","collapseStatus"])]),O.value?h((r(),u("div",T,[h(c("div",z,[p(s,{status:"primary",onClick:t[4]||(t[4]=e=>Z("table"))},{default:f((()=>[g("表格")])),_:1}),C],512),[[x,O.value]]),c("div",{class:"chart",ref_key:"gatherChart",ref:D,id:"gatherChart"},[c("div",{ref_key:"chart",ref:S,id:"example-chart"},null,512),c("div",{ref_key:"chart2",ref:P,id:"example-chart2"},null,512)],512)])),[[oe,X.value]]):(r(),v(Q,b({key:0,ref_key:"xGrid",ref:w,class:"my-grid66"},M,{onCheckboxChange:G,onCheckboxAll:G}),{toolbar_buttons:f((()=>[h(c("div",y,[p(s,{status:"primary",onClick:Y},{default:f((()=>[g("新增")])),_:1}),p(s,{status:"danger",onClick:$},{default:f((()=>[g("删除")])),_:1}),p(s,{status:"warning",icon:"vxe-icon-chart-line",circle:"",onClick:t[1]||(t[1]=e=>Z("chart"))})],512),[[x,!O.value]])])),toolbar_tools:f((()=>[])),"row-operate":f((({row:e})=>[p(A,{link:"",type:"primary",onClick:a=>(e=>{Object.assign(R.modelData,e),q.value=e,E.value=!0})(e)},{default:f((()=>[g("编辑")])),_:2},1032,["onClick"]),p(A,{link:"",type:"danger",onClick:a=>(async e=>{if("confirm"===await VXETable.modal.confirm("您确定要删除该数据?")){const a=w.value;a&&a.remove(e)}})(e)},{default:f((()=>[g("删除")])),_:2},1032,["onClick"])])),bottom:f((()=>[])),pager:f((()=>[p(K,{perfect:"","current-page":R.tablePage.currentPage,"onUpdate:currentPage":t[2]||(t[2]=e=>R.tablePage.currentPage=e),"page-size":R.tablePage.pageSize,"onUpdate:pageSize":t[3]||(t[3]=e=>R.tablePage.pageSize=e),total:R.tablePage.totalResult,onPageChange:W},null,8,["current-page","page-size","total"])])),_:1},16)),p(ie,{modelValue:E.value,"onUpdate:modelValue":t[5]||(t[5]=e=>E.value=e),title:q.value?"编辑":"新增",width:"800","min-width":"600","min-height":"300",loading:B.value,resize:"","destroy-on-close":""},{default:f((()=>[p(j,{data:R.modelData,rules:N,"title-align":"right","title-width":"100",onSubmit:I},{default:f((()=>[p(i,{title:"Basic information","title-align":"left","title-width":200,span:24,"title-prefix":{icon:"vxe-icon-comment"}}),p(i,{field:"name",title:"Name",span:12,"item-render":{}},{default:f((({data:e})=>[p(d,{modelValue:e.name,"onUpdate:modelValue":a=>e.name=a,placeholder:"请输入名称"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),p(i,{field:"nickname",title:"Nickname",span:12,"item-render":{}},{default:f((({data:e})=>[p(d,{modelValue:e.name,"onUpdate:modelValue":a=>e.name=a,placeholder:"请输入名称"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),p(i,{field:"role",title:"Role",span:12,"item-render":{}},{default:f((({data:e})=>[p(d,{modelValue:e.name,"onUpdate:modelValue":a=>e.name=a,placeholder:"请输入角色"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),p(i,{field:"sex",title:"Sex",span:12,"item-render":{}},{default:f((({data:e})=>[p(n,{modelValue:e.sex,"onUpdate:modelValue":a=>e.sex=a,transfer:""},{default:f((()=>[(r(!0),u(k,null,V(J.value,(e=>(r(),v(o,{key:e.value,value:e.value,label:e.label},null,8,["value","label"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue"])])),_:1}),p(i,{field:"age",title:"Age",span:12,"item-render":{}},{default:f((({data:e})=>[p(d,{modelValue:e.age,"onUpdate:modelValue":a=>e.age=a,type:"number",placeholder:"请输入年龄"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),p(i,{field:"flag1",title:"是否单身",span:12,"item-render":{}},{default:f((({data:e})=>[p(ae,{modelValue:e.flag1,"onUpdate:modelValue":a=>e.flag1=a},{default:f((()=>[p(ee,{label:"Y",content:"是"}),p(ee,{label:"N",content:"否"})])),_:2},1032,["modelValue","onUpdate:modelValue"])])),_:1}),p(i,{field:"checkedList",title:"可选信息","visible-method":H,span:24,"item-render":{}},{default:f((({data:e})=>[p(te,{modelValue:e.checkedList,"onUpdate:modelValue":a=>e.checkedList=a},{default:f((()=>[p(le,{label:"1",content:"运动、跑步"}),p(le,{label:"2",content:"听音乐"}),p(le,{label:"3",content:"爬山"}),p(le,{label:"4",content:"吃美食"})])),_:2},1032,["modelValue","onUpdate:modelValue"])])),_:1}),p(i,{title:"Other information","title-align":"left","title-width":200,span:24,"title-prefix":{message:"请填写必填项",icon:"vxe-icon-info-circle-fill"}}),p(i,{field:"num",title:"Number",span:12,"item-render":{}},{default:f((({data:e})=>[p(d,{modelValue:e.num,"onUpdate:modelValue":a=>e.num=a,type:"number",placeholder:"请输入数值"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),p(i,{field:"date3",title:"Date",span:12,"item-render":{}},{default:f((({data:e})=>[p(d,{modelValue:e.date3,"onUpdate:modelValue":a=>e.date3=a,type:"date",placeholder:"请选择日期",transfer:""},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),p(i,{field:"address",title:"Date",span:24,"item-render":{},"title-suffix":{message:"提示信息！！",icon:"vxe-icon-question-circle-fill"}},{default:f((({data:e})=>[p(de,{modelValue:e.address,"onUpdate:modelValue":a=>e.address=a,autosize:{minRows:2,maxRows:4}},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),p(i,{align:"center","title-align":"left",span:24},{default:f((()=>[p(s,{type:"submit"},{default:f((()=>[g("提交")])),_:1}),p(s,{type:"reset"},{default:f((()=>[g("重置")])),_:1})])),_:1})])),_:1},8,["data","rules"])])),_:1},8,["modelValue","title","loading"])],512)}}},[["__scopeId","data-v-80a4fd68"]]);export{w as default};
