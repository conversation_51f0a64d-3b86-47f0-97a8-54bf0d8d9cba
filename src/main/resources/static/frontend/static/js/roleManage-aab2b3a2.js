import{b as e,a as t,d as a,f as s,G as l,e as o,c as i,R as r,H as n}from"./element-plus-95e0b914.js";import{F as m,W as c}from"./quasar-df1bac18.js";import{_ as u}from"./pagination-c4d8e88e.js";import"./vue-5bfa3a54.js";import{a as p,h as d,i as f,b as j,j as h,k as g}from"./systemSettingApi-34f94e8e.js";import{n as k}from"./@vicons-f32a0bdb.js";import{c as v}from"./pageUtil-3bb2e07a.js";import{l as b}from"./lodash-6d99edc3.js";import{g as y}from"./api-360ec627.js";import{L as x}from"./ui-385bff4c.js";import{m as w}from"./notification-950a5f80.js";import{_ as V}from"./index-a5df0f75.js";import{h as _,j as D,m as C,az as R,o as z,c as N,x as I,a8 as T,a as U,a9 as S,b as L,aa as M,F as q,k as B,t as F,C as W,D as A}from"./@vue-5e5cdef9.js";import"./lodash-es-ea7deab5.js";import"./@vueuse-5227c686.js";import"./vue-router-6159329f.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./dayjs-67f8ddef.js";import"./@babel-f3c0a00c.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./naive-ui-0ee0b8c3.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./date-fns-tz-0cc073c8.js";import"./proxyUtil-6f30f7ef.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./menuStore-30bf76d3.js";import"./icons-95011f8c.js";import"./@x-ui-vue3-df3ba55b.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./xe-utils-fe99d42a.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";/* empty css                *//* empty css                    */import"./vxe-table-plugin-element-be30dbb6.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";const K=e=>(W("data-v-0608f75a"),e=e(),A(),e),$={class:"q-pa-md"},E={class:"header tw-h-10 fontWhite"},G={colspan:"5"},H=K((()=>U("h6",{class:"tw-my-0 tw-text-2xl text-white text-left"},"角色管理",-1))),P=K((()=>U("div",null,null,-1))),Q=K((()=>U("tr",null,[U("th",{class:"text-center"},"角色ID"),U("th",{class:"text-center"},"角色名称"),U("th",{class:"text-center"},"备注"),U("th",{class:"text-center"},"创建时间"),U("th",{class:"text-center"},"操作")],-1))),X={class:"tw-relative"},Z={class:"text-center",style:{width:"250px"}},J={class:"text-center",style:{width:"250px"}},O={class:"text-center"},Y={class:"text-center"},ee={class:"operation text-center"},te=K((()=>U("tr",null,null,-1))),ae={colspan:"5"},se={class:"dialog-footer"},le={class:"dialog-footer"},oe=V({__name:"roleManage",setup(V){const W=p;let A=_();const K=k;_();const oe=v(ue),ie=_();let re=D({searchValue:"",dialogVisible:!1,dialogTitle:"",form:{},listData:[],authDialogVisible:!1,treeData:[],authID:"",authName:"",authRemark:"",defaultMenusID:[]});function ne(){A&&A.value.validate((async e=>{if(!e)return!1;re.dialogTitle.includes("新增")?await async function(){try{const e=await h([],re.form.roleName,re.form.roleRemark),t=y(e);"00000"===t.status?(w.success("新增操作成功"),re.dialogVisible=!1,ue()):w.error(t.message)}catch(e){w.error("新增操作接口请求出错")}}():await async function(){try{const e=await g(re.form.roleID,re.form.roleName,re.form.roleRemark,re.form.menuList||[]),t=y(e);"00000"===t.status?(w.success("编辑操作成功"),re.dialogVisible=!1,ue()):w.error(t.message)}catch(e){w.error("编辑操作接口请求出错")}}()}))}function me(){ie.value.getCheckedNodes(!1,!0).map((e=>e.id))}const ce=new x;async function ue(){ce.set(!0);try{const e=await f(oe.page,oe.pageSize,re.searchValue),t=y(e);re.listData=t.data.records,oe.total=t.data.total}catch(e){}ce.set(!1)}return C((async()=>{ce.set(!1),await ue(),await async function(){ce.set(!0);try{const e=await j(re.searchValue),t=y(e);re.treeData=t.data}catch(e){}ce.set(!1)}()})),(p,f)=>{const j=a,h=m,g=s,k=l,v=u,x=c,w=o,V=i,_=r,D=n,C=R("skeleton-item"),ce=R("x"),pe=R("g");return z(),N("div",$,[I(x,{class:"tw-rounded-none tw-w-full tw-h-full",separator:"cell"},{default:T((()=>[U("thead",null,[U("tr",E,[U("th",G,[S((z(),N("section",null,[H,I(j,{modelValue:L(re).searchValue,"onUpdate:modelValue":f[0]||(f[0]=e=>L(re).searchValue=e),placeholder:""},{prepend:T((()=>[M(" 角色名称 ")])),_:1},8,["modelValue"]),P,S(I(h,{class:"tw-bg-blue-500 tw-text-white",label:"查询","no-caps":"",dense:"",onClick:f[1]||(f[1]=e=>L(oe).page=1)},null,512),[[C]]),S(I(h,{class:"tw-bg-blue-500 tw-text-white",label:"新增","no-caps":"",dense:"",onClick:f[2]||(f[2]=e=>(re.dialogVisible=!0,re.dialogTitle="新增角色",void(re.form={})))},null,512),[[C]])])),[[ce,[3,5,11,1,1]],[pe,10]])])]),Q]),U("tbody",X,[(z(!0),N(q,null,B(L(re).listData,((a,s)=>(z(),N("tr",{key:s},[U("td",Z,F(a.roleID),1),U("td",J,F(a.roleName),1),U("td",O,F(a.roleRemark),1),U("td",Y,F(a.createTime),1),U("td",ee,[I(k,{effect:"dark",content:"编辑",placement:"top"},{default:T((()=>[I(g,{icon:L(K).PencilSharp,plain:"",onClick:e=>function(e){re.form=b._.cloneDeep(e),re.dialogTitle="编辑角色",re.dialogVisible=!0}(a)},null,8,["icon","onClick"])])),_:2},1024),I(k,{effect:"dark",content:"授权角色权限",placement:"top"},{default:T((()=>[I(g,{icon:L(K).KeySharp,plain:"",onClick:e=>function(e){ie.value?ie.value.setCheckedKeys(e.menuList.filter((e=>e>=100)),!0):re.defaultMenusID=e.menuList.filter((e=>e>=100)),re.authID=e.roleID,re.authName=e.roleName,re.authRemark=e.roleRemark,re.authDialogVisible=!0}(a)},null,8,["icon","onClick"])])),_:2},1024),I(k,{effect:"dark",content:"删除",placement:"top"},{default:T((()=>[I(g,{icon:L(K).TrashSharp,plain:"",onClick:s=>{return l=a.roleID,void e.confirm("是否要删除该角色？(该删除操作不可逆)",{confirmButtonText:"确定",cancelButtonText:"取消"}).then((async()=>{try{const e=await d(l);"00000"===y(e).status&&(t.success("角色删除成功"),ue())}catch(e){}})).catch((()=>{}));var l}},null,8,["icon","onClick"])])),_:2},1024)])])))),128)),te]),U("tfoot",null,[U("tr",null,[U("td",ae,[I(v,{page:L(oe)},null,8,["page"])])])])])),_:1}),I(_,{modelValue:L(re).dialogVisible,"onUpdate:modelValue":f[7]||(f[7]=e=>L(re).dialogVisible=e),title:L(re).dialogTitle,width:"35%","z-index":1e3},{footer:T((()=>[U("span",se,[I(g,{type:"primary",plain:"",onClick:f[5]||(f[5]=e=>L(re).dialogVisible=!1)},{default:T((()=>[M("取消")])),_:1}),I(g,{type:"primary",onClick:f[6]||(f[6]=e=>ne(L(A)))},{default:T((()=>[M("确定")])),_:1})])])),default:T((()=>[I(V,{ref_key:"ruleFormRef",ref:A,model:L(re).form,"label-width":"150px",size:"large",rules:L(W)},{default:T((()=>[I(w,{label:"角色名称",prop:"roleName"},{default:T((()=>[I(j,{modelValue:L(re).form.roleName,"onUpdate:modelValue":f[3]||(f[3]=e=>L(re).form.roleName=e)},null,8,["modelValue"])])),_:1}),I(w,{label:"备注",prop:"roleRemark"},{default:T((()=>[I(j,{modelValue:L(re).form.roleRemark,"onUpdate:modelValue":f[4]||(f[4]=e=>L(re).form.roleRemark=e)},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue","title"]),I(_,{class:"role-auth",modelValue:L(re).authDialogVisible,"onUpdate:modelValue":f[9]||(f[9]=e=>L(re).authDialogVisible=e),title:"用户角色授权",width:"20%","z-index":1e3},{footer:T((()=>[U("span",le,[I(g,{type:"primary",plain:"",onClick:f[8]||(f[8]=e=>L(re).authDialogVisible=!1)},{default:T((()=>[M("取消")])),_:1}),I(g,{type:"primary",onClick:me},{default:T((()=>[M("确定")])),_:1})])])),default:T((()=>[I(D,{ref_key:"treeRef",ref:ie,data:L(re).treeData,"show-checkbox":"","node-key":"id","default-expand-all":"","default-checked-keys":L(re).defaultMenusID},null,8,["data","default-checked-keys"])])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-0608f75a"]]);export{oe as default};
