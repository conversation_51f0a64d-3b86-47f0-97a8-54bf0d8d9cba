import{l as e}from"./lodash-6d99edc3.js";import{d as t}from"./dayjs-d60cc07f.js";function i(i,a,l={electricity:"发电量",income:"收益",electricityEfficiency:"发电效率",yoyRatio:"同比",momRatio:"环比",efficiencyPerHours:"等效小时",plantPrice:"电价"}){const c=[];let n=[];switch(n.push({field:"plantName",label:"站点名称",minWidth:300,fixed:"left",align:"center"}),n.push({field:"type",label:"类型",width:100,fixed:"left",align:"center"}),a.length){case 10:l=e._.omit(l,["efficiencyPerHours"]);break;case 7:case 4:n.push({field:"avg",label:"平均",minWidth:100,fixed:"right",align:"center"}),n.push({field:"total",label:"合计",minWidth:100,fixed:"right",align:"center"}),l=e._.omit(l,["electricityEfficiency"])}i.forEach((t=>{const{plantName:i,plantUid:a,electricityList:n}=t;Object.keys(l).forEach((o=>{const s=o,f=l[o],r=Object.keys(t);let d="-";r.includes("total"+e._.capitalize(s))&&(d=t["total"+e._.capitalize(s)]);let p="-";r.includes("avg"+e._.capitalize(s))&&(p=t["avg"+e._.capitalize(s)]);const h={name:s,plantName:i,plantUid:a,type:f,total:d,avg:p};n.forEach((e=>{const{dataTime:t}=e;h[t]=e[s]})),c.push(h)}))}));const o=new Set;return c.forEach((e=>{Object.keys(e).forEach((e=>{"name"!==e&&"plantName"!==e&&"plantUid"!==e&&"type"!==e&&"total"!==e&&"avg"!==e&&o.add(e)}))})),o.forEach((e=>{const t={19:e.slice(11),10:e.slice(5),7:e},i={field:e,minWidth:100,align:"center",label:t[e.length]};n.push(i)})),n=n.sort(((e,i)=>t(e.field).valueOf()-t(i.field).valueOf())),{rows:c,columns:n}}export{i};
