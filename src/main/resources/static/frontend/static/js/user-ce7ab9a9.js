import{D as e,F as t,C as a,J as l}from"./element-plus-95e0b914.js";import"./vue-5bfa3a54.js";import{g as o}from"./index-dde80e00.js";import{u as s}from"./user-cde6e84b.js";import r from"./addUser-bb6895b7.js";import{i as u}from"./index-a5df0f75.js";import{l as i}from"./lodash-6d99edc3.js";import{h as d,j as n,w as c,as as m,o as p,f,a8 as g,a as b,x as h,c as y,k as v,F as S,aa as P,t as _,a6 as j}from"./@vue-5e5cdef9.js";function w(e){return u({url:"/system/role/getRoleList",method:"post",data:{...e,date:""}})}function V(e){return u({url:"/system/role/addRole",method:"post",data:{...e}})}function x(e){return u({url:"/system/role/editRole",method:"put",data:{...e}})}function z(e){return u({url:`/system/role/deleteRole/${e}`,method:"DELETE"})}function C(){return u({url:"/system/menu/getMenuList",method:"get"})}const N={class:"form"},U={class:"table-btn"},k={class:"flex justify-center items-center"},I={__name:"user",props:{roleId:{type:String}},setup(w){const V=w,x=d(!1),z=n({condition:{roleId:"",userName:"",userPhone:"",userStatus:"",userType:"",projectSpecial:"",projectProps:{value:"id",multiple:!0,label:"projectName",checkStrictly:!1}},tablePage:{totalResult:0,currentPage:1,pageSize:15},modelData:{userTitle:"新增用户",projectProps:{value:"id",label:"projectName",emitPath:!1,checkStrictly:!0},selectOptions:{value:"id",label:"projectName"},projectStatus:!0,userName:"",userStatus:"",projectID:"",userType:"",roleID:"",userPhone:"",userEmail:"",userPassword:""}}),C=d([]),I=n({userStatusOptions:s}),D=n({border:"full",showFooter:!1,loading:!1,columnConfig:{resizable:!0},editConfig:{trigger:"click",mode:"cell"},rowConfig:{},data:[],toolbarConfig:{custom:!1,slots:{buttons:"toolbar_buttons",tools:"toolbar_tools"}},columns:[{field:"seq",type:"seq",width:50},{field:"userName",title:"用户名",width:330},{field:"userType",title:"用户类型",width:180},{field:"userPhone",title:"手机号",width:180},{field:"userStatus",title:"用户状态",width:150,slots:{default:"row-userStatus"}}]}),R=(e,t)=>{},T=({field:e,order:t})=>{z.condition.order=e,null!==t?z.condition.isAsc="asc"===t:(z.condition.order="",z.condition.isAsc=""),q()},E=e=>{z.modelData.title="新增用户",x.value=!0},L=e=>{C.value=e},O=async()=>{var e;await(e={roleId:z.condition.roleId,userIdList:C.value},u({url:"/system/user/assignRoles",method:"post",data:{...e}})),x.value=!1,q()},q=async(e=1,t=10)=>{D.loading=!0,z.tablePage.currentPage=e,z.tablePage.pageSize=t,z.condition.projectSpecial=i._.uniq(i._.flattenDeep(z.condition.projectSpecial));const a=await o({...z.condition,...z.tablePage});D.loading=!1,D.data=a.data.records,z.tablePage.totalResult=a.data.total},A=e=>{q(e.currentPage,e.pageSize)};return c((()=>V.roleId),((e,t)=>{z.condition.roleId=e,q()}),{immediate:!0}),(o,s)=>{const u=m("vxe-input"),i=m("vxe-form-item"),d=e,n=t,c=m("vxe-button"),w=m("vxe-form"),V=a,C=m("vxe-pager"),F=m("vxe-grid"),M=m("vxe-modal"),G=l;return p(),f(G,null,{default:g((()=>[b("div",N,[h(w,{data:z.condition},{default:g((()=>[h(i,{field:"userName",title:"用户名"},{default:g((({data:e})=>[h(u,{modelValue:e.userName,"onUpdate:modelValue":t=>e.userName=t,placeholder:"请输入用户名"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),h(i,{field:"userName",title:"手机号"},{default:g((({data:e})=>[h(u,{modelValue:e.userPhone,"onUpdate:modelValue":t=>e.userPhone=t,placeholder:"请输入手机号"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),h(i,{field:"userName",title:"用户状态"},{default:g((({data:e})=>[h(n,{modelValue:e.userStatus,"onUpdate:modelValue":t=>e.userStatus=t,class:"tw-w-full",clearable:"",style:{width:"180px !important"},placeholder:"用户状态"},{default:g((()=>[(p(!0),y(S,null,v(I.userStatusOptions,(e=>(p(),f(d,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue"])])),_:1}),h(i,null,{default:g((()=>[h(c,{status:"primary",onClick:s[0]||(s[0]=e=>q(1))},{default:g((()=>[P("查询")])),_:1})])),_:1})])),_:1},8,["data"])]),h(F,j({id:"userAllocation",ref:"xGrid","scroll-y":{enabled:!1},class:"my-grid66"},D,{onCustom:R,onSortChange:T}),{toolbar_buttons:g((()=>[])),toolbar_tools:g((()=>[b("div",U,[h(c,{status:"primary",onClick:E},{default:g((()=>[P("新增")])),_:1})])])),top:g((()=>[])),"row-userStatus":g((({row:e})=>[b("div",k,[b("span",null,[h(V,{type:"启用"===e.userStatus?"success":""},{default:g((()=>[P(_(e.userStatus),1)])),_:2},1032,["type"])])])])),bottom:g((()=>[])),pager:g((()=>[h(C,{"current-page":z.tablePage.currentPage,"onUpdate:currentPage":s[1]||(s[1]=e=>z.tablePage.currentPage=e),"page-size":z.tablePage.pageSize,"onUpdate:pageSize":s[2]||(s[2]=e=>z.tablePage.pageSize=e),"page-sizes":[10,15,20],total:z.tablePage.totalResult,perfect:"",onPageChange:A},null,8,["current-page","page-size","total"])])),_:1},16),h(M,{modelValue:x.value,"onUpdate:modelValue":s[4]||(s[4]=e=>x.value=e),position:{top:0},title:z.modelData.title,"destroy-on-close":"",escClosable:"",height:"800","min-width":"1000",resize:"","show-footer":"",width:"1000"},{default:g((()=>[h(r,{onSelectEmit:L})])),footer:g((()=>[h(c,{type:"submit",onClick:O},{default:g((()=>[P("提交")])),_:1}),h(c,{type:"cancel",onClick:s[3]||(s[3]=e=>x.value=!1)},{default:g((()=>[P("取消")])),_:1})])),_:1},8,["modelValue","title"])])),_:1})}}},D=Object.freeze(Object.defineProperty({__proto__:null,default:I},Symbol.toStringTag,{value:"Module"}));export{I as U,V as a,z as b,w as c,C as g,x as r,D as u};
