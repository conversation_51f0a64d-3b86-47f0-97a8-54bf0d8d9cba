import{f as e,B as a}from"./element-plus-95e0b914.js";import"./vue-5bfa3a54.js";import{i as t,_ as l,g as s,l as i,b as n}from"./index-a5df0f75.js";import{s as r}from"./pinia-c7531a5f.js";import{m as o,J as c}from"./vue3-drr-grid-layout-3f9cba0a.js";import{f as u,c as m,d,i as p}from"./chartResize-3e3d11d7.js";import{X as v}from"./xe-utils-fe99d42a.js";import{B as f}from"./bignumber.js-a537a5ca.js";import{a as h}from"./vxe-table-3a25f2d2.js";import{s as g}from"./sass-ac65759c.js";import"./echarts-f30da64f.js";import{V as x}from"./zrender-c058db04.js";import{g as y,a as w,b,c as C,d as j}from"./index-092b8780.js";import{g as k}from"./imgImport-cfe60b78.js";import{h as E,j as N,w as P,e as S,m as z,v as I,as as _,o as q,c as D,b as L,a9 as M,l as W,a as O,F as $,k as A,y as T,t as R,aa as J,x as F,a8 as U,f as V,a6 as B,C as G,D as H}from"./@vue-5e5cdef9.js";import"./lodash-es-ea7deab5.js";import"./@vueuse-5227c686.js";import"./vue-router-6159329f.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./dayjs-67f8ddef.js";import"./@babel-f3c0a00c.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./nprogress-e136a1b4.js";import"./quasar-df1bac18.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-be30dbb6.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./lodash-6d99edc3.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./vue-demi-01e7384c.js";import"./dom-zindex-5f662ad1.js";import"./tslib-a4e99503.js";import"./element-resize-detector-0d37a2ab.js";import"./batch-processor-06abf2b4.js";import"./immutable-901ee85f.js";import"./exceljs-b3a0e81d.js";const K={graphic:[{type:"text",left:"center",top:"42%",style:{text:"98%",textAlign:"center",fill:"#000",fontSize:u(28)}},{type:"text",left:"center",top:"56%",style:{text:"电站正常运行率",textAlign:"center",fill:"#999",fontSize:u(16)}}],series:[{type:"pie",radius:["60%","48%"],center:["50%","50%"],hoverAnimation:!1,animationEasing:"cubicOut",labelLine:{normal:{show:!1}},itemStyle:{normal:{}},data:[{value:g,itemStyle:{normal:{color:"#9eea97"}}},{value:null,itemStyle:{normal:{color:"#eaebed"}}}]}]},X={title:{show:!0,text:"",textStyle:{fontSize:u(14)}},legend:{show:!0,bottom:"2%",selected:{}},tooltip:{show:!0,trigger:"axis",confine:!0,formatter:null},grid:{left:"15%",right:"10%",top:"22%",bottom:"20%"},xAxis:{data:[]},yAxis:[{show:!0,name:"MWh",type:"value",axisLine:{show:!0},axisTick:{show:!1},splitLine:{show:!0,lineStyle:{color:["rgba(77, 182, 232, .6)"],type:"dashed"}}}],series:[]},Z={name:"",type:"bar",data:[],itemStyle:{},label:{show:!1,position:"top"},color:new x(0,1,0,0,[{offset:0,color:"#ffdfa7"},{offset:1,color:"#ffb638"}])},Q={name:"",type:"line",data:[],smooth:!0,itemStyle:{color:"#4db6e8"}},Y=e=>(G("data-v-4bab75a8"),e=e(),H(),e),ee={key:0,class:"screen-loading u-wh-full","element-loading-text":"正在获取数据"},ae={class:"u-wh-full info-statics u-flex-column u-border"},te=Y((()=>O("div",{class:"title regular-title u-flex-y-center"},[O("p",{class:"square"}),O("p",null,"整体概况")],-1))),le={class:"info-statics-content u-flex-1 w-full"},se=["onClick"],ie={class:"card-icon u-flex-center-no"},ne=["src","alt"],re={class:"u-flex-1 u-flex-column justify-center"},oe={class:"text-left small-title"},ce={class:"text-left digtal"},ue={class:"u-wh-full station-statics u-flex-column u-border"},me=Y((()=>O("div",{class:"title regular-title u-flex-y-center"},[O("p",{class:"square"}),O("p",null,"电站实时状态")],-1))),de={class:"station-statics-content u-flex-1 w-full u-flex-center-no"},pe=Y((()=>O("figure",{id:"plantProgressChart",class:"progress u-flex-1 h-full"},null,-1))),ve={class:"index u-flex-1 station-statics-content-item"},fe=["onClick"],he={class:"w-full u-flex-center-no u-gap-6"},ge=["xlink:href"],xe=["src"],ye={class:"small-title text-center"},we={class:"digtal text-center u-flex-center-no"},be={class:"u-wh-full elec-chart u-flex-column u-border"},Ce=Y((()=>O("div",{class:"title regular-title u-flex-y-center"},[O("p",{class:"square"}),O("p",null,"近期发电")],-1))),je={class:"elec-chart-content u-flex-1 u-flex-center-no u-gap-18"},ke={class:"elec-chart-content-chart u-flex-1 h-full u-flex-column"},Ee={class:"u-flex-center title1"},Ne=Y((()=>O("span",null,"今日发电",-1))),Pe=Y((()=>O("figure",{class:"u-flex-1 w-full",id:"dayElecChart"},null,-1))),Se=Y((()=>O("div",{class:"elec-chart-content-chart u-flex-1 h-full u-flex-column"},[O("p",{class:"text-center title0"},"近6月发电趋势"),O("figure",{class:"u-flex-1 w-full",id:"monthElecChart"})],-1))),ze={class:"u-wh-full inverter-statics u-flex-column u-border"},Ie=Y((()=>O("div",{class:"title regular-title u-flex-y-center"},[O("p",{class:"square"}),O("p",null,"逆变器实时状态")],-1))),_e={class:"inverter-statics-content u-flex-1 w-full u-flex-center-no"},qe=Y((()=>O("figure",{id:"inverterProgressChart",class:"progress u-flex-1 h-full"},null,-1))),De={class:"index u-flex-1 inverter-statics-content-item"},Le=["onClick"],Me={class:"w-full u-flex-center-no u-gap-6"},We=["xlink:href"],Oe=["src"],$e={class:"small-title text-center"},Ae={class:"digtal text-center u-flex-center-no"},Te={class:"grid-layout u-wh-full"},Re={class:"grid-layout-box u-flex-center"},Je={class:"u-wh-full u-flex-center"},Fe={class:"grid-save-btns u-flex-y-center justify-end"},Ue=l({__name:"index",setup(l){const u=s(),{projectId:g}=r(u),x=E();E();const G=E(),H=E(!0);let Y=null;const Ue=N({loading:!1,rowHeight:400,layout:[{x:0,y:0,w:1,h:1,i:1,name:"infoStatics",template:'\n<div class="info-statics u-flex-column shadow-md">\n<div class="title regular-title u-flex-y-center">\n  <p class="square"></p>\n  <p>整体概况</p>\n</div>\n<div class="info-statics-content u-flex-1 w-full">\n  <p v-for="(value, key) in infoStatics" :key="key" class="u-flex-column justify-center">\n    <span class="text-center digtal">{{ value.value }}</span>\n    <span class="text-center small-title">{{ value.label }} {{ value.unit }}</span>\n  </p>\n</div>\n</div>\n'},{x:1,y:0,w:1,h:1,i:2,name:"plantStatus",template:'\n<div class="station-statics u-flex-column shadow-md">\n<div class="title regular-title u-flex-y-center">\n  <p class="square"></p>\n  <p>电站状态统计</p>\n</div>\n<div class="station-statics-content u-flex-1 w-full u-flex-center-no">\n  <figure id="plantProgressChart" class="progress u-flex-1 h-full"></figure>\n  <p class="index u-flex-1 station-statics-content-item">\n    <span v-for="(value, key) in plantStatus" :key="key" class="u-flex-y-center u-gap-2">\n      <i class="dot" :class="value.color"></i>\n      <i class="small-title">{{ value.label }}</i>\n      <i class="flex-1 text-right">{{ value.value }}</i>\n    </span>\n  </p>\n</div>\n</div>\n'},{x:0,y:1,w:1,h:1,i:3,name:"plantElec",template:'\n<div class="elec-chart u-flex-column shadow-md">\n              <div class="title regular-title u-flex-y-center">\n                <p class="square"></p>\n                <p>发电趋势</p>\n              </div>\n              <div class="elec-chart-content u-flex-1 u-flex-center-no">\n                <div class="u-flex-1 h-full u-flex-column">\n                  <p class="small-title">今日发电趋势</p>\n                  <figure class="u-flex-1 w-full" id="dayElecChart"></figure>\n                </div>\n                <div class="u-flex-1 h-full u-flex-column">\n                  <p class="small-title">近6月发电趋势</p>\n                  <figure class="u-flex-1 w-full" id="monthElecChart"></figure>\n                </div>\n              </div>\n            </div>\n'},{x:1,y:1,w:1,h:1,i:4,name:"inverterStatus",template:'\n<div class="inverter-statics u-flex-column shadow-md">\n              <div class="title regular-title u-flex-y-center">\n                <p class="square"></p>\n                <p>逆变器状态统计</p>\n              </div>\n              <div class="inverter-statics-content u-flex-1 w-full u-flex-center-no">\n                <figure id="inverterProgressChart" class="progress u-flex-1 h-full"></figure>\n                <p class="index u-flex-1 inverter-statics-content-item">\n                  <span v-for="(value, key) in inverterStatus" :key="key" class="u-flex-y-center u-gap-2">\n                    <i class="dot" :class="value.color"></i>\n                    <i class="small-title">{{ value.label }}</i>\n                    <i class="flex-1 text-right">{{ value.value }}</i>\n                  </span>\n                </p>\n              </div>\n            </div>\n'}]}),Ve=N({visible:!1,submitLoading:!1,layout:[{x:0,y:0,w:1,h:1,i:1,name:"infoStatics",text:"电站概况"},{x:1,y:0,w:1,h:1,i:2,name:"plantStatus",text:"电站状态"},{x:0,y:1,w:1,h:1,i:3,name:"plantElec",text:"发电趋势"},{x:1,y:1,w:1,h:1,i:4,name:"inverterStatus",text:"逆变器状态"}]}),Be=E(""),Ge=N({totalNum:{label:"电站数量",value:0,unit:null,icon:"icon-Union",bgColor:"bg-fff4e7",color:"font-ffa828",picName:"plant_num.png",event:()=>aa()},InverterNum:{label:"逆变器数",value:0,unit:null,icon:"icon-nibianqi2",bgColor:"bg-eaf4ff",color:"font-3a8fea",picName:"inverter_num.png",event:()=>ta()},plantCapacity:{label:"装机容量",value:0,icon:"icon-zhuangjirongliang-mianicon",unit:"(KWp)",bgColor:"bg-eefbff",picName:"capacity.png",color:"font-0facd5"},totalElectricity:{label:"总发电量",value:0,icon:"icon-leijifadianliang",unit:"(kWh)",bgColor:"bg-ebfefd",picName:"total_elec.png",color:"font-36cfc9"}}),He=N({visible:!1,filterText:"",sel:"",selLabel:"",data:[]}),Ke=N({offlineNum:{label:"光精灵离线",value:0,color:"color-outline",icon:"#icon-RemoveFilled",event:()=>aa(0)},alarmNum:{label:"告警",value:0,color:"color-alarm",icon:"#icon-jinggaozhuangtai",event:()=>ea("")},inverterShutdownNum:{label:"夜间模式",value:0,color:"color-night",icon:"#icon-a-zu2837",event:()=>aa(4)},selfCheckNum:{label:"自检提示",value:0,color:"color-self",icon:"#icon-jinggaozhuangtai",event:()=>aa(5)}}),Xe=N({offlineNum:{label:"离线",value:0,color:"color-outline",icon:"#icon-RemoveFilled",event:()=>ta("0")},alarmNum:{label:"告警",value:0,color:"color-alarm",icon:"icon-alarmNew",event:()=>ta("1")},inverterShutdownNum:{label:"夜间模式",value:0,color:"color-night",icon:"#icon-a-zu2837",event:()=>ta("4")},selfCheckNum:{label:"自检提示",value:0,color:"color-self",icon:"#icon-jinggaozhuangtai",event:()=>ta()}}),Ze={plantProgressChart:null,inverterProgressChart:null,dayElecChart:null,monthElecChart:null},Qe=N({plantProgressChart:null,inverterProgressChart:null,dayElecChart:null,monthElecChart:null}),Ye=N({plantProgressChart:0,inverterProgressChart:0,dayElecChart:{xData:[],data:[]},monthElecChart:{xData:[],data:[]}}),ea=e=>{i.push({path:"/plantManage/alarmManage",query:{deviceType:e}})},aa=e=>{i.push({path:"statementManage/plant",query:{status:e}})},ta=e=>{i.push({path:"plantManage/alarmManage",query:{multiInverterStatus:e}})},la=(e,a)=>{let t;switch(Qe[e]=!0,e){case"dayElecChart":t=v.clone(X,!0);let e=v.clone(Q,!0);e.data=Ye.dayElecChart.data,e.name="发电量(MWh)",t.series[0]=e,t.xAxis.data=Ye.dayElecChart.xData;break;case"monthElecChart":t=v.clone(X,!0);let a=v.clone(Z,!0);a.data=Ye.monthElecChart.data,a.name="发电量(MWh)",t.series[0]=a,t.yAxis[0].splitLine.lineStyle.color=["rgba(248, 182, 72, .6)"],t.xAxis.data=Ye.monthElecChart.xData;break;case"plantProgressChart":t=v.clone(K,!0),t.graphic[0].style.text=`${new f(Ye.plantProgressChart).times(100).decimalPlaces(2).toNumber()}%`;let l=new f(Ye.plantProgressChart).times(100).decimalPlaces(2).toNumber();t.series[0].data[0].value=l,t.series[0].data[1].value=new f(100).minus(l).decimalPlaces(2).toNumber();break;case"inverterProgressChart":t=v.clone(K,!0),t.graphic[0].style.text=`${new f(Ye.inverterProgressChart).times(100).decimalPlaces(2).toNumber()}%`,t.graphic[1].style.text="逆变器正常运行率";let s=new f(Ye.inverterProgressChart).times(100).decimalPlaces(2).toNumber();t.series[0].data[0].value=s,t.series[0].data[1].value=new f(100).minus(s).decimalPlaces(2).toNumber()}a[e]&&a[e].dispose(),a[e]=p(e,t),Qe[e]=!1,m(x.value,a)},sa=async e=>{try{const t=await(a=e,y(a));if("00000"==t.status){for(let e in Ke)t.data[e]&&(Ke[e].value=t.data[e]);Ge.totalNum.value=t.data.totalNum,Ye.plantProgressChart=t.data.normalRate,Ue.loading&&la("plantProgressChart",Ze)}}catch(t){}var a},ia=async e=>{try{const t=await(a=e,w(a));if("00000"==t.status){let e=t.data.plantCapacity;e.length>9?(Ge.plantCapacity.value=new f(e).div(1e3).decimalPlaces(2).toString(),Ge.plantCapacity.unit="(MWp)"):(Ge.plantCapacity.value=new f(e).decimalPlaces(2).toString(),Ge.plantCapacity.unit="(kWp)");let a=t.data.totalElectricity;a.length>9?(Ge.totalElectricity.value=new f(a).div(1e3).decimalPlaces(2).toString(),Ge.totalElectricity.unit="(MWh)"):(Ge.totalElectricity.value=new f(a).decimalPlaces(2).toString(),Ge.totalElectricity.unit="(kWh)"),Be.value=`${t.data.todayElectricity}kWh`}}catch(t){}var a},na=async e=>{try{const t=await(a=e,b(a));if("00000"==t.status){for(let e in Xe)t.data[e]?Xe[e].value=t.data[e]:Xe[e].value="NaN";Ge.InverterNum.value=t.data.totalNum,Ye.inverterProgressChart=t.data.normalRate,Ue.loading&&la("inverterProgressChart",Ze)}}catch(t){}var a},ra=async e=>{try{const t=await(a=e,C(a));"00000"==t.status&&(Ye.monthElecChart={xData:[],data:[]},t.data.forEach((e=>{Ye.monthElecChart.xData.push(`${e.collectDate.substr(5,2)}月`),Ye.monthElecChart.data.push(new f(e.electricity).div(1e3).decimalPlaces(2).toNumber())})),Ue.loading&&la("monthElecChart",Ze))}catch(t){}var a},oa=async e=>{try{const t=await(a=e,j(a));"00000"==t.status&&(Ye.dayElecChart={xData:[],data:[]},t.data.forEach((e=>{Ye.dayElecChart.xData.push(`${e.collectDate.substr(11,5)}时`),Ye.dayElecChart.data.push(new f(e.electricity).div(1e3).decimalPlaces(2).toNumber())})),Ue.loading&&la("dayElecChart",Ze))}catch(t){}var a},ca=()=>{Ve.visible=!1},ua=async()=>{Ve.submitLoading=!0;try{const a=await(e=JSON.stringify(Ve.layout),t({url:"/system/user/layout",method:"put",data:e+""}));Ve.submitLoading=!1,"00000"==a.status?(localStorage.setItem("plantOverviewGrid",encodeURIComponent(JSON.stringify(Ve.layout))),Ve.visible=!1,Ue.loading=!1,H.value=!0,ma()):h.modal.message({content:"保存布局异常，请稍后再试",status:"info"})}catch(a){Ve.submitLoading=!1}var e},ma=()=>{let e=JSON.parse(decodeURIComponent(localStorage.getItem("plantOverviewGrid")));e.layout&&(e=e.layout),Ve.layout=e;let a={};e.forEach((e=>{a[e.name]={x:e.x,y:e.y,w:e.w,h:e.h}}));for(let t in Ue.layout)Ue.layout[t]={...Ue.layout[t],...a[Ue.layout[t].name]};setTimeout((()=>{Ue.loading=!0,H.value=!1}),1e3)};P((()=>Ue.loading),(e=>{e&&setTimeout((async()=>{await la("dayElecChart",Ze),await la("monthElecChart",Ze),await la("plantProgressChart",Ze),await la("inverterProgressChart",Ze)}),300)})),P((()=>He.filterText),((e,a)=>{G.value.filter(e)})),P(g,((e,a)=>{He.sel=e,(async()=>{H.value=!0,Y&&(clearInterval(Y),Y=null),await sa(He.sel),await ia(He.sel),await na(He.sel),await ra(He.sel),await oa(He.sel),Y||(Y=setInterval((async()=>{await sa(He.sel),await ia(He.sel),await na(He.sel),await ra(He.sel),await oa(He.sel)}),3e5)),H.value=!1})()}));const da=n(),{isFullScreen:pa}=r(da),va=S((()=>!da.sidebar.opened));P(va,(e=>{m(x.value,Ze)}));let fa=null;return P(pa,(e=>{H.value=!0,Ue.loading=!1,fa&&(clearTimeout(fa),fa=null),fa=setTimeout((()=>{Ue.loading=!0,H.value=!1}),300)})),z((async()=>{u.getProjectId(),He.sel=u.projectId,Ue.loading=!0,H.value=!0,await sa(He.sel),await ia(He.sel),await na(He.sel),await ra(He.sel),await oa(He.sel),Ue.loading=!1,H.value=!1,Y=setInterval((async()=>{await sa(He.sel),await ia(He.sel),await na(He.sel),await ra(He.sel),await oa(He.sel)}),3e5)})),I((()=>{d(x.value),Y&&(clearInterval(Y),Y=null)})),(t,l)=>{const s=e,i=_("vxe-modal"),n=a;return q(),D("div",{class:"app-container plantOverview-margin u-flex-column",ref_key:"overviewRef",ref:x},[L(H)?M((q(),D("div",ee,null,512)),[[n,L(H)]]):W("",!0),O("div",ae,[te,O("div",le,[(q(!0),D($,null,A(L(Ge),((e,a)=>(q(),D("p",{key:a,class:T(["card-style",e.event?`cursor-pointer ${e.bgColor}`:`${e.bgColor}`]),onClick:e.event},[O("span",ie,[O("img",{src:L(k)("plantOverview",e.picName),class:"card-icon-img",alt:e.picName},null,8,ne)]),O("span",re,[O("i",oe,R(e.label)+" "+R(e.unit),1),O("i",ce,R(e.value),1)])],10,se)))),128))])]),O("div",ue,[me,O("div",de,[pe,O("div",ve,[(q(!0),D($,null,A(L(Ke),((e,a)=>(q(),D("p",{key:a,class:T(["card-style2",`card-${e.color}`]),onClick:e.event},[O("span",he,["告警"!==e.label?(q(),D("svg",{key:0,class:T(["icon status-icon",e.color]),"aria-hidden":"true"},[O("use",{"xlink:href":e.icon},null,8,ge)],2)):(q(),D("img",{key:1,src:L(k)("plantOverview","icon_alarm.png"),class:"icon-alarmNew",alt:"icon_alarm"},null,8,xe)),O("i",ye,R(e.label),1)]),O("span",we,R(e.value),1)],10,fe)))),128))])])]),O("div",be,[Ce,O("div",je,[O("div",ke,[O("p",Ee,[Ne,J("  "),O("span",null,R(L(Be)),1)]),Pe]),Se])]),O("div",ze,[Ie,O("div",_e,[qe,O("div",De,[(q(!0),D($,null,A(L(Xe),((e,a)=>(q(),D("p",{key:a,class:T(["card-style2",`card-${e.color}`]),onClick:e.event},[O("span",Me,["告警"!==e.label?(q(),D("svg",{key:0,class:T(["icon status-icon",e.color]),"aria-hidden":"true"},[O("use",{"xlink:href":e.icon},null,8,We)],2)):(q(),D("img",{key:1,src:L(k)("plantOverview","icon_alarm.png"),class:"icon-alarmNew",alt:"icon_alarm"},null,8,Oe)),O("i",$e,R(e.label),1)]),O("span",Ae,R(e.value),1)],10,Le)))),128))])])]),F(i,{modelValue:L(Ve).visible,"onUpdate:modelValue":l[1]||(l[1]=e=>L(Ve).visible=e),title:"自定义布局",width:"1200","min-width":"400","min-height":"400",height:"600",loading:L(Ve).submitLoading,resize:"","destroy-on-close":"",zIndex:2008},{default:U((()=>[O("div",Te,[O("div",Re,[F(L(o),{layout:L(Ve).layout,"onUpdate:layout":l[0]||(l[0]=e=>L(Ve).layout=e),"col-num":2,"row-height":200,"max-rows":10,"is-resizable":!1,style:{width:"100%",height:"100%"}},{default:U((({gridItemProps:e})=>[(q(!0),D($,null,A(L(Ve).layout,(a=>(q(),V(L(c),B({key:a.i},e,{x:a.x,y:a.y,w:a.w,h:a.h,i:a.i}),{default:U((()=>[O("div",Je,[O("p",null,R(a.text),1)])])),_:2},1040,["x","y","w","h","i"])))),128))])),_:1},8,["layout"])]),O("div",Fe,[F(s,{onClick:ca},{default:U((()=>[J("取消")])),_:1}),F(s,{type:"primary",onClick:ua},{default:U((()=>[J("保存")])),_:1})])])])),_:1},8,["modelValue","loading"])],512)}}},[["__scopeId","data-v-4bab75a8"]]);export{Ue as default};
