import{_ as t}from"./MyTable-15b6ab92.js";import{_ as e}from"./pagination-c4d8e88e.js";import{_ as s}from"./MyForm-f1cf6891.js";import{c as o,_ as r}from"./dateUtil-5e0180b5.js";import"./vue-5bfa3a54.js";import{f as a}from"./formatTableData-0442e1d7.js";import{d as i}from"./dayjs-67f8ddef.js";import{c as p}from"./pageUtil-3bb2e07a.js";import{c as m}from"./getSetObj-f4228515.js";import{f as l}from"./formUtil-7f692cbf.js";import{e as j}from"./statisticReportApi-8df5b5f8.js";import{e as n}from"./exportFile-75030642.js";import{_ as u}from"./index-a5df0f75.js";import{e as c,h as d,j as v,m as f,o as w,c as b,x as g,a8 as y,b as x,C as T,D as k,a as _}from"./@vue-5e5cdef9.js";import"./quasar-df1bac18.js";import"./naive-ui-0ee0b8c3.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./lodash-es-ea7deab5.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./@babel-f3c0a00c.js";import"./date-fns-tz-0cc073c8.js";import"./async-validator-cf877c1f.js";import"./@vueuse-5227c686.js";import"./vue-router-6159329f.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./lodash-6d99edc3.js";import"./@vicons-f32a0bdb.js";import"./notification-950a5f80.js";import"./proxyUtil-6f30f7ef.js";import"./menuStore-30bf76d3.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./icons-95011f8c.js";import"./api-360ec627.js";import"./element-plus-95e0b914.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./xe-utils-fe99d42a.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-be30dbb6.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";const N=a({meterID:"电厂(交易对象)编号",plantName:"站点名称",userName:"所属用户",plantCapacity:"电站容量",totalElectricity:"总发电量",address:"地址",status:"电站状态",createTime:"创建时间",updateTime:"更新时间",inverterNum:"逆变器数量",electricity:"发电量",alarmNum:"告警数量",equivalentUseHour:"等效小时",electricityPrice:"电价",income:"收入",totalReduceCo2:"二氧化碳减排",totalPlantTreeNum:"累计植树",offlineTime:"离线时长"}),h=t=>(T("data-v-358f16ae"),t=t(),k(),t),z={class:"tw-h-full tw-w-full tw-p-4"},S=h((()=>_("div",{class:"tw-text-2xl tw-ml-2 tw-mb-2"},null,-1))),U=h((()=>_("article",{class:"tw-mt-1 tw-mr-1"},"发电量:",-1))),D=h((()=>_("article",{class:"tw-mr-1"},"建站时间:",-1))),q=u({__name:"plantStatistics",setup(a){const u=o(2,"日月年"),T=o([i("2018-01-01").valueOf(),Date.now()],"日");u.startDate=c((()=>T.value[0])),u.endDate=c((()=>T.value[1]));let k=d([]);const _=p(q),h=v([{formType:"input",label:"站点名称",prop:"plantName",value:"",class:"tw-w-[220px]"},{formType:"input",label:"输入地区",prop:"address",class:"tw-w-[220px]",value:""},{formType:"slot",label:"",prop:"elecTime",value:c(m(u))},{formType:"slot",label:"",prop:"createTime",value:c(m(T))},{formType:"button",label:"查询",value:!1,prop:"check",invoke:q},{formType:"space"},{formType:"button",label:"重置",value:!1,prop:"reset",invoke:()=>{_.page=1,_.pageSize=10}},{formType:"button",label:"导出",value:!1,prop:"export",invoke:async function(t=l.getValue(h)){const e=await exportPlantStatisticsInfo(t.plantName,t.address,N.map((t=>t.field)),...T.date,...u.date),s=await l.exportFile(e,h,"export");n(s,"电站统计报表")}}]);async function q(t=l.getValue(h),e,s){const o=await j(_.page,_.pageSize,...T.date,...u.date,t.plantName,t.address);l.tableResponse(o,k,_,h,"check",e,s)}return d(!1),f((async()=>{await q()})),(o,a)=>{const i=r,p=s,m=e,l=t;return w(),b("div",z,[g(l,{rowKey:"plantUid",rows:x(k),columns:x(N)},{top:y((()=>[S,g(p,{page:x(_),title:"",formList:x(h)},{elecTime:y((()=>[U,g(i,{date:x(u),class:"tw-w-[440px] tw-mr-2"},null,8,["date"])])),createTime:y((()=>[D,g(i,{date:x(T),class:"tw-w-[300px] tw-mr-2"},null,8,["date"])])),_:1},8,["page","formList"])])),bottom:y((()=>[g(m,{page:x(_)},null,8,["page"])])),_:1},8,["rows","columns"])])}}},[["__scopeId","data-v-358f16ae"]]);export{q as default};
