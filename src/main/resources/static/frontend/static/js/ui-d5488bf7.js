import"./@x-ui-vue3-df3ba55b.js";import"./vue-5bfa3a54.js";import{C as t}from"./@vueuse-af86c621.js";import{j as s}from"./@vue-5e5cdef9.js";class o{constructor(o){this.tog=t(o),this.toggle=this.tog.at(1),this.value=s({loading:this.tog.at(0),loadingClass:"loadingClass",loadingStyle:"box-shadow: 0 0 20px -14px #000",class:"loadingItem",style:"cursor: pointer"})}set(t){t.value&&(t=t.value),this.value.loading=t}get(){return this.tog.at(0).value}}export{o as L};
