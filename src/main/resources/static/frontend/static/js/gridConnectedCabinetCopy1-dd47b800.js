import{_ as s,u as e,a as t}from"./index-a5df0f75.js";import{_ as i}from"./index-04837d66.js";import{X as l}from"./element-plus-95e0b914.js";import"./vue-5bfa3a54.js";import{g as a}from"./index-05697651.js";import{g as n}from"./imgImport-cfe60b78.js";import{f as r}from"./fabric-8dd10b04.js";import{d as c}from"./dayjs-67f8ddef.js";import{x as o}from"./xe-utils-fe99d42a.js";import{d as m}from"./@vueuse-5227c686.js";import{h as p,j as u,e as f,m as d,v as g,o as v,c as j,a as h,t as b,b as x,f as k,l as y,x as w,F as z,k as _,y as C,C as N,D}from"./@vue-5e5cdef9.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./vue-router-6159329f.js";import"./bignumber.js-a537a5ca.js";import"./js-cookie-8253c38e.js";import"./axios-84f1a956.js";import"./lodash-es-ea7deab5.js";import"./spark-md5-022b35d0.js";import"./@babel-f3c0a00c.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";import"./quasar-df1bac18.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./@element-plus-4c34063a.js";import"./vxe-table-plugin-element-be30dbb6.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./lodash-6d99edc3.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./screenfull-c82f2093.js";import"./chartResize-3e3d11d7.js";import"./element-resize-detector-0d37a2ab.js";import"./batch-processor-06abf2b4.js";const S=s=>(N("data-v-4f1e4de8"),s=s(),D(),s),I={class:"header"},R={class:"date font-small-text-size2 normal regular-title"},H={class:"title"},Y={class:"main-title font-title-size text"},L={class:"screen-full"},M={class:"left-sensor"},U={class:"item-title-bg font-title-size"},W=["src"],q={class:"sensor-box"},A={key:0,class:"circle-img"},O=["src"],P=["src"],V={key:1,class:"circle-img"},X=["src"],B={class:"u-flex-column"},F={class:"font-small-text-size2"},G={key:0},J={key:1},T={class:"smokeValue font-small-text-size1"},Z={class:"smokeUnit font-small-text-size3"},$={class:"right-sensor"},E={class:"item-title-bg font-title-size"},K=["src"],Q={class:"sensor-box"},ss={key:0,class:"circle-img"},es=["src"],ts=["src"],is={key:1,class:"circle-img"},ls=["src"],as={class:"u-flex-column"},ns={class:"font-small-text-size2"},rs={key:0},cs={key:1},os={class:"smokeValue font-small-text-size1"},ms={class:"smokeUnit font-small-text-size3"},ps={class:"title"},us={class:"item-title-bg font-title-size"},fs=["src"],ds=S((()=>h("span",null,"配电房1",-1))),gs={class:"right-wiringDiagram"},vs={class:"title"},js={class:"item-title-bg font-title-size"},hs=["src"],bs=S((()=>h("span",null,"配电房2",-1))),xs=s({__name:"gridConnectedCabinetCopy1",setup(s){const N=e(),D=p(),S=u({data:c().format("YYYY-MM-DD HH:mm:ss")});p(),p(),p(),p(),p(),p(),p(),p(),p(!1),p();const xs=u([]),ks={alarmStatus:{icon:"",label:"故障状态",value:"报警",unit:"",picName:"warn.png",end:!1},smokeConcentr:{icon:"mist",label:"烟雾浓度",value:0,unit:"PPM",picName:"",end:!1},temp:{icon:"temp",label:"当前温度",value:0,unit:"°C",picName:"",end:!1},humidity:{icon:"humidity",label:"当前湿度",value:0,unit:"%RH",picName:"",end:!0}},ys=u([]),ws=p(null),zs=p(null),_s=p(null),Cs=u({width:500,height:500});let Ns=null,Ds=null;f((()=>Cs.width/10));const Ss=()=>{if(Ns||Ds)return Ns.setDimensions({width:Cs.width,height:Cs.height}),void Ds.setDimensions({width:Cs.width,height:Cs.height});Ns=new r.fabric.StaticCanvas(zs.value,{width:Cs.width,height:Cs.height}),Ds=new r.fabric.StaticCanvas(_s.value,{width:Cs.width,height:Cs.height}),new r.fabric.Image.fromURL("https://osstest.jrdaimao.com/ac/1692347493428_300x300.jpg",(s=>{s.set({scaleX:.5,scaleY:.5,left:0,top:0,width:150,height:150}),Ns.add(s).renderAll()}),{crossOrigin:"anonymous"})},Is=o.debounce((()=>{m(ws,(s=>{Cs.width=ws.value.offsetWidth,Cs.height=ws.value.offsetHeight,Ss()}))}),300);return d((()=>{(async()=>{const{data:s}=await a();for(let e=0;e<s.length;e++){const t=o.clone(ks,!0),i=Object.keys(t);xs.push(s[e].deviceName);for(let l=0;l<i.length;l++)"alarmStatus"===i[l]&&0==s[e].children[0].alarmStatus?t[i[l]].value="正常":"alarmStatus"===i[l]&&1==s[e].children[0].alarmStatus?t[i[l]].value="告警":t[i[l]].value=s[e].children[0][i[l]];ys.push(t)}})(),Ss(),Is()})),g((()=>{})),(s,e)=>{const a=l,r=i,c=t;return v(),j("div",{class:"screenfull-content tw-h-full tw-w-full bwg-bg screen-box",ref_key:"screenRef",ref:D},[h("div",I,[h("div",R,b(x(S).data),1),h("div",H,[x(N).userInfo.screenLogo?(v(),k(a,{key:0,src:x(N).userInfo.screenLogo,fit:"contain",class:"image"},null,8,["src"])):y("",!0),h("h1",Y,b(x(N).userInfo.projectTitle),1)]),h("div",L,[w(r,{class:"setting-item",type:"font",element:x(D)},null,8,["element"])])]),h("div",M,[h("div",null,[h("p",U,[h("img",{src:x(n)("screen","title_icon.png"),class:"title-icon"},null,8,W),h("span",null,b(x(xs)[0]),1)])]),h("div",q,[(v(!0),j(z,null,_(x(ys)[0],((s,e)=>(v(),j("div",{key:e,class:"u-flex-1 h-full u-flex-center-no asset-content-item"},[""!==s.picName?(v(),j("p",A,[h("img",{src:x(n)("screen","circle.png"),class:"title-icon0"},null,8,O),h("img",{src:x(n)("screen",s.picName),class:"title-icon1"},null,8,P)])):(v(),j("p",V,[h("img",{src:x(n)("screen","circle.png"),class:"title-icon0"},null,8,X),w(c,{name:s.icon,class:"title-icon1"},null,8,["name"])])),h("p",B,[h("span",F,b(s.label),1),"故障状态"===s.label?(v(),j("span",G,[h("i",{class:C(["font-small-text-size1",!0===s.value?"normal":"abnormal"])},b(s.value),3)])):(v(),j("span",J,[h("i",T,b(s.value),1),h("i",Z,b(s.unit),1)]))])])))),128))])]),h("div",$,[h("p",E,[h("img",{src:x(n)("screen","title_icon.png"),class:"title-icon"},null,8,K),h("span",null,b(x(xs)[1]),1)]),h("div",Q,[(v(!0),j(z,null,_(x(ys)[1],((s,e)=>(v(),j("div",{key:e,class:"u-flex-1 h-full u-flex-center-no asset-content-item"},[""!==s.picName?(v(),j("p",ss,[h("img",{src:x(n)("screen","circle.png"),class:"title-icon0"},null,8,es),h("img",{src:x(n)("screen",s.picName),class:"title-icon1"},null,8,ts)])):(v(),j("p",is,[h("img",{src:x(n)("screen","circle.png"),class:"title-icon0"},null,8,ls),w(c,{name:s.icon,class:"title-icon1"},null,8,["name"])])),h("p",as,[h("span",ns,b(s.label),1),"故障状态"===s.label?(v(),j("span",rs,[h("i",{class:C(["font-small-text-size1",!0===s.value?"normal":"abnormal"])},b(s.value),3)])):(v(),j("span",cs,[h("i",os,b(s.value),1),h("i",ms,b(s.unit),1)]))])])))),128))])]),h("div",{class:"left-wiringDiagram",ref_key:"leftWiringDiagramRef",ref:ws},[h("div",ps,[h("p",us,[h("img",{src:x(n)("screen","title_icon.png"),class:"title-icon"},null,8,fs),ds])]),h("canvas",{ref_key:"leftCanvasRef",ref:zs,class:"canvasStyle"},null,512)],512),h("div",gs,[h("div",vs,[h("p",js,[h("img",{src:x(n)("screen","title_icon.png"),class:"title-icon"},null,8,hs),bs])]),h("canvas",{ref_key:"rightCanvasRef",ref:_s,class:"canvasStyle"},null,512)])],512)}}},[["__scopeId","data-v-4f1e4de8"]]);export{xs as default};
