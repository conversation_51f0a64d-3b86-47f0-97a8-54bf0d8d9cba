import{B as e}from"./element-plus-95e0b914.js";import"./vue-5bfa3a54.js";import"./vxe-table-3a25f2d2.js";import{a as t}from"./index-e25d91f2.js";import{p as o,a}from"./plant-5d7dddcf.js";import"./chartResize-3e3d11d7.js";import{i as s,p as i,_ as l}from"./index-a5df0f75.js";import{i as r}from"./echarts-f30da64f.js";import{V as n}from"./zrender-c058db04.js";import"./xe-utils-fe99d42a.js";import{M as d}from"./index-daa15067.js";import{d as c}from"./dayjs-67f8ddef.js";import{l as p}from"./lodash-6d99edc3.js";import{e as m}from"./exportFile-75030642.js";import{i as u}from"./invertedTable-d05620ed.js";import{d as f}from"./@vueuse-5227c686.js";import{u as g,h as v,j as y,m as h,w as x,v as b,as as j,o as w,c as S,x as z,a8 as k,a as _,aa as C,a9 as L,ab as P,F as D,k as T,b as E,t as I,a6 as B,n as F}from"./@vue-5e5cdef9.js";import"./lodash-es-ea7deab5.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./@babel-f3c0a00c.js";import"./dom-zindex-5f662ad1.js";import"./element-resize-detector-0d37a2ab.js";import"./batch-processor-06abf2b4.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./vue-router-6159329f.js";import"./bignumber.js-a537a5ca.js";import"./js-cookie-8253c38e.js";import"./axios-84f1a956.js";import"./spark-md5-022b35d0.js";import"./nprogress-e136a1b4.js";import"./quasar-df1bac18.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-be30dbb6.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./tslib-a4e99503.js";import"./notification-950a5f80.js";import"./naive-ui-0ee0b8c3.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./date-fns-tz-0cc073c8.js";let N="#fff",U=["rgba(115, 212, 112, 1)","#36CE9E","#0090FF","#8f05ff","#FF515A","#8B5CFF","#00CA69"];const $={"收益":"￥","发电量":"KWh","等效小时":"h","发电效率":"%"},O=(e,t)=>{let o="";return/^#[\da-f]{6}$/i.test(e)&&(o=`rgba(${parseInt("0x"+e.slice(1,3))},${parseInt("0x"+e.slice(3,5))},${parseInt("0x"+e.slice(5,7))},${t})`),o},M={title:{text:"",textStyle:{fontSize:14},left:"30%"},backgroundColor:N,color:U,legend:{left:"center",top:20},tooltip:{trigger:"axis",formatter:function(e){let t="";return e.forEach(((e,o)=>{t+=`<div style="color: #666;font-size: 14px;line-height: 24px">\n                <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${U[e.componentIndex]};"></span>\n                ${e.seriesName}.${e.name}\n                <span style="color:${U[e.componentIndex]};font-weight:700;font-size: 18px">${e.value+$[e.seriesName]}</span>\n                `})),t},extraCssText:"background: #fff; border-radius: 0;box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);color: #333;",axisPointer:{type:"shadow",shadowStyle:{color:"#ffffff",shadowColor:"rgba(225,225,225,1)",shadowBlur:5}}},grid:{top:100,containLabel:!0},xAxis:[{type:"category",axisLabel:{formatter:"{value}",textStyle:{color:"#333"}},axisLine:{lineStyle:{color:"#D9D9D9"}},data:[]},{type:"category",axisLabel:{formatter:"{value}",textStyle:{color:"#333"}},axisLine:{lineStyle:{color:"#D9D9D9"}},data:[]},{type:"category",axisLabel:{formatter:"{value}",textStyle:{color:"#333"}},axisLine:{lineStyle:{color:"#D9D9D9"}},data:[]}],yAxis:[{type:"value",axisLabel:{textStyle:{color:"#666"}},nameTextStyle:{color:"#666",fontSize:12,lineHeight:40},splitLine:{lineStyle:{type:"dashed",color:"#E9E9E9"}},axisLine:{show:!1},axisTick:{show:!1}}],series:[{name:"收益",type:"line",symbolSize:8,smooth:!0,lineStyle:{normal:{color:U[0],shadowBlur:3,shadowColor:O(U[0],.5),shadowOffsetY:8}},areaStyle:{normal:{color:new n(0,0,0,1,[{offset:0,color:O(U[0],.3)},{offset:1,color:O(U[0],.1)}],!1),shadowColor:O(U[0],.1),shadowBlur:10},zlevel:4},zlevel:2,data:[]},{name:"发电量",type:"line",smooth:!0,symbolSize:8,lineStyle:{normal:{color:U[1],shadowBlur:3,shadowColor:O(U[1],.5),shadowOffsetY:8}},areaStyle:{normal:{color:new n(0,0,0,1,[{offset:0,color:O(U[1],.3)},{offset:1,color:O(U[1],.1)}],!1),shadowColor:O(U[1],.1),shadowBlur:10},zlevel:1},zlevel:1,data:[]},{name:"等效小时",type:"line",smooth:!0,symbolSize:8,lineStyle:{normal:{color:U[2],shadowBlur:3,shadowColor:O(U[2],.5),shadowOffsetY:8}},areaStyle:{normal:{color:new n(0,0,0,1,[{offset:0,color:O(U[2],.3)},{offset:1,color:O(U[2],.1)}],!1),shadowColor:O(U[2],.1),shadowBlur:10},zlevel:3},zlevel:3,data:[]}]};O(U[0],.5),new n(0,0,0,1,[{offset:0,color:O(U[0],.3)},{offset:1,color:O(U[0],.1)}],!1),O(U[0],.1),O(U[1],.5),new n(0,0,0,1,[{offset:0,color:O(U[1],.3)},{offset:1,color:O(U[1],.1)}],!1),O(U[1],.1);const A={class:"form"},q={class:"table-btn"},R=["onClick"],V=l(Object.assign({name:"energyIncom"},{__name:"index",setup(l){g((e=>({"8bcd54a6":$.value})));const n=v(),N=v(),U=v(!0),$=v("block"),O=v();let V=v([]);const Y=y({chartLoading:!0,exportLoading:!1});v("发电效益分析图");let H=v([]);const Q=v([]),W=v(),G=v(),K=y({condition:{address:"",timeType:"date",date:c().format("YYYY-MM-DD")},tablePage:{totalResult:0,currentPage:1,pageSize:10},modelData:{}}),Z=p._.cloneDeep(K.condition),J=p._.curry(i)("/deviceMonitor/plantDetail?plantUid=");y({name:[{required:!0,message:"请输入名称"},{min:3,max:5,message:"长度在 3 到 5 个字符"}],nickname:[{required:!0,message:"请输入昵称"}],sex:[{required:!0,message:"请选择性别"}]});const X=y({border:"full",showFooter:!1,loading:!1,minHeight:600,height:"auto",autoResize:!0,columnConfig:{resizable:!0},customConfig:{storage:{visible:!0,fixed:!0},checkMethod:({column:e})=>!["seq"].includes(e.field)},editConfig:{trigger:"click",mode:"cell"},sortConfig:{sortMethod:({sortList:e})=>{let t={};e.forEach((e=>{t[e.field]=e.order}))}},data:[],toolbarConfig:{custom:{allowFixed:!1},slots:{buttons:"toolbar_buttons",tools:"toolbar_tools"}},columns:Q.value}),ee=y({entryNameOption:[],plantOptions:o,powerDistributorOptions:a}),te=()=>{},oe=(e,t)=>{},ae=e=>{de(e.currentPage,e.pageSize)},se=()=>{Object.assign(K.condition,Z)},ie=[],le=async e=>{var t;if(Y.chartLoading=!0,"chart"==e){$.value="none";for(let e=0;e<(null==(t=G.value)?void 0:t.length);e++)ie[e]=r(G.value[e]),M.series[0].data=V.value[e].electricityList.map((e=>e.income)),M.series[1].data=V.value[e].electricityList.map((e=>e.electricity)),"date"===K.condition.timeType?(M.series[2].name="发电效率",M.series[2].data=V.value[e].electricityList.map((e=>Math.floor(100*e.electricityEfficiency)))):(M.series[2].name="等效小时",M.series[2].data=V.value[e].electricityList.map((e=>e.efficiencyPerHours))),M.xAxis[0].data=V.value[e].electricityList.map((e=>{var t;return"date"===K.condition.timeType?null==(t=e.dataTime)?void 0:t.split(" ")[1]:e.dataTime})),M.title.text=V.value[e].plantName,1===M.xAxis[0].data.length?M.series.forEach((e=>{e.type="bar"})):M.series.forEach((e=>{e.type="line"})),await F((()=>{ie[e].setOption({...M})}));Y.chartLoading=!1}else $.value="block"};async function re(){Y.exportLoading=!0;try{const t=await(e={...K.condition,columnsList:X.columns.map((e=>e.field)),sheetName:""},s({url:"/system/statistics/exportElectricityStatisticsInfo",method:"post",data:{...e},responseType:"blob"}));m(t,"能效收益报表")}catch(t){}finally{Y.exportLoading=!1}var e}const ne=({row:e,_rowIndex:t,column:o,visibleData:a,columnIndex:s})=>{const i=e[o.field];if(i&&["plantName"].includes(o.field)){const e=a[t-1];let s=a[t+1];if(e&&e[o.field]===i)return{rowspan:0,colspan:0};{let e=1;for(;s&&s[o.field]===i;)s=a[++e+t];if(e>1)return{rowspan:e,colspan:1}}}},de=async(e=1,t=10)=>{X.loading=!0,K.tablePage.currentPage=e,K.tablePage.pageSize=t;const o=await(a={...K.condition,...K.tablePage},s({url:"/system/statistics/getElectricityStatisticsInfo",method:"post",data:{...a,date:null===a.date?"":a.date,state:0===(null==(i=a.state)?void 0:i.length)?"":a.state,sort:a.sort||0}}));var a,i;X.loading=!1,O.value=o.data.list,V.value=o.data.list;const l=u(o.data.list||[],K.condition.date);H.value=l.rows,l.columns.forEach((e=>{e.title=e.label})),Q.value=l.columns,X.data=H.value,K.tablePage.totalResult=o.data.total,"none"===$.value&&le("chart")};return h((()=>{x((()=>Q.value),(()=>{X.columns=Q.value}),{deep:!0}),de(),(async()=>{const e=await t();!function e(t){var o;for(const a of t)(null==(o=null==a?void 0:a.children)?void 0:o.length)?e(a.children):delete a.children}(e.data),ee.entryNameOption=e.data})(),f(W,(e=>{var t;for(let o=0;o<(null==(t=G.value)?void 0:t.length);o++)ie[o]&&setTimeout((()=>{ie[o].resize()}),100)})),window.addEventListener("resize",(()=>{}))})),b((()=>{var e;for(let t=0;t<(null==(e=G.value)?void 0:e.length);t++)ie[t]&&ie[t].dispose()})),(t,o)=>{const a=j("vxe-input"),s=j("vxe-form-item"),i=j("vxe-button"),l=j("vxe-form"),r=j("vxe-pager"),c=j("vxe-grid"),m=e;return w(),S("div",{ref_key:"appContainerRef",ref:n,class:"app-container"},[z(c,B({id:"energyEfficiencyBenefits",ref_key:"xGrid",ref:N,class:"my-grid66"},X,{"scroll-x":{enabled:!1},"scroll-y":{enabled:!1},"span-method":ne,onCustom:oe}),{form:k((()=>[_("div",A,[z(l,{ref:"ordinaryForm",collapseStatus:U.value,"onUpdate:collapseStatus":o[3]||(o[3]=e=>U.value=e),data:K.condition},{default:k((()=>[z(s,{field:"address",title:"地址信息"},{default:k((({data:e})=>[z(a,{modelValue:e.address,"onUpdate:modelValue":t=>e.address=t,clearable:"",placeholder:"请输入地址信息"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),z(s,{field:"date"},{default:k((({data:e})=>[z(d,{"is-range":!1,"time-value":K.condition.date,timeType:K.condition.timeType,onTypeChange:te,"onUpdate:timeValue":o[0]||(o[0]=e=>{(e=>{p._.isArray(e)?K.condition.date=p._.cloneDeep(e):K.condition.date=e})(e)}),"onUpdate:timeType":o[1]||(o[1]=e=>{K.condition.timeType=e})},null,8,["time-value","timeType"])])),_:1}),z(s,null,{default:k((()=>[z(i,{status:"danger",onClick:se},{default:k((()=>[C("重置")])),_:1})])),_:1}),z(s,null,{default:k((()=>[z(i,{status:"primary",onClick:o[2]||(o[2]=e=>de(1))},{default:k((()=>[C("查询")])),_:1})])),_:1})])),_:1},8,["collapseStatus","data"])])])),toolbar_buttons:k((()=>[])),toolbar_tools:k((()=>[_("div",q,[L(z(i,{status:"warning",icon:"vxe-icon-chart-line",onClick:o[4]||(o[4]=e=>le("chart"))},null,512),[[P,"block"===$.value]]),L(z(i,{status:"warning",onClick:o[5]||(o[5]=e=>le("table"))},{default:k((()=>[C("表格")])),_:1},512),[[P,"none"===$.value]]),z(i,{status:"primary",onClick:re,loading:Y.exportLoading},{default:k((()=>[C("导出")])),_:1},8,["loading"])])])),top:k((()=>[L((w(),S("div",{ref_key:"chart",ref:W,class:"bg-white chart-box p-x-[20px]"},[(w(!0),S(D,null,T(E(V).length,(e=>(w(),S("div",{ref_for:!0,ref_key:"chartItem",ref:G},null,512)))),256))])),[[m,Y.chartLoading],[P,"none"===$.value]])])),"row-plantStatus":k((({row:e})=>[])),"row-plantName":k((({row:e})=>[_("div",{style:{cursor:"pointer"},onClick:t=>E(J)(e.plantUid)},I(e.plantName),9,R)])),bottom:k((()=>[])),pager:k((()=>[z(r,{"current-page":K.tablePage.currentPage,"onUpdate:currentPage":o[6]||(o[6]=e=>K.tablePage.currentPage=e),"page-size":K.tablePage.pageSize,"onUpdate:pageSize":o[7]||(o[7]=e=>K.tablePage.pageSize=e),"page-sizes":[10],total:K.tablePage.totalResult,perfect:"",onPageChange:ae},null,8,["current-page","page-size","total"])])),_:1},16)],512)}}}),[["__scopeId","data-v-2ab89716"]]);export{V as default};
