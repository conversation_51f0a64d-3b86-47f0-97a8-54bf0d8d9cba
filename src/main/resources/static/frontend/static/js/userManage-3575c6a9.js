import{b as e,a as l,d as t,D as a,F as s,K as o,x as r,f as i,G as u,e as n,L as p,A as d,c,R as m}from"./element-plus-95e0b914.js";import{F as f,W as j}from"./quasar-df1bac18.js";import{_ as b}from"./pagination-c4d8e88e.js";import"./vue-5bfa3a54.js";import{a as h,l as w,m as v,n as V,e as y,i as g,o as x,p as k}from"./systemSettingApi-34f94e8e.js";import"./vxe-table-3a25f2d2.js";import{_}from"./index-a5df0f75.js";import{n as C}from"./@vicons-f32a0bdb.js";import{c as S}from"./pageUtil-3bb2e07a.js";import{l as T}from"./lodash-6d99edc3.js";import{g as D}from"./api-360ec627.js";import{m as U}from"./notification-950a5f80.js";import{L as P}from"./ui-385bff4c.js";import{h as I,j as N,m as z,w as O,az as F,o as B,c as E,x as R,a8 as q,a as A,a9 as L,b as W,aa as $,F as M,k as G,f as K,t as Q,q as X,l as Z,C as H,D as J}from"./@vue-5e5cdef9.js";import"./lodash-es-ea7deab5.js";import"./@vueuse-5227c686.js";import"./vue-router-6159329f.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./dayjs-67f8ddef.js";import"./@babel-f3c0a00c.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./naive-ui-0ee0b8c3.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./date-fns-tz-0cc073c8.js";import"./xe-utils-fe99d42a.js";import"./dom-zindex-5f662ad1.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./nprogress-e136a1b4.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-be30dbb6.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./proxyUtil-6f30f7ef.js";import"./menuStore-30bf76d3.js";import"./icons-95011f8c.js";const Y=e=>(H("data-v-d20de163"),e=e(),J(),e),ee={class:"q-pa-md"},le={class:"header tw-h-10 fontWhite"},te={colspan:"9"},ae=Y((()=>A("h6",{class:"tw-text-2xl tw-my-0 text-white text-left"},"用户管理",-1))),se=Y((()=>A("div",null,null,-1))),oe=Y((()=>A("tr",null,[A("th",{class:"text-center"},"用户名"),A("th",{class:"text-center"},"用户类型"),A("th",{class:"text-center"},"用户状态"),A("th",{class:"text-center"},"项目专项"),A("th",{class:"text-center"},"用户角色"),A("th",{class:"text-center"},"手机号"),A("th",{class:"text-center"},"邮箱"),A("th",{class:"text-center"},"创建时间"),A("th",{class:"text-center"},"操作")],-1))),re={class:"tw-relative"},ie={class:"text-center"},ue={class:"text-center"},ne={class:"text-center"},pe={class:"tw-inline-flex items-center"},de={class:"text-center"},ce={class:"text-center"},me={class:"text-center"},fe={class:"text-center"},je={class:"text-center"},be={class:"operation text-center"},he=Y((()=>A("tr",null,null,-1))),we={colspan:"9"},ve={class:"dialog-footer"},Ve={class:"dialog-footer"},ye=_({__name:"userManage",setup(_){const H=h,J=C;let Y=I();I();const ye=S(Ce);let ge=N({searchType:"name",searchValue:"",dialogVisible:!1,dialogTitle:"",form:{},passwordDialogVisible:!1,passwordForm:{},userStatusOptions:[{label:"未启用",value:"0"},{label:"启用",value:"1"}],userTypeOptions:[{label:"个人用户",value:"0"},{label:"企业用户",value:"1"}],userRoleOptions:[],projectOptions:[],projectProps:{value:"id",label:"projectName",emitPath:!1,checkStrictly:!0},projectStatus:!1,visibleCondition:!1,userCondition:{},listData:[]});function xe(){ge.userCondition={}}function ke(){}function _e(){Y&&Y.value.validate((async e=>{if(!e)return!1;ge.dialogTitle.includes("新增")?await async function(){try{const e=await x(ge.form.projectID,ge.form.roleID,ge.form.userEmail,ge.form.userName,ge.form.userPhone,ge.form.userStatus,ge.form.userType),l=D(e);"00000"===l.status?(U.success("新增操作成功"),ge.dialogVisible=!1,Ce()):U.error(l.message)}catch(e){}}():await async function(){try{const e=await k(ge.form.projectID,ge.form.roleID,ge.form.userEmail,ge.form.userName,ge.form.userPhone,ge.form.userType,ge.form.userUid),l=D(e);"00000"===l.status?(U.success("编辑操作成功"),ge.dialogVisible=!1,Ce()):U.error(l.message)}catch(e){U.error("编辑操作接口请求出错")}}()}))}async function Ce(){Se.set(!0);try{const e=await V(ye.page,ye.pageSize,ge.userCondition.projectID,ge.userCondition.userName,ge.userCondition.userPhone,ge.userCondition.userStatus,ge.userCondition.userType),l=D(e);ge.listData=l.data.records,ye.total=l.data.total}catch(e){}Se.set(!1)}const Se=new P;return z((async()=>{Se.set(!1),await Ce(),await async function(){try{const e=await y(),l=D(e);ge.projectOptions=l.data[0].children}catch(e){}}(),await async function(){try{const e=await g(1,100,""),l=D(e);ge.userRoleOptions=l.data.records}catch(e){}}()})),O((()=>ge.form.userType),((e,l)=>{"0"==e?(ge.projectStatus=!1,ge.projectProps.checkStrictly=!1):"1"==e?(ge.projectStatus=!1,ge.projectProps.checkStrictly=!0):ge.projectStatus=!0}),{immediate:!0,deep:!0}),(h,V)=>{const y=f,g=t,x=a,k=s,_=o,C=r,S=i,U=u,P=b,I=j,N=n,z=p,O=d,Se=c,Te=m,De=F("skeleton-item"),Ue=F("x"),Pe=F("g");return B(),E("div",ee,[R(I,{class:"tw-rounded-none tw-w-full tw-h-full",separator:"cell"},{default:q((()=>[A("thead",null,[A("tr",le,[A("th",te,[L((B(),E("section",null,[ae,R(C,{visible:W(ge).visibleCondition,placement:"bottom",width:330,trigger:"click"},{reference:q((()=>[L(R(y,{class:"tw-bg-green-500 tw-text-white",label:"多条件查询",onClick:V[0]||(V[0]=e=>W(ge).visibleCondition=!W(ge).visibleCondition),"no-caps":"",dense:""},null,512),[[De]])])),default:q((()=>[R(g,{modelValue:W(ge).userCondition.userName,"onUpdate:modelValue":V[1]||(V[1]=e=>W(ge).userCondition.userName=e),placeholder:"Please input",clearable:""},{prepend:q((()=>[$("用户名")])),_:1},8,["modelValue"]),R(g,{modelValue:W(ge).userCondition.userPhone,"onUpdate:modelValue":V[2]||(V[2]=e=>W(ge).userCondition.userPhone=e),placeholder:"Please input",clearable:""},{prepend:q((()=>[$("用户手机号")])),_:1},8,["modelValue"]),R(k,{class:"tw-w-full",modelValue:W(ge).userCondition.userStatus,"onUpdate:modelValue":V[3]||(V[3]=e=>W(ge).userCondition.userStatus=e),placeholder:"用户状态",clearable:""},{default:q((()=>[(B(!0),E(M,null,G(W(ge).userStatusOptions,(e=>(B(),K(x,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),R(k,{class:"tw-w-full",modelValue:W(ge).userCondition.userType,"onUpdate:modelValue":V[4]||(V[4]=e=>W(ge).userCondition.userType=e),placeholder:"用户类型",clearable:""},{default:q((()=>[(B(!0),E(M,null,G(W(ge).userTypeOptions,(e=>(B(),K(x,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),R(_,{clearable:"",class:"tw-w-full",placeholder:"请先选择用户类型",modelValue:W(ge).userCondition.projectID,"onUpdate:modelValue":V[5]||(V[5]=e=>W(ge).userCondition.projectID=e),props:W(ge).projectProps,options:W(ge).projectOptions},null,8,["modelValue","props","options"])])),_:1},8,["visible"]),L(R(y,{class:"tw-bg-green-500 tw-text-white",label:"置空",onClick:xe,"no-caps":"",dense:""},null,512),[[De]]),se,L(R(y,{class:"tw-bg-blue-500 tw-text-white",label:"查询","no-caps":"",dense:"",onClick:V[6]||(V[6]=e=>W(ye).page=1)},null,512),[[De]]),L(R(y,{class:"tw-bg-blue-500 tw-text-white",label:"新增","no-caps":"",dense:"",onClick:V[7]||(V[7]=e=>(ge.dialogTitle="新增用户",ge.dialogVisible=!0,void(ge.form={userStatus:"1"})))},null,512),[[De]])])),[[Ue,[3,2,1,13,1,1]],[Pe,10]])])]),oe]),A("tbody",re,[(B(!0),E(M,null,G(W(ge).listData,((t,a)=>(B(),E("tr",{key:a},[A("td",ie,Q(t.userName),1),A("td",ue,Q(t.userType),1),A("td",ne,[A("div",pe,[A("div",{class:"circle",style:X({background:"启用"===t.userStatus?"#24b276":"grey"})},null,4),$(" "+Q(t.userStatus),1)])]),A("td",de,Q(t.projectName),1),A("td",ce,Q(t.roleName),1),A("td",me,Q(t.userPhone),1),A("td",fe,Q(t.userEmail),1),A("td",je,Q(t.createTime),1),A("td",be,[R(U,{effect:"dark",content:"编辑",placement:"top"},{default:q((()=>[R(S,{icon:W(J).PencilSharp,plain:"",onClick:e=>function(e){ge.form=T._.cloneDeep(e),ge.form.userType="企业用户"===ge.form.userType?"1":"0",ge.form.userStatus="未启用"===ge.form.userStatus?"0":"1",ge.dialogTitle="编辑用户",ge.dialogVisible=!0}(t)},null,8,["icon","onClick"])])),_:2},1024),R(U,{effect:"dark",content:"启用"===t.userStatus?"禁用":"启用",placement:"top"},{default:q((()=>[R(S,{icon:"启用"===t.userStatus?W(J).BanSharp:W(J).PlayCircleSharp,plain:"",onClick:a=>function(t,a){const s="启用"===t?"禁用":"启用";e.confirm(`是否要${s}该用户`,{confirmButtonText:"确定",cancelButtonText:"取消"}).then((async()=>{try{const e=await w("启用"===t?"0":"1",a),s=D(e);"00000"===s.status?(l.success("状态修改成功"),Ce()):l.error(s.message)}catch(e){}})).catch((()=>{}))}(t.userStatus,t.userUid)},null,8,["icon","onClick"])])),_:2},1032,["content"]),R(U,{effect:"dark",content:"删除",placement:"top"},{default:q((()=>[R(S,{icon:W(J).TrashSharp,plain:"",onClick:a=>{return s=t.userUid,void e.confirm("是否要删除该用户？(该删除操作不可逆)",{confirmButtonText:"确定",cancelButtonText:"取消"}).then((async()=>{try{const e=await v(s),t=D(e);"00000"===t.status?(l.success("用户删除成功"),Ce()):l.error(t.message)}catch(e){}})).catch((()=>{}));var s}},null,8,["icon","onClick"])])),_:2},1024)])])))),128)),he]),A("tfoot",null,[A("tr",null,[A("td",we,[R(P,{page:W(ye)},null,8,["page"])])])])])),_:1}),R(Te,{modelValue:W(ge).dialogVisible,"onUpdate:modelValue":V[17]||(V[17]=e=>W(ge).dialogVisible=e),title:W(ge).dialogTitle,width:"35%","z-index":1e3},{footer:q((()=>[A("span",ve,[R(S,{type:"primary",plain:"",onClick:V[15]||(V[15]=e=>W(ge).dialogVisible=!1)},{default:q((()=>[$("取消")])),_:1}),R(S,{type:"primary",onClick:V[16]||(V[16]=e=>_e(W(Y)))},{default:q((()=>[$("确定")])),_:1})])])),default:q((()=>[R(Se,{ref_key:"ruleFormRef",ref:Y,model:W(ge).form,"label-width":"150px",size:"large",rules:W(H)},{default:q((()=>[R(N,{label:"用户名",prop:"userName"},{default:q((()=>[R(g,{modelValue:W(ge).form.userName,"onUpdate:modelValue":V[8]||(V[8]=e=>W(ge).form.userName=e)},null,8,["modelValue"])])),_:1}),R(N,{label:"用户类型",prop:"userType"},{default:q((()=>[R(k,{class:"tw-w-full",modelValue:W(ge).form.userType,"onUpdate:modelValue":V[9]||(V[9]=e=>W(ge).form.userType=e),placeholder:"用户类型",clearable:""},{default:q((()=>[(B(!0),E(M,null,G(W(ge).userTypeOptions,(e=>(B(),K(x,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),W(ge).dialogTitle.includes("新增")?(B(),K(N,{key:0,label:"用户状态",prop:"userStatus"},{default:q((()=>[R(O,{modelValue:W(ge).form.userStatus,"onUpdate:modelValue":V[10]||(V[10]=e=>W(ge).form.userStatus=e)},{default:q((()=>[(B(!0),E(M,null,G(W(ge).userStatusOptions,(e=>(B(),K(z,{label:e.value,size:"large"},{default:q((()=>[$(Q(e.label),1)])),_:2},1032,["label"])))),256))])),_:1},8,["modelValue"])])),_:1})):Z("",!0),R(N,{label:"项目专项",prop:"projectID"},{default:q((()=>[R(_,{clearable:"",class:"tw-w-full",placeholder:"项目专项",modelValue:W(ge).form.projectID,"onUpdate:modelValue":V[11]||(V[11]=e=>W(ge).form.projectID=e),props:W(ge).projectProps,options:W(ge).projectOptions,disabled:W(ge).projectStatus},null,8,["modelValue","props","options","disabled"])])),_:1}),R(N,{label:"用户角色",prop:"roleID"},{default:q((()=>[R(k,{class:"tw-w-full",modelValue:W(ge).form.roleID,"onUpdate:modelValue":V[12]||(V[12]=e=>W(ge).form.roleID=e),placeholder:"用户角色",clearable:""},{default:q((()=>[(B(!0),E(M,null,G(W(ge).userRoleOptions,(e=>(B(),K(x,{key:e.roleID,label:e.roleName,value:e.roleID},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),R(N,{label:"手机号",prop:"userPhone"},{default:q((()=>[R(g,{modelValue:W(ge).form.userPhone,"onUpdate:modelValue":V[13]||(V[13]=e=>W(ge).form.userPhone=e)},null,8,["modelValue"])])),_:1}),R(N,{label:"邮箱",prop:"email"},{default:q((()=>[R(g,{modelValue:W(ge).form.userEmail,"onUpdate:modelValue":V[14]||(V[14]=e=>W(ge).form.userEmail=e)},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue","title"]),R(Te,{modelValue:W(ge).passwordDialogVisible,"onUpdate:modelValue":V[22]||(V[22]=e=>W(ge).passwordDialogVisible=e),title:"重置密码",width:"35%","z-index":1e3},{footer:q((()=>[A("span",Ve,[R(S,{type:"primary",plain:"",onClick:V[21]||(V[21]=e=>W(ge).passwordDialogVisible=!1)},{default:q((()=>[$("取消")])),_:1}),R(S,{type:"primary",onClick:ke},{default:q((()=>[$("确定")])),_:1})])])),default:q((()=>[R(Se,{model:W(ge).passwordForm,"label-width":"150px",size:"large",rules:W(H)},{default:q((()=>[R(N,{label:"旧密码",prop:"old"},{default:q((()=>[R(g,{modelValue:W(ge).passwordForm.old,"onUpdate:modelValue":V[18]||(V[18]=e=>W(ge).passwordForm.old=e)},null,8,["modelValue"])])),_:1}),R(N,{label:"新密码",prop:"new"},{default:q((()=>[R(g,{modelValue:W(ge).passwordForm.new,"onUpdate:modelValue":V[19]||(V[19]=e=>W(ge).passwordForm.new=e)},null,8,["modelValue"])])),_:1}),R(N,{label:"重复新密码",prop:"new"},{default:q((()=>[R(g,{modelValue:W(ge).passwordForm.repeat,"onUpdate:modelValue":V[20]||(V[20]=e=>W(ge).passwordForm.repeat=e)},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-d20de163"]]);export{ye as default};
