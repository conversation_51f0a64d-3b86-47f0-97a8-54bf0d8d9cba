import{e,d as t}from"./quasar-df1bac18.js";import{_ as a}from"./date-7dd9d7d0.js";import"./vue-5bfa3a54.js";import{l as s}from"./lodash-6d99edc3.js";import{c as l,a as i}from"./dataAnalysisApi-94c88eef.js";import{g as o}from"./api-360ec627.js";import{e as r}from"./echartsInit-2e16a3ff.js";import{d as m}from"./dayjs-67f8ddef.js";import{_ as n}from"./index-a5df0f75.js";import{h as p,m as u,az as d,a9 as c,o as j,c as w,a as v,t as f,x,b as y,H as b,f as g,a8 as h,C as V,D as k}from"./@vue-5e5cdef9.js";import"./notification-950a5f80.js";import"./naive-ui-0ee0b8c3.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./lodash-es-ea7deab5.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./@babel-f3c0a00c.js";import"./date-fns-tz-0cc073c8.js";import"./async-validator-cf877c1f.js";import"./@vicons-f32a0bdb.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./@vueuse-5227c686.js";import"./vue-router-6159329f.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./menuStore-30bf76d3.js";import"./icons-95011f8c.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./element-plus-95e0b914.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./xe-utils-fe99d42a.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-be30dbb6.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";const _={title:{text:"实时发电量(单位为: KWh)",left:"center",right:"center",top:"5px",bottom:"1px",textStyle:{fontSize:function(e){const t=window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth;return parseInt(e*((t>=1200?t:1200)/1920))}(30)}},tooltip:{trigger:"axis"},grid:{top:"50px",bottom:"75px",right:"10px",left:"80px"},xAxis:{type:"category",data:[]},yAxis:[{type:"value",name:"",position:"left",show:!0,axisLine:{lineStyle:{color:"black"}}},{type:"value",name:"",position:"left",show:!1,axisLine:{lineStyle:{color:"black"}}}],series:[{data:[],type:"line",barMaxWidth:"80",smooth:!0,itemStyle:{}}]};function U(e="标题",t=["1:00","2:00","3:00","4:00"],a=[{value:[12,7,11,6],name:""}]){return _.title.text=e,_.series=a.map(((e,t)=>{const a=s._.cloneDeep(_.series[0]);return a.data=e.value,a.name=e.name,a.yAxisIndex=t>=1?1:0,a.type=e.type??"line",a})),_.xAxis.data=t,_}const z=e=>(V("data-v-88f3ec1b"),e=e(),k(),e),S={class:"tw-bg-white"},A={class:"tw-bg-gray-300 tw-pt-1 tw-pl-4"},R={class:"tw-flex tw-items-center"},W={class:"tw-m-0 tw-text-black tw-text-2xl"},D=z((()=>v("div",null,null,-1))),E={class:"tw-bg-gray-100"},I={class:"tw-px-4"},M={class:"tw-flex tw-justify-evenly tw-items-end"},N=z((()=>v("p",{class:"tw-text-xl tw-m-0"},"天气",-1))),Y=z((()=>v("span",{class:"tw-text-black"},"选择参数:",-1))),q={class:"tw-flex tw-justify-evenly tw-items-end"},L=z((()=>v("p",{class:"tw-text-xl tw-m-0"},"温度",-1))),C=z((()=>v("span",{class:"tw-text-black"},"选择参数:",-1))),T={class:"tw-flex tw-justify-evenly tw-items-end"},B=z((()=>v("p",{class:"tw-text-xl tw-m-0"},"云量",-1))),F=z((()=>v("span",{class:"tw-text-black"},"选择参数:",-1))),H={class:"tw-flex tw-justify-evenly tw-items-end"},K=z((()=>v("p",{class:"tw-text-xl tw-m-0"},"风速",-1))),P=z((()=>v("span",{class:"tw-text-black"},"选择参数:",-1))),Q=n({__name:"spaceAnalysis",props:{plantUid:{type:String},plantName:{type:String}},setup(s){const n=s,V=p(""),k=p(),_=p(),z=p(),Q=p(),Z=p(2);async function $(){{const e=await l(n.plantUid,k.value.date),t=o(e),a=t.data.map((e=>e.dataTime)),s=t.data.map((e=>e.totalPower)),i=t.data.map((e=>e.workEfficiency.slice(0,e.workEfficiency.length-1)));await r(_,U("功率 效率",a,[{value:i,name:"效率",type:"bar"},{value:s,name:"功率",type:"line"}])),await r(z,U("预测功率 效率",a,[{value:i,name:"效率",type:"bar"},{value:s,name:"功率",type:"line"}]))}{const e=m(k.value.data).subtract(1,"day").format("YYYY-MM-DD"),t=await i(n.plantUid,e,k.value.date),a=o(t),s=a.data.weatherResult.map((e=>e.time)),l=a.data.weatherResult;await r(Q,U("天气",s,[{value:l.map((e=>e.cloud)),name:"云量"},{value:l.map((e=>e.pressure)),name:"气压"},{value:l.map((e=>e.windspeed)),name:"风速"},{value:l.map((e=>e.humidity)),name:"湿度"},{value:l.map((e=>e.precip)),name:"降雨"}]))}}return u((async()=>{await $()})),(s,l)=>{const i=a,o=e,r=t,m=d("x"),p=d("g"),u=d("skeleton-item"),U=d("y");return c((j(),w("div",S,[c((j(),w("section",A,[v("div",R,[v("p",W," 站点名称: "+f(n.plantName),1)]),x(i,{class:"date",tabs:"日",type:"single",ref_key:"dateRef",ref:k,onUpdateDate:$},null,512),D])),[[m,[2,1,3]],[p,10]]),c((j(),w("section",E,[v("figure",{ref_key:"powerRef",ref:_},null,512),v("figure",{ref_key:"powerForecastRef",ref:z},null,512),c((j(),w("section",I,[v("div",M,[N,x(o,{modelValue:y(Z),"onUpdate:modelValue":l[0]||(l[0]=e=>b(Z)?Z.value=e:null),class:"tw-w-1/2","label-always":"",min:0,max:10},null,8,["modelValue"]),c((j(),g(r,{"label-color":"white",modelValue:y(V),"onUpdate:modelValue":l[1]||(l[1]=e=>b(V)?V.value=e:null),filled:"",dense:""},{prepend:h((()=>[Y])),_:1},8,["modelValue"])),[[u]])]),v("div",q,[L,x(o,{modelValue:y(Z),"onUpdate:modelValue":l[2]||(l[2]=e=>b(Z)?Z.value=e:null),class:"tw-w-1/2","label-always":"",min:0,max:10},null,8,["modelValue"]),c((j(),g(r,{"label-color":"white",modelValue:y(V),"onUpdate:modelValue":l[3]||(l[3]=e=>b(V)?V.value=e:null),filled:"",dense:""},{prepend:h((()=>[C])),_:1},8,["modelValue"])),[[u]])]),v("div",T,[B,x(o,{modelValue:y(Z),"onUpdate:modelValue":l[4]||(l[4]=e=>b(Z)?Z.value=e:null),class:"tw-w-1/2","label-always":"",min:0,max:10},null,8,["modelValue"]),c((j(),g(r,{"label-color":"white",modelValue:y(V),"onUpdate:modelValue":l[5]||(l[5]=e=>b(V)?V.value=e:null),filled:"",dense:""},{prepend:h((()=>[F])),_:1},8,["modelValue"])),[[u]])]),v("div",H,[K,x(o,{modelValue:y(Z),"onUpdate:modelValue":l[6]||(l[6]=e=>b(Z)?Z.value=e:null),class:"tw-w-1/2","label-always":"",min:0,max:10},null,8,["modelValue"]),c((j(),g(r,{"label-color":"white",modelValue:y(V),"onUpdate:modelValue":l[7]||(l[7]=e=>b(V)?V.value=e:null),filled:"",dense:""},{prepend:h((()=>[P])),_:1},8,["modelValue"])),[[u]])])])),[[U,[1,1,1,1,3]]]),v("figure",{ref_key:"weatherRef",ref:Q},null,512)])),[[m,[1,1]],[U,[1,1]]])])),[[U,[1,17]]])}}},[["__scopeId","data-v-88f3ec1b"]]);export{Q as default};
