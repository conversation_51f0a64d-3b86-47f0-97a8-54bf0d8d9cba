import{x as t}from"./menuStore-26f8ddd8.js";import{b as e}from"./index-8cc8d4b8.js";import{i}from"./echarts-f30da64f.js";import"./vue-5bfa3a54.js";import{w as l}from"./@vue-5e5cdef9.js";t();const n=e();function o(t){const e=window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth;return parseInt(t*((e>=1200?e:1200)/1920))}async function d(t,e){let d;return t.value&&t.value.removeAttribute("_echarts_instance_"),d=i((null==t?void 0:t.value)||t),d.setOption(e,!0),d.resize(),window.addEventListener("resize",(()=>{setTimeout((()=>{d&&d.resize(),function(t,e){var i,l,n,d,u,s,v,r,a,c,f,S,z,x,m,p,h,y;(null==(l=null==(i=null==e?void 0:e.title)?void 0:i.text)?void 0:l.includes("电站总发电功率"))?t.setOption({title:{textStyle:{fontSize:o(25)}},series:[{detail:{fontSize:o(45)}}]}):(null==(d=null==(n=null==e?void 0:e.title)?void 0:n.text)?void 0:d.includes("逆变器正常运行率"))?t.setOption({title:{textStyle:{fontSize:o(25)}},series:[{axisLabel:{fontSize:o(20)},detail:{fontSize:o(45)}}]}):(null==(s=null==(u=null==e?void 0:e.title)?void 0:u.text)?void 0:s.includes("日发电量(单位为: MWh)"))||(null==(r=null==(v=null==e?void 0:e.title)?void 0:v.text)?void 0:r.includes("实时发电量(单位为: KWh)"))?t.setOption({title:{textStyle:{fontSize:o(22)}}}):(null==(c=null==(a=null==e?void 0:e.title)?void 0:a.text)?void 0:c.includes("告警电站"))||(null==(S=null==(f=null==e?void 0:e.title)?void 0:f.text)?void 0:S.includes("告警信息"))?t.setOption({title:{textStyle:{fontSize:o(20)}},series:[{axisLabel:{fontSize:o(15)},detail:{fontSize:o(40)}}]}):(null==(x=null==(z=null==e?void 0:e.title)?void 0:z.text)?void 0:x.includes("日发电量(单位为: MWh)"))||(null==(p=null==(m=null==e?void 0:e.title)?void 0:m.text)?void 0:p.includes("月发电量(单位为: MWh)"))?t.setOption({title:{textStyle:{fontSize:o(22)}}}):(null==(y=null==(h=null==e?void 0:e.title)?void 0:h.text)?void 0:y.includes("站点轮播仪表盘"))&&t.setOption({series:[{title:{fontSize:o(15)},detail:{fontSize:o(40)}},{title:{fontSize:o(13)},detail:{fontSize:o(25)}},{title:{fontSize:o(17)},detail:{fontSize:o(25),rich:{a:{fontSize:o(13)}}}}]})}(d,e)}),300)})),l((()=>n.sidebar.opened),(()=>{setTimeout((()=>{d&&d.resize()}),300)})),d}async function u(t,e){let l;return l=i((null==t?void 0:t.value)||t),l.setOption(e,!0),l.resize(),l}export{u as a,d as e};
