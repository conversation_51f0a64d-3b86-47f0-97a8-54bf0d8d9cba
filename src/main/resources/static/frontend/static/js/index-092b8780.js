import{i as t}from"./index-a5df0f75.js";function e(e){return t({url:"/system/statistics/getAlarmNumInfo",method:"get",data:{},headers:{msg:!1}})}function s(e){return t({url:"/system/statistics/getElectricityBySixMonth",method:"get",data:{},headers:{projectId:e,msg:!1}})}function a(e){return t({url:"/system/statistics/getEveryHourElectricityInfo",method:"get",data:{},headers:{projectId:e,msg:!1}})}function r(e){return t({url:"/system/statistics/getPlantElectricityInfo",method:"get",data:{},headers:{projectId:e,msg:!1}})}function n(e){return t({url:"/system/statistics/getPlantNumInfo",method:"get",data:{},headers:{projectId:e,msg:!1}})}function o(e){return t({url:"/system/statistics/getInverterNumInfo",method:"get",data:{},headers:{projectId:e,msg:!1}})}function i(e){return t({url:"/system/plantManage/getPlantInfoCoordinateOfArea",method:"post",data:{...e},headers:{msg:!1}})}function d(e){return t({url:"/system/plantManage/getPlantCarousel",method:"get",params:{...e},headers:{msg:!1}})}function u(e){return t({url:"/statistics/generateElectricityOperation/getWeatherDataByCity",method:"post",data:{...e},headers:{msg:!1}})}function m(e){return t({url:"/plant/carousel/getPlantCoordinate",method:"post",data:{...e},headers:{msg:!1}})}function c(){return t({url:"/system/statistics/getElectricityByNumDay/30",method:"get",headers:{msg:!1}})}export{r as a,o as b,s as c,a as d,d as e,i as f,n as g,m as h,u as i,c as j,e as k};
