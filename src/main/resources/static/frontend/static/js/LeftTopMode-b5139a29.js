import"./vue-5bfa3a54.js";import{s as e}from"./pinia-c7531a5f.js";import{a,_ as t,b as s,c as l,d as o,u as r,e as i,f as n,g as u,h as d}from"./index-8cc8d4b8.js";import{g as c,h as p,j as m,k as v,l as f,m as h,n as g,o as y,p as _,q as b,r as w,s as k,t as j}from"./element-plus-d975be09.js";import{u as x,a as I,R as V}from"./vue-router-6159329f.js";import{d as P,as as C,b as L,o as R,c as S,g as W,f as q,a8 as D,e as U,F as M,x as z,al as $,aa as B,t as E,r as A,l as N,k as T,y as F,a as H,h as O,ac as Y,w as X,af as Z,j as G,H as J,C as K,D as Q,aH as ee,n as ae,s as te,m as se,a9 as le,ab as oe,q as re}from"./@vue-5e5cdef9.js";import{p as ie}from"./path-browserify-5de4ab56.js";import{c as ne}from"./path-to-regexp-6da82254.js";import{a as ue}from"./vxe-table-3a25f2d2.js";import{R as de,m as ce,n as pe,d as me}from"./@element-plus-4c34063a.js";import{_ as ve}from"./index-f2383b94.js";import"./lodash-6d99edc3.js";import"./@babel-f3c0a00c.js";import"./vue-demi-01e7384c.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./xe-utils-fe99d42a.js";import"./js-cookie-8253c38e.js";import"./axios-84f1a956.js";import"./lodash-es-ea7deab5.js";import"./spark-md5-022b35d0.js";import"./nprogress-e136a1b4.js";import"./@vueuse-af86c621.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./quasar-b3f06d8a.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./dayjs-d60cc07f.js";import"./vxe-table-plugin-element-7bbbb435.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./dom-zindex-5f662ad1.js";import"./screenfull-c82f2093.js";const fe=e=>{let a=e.slice(1);return/^(https?:|mailto:|tel:)/.test(a)},he=["href"],ge=P({__name:"SidebarItemLink",props:{to:{}},setup(e){const a=e;return(e,t)=>{const s=C("router-link");return L(fe)(a.to)?(R(),S("a",{key:0,href:a.to.slice(1),target:"_blank",rel:"noopener"},[W(e.$slots,"default")],8,he)):(R(),q(s,{key:1,to:a.to},{default:D((()=>[W(e.$slots,"default")])),_:3},8,["to"]))}}}),ye={key:2},_e=t(P({__name:"SidebarItem",props:{item:{},isCollapse:{type:Boolean,default:!1},isFirstLevel:{type:Boolean,default:!0},basePath:{default:""}},setup(e){const t=e,s=U((()=>{var e;return null==(e=t.item.meta)?void 0:e.alwaysShow})),l=U((()=>{var e;return(null==(e=t.item.children)?void 0:e.filter((e=>{var a;return!(null==(a=e.meta)?void 0:a.hidden)})))??[]})),o=U((()=>l.value.length)),r=U((()=>{const e=o.value;switch(!0){case e>1:return null;case 1===e:return l.value[0];default:return{...t.item,path:""}}})),i=e=>{switch(!0){case fe(e):return e;case fe(t.basePath):return t.basePath;default:return ie.resolve(t.basePath,e)}};return(e,l)=>{var o;const n=a,u=c,d=C("sidebar-item",!0),m=p;return(null==(o=t.item.meta)?void 0:o.hidden)?N("",!0):(R(),S("div",{key:0,class:F({"simple-mode":t.isCollapse,"first-level":t.isFirstLevel})},[s.value||!r.value||r.value.children?(R(),q(m,{key:1,index:i(t.item.path),teleported:""},{title:D((()=>{var e,a,s;return[(null==(e=t.item.meta)?void 0:e.svgIcon)?(R(),q(n,{key:0,name:t.item.meta.svgIcon},null,8,["name"])):(null==(a=t.item.meta)?void 0:a.elIcon)?(R(),q(A(t.item.meta.elIcon),{key:1,class:"el-icon"})):N("",!0),(null==(s=t.item.meta)?void 0:s.title)?(R(),S("span",ye,E(t.item.meta.title),1)):N("",!0)]})),default:D((()=>[t.item.children?(R(!0),S(M,{key:0},T(t.item.children,(e=>(R(),q(d,{key:e.path,item:e,"is-collapse":t.isCollapse,"is-first-level":!1,"base-path":i(e.path)},null,8,["item","is-collapse","base-path"])))),128)):N("",!0)])),_:1},8,["index"])):(R(),S(M,{key:0},[r.value.meta?(R(),q(ge,{key:0,to:i(r.value.path)},{default:D((()=>[z(u,{index:i(r.value.path)},$({default:D((()=>[r.value.meta.svgIcon?(R(),q(n,{key:0,name:r.value.meta.svgIcon},null,8,["name"])):r.value.meta.elIcon?(R(),q(A(r.value.meta.elIcon),{key:1,class:"el-icon"})):N("",!0)])),_:2},[r.value.meta.title?{name:"title",fn:D((()=>[B(E(r.value.meta.title),1)])),key:"0"}:void 0]),1032,["index"])])),_:1},8,["to"])):N("",!0)],64))],2))}}}),[["__scopeId","data-v-9637ef6a"]]),be=e=>{let a="";try{a=getComputedStyle(document.documentElement).getPropertyValue(e)}catch(t){}return a},we={class:"box"},ke=t(P({__name:"index",setup(a){const t=be("--v3-sidebar-menu-bg-color"),r=be("--v3-sidebar-menu-text-color"),i=be("--v3-sidebar-menu-active-text-color"),n=x(),u=s(),d=l(),c=o(),{layoutMode:p,showLogo:f}=e(c),h=U((()=>{const{meta:{activeMenu:e},path:a}=n;return e||a})),g=()=>{u.toggleSidebar(!1)},y=U((()=>!u.sidebar.opened)),_=U((()=>"left"===p.value));U((()=>!1));const b=U((()=>_.value?t:void 0)),w=U((()=>_.value?r:void 0)),k=U((()=>_.value?i:void 0));return(e,a)=>{const t=m,s=v;return R(),S("div",we,[z(s,{"wrap-class":"scrollbar-wrapper"},{default:D((()=>[z(t,{"default-active":h.value,collapse:!1,"background-color":b.value,"text-color":w.value,"active-text-color":k.value,"unique-opened":!0,"collapse-transition":!1,mode:"vertical"},{default:D((()=>[(R(!0),S(M,null,T(L(d).routes,(e=>(R(),q(_e,{key:e.path,item:e,"base-path":e.path,"is-collapse":!1},null,8,["item","base-path"])))),128))])),_:1},8,["default-active","background-color","text-color","active-text-color"])])),_:1}),H("div",{class:F(["arrow",y.value?"arrowRight":"arrowLeft"]),onClick:g},null,2)])}}}),[["__scopeId","data-v-7d36606a"]]),je=["src"],xe=["src"],Ie=t({__name:"index",props:{collapse:{type:Boolean,default:!0}},setup(e){const a=e,t=O("top"),s=r();s.getUser();const l=O(s.userInfo.imgUrl),o=O(s.userInfo.imgUrl);return(e,s)=>{const r=C("router-link");return R(),S("div",{class:F(["layout-logo-container",{collapse:a.collapse,"layout-mode-top":"top"===t.value}])},[z(Y,{name:"layout-logo-fade"},{default:D((()=>[a.collapse?(R(),q(r,{key:"collapse",to:"/"},{default:D((()=>[H("img",{src:e.logo,class:"layout-logo",alt:"企业logo"},null,8,je)])),_:1})):(R(),q(r,{key:"expand",to:"/"},{default:D((()=>[H("img",{src:"left"!==t.value?o.value:l.value,class:"layout-logo-text",alt:"企业logo"},null,8,xe)])),_:1}))])),_:1})],2)}}},[["__scopeId","data-v-46f8953f"]]),Ve={key:0,class:"small-title no-redirect"},Pe=["onClick"],Ce=t(P({__name:"index",setup(e){const a=x(),t=I(),s=O([]),l=()=>{s.value=a.matched.filter((e=>{var a,t;return(null==(a=e.meta)?void 0:a.title)&&!1!==(null==(t=e.meta)?void 0:t.breadcrumb)}))},o=e=>{const{redirect:s,path:l}=e;s?t.push(s):t.push((e=>ne(e)(a.params))(l))};return X((()=>a.path),(e=>{e.startsWith("/redirect/")||l()})),l(),(e,a)=>{const t=f,l=h;return R(),q(l,{class:"app-breadcrumb","separator-icon":"ArrowRight"},{default:D((()=>[(R(!0),S(M,null,T(s.value,((e,a)=>(R(),q(t,{key:e.path},{default:D((()=>["noRedirect"===e.redirect||a===s.value.length-1?(R(),S("span",Ve,E(e.meta.title),1)):(R(),S("a",{key:1,class:"small-title",onClick:Z((a=>o(e)),["prevent"])},E(e.meta.title),9,Pe))])),_:2},1024)))),128))])),_:1})}}}),[["__scopeId","data-v-70973952"]]),Le=e=>(K("data-v-3e0a0589"),e=e(),Q(),e),Re={class:"right-menu-avatar"},Se={class:"small-title"},We=Le((()=>H("span",{class:"body-small-text",style:{display:"block"}},"退出登录",-1))),qe={key:1,class:"w-full u-flex-center-no gap-10"},De={class:"upLoad u-flex-center-no u-border"},Ue={key:0,class:"img-box u-flex-center-no"},Me=["src"],ze={class:"img-remove u-flex-center-no"},$e={class:"notice flex-1 u-flex-column gap-10 justify-between"},Be={class:"body-text"},Ee=Le((()=>H("span",null,"当前所属机构：",-1))),Ae=Le((()=>H("p",{class:"body-small-text"},[H("span",null,"注意事项："),H("span",null,"图片格式需为png、jpg格式，图片大小不超过2M")],-1))),Ne={class:"u-text-right"},Te=t({__name:"index",setup(a){const t=r(),l=s();e(l);const o=O(),d=O(!1),c=O(!1),p=G({type:"password",modelData:{oldPass:"",newPass:""},imgPath:"",fileList:[]}),m=G({oldPass:[{required:!0,message:"请输入原密码"}],newPass:[{required:!0,message:"请输入新密码"}]}),v=()=>{p.imgPath="",p.modelData={oldPass:"",newPass:""}},f=async()=>{c.value=!0;try{const e=await i(p.modelData);c.value=!1,"00000"==e.status&&(d.value=!1,p.modelData={oldPass:"",newPass:""})}catch(e){c.value=!1}},h=async e=>{if(0==p.fileList.length)return void ue.modal.message({content:"请先上传图片",status:"info"});const a=p.fileList[0].raw;try{const e=await n(t.userInfo.projectID,a);if("00000"==e.status){d.value=!1;t.userInfo,e.data.url}}catch(s){}},j=e=>{p.imgPath=URL.createObjectURL(e.raw)},x=()=>{p.imgPath="",p.fileList=[],ue.modal.message({content:"清除成功",status:"info"})},I=async()=>{"confirm"===await ue.modal.confirm("您确定要退出登陆吗?")&&(u().clearLocalStorage(),t.logout())};return(e,a)=>{const s=g,l=y,r=_,i=b,n=C("vxe-input"),u=C("vxe-form-item"),V=C("vxe-button"),P=C("vxe-form"),W=C("Delete"),U=w,$=C("Plus"),A=k,N=C("vxe-modal");return R(),S(M,null,[H("div",null,[z(i,{class:"right-menu-item"},{dropdown:D((()=>[z(r,null,{default:D((()=>[z(l,{divided:"",onClick:I},{default:D((()=>[We])),_:1})])),_:1})])),default:D((()=>{var e;return[H("div",Re,[L(t).userInfo.avatar&&""!=L(t).userInfo.avatar?(R(),q(s,{key:1,src:L(t).userInfo.avatar,class:"avatar"},null,8,["src"])):(R(),q(s,{key:0,icon:L(de),class:"avatar"},null,8,["icon"])),H("span",Se,E(null==(e=L(t).userInfo)?void 0:e.username),1)])]})),_:1})]),z(N,{modelValue:L(d),"onUpdate:modelValue":a[1]||(a[1]=e=>J(d)?d.value=e:null),title:"logo"==L(p).type?"logo上传":"修改密码",width:"600","min-width":"400","min-height":"300",loading:L(c),resize:"","destroy-on-close":"",onHide:v},{default:D((()=>["password"==L(p).type?(R(),q(P,{key:0,data:L(p).modelData,rules:L(m),"title-align":"right","title-width":"100",onSubmit:f},{default:D((()=>[z(u,{field:"oldPass",title:"原密码",span:24,"item-render":{}},{default:D((({data:e})=>[z(n,{modelValue:e.oldPass,"onUpdate:modelValue":a=>e.oldPass=a,type:"password",placeholder:"请输入原密码"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),z(u,{field:"newPass",title:"新密码",span:24,"item-render":{}},{default:D((({data:e})=>[z(n,{modelValue:e.newPass,"onUpdate:modelValue":a=>e.newPass=a,type:"password",placeholder:"请输入新密码"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),z(u,{align:"right","title-align":"left",span:24},{default:D((()=>[z(V,{type:"submit"},{default:D((()=>[B("提交")])),_:1})])),_:1})])),_:1},8,["data","rules"])):(R(),S("div",qe,[H("div",De,[z(A,{ref_key:"uploadRef",ref:o,action:"","auto-upload":!1,"show-file-list":!1,limit:1,"file-list":L(p).fileList,"onUpdate:fileList":a[0]||(a[0]=e=>L(p).fileList=e),"on-change":j},{default:D((()=>[L(p).imgPath?(R(),S("div",Ue,[H("img",{src:L(p).imgPath},null,8,Me),H("span",ze,[z(U,{size:30,onClick:Z(x,["stop"])},{default:D((()=>[z(W)])),_:1})])])):(R(),q(U,{key:1,size:30,class:"avatar-uploader-icon"},{default:D((()=>[z($)])),_:1}))])),_:1},8,["file-list"])]),H("div",$e,[H("p",Be,[Ee,H("span",null,E(L(t).userInfo.projectName),1)]),Ae,H("p",Ne,[z(V,{onClick:h},{default:D((()=>[B("提交")])),_:1})])])]))])),_:1},8,["modelValue","title","loading"])],64)}}},[["__scopeId","data-v-3e0a0589"]]),Fe={class:"navigation-bar"},He={class:"right-menu"},Oe=t({__name:"index",setup(a){I();const t=s(),l=o();r(),e(t),e(t);const{showNotify:i,showThemeSwitch:n,showScreenfull:u}=e(l);return O(!1),O(!1),(e,a)=>(R(),S("div",Fe,[z(Ce,{class:"breadcrumb"}),H("div",He,[L(u)?(R(),q(ve,{key:0})):N("",!0),z(Te)])]))}},[["__scopeId","data-v-ca31fbef"]]),Ye={class:"app-main"},Xe={class:"app-scrollbar"},Ze=t(P({__name:"appMain",setup(e){const a=x(),t=d(),s=U((()=>a.path));return(e,a)=>{const l=C("router-view"),o=j;return R(),S("section",Ye,[H("div",Xe,[z(l,null,{default:D((({Component:e,route:a})=>[z(Y,{name:"el-fade-in",mode:"out-in"},{default:D((()=>[(R(),q(ee,{include:L(t).cachedViews},[(R(),q(A(e),{key:L(s),class:"app-container-grow"}))],1032,["include"]))])),_:2},1024)])),_:1})]),z(o),z(o,{target:".app-scrollbar"})])}}}),[["__scopeId","data-v-88cd012f"]]),Ge={class:"scroll-container"},Je=t(P({__name:"ScrollPane",props:{tagRefs:{}},setup(e){const a=e,t=x(),s=o(),l=O(),r=O();let i=0;const n=({scrollLeft:e})=>{i=e},u=({deltaY:e})=>{/^-/.test(e.toString())?c("left"):c("right")},d=()=>{const e=r.value.clientWidth,a=l.value.wrapRef.clientWidth;return{scrollbarContentRefWidth:e,scrollbarRefWidth:a,lastDistance:e-a-i}},c=(e,a=200)=>{let t=0;const{scrollbarContentRefWidth:s,scrollbarRefWidth:o,lastDistance:r}=d();o>s||(t="left"===e?Math.max(0,i-a):Math.min(i+a,i+r),l.value.setScrollLeft(t))},p=()=>{const e=a.tagRefs;for(let a=0;a<e.length;a++)if(t.path===e[a].$props.to.path){const t=e[a].$el,s=t.offsetWidth,l=t.offsetLeft,{scrollbarRefWidth:o}=d();if(l<i){return void c("left",i-l)}const r=o+i-s;if(l>r){return void c("right",l-r)}}};return X(t,(()=>{ae(p)}),{deep:!0}),(e,a)=>{const t=w;return R(),S("div",Ge,[z(t,{class:"arrow left",onClick:a[0]||(a[0]=e=>c("left"))},{default:D((()=>[z(L(ce))])),_:1}),z(L(v),{ref_key:"scrollbarRef",ref:l,onWheelPassive:u,onScroll:n},{default:D((()=>[H("div",{ref_key:"scrollbarContentRef",ref:r,class:"scrollbar-content"},[W(e.$slots,"default",{},void 0,!0)],512)])),_:3},512),z(t,{class:"arrow right",onClick:a[1]||(a[1]=e=>c("right"))},{default:D((()=>[z(L(pe))])),_:1}),L(s).showScreenfull?(R(),q(ve,{key:0,element:".screenfull-content",content:!0,class:"screenfull"})):N("",!0)])}}}),[["__scopeId","data-v-5de1cb31"]]),Ke={class:"tags-view-container"},Qe=t(P({__name:"index",setup(e){const a=te(),t=I(),s=x(),o=d(),r=l(),i=O([]),n=O(!1),u=O(0),c=O(0),p=O({});let m=[];const v=e=>e.path===s.path,f=e=>{var a;return null==(a=e.meta)?void 0:a.affix},h=(e,a="/")=>{const t=[];return e.forEach((e=>{Number(localStorage.getItem("routeEdition"));if(f(e)){const s=ie.resolve(a,e.path);t.push({fullPath:s,path:s,name:e.name,meta:{...e.meta}})}if(e.children){const a=h(e.children,e.path);t.push(...a)}})),t},g=()=>{s.name&&(o.addVisitedView(s),o.addCachedView(s))},y=e=>{o.delVisitedView(e),o.delCachedView(e),v(e)&&b(o.visitedViews,e)},_=()=>{const e=p.value.fullPath;e!==s.path&&void 0!==e&&t.push(e),o.delOthersVisitedViews(p.value),o.delOthersCachedViews(p.value)},b=(e,a)=>{const s=e.slice(-1)[0],l=null==s?void 0:s.fullPath;void 0!==l?t.push(l):"Dashboard"===a.name?t.push({path:"/redirect"+a.path,query:a.query}):t.push("/interface")},k=()=>{n.value=!1};return X(s,(()=>{g()}),{deep:!0}),X(n,(e=>{e?document.body.addEventListener("click",k):document.body.removeEventListener("click",k)})),se((()=>{(()=>{m=h(r.routes);for(const e of m)e.name&&o.addVisitedView(e)})(),g()})),(e,t)=>{const l=w;return R(),S("div",Ke,[z(Je,{class:"tags-view-wrapper","tag-refs":i.value},{default:D((()=>[(R(!0),S(M,null,T(L(o).visitedViews,(e=>(R(),q(L(V),{ref_for:!0,ref_key:"tagRefs",ref:i,key:e.path,class:F([{active:v(e)},"tags-view-item"]),to:{path:e.path,query:e.query},onMouseup:Z((a=>!f(e)&&y(e)),["middle"]),onContextmenu:Z((t=>((e,t)=>{const s=a.proxy.$el.getBoundingClientRect().left,l=a.proxy.$el.offsetWidth-105,o=t.clientX-s+15;c.value=o>l?l:o,u.value=t.clientY,n.value=!0,p.value=e})(e,t)),["prevent"])},{default:D((()=>{var a;return[B(E(null==(a=e.meta)?void 0:a.title)+" ",1),f(e)?N("",!0):(R(),q(l,{key:0,class:"tagIcon",onClick:Z((a=>y(e)),["prevent","stop"])},{default:D((()=>[z(L(me))])),_:2},1032,["onClick"]))]})),_:2},1032,["class","to","onMouseup","onContextmenu"])))),128))])),_:1},8,["tag-refs"]),le(H("ul",{class:"contextmenu",style:re({left:c.value+"px",top:u.value+"px"})},[f(p.value)?N("",!0):(R(),S("li",{key:0,onClick:t[0]||(t[0]=e=>y(p.value))},"关闭")),H("li",{onClick:_},"关闭其它"),H("li",{onClick:t[1]||(t[1]=e=>{return a=p.value,o.delAllVisitedViews(),o.delAllCachedViews(),void(m.some((e=>e.path===s.path))||b(o.visitedViews,a));var a})},"关闭所有")],4),[[oe,n.value]])])}}}),[["__scopeId","data-v-818c833e"]]),ea={class:"fixed-header layout-header"},aa={class:"flex"},ta={class:"content"},sa=t({__name:"LeftTopMode",setup(a){const t=s(),l=o(),{showTagsView:r,showLogo:i}=e(l),n=U((()=>!t.sidebar.opened)),u=U((()=>({hideSidebar:!t.sidebar.opened})));return(e,a)=>(R(),S("div",{class:F([u.value,"app-wrapper"])},[H("div",ea,[H("div",aa,[L(i)?(R(),q(Ie,{key:0,collapse:!1,class:"logo"})):N("",!0),H("div",ta,[z(Oe)])]),le(z(Qe,{class:F(["tag-view-normal",n.value?"":"tag-view-collapse"])},null,8,["class"]),[[oe,L(r)]])]),H("div",{class:F([{hasTagsView:!0},"main-container"])},[z(ke,{class:F(["sidebar-container",n.value?"collapse-sidebar":""])},null,8,["class"]),z(Ze,{class:"app-main"})])],2))}},[["__scopeId","data-v-7631c467"]]);export{sa as default};
