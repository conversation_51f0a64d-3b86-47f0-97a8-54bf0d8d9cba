import{i as e}from"./index-8cc8d4b8.js";function t(t){return e({url:"/system/deviceManage/operator/list",method:"post",data:{...t}})}function a(t){return e({url:"/device/export/exportOperatorInfo",method:"post",responseType:"blob",data:{...t}})}function o(t,a){return e({url:"/device/getIotCardByPage",method:"post",data:{iccid:t,plantUid:a,currentPage:1,pageSize:50}})}const r={"正常运行":"bg67c23a","离线":"bg909399","告警运行":"bgf56c6c","自检提示":"bge6a23c","夜间离线":"bg409eff"},s={"正常":"success","已过期":"info"};export{r as O,s as S,a,o as b,t as g};
