var t=Object.defineProperty,e=(e,n,i)=>(((e,n,i)=>{n in e?t(e,n,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[n]=i})(e,"symbol"!=typeof n?n+"":n,i),i);import{_ as n,u as i,a}from"./index-8cc8d4b8.js";import{_ as s}from"./index-f2383b94.js";import{G as l,X as o}from"./element-plus-d975be09.js";import"./vue-5bfa3a54.js";import{g as r}from"./imgImport-3dead1a5.js";import{e as c,v as u}from"./girdConnectedCabinet-7ccbd3a2.js";import{T as d,S as m,C as v,V as p,L as f,a as g,R as h,b as w,c as x,W as y,P as b,A as C,d as _,e as j,M as A,G as M,B as W,f as z,g as S,D as B,h as I,i as E}from"./three-59a86278.js";import{j as D,h as F,w as P,m as k,v as R,o as U,c as N,a as Q,t as O,y as Y,f as V,a8 as H,aa as L,b as T,l as J,x as G,F as Z,k as K,C as X,D as $}from"./@vue-5e5cdef9.js";import{g as q}from"./index-791aa09c.js";import{d as tt}from"./dayjs-d60cc07f.js";import{G as et,H as nt,d as it}from"./@vueuse-af86c621.js";import{x as at}from"./xe-utils-fe99d42a.js";import{i as st}from"./echarts-f30da64f.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./vue-router-6159329f.js";import"./bignumber.js-a537a5ca.js";import"./js-cookie-8253c38e.js";import"./axios-84f1a956.js";import"./lodash-es-ea7deab5.js";import"./spark-md5-022b35d0.js";import"./@babel-f3c0a00c.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";import"./quasar-b3f06d8a.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./@element-plus-4c34063a.js";import"./vxe-table-plugin-element-7bbbb435.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./lodash-6d99edc3.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./screenfull-c82f2093.js";import"./chartResize-3e3d11d7.js";import"./element-resize-detector-0d37a2ab.js";import"./batch-processor-06abf2b4.js";const lt={floor:new URL(""+new URL("../jpg/floor-e50a3342.jpg",import.meta.url).href,self.location).href,b1:new URL(""+new URL("../png/1-a48616a1.png",import.meta.url).href,self.location).href,b2:new URL(""+new URL("../jpg/2-635f67ed.jpg",import.meta.url).href,self.location).href,b3:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAPMAAAB0CAYAAABQQwRfAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsQAAA7EAZUrDhsAAAARdEVYdFNvZnR3YXJlAFNuaXBhc3RlXRfO3QAAAYpJREFUeF7t07kNhEAQAEG4nLDxCBoPm6C4R6vLYVtVzjx+r+d1PwswvV/Mx76NE5jVP+bPHC9gNt+GX2MHJidmiBAzRIgZIsQMEWKGCDFDhJghQswQIWaIEDNEiBkixAwRYoYIMUOEmCFCzBAhZogQM0SIGSLEDBFihggxQ4SYIULMECFmiBAzRIgZIsQMEWKGCDFDhJghQswQIWaIEDNEiBkixAwRYoYIMUOEmCFCzBAhZogQM0SIGSLEDBFihggxQ4SYIULMECFmiBAzRIgZIsQMEWKGCDFDhJghQswQIWaIEDNEiBkixAwRYoYIMUOEmCFCzBAhZogQM0SIGSLEDBFihggxQ4SYIULMECFmiBAzRIgZIsQMEWKGCDFDhJghQswQIWaIEDNEiBkixAwRYoYIMUOEmCFCzBAhZogQM0SIGSLEDBFihggxQ4SYIULMECFmiBAzRIgZIsQMEWKGCDFDhJghQswQIWaIEDNEiBkixAwRYoYIMUOEmCFiPa/7OfZtnMCcluUNNVgTJ/YBKeIAAAAASUVORK5CYII=",self.location).href};class ot{constructor(t,n){e(this,"shaderMat"),e(this,"planeMat"),e(this,"updateScan"),e(this,"shaderMatArr"),e(this,"radius"),e(this,"width"),this.radius=n,this.width=t,(new d).load(lt.floor);const i=(new d).load(lt.b1);(new d).load(lt.b2),(new d).load(lt.b3),this.shaderMat=new m({uniforms:{innerCircleWidth:{value:0},circleWidth:{value:t},color:{value:new v(.8,.85,.9)},center:{value:new p(0,0,0)},texture1:{value:i}},vertexShader:"\n\t\t\tvarying vec2 vUv;\n\t\t\tvarying vec4 v_position;\n\t\t\tvarying vec3 v_normal;\n\t\t\tvoid main() {\n\t\t\t\tvUv = uv;\n\t\t\t\tv_position = modelMatrix * vec4(position, 1.0);\n\t\t\t\tv_normal = normal;\n\t\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n\t\t\t}\n\t\t\t",fragmentShader:"\n\t\t\tvarying vec2 vUv;\n\t\t\tvarying vec4 v_position;\n\t\t\tvarying vec3 v_normal;\n\t\n\t\t\tuniform float innerCircleWidth;\n\t\t\tuniform float circleWidth;\n\t\t\tuniform vec3 center;\n\t\t\tuniform vec3 color;\n      uniform sampler2D texture1;\n\t\n\t\t\tvoid main() {\n\t\t\t\tfloat dis = length(v_position.xyz - center);\n\t\t\t\tvec4 buildingColor = vec4(0.2,0.3,0.4,0.6);\n        vec4 textureColor = texture(texture1,vUv);\n\t\t\t\tvec4 lightColor = vec4(0.2);\n\t\t\t\tvec3 lightDir = vec3(1.0,1.0,0.5);\n\t\t\t\tfloat c = dot(lightDir,v_normal);\n        float r = 1.0- smoothstep(50.0,800.0,dis);\n\t\t\t\tfloat col = smoothstep(innerCircleWidth-circleWidth,innerCircleWidth,dis) - smoothstep(innerCircleWidth,innerCircleWidth+circleWidth,dis);\n\t\t\t\tvec4 scanColor = mix(buildingColor * r,vec4(color, 1.0),col);\n\t\t\t\tscanColor += lightColor*c + vec4(0.05);\n\t\t\t\tgl_FragColor = scanColor + vec4(textureColor.xyz * 2.5, 1.0);\n\t\t\t}\n\t\t\t"}),this.shaderMatArr=[];["b1","b2","b3","b3","b3","b3"].forEach((e=>{let n=(new d).load(lt[e]);n.minFilter=f,n.magFilter=g,n.wrapS=h,n.wrapT=h;let i=new m({uniforms:{innerCircleWidth:{value:0},circleWidth:{value:t},color:{value:new v("#6ff1f4")},center:{value:new p(0,0,0)},texture1:{value:n}},vertexShader:"\n        varying vec2 vUv;\n        varying vec4 v_position;\n        varying vec3 v_normal;\n        void main() {\n          vUv = uv;\n          v_position = modelMatrix * vec4(position, 0.2);\n          v_normal = normal;\n          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n        }\n        ",fragmentShader:"\n        varying vec2 vUv;\n        varying vec4 v_position;\n        varying vec3 v_normal;\n    \n        uniform float innerCircleWidth;\n        uniform float circleWidth;\n        uniform vec3 center;\n        uniform vec3 color;\n        uniform sampler2D texture1;\n    \n        void main() {\n          float dis = length(v_position.xyz - center);\n        \n          // 直接使用原始纹理颜色，不改变其亮度\n          vec4 textureColor = texture(texture1, vUv);\n        \n          // 优化光线方向以匹配预期的光线效果\n          vec3 lightDir = normalize(vec3(0.5, 0.8, 1.0)); // 示例光线方向，可根据需求调整\n\n          // 计算光照强度，仅当光线方向与法线方向相同才增加光照\n          float c = max(dot(lightDir, v_normal), 0.0);\n\n          // 光波扫描的混合逻辑\n          float r = 1.0 - smoothstep(50.0, 800.0, dis);\n          float col = smoothstep(innerCircleWidth - circleWidth, innerCircleWidth, dis) - smoothstep(innerCircleWidth, innerCircleWidth + circleWidth, dis);\n\n          // 创建一个仅用于表示光线扫描影响的颜色，这里使用传入的color并根据光照强度调整\n          vec4 lightScanColor = vec4(color, 1.0) * c;\n        \n          // 混合原始纹理颜色与光波扫描产生的颜色\n          // 当光波扫过时（col接近1），lightScanColor的影响增加；否则，主要显示原始纹理颜色\n          gl_FragColor = mix(textureColor, textureColor + lightScanColor, col);\n\n        }\n        "});this.shaderMatArr.push(i)})),this.planeMat=new m({uniforms:{innerCircleWidth:{value:0},circleWidth:{value:t},diff:{value:new v(.2,.2,.2)},color:{value:new v("#da242a")},opacity:{value:.9},center:{value:new p(0,0,0)}},vertexShader:"\n\t\t\tvarying vec2 vUv;\n\t\t\tvarying vec3 v_position;\n\t\t\tvoid main() {\n\t\t\t\tvUv = uv;\n\t\t\t\tv_position = position;\n\t\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n\t\t\t}\n\t\t\t",fragmentShader:"\n\t\t\tvarying vec2 vUv;\n\t\t\tvarying vec3 v_position;\n\t\n\t\t\tuniform float innerCircleWidth;\n\t\t\tuniform float circleWidth;\n\t\t\tuniform float opacity;\n\t\t\tuniform vec3 center;\n\t\t\n\t\t\tuniform vec3 color;\n\t\t\tuniform vec3 diff;\n\t\t\tbool hex(vec2 p) {\n\t\t\t\tp.x *= 0.57735*2.0;\n\t\t\t\tp.y += mod(floor(p.x), 2.0)*0.5;\n\t\t\t\tp = abs((mod(p, 1.0) - 0.5));\n\t\t\t\treturn abs(max(p.x*1.5 + p.y, p.y*2.0) - 1.0) > 0.05;\n\t\t    }\n\t\t\tvoid main() {\n\t\t\t\tfloat dis = length(v_position - center);\n\t\t\t\tbool h = hex(vUv*100.0);\n\t\t\t\tfloat col = smoothstep(innerCircleWidth-circleWidth,innerCircleWidth,dis) - smoothstep(innerCircleWidth,innerCircleWidth+circleWidth,dis);\n\t\t\t\tvec4 finalColor = 1.0- mix(vec4(0.9),vec4(color, opacity),col);\n\t\t\t\tfloat r = 1.0- smoothstep(50.0,1000.0,dis);\n\t\t\n\t\t\t\tfloat hh;\n\t\t\t\tif(h){\n\t\t\t\t\thh = float(h);\n\t\t\t\t\tgl_FragColor = finalColor + vec4(hh) * r * 0.6 + (1.0-r) * vec4(vec3(0.001),1.0);\n\t\t\t\t}else{\n\t\t\t\t\tgl_FragColor = vec4(0.0);     \n\t\t\t\t}\n\t\t\t}\n\t\t\t",transparent:!0}),this.updateScan=()=>{this.shaderMat.uniforms.innerCircleWidth.value+=.1,this.shaderMat.uniforms.innerCircleWidth.value>10&&(this.shaderMat.uniforms.innerCircleWidth.value=-this.radius),this.shaderMatArr.forEach((t=>{t.uniforms.innerCircleWidth.value+=.1,t.uniforms.innerCircleWidth.value>15&&(t.uniforms.innerCircleWidth.value=-this.radius)})),this.planeMat.uniforms.innerCircleWidth.value+=.1,this.planeMat.uniforms.innerCircleWidth.value>15&&(this.planeMat.uniforms.innerCircleWidth.value=-this.radius)}}}const rt={id:"modelBox"},ct=n({__name:"bingwang",props:{resize:{type:Boolean}},setup(t){const e=t,n=D({show:!1,data:""}),i=F(!1),a={scene:null,light:null,axesHelper:null,renderer:null,modelDom:null,controls:null,mouse:null,raycaster:null,animateId:null,tween:null},s={bingwang:null,normal:null,scan:null,wall:null},l={sel:[],scanB:[],scanH:null,selBingW:null,startPosition:null,endPosition:null},o={uperVertext:"\n      varying vec3 vPosition;\n      void main()\n      {\n        vPosition = position;\n        gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.1 );\n      }\n    ",uperFragment:"\n      varying vec3 vPosition;\n        uniform float height;\n        uniform vec4 uFlowColor;\n        uniform vec4 uModelColor;\n      void main()\n      {\n        //模型的基础颜色\n       vec4 distColor=uModelColor;\n      // 流动范围当前点z的高度加上流动线的高度\n       float topY = vPosition.y + 0.05;\n      if (height > vPosition.y && height < topY) {\n       // 颜色渐变 \n        distColor = uFlowColor; \n      }\n\n       gl_FragColor = distColor;\n      }",scanBshader:{vertexShader:"\n    varying vec2 v_position;\n    \n    void main() {\n        v_position = vec2(position.x, position.y);\n        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n    }",fragmentShader:"\n    precision mediump float;\n      \n    float atan2(float y, float x){\n      float t0, t1, t2, t3, t4;\n      t3 = abs(x);\n      t1 = abs(y);\n      t0 = max(t3, t1);\n      t1 = min(t3, t1);\n      t3 = float(1) / t0;\n      t3 = t1 * t3;\n      t4 = t3 * t3;\n      t0 = -float(0.013480470);\n      t0 = t0 * t4 + float(0.057477314);\n      t0 = t0 * t4 - float(0.121239071);\n      t0 = t0 * t4 + float(0.195635925);\n      t0 = t0 * t4 - float(0.332994597);\n      t0 = t0 * t4 + float(0.999995630);\n      t3 = t0 * t3;\n      t3 = (abs(y) > abs(x)) ? float(1.570796327) - t3 : t3;\n      t3 = (x < 0.0) ?  float(3.141592654) - t3 : t3;\n      t3 = (y < 0.0) ? -t3 : t3;\n      return t3;\n    }\n    // 计算距离\n    float distanceTo(vec2 src, vec2 dst) {\n      float dx = src.x - dst.x;\n      float dy = src.y - dst.y;\n      float dv = dx * dx + dy * dy;\n      return sqrt(dv);\n    }\n    \n    #define PI 3.14159265359\n    #define PI2 6.28318530718\n    \n    uniform vec3 u_color;\n    uniform float time;\n    uniform float u_opacity;\n    uniform float u_radius;\n    uniform float u_width;\n    uniform float u_speed;\n    \n    varying vec2 v_position;\n    void main() {\n        float d_time = u_speed * time;\n\n        float angle = atan2(v_position.x, v_position.y) + PI;\n        \n        float angleT = mod(angle + d_time, PI2);\n\n        float width = u_width;\n    \n        float d_opacity = 0.0;\n\n        // 当前位置离中心位置\n        float length = distanceTo(vec2(0.0, 0.0), v_position);\n        \n        float bw = 5.0;\n        if (length < u_radius && length > u_radius - bw) {\n            float o = (length - (u_radius - bw)) / bw;\n            d_opacity = sin(o * PI); \n        }\n\n        if (length < u_radius - bw / 1.1) {\n            d_opacity = 1.0 - angleT / PI * (PI / width);\n        } \n\n        if (length > u_radius) { d_opacity = 0.0; }\n \n        gl_FragColor = vec4(u_color, d_opacity * u_opacity);\n    }"},halo:{planeMat:"",shaderMat:""}},r={position:{"并网柜1":{x:5.5,y:1,z:4},"并网柜2":{x:5.5,y:1,z:4},"并网柜3":{x:5.5,y:1,z:-4},"并网柜4":{x:5.5,y:1,z:-4}},scanConfig:{value:1,start:0,end:0,during:3},scanB:[{position:{x:0,y:-3.5,z:0},radius:10,color:"#6ff1f4",opacity:.6,speed:1}]},c=()=>{if(a.animateId=requestAnimationFrame(c),s.scan){let t=r.scanConfig.end-r.scanConfig.start;r.scanConfig.value+=t/r.scanConfig.during/60,r.scanConfig.value>=r.scanConfig.end&&(r.scanConfig.value=r.scanConfig.start)}l.scanB.length>0&&l.scanB.forEach((t=>{t.material.uniforms.time.value+=.2})),l.scanH&&l.scanH.updateScan(),w.update(),a.renderer.render(a.scene,a.camera)},u=()=>{a.modelDom=document.getElementById("modelBox"),a.scene=new x,a.renderer=new y({antialias:!0,alpha:!0}),a.renderer.setPixelRatio(window.devicePixelRatio),a.renderer.setSize(a.modelDom.clientWidth,a.modelDom.clientHeight),a.renderer.setClearColor(0,0),a.camera=new b(55,a.modelDom.clientWidth/a.modelDom.clientHeight,.1,1e3),a.camera.position.set(16,0,0),a.camera.lookAt(new p(0,0,0)),a.light=new C(16777215,.6),a.scene.add(a.light),a.modelDom.appendChild(a.renderer.domElement),(()=>{const t=(new d).load(lt.b1);t.minFilter=f,t.magFilter=g,t.wrapS=h,t.wrapT=h;const e=(new d).load(lt.b2);e.minFilter=f,e.magFilter=g,e.wrapS=h,e.wrapT=h;const n=(new d).load(lt.b3);n.minFilter=f,n.magFilter=g,n.wrapS=h,n.wrapT=h;const i=(new d).load(lt.b3);i.minFilter=f,i.magFilter=g,i.wrapS=h,i.wrapT=h;const a=(new d).load(lt.b3);a.minFilter=f,a.magFilter=g,a.wrapS=h,a.wrapT=h;const l=(new d).load(lt.b3);l.minFilter=f,l.magFilter=g,l.wrapS=h,l.wrapT=h,s.bingwang=[new A({map:t,transparent:!0,opacity:.5}),new A({map:e,transparent:!0,opacity:.5}),new A({map:n,transparent:!0,opacity:.5}),new A({map:i,transparent:!0,opacity:.5}),new A({map:a,transparent:!0,opacity:.5}),new A({map:l,transparent:!0,opacity:.5})],s.wall=new A({color:13487565}),s.normal=new A({color:16777215})})(),(()=>{l.scanH=new ot(3,10);let t={"并网柜1":[2,0,1.5],"并网柜2":[-2,0,-1.5],"并网柜3":[-2,0,1.5],"并网柜4":[2,0,-1.5]},e=[];for(let a=0;a<4;a++){let t=new M,n=new W(2,5,3,2,2,3),i=new W(3.05,5.05,3.05,2,2,3),o=new z(n,s.normal);o.name=`inter${a+1}`;let r=new z(i,l.scanH.shaderMatArr);r.name=`outer${a+1}`,t.add(o),t.add(r),t.name=`并网柜${a+1}`,e.push(t)}const n=new W(6.5,.1,6.5,2,1,2),i=new z(n,s.wall),o=new z(n,s.wall);let c=new M,u=new M;e.forEach(((e,n)=>{n<2?(c.add(e),e.position.set(...t[e.name])):(u.add(e),e.position.set(...t[e.name]))})),c.add(i),u.add(o),i.position.set(0,-2.6,0),o.position.set(0,-2.6,0),c.name="peidian1",u.name="peidian2",c.position.set(0,-1,-3.5),u.position.set(0,-1,3.5),a.scene.add(c),a.scene.add(u),0==l.scanB.length&&r.scanB.forEach((t=>{let e=H(t);l.scanB.push(e),e.material.uniforms.time={value:2},a.scene.add(e)}));const d=new S(12,100),m=new z(d,l.scanH.planeMat);m.rotateX(-Math.PI/2),m.position.y=-3.5,a.scene.add(m)})(),a.mouse=new _},V=(t,e)=>{t?(s.scan=new m({transparent:!0,side:B,uniforms:{height:r.scanConfig,uFlowColor:{value:new I(0,1,1,1)},uModelColor:{value:new I(1,1,1,0)}},vertexShader:o.uperVertext,fragmentShader:o.uperFragment}),s.scan.needsUpdate=!0,e.traverse((t=>{t.isMesh&&(t.material=s.scan)})),r.scanConfig.start=-2,r.scanConfig.end=2.45,r.scanConfig.value=r.scanConfig.start):(s.scan=null,e.traverse((t=>{t.isMesh&&(t.material=s.normal)})))},H=t=>{const{radius:e=10,color:n="#fff",speed:i=1,opacity:a=1,angle:s=Math.PI,position:l={x:0,y:0,z:0},rotation:r={x:-Math.PI/2,y:0,z:0}}=t,c=2*e,u=new E(c,c,1,1),d=new m({uniforms:{u_radius:{value:e},u_speed:{value:i},u_opacity:{value:a},u_width:{value:s},u_color:{value:new v(n)},time:{value:0}},transparent:!0,depthWrite:!1,side:B,vertexShader:o.scanBshader.vertexShader,fragmentShader:o.scanBshader.fragmentShader}),p=new z(u,d);return p.rotation.set(r.x,r.y,r.z),p.position.copy(l),p},L=(t,e)=>{if("move"==t)if(l.selBingW)a.tween=new w.Tween(l.endPosition).to(l.startPosition,1e3).easing(w.Easing.Quadratic.Out).onUpdate((t=>{l.selBingW.position.set(t.x,t.y,t.z)})).onComplete((()=>{let t=a.scene.getObjectByName(e);l.selBingW=t;let n=JSON.parse(JSON.stringify(t.position));l.startPosition={x:t.position.x,y:t.position.y,z:t.position.z},l.endPosition=JSON.parse(JSON.stringify(r.position[`${t.name}`])),a.tween=new w.Tween(l.startPosition).to(l.endPosition,1e3).easing(w.Easing.Quadratic.Out).onUpdate((e=>{t.position.set(e.x,e.y,e.z)})).onComplete((()=>{i.value=!1})).start(),l.startPosition={x:n.x,y:n.y,z:n.z}})).start();else{let t=a.scene.getObjectByName(e);l.selBingW=t;let n=JSON.parse(JSON.stringify(t.position));l.startPosition={x:t.position.x,y:t.position.y,z:t.position.z},l.endPosition=JSON.parse(JSON.stringify(r.position[`${t.name}`])),a.tween=new w.Tween(l.startPosition).to(l.endPosition,1e3).easing(w.Easing.Quadratic.Out).onUpdate((e=>{t.position.set(e.x,e.y,e.z)})).onComplete((()=>{i.value=!1})).start(),l.startPosition={x:n.x,y:n.y,z:n.z}}else a.tween=new w.Tween(l.endPosition).to(l.startPosition,1e3).easing(w.Easing.Quadratic.Out).onUpdate((t=>{l.selBingW.position.set(t.x,t.y,t.z)})).onComplete((()=>{l.selBingW=null,i.value=!1})).start()},T=()=>{a.animateId&&(cancelAnimationFrame(a.animateId),a.animateId=null)};return P((()=>e.resize),(t=>{a.renderer&&a.modelDom&&(T(),a.renderer.setSize(a.modelDom.clientWidth,a.modelDom.clientHeight),a.camera.aspect=a.modelDom.clientWidth/a.modelDom.clientHeight,a.camera.updateProjectionMatrix(),c())})),k((()=>{u(),c(),a.modelDom.addEventListener("click",(t=>{if(i.value)return;i.value=!0,a.mouse.x=(t.clientX-a.modelDom.getBoundingClientRect().left)/a.modelDom.offsetWidth*2-1,a.mouse.y=-(t.clientY-a.modelDom.getBoundingClientRect().top)/a.modelDom.offsetHeight*2+1;let e=new p(a.mouse.x,a.mouse.y,1).unproject(a.camera).sub(a.camera.position).normalize();a.raycaster=new j(a.camera.position,e);let o=a.raycaster.intersectObjects(a.scene.children,!0);if(o.length>0){let t=o.slice(0,2);l.sel=[...t,...l.sel],t.forEach((t=>{if(t.object.name.indexOf("inter")>-1){n.data=t.object.parent.name,n.show=!0;let e=a.scene.getObjectByName(t.object.name);V(!0,e),L("move",t.object.parent.name)}else t.object.name.indexOf("outer")>-1&&(t.object.material=s.bingwang)}))}else n.show=!1,l.sel.forEach((t=>{if(t.object.name.indexOf("inter")>-1){let e=a.scene.getObjectByName(t.object.name);V(!1,e)}else t.object.name.indexOf("outer")>-1&&(t.object.material=l.scanH.shaderMatArr)})),l.selBingW&&L("restore")}))})),R((()=>{T()})),(t,e)=>(U(),N("div",rt,[Q("div",{class:Y(["tip u-flex-center border",n.show?"tipShow":""])},O(n.data),3)]))}},[["__scopeId","data-v-bd6e18b8"]]),ut=n({__name:"switchStatusCmp",props:{status:{type:String,default:"normal"}},setup:t=>(e,n)=>{const i=l;return U(),V(i,{placement:"bottom"},{content:H((()=>[L(O("normal"===t.status?"正常":"告警"),1)])),default:H((()=>[Q("div",{class:Y(["circle","normal"===t.status?"normal":"warn"])},null,2)])),_:1})}},[["__scopeId","data-v-7a727ad7"]]),dt=t=>(X("data-v-26d5ce19"),t=t(),$(),t),mt={class:"header"},vt={class:"date font-small-text-size2 normal regular-title"},pt={class:"title"},ft={class:"main-title font-title-size text"},gt={class:"screen-full"},ht={class:"sensor-left"},wt={class:"item-title-bg font-title-size"},xt=["src"],yt={class:"sensor-box"},bt={key:0,class:"circle-img"},Ct=["src"],_t=["src"],jt={key:1,class:"circle-img"},At=["src"],Mt={class:"u-flex-column"},Wt={class:"font-small-text-size2"},zt={key:0},St={key:1},Bt={class:"smokeValue font-small-text-size1"},It={class:"smokeUnit font-small-text-size3"},Et={class:"sensor-right"},Dt={class:"item-title-bg font-title-size"},Ft=["src"],Pt={class:"sensor-box"},kt={key:0,class:"circle-img"},Rt=["src"],Ut=["src"],Nt={key:1,class:"circle-img"},Qt=["src"],Ot={class:"u-flex-column"},Yt={class:"font-small-text-size2"},Vt={key:0},Ht={key:1},Lt={class:"smokeValue font-small-text-size1"},Tt={class:"smokeUnit font-small-text-size3"},Jt={class:"distributionRoom-left"},Gt={class:"distributionBox body-text"},Zt={class:"item-title-bg font-title-size"},Kt=["src"],Xt=dt((()=>Q("span",null,"xx配电房-并网柜1",-1))),$t={class:"switchStatus sub-title"},qt=dt((()=>Q("div",{class:"sub-title"},"开关状态",-1))),te={class:"status-layout"},ee={class:"voltageAndCurrent sub-title"},ne=dt((()=>Q("div",null,[L("电压电流"),Q("span",{class:"uint"}," KV/A")],-1))),ie={class:"generation sub-title"},ae=dt((()=>Q("div",null,[L("发电曲线 "),Q("span",{class:"unit"},"MWH")],-1))),se={class:"distributionBox body-text"},le={class:"item-title-bg font-title-size"},oe=["src"],re=dt((()=>Q("span",null,"xx配电房-并网柜1",-1))),ce={class:"switchStatus sub-title"},ue=dt((()=>Q("div",{class:"sub-title"},"开关状态",-1))),de={class:"status-layout"},me={class:"voltageAndCurrent sub-title"},ve=dt((()=>Q("div",null,[L("电压电流"),Q("span",{class:"uint"}," KV/A")],-1))),pe={class:"generation sub-title"},fe=dt((()=>Q("div",null,[L("发电曲线 "),Q("span",{class:"unit"},"MWH")],-1))),ge={class:"distributionRoom-right"},he={class:"item-title-bg font-title-size"},we=["src"],xe=dt((()=>Q("span",null,"xx配电房-并网柜1",-1))),ye={class:"switchStatus sub-title"},be=dt((()=>Q("div",{class:"sub-title"},"开关状态",-1))),Ce={class:"status-layout"},_e={class:"voltageAndCurrent sub-title"},je=dt((()=>Q("div",null,[L("电压电流"),Q("span",{class:"uint"}," KV/A")],-1))),Ae={class:"generation sub-title"},Me=dt((()=>Q("div",null,[L("发电曲线 "),Q("span",{class:"unit"},"MWH")],-1))),We={class:"distributionBox body-text"},ze={class:"item-title-bg font-title-size"},Se=["src"],Be=dt((()=>Q("span",null,"xx配电房-并网柜1",-1))),Ie={class:"switchStatus sub-title"},Ee=dt((()=>Q("div",{class:"sub-title"},"开关状态",-1))),De={class:"status-layout"},Fe={class:"voltageAndCurrent sub-title"},Pe=dt((()=>Q("div",null,[L("电压电流"),Q("span",{class:"uint"}," KV/A")],-1))),ke={class:"generation sub-title"},Re=dt((()=>Q("div",null,[L("发电曲线 "),Q("span",{class:"unit"},"MWH")],-1))),Ue={class:"bingwanggui"},Ne=n({__name:"gridConnectedCabinetCopy",setup(t){const e=i(),n=F(),l=D({data:tt().format("YYYY-MM-DD HH:mm:ss")});D({curDate:et(nt(),"HH:MM YYYY/MM/DD"),map:{alarmList:[],nodeStatus:"",weatherData:null,parentIdList:[]}});const d=[{label:"开关1",value:"normal"},{label:"开关1",value:"warn"},{label:"开关1",value:"warn"},{label:"开关1",value:"warn"}],m=F(),v=F(),p=F(),f=F(),g=F(),h=F(),w=F(),x=F(),y=[],b=F(!1),C=F(),_=D([]),j={alarmStatus:{icon:"",label:"故障状态",value:"报警",unit:"",picName:"warn.png",end:!1},smokeConcentr:{icon:"mist",label:"烟雾浓度",value:0,unit:"PPM",picName:"",end:!1},temp:{icon:"temp",label:"当前温度",value:0,unit:"°C",picName:"",end:!1},humidity:{icon:"humidity",label:"当前湿度",value:0,unit:"%RH",picName:"",end:!0}},A=D([]),M=(t,e)=>{y.push({value:t,type:e})};return k((()=>{(async()=>{const{data:t}=await q();for(let e=0;e<t.length;e++){const n=at.clone(j,!0),i=Object.keys(n);_.push(t[e].deviceName);for(let a=0;a<i.length;a++)"alarmStatus"===i[a]&&0==t[e].children[0].alarmStatus?n[i[a]].value="正常":"alarmStatus"===i[a]&&1==t[e].children[0].alarmStatus?n[i[a]].value="告警":n[i[a]].value=t[e].children[0][i[a]];A.push(n)}})(),M(st(m.value),"Elec"),M(st(v.value),"VC"),M(st(p.value),"Elec"),M(st(f.value),"VC"),M(st(g.value),"Elec"),M(st(h.value),"VC"),M(st(w.value),"Elec"),M(st(x.value),"VC"),y.forEach((t=>{const e=at.clone(c,!0),n=at.clone(u,!0);"Elec"===t.type?t.value.setOption(e):t.value.setOption(n)})),it(C,(t=>{b.value=!b.value;for(let e=0;e<(null==y?void 0:y.length);e++)y[e].value&&setTimeout((()=>{y[e].value.resize()}),100)}))})),R((()=>{for(let t=0;t<(null==y?void 0:y.length);t++)y[t].value&&y[t].value.dispose()})),(t,i)=>{const c=o,u=s,y=a;return U(),N("div",{class:"screenfull-content tw-h-full tw-w-full bwg-bg screen-box",ref_key:"screenRef",ref:n},[Q("div",mt,[Q("div",vt,O(T(l).data),1),Q("div",pt,[T(e).userInfo.screenLogo?(U(),V(c,{key:0,src:T(e).userInfo.screenLogo,fit:"contain",class:"image"},null,8,["src"])):J("",!0),Q("h1",ft,O(T(e).userInfo.projectTitle),1)]),Q("div",gt,[G(u,{class:"setting-item",type:"font",element:T(n)},null,8,["element"])])]),Q("div",ht,[Q("div",null,[Q("p",wt,[Q("img",{src:T(r)("screen","title_icon.png"),class:"title-icon"},null,8,xt),Q("span",null,O(T(_)[0]),1)])]),Q("div",yt,[(U(!0),N(Z,null,K(T(A)[0],((t,e)=>(U(),N("div",{key:e,class:"u-flex-1 h-full u-flex-center-no asset-content-item"},[""!==t.picName?(U(),N("p",bt,[Q("img",{src:T(r)("screen","circle.png"),class:"title-icon0"},null,8,Ct),Q("img",{src:T(r)("screen",t.picName),class:"title-icon1"},null,8,_t)])):(U(),N("p",jt,[Q("img",{src:T(r)("screen","circle.png"),class:"title-icon0"},null,8,At),G(y,{name:t.icon,class:"title-icon1"},null,8,["name"])])),Q("p",Mt,[Q("span",Wt,O(t.label),1),"故障状态"===t.label?(U(),N("span",zt,[Q("i",{class:Y(["font-small-text-size1",!0===t.value?"normal":"abnormal"])},O(t.value),3)])):(U(),N("span",St,[Q("i",Bt,O(t.value),1),Q("i",It,O(t.unit),1)]))])])))),128))])]),Q("div",Et,[Q("p",Dt,[Q("img",{src:T(r)("screen","title_icon.png"),class:"title-icon"},null,8,Ft),Q("span",null,O(T(_)[1]),1)]),Q("div",Pt,[(U(!0),N(Z,null,K(T(A)[1],((t,e)=>(U(),N("div",{key:e,class:"u-flex-1 h-full u-flex-center-no asset-content-item"},[""!==t.picName?(U(),N("p",kt,[Q("img",{src:T(r)("screen","circle.png"),class:"title-icon0"},null,8,Rt),Q("img",{src:T(r)("screen",t.picName),class:"title-icon1"},null,8,Ut)])):(U(),N("p",Nt,[Q("img",{src:T(r)("screen","circle.png"),class:"title-icon0"},null,8,Qt),G(y,{name:t.icon,class:"title-icon1"},null,8,["name"])])),Q("p",Ot,[Q("span",Yt,O(t.label),1),"故障状态"===t.label?(U(),N("span",Vt,[Q("i",{class:Y(["font-small-text-size1",!0===t.value?"normal":"abnormal"])},O(t.value),3)])):(U(),N("span",Ht,[Q("i",Lt,O(t.value),1),Q("i",Tt,O(t.unit),1)]))])])))),128))])]),Q("div",Jt,[Q("div",Gt,[Q("div",null,[Q("p",Zt,[Q("img",{src:T(r)("screen","title_icon.png"),class:"title-icon"},null,8,Kt),Xt])]),Q("div",$t,[qt,Q("div",te,[(U(),N(Z,null,K(d,(t=>Q("div",{class:"status",key:t.label},[Q("span",null,O(t.label),1),G(ut,{status:t.value},null,8,["status"])]))),64))])]),Q("div",ee,[ne,Q("div",{class:"fg",ref_key:"leftTopVCChartRef",ref:v},null,512)]),Q("div",ie,[ae,Q("div",{class:"fg",ref_key:"leftTopElecChartRef",ref:m},null,512)])]),Q("div",se,[Q("div",null,[Q("p",le,[Q("img",{src:T(r)("screen","title_icon.png"),class:"title-icon"},null,8,oe),re])]),Q("div",ce,[ue,Q("div",de,[(U(),N(Z,null,K(d,(t=>Q("div",{class:"status",key:t.label},[Q("span",null,O(t.label),1),G(ut,{status:t.value},null,8,["status"])]))),64))])]),Q("div",me,[ve,Q("div",{class:"fg",ref_key:"rightTopVCChartRef",ref:h},null,512)]),Q("div",pe,[fe,Q("div",{class:"fg",ref_key:"rightTopElecChartRef",ref:g},null,512)])])]),Q("div",ge,[Q("div",{class:"distributionBox body-text",ref_key:"distributionBoxRef",ref:C},[Q("div",null,[Q("p",he,[Q("img",{src:T(r)("screen","title_icon.png"),class:"title-icon"},null,8,we),xe])]),Q("div",ye,[be,Q("div",Ce,[(U(),N(Z,null,K(d,(t=>Q("div",{class:"status",key:t.label},[Q("span",null,O(t.label),1),G(ut,{status:t.value},null,8,["status"])]))),64))])]),Q("div",_e,[je,Q("div",{class:"fg",ref_key:"leftBottomVCChartRef",ref:f},null,512)]),Q("div",Ae,[Me,Q("div",{class:"fg",ref_key:"leftBottomElecChartRef",ref:p},null,512)])],512),Q("div",We,[Q("div",null,[Q("p",ze,[Q("img",{src:T(r)("screen","title_icon.png"),class:"title-icon"},null,8,Se),Be])]),Q("div",Ie,[Ee,Q("div",De,[(U(),N(Z,null,K(d,(t=>Q("div",{class:"status",key:t.label},[Q("span",null,O(t.label),1),G(ut,{status:t.value},null,8,["status"])]))),64))])]),Q("div",Fe,[Pe,Q("div",{class:"fg",ref_key:"rightBottomVCChartRef",ref:x},null,512)]),Q("div",ke,[Re,Q("div",{class:"fg",ref_key:"rightBottomElecChartRef",ref:w},null,512)])])]),Q("div",Ue,[G(ct,{resize:T(b)},null,8,["resize"])])],512)}}},[["__scopeId","data-v-26d5ce19"]]);export{Ne as default};
