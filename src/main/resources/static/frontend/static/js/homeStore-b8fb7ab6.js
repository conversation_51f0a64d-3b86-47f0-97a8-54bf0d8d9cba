import{d as t}from"./pinia-c7531a5f.js";import"./vue-5bfa3a54.js";import{X as a,a as e,b as o,c as n,d as l,e as i,f as u,g as c}from"./homeApi-54bb989e.js";import{t as m}from"./taskUitls-36951a34.js";import{h as r,e as s}from"./@vue-5e5cdef9.js";import{C as f}from"./@vueuse-af86c621.js";function v(t,a=6e4,e=!1){const o=60-(new Date).getSeconds();return e&&t(),setTimeout((()=>{t(),setInterval(t,a)}),1e3*o)}function y(t){return(t*=1)<1e3?t:t>=1e3?t/1e3:0}function N(t){return(t*=1)<1e3||!(t>=1e3)}const d=t("xwhomeStore",(()=>{let t=r({plantName:0,maxEfficiency:0,averageEfficiency:0,todayElectricity:0,yearElectricity:0,plantCapacity:0,totalElectricity:0,monthElectricity:0,totalPower:0,todayCo2:0,totalCocal:0,totalCo2:0,treeNum:0}),d=r({totalNum:"0",onlineNum:"0",offlineNum:"0",normalNum:"0",alarmNum:"0",selfCheckNum:"0",inverterShutdownNum:"0",onlineRate:"0"}),p=r({totalNum:"0",onlineNum:"0",offlineNum:"0",normalNum:"0",alarmNum:"0",selfCheckNum:"0",inverterShutdownNum:"0",onlineRate:"0"}),h=r({alarmInfoNum:"0",alarmPlantNum:"0"}),w=r({totalNum:"0",onlineNum:"0",offlineNum:"0",normalNum:"0",abnormalNum:"0",alarmNum:"0",selfCheckNum:"0",noInitNum:"0",onlineRate:"0"});r([]);let E=s((()=>`${d.value.normalNum}/${d.value.alarmNum}/${1*d.value.offlineNum+1*d.value.inverterShutdownNum}/${d.value.totalNum}`)),C=s((()=>`${w.value.normalNum}/${w.value.alarmNum}/${w.value.offlineNum}/${w.value.totalNum}`));const x=s((()=>d.value.normalNum/d.value.totalNum*100)),I=s((()=>w.value.normalNum/w.value.totalNum*100)),P=r([]),F=r([]),k=r([]),$=r([]),g=r([]);function T(t){return"00000"==(null==t?void 0:t.status)}async function j(){const e=await a();if(T(e)){const a=e.data;t.value=a,t.value.plantCapacityThousandUnit=N(a.plantCapacity),t.value.plantCapacity=y(a.plantCapacity).toFixed(3),t.value.totalPowerThousandUnit=N(a.totalPower),t.value.totalPower=y(a.totalPower).toFixed(2),t.value.monthElectricityThousandUnit=N(a.monthElectricity),t.value.monthElectricity=y(a.monthElectricity).toFixed(2),t.value.todayElectricityThousandUnit=N(a.todayElectricity),t.value.todayElectricity=y(a.todayElectricity).toFixed(2),t.value.totalCo2=(a.totalCo2/1e4).toFixed(4),t.value.totalCocal=(a.totalCocal/1e4).toFixed(4),t.value.totalElectricityThousandUnit=N(a.totalElectricity),t.value.totalElectricity=y(a.totalElectricity).toFixed(2)}}async function S(){const t=await e();if(T(t)){const a=t.data;d.value=a}}async function U(){const t=await o();if(T(t)){const a=t.data;p.value=a}}async function R(){const t=await n();if(T(t)){const a=t.data;w.value=a}}async function D(){const t=await l();if(T(t)){const a=t.data;h.value=a}}async function b(){const t=await i();if(T(t)){const a=t.data;P.value=null==a?void 0:a.map((t=>({...t,electricity:(t.electricity/1e3).toFixed(2)})))}}async function q(){const t=await u();if(T(t)){const a=t.data,e={"01":"一","02":"二","03":"三","04":"四 ","05":"五","06":"六","07":"七","08":"八","09":"九",10:"十",11:"十一",12:"十二"};F.value=a.map((t=>({...t,collectDate:e[t.collectDate.slice(5,7)],electricity:(t.electricity/1e3).toFixed(2)})))}}async function A(){const t=await c();if(T(t)){const a=t.data;k.value=a}}async function O(){}async function X(){if(location.hash.includes("#/home")||location.hash.includes("#/plantOverview")){const t=new m([S(),j(),U(),D(),R(),b(),q(),A(),O()]);await t.run()}}return f(!1),{init:async function(){v(X)},plantInfo:t,plantNumInfo:d,inverterNumInfo:p,alarmNumInfo:h,deviceNumInfo:w,rankInfo:$,rankTable:g,plantNumComputed:E,deviceNumComputed:C,normalEquipmentRatio:x,normalPowerPlantProportion:I,day:P,month:F,hour:k,setData:X,getPlantInof:j,getRankInfo:O}}),{persist:{key:"xwhome",storage:sessionStorage}});export{v as s,d as x};
