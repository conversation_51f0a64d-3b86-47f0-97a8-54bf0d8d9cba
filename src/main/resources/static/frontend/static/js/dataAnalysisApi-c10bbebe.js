import{a as t}from"./api-b858041e.js";import{d as e}from"./dayjs-d60cc07f.js";const a=(e=void 0,a=void 0,i=void 0,r="",s="",c=void 0,n=void 0,o="",p=0,l=0,d="",m="",y="",g="",D=void 0)=>t("/statistics/generateElectricityOperation/getAnalyzeObjectData",{},{city:c,columnList:D,createEndTime:o,createStartTime:n,currentPage:p,filterType:m,isAlarm:e,pageSize:l,plantStatus:i,projectTypeList:a,province:s,referenceType:y,referenceValue:g,town:d,useYears:r},"post"),i=e().format("YYYY-MM-DD"),r=e(Date.now()-5184e5).format("YYYY-MM-DD"),s=(e,a=r,s=i)=>t("/statistics/generateElectricityOperation/getElectricityAnalyzeData",{},{plantUid:e,startTime:a,endTime:s},"post"),c=(e,a)=>t("/statistics/generateElectricityOperation/getSpaceAnalyzeData",{plantUid:e,dateTime:a},{},"get"),n=e=>t("/statistics/generateElectricityOperation/getFaultStatisticsData/"+plantUID);export{n as X,s as a,a as b,c};
