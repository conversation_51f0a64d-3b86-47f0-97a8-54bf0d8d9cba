import{a as e,M as t,C as a,f as i}from"./element-plus-d975be09.js";import"./vue-5bfa3a54.js";import"./vxe-table-3a25f2d2.js";import{a as o}from"./index-fec80322.js";import{p as l,a as s,b as r}from"./plant-5d7dddcf.js";import{i as n,p as m,_ as p}from"./index-8cc8d4b8.js";import{c as d}from"./tableMapUtils-2651efc6.js";import{p as u}from"./@vueuse-af86c621.js";import{d as c}from"./dayjs-d60cc07f.js";import{l as f}from"./lodash-6d99edc3.js";import{x as g}from"./xe-utils-fe99d42a.js";import{e as j}from"./exportFile-7631667a.js";import{h as v,j as b,m as h,p as w,as as y,o as _,c as x,x as S,a8 as k,a as P,aa as T,t as C,b as D,a6 as V,F as z,k as N,f as U}from"./@vue-5e5cdef9.js";import"./lodash-es-ea7deab5.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./@babel-f3c0a00c.js";import"./dom-zindex-5f662ad1.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./vue-router-6159329f.js";import"./bignumber.js-a537a5ca.js";import"./js-cookie-8253c38e.js";import"./axios-84f1a956.js";import"./spark-md5-022b35d0.js";import"./nprogress-e136a1b4.js";import"./quasar-b3f06d8a.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-7bbbb435.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./notification-950a5f80.js";import"./naive-ui-0ee0b8c3.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./date-fns-tz-0cc073c8.js";const L={status:"运行状态",userName:"所属用户",plantCapacity:"装机容量:KWp",totalElectricity:"总发电量:KWh",address:"地址",createTime:"创建时间",updateTime:"更新时间",inverterNum:"逆变器数量",alarmNum:"告警数量",plantType:"电站类型",powerDistributor:"配电箱状态",equivalentUseHour:"当日等效小时",electricityPrice:"电价",income:"当日收入",totalReduceCo2:"二氧化碳减排",totalPlantTreeNum:"累计植树",offlineTime:"离线时长"},q={class:"form"},O={class:"table-btn"},M=["onClick"],R=p(Object.assign({name:"plantStatement"},{__name:"index",setup(p){const R=u("status"),Y=v(),E=v(),I=v(),F=v(),W=v(!0),H=v(!1),K=v(!1),$=v(!1),A=b({condition:{address:"",timeType:"daterange",plantName:"",powerTime:[c().format("YYYY-MM-DD"),c().format("YYYY-MM-DD")],createTime:["",""],plantStatus:[],powerDistributor:[],multiplePlantStatus:[],get powerStartTime(){return this.powerTime?this.powerTime[0]:""},get powerEndTime(){return this.powerTime?this.powerTime[1]:""},get createStartTime(){return this.createTime?this.createTime[0]:""},get createEndTime(){return this.createTime?this.createTime[1]:""}},tablePage:{totalResult:0,currentPage:1,pageSize:15},modelData:{emailList:[""]}}),B=f._.omit(f._.cloneDeep(A.condition),["powerStartTime","powerEndTime","createStartTime","createEndTime"]),G=f._.curry(m)("/deviceMonitor/plantDetail?plantUid="),Q=b({"emailList[0]":[{required:!0,message:"请输入名称"}],"emailList[1]":[{required:!0,message:"请输入名称"}],"emailList[2]":[{required:!0,message:"请输入名称"}],"emailList[3]":[{required:!0,message:"请输入名称"}],"emailList[4]":[{required:!0,message:"请输入名称"}],"emailList[5]":[{required:!0,message:"请输入名称"}]}),Z=b({border:"full",showFooter:!1,loading:!1,minHeight:600,height:"auto",autoResize:!0,rowConfig:{isHover:!1,isCurrent:!1},columnConfig:{resizable:!0},customConfig:{storage:{visible:!0,fixed:!0},checkMethod:({column:e})=>!["seq"].includes(e.field)},editConfig:{trigger:"click",mode:"cell"},sortConfig:{remote:!0},data:[],toolbarConfig:{slots:{tools:"toolbar_tools"}},columns:[{field:"seq",type:"seq",width:50,fixed:"left"},{field:"plantName",title:"电站名称",align:"center",fixed:"left",slots:{default:"row-plantName"}},{field:"label",title:"电站属性",align:"center"},{field:"value",title:"属性值",align:"center"}]}),J=b({entryNameOption:[],plantOptions:l,powerDistributorOptions:s}),X=(e,t)=>{},ee=e=>{le(e.currentPage,e.pageSize)};async function te(){try{$.value=!0;const t=await(e={...A.condition,columnsList:Z.columns.map((e=>e.field)),sheetName:""},n({url:"/system/statistics/exportPlantStatisticsInfo",method:"post",data:{...e},responseType:"blob"}));j(t,"电站统计报表")}catch(t){}finally{$.value=!1}var e}const ae=()=>{A.modelData.emailList.length>5?e.info("最多添加5个邮箱"):A.modelData.emailList.push("")};async function ie(){var e;"00000"==(await(e={...A.condition,...A.modelData},n({url:"/system/email/sendList",method:"post",data:{...e}}))).status&&(H.value=!1)}const oe=({row:e,_rowIndex:t,column:a,visibleData:i,columnIndex:o})=>{const l=e[a.field];if(l&&["plantName","operations1"].includes(a.field)){const e=i[t-1];let o=i[t+1];if(e&&e[a.field]===l)return{rowspan:0,colspan:0};{let e=1;for(;o&&o[a.field]===l;)o=i[++e+t];if(e>1)return{rowspan:e,colspan:1}}}},le=async(e=1,t=1)=>{var a;Z.loading=!0,A.tablePage.currentPage=e,A.tablePage.pageSize=t,A.condition.plantStatus=g.uniq(null==(a=A.condition.multiplePlantStatus)?void 0:a.map((e=>r[e])).flat());const i=await function(e){var t,a;return n({url:"/system/statistics/getPlantStatisticsInfo",method:"post",data:{...e,date:null===e.date?"":e.date,state:0===(null==(t=e.state)?void 0:t.length)?"":e.state,sort:e.sort||0,powerDistributor:0===(null==(a=e.powerDistributor)?void 0:a.length)?"":e.powerDistributor}})}({...A.condition,...A.tablePage});Z.loading=!1,Z.data=d(i.data.records,L,"plantName"),A.tablePage.totalResult=i.data.total};return h((()=>{R.value&&(A.condition.multiplePlantStatus=R.value.split("")),le(),(async()=>{const e=await o();!function e(t){var a;for(const i of t)(null==(a=null==i?void 0:i.children)?void 0:a.length)?e(i.children):delete i.children}(e.data),J.entryNameOption=e.data})()})),w((()=>{})),(e,o)=>{const l=y("vxe-input"),s=y("vxe-form-item"),r=t,n=y("vxe-button"),m=y("vxe-form"),p=a,d=i,u=y("vxe-pager"),c=y("vxe-grid"),f=y("vxe-modal");return _(),x("div",{ref_key:"appContainerRef",ref:I,class:"app-container"},[S(c,V({id:"powerStationReport",ref_key:"xGrid",ref:F,class:"my-grid66"},Z,{"span-method":oe,onCustom:X}),{form:k((()=>[P("div",q,[S(m,{ref_key:"ordinaryForm",ref:E,collapseStatus:W.value,"onUpdate:collapseStatus":o[2]||(o[2]=e=>W.value=e),data:A.condition},{default:k((()=>[S(s,{field:"plantName",title:"电站名称"},{default:k((({data:e})=>[S(l,{modelValue:e.plantName,"onUpdate:modelValue":t=>e.plantName=t,clearable:"",placeholder:"请输入电站名称"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),S(s,{field:"multiplePlantStatus",title:"运行状态"},{default:k((({data:e})=>[S(r,{modelValue:e.multiplePlantStatus,"onUpdate:modelValue":t=>e.multiplePlantStatus=t,options:J.plantOptions,clearable:"","collapse-tags":"",multiple:"",placeholder:"请选择电站状态",style:{width:"220px"}},null,8,["modelValue","onUpdate:modelValue","options"])])),_:1}),S(s,{field:"powerDistributor",title:"配电箱状态"},{default:k((({data:e})=>[S(r,{modelValue:e.powerDistributor,"onUpdate:modelValue":t=>e.powerDistributor=t,options:J.powerDistributorOptions,clearable:"",placeholder:"请输入配电箱状态",style:{width:"220px"}},null,8,["modelValue","onUpdate:modelValue","options"])])),_:1}),S(s,null,{default:k((()=>[S(n,{status:"danger",onClick:o[0]||(o[0]=e=>(e=>{if("senior"===e){const e=Y.value.getItems().map((e=>e.field));Object.assign(A.condition,g.pick(B,e))}else{const e=E.value.getItems().map((e=>e.field));Object.assign(A.condition,g.pick(B,e))}})("ordinary"))},{default:k((()=>[T("重置")])),_:1})])),_:1}),S(s,null,{default:k((()=>[S(n,{status:"primary",onClick:o[1]||(o[1]=e=>le(1))},{default:k((()=>[T("查询")])),_:1})])),_:1})])),_:1},8,["collapseStatus","data"])])])),toolbar_buttons:k((()=>[])),toolbar_tools:k((()=>[P("div",O,[S(n,{status:"primary",onClick:te,loading:$.value},{default:k((()=>[T("导出")])),_:1},8,["loading"])])])),top:k((()=>[])),"row-plantStatus":k((({row:e})=>[S(p,{type:"正常"===e.status?"success":"warning"},{default:k((()=>[T(C(e.status),1)])),_:2},1032,["type"])])),"row-plantName":k((({row:e})=>[P("div",{style:{cursor:"pointer"},onClick:t=>D(G)(e.plantUid)},C(e.plantName),9,M)])),"row-operate":k((({row:e})=>[S(d,{link:"",type:"primary",onClick:t=>(e=>{Object.assign(A.modelData,e),showEdit.value=!0})(e)},{default:k((()=>[T("编辑")])),_:2},1032,["onClick"])])),bottom:k((()=>[])),pager:k((()=>[S(u,{"current-page":A.tablePage.currentPage,"onUpdate:currentPage":o[3]||(o[3]=e=>A.tablePage.currentPage=e),"page-size":A.tablePage.pageSize,"onUpdate:pageSize":o[4]||(o[4]=e=>A.tablePage.pageSize=e),"page-sizes":[1,2,5],total:A.tablePage.totalResult,perfect:"",onPageChange:ee},null,8,["current-page","page-size","total"])])),_:1},16),S(f,{modelValue:H.value,"onUpdate:modelValue":o[6]||(o[6]=e=>H.value=e),loading:K.value,"destroy-on-close":"",escClosable:"","min-height":"300","min-width":"600",resize:"",width:"800"},{title:k((()=>[P("div",null,[T(" 发送邮件"),S(n,{icon:"vxe-icon-add",type:"text",onClick:ae})])])),default:k((()=>[S(m,{data:A.modelData,rules:Q,"title-align":"right","title-width":"100",onSubmit:ie},{default:k((()=>[(_(!0),x(z,null,N(A.modelData.emailList,((e,t)=>(_(),U(s,{field:`emailList[${t}]`,"item-render":{},span:24,title:"邮箱"},{default:k((({data:e})=>[S(l,{modelValue:e.emailList[t],"onUpdate:modelValue":a=>e.emailList[t]=a,placeholder:"请输入邮箱"},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1032,["field"])))),256)),S(s,{span:24,align:"center","title-align":"left"},{default:k((()=>[S(n,{type:"submit"},{default:k((()=>[T("提交")])),_:1}),S(n,{type:"cancel",onClick:o[5]||(o[5]=e=>H.value=!1)},{default:k((()=>[T("取消")])),_:1})])),_:1})])),_:1},8,["data","rules"])])),_:1},8,["modelValue","loading"])],512)}}}),[["__scopeId","data-v-7fe7c28e"]]);export{R as default};
