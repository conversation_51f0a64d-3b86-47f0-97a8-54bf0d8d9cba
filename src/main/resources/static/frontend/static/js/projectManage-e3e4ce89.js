import{O as e,f as t,G as o,P as a,d as s,e as i,K as l,c as r,R as p}from"./element-plus-95e0b914.js";import{F as m,W as n}from"./quasar-df1bac18.js";import"./vue-5bfa3a54.js";import{a as c,e as d,f as j,g as u}from"./systemSettingApi-34f94e8e.js";import{n as f}from"./@vicons-f32a0bdb.js";import{l as v}from"./lodash-6d99edc3.js";import{d as b}from"./notification-950a5f80.js";import{g}from"./api-360ec627.js";import{L as w}from"./ui-385bff4c.js";import{_ as h}from"./index-a5df0f75.js";import{h as y,j as k,m as x,az as V,o as _,c as D,x as P,a8 as T,a as z,a9 as N,b as C,aa as I,f as S,l as q,C as U,D as R}from"./@vue-5e5cdef9.js";import"./lodash-es-ea7deab5.js";import"./@vueuse-5227c686.js";import"./vue-router-6159329f.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./dayjs-67f8ddef.js";import"./@babel-f3c0a00c.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./naive-ui-0ee0b8c3.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./date-fns-tz-0cc073c8.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./menuStore-30bf76d3.js";import"./icons-95011f8c.js";import"./@x-ui-vue3-df3ba55b.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./xe-utils-fe99d42a.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";/* empty css                *//* empty css                    */import"./vxe-table-plugin-element-be30dbb6.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";const W=e=>(U("data-v-ceeac6fc"),e=e(),R(),e),A={class:"q-pa-md"},F={class:"header tw-h-10 fontWhite"},L={colspan:"1"},M=W((()=>z("h6",{class:"tw-text-2xl tw-my-0 text-white text-left"},"项目管理",-1))),B=W((()=>z("div",null,null,-1))),E={class:"tw-relative tw-w-full"},G={class:"tw-w-full tw-inline-flex tw-justify-center"},K={class:"dialog-footer"},O=h({__name:"projectManage",setup(h){const U=c;let R=y();const W=f;let O=k({searchType:"name",searchValue:"",dialogVisible:!1,dialogTitle:"",form:{},tableData:[],projectProps:{value:"id",label:"projectName",emitPath:!1,checkStrictly:!0},projectPID:""});function Q(){R&&R.value.validate((async e=>{if(!e)return!1;b.info({title:"警告",content:"是否要提交该表单？",positiveText:"确定",negativeText:"取消",onPositiveClick:()=>{O.dialogTitle.includes("新增")?async function(){try{const e=await j(O.form.projectPID||"0",O.form.projectName);"00000"===g(e).status&&(O.dialogVisible=!1,Z())}catch(e){}}():async function(){try{const e=await u(O.form.id,O.form.pid,O.form.projectName);"00000"===g(e).status&&(O.dialogVisible=!1,Z())}catch(e){}}()},onNegativeClick:()=>{}})}))}async function Z(){$.set(!0);try{const e=await d(),t=g(e);O.tableData=t.data}catch(e){}$.set(!1)}const $=new w;return x((async()=>{$.set(!1),await Z()})),(c,d)=>{const j=m,u=e,f=t,b=o,g=a,w=n,h=s,y=i,k=l,x=r,Z=p,$=V("skeleton-item"),H=V("x"),J=V("g");return _(),D("div",A,[P(w,{class:"tw-rounded-none tw-w-full tw-h-full",separator:"cell"},{default:T((()=>[z("thead",null,[z("tr",F,[z("th",L,[N((_(),D("section",null,[M,B,N(P(j,{class:"tw-bg-blue-500 tw-text-white",label:"新增","no-caps":"",dense:"",onClick:d[0]||(d[0]=e=>(O.dialogTitle="新增项目",O.dialogVisible=!0,void(O.form={})))},null,512),[[$]])])),[[H,[3,17,1]],[J,10]])])])]),z("tbody",E,[P(g,{data:C(O).tableData,"row-key":"id",border:""},{default:T((()=>[P(u,{prop:"projectName",label:"项目专项名称",align:"center"}),P(u,{prop:"createTime",label:"创建时间",align:"center"}),P(u,{label:"操作",width:"100",align:"center"},{default:T((e=>[z("div",G,[P(b,{effect:"dark",content:"编辑",placement:"top"},{default:T((()=>[P(f,{icon:C(W).PencilSharp,class:"operation-button",plain:"",onClick:t=>{return o=e.row,O.form=v._.cloneDeep(o),O.form.projectPID=O.form.pid,O.dialogTitle="编辑项目",void(O.dialogVisible=!0);var o}},null,8,["icon","onClick"])])),_:2},1024)])])),_:1})])),_:1},8,["data"])])])),_:1}),P(Z,{modelValue:C(O).dialogVisible,"onUpdate:modelValue":d[5]||(d[5]=e=>C(O).dialogVisible=e),title:C(O).dialogTitle,width:"35%","z-index":1e3},{footer:T((()=>[z("span",K,[P(f,{type:"primary",plain:"",onClick:d[3]||(d[3]=e=>C(O).dialogVisible=!1)},{default:T((()=>[I("取消")])),_:1}),P(f,{type:"primary",onClick:d[4]||(d[4]=e=>Q(C(R)))},{default:T((()=>[I("确定")])),_:1})])])),default:T((()=>[P(x,{ref_key:"ruleFormRef",ref:R,model:C(O).form,"label-width":"150px",size:"large",rules:C(U)},{default:T((()=>[P(y,{label:"项目专项名称",prop:"projectName"},{default:T((()=>[P(h,{modelValue:C(O).form.projectName,"onUpdate:modelValue":d[1]||(d[1]=e=>C(O).form.projectName=e)},null,8,["modelValue"])])),_:1}),C(O).dialogTitle.includes("新增")?(_(),S(y,{key:0,label:"新增下级项目",prop:"projectPID"},{default:T((()=>[P(k,{clearable:"",class:"tw-w-full",placeholder:"不选择即为新增初始项目级别",modelValue:C(O).form.projectPID,"onUpdate:modelValue":d[2]||(d[2]=e=>C(O).form.projectPID=e),props:C(O).projectProps,options:C(O).tableData},null,8,["modelValue","props","options"])])),_:1})):q("",!0)])),_:1},8,["model","rules"])])),_:1},8,["modelValue","title"])])}}},[["__scopeId","data-v-ceeac6fc"]]);export{O as default};
