import{F as t,C as e}from"./quasar-b3f06d8a.js";import{_ as o}from"./date-33a67ff0.js";import"./vue-5bfa3a54.js";import{l as s}from"./lodash-6d99edc3.js";import{e as i}from"./echartsInit-0067e609.js";import{a as r}from"./dataAnalysisApi-c10bbebe.js";import{g as l}from"./api-b858041e.js";import{d as a}from"./dayjs-d60cc07f.js";import{A as n,c as p}from"./@vicons-f32a0bdb.js";import{h as w,m as c,az as m,a9 as u,o as d,c as f,a as h,x,b as g}from"./@vue-5e5cdef9.js";import"./notification-950a5f80.js";import"./naive-ui-0ee0b8c3.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./lodash-es-ea7deab5.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./@babel-f3c0a00c.js";import"./date-fns-tz-0cc073c8.js";import"./async-validator-cf877c1f.js";import"./index-8cc8d4b8.js";import"./element-plus-d975be09.js";import"./@vueuse-af86c621.js";import"./vue-router-6159329f.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./xe-utils-fe99d42a.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-7bbbb435.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./menuStore-26f8ddd8.js";import"./icons-95011f8c.js";const b={title:{text:"电站总发电功率（MW）",right:"center",top:"2px",subtextStyle:{fontSize:22,color:"#000"}},tooltip:{trigger:"axis"},grid:{top:"50px",bottom:"80px",right:"40px",left:"60px"},xAxis:{type:"category",data:["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],axisLabel:{show:!0,color:"#000"},axisLine:{show:!0,lineStyle:{color:"#000",width:0,type:"solid"}},splitLine:{lineStyle:{color:"#000"}}},yAxis:s._.range(7).map(((t,e)=>({type:"value",name:"",show:0==e,nameLocation:"end",axisLabel:{color:"#000",margin:15}}))),series:[{data:[820,932,901,934,1290,1330,1320],type:"line",barMaxWidth:"80",smooth:!0,itemStyle:{}}]},y={backgroundColor:"#fff",tooltip:{formatter:"{a} <br/>{b} : {c}%"},graphic:{elements:[{type:"image",z:3,style:{image:"/btosolar/picture/dataAnalysis/weather.svg",width:70,height:70},top:"middle",left:"center",scale:[.5,.5]}]},series:[{name:"内部进度条",type:"gauge",center:["50%","50%"],radius:"90%",z:1,min:0,max:100,startAngle:270,endAngle:0,splitNumber:5,progress:{show:!0,width:27,itemStyle:{color:{type:"linear",x:0,y:1,x2:0,y2:0,colorStops:[{offset:0,color:"rgba(17,95,234,0)"},{offset:.5,color:"rgba(17,95,234,0.5)"},{offset:1,color:"rgba(17,95,234,1)"}],global:!1}}},axisLine:{show:!1},axisLabel:{show:!0,color:"#000",fontSize:12,distance:15},axisTick:{show:!1},splitLine:{show:!1},itemStyle:{show:!1},detail:{offsetCenter:["92%","30%"],textStyle:{padding:[0,0,0,0],fontSize:24,color:"#000"}},title:{show:!0,offsetCenter:["92%","50%"],textStyle:{color:"#000",fontSize:12,fontFamily:"PingFangSC"}},data:[{name:"标题",value:54}],pointer:{show:!1}},{name:"刻度",type:"gauge",center:["50%","50%"],radius:"90%",z:999,startAngle:270,endAngle:-0,splitNumber:80,progress:{show:!1},axisLine:{show:!1,lineStyle:{width:27}},axisLabel:{show:!1},axisTick:{show:!1},splitLine:{show:!0,length:27,lineStyle:{color:"rgba(17,95,234,0.2)",width:3},distance:-27},itemStyle:{show:!1},detail:{show:!1},title:{show:!1},pointer:{show:!1}},{name:"最外边线",type:"gauge",center:["50%","50%"],radius:"45%",z:999,startAngle:270,endAngle:0,splitNumber:50,progress:{show:!1},axisLine:{show:!1,lineStyle:{width:1,color:[[1,"rgba(240,240,240,0.2)"]]}},axisLabel:{show:!1},axisTick:{show:!1},splitLine:{show:!0,length:1,lineStyle:{color:"rgba(240,240,240,0.2)",width:5}},itemStyle:{show:!1},detail:{show:!1},title:{show:!1},pointer:{show:!1}},{name:"最外刻度亮线",type:"gauge",radius:"90%",center:["50%","50%"],startAngle:270-145.8+1,endAngle:270-145.8-30,splitNumber:10,axisLine:{lineStyle:{color:[[.03,"rgba(16,235,227,1)"]],width:29,shadowColor:"rgba(16,235,227,1)",shadowBlur:10}},progress:{show:!1,width:29,itemStyle:{color:"#10EBE3",shadowColor:"rgba(16,235,227,1)",shadowBlur:10}},axisLabel:{show:!1},axisTick:{show:!1},splitLine:{show:!1},detail:{show:!1},pointer:{show:!1},animationDelay:850,animationDuration:300,data:[{name:"title",value:100}],title:{show:!1}},{name:"",type:"pie",radius:[0,"36%"],center:["50%","50%"],tooltip:{show:!1},label:{show:!1},itemStyle:{normal:{color:"rgba(24,219,159,0.1)"}},hoverAnimation:!1,animation:!1,data:[100]}]};function j(t="标题",e=["1:00","2:00","3:00","4:00"],o=[{value:[12,7,11,6],name:""}]){return b.title.text=t,b.series=o.map((t=>{const e=s._.cloneDeep(b.series[0]);return e.data=t.value,e.name=t.name,e})),b.xAxis.data=e,b}function v(t="标题",e=0,o=100,s=50,i="weather"){return i="/btosolar/picture/dataAnalysis/"+i+".svg",y.series[0].min=e,y.series[0].max=o,y.series[0].data[0].value=s,y.series[0].data[0].name=t,y.graphic.elements[0].style.image=i,y}var S={backgroundColor:"#0F224C",title:[{text:"预测值",x:"22%",y:"70%",textStyle:{fontSize:14,fontWeight:"100",color:"#5dc3ea",lineHeight:16,textAlign:"center"}},{text:"实际值",x:"73%",y:"70%",textStyle:{fontSize:14,fontWeight:"100",color:"#5dc3ea",lineHeight:16,textAlign:"center"}}],series:[{type:"liquidFill",radius:"47%",center:["25%","45%"],color:[{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"#446bf5"},{offset:1,color:"#2ca3e2"}],globalCoord:!1}],data:[.45,.45],backgroundStyle:{borderWidth:1,color:"RGBA(51, 66, 127, 0.7)"},label:{normal:{textStyle:{fontSize:50,color:"#fff"}}},outline:{borderDistance:0,itemStyle:{borderWidth:2,borderColor:"#112165"}}},{type:"liquidFill",radius:"47%",center:["75%","45%"],color:[{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"#2aa1e3"},{offset:1,color:"#08bbc9"}],globalCoord:!1}],data:[.76,.76],backgroundStyle:{borderWidth:1,color:"RGBA(51, 66, 127, 0.7)"},label:{normal:{textStyle:{fontSize:28,color:"#fff"}}},outline:{borderDistance:0,itemStyle:{borderWidth:2,borderColor:"#112165"}}}]};function k(t,e){const o=t.getOption();return s._.get(o,e)}function z(t,e,o,i=!1){t.setOption(function(t,e,o){if(!o)return k(t,e);if(o.startsWith("+")||o.startsWith("-")){let s=k(t,e);const i=o[0];o=1*o.slice(1),"+"==i?s+=o:s-=o,o=s}const i=t.getOption();return s._.set(i,e,o),i}(t,e,o)),t.resize()}const A={class:"tw-bg- tw-h-full"},C={class:"tw-bg-white"},L={class:"tw-bg-white"},_={class:"tw-h-full tw-w-full tw-relative"},R={class:"tw-h-full tw-w-full tw-relative"},W={class:"tw-h-full tw-w-full tw-relative"},D={class:"tw-h-full tw-w-full tw-relative"},F={class:"tw-bg-slate-200"},T={class:"tw-text-xl tw-px-4"},q={class:"tw-flex tw-items-center"},B={class:""},I={class:"tw-text-xl tw-px-4"},M={class:"tw-flex tw-items-center"},N={__name:"timeAnalysis",props:{plantUid:{type:String,required:!0}},setup(b){const y=b;w(s._.cloneDeep(y.plantUid)),w();const k=w(),N=w(),U=w(),Y=w(),E=w(),O=w(),G=w(),H=w(),P=w();let Q=null,V=null,Z=null,$=null;function J(t,e){z({"温度":Q,"天气":$,"云量":V,"风速":Z}[t],"series.0.data.0.value",e+1)}return c((async()=>{await async function(){const t=await r(y.plantUid,...function(){const t=a(k.value.date);return[t.subtract(7,"day").format("YYYY-MM-DD"),k.value.date]}()),e=l(t),o=e.data.electricityResult.map((t=>t.collectDate)),s=e.data.electricityResult.map((t=>t.electricity)),n=e.data.weatherResult.map((t=>t.time)),p=e.data.weatherResult;i(N,j("发电量",o,[{value:s,name:"电量"}])),i(U,j("天气",n,[{value:p.map((t=>t.cloud)),name:"云量",yAxisIndex:0},{value:p.map((t=>t.pressure/100)),name:"气压",yAxisIndex:1},{value:p.map((t=>t.windspeed)),name:"风速",yAxisIndex:2},{value:p.map((t=>t.humidity)),name:"湿度",yAxisIndex:3},{value:p.map((t=>t.precip)),name:"降雨",yAxisIndex:4}]))}(),$=await i(Y,v("天气",0,100,10)),Q=await i(E,v("温度",0,100,20,"temp")),V=await i(O,v("云量",0,100,10,"cloud")),Z=i(G,v("风速",0,100,30,"wind")),i(H,S),i(P,S)})),(s,i)=>{const r=t,l=o,a=e,w=m("x"),c=m("y"),b=m("g");return u((d(),f("div",A,[u((d(),f("section",null,[u((d(),f("section",C,[h("figure",{class:"tw-m-0",ref_key:"electRef",ref:N},null,512),h("figure",{v:"",ref_key:"weatherRef",ref:U},null,512)])),[[w,[1,1]]]),u((d(),f("section",L,[h("div",_,[h("figure",{ref_key:"weatherDownRef",ref:Y,class:"tw-w-full tw-h-full tw-m-0"},null,512),x(g(n),{onClick:i[0]||(i[0]=t=>J("天气","+")),class:"tw-cursor-pointer tw-w-10 tw-z-10 tw-absolute tw-bottom-[20px] tw-right-[210px] hover:tw-text-blue-400"}),x(g(p),{onClick:i[1]||(i[1]=t=>J("天气","-")),class:"tw-cursor-pointer tw-w-10 tw-z-10 tw-absolute tw-bottom-[20px] tw-right-[250px] hover:tw-text-blue-400"})]),h("div",R,[h("figure",{ref_key:"tempRef",ref:E,class:"tw-w-full tw-h-full tw-m-0"},null,512),x(g(n),{onClick:i[2]||(i[2]=t=>J("温度","+")),class:"tw-cursor-pointer tw-w-10 tw-z-10 tw-absolute tw-bottom-[20px] tw-right-[210px] hover:tw-text-blue-400"}),x(g(p),{onClick:i[3]||(i[3]=t=>J("温度","-")),class:"tw-cursor-pointer tw-w-10 tw-z-10 tw-absolute tw-bottom-[20px] tw-right-[250px] hover:tw-text-blue-400"})]),h("div",W,[h("figure",{ref_key:"cloudRef",ref:O,class:"tw-w-full tw-h-full tw-m-0"},null,512),x(g(n),{onClick:i[4]||(i[4]=t=>J("云量","+")),class:"tw-cursor-pointer tw-w-10 tw-z-10 tw-absolute tw-bottom-[20px] tw-right-[210px] hover:tw-text-blue-400"}),x(g(p),{onClick:i[5]||(i[5]=t=>J("云量","-")),class:"tw-cursor-pointer tw-w-10 tw-z-10 tw-absolute tw-bottom-[20px] tw-right-[250px] hover:tw-text-blue-400"})]),h("div",D,[h("figure",{ref_key:"windRef",ref:G,class:"tw-w-full tw-h-full tw-m-0"},null,512),x(g(n),{onClick:i[6]||(i[6]=t=>J("风速","+")),class:"tw-cursor-pointer tw-w-10 tw-z-10 tw-absolute tw-bottom-[20px] tw-right-[210px] hover:tw-text-blue-400"}),x(g(p),{onClick:i[7]||(i[7]=t=>J("风速","-")),class:"tw-cursor-pointer tw-w-10 tw-z-10 tw-absolute tw-bottom-[20px] tw-right-[250px] hover:tw-text-blue-400"})])])),[[w,[1,1]],[c,[1,1]]])])),[[c,[1,1.5]]]),u((d(),f("section",F,[u((d(),f("section",null,[u((d(),f("header",T,[h("div",q,[x(r,{outline:"",class:"tw-cursor-default tw-text-base tw-text-green-700",label:"学习"})]),x(l,{tabs:"日",ref_key:"dateRef",ref:k,type:"single"},null,512)])),[[w,[1,1]],[b,120]]),h("figure",{ref_key:"realNumRef",ref:P},null,512),x(a)])),[[c,[1,5]]]),u((d(),f("section",B,[u((d(),f("header",I,[h("div",M,[x(r,{outline:"",class:"tw-cursor-default tw-text-base tw-text-green-700",label:"预测"})]),x(l,{tabs:"日",type:"single"})])),[[w,[1,1]],[b,120]]),h("figure",{ref_key:"forecastRef",ref:H,class:"tw-m-0"},null,512)])),[[c,[1,5]]])])),[[c,[1,1]]])])),[[w,[2,1]]])}}};export{N as default};
