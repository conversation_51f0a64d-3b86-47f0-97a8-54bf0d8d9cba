import{h as r}from"./@babel-f3c0a00c.js";import{c as t}from"./crypt-d45ab006.js";import{c as n}from"./charenc-ab2e9fee.js";import{i as o}from"./is-buffer-9e0e61f6.js";var e,s,i,a,c,f={exports:{}};e=t,s=n.utf8,i=o,a=n.bin,(c=function(r,t){r.constructor==String?r=t&&"binary"===t.encoding?a.stringToBytes(r):s.stringToBytes(r):i(r)?r=Array.prototype.slice.call(r,0):Array.isArray(r)||r.constructor===Uint8Array||(r=r.toString());for(var n=e.bytesToWords(r),o=8*r.length,f=1732584193,u=-271733879,g=-1732584194,y=271733878,l=0;l<n.length;l++)n[l]=16711935&(n[l]<<8|n[l]>>>24)|4278255360&(n[l]<<24|n[l]>>>8);n[o>>>5]|=128<<o%32,n[14+(o+64>>>9<<4)]=o;var p=c._ff,h=c._gg,m=c._hh,_=c._ii;for(l=0;l<n.length;l+=16){var b=f,v=u,T=g,d=y;f=p(f,u,g,y,n[l+0],7,-680876936),y=p(y,f,u,g,n[l+1],12,-389564586),g=p(g,y,f,u,n[l+2],17,606105819),u=p(u,g,y,f,n[l+3],22,-1044525330),f=p(f,u,g,y,n[l+4],7,-176418897),y=p(y,f,u,g,n[l+5],12,1200080426),g=p(g,y,f,u,n[l+6],17,-1473231341),u=p(u,g,y,f,n[l+7],22,-45705983),f=p(f,u,g,y,n[l+8],7,1770035416),y=p(y,f,u,g,n[l+9],12,-1958414417),g=p(g,y,f,u,n[l+10],17,-42063),u=p(u,g,y,f,n[l+11],22,-1990404162),f=p(f,u,g,y,n[l+12],7,1804603682),y=p(y,f,u,g,n[l+13],12,-40341101),g=p(g,y,f,u,n[l+14],17,-1502002290),f=h(f,u=p(u,g,y,f,n[l+15],22,1236535329),g,y,n[l+1],5,-165796510),y=h(y,f,u,g,n[l+6],9,-1069501632),g=h(g,y,f,u,n[l+11],14,643717713),u=h(u,g,y,f,n[l+0],20,-373897302),f=h(f,u,g,y,n[l+5],5,-701558691),y=h(y,f,u,g,n[l+10],9,38016083),g=h(g,y,f,u,n[l+15],14,-660478335),u=h(u,g,y,f,n[l+4],20,-405537848),f=h(f,u,g,y,n[l+9],5,568446438),y=h(y,f,u,g,n[l+14],9,-1019803690),g=h(g,y,f,u,n[l+3],14,-187363961),u=h(u,g,y,f,n[l+8],20,1163531501),f=h(f,u,g,y,n[l+13],5,-1444681467),y=h(y,f,u,g,n[l+2],9,-51403784),g=h(g,y,f,u,n[l+7],14,1735328473),f=m(f,u=h(u,g,y,f,n[l+12],20,-1926607734),g,y,n[l+5],4,-378558),y=m(y,f,u,g,n[l+8],11,-2022574463),g=m(g,y,f,u,n[l+11],16,1839030562),u=m(u,g,y,f,n[l+14],23,-35309556),f=m(f,u,g,y,n[l+1],4,-1530992060),y=m(y,f,u,g,n[l+4],11,1272893353),g=m(g,y,f,u,n[l+7],16,-155497632),u=m(u,g,y,f,n[l+10],23,-1094730640),f=m(f,u,g,y,n[l+13],4,681279174),y=m(y,f,u,g,n[l+0],11,-358537222),g=m(g,y,f,u,n[l+3],16,-722521979),u=m(u,g,y,f,n[l+6],23,76029189),f=m(f,u,g,y,n[l+9],4,-640364487),y=m(y,f,u,g,n[l+12],11,-421815835),g=m(g,y,f,u,n[l+15],16,530742520),f=_(f,u=m(u,g,y,f,n[l+2],23,-995338651),g,y,n[l+0],6,-198630844),y=_(y,f,u,g,n[l+7],10,1126891415),g=_(g,y,f,u,n[l+14],15,-1416354905),u=_(u,g,y,f,n[l+5],21,-57434055),f=_(f,u,g,y,n[l+12],6,1700485571),y=_(y,f,u,g,n[l+3],10,-1894986606),g=_(g,y,f,u,n[l+10],15,-1051523),u=_(u,g,y,f,n[l+1],21,-2054922799),f=_(f,u,g,y,n[l+8],6,1873313359),y=_(y,f,u,g,n[l+15],10,-30611744),g=_(g,y,f,u,n[l+6],15,-1560198380),u=_(u,g,y,f,n[l+13],21,1309151649),f=_(f,u,g,y,n[l+4],6,-145523070),y=_(y,f,u,g,n[l+11],10,-1120210379),g=_(g,y,f,u,n[l+2],15,718787259),u=_(u,g,y,f,n[l+9],21,-343485551),f=f+b>>>0,u=u+v>>>0,g=g+T>>>0,y=y+d>>>0}return e.endian([f,u,g,y])})._ff=function(r,t,n,o,e,s,i){var a=r+(t&n|~t&o)+(e>>>0)+i;return(a<<s|a>>>32-s)+t},c._gg=function(r,t,n,o,e,s,i){var a=r+(t&o|n&~o)+(e>>>0)+i;return(a<<s|a>>>32-s)+t},c._hh=function(r,t,n,o,e,s,i){var a=r+(t^n^o)+(e>>>0)+i;return(a<<s|a>>>32-s)+t},c._ii=function(r,t,n,o,e,s,i){var a=r+(n^(t|~o))+(e>>>0)+i;return(a<<s|a>>>32-s)+t},c._blocksize=16,c._digestsize=16,f.exports=function(r,t){if(null==r)throw new Error("Illegal argument "+r);var n=e.wordsToBytes(c(r,t));return t&&t.asBytes?n:t&&t.asString?a.bytesToString(n):e.bytesToHex(n)};const u=r(f.exports);export{u as m};
