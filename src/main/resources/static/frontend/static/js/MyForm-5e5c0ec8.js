import{a as e}from"./quasar-b3f06d8a.js";import"./vue-5bfa3a54.js";import{m as a,s as t}from"./@vueuse-af86c621.js";import{f as s}from"./formUtil-a2e6828b.js";import{l}from"./lodash-6d99edc3.js";import{_ as o}from"./index-8cc8d4b8.js";import{b as i,d as p,e as r,f as m,g as n,h as u}from"./naive-ui-0ee0b8c3.js";import{e as f,w as c,m as d,v,o as w,f as y,a8 as x,a as g,t as k,c as b,F as _,k as j,ak as h,y as L,aa as T,l as U,x as F,g as V}from"./@vue-5e5cdef9.js";const q={class:"body-text tw-mr-2"},C=o({__name:"MyForm",props:["title","formList","page"],emits:["update:modelValue"],setup(o,{emit:C}){const z=o,D=a(z,"page",C),E=f((()=>s.getValue(z.formList))),I=l._.find(z.formList,{prop:"check"}),K=t(E),M=f((()=>K.history.value.at(0).snapshot));c([()=>M.value,()=>E.value],((e,a)=>{const t=e.map((e=>l._.omit(e,["check","export","reset"]))),o=s.isObjEqual(...t);I.type=o?"info":"warning"})),c([()=>z.page.page,()=>z.page.pageSize],(e=>{K.commit()}));let N=l._.cloneDeep(s.getValue(z.formList));async function O(e){"warning"==I.type&&I.invoke(E,I,K),"reset"==e.prop&&(e.value=!0,await s.reset(z.formList,N,e,D)),"export"==e.prop&&(e.value=!0,await e.invoke())}return d((()=>{K.commit()})),v((()=>{K.clear()})),(a,t)=>{const s=i,l=p,o=r,f=m,c=e,d=n,v=u;return w(),y(v,{class:"tw-inline-flex tw-w-full tw-text-xl tw-items-center n-form-box"},{default:x((()=>[g("span",q,k(z.title),1),(w(!0),b(_,null,j(z.formList,(e=>(w(),b(_,null,["input"==e.formType?(w(),y(s,{key:0,value:e.value,"onUpdate:value":a=>e.value=a,onKeydown:h(O,["enter"]),class:L(["tw-mr-2",e.class??"tw-w-[300px]"])},{prefix:x((()=>[T(k(e.label),1)])),_:2},1032,["value","onUpdate:value","class"])):U("",!0),"select"==e.formType?(w(),y(f,{key:1,class:L(["tw-mr-2",e.class??"tw-w-[300px]"])},{default:x((()=>[F(l,null,{default:x((()=>[T(k(e.label),1)])),_:2},1024),F(o,{class:L(e.class??"tw-w-[300px]"),multiple:e.multiple,clearable:"","max-tag-count":1,options:e.options,value:e.value,"onUpdate:value":a=>e.value=a,"key-field":e.keyField??"value","label-field":e.labelField??"label"},null,8,["class","multiple","options","value","onUpdate:value","key-field","label-field"])])),_:2},1032,["class"])):U("",!0),"slot"==e.formType?V(a.$slots,e.prop,{key:2,scope:e},void 0,!0):U("",!0),"space"==e.formType?(w(),y(c,{key:3})):U("",!0),"button"==e.formType?(w(),y(d,{key:4,onClick:a=>O(e),type:e.type??"info",loading:e.value,class:"tw-text-right tw-mr-2 tw-text-white"},{default:x((()=>[T(k(e.label),1)])),_:2},1032,["onClick","type","loading"])):U("",!0)],64)))),256))])),_:3})}}},[["__scopeId","data-v-36bfa8ed"]]);export{C as _};
