import{l as t}from"./lodash-6d99edc3.js";class s{constructor(s){this.getList=[],this.setList=[],this.handler={get:(s,i)=>(s.getList.forEach((([e,h])=>{(i==e||t._.isUndefined(e))&&h(s,i)})),s[i]),set:(s,i,e)=>(s.setList.forEach((([h,o])=>{(i==h||t._.isUndefined(h))&&o(s,i,e)})),s[i]=e,!0)},Object.assign(this,s);const i=new Proxy(this,this.handler);return t._.isFunction(i.init)&&i.init(),this.mounted(),i}getProxy(t,s){this.getList.push([s,t])}setProxy(t,s){this.setList.push([s,t])}getSetProxy(t,s){this.getProxy(t,s),this.setProxy(t,s)}mounted(){this.getList.forEach((([t,s])=>{s(this)}))}}export{s as p};
