import"./vue-5bfa3a54.js";import{e,h as t,E as n,G as r,H as o,b as l,I as a,J as i,s as u,m as s,n as c,w as v,K as d,L as f,M as p,N as m,O as h,j as y}from"./@vue-5e5cdef9.js";import{u as g,a as b}from"./vue-router-6159329f.js";import{C as w}from"./universal-cookie-eda71741.js";import{a as O,A as S}from"./axios-84f1a956.js";var P,j=Object.defineProperty,E=Object.defineProperties,A=Object.getOwnPropertyDescriptors,x=Object.getOwnPropertySymbols,D=Object.prototype.hasOwnProperty,T=Object.prototype.propertyIsEnumerable,I=(e,t,n)=>t in e?j(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;function k(e,t){var n;const r=f();var o,l;return p((()=>{r.value=e()}),(o=((e,t)=>{for(var n in t||(t={}))D.call(t,n)&&I(e,n,t[n]);if(x)for(var n of x(t))T.call(t,n)&&I(e,n,t[n]);return e})({},t),l={flush:null!=(n=null==t?void 0:t.flush)?n:"sync"},E(o,A(l)))),d(r)}const F="undefined"!=typeof window,M=e=>"function"==typeof e,C=e=>"string"==typeof e,L=()=>{},N=F&&(null==(P=null==window?void 0:window.navigator)?void 0:P.userAgent)&&/iP(ad|hone|od)/.test(window.navigator.userAgent);function $(e){return"function"==typeof e?e():l(e)}function q(e,t){return function(...n){return new Promise(((r,o)=>{Promise.resolve(e((()=>t.apply(this,n)),{fn:t,thisArg:this,args:n})).then(r).catch(o)}))}}const R=e=>e();function z(e,t=!0,n=!0,r=!1){let o,l,a=0,i=!0,u=L;const s=()=>{o&&(clearTimeout(o),o=void 0,u(),u=L)};return c=>{const v=$(e),d=Date.now()-a,f=()=>l=c();return s(),v<=0?(a=Date.now(),f()):(d>v&&(n||!i)?(a=Date.now(),f()):t&&(l=new Promise(((e,t)=>{u=r?t:e,o=setTimeout((()=>{a=Date.now(),i=!0,e(f()),s()}),Math.max(0,v-d))}))),n||o||(o=setTimeout((()=>i=!0),v)),i=!1,l)}}function B(e,t=!1,n="Timeout"){return new Promise(((r,o)=>{t?setTimeout((()=>o(n)),e):setTimeout(r,e)}))}function H(e){return!!n()&&(r(e),!0)}function _(e,t){return null==t?l(e):l(e)[t]}function W(e,t=200,n={}){return q(function(e,t={}){let n,r,o=L;const l=e=>{clearTimeout(e),o(),o=L};return a=>{const i=$(e),u=$(t.maxWait);return n&&l(n),i<=0||void 0!==u&&u<=0?(r&&(l(r),r=null),Promise.resolve(a())):new Promise(((e,s)=>{o=t.rejectOnCancel?s:e,u&&!r&&(r=setTimeout((()=>{n&&l(n),r=null,e(a())}),u)),n=setTimeout((()=>{r&&l(r),r=null,e(a())}),i)}))}}(t,n),e)}function Y(e,n=200,r={}){const o=t(e.value),l=W((()=>{o.value=e.value}),n,r);return v(e,(()=>l())),o}function Q(e,t=200,n=!1,r=!0,o=!1){return q(z(t,n,r,o),e)}function U(n){return"function"==typeof n?e(n):t(n)}function V(...e){if(2===e.length){const[t,n]=e;t.value=n}if(3===e.length){const[t,n,r]=e;t[n]=r}}var G=Object.defineProperty,J=Object.defineProperties,X=Object.getOwnPropertyDescriptors,Z=Object.getOwnPropertySymbols,K=Object.prototype.hasOwnProperty,ee=Object.prototype.propertyIsEnumerable,te=(e,t,n)=>t in e?G(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,ne=(e,t)=>{for(var n in t||(t={}))K.call(t,n)&&te(e,n,t[n]);if(Z)for(var n of Z(t))ee.call(t,n)&&te(e,n,t[n]);return e},re=(e,t)=>J(e,X(t));function oe(e,t=!0){u()?s(e):t?e():c(e)}function le(e,t=!1){function n(n,{flush:r="sync",deep:o=!1,timeout:l,throwOnTimeout:a}={}){let i=null;const u=[new Promise((l=>{i=v(e,(e=>{n(e)!==t&&(null==i||i(),l(e))}),{flush:r,deep:o,immediate:!0})}))];return null!=l&&u.push(B(l,a).then((()=>$(e))).finally((()=>null==i?void 0:i()))),Promise.race(u)}function r(r,l){if(!o(r))return n((e=>e===r),l);const{flush:a="sync",deep:i=!1,timeout:u,throwOnTimeout:s}=null!=l?l:{};let c=null;const d=[new Promise((n=>{c=v([e,r],(([e,r])=>{t!==(e===r)&&(null==c||c(),n(e))}),{flush:a,deep:i,immediate:!0})}))];return null!=u&&d.push(B(u,s).then((()=>$(e))).finally((()=>(null==c||c(),$(e))))),Promise.race(d)}function l(e){return a(1,e)}function a(e=1,t){let r=-1;return n((()=>(r+=1,r>=e)),t)}if(Array.isArray($(e))){return{toMatch:n,toContains:function(e,t){return n((t=>{const n=Array.from(t);return n.includes(e)||n.includes($(e))}),t)},changed:l,changedTimes:a,get not(){return le(e,!t)}}}return{toMatch:n,toBe:r,toBeTruthy:function(e){return n((e=>Boolean(e)),e)},toBeNull:function(e){return r(null,e)},toBeNaN:function(e){return n(Number.isNaN,e)},toBeUndefined:function(e){return r(void 0,e)},changed:l,changedTimes:a,get not(){return le(e,!t)}}}function ae(e){return le(e)}function ie(e=0,n={}){const r=t(e),{max:o=1/0,min:l=-1/0}=n,a=e=>r.value=Math.max(l,Math.min(o,e));return{count:r,inc:(e=1)=>r.value=Math.min(o,r.value+e),dec:(e=1)=>r.value=Math.max(l,r.value-e),get:()=>r.value,set:a,reset:(t=e)=>(e=t,a(t))}}const ue=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,se=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a{1,2}|A{1,2}|m{1,2}|s{1,2}|Z{1,2}|SSS/g,ce=(e,t,n,r)=>{let o=e<12?"AM":"PM";return r&&(o=o.split("").reduce(((e,t)=>e+`${t}.`),"")),n?o.toLowerCase():o};function ve(t,n="HH:mm:ss",r={}){return e((()=>((e,t,n={})=>{var r;const o=e.getFullYear(),l=e.getMonth(),a=e.getDate(),i=e.getHours(),u=e.getMinutes(),s=e.getSeconds(),c=e.getMilliseconds(),v=e.getDay(),d=null!=(r=n.customMeridiem)?r:ce,f={YY:()=>String(o).slice(-2),YYYY:()=>o,M:()=>l+1,MM:()=>`${l+1}`.padStart(2,"0"),MMM:()=>e.toLocaleDateString(n.locales,{month:"short"}),MMMM:()=>e.toLocaleDateString(n.locales,{month:"long"}),D:()=>String(a),DD:()=>`${a}`.padStart(2,"0"),H:()=>String(i),HH:()=>`${i}`.padStart(2,"0"),h:()=>`${i%12||12}`.padStart(1,"0"),hh:()=>`${i%12||12}`.padStart(2,"0"),m:()=>String(u),mm:()=>`${u}`.padStart(2,"0"),s:()=>String(s),ss:()=>`${s}`.padStart(2,"0"),SSS:()=>`${c}`.padStart(3,"0"),d:()=>v,dd:()=>e.toLocaleDateString(n.locales,{weekday:"narrow"}),ddd:()=>e.toLocaleDateString(n.locales,{weekday:"short"}),dddd:()=>e.toLocaleDateString(n.locales,{weekday:"long"}),A:()=>d(i,u),AA:()=>d(i,u,!1,!0),a:()=>d(i,u,!0),aa:()=>d(i,u,!0,!0)};return t.replace(se,((e,t)=>t||f[e]()))})((e=>{if(null===e)return new Date(NaN);if(void 0===e)return new Date;if(e instanceof Date)return new Date(e);if("string"==typeof e&&!/Z$/i.test(e)){const t=e.match(ue);if(t){const e=t[2]-1||0,n=(t[7]||"0").substring(0,3);return new Date(t[1],e,t[3]||1,t[4]||0,t[5]||0,t[6]||0,n)}}return new Date(e)})($(t)),$(n),r)))}function de(e,n,r={}){const{immediate:o=!0}=r,l=t(!1);let a=null;function i(){a&&(clearTimeout(a),a=null)}function u(){l.value=!1,i()}function s(...t){i(),l.value=!0,a=setTimeout((()=>{l.value=!1,a=null,e(...t)}),$(n))}return o&&(l.value=!0,F&&s()),H(u),{isPending:d(l),start:s,stop:u}}function fe(e=!1,n={}){const{truthyValue:r=!0,falsyValue:l=!1}=n,a=o(e),i=t(e);function u(e){if(arguments.length)return i.value=e,i.value;{const e=$(r);return i.value=i.value===e?$(l):e,i.value}}return a?u:[i,u]}var pe=Object.getOwnPropertySymbols,me=Object.prototype.hasOwnProperty,he=Object.prototype.propertyIsEnumerable,ye=(e,t)=>{var n={};for(var r in e)me.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&pe)for(var r of pe(e))t.indexOf(r)<0&&he.call(e,r)&&(n[r]=e[r]);return n};var ge=Object.defineProperty,be=Object.defineProperties,we=Object.getOwnPropertyDescriptors,Oe=Object.getOwnPropertySymbols,Se=Object.prototype.hasOwnProperty,Pe=Object.prototype.propertyIsEnumerable,je=(e,t,n)=>t in e?ge(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;function Ee(e,t,n={}){const r=n,{throttle:o=0,trailing:l=!0,leading:a=!0}=r,i=((e,t)=>{var n={};for(var r in e)Se.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&Oe)for(var r of Oe(e))t.indexOf(r)<0&&Pe.call(e,r)&&(n[r]=e[r]);return n})(r,["throttle","trailing","leading"]);return function(e,t,n={}){const r=n,{eventFilter:o=R}=r,l=ye(r,["eventFilter"]);return v(e,q(o,t),l)}(e,t,(u=((e,t)=>{for(var n in t||(t={}))Se.call(t,n)&&je(e,n,t[n]);if(Oe)for(var n of Oe(t))Pe.call(t,n)&&je(e,n,t[n]);return e})({},i),s={eventFilter:z(o,l,a)},be(u,we(s))));var u,s}function Ae(e){var t;const n=$(e);return null!=(t=null==n?void 0:n.$el)?t:n}const xe=F?window:void 0,De=F?window.document:void 0;function Te(...e){let t,n,r,o;if(C(e[0])||Array.isArray(e[0])?([n,r,o]=e,t=xe):[t,n,r,o]=e,!t)return L;Array.isArray(n)||(n=[n]),Array.isArray(r)||(r=[r]);const l=[],a=()=>{l.forEach((e=>e())),l.length=0},i=v((()=>[Ae(t),$(o)]),(([e,t])=>{a(),e&&l.push(...n.flatMap((n=>r.map((r=>((e,t,n,r)=>(e.addEventListener(t,n,r),()=>e.removeEventListener(t,n,r)))(e,n,r,t))))))}),{immediate:!0,flush:"post"}),u=()=>{i(),a()};return H(u),u}let Ie=!1;function ke(e,t,n={}){const{window:r=xe,ignore:o=[],capture:l=!0,detectIframe:a=!1}=n;if(!r)return;N&&!Ie&&(Ie=!0,Array.from(r.document.body.children).forEach((e=>e.addEventListener("click",L))));let i=!0;const u=e=>o.some((t=>{if("string"==typeof t)return Array.from(r.document.querySelectorAll(t)).some((t=>t===e.target||e.composedPath().includes(t)));{const n=Ae(t);return n&&(e.target===n||e.composedPath().includes(n))}})),s=[Te(r,"click",(n=>{const r=Ae(e);r&&r!==n.target&&!n.composedPath().includes(r)&&(0===n.detail&&(i=!u(n)),i?t(n):i=!0)}),{passive:!0,capture:l}),Te(r,"pointerdown",(t=>{const n=Ae(e);n&&(i=!t.composedPath().includes(n)&&!u(t))}),{passive:!0}),a&&Te(r,"blur",(n=>{var o;const l=Ae(e);"IFRAME"!==(null==(o=r.document.activeElement)?void 0:o.tagName)||(null==l?void 0:l.contains(r.document.activeElement))||t(n)}))].filter(Boolean);return()=>s.forEach((e=>e()))}function Fe(e,t=null){const n=u();let r=()=>{};const o=i(((o,l)=>(r=l,{get(){var r,l;return o(),null!=(l=null==(r=null==n?void 0:n.proxy)?void 0:r.$refs[e])?l:t},set(){}})));return oe(r),m(r),o}function Me(e,n=!1){const r=t(),o=()=>r.value=Boolean(e());return o(),oe(o,n),r}function Ce(e){return JSON.parse(JSON.stringify(e))}const Le="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},Ne="__vueuse_ssr_handlers__";function $e(n,r,{window:o=xe,initialValue:l=""}={}){const a=t(l),i=e((()=>{var e;return Ae(r)||(null==(e=null==o?void 0:o.document)?void 0:e.documentElement)}));return v([i,()=>$(n)],(([e,t])=>{var n;if(e&&o){const r=null==(n=o.getComputedStyle(e).getPropertyValue(t))?void 0:n.trim();a.value=r||l}}),{immediate:!0}),v(a,(e=>{var t;(null==(t=i.value)?void 0:t.style)&&i.value.style.setProperty($(n),e)})),a}Le[Ne]=Le[Ne]||{};const qe=e=>e,Re=(e,t)=>e.value=t;function ze(e){return e?M(e)?e:Ce:qe}function Be(e){return e?M(e)?e:Ce:qe}function He(n,r={}){const{clone:o=!1,dump:l=ze(o),parse:a=Be(o),setSource:i=Re}=r;function u(){return h({snapshot:l(n.value),timestamp:+Date.now()})}const s=t(u()),c=t([]),v=t([]),d=e=>{i(n,a(e.snapshot)),s.value=e},f=e((()=>[s.value,...c.value])),p=e((()=>c.value.length>0)),m=e((()=>v.value.length>0));return{source:n,undoStack:c,redoStack:v,last:s,history:f,canUndo:p,canRedo:m,clear:()=>{c.value.splice(0,c.value.length),v.value.splice(0,v.value.length)},commit:()=>{c.value.unshift(s.value),s.value=u(),r.capacity&&c.value.length>r.capacity&&c.value.splice(r.capacity,1/0),v.value.length&&v.value.splice(0,v.value.length)},reset:()=>{d(s.value)},undo:()=>{const e=c.value.shift();e&&(v.value.unshift(s.value),d(e))},redo:()=>{const e=v.value.shift();e&&(c.value.unshift(s.value),d(e))}}}function _e({document:e=De}={}){if(!e)return t("visible");const n=t(e.visibilityState);return Te(e,"visibilitychange",(()=>{n.value=e.visibilityState})),n}var We=Object.defineProperty,Ye=Object.defineProperties,Qe=Object.getOwnPropertyDescriptors,Ue=Object.getOwnPropertySymbols,Ve=Object.prototype.hasOwnProperty,Ge=Object.prototype.propertyIsEnumerable,Je=(e,t,n)=>t in e?We(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;function Xe(n,r={}){var l,u,s;const c=null!=(l=r.draggingElement)?l:xe,v=null!=(u=r.handle)?u:n,d=t(null!=(s=$(r.initialValue))?s:{x:0,y:0}),f=t(),p=e=>!r.pointerTypes||r.pointerTypes.includes(e.pointerType),m=e=>{$(r.preventDefault)&&e.preventDefault(),$(r.stopPropagation)&&e.stopPropagation()},h=e=>{var t;p(e)&&f.value&&(d.value={x:e.clientX-f.value.x,y:e.clientY-f.value.y},null==(t=r.onMove)||t.call(r,d.value,e),m(e))},y=e=>{var t;p(e)&&f.value&&(f.value=void 0,null==(t=r.onEnd)||t.call(r,d.value,e),m(e))};return F&&(Te(v,"pointerdown",(e=>{var t;if(!p(e))return;if($(r.exact)&&e.target!==$(n))return;const o=$(n).getBoundingClientRect(),l={x:e.clientX-o.left,y:e.clientY-o.top};!1!==(null==(t=r.onStart)?void 0:t.call(r,l,e))&&(f.value=l,m(e))}),!0),Te(c,"pointermove",h,!0),Te(c,"pointerup",y,!0)),g=((e,t)=>{for(var n in t||(t={}))Ve.call(t,n)&&Je(e,n,t[n]);if(Ue)for(var n of Ue(t))Ge.call(t,n)&&Je(e,n,t[n]);return e})({},function(e){if(!o(e))return a(e);const t=Array.isArray(e.value)?new Array(e.value.length):{};for(const n in e.value)t[n]=i((()=>({get:()=>e.value[n],set(t){if(Array.isArray(e.value)){const r=[...e.value];r[n]=t,e.value=r}else{const r=re(ne({},e.value),{[n]:t});Object.setPrototypeOf(r,e.value),e.value=r}}})));return t}(d)),b={position:d,isDragging:e((()=>!!f.value)),style:e((()=>`left:${d.value.x}px;top:${d.value.y}px;`))},Ye(g,Qe(b));var g,b}var Ze=Object.getOwnPropertySymbols,Ke=Object.prototype.hasOwnProperty,et=Object.prototype.propertyIsEnumerable,tt=(e,t)=>{var n={};for(var r in e)Ke.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&Ze)for(var r of Ze(e))t.indexOf(r)<0&&et.call(e,r)&&(n[r]=e[r]);return n};function nt(e,t,n={}){const r=n,{window:o=xe}=r,l=tt(r,["window"]);let a;const i=Me((()=>o&&"ResizeObserver"in o)),u=()=>{a&&(a.disconnect(),a=void 0)},s=v((()=>Ae(e)),(e=>{u(),i.value&&o&&e&&(a=new ResizeObserver(t),a.observe(e,l))}),{immediate:!0,flush:"post"}),c=()=>{u(),s()};return H(c),{isSupported:i,stop:c}}function rt(e,n={}){const{reset:r=!0,windowResize:o=!0,windowScroll:l=!0,immediate:a=!0}=n,i=t(0),u=t(0),s=t(0),c=t(0),d=t(0),f=t(0),p=t(0),m=t(0);function h(){const t=Ae(e);if(!t)return void(r&&(i.value=0,u.value=0,s.value=0,c.value=0,d.value=0,f.value=0,p.value=0,m.value=0));const n=t.getBoundingClientRect();i.value=n.height,u.value=n.bottom,s.value=n.left,c.value=n.right,d.value=n.top,f.value=n.width,p.value=n.x,m.value=n.y}return nt(e,h),v((()=>Ae(e)),(e=>!e&&h())),l&&Te("scroll",h,{capture:!0,passive:!0}),o&&Te("resize",h,{passive:!0}),oe((()=>{a&&h()})),{height:i,bottom:u,left:s,right:c,top:d,width:f,x:p,y:m,update:h}}function ot(n,r={width:0,height:0},o={}){const{window:l=xe,box:a="content-box"}=o,i=e((()=>{var e,t;return null==(t=null==(e=Ae(n))?void 0:e.namespaceURI)?void 0:t.includes("svg")})),u=t(r.width),s=t(r.height);return nt(n,(([e])=>{const t="border-box"===a?e.borderBoxSize:"content-box"===a?e.contentBoxSize:e.devicePixelContentBoxSize;if(l&&i.value){const e=Ae(n);if(e){const t=l.getComputedStyle(e);u.value=parseFloat(t.width),s.value=parseFloat(t.height)}}else if(t){const e=Array.isArray(t)?t:[t];u.value=e.reduce(((e,{inlineSize:t})=>e+t),0),s.value=e.reduce(((e,{blockSize:t})=>e+t),0)}else u.value=e.contentRect.width,s.value=e.contentRect.height}),o),v((()=>Ae(n)),(e=>{u.value=e?r.width:0,s.value=e?r.height:0})),{width:u,height:s}}function lt(e=null,t={}){const{baseUrl:n="",rel:r="icon",document:o=De}=t,l=U(e);return v(l,((e,t)=>{var l;C(e)&&e!==t&&(l=e,null==o||o.head.querySelectorAll(`link[rel*="${r}"]`).forEach((e=>e.href=`${n}${l}`)))}),{immediate:!0}),l}const at=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]];function it(e,n={}){const{document:r=De,autoExit:o=!1}=n,l=e||(null==r?void 0:r.querySelector("html")),a=t(!1);let i=at[0];const u=Me((()=>{if(!r)return!1;for(const e of at)if(e[1]in r)return i=e,!0;return!1})),[s,c,v,,d]=i;async function f(){u.value&&((null==r?void 0:r[v])&&await r[c](),a.value=!1)}async function p(){if(!u.value)return;await f();const e=Ae(l);e&&(await e[s](),a.value=!0)}return r&&Te(r,d,(()=>{a.value=!!(null==r?void 0:r[v])}),!1),o&&H(f),{isSupported:u,isFullscreen:a,enter:p,exit:f,toggle:async function(){a.value?await f():await p()}}}var ut=Object.getOwnPropertySymbols,st=Object.prototype.hasOwnProperty,ct=Object.prototype.propertyIsEnumerable,vt=(e,t)=>{var n={};for(var r in e)st.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&ut)for(var r of ut(e))t.indexOf(r)<0&&ct.call(e,r)&&(n[r]=e[r]);return n};function dt(e,t,n={}){const r=n,{window:o=xe}=r,l=vt(r,["window"]);let a;const i=Me((()=>o&&"MutationObserver"in o)),u=()=>{a&&(a.disconnect(),a=void 0)},s=v((()=>Ae(e)),(e=>{u(),i.value&&o&&e&&(a=new MutationObserver(t),a.observe(e,l))}),{immediate:!0}),c=()=>{u(),s()};return H(c),{isSupported:i,stop:c}}function ft(e={}){const{window:n=xe}=e,r=null==n?void 0:n.navigator,o=Me((()=>r&&"connection"in r)),l=t(!0),a=t(!1),i=t(void 0),u=t(void 0),s=t(void 0),c=t(void 0),v=t(void 0),d=t(void 0),f=t("unknown"),p=o.value&&r.connection;function m(){r&&(l.value=r.onLine,i.value=l.value?void 0:Date.now(),u.value=l.value?Date.now():void 0,p&&(s.value=p.downlink,c.value=p.downlinkMax,d.value=p.effectiveType,v.value=p.rtt,a.value=p.saveData,f.value=p.type))}return n&&(Te(n,"offline",(()=>{l.value=!1,i.value=Date.now()})),Te(n,"online",(()=>{l.value=!0,u.value=Date.now()}))),p&&Te(p,"change",m,!1),m(),{isSupported:o,isOnline:l,saveData:a,offlineAt:i,onlineAt:u,downlink:s,downlinkMax:c,effectiveType:d,rtt:v,type:f}}var pt,mt,ht=Object.defineProperty,yt=Object.getOwnPropertySymbols,gt=Object.prototype.hasOwnProperty,bt=Object.prototype.propertyIsEnumerable,wt=(e,t,n)=>t in e?ht(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;function Ot(e={}){const{controls:n=!1,interval:r="requestAnimationFrame"}=e,l=t(new Date),a=()=>l.value=new Date,i="requestAnimationFrame"===r?function(e,n={}){const{immediate:r=!0,window:o=xe}=n,l=t(!1);let a=0,i=null;function u(t){l.value&&o&&(e({delta:t-a,timestamp:t}),a=t,i=o.requestAnimationFrame(u))}function s(){!l.value&&o&&(l.value=!0,i=o.requestAnimationFrame(u))}function c(){l.value=!1,null!=i&&o&&(o.cancelAnimationFrame(i),i=null)}return r&&s(),H(c),{isActive:d(l),pause:c,resume:s}}(a,{immediate:!0}):function(e,n=1e3,r={}){const{immediate:l=!0,immediateCallback:a=!1}=r;let i=null;const u=t(!1);function s(){i&&(clearInterval(i),i=null)}function c(){u.value=!1,s()}function d(){const t=$(n);t<=0||(u.value=!0,a&&e(),s(),i=setInterval(e,t))}l&&F&&d(),(o(n)||M(n))&&H(v(n,(()=>{u.value&&F&&d()})));return H(c),{isActive:u,pause:c,resume:d}}(a,r,{immediate:!0});return n?((e,t)=>{for(var n in t||(t={}))gt.call(t,n)&&wt(e,n,t[n]);if(yt)for(var n of yt(t))bt.call(t,n)&&wt(e,n,t[n]);return e})({now:l},i):l}function St(e=null,t={}){var n,r;const{document:o=De}=t,a=U(null!=(n=null!=e?e:null==o?void 0:o.title)?n:null),i=e&&M(e);function u(e){if(!("titleTemplate"in t))return e;const n=t.titleTemplate||"%s";return M(n)?n(e):l(n).replace(/%s/g,e)}return v(a,((e,t)=>{e!==t&&o&&(o.title=u(C(e)?e:""))}),{immediate:!0}),t.observe&&!t.titleTemplate&&o&&!i&&dt(null==(r=o.head)?void 0:r.querySelector("title"),(()=>{o&&o.title!==a.value&&(a.value=u(o.title))}),{childList:!0}),a}(mt=pt||(pt={})).UP="UP",mt.RIGHT="RIGHT",mt.DOWN="DOWN",mt.LEFT="LEFT",mt.NONE="NONE";var Pt=Object.defineProperty,jt=Object.getOwnPropertySymbols,Et=Object.prototype.hasOwnProperty,At=Object.prototype.propertyIsEnumerable,xt=(e,t,n)=>t in e?Pt(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;function Dt(n,r,o,l={}){var a,i,s;const{clone:c=!1,passive:d=!1,eventName:f,deep:p=!1,defaultValue:m}=l,h=u(),y=o||(null==h?void 0:h.emit)||(null==(a=null==h?void 0:h.$emit)?void 0:a.bind(h))||(null==(s=null==(i=null==h?void 0:h.proxy)?void 0:i.$emit)?void 0:s.bind(null==h?void 0:h.proxy));let g=f;r||(r="modelValue"),g=f||g||`update:${r.toString()}`;const b=e=>c?M(c)?c(e):Ce(e):e,w=()=>void 0!==n[r]?b(n[r]):m;if(d){const e=w(),o=t(e);return v((()=>n[r]),(e=>o.value=b(e))),v(o,(e=>{(e!==n[r]||p)&&y(g,e)}),{deep:p}),o}return e({get:()=>w(),set(e){y(g,e)}})}function Tt({window:e=xe}={}){if(!e)return t(!1);const n=t(e.document.hasFocus());return Te(e,"blur",(()=>{n.value=!1})),Te(e,"focus",(()=>{n.value=!0})),n}function It(e={}){const{window:n=xe,initialWidth:r=1/0,initialHeight:o=1/0,listenOrientation:l=!0,includeScrollbar:a=!0}=e,i=t(r),u=t(o),s=()=>{n&&(a?(i.value=n.innerWidth,u.value=n.innerHeight):(i.value=n.document.documentElement.clientWidth,u.value=n.document.documentElement.clientHeight))};return s(),oe(s),Te("resize",s,{passive:!0}),l&&Te("orientationchange",s,{passive:!0}),{width:i,height:u}}function kt(e){var t;const n=$(e);return null!=(t=null==n?void 0:n.$el)?t:n}((e,t)=>{for(var n in t||(t={}))Et.call(t,n)&&xt(e,n,t[n]);if(jt)for(var n of jt(t))At.call(t,n)&&xt(e,n,t[n])})({linear:function(e){return e}},{easeInSine:[.12,0,.39,0],easeOutSine:[.61,1,.88,1],easeInOutSine:[.37,0,.63,1],easeInQuad:[.11,0,.5,0],easeOutQuad:[.5,1,.89,1],easeInOutQuad:[.45,0,.55,1],easeInCubic:[.32,0,.67,0],easeOutCubic:[.33,1,.68,1],easeInOutCubic:[.65,0,.35,1],easeInQuart:[.5,0,.75,0],easeOutQuart:[.25,1,.5,1],easeInOutQuart:[.76,0,.24,1],easeInQuint:[.64,0,.78,0],easeOutQuint:[.22,1,.36,1],easeInOutQuint:[.83,0,.17,1],easeInExpo:[.7,0,.84,0],easeOutExpo:[.16,1,.3,1],easeInOutExpo:[.87,0,.13,1],easeInCirc:[.55,0,1,.45],easeOutCirc:[0,.55,.45,1],easeInOutCirc:[.85,0,.15,1],easeInBack:[.36,0,.66,-.56],easeOutBack:[.34,1.56,.64,1],easeInOutBack:[.68,-.6,.32,1.6]});const Ft=F?window:void 0;function Mt(...e){let t,n,r,o;if(C(e[0])||Array.isArray(e[0])?([n,r,o]=e,t=Ft):[t,n,r,o]=e,!t)return L;Array.isArray(n)||(n=[n]),Array.isArray(r)||(r=[r]);const l=[],a=()=>{l.forEach((e=>e())),l.length=0},i=v((()=>[kt(t),$(o)]),(([e,t])=>{a(),e&&l.push(...n.flatMap((n=>r.map((r=>((e,t,n,r)=>(e.addEventListener(t,n,r),()=>e.removeEventListener(t,n,r)))(e,n,r,t))))))}),{immediate:!0,flush:"post"}),u=()=>{i(),a()};return H(u),u}const Ct="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},Lt="__vueuse_ssr_handlers__";Ct[Lt]=Ct[Lt]||{};const Nt=1;var $t=Object.defineProperty,qt=Object.defineProperties,Rt=Object.getOwnPropertyDescriptors,zt=Object.getOwnPropertySymbols,Bt=Object.prototype.hasOwnProperty,Ht=Object.prototype.propertyIsEnumerable,_t=(e,t,n)=>t in e?$t(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Wt=(e,t)=>{for(var n in t||(t={}))Bt.call(t,n)&&_t(e,n,t[n]);if(zt)for(var n of zt(t))Ht.call(t,n)&&_t(e,n,t[n]);return e};function Yt(n,r,o={}){var l,a;const i=null!=(l=o.direction)?l:"bottom",u=y(function(n,r={}){const{throttle:o=0,idle:l=200,onStop:a=L,onScroll:i=L,offset:u={left:0,right:0,top:0,bottom:0},eventListenerOptions:s={capture:!1,passive:!0},behavior:c="auto"}=r,v=t(0),d=t(0),f=e({get:()=>v.value,set(e){m(e,void 0)}}),p=e({get:()=>d.value,set(e){m(void 0,e)}});function m(e,t){var r,o,l;const a=$(n);a&&(null==(l=a instanceof Document?document.body:a)||l.scrollTo({top:null!=(r=$(t))?r:p.value,left:null!=(o=$(e))?o:f.value,behavior:$(c)}))}const h=t(!1),g=y({left:!0,right:!1,top:!0,bottom:!1}),b=y({left:!1,right:!1,top:!1,bottom:!1}),w=e=>{h.value&&(h.value=!1,b.left=!1,b.right=!1,b.top=!1,b.bottom=!1,a(e))},O=W(w,o+l),S=e=>{const t=e.target===document?e.target.documentElement:e.target,n=t.scrollLeft;b.left=n<v.value,b.right=n>d.value,g.left=n<=0+(u.left||0),g.right=n+t.clientWidth>=t.scrollWidth-(u.right||0)-Nt,v.value=n;let r=t.scrollTop;e.target!==document||r||(r=document.body.scrollTop),b.top=r<d.value,b.bottom=r>d.value,g.top=r<=0+(u.top||0),g.bottom=r+t.clientHeight>=t.scrollHeight-(u.bottom||0)-Nt,d.value=r,h.value=!0,O(e),i(e)};return Mt(n,"scroll",o?Q(S,o,!0,!1):S,s),Mt(n,"scrollend",w,s),{x:f,y:p,isScrolling:h,arrivedState:g,directions:b}}(n,(s=Wt({},o),d={offset:Wt({[i]:null!=(a=o.distance)?a:0},o.offset)},qt(s,Rt(d)))));var s,d;v((()=>u.arrivedState[i]),(async e=>{var t,l;if(e){const e=$(n),a={height:null!=(t=null==e?void 0:e.scrollHeight)?t:0,width:null!=(l=null==e?void 0:e.scrollWidth)?l:0};await r(u),o.preserveScrollPosition&&e&&c((()=>{e.scrollTo({top:e.scrollHeight-a.height,left:e.scrollWidth-a.width})}))}}))}const Qt={mounted(e,t){"function"==typeof t.value?Yt(e,t.value):Yt(e,...t.value)}};function Ut(e){return"function"==typeof e?e():l(e)}(()=>{let e=!1;const n=t(!1)})(),"undefined"!=typeof WorkerGlobalScope&&(globalThis,WorkerGlobalScope);const Vt=new WeakMap;function Gt(e,t,o={}){const{mode:l="replace",route:a=g(),router:u=b(),transform:s=(e=>e)}=o;Vt.has(u)||Vt.set(u,new Map);const d=Vt.get(u);let f=a.query[e];var p;let m;p=()=>{f=void 0},n()&&r(p);const h=i(((n,r)=>(m=r,{get:()=>(n(),s(void 0!==f?f:Ut(t))),set(n){f!==n&&(f=n===t||null===n?void 0:n,d.set(e,n===t||null===n?void 0:n),r(),c((()=>{if(0===d.size)return;const e=Object.fromEntries(d.entries());d.clear();const{params:t,query:n,hash:r}=a;u[Ut(l)]({params:t,query:{...n,...e},hash:r})})))}})));return v((()=>a.query[e]),(e=>{f=e,m()}),{flush:"sync"}),h}var Jt=Object.defineProperty,Xt=Object.getOwnPropertySymbols,Zt=Object.prototype.hasOwnProperty,Kt=Object.prototype.propertyIsEnumerable,en=(e,t,n)=>t in e?Jt(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,tn=(e,t)=>{for(var n in t||(t={}))Zt.call(t,n)&&en(e,n,t[n]);if(Xt)for(var n of Xt(t))Kt.call(t,n)&&en(e,n,t[n]);return e};function nn(e,{doNotParse:n=!1,autoUpdateDependencies:r=!1}={},o=new w){const l=r?[...e||[]]:e;let a=o.getAll({doNotParse:!0});const i=t(0),u=()=>{const e=o.getAll({doNotParse:!0});(function(e,t,n){if(!e)return!0;for(const r of e)if(t[r]!==n[r])return!0;return!1})(l||null,e,a)&&i.value++,a=e};return o.addChangeListener(u),H((()=>{o.removeChangeListener(u)})),{get:(...e)=>(r&&l&&!l.includes(e[0])&&l.push(e[0]),i.value,o.get(e[0],tn({doNotParse:n},e[1]))),getAll:(...e)=>(i.value,o.getAll(tn({doNotParse:n},e[0]))),set:(...e)=>o.set(...e),remove:(...e)=>o.remove(...e),addChangeListener:(...e)=>o.addChangeListener(...e),removeChangeListener:(...e)=>o.removeChangeListener(...e)}}var rn=Object.defineProperty,on=Object.defineProperties,ln=Object.getOwnPropertyDescriptors,an=Object.getOwnPropertySymbols,un=Object.prototype.hasOwnProperty,sn=Object.prototype.propertyIsEnumerable,cn=(e,t,n)=>t in e?rn(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,vn=(e,t)=>{for(var n in t||(t={}))un.call(t,n)&&cn(e,n,t[n]);if(an)for(var n of an(t))sn.call(t,n)&&cn(e,n,t[n]);return e},dn=(e,t)=>on(e,ln(t));function fn(...e){const n="string"==typeof e[0]?e[0]:void 0,r=C(n)?1:0;let o={},l=O,a={immediate:!!r,shallow:!0};const i=e=>!!(null==e?void 0:e.request);e.length>0+r&&(i(e[0+r])?l=e[0+r]:o=e[0+r]),e.length>1+r&&i(e[1+r])&&(l=e[1+r]),(e.length===2+r&&!i(e[1+r])||e.length===3+r)&&(a=e[e.length-1]);const u=f(),s=a.shallow?f():t(),c=t(!1),v=t(!1),d=t(!1),p=f(),m=O.CancelToken.source;let h=m();const y=e=>{!c.value&&v.value&&(h.cancel(e),h=m(),d.value=!0,v.value=!1,c.value=!1)},g=e=>{v.value=e,c.value=!e},b=(e,t)=>new Promise(((e,t)=>{ae(c).toBe(!0).then((()=>e(P))).catch(t)})).then(e,t),w=(e=n,t={})=>{p.value=void 0;const r="string"==typeof e?e:null!=n?n:t.url;return void 0===r?(p.value=new S(S.ERR_INVALID_URL),c.value=!0,{then:b}):(y(),g(!0),l(r,dn(vn(vn({},o),"object"==typeof e?e:t),{cancelToken:h.token})).then((e=>{var t;u.value=e;const n=e.data;s.value=n,null==(t=a.onSuccess)||t.call(a,n)})).catch((e=>{var t;p.value=e,null==(t=a.onError)||t.call(a,e)})).finally((()=>g(!1))),{then:b})};a.immediate&&n&&w();const P={response:u,data:s,error:p,finished:c,loading:v,isFinished:c,isLoading:v,cancel:y,isAborted:d,canceled:d,aborted:d,isCanceled:d,abort:y,execute:w};return dn(vn({},P),{then:b})}export{fn as A,V as B,fe as C,Ee as D,ie as E,Fe as F,ve as G,Ot as H,Xe as I,it as J,Te as a,It as b,rt as c,nt as d,Q as e,$e as f,de as g,dt as h,F as i,_e as j,Tt as k,k as l,Dt as m,St as n,ke as o,Gt as p,ot as q,Y as r,He as s,H as t,Ae as u,Qt as v,nn as w,lt as x,ft as y,_ as z};
