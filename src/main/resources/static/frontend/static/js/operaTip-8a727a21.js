import{K as s,F as t,g as e,$ as r}from"./quasar-df1bac18.js";import"./vue-5bfa3a54.js";import{x as o}from"./menuStore-30bf76d3.js";import{s as a,t as i}from"./naive-ui-0ee0b8c3.js";import{h as m,v as p,as as n,az as u,o as l,f as j,a8 as d,x as c,a9 as f,a as v,aa as h,t as _,b}from"./@vue-5e5cdef9.js";import"./@babel-f3c0a00c.js";import"./vue-router-6159329f.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./dayjs-67f8ddef.js";import"./lodash-6d99edc3.js";import"./icons-95011f8c.js";import"./@vicons-f32a0bdb.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./lodash-es-ea7deab5.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./date-fns-tz-0cc073c8.js";import"./async-validator-cf877c1f.js";const g=v("div",{class:"tw-text-xl"},"操作提示",-1),w={id:"title"},x={__name:"operaTip",setup(x){const k=m();o();const y=router.currentRoute.value.href;return p((()=>{location.href=y})),(o,m)=>{const p=s,x=t,q=e,R=n("v-md-editor"),z=a,S=i,T=r,$=u("close-popup");return l(),j(T,null,{default:d((()=>[c(q,{class:"row items-center q-pb-none"},{default:d((()=>[g,c(p),f(c(x,{icon:"close",flat:"",round:"",dense:""},null,512),[[$]])])),_:1}),c(q,{ref_key:"contain",ref:k},{default:d((()=>[v("div",null,[v("h1",w,[h(_(o.$router.currentRoute.href)+" ",1),c(R,{"model-value":"#testdata",mode:"preview"})])]),c(S,{"show-rail":"","show-background":""},{default:d((()=>[c(z,{title:"演示",href:b(y)+"#title"},null,8,["href"])])),_:1})])),_:1},512)])),_:1})}}};export{x as default};
