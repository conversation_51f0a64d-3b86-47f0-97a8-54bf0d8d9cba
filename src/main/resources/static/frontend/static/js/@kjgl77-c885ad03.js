import"./vue-5bfa3a54.js";import{d as e,u as t,b as n,e as r,o as i,c as a,a as o,f as l,r as u,g as s,h as f,j as c,w as d,F as p,k as h,t as g,l as v,m,n as y,p as b,q as x,s as C,v as $,x as w,y as k,z as P,A,B as O,C as L,D as I,E as S,G as M}from"./@vue-5e5cdef9.js";var j=(e=>(e.transparent="rgba(0,0,0,0)",e.black="#000000",e.silver="#C0C0C0",e.gray="#808080",e.white="#FFFFFF",e.maroon="#800000",e.red="#FF0000",e.purple="#800080",e.fuchsia="#FF00FF",e.green="#008000",e.lime="#00FF00",e.olive="#808000",e.yellow="#FFFF00",e.navy="#000080",e.blue="#0000FF",e.teal="#008080",e.aqua="#00FFFF",e.aliceblue="#f0f8ff",e.antiquewhite="#faebd7",e.aquamarine="#7fffd4",e.azure="#f0ffff",e.beige="#f5f5dc",e.bisque="#ffe4c4",e.blanchedalmond="#ffebcd",e.blueviolet="#8a2be2",e.brown="#a52a2a",e.burlywood="#deb887",e.cadetblue="#5f9ea0",e.chartreuse="#7fff00",e.chocolate="#d2691e",e.coral="#ff7f50",e.cornflowerblue="#6495ed",e.cornsilk="#fff8dc",e.crimson="#dc143c",e.cyan="#00ffff",e.darkblue="#00008b",e.darkcyan="#008b8b",e.darkgoldenrod="#b8860b",e.darkgray="#a9a9a9",e.darkgreen="#006400",e.darkgrey="#a9a9a9",e.darkkhaki="#bdb76b",e.darkmagenta="#8b008b",e.darkolivegreen="#556b2f",e.darkorange="#ff8c00",e.darkorchid="#9932cc",e.darkred="#8b0000",e.darksalmon="#e9967a",e.darkseagreen="#8fbc8f",e.darkslateblue="#483d8b",e.darkslategray="#2f4f4f",e.darkslategrey="#2f4f4f",e.darkturquoise="#00ced1",e.darkviolet="#9400d3",e.deeppink="#ff1493",e.deepskyblue="#00bfff",e.dimgray="#696969",e.dimgrey="#696969",e.dodgerblue="#1e90ff",e.firebrick="#b22222",e.floralwhite="#fffaf0",e.forestgreen="#228b22",e.gainsboro="#dcdcdc",e.ghostwhite="#f8f8ff",e.gold="#ffd700",e.goldenrod="#daa520",e.greenyellow="#adff2f",e.grey="#808080",e.honeydew="#f0fff0",e.hotpink="#ff69b4",e.indianred="#cd5c5c",e.indigo="#4b0082",e.ivory="#fffff0",e.khaki="#f0e68c",e.lavender="#e6e6fa",e.lavenderblush="#fff0f5",e.lawngreen="#7cfc00",e.lemonchiffon="#fffacd",e.lightblue="#add8e6",e.lightcoral="#f08080",e.lightcyan="#e0ffff",e.lightgoldenrodyellow="#fafad2",e.lightgray="#d3d3d3",e.lightgreen="#90ee90",e.lightgrey="#d3d3d3",e.lightpink="#ffb6c1",e.lightsalmon="#ffa07a",e.lightseagreen="#20b2aa",e.lightskyblue="#87cefa",e.lightslategray="#778899",e.lightslategrey="#778899",e.lightsteelblue="#b0c4de",e.lightyellow="#ffffe0",e.limegreen="#32cd32",e.linen="#faf0e6",e.magenta="#ff00ff",e.mediumaquamarine="#66cdaa",e.mediumblue="#0000cd",e.mediumorchid="#ba55d3",e.mediumpurple="#9370db",e.mediumseagreen="#3cb371",e.mediumslateblue="#7b68ee",e.mediumspringgreen="#00fa9a",e.mediumturquoise="#48d1cc",e.mediumvioletred="#c71585",e.midnightblue="#191970",e.mintcream="#f5fffa",e.mistyrose="#ffe4e1",e.moccasin="#ffe4b5",e.navajowhite="#ffdead",e.oldlace="#fdf5e6",e.olivedrab="#6b8e23",e.orange="#ffa500",e.orangered="#ff4500",e.orchid="#da70d6",e.palegoldenrod="#eee8aa",e.palegreen="#98fb98",e.paleturquoise="#afeeee",e.palevioletred="#db7093",e.papayawhip="#ffefd5",e.peachpuff="#ffdab9",e.peru="#cd853f",e.pink="#ffc0cb",e.plum="#dda0dd",e.powderblue="#b0e0e6",e.rosybrown="#bc8f8f",e.royalblue="#4169e1",e.saddlebrown="#8b4513",e.salmon="#fa8072",e.sandybrown="#f4a460",e.seagreen="#2e8b57",e.seashell="#fff5ee",e.sienna="#a0522d",e.skyblue="#87ceeb",e.slateblue="#6a5acd",e.slategray="#708090",e.snow="#fffafa",e.springgreen="#00ff7f",e.steelblue="#4682b4",e.tan="#d2b48c",e.thistle="#d8bfd8",e.tomato="#ff6347",e.turquoise="#40e0d0",e.violet="#ee82ee",e.wheat="#f5deb3",e.whitesmoke="#f5f5f5",e.yellowgreen="#9acd32",e))(j||{});function F(e){return"string"==typeof e&&(e=e.toLowerCase(),/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(e))}function _(e){return"string"==typeof e&&(e=e.toLowerCase(),/^(rgba|RGBA)/.test(e))}function G(e){return/^(rgb|rgba|RGB|RGBA)/.test(e)}function E(e){if(F(e)||G(e))return e;const t=function(e){return j[e]}(e);if(!t)throw new Error(`Color: Invalid Input of ${e}`);return t}function B(e){const t=E(e).toLowerCase();return F(t)?function(e){3===(e=e.replace("#","")).length&&(e=Array.from(e).map((e=>e+e)).join(""));const t=e.split("");return new Array(3).fill(0).map(((e,n)=>parseInt(`0x${t[2*n]}${t[2*n+1]}`)))}(t):function(e){return e.replace(/rgb\(|rgba\(|\)/g,"").split(",").slice(0,3).map((e=>parseInt(e)))}(t)}function D(e){const t=E(e);return _(t)?Number(t.toLowerCase().split(",").slice(-1)[0].replace(/[)|\s]/g,"")):1}function W(e){const t=B(e);return t&&[...t,D(e)]}function N(e){if(!Array.isArray(e))throw new Error(`getColorFromRgbValue: ${e} is not an array`);const{length:t}=e;if(3!==t&&4!==t)throw new Error("getColorFromRgbValue: value length should be 3 or 4");return(3===t?"rgb(":"rgba(")+e.join(",")+")"}function T(e,t=0){let n=W(e);return n=n.map(((e,n)=>3===n?e:e+Math.ceil(2.55*t))).map((e=>e>255?255:e)),N(n)}function R(e,t=100){return N([...B(e),t/100])}const z=Object.freeze(Object.defineProperty({__proto__:null,darken:function(e,t=0){let n=W(e);return n=n.map(((e,n)=>3===n?e:e-Math.ceil(2.55*t))).map((e=>e<0?0:e)),N(n)},fade:R,getColorFromRgbValue:N,getOpacity:D,getRgbValue:B,getRgbaValue:W,isHex:F,isRgb:function(e){return"string"==typeof e&&(e=e.toLowerCase(),/^(rgb\(|RGB\()/.test(e))},isRgbOrRgba:G,isRgba:_,lighten:T,toHex:function(e){return F(e)?e:`#${B(e).map((e=>Number(e).toString(16).padStart(2,"0"))).join("")}`},toRgb:function(e,t){const n=B(e);return"number"==typeof t?`rgba(${n.join(",")},${t})`:`rgb(${n.join(",")})`}},Symbol.toStringTag,{value:"Module"})),H=(e,t)=>{const n=e.__vccOpts||e;for(const[r,i]of t)n[r]=i;return n},U={},V={viewBox:"0 0 187 38",preserveAspectRatio:"none",class:"dv-button-svg"},Q=[o("g",{style:{transform:"translate(2px, 2px)"}},[o("g",null,[o("path",{"data-type":"shape",d:"M0,0 L0,34 L168,34 L183,19 L183,0",class:"dv-button-svg-bg"})]),o("path",{"data-type":"polyline",d:"M0,34 L168,34 L183,19",class:"dv-button-svg-line"})],-1)];const q=H(U,[["render",function(e,t){return i(),a("svg",V,Q)}]]),Y={},X={viewBox:"0 0 167 38",preserveAspectRatio:"none",class:"dv-button-svg"},J=[O('<g style="transform:translate(2px, 2px);"><g><path data-type="shape" d="M0,0 L0,34 L163,34 L163,0" class="dv-button-svg-bg"></path></g><path data-type="polyline" d="M0,0 L164.1,0" class="dv-button-svg-line"></path><path data-type="polyline" d="M163,0 L163,34" class="dv-button-svg-line"></path><path data-type="polyline" d="M164.1,34 L0,34" class="dv-button-svg-line"></path><path data-type="polyline" d="M1.1,34 L1.1,0" class="dv-button-svg-line"></path></g>',1)];const K=H(Y,[["render",function(e,t){return i(),a("svg",X,J)}]]),Z={},ee={viewBox:"0 0 167 38",preserveAspectRatio:"none",class:"dv-button-svg"},te=[O('<g style="transform:translate(2px, 2px);"><g><path data-type="shape" d="M1,1 L1,33 L162,33 L162,1" class="dv-button-svg-bg"></path></g><path data-type="polyline" d="M0,0 L0,10" class="dv-button-svg-line"></path><path data-type="polyline" d="M-1.1,0 L10,0" class="dv-button-svg-line"></path><path data-type="polyline" d="M164.1,0 L153,0" class="dv-button-svg-line"></path><path data-type="polyline" d="M163,0 L163,10" class="dv-button-svg-line"></path><path data-type="polyline" d="M164.1,34 L153,34" class="dv-button-svg-line"></path><path data-type="polyline" d="M163,34 L163,24" class="dv-button-svg-line"></path><path data-type="polyline" d="M0,34 L0,24" class="dv-button-svg-line"></path><path data-type="polyline" d="M-1.1,34 L10,34" class="dv-button-svg-line"></path></g>',1)];const ne=H(Z,[["render",function(e,t){return i(),a("svg",ee,te)}]]),re={},ie={viewBox:"0 0 187 38",preserveAspectRatio:"none",class:"dv-button-svg"},ae=[o("g",{style:{transform:"translate(2px, 2px)"}},[o("g",null,[o("path",{"data-type":"shape",d:"M0,34 L168,34 L183,19 L183,0 L0,0",class:"dv-button-svg-bg"})]),o("path",{"data-type":"polyline",d:"M0,34 L168,34 L183,19 L183,0",class:"dv-button-svg-line"}),o("path",{"data-type":"polyline",d:"M184.1,0 L0,0 L0,34.7",class:"dv-button-svg-line"})],-1)];const oe=H(re,[["render",function(e,t){return i(),a("svg",ie,ae)}]]),le={},ue={viewBox:"0 0 187 38",preserveAspectRatio:"none",class:"dv-button-svg"},se=[o("g",{style:{transform:"translate(2px, 2px)"}},[o("g",null,[o("path",{"data-type":"shape",d:"M0,34 L168,34 L183,19 L183,0 L15,0 L0,15",class:"dv-button-svg-bg"})]),o("path",{"data-type":"polyline",d:"M0,34 L168,34 L183,19 L183,0",class:"dv-button-svg-line"}),o("path",{"data-type":"polyline",d:"M183,0 L15,0 L0,15 L0,34",class:"dv-button-svg-line"})],-1)];const fe=H(le,[["render",function(e,t){return i(),a("svg",ue,se)}]]),ce={},de={viewBox:"0 0 167 38",preserveAspectRatio:"none",class:"dv-button-svg"},pe=[O('<g style="transform:translate(2px, 2px);"><g><path data-type="shape" d="M0,0 L0,34 L163,34 L163,0" class="dv-button-svg-bg"></path></g><path data-type="polyline" d="M0,0 L81.6,0" class="dv-button-svg-line"></path><path data-type="polyline" d="M163,0 L81.4,0" class="dv-button-svg-line"></path><path data-type="polyline" d="M0,34 L81.6,34" class="dv-button-svg-line"></path><path data-type="polyline" d="M163,34 L81.4,34" class="dv-button-svg-line"></path><path data-type="polyline" d="M0,1 L10,1" class="dv-button-svg-line"></path><path data-type="polyline" d="M163,1 L153,1" class="dv-button-svg-line"></path><path data-type="polyline" d="M0,33 L10,33" class="dv-button-svg-line"></path><path data-type="polyline" d="M163,33 L153,33" class="dv-button-svg-line"></path></g>',1)];const he={class:"dv-button-wrapper"},ge={class:"dv-button"},ve={class:"dv-button-svg-container"},me={class:"dv-button-text"},ye=e({components:{Border1:q,Border2:K,Border3:ne,Border4:oe,Border5:fe,Border6:H(ce,[["render",function(e,t){return i(),a("svg",de,pe)}]])},__name:"index",props:{color:{default:"#2058c7"},fontColor:{default:""},bg:{type:Boolean,default:!0},border:{default:"Border1"},fontSize:{default:14}},setup(e){t((e=>({"3b09a6e4":n(g),"5f757885":e.color,"505f902a":n(d),"714af7a5":n(c),ea6738d4:n(p),"1e0a24df":n(h)})));const f=e,c=r((()=>T(f.color,40))),d=r((()=>""===f.fontColor?f.color:f.fontColor)),p=r((()=>T(d.value,40))),h=r((()=>f.bg?.1:0)),g=r((()=>`${f.fontSize}px`));return(e,t)=>(i(),a("div",he,[o("button",ge,[o("div",ve,[(i(),l(u(e.border)))]),o("div",me,[s(e.$slots,"default")])])]))}}),be={install(e){e.component("DvButton",ye)}};function xe(e){return"function"==typeof e?e():n(e)}const Ce=typeof window<"u"&&typeof document<"u";typeof WorkerGlobalScope<"u"&&(globalThis,WorkerGlobalScope);const $e=Object.prototype.toString,we=()=>{};function ke(e,t=200,n={}){return function(e,t){return function(...n){return new Promise(((r,i)=>{Promise.resolve(e((()=>t.apply(this,n)),{fn:t,thisArg:this,args:n})).then(r).catch(i)}))}}(function(e,t={}){let n,r,i=we;const a=e=>{clearTimeout(e),i(),i=we};return o=>{const l=xe(e),u=xe(t.maxWait);return n&&a(n),l<=0||void 0!==u&&u<=0?(r&&(a(r),r=null),Promise.resolve(o())):new Promise(((e,s)=>{i=t.rejectOnCancel?s:e,u&&!r&&(r=setTimeout((()=>{n&&a(n),r=null,e(o())}),u)),n=setTimeout((()=>{r&&a(r),r=null,e(o())}),l)}))}}(t,n),e)}function Pe(e){var t;const n=xe(e);return null!=(t=null==n?void 0:n.$el)?t:n}const Ae=Ce?window:void 0;function Oe(...e){let t,n,r,i;if("string"==typeof e[0]||Array.isArray(e[0])?([n,r,i]=e,t=Ae):[t,n,r,i]=e,!t)return we;Array.isArray(n)||(n=[n]),Array.isArray(r)||(r=[r]);const a=[],o=()=>{a.forEach((e=>e())),a.length=0},l=d((()=>[Pe(t),xe(i)]),(([e,t])=>{if(o(),!e)return;const i=(e=>"[object Object]"===$e.call(e))(t)?{...t}:t;a.push(...n.flatMap((t=>r.map((n=>((e,t,n,r)=>(e.addEventListener(t,n,r),()=>e.removeEventListener(t,n,r)))(e,t,n,i))))))}),{immediate:!0,flush:"post"}),u=()=>{l(),o()};return function(e){!!S()&&M(e)}(u),u}function Le(e,t){return 1===arguments.length?parseInt((Math.random()*e+1).toString(),10):parseInt((Math.random()*(t-e+1)+e).toString(),10)}function Ie(e,t){const n=Math.abs(e[0]-t[0]),r=Math.abs(e[1]-t[1]);return Math.sqrt(n*n+r*r)}function Se(e,t,n,r){return[e+Math.cos(r)*n,t+Math.sin(r)*n]}function Me(e){return function(e){return(e=function(e){return e.filter((e=>"number"==typeof e))}(e)).reduce(((e,t)=>e+t),0)}(new Array(e.length-1).fill(0).map(((t,n)=>[e[n],e[n+1]])).map((e=>function(e,t){const n=Math.abs(e.x-t.x),r=Math.abs(e.y-t.y);return Math.sqrt(n*n+r*r)}(e[0],e[1]))))}function je(e){return`${e.x},${e.y}`}function Fe(e){return e.map(je).join(" ")}function _e(e){return(e?"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx":"xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx").replace(/[xy]/g,(e=>{const t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))}function Ge(e,t){for(const n in t)e[n]&&"object"==typeof e[n]?Ge(e[n],t[n]):"object"!=typeof t[n]?e[n]=t[n]:e[n]=Ee(t[n],!0);return e}function Ee(e,t){if(!e)return e;const{parse:n,stringify:r}=JSON;if(!t)return n(r(e));const i=Array.isArray(e)?[]:{};if(e&&"object"==typeof e)for(const a in e)Object.prototype.hasOwnProperty.call(e,a)&&(e[a]&&"object"==typeof e[a]?i[a]=Ee(e[a],!0):i[a]=e[a]);return i}const Be=(e,t,n)=>{const r=f(0),i=f(0);let a,o=null,l=null;const u=(n=!0)=>new Promise((a=>{y((()=>{l=e.value,r.value=e.value?e.value.clientWidth:0,i.value=e.value?e.value.clientHeight:0,e.value&&(!r.value||i.value),"function"==typeof t&&n&&t(),a(!0)}))})),s=()=>{o=function(e,t){const n=new(0,window.MutationObserver)(t);return n.observe(e,{attributes:!0,attributeFilter:["style"],attributeOldValue:!0}),n}(l,a),Oe(window,"resize",a)},c=async()=>{await u(!1),a=ke(u,200),s(),"function"==typeof n&&n()};return m((()=>{c()})),$((()=>{o&&(o.disconnect(),o.takeRecords(),o=null)})),{width:r,height:i,initWH:u}},De=["width","height"],We=["d","fill"],Ne=["fill","x","y"],Te=["xlink:href","width","height","x","y"],Re=["fill","x","y"],ze={__name:"index",props:{config:{type:Object,default:()=>({})}},setup(e){t((e=>({"5914205c":n(b)})));const l=e,u=f(null),{width:s,height:m}=Be(u,(function(){x()}),(function(){x()})),y=c({defaultConfig:{data:[],img:[],fontSize:12,imgSideLength:30,columnColor:"rgba(0, 194, 255, 0.4)",textColor:"#fff",showValue:!1,sort:!0},mergedConfig:null,column:[]}),b=r((()=>`${l.config.fontSize?l.config.fontSize:y.defaultConfig.fontSize}px`));function x(){y.mergedConfig=Ge(Ee(y.defaultConfig,!0),l.config||{}),function(){let{data:e}=y.mergedConfig;const{sort:t}=y.mergedConfig;e=Ee(e,!0),t&&e.sort((({value:e},{value:t})=>e>t?-1:e<t?1:0));const n=Math.max(...e.map((e=>e.value)));e=e.map((e=>({...e,percent:0===n?0:e.value/n}))),y.mergedConfig.data=e}(),function(){const{imgSideLength:e,fontSize:t,data:n}=y.mergedConfig,r=n.length,i=s.value/(r+1),a=m.value-e-t-5,o=m.value-t-5;y.column=n.map(((e,n)=>{const{percent:r}=e,l=i*(n+1),u=i*n,s=o-a*r,f=a*r*.6+s,c=`\n          M${u}, ${o}\n          Q${l}, ${f} ${l},${s}\n          M${l},${s}\n          Q${l}, ${f} ${i*(n+2)},${o}\n          L${u}, ${o}\n          Z\n        `,d=(o+s)/2+t/2;return{...e,d:c,x:l,y:s,textY:d}}))}()}return d((()=>l.config),(()=>{x()}),{deep:!0}),(e,t)=>(i(),a("div",{ref_key:"conicalColumnChart",ref:u,class:"dv-conical-column-chart"},[(i(),a("svg",{width:n(s),height:n(m)},[(i(!0),a(p,null,h(n(y).column,((e,t)=>(i(),a("g",{key:t},[o("path",{d:e.d,fill:n(y).mergedConfig.columnColor},null,8,We),o("text",{fill:n(y).mergedConfig.textColor,x:e.x,y:n(m)-4},g(e.name),9,Ne),n(y).mergedConfig.img.length?(i(),a("image",{key:0,"xlink:href":n(y).mergedConfig.img[t%n(y).mergedConfig.img.length],width:n(y).mergedConfig.imgSideLength,height:n(y).mergedConfig.imgSideLength,x:e.x-n(y).mergedConfig.imgSideLength/2,y:e.y-n(y).mergedConfig.imgSideLength},null,8,Te)):v("",!0),n(y).mergedConfig.showValue?(i(),a("text",{key:1,fill:n(y).mergedConfig.textColor,x:e.x,y:e.textY},g(e.value),9,Re)):v("",!0)])))),128))],8,De))],512))}},He={install(e){e.component("DvConicalColumnChart",ze)}},Ue=["id"],Ve=["offset","stop-color"],Qe=["id","x2"],qe=["offset","stop-color"],Ye=["x","y","rx","ry","stroke-width","stroke","width","height"],Xe=["stroke-width","stroke-dasharray","stroke","points"],Je=["stroke","fill","x","y"],Ke={__name:"index",props:{config:{type:Object,default:()=>({})}},setup(e){const t=e,l=_e(),u=f(null),s=c({gradientId1:`percent-pond-gradientId1-${l}`,gradientId2:`percent-pond-gradientId2-${l}`,width:0,height:0,defaultConfig:{value:0,colors:["#3DE7C9","#00BAFF"],borderWidth:3,borderGap:3,lineDash:[5,1],textColor:"#fff",borderRadius:5,localGradient:!1,formatter:"{value}%"},mergedConfig:null}),v=r((()=>{if(!s.mergedConfig)return 0;const{borderWidth:e}=s.mergedConfig;return s.width-e})),b=r((()=>{if(!s.mergedConfig)return 0;const{borderWidth:e}=s.mergedConfig;return s.height-e})),x=r((()=>{const e=s.height/2;if(!s.mergedConfig)return`0, ${e} 0, ${e}`;const{borderWidth:t,borderGap:n,value:r}=s.mergedConfig;return`\n        ${t+n}, ${e}\n        ${t+n+(s.width-2*(t+n))/100*r}, ${e+.001}\n      `})),C=r((()=>{if(!s.mergedConfig)return 0;const{borderWidth:e,borderGap:t}=s.mergedConfig;return s.height-2*(e+t)})),$=r((()=>{if(!s.mergedConfig)return[];const{colors:e}=s.mergedConfig,t=100/(e.length-1);return e.map(((e,n)=>[t*n,e]))})),w=r((()=>s.mergedConfig&&s.mergedConfig.localGradient?s.gradientId1:s.gradientId2)),k=r((()=>{if(!s.mergedConfig)return"100%";const{value:e}=s.mergedConfig;return 200-e+"%"})),P=r((()=>{if(!s.mergedConfig)return"";const{value:e,formatter:t}=s.mergedConfig;return t.replace("{value}",e)}));function A(){s.mergedConfig=Ge(Ee(s.defaultConfig,!0),t.config||{})}return d((()=>t.config),(()=>{A()}),{deep:!0}),m((()=>{!async function(){await async function(){await y();const{clientWidth:e,clientHeight:t}=u.value;s.width=e,s.height=t}(),t.config&&A()}()})),(e,t)=>(i(),a("div",{ref_key:"percentPond",ref:u,class:"dv-percent-pond"},[(i(),a("svg",null,[o("defs",null,[o("linearGradient",{id:n(s).gradientId1,x1:"0%",y1:"0%",x2:"100%",y2:"0%"},[(i(!0),a(p,null,h(n($),(e=>(i(),a("stop",{key:e[0],offset:`${e[0]}%`,"stop-color":e[1]},null,8,Ve)))),128))],8,Ue),o("linearGradient",{id:n(s).gradientId2,x1:"0%",y1:"0%",x2:n(k),y2:"0%"},[(i(!0),a(p,null,h(n($),(e=>(i(),a("stop",{key:e[0],offset:`${e[0]}%`,"stop-color":e[1]},null,8,qe)))),128))],8,Qe)]),o("rect",{x:n(s).mergedConfig?n(s).mergedConfig.borderWidth/2:"0",y:n(s).mergedConfig?n(s).mergedConfig.borderWidth/2:"0",rx:n(s).mergedConfig?n(s).mergedConfig.borderRadius:"0",ry:n(s).mergedConfig?n(s).mergedConfig.borderRadius:"0",fill:"transparent","stroke-width":n(s).mergedConfig?n(s).mergedConfig.borderWidth:"0",stroke:`url(#${n(s).gradientId1})`,width:n(v)>0?n(v):0,height:n(b)>0?n(b):0},null,8,Ye),o("polyline",{"stroke-width":n(C),"stroke-dasharray":n(s).mergedConfig?n(s).mergedConfig.lineDash.join(","):"0",stroke:`url(#${n(w)})`,points:n(x)},null,8,Xe),o("text",{stroke:n(s).mergedConfig?n(s).mergedConfig.textColor:"#fff",fill:n(s).mergedConfig?n(s).mergedConfig.textColor:"#fff",x:n(s).width/2,y:n(s).height/2},g(n(P)),9,Je)]))],512))}},Ze={install(e){e.component("DvPercentPond",Ke)}};function et(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function tt(e){if(e.__esModule)return e;var t=e.default;if("function"==typeof t){var n=function e(){return this instanceof e?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};n.prototype=t.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(e).forEach((function(t){var r=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(n,t,r.get?r:{enumerable:!0,get:function(){return e[t]}})})),n}var nt,rt={},it={exports:{}};(nt=it).exports=function(e){return e&&e.__esModule?e:{default:e}},nt.exports.__esModule=!0,nt.exports.default=nt.exports;var at,ot=it.exports,lt={},ut={exports:{}};function st(){return at||(at=1,function(e){e.exports=function(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},e.exports.__esModule=!0,e.exports.default=e.exports}(ut)),ut.exports}var ft,ct,dt={exports:{}},pt={exports:{}},ht={exports:{}};function gt(){return ft||(ft=1,function(e){e.exports=function(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r},e.exports.__esModule=!0,e.exports.default=e.exports}(ht)),ht.exports}var vt,mt={exports:{}};var yt,bt={exports:{}};function xt(){return yt||(yt=1,function(e){var t=gt();e.exports=function(e,n){if(e){if("string"==typeof e)return t(e,n);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return t(e,n)}},e.exports.__esModule=!0,e.exports.default=e.exports}(bt)),bt.exports}var Ct,$t,wt={exports:{}};function kt(){return $t||($t=1,function(e){var t=(ct||(ct=1,function(e){var t=gt();e.exports=function(e){if(Array.isArray(e))return t(e)},e.exports.__esModule=!0,e.exports.default=e.exports}(pt)),pt.exports),n=(vt||(vt=1,function(e){e.exports=function(e){if(typeof Symbol<"u"&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)},e.exports.__esModule=!0,e.exports.default=e.exports}(mt)),mt.exports),r=xt(),i=(Ct||(Ct=1,function(e){e.exports=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports}(wt)),wt.exports);e.exports=function(e){return t(e)||n(e)||r(e)||i()},e.exports.__esModule=!0,e.exports.default=e.exports}(dt)),dt.exports}var Pt,At={exports:{}};function Ot(){return Pt||(Pt=1,function(e){e.exports=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports}(At)),At.exports}const Lt=tt(z);var It,St={},Mt={},jt={exports:{}},Ft={exports:{}};var _t,Gt={exports:{}};var Et,Bt,Dt,Wt={exports:{}};function Nt(){return Bt||(Bt=1,function(e){var t=(It||(It=1,function(e){e.exports=function(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports.default=e.exports}(Ft)),Ft.exports),n=(_t||(_t=1,function(e){e.exports=function(e,t){var n=null==e?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,a=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);o=!0);}catch(u){l=!0,i=u}finally{try{!o&&null!=n.return&&n.return()}finally{if(l)throw i}}return a}},e.exports.__esModule=!0,e.exports.default=e.exports}(Gt)),Gt.exports),r=xt(),i=(Et||(Et=1,function(e){e.exports=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports}(Wt)),Wt.exports);e.exports=function(e,a){return t(e)||n(e,a)||r(e,a)||i()},e.exports.__esModule=!0,e.exports.default=e.exports}(jt)),jt.exports}function Tt(){return Dt||(Dt=1,function(e){var t=ot;Object.defineProperty(e,"__esModule",{value:!0}),e.bezierCurveToPolyline=h,e.getBezierCurveLength=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5;return!!e&&(e instanceof Array&&("number"==typeof t&&c(d([s(e,t).segmentPoints])[0])))},e.default=void 0;var n=t(Nt()),r=t(kt()),i=Math.sqrt,a=Math.pow,o=Math.ceil,l=Math.abs,u=50;function s(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5,n=e.length-1,i=e[0],a=e[n][2],s=e.slice(1),h=s.map((function(e,t){var n=0===t?i:s[t-1][2];return f.apply(void 0,[n].concat((0,r.default)(e)))})),g=function(e,t,n,r){var i=4,a=1,u=function(){var u=e.reduce((function(e,t){return e+t.length}),0);e.forEach((function(e,t){return e.push(n[t][2])}));var s=d(e),f=s.reduce((function(e,t){return e+t.length}),0),h=s.map((function(e){return c(e)})),g=c(h),v=g/f,m=function(e,t){return e.map((function(e){return e.map((function(e){return l(e-t)}))})).map((function(e){return c(e)})).reduce((function(e,t){return e+t}),0)}(s,v);if(m<=r)return"break";u=o(v/r*u*1.1);var y=h.map((function(e){return o(e/g*u)}));e=p(t,y),u=e.reduce((function(e,t){return e+t.length}),0);var b=JSON.parse(JSON.stringify(e));b.forEach((function(e,t){return e.push(n[t][2])})),f=(s=d(b)).reduce((function(e,t){return e+t.length}),0),h=s.map((function(e){return c(e)})),g=c(h),v=g/f;var x=1/u/10;t.forEach((function(t,n){for(var r=y[n],a=new Array(r).fill("").map((function(e,t){return t/y[n]})),o=0;o<i;o++)for(var l=d([e[n]])[0].map((function(e){return e-v})),u=0,s=0;s<r;s++){if(0===s)return;u+=l[s-1],a[s]-=x*u,a[s]>1&&(a[s]=1),a[s]<0&&(a[s]=0),e[n][s]=t(a[s])}})),i*=4,a++};do{if("break"===u())break}while(i<=1025);return e=e.reduce((function(e,t){return e.concat(t)}),[]),{segmentPoints:e,cycles:a,rounds:i}}(p(h,new Array(n).fill(u)),h,s,t);return g.segmentPoints.push(a),g}function f(e,t,n,r){return function(i){var o=1-i,l=a(o,3),u=a(o,2),s=a(i,3),f=a(i,2);return[e[0]*l+3*t[0]*i*u+3*n[0]*f*o+r[0]*s,e[1]*l+3*t[1]*i*u+3*n[1]*f*o+r[1]*s]}}function c(e){return e.reduce((function(e,t){return e+t}),0)}function d(e){return e.map((function(e,t){return new Array(e.length-1).fill(0).map((function(t,r){return function(e,t){var r=(0,n.default)(e,2),o=r[0],l=r[1],u=(0,n.default)(t,2),s=u[0],f=u[1];return i(a(o-s,2)+a(l-f,2))}(e[r],e[r+1])}))}))}function p(e,t){return e.map((function(e,n){var r=1/t[n];return new Array(t[n]).fill("").map((function(t,n){return e(n*r)}))}))}function h(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5;return!!e&&(e instanceof Array&&("number"==typeof t&&s(e,t).segmentPoints))}var g=h;e.default=g}(Mt)),Mt}var Rt,zt,Ht={};function Ut(){return Rt||(Rt=1,function(e){var t=ot;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=t(Nt()),r=t(kt());function i(e,t){var r=(0,n.default)(e,2),i=r[0],a=r[1],o=(0,n.default)(t,2),l=o[0],u=o[1];return[l+(l-i),u+(u-a)]}var a=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:.25,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.25;if(!(e instanceof Array))return!1;if(e.length<=2)return!1;var o=e[0],l=e.length-1,u=new Array(l).fill(0).map((function(i,o){return[].concat((0,r.default)(function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.25,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:.25,a=e.length;if(!(a<3||t>=a)){var o=t-1;o<0&&(o=n?a+o:0);var l=t+1;l>=a&&(l=n?l-a:a-1);var u=t+2;u>=a&&(u=n?u-a:a-1);var s=e[o],f=e[t],c=e[l],d=e[u];return[[f[0]+r*(c[0]-s[0]),f[1]+r*(c[1]-s[1])],[c[0]-i*(d[0]-f[0]),c[1]-i*(d[1]-f[1])]]}}(e,o,t,n,a)),[e[o+1]])}));return t&&function(e,t){var n=e[0],r=e.slice(-1)[0];e.push([i(r[1],r[2]),i(n[0],t),t])}(u,o),u.unshift(e[0]),u};e.default=a}(Ht)),Ht}function Vt(){return zt||(zt=1,function(e){var t=ot;Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"bezierCurveToPolyline",{enumerable:!0,get:function(){return n.bezierCurveToPolyline}}),Object.defineProperty(e,"getBezierCurveLength",{enumerable:!0,get:function(){return n.getBezierCurveLength}}),Object.defineProperty(e,"polylineToBezierCurve",{enumerable:!0,get:function(){return r.default}}),e.default=void 0;var n=Tt(),r=t(Ut()),i={bezierCurveToPolyline:n.bezierCurveToPolyline,getBezierCurveLength:n.getBezierCurveLength,polylineToBezierCurve:r.default};e.default=i}(St)),St}var Qt,qt,Yt={},Xt={exports:{}};function Jt(){return Qt||(Qt=1,function(e){function t(n){return e.exports=t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,t(n)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}(Xt)),Xt.exports}function Kt(){return qt||(qt=1,function(e){var t=ot;Object.defineProperty(e,"__esModule",{value:!0}),e.deepClone=d,e.eliminateBlur=p,e.checkPointIsInCircle=h,e.getTwoPointDistance=g,e.checkPointIsInPolygon=v,e.checkPointIsInSector=m,e.checkPointIsNearPolyline=b,e.checkPointIsInRect=function(e,t,n,i,a){var o=(0,r.default)(e,2),l=o[0],u=o[1];return!(l<t||u<n||l>t+i||u>n+a)},e.getRotatePointPos=x,e.getScalePointPos=C,e.getTranslatePointPos=$,e.getDistanceBetweenPointAndLine=w,e.getCircleRadianPoint=k,e.getRegularPolygonPoints=P,e.default=void 0;var n=t(kt()),r=t(Nt()),i=t(Jt()),a=Math.abs,o=Math.sqrt,l=Math.sin,u=Math.cos,s=Math.max,f=Math.min,c=Math.PI;function d(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e)return e;var n=JSON.parse,r=JSON.stringify;if(!t)return n(r(e));var a=e instanceof Array?[]:{};if(e&&"object"===(0,i.default)(e))for(var o in e)e.hasOwnProperty(o)&&(e[o]&&"object"===(0,i.default)(e[o])?a[o]=d(e[o],!0):a[o]=e[o]);return a}function p(e){return e.map((function(e){var t=(0,r.default)(e,2),n=t[0],i=t[1];return[parseInt(n)+.5,parseInt(i)+.5]}))}function h(e,t,n,r){return g(e,[t,n])<=r}function g(e,t){var n=(0,r.default)(e,2),i=n[0],l=n[1],u=(0,r.default)(t,2),s=u[0],f=u[1],c=a(i-s),d=a(l-f);return o(c*c+d*d)}function v(e,t){for(var n=0,i=(0,r.default)(e,2),a=i[0],o=i[1],l=t.length,u=1,c=t[0];u<=l;u++){var d=t[u%l];if(a>f(c[0],d[0])&&a<=s(c[0],d[0])&&o<=s(c[1],d[1])&&c[0]!==d[0]){var p=(a-c[0])*(d[1]-c[1])/(d[0]-c[0])+c[1];(c[1]===d[1]||o<=p)&&n++}c=d}return n%2==1}function m(e,t,n,i,a,o,l){if(!e||g(e,[t,n])>i)return!1;if(!l){var u=d([o,a]),s=(0,r.default)(u,2);a=s[0],o=s[1]}var f=a>o;if(f){var p=[o,a];a=p[0],o=p[1]}var h=o-a;if(h>=2*c)return!0;var v=(0,r.default)(e,2),m=v[0],b=v[1],x=k(t,n,i,a),C=(0,r.default)(x,2),$=C[0],w=C[1],P=k(t,n,i,o),A=(0,r.default)(P,2),O=[m-t,b-n],L=[$-t,w-n],I=[A[0]-t,A[1]-n],S=h>c;if(S){var M=d([I,L]),j=(0,r.default)(M,2);L=j[0],I=j[1]}var F=y(L,O)&&!y(I,O);return S&&(F=!F),f&&(F=!F),F}function y(e,t){var n=(0,r.default)(e,2),i=n[0],a=n[1],o=(0,r.default)(t,2);return-a*o[0]+i*o[1]>0}function b(e,t,i){var a=i/2,o=t.map((function(e){var t=(0,r.default)(e,2);return[t[0],t[1]-a]})),l=t.map((function(e){var t=(0,r.default)(e,2);return[t[0],t[1]+a]}));return v(e,[].concat((0,n.default)(o),(0,n.default)(l.reverse())))}function x(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1?arguments[1]:void 0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[0,0];if(!t)return!1;if(e%360==0)return t;var i=(0,r.default)(t,2),a=i[0],o=i[1],s=(0,r.default)(n,2),f=s[0],d=s[1];return[(a-f)*u(e*=c/180)-(o-d)*l(e)+f,(a-f)*l(e)+(o-d)*u(e)+d]}function C(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[1,1],t=arguments.length>1?arguments[1]:void 0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[0,0];if(!t)return!1;if(1===e)return t;var i=(0,r.default)(t,2),a=i[0],o=i[1],l=(0,r.default)(n,2),u=l[0],s=l[1],f=(0,r.default)(e,2);return[(a-u)*f[0]+u,(o-s)*f[1]+s]}function $(e,t){if(!e||!t)return!1;var n=(0,r.default)(t,2),i=n[0],a=n[1],o=(0,r.default)(e,2);return[i+o[0],a+o[1]]}function w(e,t,n){if(!e||!t||!n)return!1;var i=(0,r.default)(e,2),l=i[0],u=i[1],s=(0,r.default)(t,2),f=s[0],c=s[1],d=(0,r.default)(n,2),p=d[0],h=d[1],g=h-c,v=f-p;return a(g*l+v*u+(c*(p-f)-f*(h-c)))/o(g*g+v*v)}function k(e,t,n,r){return[e+u(r)*n,t+l(r)*n]}function P(e,t,n,r){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:-.5*c,a=2*c/r;return new Array(r).fill("").map((function(e,t){return t*a+i})).map((function(r){return k(e,t,n,r)}))}var A={deepClone:d,eliminateBlur:p,checkPointIsInCircle:h,checkPointIsInPolygon:v,checkPointIsInSector:m,checkPointIsNearPolyline:b,getTwoPointDistance:g,getRotatePointPos:x,getScalePointPos:C,getTranslatePointPos:$,getCircleRadianPoint:k,getRegularPolygonPoints:P,getDistanceBetweenPointAndLine:w};e.default=A}(Yt)),Yt}var Zt,en,tn={},nn={};function rn(){return en||(en=1,function(e){var t=ot;Object.defineProperty(e,"__esModule",{value:!0}),e.extendNewGraph=function(e,t){if(!e||!t)return;if(!t.shape)return;if(!t.validator)return;if(!t.draw)return;x.set(e,t)},e.default=e.text=e.bezierCurve=e.smoothline=e.polyline=e.regPolygon=e.sector=e.arc=e.ring=e.rect=e.ellipse=e.circle=void 0;var n=t(kt()),r=t(Nt()),i=t(Vt()),a=Kt(),o=(Zt||(Zt=1,function(e){var t=ot;Object.defineProperty(e,"__esModule",{value:!0}),e.drawPolylinePath=r,e.drawBezierCurvePath=i,e.default=void 0;var n=t(kt());function r(e,t){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!e||t.length<2)return!1;r&&e.beginPath(),t.forEach((function(t,r){return t&&(0===r?e.moveTo.apply(e,(0,n.default)(t)):e.lineTo.apply(e,(0,n.default)(t)))})),i&&e.closePath()}function i(e,t){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(!e||!t)return!1;arguments.length>3&&void 0!==arguments[3]&&arguments[3]&&e.beginPath(),r&&e.moveTo.apply(e,(0,n.default)(r)),t.forEach((function(t){return t&&e.bezierCurveTo.apply(e,(0,n.default)(t[0]).concat((0,n.default)(t[1]),(0,n.default)(t[2])))})),i&&e.closePath()}var a={drawPolylinePath:r,drawBezierCurvePath:i};e.default=a}(nn)),nn),l=i.default.polylineToBezierCurve,u=i.default.bezierCurveToPolyline,s={shape:{rx:0,ry:0,r:0},validator:function(e){var t=e.shape,n=t.rx,r=t.ry,i=t.r;return"number"==typeof n&&"number"==typeof r&&"number"==typeof i},draw:function(e,t){var n=e.ctx,r=t.shape;n.beginPath();var i=r.rx,a=r.ry,o=r.r;n.arc(i,a,o>0?o:.01,0,2*Math.PI),n.fill(),n.stroke(),n.closePath()},hoverCheck:function(e,t){var n=t.shape,r=n.rx,i=n.ry,o=n.r;return(0,a.checkPointIsInCircle)(e,r,i,o)},setGraphCenter:function(e,t){var n=t.shape,r=t.style,i=n.rx,a=n.ry;r.graphCenter=[i,a]},move:function(e,t){var n=e.movementX,r=e.movementY,i=t.shape;this.attr("shape",{rx:i.rx+n,ry:i.ry+r})}};e.circle=s;var f={shape:{rx:0,ry:0,hr:0,vr:0},validator:function(e){var t=e.shape,n=t.rx,r=t.ry,i=t.hr,a=t.vr;return"number"==typeof n&&"number"==typeof r&&"number"==typeof i&&"number"==typeof a},draw:function(e,t){var n=e.ctx,r=t.shape;n.beginPath();var i=r.rx,a=r.ry,o=r.hr,l=r.vr;n.ellipse(i,a,o>0?o:.01,l>0?l:.01,0,0,2*Math.PI),n.fill(),n.stroke(),n.closePath()},hoverCheck:function(e,t){var n=t.shape,r=n.rx,i=n.ry,o=n.hr,l=n.vr,u=Math.max(o,l),s=Math.min(o,l),f=Math.sqrt(u*u-s*s),c=[r-f,i],d=[r+f,i];return(0,a.getTwoPointDistance)(e,c)+(0,a.getTwoPointDistance)(e,d)<=2*u},setGraphCenter:function(e,t){var n=t.shape,r=t.style,i=n.rx,a=n.ry;r.graphCenter=[i,a]},move:function(e,t){var n=e.movementX,r=e.movementY,i=t.shape;this.attr("shape",{rx:i.rx+n,ry:i.ry+r})}};e.ellipse=f;var c={shape:{x:0,y:0,w:0,h:0},validator:function(e){var t=e.shape,n=t.x,r=t.y,i=t.w,a=t.h;return"number"==typeof n&&"number"==typeof r&&"number"==typeof i&&"number"==typeof a},draw:function(e,t){var n=e.ctx,r=t.shape;n.beginPath();var i=r.x,a=r.y,o=r.w,l=r.h;n.rect(i,a,o,l),n.fill(),n.stroke(),n.closePath()},hoverCheck:function(e,t){var n=t.shape,r=n.x,i=n.y,o=n.w,l=n.h;return(0,a.checkPointIsInRect)(e,r,i,o,l)},setGraphCenter:function(e,t){var n=t.shape,r=t.style,i=n.x,a=n.y,o=n.w,l=n.h;r.graphCenter=[i+o/2,a+l/2]},move:function(e,t){var n=e.movementX,r=e.movementY,i=t.shape;this.attr("shape",{x:i.x+n,y:i.y+r})}};e.rect=c;var d={shape:{rx:0,ry:0,r:0},validator:function(e){var t=e.shape,n=t.rx,r=t.ry,i=t.r;return"number"==typeof n&&"number"==typeof r&&"number"==typeof i},draw:function(e,t){var n=e.ctx,r=t.shape;n.beginPath();var i=r.rx,a=r.ry,o=r.r;n.arc(i,a,o>0?o:.01,0,2*Math.PI),n.stroke(),n.closePath()},hoverCheck:function(e,t){var n=t.shape,r=t.style,i=n.rx,o=n.ry,l=n.r,u=r.lineWidth/2,s=l-u,f=l+u,c=(0,a.getTwoPointDistance)(e,[i,o]);return c>=s&&c<=f},setGraphCenter:function(e,t){var n=t.shape,r=t.style,i=n.rx,a=n.ry;r.graphCenter=[i,a]},move:function(e,t){var n=e.movementX,r=e.movementY,i=t.shape;this.attr("shape",{rx:i.rx+n,ry:i.ry+r})}};e.ring=d;var p={shape:{rx:0,ry:0,r:0,startAngle:0,endAngle:0,clockWise:!0},validator:function(e){var t=e.shape;return!["rx","ry","r","startAngle","endAngle"].find((function(e){return"number"!=typeof t[e]}))},draw:function(e,t){var n=e.ctx,r=t.shape;n.beginPath();var i=r.rx,a=r.ry,o=r.r,l=r.startAngle,u=r.endAngle,s=r.clockWise;n.arc(i,a,o>0?o:.001,l,u,!s),n.stroke(),n.closePath()},hoverCheck:function(e,t){var n=t.shape,r=t.style,i=n.rx,o=n.ry,l=n.r,u=n.startAngle,s=n.endAngle,f=n.clockWise,c=r.lineWidth/2,d=l-c,p=l+c;return!(0,a.checkPointIsInSector)(e,i,o,d,u,s,f)&&(0,a.checkPointIsInSector)(e,i,o,p,u,s,f)},setGraphCenter:function(e,t){var n=t.shape,r=t.style,i=n.rx,a=n.ry;r.graphCenter=[i,a]},move:function(e,t){var n=e.movementX,r=e.movementY,i=t.shape;this.attr("shape",{rx:i.rx+n,ry:i.ry+r})}};e.arc=p;var h={shape:{rx:0,ry:0,r:0,startAngle:0,endAngle:0,clockWise:!0},validator:function(e){var t=e.shape;return!["rx","ry","r","startAngle","endAngle"].find((function(e){return"number"!=typeof t[e]}))},draw:function(e,t){var n=e.ctx,r=t.shape;n.beginPath();var i=r.rx,a=r.ry,o=r.r,l=r.startAngle,u=r.endAngle,s=r.clockWise;n.arc(i,a,o>0?o:.01,l,u,!s),n.lineTo(i,a),n.closePath(),n.stroke(),n.fill()},hoverCheck:function(e,t){var n=t.shape,r=n.rx,i=n.ry,o=n.r,l=n.startAngle,u=n.endAngle,s=n.clockWise;return(0,a.checkPointIsInSector)(e,r,i,o,l,u,s)},setGraphCenter:function(e,t){var n=t.shape,r=t.style,i=n.rx,a=n.ry;r.graphCenter=[i,a]},move:function(e,t){var n=e.movementX,r=e.movementY,i=t.shape,a=i.rx,o=i.ry;this.attr("shape",{rx:a+n,ry:o+r})}};e.sector=h;var g={shape:{rx:0,ry:0,r:0,side:0},validator:function(e){var t=e.shape,n=t.side;return!["rx","ry","r","side"].find((function(e){return"number"!=typeof t[e]}))&&!(n<3)},draw:function(e,t){var n=e.ctx,r=t.shape,i=t.cache;n.beginPath();var l=r.rx,u=r.ry,s=r.r,f=r.side;if(!i.points||i.rx!==l||i.ry!==u||i.r!==s||i.side!==f){var c=(0,a.getRegularPolygonPoints)(l,u,s,f);Object.assign(i,{points:c,rx:l,ry:u,r:s,side:f})}var d=i.points;(0,o.drawPolylinePath)(n,d),n.closePath(),n.stroke(),n.fill()},hoverCheck:function(e,t){var n=t.cache.points;return(0,a.checkPointIsInPolygon)(e,n)},setGraphCenter:function(e,t){var n=t.shape,r=t.style,i=n.rx,a=n.ry;r.graphCenter=[i,a]},move:function(e,t){var n=e.movementX,i=e.movementY,a=t.shape,o=t.cache,l=a.rx,u=a.ry;o.rx+=n,o.ry+=i,this.attr("shape",{rx:l+n,ry:u+i}),o.points=o.points.map((function(e){var t=(0,r.default)(e,2),a=t[0],o=t[1];return[a+n,o+i]}))}};e.regPolygon=g;var v={shape:{points:[],close:!1},validator:function(e){return e.shape.points instanceof Array},draw:function(e,t){var n=e.ctx,r=t.shape,i=t.style.lineWidth;n.beginPath();var l=r.points,u=r.close;1===i&&(l=(0,a.eliminateBlur)(l)),(0,o.drawPolylinePath)(n,l),u&&(n.closePath(),n.fill()),n.stroke()},hoverCheck:function(e,t){var n=t.shape,r=t.style,i=n.points,o=n.close,l=r.lineWidth;return o?(0,a.checkPointIsInPolygon)(e,i):(0,a.checkPointIsNearPolyline)(e,i,l)},setGraphCenter:function(e,t){var n=t.shape,r=t.style,i=n.points;r.graphCenter=i[0]},move:function(e,t){var n=e.movementX,i=e.movementY,a=t.shape.points.map((function(e){var t=(0,r.default)(e,2),a=t[0],o=t[1];return[a+n,o+i]}));this.attr("shape",{points:a})}};e.polyline=v;var m={shape:{points:[],close:!1},validator:function(e){return e.shape.points instanceof Array},draw:function(e,t){var n=e.ctx,r=t.shape,i=t.cache,s=r.points,f=r.close;if(!i.points||i.points.toString()!==s.toString()){var c=l(s,f),d=u(c);Object.assign(i,{points:(0,a.deepClone)(s,!0),bezierCurve:c,hoverPoints:d})}var p=i.bezierCurve;n.beginPath(),(0,o.drawBezierCurvePath)(n,p.slice(1),p[0]),f&&(n.closePath(),n.fill()),n.stroke()},hoverCheck:function(e,t){var n=t.cache,r=t.shape,i=t.style,o=n.hoverPoints,l=r.close,u=i.lineWidth;return l?(0,a.checkPointIsInPolygon)(e,o):(0,a.checkPointIsNearPolyline)(e,o,u)},setGraphCenter:function(e,t){var n=t.shape,r=t.style,i=n.points;r.graphCenter=i[0]},move:function(e,t){var i=e.movementX,a=e.movementY,o=t.shape,l=t.cache,u=o.points.map((function(e){var t=(0,r.default)(e,2),n=t[0],o=t[1];return[n+i,o+a]}));l.points=u;var s=(0,r.default)(l.bezierCurve[0],2),f=s[0],c=s[1],d=l.bezierCurve.slice(1);l.bezierCurve=[[f+i,c+a]].concat((0,n.default)(d.map((function(e){return e.map((function(e){var t=(0,r.default)(e,2),n=t[0],o=t[1];return[n+i,o+a]}))})))),l.hoverPoints=l.hoverPoints.map((function(e){var t=(0,r.default)(e,2),n=t[0],o=t[1];return[n+i,o+a]})),this.attr("shape",{points:u})}};e.smoothline=m;var y={shape:{points:[],close:!1},validator:function(e){return e.shape.points instanceof Array},draw:function(e,t){var n=e.ctx,r=t.shape,i=t.cache,l=r.points,s=r.close;if(!i.points||i.points.toString()!==l.toString()){var f=u(l,20);Object.assign(i,{points:(0,a.deepClone)(l,!0),hoverPoints:f})}n.beginPath(),(0,o.drawBezierCurvePath)(n,l.slice(1),l[0]),s&&(n.closePath(),n.fill()),n.stroke()},hoverCheck:function(e,t){var n=t.cache,r=t.shape,i=t.style,o=n.hoverPoints,l=r.close,u=i.lineWidth;return l?(0,a.checkPointIsInPolygon)(e,o):(0,a.checkPointIsNearPolyline)(e,o,u)},setGraphCenter:function(e,t){var n=t.shape,r=t.style,i=n.points;r.graphCenter=i[0]},move:function(e,t){var i=e.movementX,a=e.movementY,o=t.shape,l=t.cache,u=o.points,s=(0,r.default)(u[0],2),f=s[0],c=s[1],d=u.slice(1),p=[[f+i,c+a]].concat((0,n.default)(d.map((function(e){return e.map((function(e){var t=(0,r.default)(e,2),n=t[0],o=t[1];return[n+i,o+a]}))}))));l.points=p,l.hoverPoints=l.hoverPoints.map((function(e){var t=(0,r.default)(e,2),n=t[0],o=t[1];return[n+i,o+a]})),this.attr("shape",{points:p})}};e.bezierCurve=y;var b={shape:{content:"",position:[],maxWidth:void 0,rowGap:0},validator:function(e){var t=e.shape,n=t.content,r=t.position,i=t.rowGap;return"string"==typeof n&&(r instanceof Array&&"number"==typeof i)},draw:function(e,t){var i=e.ctx,a=t.shape,o=a.content,l=a.position,u=a.maxWidth,s=a.rowGap,f=i.textBaseline,c=i.font,d=parseInt(c.replace(/\D/g,"")),p=l,h=(0,r.default)(p,2),g=h[0],v=h[1],m=(o=o.split("\n")).length,y=d+s,b=m*y-s,x=0;"middle"===f&&(x=b/2,v+=d/2),"bottom"===f&&(x=b,v+=d),l=new Array(m).fill(0).map((function(e,t){return[g,v+t*y-x]})),i.beginPath(),o.forEach((function(e,t){i.fillText.apply(i,[e].concat((0,n.default)(l[t]),[u])),i.strokeText.apply(i,[e].concat((0,n.default)(l[t]),[u]))})),i.closePath()},hoverCheck:function(e,t){return t.shape,t.style,!1},setGraphCenter:function(e,t){var r=t.shape,i=t.style,a=r.position;i.graphCenter=(0,n.default)(a)},move:function(e,t){var n=e.movementX,i=e.movementY,a=t.shape,o=(0,r.default)(a.position,2),l=o[0],u=o[1];this.attr("shape",{position:[l+n,u+i]})}};e.text=b;var x=new Map([["circle",s],["ellipse",f],["rect",c],["ring",d],["arc",p],["sector",h],["regPolygon",g],["polyline",v],["smoothline",m],["bezierCurve",y],["text",b]]),C=x;e.default=C}(tn)),tn}var an,on,ln,un={},sn={exports:{}};function fn(){return an||(an=1,function(e){var t=function(e){var t,n=Object.prototype,r=n.hasOwnProperty,i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",o=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function u(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch{u=function(e,t,n){return e[t]=n}}function s(e,t,n,r){var i=t&&t.prototype instanceof v?t:v,a=Object.create(i.prototype),o=new L(r||[]);return a._invoke=function(e,t,n){var r=c;return function(i,a){if(r===p)throw new Error("Generator is already running");if(r===h){if("throw"===i)throw a;return S()}for(n.method=i,n.arg=a;;){var o=n.delegate;if(o){var l=P(o,n);if(l){if(l===g)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(r===c)throw r=h,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r=p;var u=f(e,t,n);if("normal"===u.type){if(r=n.done?h:d,u.arg===g)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(r=h,n.method="throw",n.arg=u.arg)}}}(e,n,o),a}function f(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(r){return{type:"throw",arg:r}}}e.wrap=s;var c="suspendedStart",d="suspendedYield",p="executing",h="completed",g={};function v(){}function m(){}function y(){}var b={};u(b,a,(function(){return this}));var x=Object.getPrototypeOf,C=x&&x(x(I([])));C&&C!==n&&r.call(C,a)&&(b=C);var $=y.prototype=v.prototype=Object.create(b);function w(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function k(e,t){function n(i,a,o,l){var u=f(e[i],e,a);if("throw"!==u.type){var s=u.arg,c=s.value;return c&&"object"==typeof c&&r.call(c,"__await")?t.resolve(c.__await).then((function(e){n("next",e,o,l)}),(function(e){n("throw",e,o,l)})):t.resolve(c).then((function(e){s.value=e,o(s)}),(function(e){return n("throw",e,o,l)}))}l(u.arg)}var i;this._invoke=function(e,r){function a(){return new t((function(t,i){n(e,r,t,i)}))}return i=i?i.then(a,a):a()}}function P(e,n){var r=e.iterator[n.method];if(r===t){if(n.delegate=null,"throw"===n.method){if(e.iterator.return&&(n.method="return",n.arg=t,P(e,n),"throw"===n.method))return g;n.method="throw",n.arg=new TypeError("The iterator does not provide a 'throw' method")}return g}var i=f(r,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,g;var a=i.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,g):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,g)}function A(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function O(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function L(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(A,this),this.reset(!0)}function I(e){if(e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,o=function n(){for(;++i<e.length;)if(r.call(e,i))return n.value=e[i],n.done=!1,n;return n.value=t,n.done=!0,n};return o.next=o}}return{next:S}}function S(){return{value:t,done:!0}}return m.prototype=y,u($,"constructor",y),u(y,"constructor",m),m.displayName=u(y,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===m||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,y):(e.__proto__=y,u(e,l,"GeneratorFunction")),e.prototype=Object.create($),e},e.awrap=function(e){return{__await:e}},w(k.prototype),u(k.prototype,o,(function(){return this})),e.AsyncIterator=k,e.async=function(t,n,r,i,a){void 0===a&&(a=Promise);var o=new k(s(t,n,r,i),a);return e.isGeneratorFunction(n)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},w($),u($,l,"Generator"),u($,a,(function(){return this})),u($,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var n in e)t.push(n);return t.reverse(),function n(){for(;t.length;){var r=t.pop();if(r in e)return n.value=r,n.done=!1,n}return n.done=!0,n}},e.values=I,L.prototype={constructor:L,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function i(r,i){return l.type="throw",l.arg=e,n.next=r,i&&(n.method="next",n.arg=t),!!i}for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a],l=o.completion;if("root"===o.tryLoc)return i("end");if(o.tryLoc<=this.prev){var u=r.call(o,"catchLoc"),s=r.call(o,"finallyLoc");if(u&&s){if(this.prev<o.catchLoc)return i(o.catchLoc,!0);if(this.prev<o.finallyLoc)return i(o.finallyLoc)}else if(u){if(this.prev<o.catchLoc)return i(o.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return i(o.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=e,o.arg=t,a?(this.method="next",this.next=a.finallyLoc,g):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;O(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:I(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),g}},e}(sn.exports);try{regeneratorRuntime=t}catch{"object"==typeof globalThis?globalThis.regeneratorRuntime=t:Function("r","regeneratorRuntime = r")(t)}}()),sn.exports}var cn,dn={exports:{}};var pn,hn={};function gn(){return pn||(pn=1,function(e){var t=ot;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=t(kt()),r=t(Ot()),i=Lt,a=Kt(),o=function e(t){(0,r.default)(this,e),this.colorProcessor(t);Object.assign(this,{fill:[0,0,0,1],stroke:[0,0,0,0],opacity:1,lineCap:null,lineJoin:null,lineDash:null,lineDashOffset:null,shadowBlur:0,shadowColor:[0,0,0,0],shadowOffsetX:0,shadowOffsetY:0,lineWidth:0,graphCenter:null,scale:null,rotate:null,translate:null,hoverCursor:"pointer",fontStyle:"normal",fontVarient:"normal",fontWeight:"normal",fontSize:10,fontFamily:"Arial",textAlign:"center",textBaseline:"middle",gradientColor:null,gradientType:"linear",gradientParams:null,gradientWith:"stroke",gradientStops:"auto",colors:null},t)};e.default=o,o.prototype.colorProcessor=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1]?i.getColorFromRgbValue:i.getRgbaValue,n=["fill","stroke","shadowColor"];Object.keys(e).filter((function(e){return n.find((function(t){return t===e}))})).forEach((function(n){return e[n]=t(e[n])}));var r=e.gradientColor,a=e.colors;(r&&(e.gradientColor=r.map((function(e){return t(e)}))),a)&&Object.keys(a).forEach((function(e){return a[e]=t(a[e])}))},o.prototype.initStyle=function(e){(function(e,t){e.save();var r=t.graphCenter,i=t.rotate,a=t.scale,o=t.translate;r instanceof Array&&(e.translate.apply(e,(0,n.default)(r)),i&&e.rotate(i*Math.PI/180),a instanceof Array&&e.scale.apply(e,(0,n.default)(a)),o&&e.translate.apply(e,(0,n.default)(o)),e.translate(-r[0],-r[1]))})(e,this),function(e,t){var r=t.fill,a=t.stroke,o=t.shadowColor,u=t.opacity;l.forEach((function(n){(n||"number"==typeof n)&&(e[n]=t[n])})),r=(0,n.default)(r),a=(0,n.default)(a),o=(0,n.default)(o),r[3]*=u,a[3]*=u,o[3]*=u,e.fillStyle=(0,i.getColorFromRgbValue)(r),e.strokeStyle=(0,i.getColorFromRgbValue)(a),e.shadowColor=(0,i.getColorFromRgbValue)(o);var s=t.lineDash,f=t.shadowBlur;s&&(s=s.map((function(e){return e>=0?e:0})),e.setLineDash(s)),"number"==typeof f&&(e.shadowBlur=f>0?f:.001);var c=t.fontStyle,d=t.fontVarient,p=t.fontWeight,h=t.fontSize,g=t.fontFamily;e.font=c+" "+d+" "+p+" "+h+"px "+g}(e,this),function(e,t){if(function(e){var t=e.gradientColor,n=e.gradientParams,r=e.gradientType,i=e.gradientWith,a=e.gradientStops;if(!t||!n)return!1;if(1===t.length)return!1;if("linear"!==r&&"radial"!==r)return!1;var o=n.length;return!("linear"===r&&4!==o||"radial"===r&&6!==o)&&(("fill"===i||"stroke"===i)&&("auto"===a||a instanceof Array))}(t)){var r=t.gradientColor,a=t.gradientParams,o=t.gradientType,l=t.gradientWith,u=t.gradientStops,s=t.opacity;r=(r=r.map((function(e){var t=e[3]*s,r=(0,n.default)(e);return r[3]=t,r}))).map((function(e){return(0,i.getColorFromRgbValue)(e)})),"auto"===u&&(u=function(e){var t=1/(e.length-1);return e.map((function(e,n){return t*n}))}(r));var f=e["create".concat(o.slice(0,1).toUpperCase()+o.slice(1),"Gradient")].apply(e,(0,n.default)(a));u.forEach((function(e,t){return f.addColorStop(e,r[t])})),e["".concat(l,"Style")]=f}}(e,this)};var l=["lineCap","lineJoin","lineDashOffset","shadowOffsetX","shadowOffsetY","lineWidth","textAlign","textBaseline"];o.prototype.restoreTransform=function(e){e.restore()},o.prototype.update=function(e){this.colorProcessor(e),Object.assign(this,e)},o.prototype.getStyle=function(){var e=(0,a.deepClone)(this,!0);return this.colorProcessor(e,!0),e}}(hn)),hn}var vn,mn,yn,bn,xn={},Cn={};function $n(){return mn||(mn=1,function(e){var t=ot;Object.defineProperty(e,"__esModule",{value:!0}),e.transition=o,e.injectNewCurve=function(e,t){if(!e||!t)return;i.default.set(e,t)},e.default=void 0;var n=t(Nt()),r=t(Jt()),i=t((vn||(vn=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.easeInOutBounce=e.easeOutBounce=e.easeInBounce=e.easeInOutElastic=e.easeOutElastic=e.easeInElastic=e.easeInOutBack=e.easeOutBack=e.easeInBack=e.easeInOutQuint=e.easeOutQuint=e.easeInQuint=e.easeInOutQuart=e.easeOutQuart=e.easeInQuart=e.easeInOutCubic=e.easeOutCubic=e.easeInCubic=e.easeInOutQuad=e.easeOutQuad=e.easeInQuad=e.easeInOutSine=e.easeOutSine=e.easeInSine=e.linear=void 0;var t=[[[0,1],"",[.33,.67]],[[1,0],[.67,.33]]];e.linear=t;var n=[[[0,1]],[[.538,.564],[.169,.912],[.88,.196]],[[1,0]]];e.easeInSine=n;var r=[[[0,1]],[[.444,.448],[.169,.736],[.718,.16]],[[1,0]]];e.easeOutSine=r;var i=[[[0,1]],[[.5,.5],[.2,1],[.8,0]],[[1,0]]];e.easeInOutSine=i;var a=[[[0,1]],[[.55,.584],[.231,.904],[.868,.264]],[[1,0]]];e.easeInQuad=a;var o=[[[0,1]],[[.413,.428],[.065,.816],[.76,.04]],[[1,0]]];e.easeOutQuad=o;var l=[[[0,1]],[[.5,.5],[.3,.9],[.7,.1]],[[1,0]]];e.easeInOutQuad=l;var u=[[[0,1]],[[.679,.688],[.366,.992],[.992,.384]],[[1,0]]];e.easeInCubic=u;var s=[[[0,1]],[[.321,.312],[.008,.616],[.634,.008]],[[1,0]]];e.easeOutCubic=s;var f=[[[0,1]],[[.5,.5],[.3,1],[.7,0]],[[1,0]]];e.easeInOutCubic=f;var c=[[[0,1]],[[.812,.74],[.611,.988],[1.013,.492]],[[1,0]]];e.easeInQuart=c;var d=[[[0,1]],[[.152,.244],[.001,.448],[.285,-.02]],[[1,0]]];e.easeOutQuart=d;var p=[[[0,1]],[[.5,.5],[.4,1],[.6,0]],[[1,0]]];e.easeInOutQuart=p;var h=[[[0,1]],[[.857,.856],[.714,1],[1,.712]],[[1,0]]];e.easeInQuint=h;var g=[[[0,1]],[[.108,.2],[.001,.4],[.214,-.012]],[[1,0]]];e.easeOutQuint=g;var v=[[[0,1]],[[.5,.5],[.5,1],[.5,0]],[[1,0]]];e.easeInOutQuint=v;var m=[[[0,1]],[[.667,.896],[.38,1.184],[.955,.616]],[[1,0]]];e.easeInBack=m;var y=[[[0,1]],[[.335,.028],[.061,.22],[.631,-.18]],[[1,0]]];e.easeOutBack=y;var b=[[[0,1]],[[.5,.5],[.4,1.4],[.6,-.4]],[[1,0]]];e.easeInOutBack=b;var x=[[[0,1]],[[.474,.964],[.382,.988],[.557,.952]],[[.619,1.076],[.565,1.088],[.669,1.08]],[[.77,.916],[.712,.924],[.847,.904]],[[.911,1.304],[.872,1.316],[.961,1.34]],[[1,0]]];e.easeInElastic=x;var C=[[[0,1]],[[.073,-.32],[.034,-.328],[.104,-.344]],[[.191,.092],[.11,.06],[.256,.08]],[[.31,-.076],[.26,-.068],[.357,-.076]],[[.432,.032],[.362,.028],[.683,-.004]],[[1,0]]];e.easeOutElastic=C;var $=[[[0,1]],[[.21,.94],[.167,.884],[.252,.98]],[[.299,1.104],[.256,1.092],[.347,1.108]],[[.5,.496],[.451,.672],[.548,.324]],[[.696,-.108],[.652,-.112],[.741,-.124]],[[.805,.064],[.756,.012],[.866,.096]],[[1,0]]];e.easeInOutElastic=$;var w=[[[0,1]],[[.148,1],[.075,.868],[.193,.848]],[[.326,1],[.276,.836],[.405,.712]],[[.6,1],[.511,.708],[.671,.348]],[[1,0]]];e.easeInBounce=w;var k=[[[0,1]],[[.357,.004],[.27,.592],[.376,.252]],[[.604,-.004],[.548,.312],[.669,.184]],[[.82,0],[.749,.184],[.905,.132]],[[1,0]]];e.easeOutBounce=k;var P=[[[0,1]],[[.102,1],[.05,.864],[.117,.86]],[[.216,.996],[.208,.844],[.227,.808]],[[.347,.996],[.343,.8],[.48,.292]],[[.635,.004],[.511,.676],[.656,.208]],[[.787,0],[.76,.2],[.795,.144]],[[.905,-.004],[.899,.164],[.944,.144]],[[1,0]]];e.easeInOutBounce=P;var A=new Map([["linear",t],["easeInSine",n],["easeOutSine",r],["easeInOutSine",i],["easeInQuad",a],["easeOutQuad",o],["easeInOutQuad",l],["easeInCubic",u],["easeOutCubic",s],["easeInOutCubic",f],["easeInQuart",c],["easeOutQuart",d],["easeInOutQuart",p],["easeInQuint",h],["easeOutQuint",g],["easeInOutQuint",v],["easeInBack",m],["easeOutBack",y],["easeInOutBack",b],["easeInElastic",x],["easeOutElastic",C],["easeInOutElastic",$],["easeInBounce",w],["easeOutBounce",k],["easeInOutBounce",P]]);e.default=A}(Cn)),Cn)),a="linear";function o(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:30,s=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(!l.apply(void 0,arguments))return!1;try{var c=function(e){var t="";return t=i.default.has(e)?i.default.get(e):e instanceof Array?e:i.default.get(a),t}(e),d=function(e,t){var r=1/(t-1),i=new Array(t).fill(0).map((function(e,t){return t*r})),a=i.map((function(t){return function(e,t){var r=function(e,t){var n=e.length-1,r="",i="";e.findIndex((function(a,o){if(o!==n){r=a,i=e[o+1];var l=r[0][0],u=i[0][0];return t>=l&&t<u}}));var a=r[0],o=r[2]||r[0],l=i[1]||i[0],u=i[0];return[a,o,l,u]}(e,t),i=function(e,t){var n=e[0][0],r=e[3][0],i=r-n,a=t-n;return a/i}(r,t);return function(e,t){var r=(0,n.default)(e,4),i=(0,n.default)(r[0],2),a=i[1],o=(0,n.default)(r[1],2),l=o[1],u=(0,n.default)(r[2],2),s=u[1],f=(0,n.default)(r[3],2),c=f[1],d=Math.pow,p=1-t,h=a*d(p,3),g=3*l*t*d(p,2),v=3*s*d(t,2)*p,m=c*d(t,3);return 1-(h+g+v+m)}(r,i)}(e,t)}));return a}(c,o);return s&&"number"!=typeof r?f(t,r,d):u(t,r,d)}catch{return[r]}}function l(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(!e||!1===t||!1===n||!(arguments.length>3&&void 0!==arguments[3]?arguments[3]:30))return!1;if((0,r.default)(t)!==(0,r.default)(n))return!1;var a=(0,r.default)(n);return!("string"===a||"boolean"===a||!e.length)&&(i.default.has(e),!0)}function u(e,t,n){var r="object";return"number"==typeof e&&(r="number"),e instanceof Array&&(r="array"),"number"===r?function(e,t,n){var r=t-e;return n.map((function(t){return e+r*t}))}(e,t,n):"array"===r?s(e,t,n):"object"===r?function(e,t,n){var r=Object.keys(t),i=r.map((function(t){return e[t]})),a=r.map((function(e){return t[e]})),o=s(i,a,n);return o.map((function(e){var t={};return e.forEach((function(e,n){return t[r[n]]=e})),t}))}(e,t,n):n.map((function(e){return t}))}function s(e,t,n){var r=t.map((function(t,n){return"number"==typeof t&&t-e[n]}));return n.map((function(n){return r.map((function(r,i){return!1===r?t[i]:e[i]+r*n}))}))}function f(e,t,n){var i=u(e,t,n),a=function(a){var o=e[a],l=t[a];if("object"!==(0,r.default)(l))return"continue";var u=f(o,l,n);i.forEach((function(e,t){return e[a]=u[t]}))};for(var o in t)a(o);return i}var c=o;e.default=c}(xn)),xn}function wn(){return yn||(yn=1,function(e){var t=ot;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=t((ln||(ln=1,on=fn()),on)),r=t((cn||(cn=1,function(e){function t(e,t,n,r,i,a,o){try{var l=e[a](o),u=l.value}catch(s){return void n(s)}l.done?t(u):Promise.resolve(u).then(r,i)}e.exports=function(e){return function(){var n=this,r=arguments;return new Promise((function(i,a){var o=e.apply(n,r);function l(e){t(o,i,a,l,u,"next",e)}function u(e){t(o,i,a,l,u,"throw",e)}l(void 0)}))}},e.exports.__esModule=!0,e.exports.default=e.exports}(dn)),dn.exports)),i=t(Jt()),a=t(kt()),o=t(Ot()),l=t(gn()),u=t($n()),s=Kt(),f=function e(t,n){(0,o.default)(this,e);var r={visible:!0,drag:!1,hover:!1,index:1,animationDelay:0,animationFrame:30,animationCurve:"linear",animationPause:!1,hoverRect:null,mouseEnter:null,mouseOuter:null,click:null};(n=(0,s.deepClone)(n,!0)).shape||(n.shape={}),n.style||(n.style={});var i=Object.assign({},t.shape,n.shape);Object.assign(r,n,{status:"static",animationRoot:[],animationKeys:[],animationFrameState:[],cache:{}}),Object.assign(this,t,r),this.shape=i,this.style=new l.default(n.style),this.addedProcessor()};function c(e){return new Promise((function(t){setTimeout(t,e)}))}e.default=f,f.prototype.addedProcessor=function(){"function"==typeof this.setGraphCenter&&this.setGraphCenter(null,this),"function"==typeof this.added&&this.added(this)},f.prototype.drawProcessor=function(e,t){var n=e.ctx;t.style.initStyle(n),"function"==typeof this.beforeDraw&&this.beforeDraw(this,e),t.draw(e,t),"function"==typeof this.drawed&&this.drawed(this,e),t.style.restoreTransform(n)},f.prototype.hoverCheckProcessor=function(e,t){var n=t.hoverRect,r=t.style,i=t.hoverCheck,o=r.graphCenter,l=r.rotate,u=r.scale,f=r.translate;return o&&(l&&(e=(0,s.getRotatePointPos)(-l,e,o)),u&&(e=(0,s.getScalePointPos)(u.map((function(e){return 1/e})),e,o)),f&&(e=(0,s.getTranslatePointPos)(f.map((function(e){return-1*e})),e))),n?s.checkPointIsInRect.apply(void 0,[e].concat((0,a.default)(n))):i(e,this)},f.prototype.moveProcessor=function(e){this.move(e,this),"function"==typeof this.beforeMove&&this.beforeMove(e,this),"function"==typeof this.setGraphCenter&&this.setGraphCenter(e,this),"function"==typeof this.moved&&this.moved(e,this)},f.prototype.attr=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;if(!e||void 0===t)return!1;var n="object"===(0,i.default)(this[e]);n&&(t=(0,s.deepClone)(t,!0));var r=this.render;"style"===e?this.style.update(t):n?Object.assign(this[e],t):this[e]=t,"index"===e&&r.sortGraphsByIndex(),r.drawAllGraph()},f.prototype.animation=function(){var e=(0,r.default)(n.default.mark((function e(t,i){var a,o,l,f,d,p,h,g,v,m=arguments;return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(a=m.length>2&&void 0!==m[2]&&m[2],"shape"===t||"style"===t){e.next=4;break}return e.abrupt("return");case 4:if(i=(0,s.deepClone)(i,!0),"style"===t&&this.style.colorProcessor(i),o=this[t],l=Object.keys(i),f={},l.forEach((function(e){return f[e]=o[e]})),d=this.animationFrame,p=this.animationCurve,h=this.animationDelay,g=(0,u.default)(p,f,i,d,!0),this.animationRoot.push(o),this.animationKeys.push(l),this.animationFrameState.push(g),!a){e.next=17;break}return e.abrupt("return");case 17:if(!(h>0)){e.next=20;break}return e.next=20,c(h);case 20:return v=this.render,e.abrupt("return",new Promise(function(){var e=(0,r.default)(n.default.mark((function e(t){return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,v.launchAnimation();case 2:t();case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()));case 22:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}(),f.prototype.turnNextAnimationFrame=function(e){var t=this.animationDelay,n=this.animationRoot,r=this.animationKeys,i=this.animationFrameState;this.animationPause||Date.now()-e<t||(n.forEach((function(e,t){r[t].forEach((function(n){e[n]=i[t][0][n]}))})),i.forEach((function(e,t){e.shift();var i=0===e.length;i&&(n[t]=null),i&&(r[t]=null)})),this.animationFrameState=i.filter((function(e){return e.length})),this.animationRoot=n.filter((function(e){return e})),this.animationKeys=r.filter((function(e){return e})))},f.prototype.animationEnd=function(){var e=this.animationFrameState,t=this.animationKeys,n=this.animationRoot,r=this.render;return n.forEach((function(n,r){var i=t[r],a=e[r].pop();i.forEach((function(e){return n[e]=a[e]}))})),this.animationFrameState=[],this.animationKeys=[],this.animationRoot=[],r.drawAllGraph()},f.prototype.pauseAnimation=function(){this.attr("animationPause",!0)},f.prototype.playAnimation=function(){var e=this.render;return this.attr("animationPause",!1),new Promise(function(){var t=(0,r.default)(n.default.mark((function t(r){return n.default.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.launchAnimation();case 2:r();case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},f.prototype.delProcessor=function(e){var t=this,n=e.graphs,r=n.findIndex((function(e){return e===t}));-1!==r&&("function"==typeof this.beforeDelete&&this.beforeDelete(this),n.splice(r,1,null),"function"==typeof this.deleted&&this.deleted(this))}}(un)),un}function kn(){return bn||(bn=1,function(e){var t=ot;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=t(st()),r=t(kt()),i=t(Ot()),a=t(Lt),o=t(Vt()),l=Kt(),u=t(rn()),s=t(wn());function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var c=function e(t){if((0,i.default)(this,e),t){var n=t.getContext("2d"),r=t.clientWidth,l=t.clientHeight,u=[r,l];t.setAttribute("width",r),t.setAttribute("height",l),this.ctx=n,this.area=u,this.animationStatus=!1,this.graphs=[],this.color=a.default,this.bezierCurve=o.default,t.addEventListener("mousedown",p.bind(this)),t.addEventListener("mousemove",h.bind(this)),t.addEventListener("mouseup",g.bind(this))}};function d(e,t){var n=this.graphs;!function(e){return e.find((function(e){return!e.animationPause&&e.animationFrameState.length}))}(n)?e():(n.forEach((function(e){return e.turnNextAnimationFrame(t)})),this.drawAllGraph(),requestAnimationFrame(d.bind(this,e,t)))}function p(e){var t=this.graphs.find((function(e){return"hover"===e.status}));t&&(t.status="active")}function h(e){var t=[e.offsetX,e.offsetY],n=this.graphs,r=n.find((function(e){return"active"===e.status||"drag"===e.status}));if(r){if(!r.drag)return;if("function"!=typeof r.move)return;return r.moveProcessor(e),void(r.status="drag")}var i=n.find((function(e){return"hover"===e.status})),a=n.filter((function(e){return e.hover&&("function"==typeof e.hoverCheck||e.hoverRect)})).find((function(e){return e.hoverCheckProcessor(t,e)}));document.body.style.cursor=a?a.style.hoverCursor:"default";var o=!1,l=!1;if(i&&(o="function"==typeof i.mouseOuter),a&&(l="function"==typeof a.mouseEnter),a||i){if(!a&&i)return o&&i.mouseOuter(e,i),void(i.status="static");if(!a||a!==i){if(a&&!i)return l&&a.mouseEnter(e,a),void(a.status="hover");a&&i&&a!==i&&(o&&i.mouseOuter(e,i),i.status="static",l&&a.mouseEnter(e,a),a.status="hover")}}}function g(e){var t=this.graphs,n=t.find((function(e){return"active"===e.status})),r=t.find((function(e){return"drag"===e.status}));n&&"function"==typeof n.click&&n.click(e,n),t.forEach((function(e){return e&&(e.status="static")})),n&&(n.status="hover"),r&&(r.status="hover")}e.default=c,c.prototype.clearArea=function(){var e,t=this.area;(e=this.ctx).clearRect.apply(e,[0,0].concat((0,r.default)(t)))},c.prototype.add=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.name;if(t){var n=u.default.get(t);if(n){var r=new s.default(n,e);return r.validator(r)?(r.render=this,this.graphs.push(r),this.sortGraphsByIndex(),this.drawAllGraph(),r):void 0}}},c.prototype.sortGraphsByIndex=function(){this.graphs.sort((function(e,t){return e.index>t.index?1:e.index===t.index?0:e.index<t.index?-1:void 0}))},c.prototype.delGraph=function(e){"function"==typeof e.delProcessor&&(e.delProcessor(this),this.graphs=this.graphs.filter((function(e){return e})),this.drawAllGraph())},c.prototype.delAllGraph=function(){var e=this;this.graphs.forEach((function(t){return t.delProcessor(e)})),this.graphs=this.graphs.filter((function(e){return e})),this.drawAllGraph()},c.prototype.drawAllGraph=function(){var e=this;this.clearArea(),this.graphs.filter((function(e){return e&&e.visible})).forEach((function(t){return t.drawProcessor(e,t)}))},c.prototype.launchAnimation=function(){var e=this;if(!this.animationStatus)return this.animationStatus=!0,new Promise((function(t){d.call(e,(function(){e.animationStatus=!1,t()}),Date.now())}))},c.prototype.clone=function(e){var t=e.style.getStyle(),r=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f(r,!0).forEach((function(t){(0,n.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(r).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({},e,{style:t});return delete r.render,r=(0,l.deepClone)(r,!0),this.add(r)}}(lt)),lt}!function(e){var t=ot;Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"CRender",{enumerable:!0,get:function(){return n.default}}),Object.defineProperty(e,"extendNewGraph",{enumerable:!0,get:function(){return r.extendNewGraph}}),e.default=void 0;var n=t(kn()),r=rn(),i=n.default;e.default=i}(rt);const Pn=et(rt),An={class:"dv-water-pond-level"},On={key:0},Ln=["id"],In=["offset","stop-color"],Sn=["stroke","fill","x","y"],Mn=["cx","cy","rx","ry","stroke"],jn=["rx","ry","width","height","stroke"],Fn={__name:"index",props:{config:Object,default:()=>({})},setup(e){const t=e,l=_e(),u=f(null),s=c({gradientId:`water-level-pond-${l}`,defaultConfig:{data:[],shape:"rect",waveNum:3,waveHeight:40,waveOpacity:.4,colors:["#3DE7C9","#00BAFF"],formatter:"{value}%"},mergedConfig:{},renderer:null,svgBorderGradient:[],details:"",waves:[],animation:!1}),y=r((()=>{const{shape:e}=s.mergedConfig;return"round"===e?"50%":"rect"===e?"0":"roundRect"===e?"10px":"0"})),C=r((()=>{const{shape:e}=s.mergedConfig;return e||"rect"}));function $(){s.mergedConfig=Ge(Ee(s.defaultConfig,!0),t.config),function(){const{colors:e}=s.mergedConfig,t=100/(e.length-1);s.svgBorderGradient=e.map(((e,n)=>[t*n,e]))}(),function(){const{data:e,formatter:t}=s.mergedConfig;if(!e.length)return void(s.details="");const n=Math.max(...e);s.details=t.replace("{value}",n)}(),function(){const e=function(){const{waveNum:e,waveHeight:t,data:n}=s.mergedConfig,[r,i]=s.renderer.area,a=4*e+4,o=r/e/2;return n.map((e=>{let n=new Array(a).fill(0).map(((n,a)=>{const l=(1-e/100)*i;return[r-o*a,a%2==0?l:l-t]}));return n=n.map((e=>function([e,t],[n,r]){return[e+n,t+r]}(e,[2*o,0]))),{points:n}}))}(),t=function(){const e=s.renderer.area[1];return{gradientColor:s.mergedConfig.colors,gradientType:"linear",gradientParams:[0,0,0,e],gradientWith:"fill",opacity:s.mergedConfig.waveOpacity,translate:[0,0]}}();s.waves=e.map((e=>s.renderer.add({name:"smoothline",animationFrame:300,shape:e,style:t,drawed:w})))}(),k()}function w({shape:{points:e}},{ctx:t,area:n}){const r=e[0],i=e.slice(-1)[0],a=n[1];t.lineTo(i[0],a),t.lineTo(r[0],a),t.closePath(),t.fill()}async function k(e=1){if(s.animation)return;s.animation=!0;const t=s.renderer.area[0];s.waves.forEach((e=>{e.attr("style",{translate:[0,0]}),e.animation("style",{translate:[t,0]},!0)})),await s.renderer.launchAnimation(),s.animation=!1,s.renderer.graphs.length&&k(e+1)}return d((()=>t.config),(()=>{s.renderer.delAllGraph(),s.waves=[],setTimeout($,0)}),{deep:!0}),m((()=>{s.renderer=new Pn(u.value),t.config&&$()})),b((()=>{s.renderer.delAllGraph(),s.waves=[]})),(e,t)=>(i(),a("div",An,[n(s).renderer?(i(),a("svg",On,[o("defs",null,[o("linearGradient",{id:n(s).gradientId,x1:"0%",y1:"0%",x2:"0%",y2:"100%"},[(i(!0),a(p,null,h(n(s).svgBorderGradient,(e=>(i(),a("stop",{key:e[0],offset:e[0],"stop-color":e[1]},null,8,In)))),128))],8,Ln)]),n(s).renderer?(i(),a("text",{key:0,stroke:`url(#${n(s).gradientId})`,fill:`url(#${n(s).gradientId})`,x:n(s).renderer.area[0]/2+8,y:n(s).renderer.area[1]/2+8},g(n(s).details),9,Sn)):v("",!0),n(C)&&"round"!==n(C)?(i(),a("rect",{key:2,x:"2",y:"2",rx:"roundRect"===n(C)?10:0,ry:"roundRect"===n(C)?10:0,width:n(s).renderer.area[0]+12,height:n(s).renderer.area[1]+12,stroke:`url(#${n(s).gradientId})`},null,8,jn)):(i(),a("ellipse",{key:1,cx:n(s).renderer.area[0]/2+8,cy:n(s).renderer.area[1]/2+8,rx:n(s).renderer.area[0]/2+5,ry:n(s).renderer.area[1]/2+5,stroke:`url(#${n(s).gradientId})`},null,8,Mn))])):v("",!0),o("canvas",{ref_key:"waterPondLevel",ref:u,style:x(`border-radius: ${n(y)};`)},null,4)]))}},_n={install(e){e.component("DvWaterLevelPond",Fn)}},Gn={},En={class:"dv-loading"},Bn=O('<svg width="50px" height="50px"><circle cx="25" cy="25" r="20" fill="transparent" stroke-width="3" stroke-dasharray="31.415, 31.415" stroke="#02bcfe" stroke-linecap="round"><animateTransform attributeName="transform" type="rotate" values="0, 25 25;360, 25 25" dur="1.5s" repeatCount="indefinite"></animateTransform><animate attributeName="stroke" values="#02bcfe;#3be6cb;#02bcfe" dur="3s" repeatCount="indefinite"></animate></circle><circle cx="25" cy="25" r="10" fill="transparent" stroke-width="3" stroke-dasharray="15.7, 15.7" stroke="#3be6cb" stroke-linecap="round"><animateTransform attributeName="transform" type="rotate" values="360, 25 25;0, 25 25" dur="1.5s" repeatCount="indefinite"></animateTransform><animate attributeName="stroke" values="#3be6cb;#02bcfe;#3be6cb" dur="3s" repeatCount="indefinite"></animate></circle></svg>',1),Dn={class:"loading-tip"};const Wn=H(Gn,[["render",function(e,t){return i(),a("div",En,[Bn,o("div",Dn,[s(e.$slots,"default")])])}]]),Nn={install(e){e.component("DvLoading",Wn)}},Tn=["width","height"],Rn=["id"],zn=[o("stop",{offset:"0%","stop-color":"#fff","stop-opacity":"1"},null,-1),o("stop",{offset:"100%","stop-color":"#fff","stop-opacity":"0"},null,-1)],Hn=["id"],Un=[o("stop",{offset:"0%","stop-color":"#fff","stop-opacity":"0"},null,-1),o("stop",{offset:"100%","stop-color":"#fff","stop-opacity":"1"},null,-1)],Vn=["id","cx","cy"],Qn=["values","dur"],qn=["dur"],Yn=["id"],Xn=["xlink:href","fill"],Jn=["xlink:href","fill","mask"],Kn=["xlink:href","width","height","x","y"],Zn=["fill","x","y"],er=["id","d"],tr=["xlink:href","stroke-width","stroke"],nr=["id"],rr=["r","fill"],ir=["dur","path"],ar=["xlink:href","stroke-width","stroke","mask"],or=["from","to","dur"],lr={__name:"index",props:{config:{type:Object,default:()=>({})},dev:{type:Boolean,default:!1}},setup(e){const t=e,r=_e(),l=f(null),{width:u,height:s}=Be(l,(function(){w()}),(function(){w()})),b=c({unique:Math.random(),flylineGradientId:`flyline-gradient-id-${r}`,haloGradientId:`halo-gradient-id-${r}`,defaultConfig:{points:[],lines:[],halo:{show:!1,duration:[20,30],color:"#fb7293",radius:120},text:{show:!1,offset:[0,15],color:"#ffdb5c",fontSize:12},icon:{show:!1,src:"",width:15,height:15},line:{width:1,color:"#ffde93",orbitColor:"rgba(103, 224, 227, .2)",duration:[20,30],radius:100},bgImgSrc:"",k:-.5,curvature:5,relative:!0},flylines:[],flylineLengths:[],flylinePoints:[],mergedConfig:null});let $;async function w(){(function(){const e=Ge(Ee(b.defaultConfig,!0),t.config||{}),{points:n,lines:r,halo:i,text:a,icon:o,line:l}=e;e.points=n.map((e=>(e.halo=Ge(Ee(i,!0),e.halo||{}),e.text=Ge(Ee(a,!0),e.text||{}),e.icon=Ge(Ee(o,!0),e.icon||{}),e))),e.lines=r.map((e=>Ge(Ee(l,!0),e))),b.mergedConfig=e})(),function(){const{relative:e,points:t}=b.mergedConfig;b.flylinePoints=t.map(((t,n)=>{const{coordinate:[r,i],halo:a,icon:o,text:l}=t;e&&(t.coordinate=[r*u.value,i*s.value]),t.halo.time=Le(...a.duration)/10;const{width:f,height:c}=o;t.icon.x=t.coordinate[0]-f/2,t.icon.y=t.coordinate[1]-c/2;const[d,p]=l.offset;return t.text.x=t.coordinate[0]+d,t.text.y=t.coordinate[1]+p,t.key=`${t.coordinate.toString()}${n}`,t}))}(),function(){const{points:e,lines:t}=b.mergedConfig;b.flylines=t.map((t=>{const{source:n,target:r,duration:i}=t,a=function(e,t){const n=function([e,t],[n,r]){const{curvature:i,k:a}=b.mergedConfig,[o,l]=[(e+n)/2,(t+r)/2],u=Ie([e,t],[n,r])/i,s=u/2;let[f,c]=[o,l];do{f+=s,c=k(a,[o,l],f)[1]}while(Ie([o,l],[f,c])<u);return[f,c]}(e,t);return[e,n,t]}(e.find((({name:e})=>e===n)).coordinate,e.find((({name:e})=>e===r)).coordinate).map((e=>e.map((e=>parseFloat(e.toFixed(10)))))),o=`M${a[0].toString()} Q${a[1].toString()} ${a[2].toString()}`,l=`path${a.toString()}`,u=Le(...i)/10;return{...t,path:a,key:l,d:o,time:u}}))}(),await async function(){await y(),b.flylineLengths=b.flylines.map((({key:e})=>$.proxy.$refs[e][0].getTotalLength()))}()}function k(e,[t,n],r){return[r,n-e*t+e*r]}function P({offsetX:e,offsetY:n}){if(!t.dev)return;(e/u.value).toFixed(2),(n/s.value).toFixed(2)}return m((()=>{$=C()})),d((()=>t.config),(()=>{w()}),{deep:!0}),(e,t)=>(i(),a("div",{ref_key:"flylineChartEnhanced",ref:l,class:"dv-flyline-chart-enhanced",style:x(`background-image: url(${n(b).mergedConfig?n(b).mergedConfig.bgImgSrc:""})`),onClick:P},[n(b).flylines.length?(i(),a("svg",{key:0,width:n(u),height:n(s)},[o("defs",null,[o("radialGradient",{id:n(b).flylineGradientId,cx:"50%",cy:"50%",r:"50%"},zn,8,Rn),o("radialGradient",{id:n(b).haloGradientId,cx:"50%",cy:"50%",r:"50%"},Un,8,Hn)]),(i(!0),a(p,null,h(n(b).flylinePoints,(e=>(i(),a("g",{key:e.key+Math.random()},[o("defs",null,[e.halo.show?(i(),a("circle",{key:0,id:`halo${n(b).unique}${e.key}`,cx:e.coordinate[0],cy:e.coordinate[1]},[o("animate",{attributeName:"r",values:`1;${e.halo.radius}`,dur:`${e.halo.time}s`,repeatCount:"indefinite"},null,8,Qn),o("animate",{attributeName:"opacity",values:"1;0",dur:`${e.halo.time}s`,repeatCount:"indefinite"},null,8,qn)],8,Vn)):v("",!0)]),o("mask",{id:`mask${n(b).unique}${e.key}`},[e.halo.show?(i(),a("use",{key:0,"xlink:href":`#halo${n(b).unique}${e.key}`,fill:`url(#${n(b).haloGradientId})`},null,8,Xn)):v("",!0)],8,Yn),e.halo.show?(i(),a("use",{key:0,"xlink:href":`#halo${n(b).unique}${e.key}`,fill:e.halo.color,mask:`url(#mask${n(b).unique}${e.key})`},null,8,Jn)):v("",!0),e.icon.show?(i(),a("image",{key:1,"xlink:href":e.icon.src,width:e.icon.width,height:e.icon.height,x:e.icon.x,y:e.icon.y},null,8,Kn)):v("",!0),e.text.show?(i(),a("text",{key:2,style:x(`fontSize:${e.text.fontSize}px;color:${e.text.color}`),fill:e.text.color,x:e.text.x,y:e.text.y},g(e.name),13,Zn)):v("",!0)])))),128)),(i(!0),a(p,null,h(n(b).flylines,((e,t)=>(i(),a("g",{key:e.key+Math.random()},[o("defs",null,[o("path",{id:e.key,ref_for:!0,ref:e.key,d:e.d,fill:"transparent"},null,8,er)]),o("use",{"xlink:href":`#${e.key}`,"stroke-width":e.width,stroke:e.orbitColor},null,8,tr),o("mask",{id:`mask${n(b).unique}${e.key}`},[o("circle",{cx:"0",cy:"0",r:e.radius,fill:`url(#${n(b).flylineGradientId})`},[o("animateMotion",{dur:e.time,path:e.d,rotate:"auto",repeatCount:"indefinite"},null,8,ir)],8,rr)],8,nr),n(b).flylineLengths[t]?(i(),a("use",{key:0,"xlink:href":`#${e.key}`,"stroke-width":e.width,stroke:e.color,mask:`url(#mask${n(b).unique}${e.key})`},[o("animate",{attributeName:"stroke-dasharray",from:`0, ${n(b).flylineLengths[t]}`,to:`${n(b).flylineLengths[t]}, 0`,dur:e.time,repeatCount:"indefinite"},null,8,or)],8,ar)):v("",!0)])))),128))],8,Tn)):v("",!0)],4))}},ur={install(e){e.component("DvFlylineChartEnhanced",lr)}},sr=["width","height"],fr=["id"],cr=[o("stop",{offset:"0%","stop-color":"#fff","stop-opacity":"1"},null,-1),o("stop",{offset:"100%","stop-color":"#fff","stop-opacity":"0"},null,-1)],dr=["id"],pr=[o("stop",{offset:"0%","stop-color":"#fff","stop-opacity":"0"},null,-1),o("stop",{offset:"100%","stop-color":"#fff","stop-opacity":"1"},null,-1)],hr=["id","cx","cy"],gr=["values","dur"],vr=["dur"],mr=["xlink:href","width","height","x","y"],yr=["id"],br=["xlink:href","fill"],xr=["xlink:href","fill","mask"],Cr=["id","d"],$r=["xlink:href","stroke-width","stroke"],wr=["xlink:href","stroke-width","stroke","mask"],kr=["from","to","dur"],Pr=["id"],Ar=["r","fill"],Or=["dur","path"],Lr=["xlink:href","width","height","x","y"],Ir=["fill","x","y"],Sr={__name:"index",props:{config:{type:Object,default:()=>({})},dev:{type:Boolean,default:!1}},setup(e){const t=e,r=_e(),l=f(null),{width:u,height:s}=Be(l,(function(){w()}),(function(){w()})),b=c({unique:Math.random(),maskId:`flyline-mask-id-${r}`,maskCircleId:`mask-circle-id-${r}`,gradientId:`gradient-id-${r}`,gradient2Id:`gradient2-id-${r}`,defaultConfig:{centerPoint:[0,0],points:[],lineWidth:1,orbitColor:"rgba(103, 224, 227, .2)",flylineColor:"#ffde93",k:-.5,curvature:5,flylineRadius:100,duration:[20,30],relative:!0,bgImgUrl:"",text:{offset:[0,15],color:"#ffdb5c",fontSize:12},halo:{show:!0,duration:30,color:"#fb7293",radius:120},centerPointImg:{width:40,height:40,url:""},pointsImg:{width:15,height:15,url:""}},mergedConfig:null,paths:[],lengths:[],times:[],texts:[]});let $;async function w(){(function(){const e=Ge(Ee(b.defaultConfig,!0),t.config||{}),{points:n}=e;e.points=n.map((e=>e instanceof Array?{position:e,text:""}:e)),b.mergedConfig=e})(),function(){let{centerPoint:e,points:t}=b.mergedConfig;const{relative:n}=b.mergedConfig;t=t.map((({position:e})=>e)),n&&(e=[u.value*e[0],s.value*e[1]],t=t.map((([e,t])=>[u.value*e,s.value*t]))),b.paths=t.map((t=>function(e,t){const n=function([e,t],[n,r]){const{curvature:i,k:a}=b.mergedConfig,[o,l]=[(e+n)/2,(t+r)/2],u=Ie([e,t],[n,r])/i,s=u/2;let[f,c]=[o,l];do{f+=s,c=k(a,[o,l],f)[1]}while(Ie([o,l],[f,c])<u);return[f,c]}(e,t);return[t,n,e]}(e,t)))}(),await async function(){await y(),b.lengths=b.paths.map(((e,t)=>$.proxy.$refs[`path${t}`][0].getTotalLength()))}(),function(){const{duration:e,points:t}=b.mergedConfig;b.times=t.map((t=>Le(...e)/10))}(),function(){const{points:e}=b.mergedConfig;b.texts=e.map((({text:e})=>e))}()}function k(e,[t,n],r){return[r,n-e*t+e*r]}function P({offsetX:e,offsetY:n}){if(!t.dev)return;(e/u.value).toFixed(2),(n/s.value).toFixed(2)}return m((()=>{$=C()})),d((()=>t.config),(()=>{w()}),{deep:!0}),(e,t)=>(i(),a("div",{ref_key:"flylineChart",ref:l,class:"dv-flyline-chart",style:x(`background-image: url(${n(b).mergedConfig?n(b).mergedConfig.bgImgUrl:""})`),onClick:P},[n(b).mergedConfig?(i(),a("svg",{key:0,width:n(u),height:n(s)},[o("defs",null,[o("radialGradient",{id:n(b).gradientId,cx:"50%",cy:"50%",r:"50%"},cr,8,fr),o("radialGradient",{id:n(b).gradient2Id,cx:"50%",cy:"50%",r:"50%"},pr,8,dr),n(b).paths[0]?(i(),a("circle",{key:0,id:`circle${n(b).paths[0].toString()}`,cx:n(b).paths[0][2][0],cy:n(b).paths[0][2][1]},[o("animate",{attributeName:"r",values:`1;${n(b).mergedConfig.halo.radius}`,dur:n(b).mergedConfig.halo.duration/10+"s",repeatCount:"indefinite"},null,8,gr),o("animate",{attributeName:"opacity",values:"1;0",dur:n(b).mergedConfig.halo.duration/10+"s",repeatCount:"indefinite"},null,8,vr)],8,hr)):v("",!0)]),n(b).paths[0]?(i(),a("image",{key:0,"xlink:href":n(b).mergedConfig.centerPointImg.url,width:n(b).mergedConfig.centerPointImg.width,height:n(b).mergedConfig.centerPointImg.height,x:n(b).paths[0][2][0]-n(b).mergedConfig.centerPointImg.width/2,y:n(b).paths[0][2][1]-n(b).mergedConfig.centerPointImg.height/2},null,8,mr)):v("",!0),o("mask",{id:`maskhalo${n(b).paths[0].toString()}`},[n(b).paths[0]?(i(),a("use",{key:0,"xlink:href":`#circle${n(b).paths[0].toString()}`,fill:`url(#${n(b).gradient2Id})`},null,8,br)):v("",!0)],8,yr),n(b).paths[0]&&n(b).mergedConfig.halo.show?(i(),a("use",{key:1,"xlink:href":`#circle${n(b).paths[0].toString()}`,fill:n(b).mergedConfig.halo.color,mask:`url(#maskhalo${n(b).paths[0].toString()})`},null,8,xr)):v("",!0),(i(!0),a(p,null,h(n(b).paths,((e,t)=>(i(),a("g",{key:t},[o("defs",null,[o("path",{id:`path${e.toString()}`,ref_for:!0,ref:`path${t}`,d:`M${e[0].toString()} Q${e[1].toString()} ${e[2].toString()}`,fill:"transparent"},null,8,Cr)]),o("use",{"xlink:href":`#path${e.toString()}`,"stroke-width":n(b).mergedConfig.lineWidth,stroke:n(b).mergedConfig.orbitColor},null,8,$r),n(b).lengths[t]?(i(),a("use",{key:0,"xlink:href":`#path${e.toString()}`,"stroke-width":n(b).mergedConfig.lineWidth,stroke:n(b).mergedConfig.flylineColor,mask:`url(#mask${n(b).unique}${e.toString()})`},[o("animate",{attributeName:"stroke-dasharray",from:`0, ${n(b).lengths[t]}`,to:`${n(b).lengths[t]}, 0`,dur:n(b).times[t]||0,repeatCount:"indefinite"},null,8,kr)],8,wr)):v("",!0),o("mask",{id:`mask${n(b).unique}${e.toString()}`},[o("circle",{cx:"0",cy:"0",r:n(b).mergedConfig.flylineRadius,fill:`url(#${n(b).gradientId})`},[o("animateMotion",{dur:n(b).times[t]||0,path:`M${e[0].toString()} Q${e[1].toString()} ${e[2].toString()}`,rotate:"auto",repeatCount:"indefinite"},null,8,Or)],8,Ar)],8,Pr),o("image",{"xlink:href":n(b).mergedConfig.pointsImg.url,width:n(b).mergedConfig.pointsImg.width,height:n(b).mergedConfig.pointsImg.height,x:e[0][0]-n(b).mergedConfig.pointsImg.width/2,y:e[0][1]-n(b).mergedConfig.pointsImg.height/2},null,8,Lr),o("text",{style:x(`fontSize:${n(b).mergedConfig.text.fontSize}px;`),fill:n(b).mergedConfig.text.color,x:e[0][0]+n(b).mergedConfig.text.offset[0],y:e[0][1]+n(b).mergedConfig.text.offset[1]},g(n(b).texts[t]),13,Ir)])))),128))],8,sr)):v("",!0)],4))}},Mr={install(e){e.component("DvFlylineChart",Sr)}},jr=e=>(L("data-v-282cb432"),e=e(),I(),e),Fr={class:"ranking-info"},_r={class:"rank"},Gr=["innerHTML"],Er={class:"ranking-value"},Br={class:"ranking-column"},Dr=[jr((()=>o("div",{class:"shine"},null,-1)))],Wr={__name:"index",props:{config:{type:Object,default:()=>({})}},setup(e){t((e=>({"2801d2f6":n(b),"5c86b458":n(y),"6524ce8e":n(w),"65ae9c69":n(C)})));const l=e,u=f(null),{width:s,height:v}=Be(u,(function(){m.mergedConfig&&P(!0)}),(function(){k()})),m=c({defaultConfig:{data:[],rowNum:5,waitTime:2e3,carousel:"single",unit:"",sort:!0,valueFormatter:null,textColor:"#fff",color:"#1370fb",fontSize:13},mergedConfig:null,rowsData:[],rows:[],heights:[],avgHeight:0,animationIndex:0,animationHandler:"",updater:0});d((()=>l.config),(()=>{L(),k()}),{deep:!0});const y=r((()=>l.config.textColor?l.config.textColor:m.defaultConfig.textColor)),b=r((()=>l.config.color?l.config.color:m.defaultConfig.color)),C=r((()=>R(b.value,50))),w=r((()=>`${l.config.fontSize?l.config.fontSize:m.defaultConfig.fontSize}px`));function k(){m.mergedConfig=Ge(Ee(m.defaultConfig,!0),l.config||{}),function(){let{data:e}=m.mergedConfig;const{rowNum:t,sort:n}=m.mergedConfig;n&&e.sort((({value:e},{value:t})=>e>t?-1:e<t?1:0));const r=e.map((({value:e})=>e)),i=Math.min(...r)||0,a=Math.abs(i),o=(Math.max(...r)||0)+a;e=e.map(((e,t)=>({...e,ranking:t+1,percent:(e.value+a)/o*100})));const l=e.length;l>t&&l<2*t&&(e=[...e,...e]),e=e.map(((e,t)=>({...e,scroll:t}))),m.rowsData=e,m.rows=e}(),P(),O(!0)}function P(e=!1){const{rowNum:t,data:n}=m.mergedConfig,r=v.value/t;m.avgHeight=r,e||(m.heights=new Array(n.length).fill(r))}$((()=>{L()}));const A=r((()=>"single"===m.mergedConfig.carousel));async function O(e=!1){const{waitTime:t,rowNum:n}=m.mergedConfig,r=m.rowsData.length;if(n>=r)return;const{updater:i}=m;if(e&&(await new Promise((e=>setTimeout(e,t))),i!==m.updater))return;const a=A.value?1:n,o=m.rowsData.slice(m.animationIndex);if(o.push(...m.rowsData.slice(0,m.animationIndex)),m.rows=o.slice(0,A.value?n+1:2*n),m.heights=new Array(r).fill(m.avgHeight),await new Promise((e=>setTimeout(e,300))),i!==m.updater)return;m.heights.fill(0,0,a),m.animationIndex+=a;const l=m.animationIndex-r;l>=0&&(m.animationIndex=l),m.animationHandler=setTimeout(O,t-300)}function L(){m.updater=(m.updater+1)%999999,m.animationHandler&&clearTimeout(m.animationHandler)}return(e,t)=>(i(),a("div",{ref_key:"scrollRankingBoard",ref:u,class:"dv-scroll-ranking-board"},[(i(!0),a(p,null,h(n(m).rows,((e,t)=>(i(),a("div",{key:e.toString()+e.scroll,class:"row-item",style:x(`height: ${n(m).heights[t]}px;`)},[o("div",Fr,[o("div",_r," No."+g(e.ranking),1),o("div",{class:"info-name",innerHTML:e.name},null,8,Gr),o("div",Er,g(n(m).mergedConfig.valueFormatter?n(m).mergedConfig.valueFormatter(e):e.value+n(m).mergedConfig.unit),1)]),o("div",Br,[o("div",{class:"inside-column",style:x(`width: ${e.percent}%;`)},Dr,4)])],4)))),128))],512))}},Nr=H(Wr,[["__scopeId","data-v-282cb432"]]),Tr={install(e){e.component("DvScrollRankingBoard",Nr)}},Rr=["align","innerHTML"],zr=["align","onClick","onMouseenter","innerHTML"],Hr={__name:"index",props:{config:{type:Object,default:()=>({})}},emits:["mouseover","click","getFirstRow"],setup(e,{expose:t,emit:r}){const o=e,l=r,u=f(null),{width:s,height:g}=Be(u,(function(){m.mergedConfig&&(w(),k())}),(function(){b()})),m=c({defaultConfig:{header:[],data:[],rowNum:5,headerBGC:"#00BAFF",oddRowBGC:"#003B51",evenRowBGC:"#0A2732",waitTime:2e3,headerHeight:35,columnWidth:[],align:[],index:!1,indexHeader:"#",carousel:"single",hoverPause:!0},mergedConfig:null,header:[],rowsData:[],rows:[],widths:[],heights:[],avgHeight:0,aligns:[],animationIndex:0,animationHandler:"",updater:0,needCalc:!1});function y(e,t,n,r,i){if(e){const{ceils:e,rowIndex:t}=r;l("mouseover",{row:e,ceil:i,rowIndex:t,columnIndex:n})}m.mergedConfig.hoverPause&&(e?A():P(!0))}function b(){m.mergedConfig=Ge(Ee(m.defaultConfig,!0),o.config||{}),function(){let{header:e}=m.mergedConfig;const{index:t,indexHeader:n}=m.mergedConfig;if(!e.length)return void(e=[]);e=[...e],t&&e.unshift(n),m.header=e}(),C(),w(),k(),function(){const e=m.header.length,t=new Array(e).fill("left"),{align:n}=m.mergedConfig;m.aligns=Ge(t,n)}(),P(!0)}function C(){let{data:e}=m.mergedConfig;const{index:t,headerBGC:n,rowNum:r}=m.mergedConfig;t&&(e=e.map(((e,t)=>{e=[...e];const r=`<span class="index" style="background-color: ${n};">${t+1}</span>`;return e.unshift(r),e}))),e=e.map(((e,t)=>({ceils:e,rowIndex:t})));const i=e.length;i>r&&i<2*r&&(e=[...e,...e]),e=e.map(((e,t)=>({...e,scroll:t}))),m.rowsData=e,m.rows=e}function w(){const{columnWidth:e,header:t}=m.mergedConfig,n=e.reduce(((e,t)=>e+t),0);let r=0;m.rowsData[0]?r=m.rowsData[0].ceils.length:t.length&&(r=t.length);const i=(s.value-n)/(r-e.length),a=new Array(r).fill(i);m.widths=Ge(a,e)}function k(e=!1){const{headerHeight:t,rowNum:n,data:r}=m.mergedConfig;let i=g.value;m.header.length&&(i-=t);const a=i/n;m.avgHeight=a,e||(m.heights=new Array(r.length).fill(a))}async function P(e=!1){m.needCalc&&(C(),k(),m.needCalc=!1);const{waitTime:t,carousel:n,rowNum:r}=m.mergedConfig,{updater:i}=m,a=m.rowsData.length;if(r>=a||(e&&await new Promise((e=>setTimeout(e,t))),i!==m.updater))return;const o="single"===n?1:r,u=m.rowsData.slice(m.animationIndex);if(u.push(...m.rowsData.slice(0,m.animationIndex)),m.rows=u.slice(0,"page"===n?2*r:r+1),m.heights=new Array(a).fill(m.avgHeight),await new Promise((e=>setTimeout(e,300))),i!==m.updater)return;m.heights.splice(0,o,...new Array(o).fill(0)),m.animationIndex+=o;const s=m.animationIndex-a;s>=0&&(m.animationIndex=s),m.animationHandler=setTimeout(P,t-300),l("getFirstRow",u[1])}function A(){m.updater=(m.updater+1)%999999,m.animationHandler&&clearTimeout(m.animationHandler)}return d((()=>o.config),(e=>{A(),b()}),{deep:!0}),$((()=>{A()})),t({updateRows:function(e,t){m.mergedConfig={...m.mergedConfig,data:[...e]},m.needCalc=!0,"number"==typeof t&&(m.animationIndex=t),m.animationHandler||P(!0)}}),(e,t)=>(i(),a("div",{ref_key:"scrollBoard",ref:u,class:"dv-scroll-board"},[n(m).header.length&&n(m).mergedConfig?(i(),a("div",{key:0,class:"header",style:x(`background-color: ${n(m).mergedConfig.headerBGC};`)},[(i(!0),a(p,null,h(n(m).header,((e,t)=>(i(),a("div",{key:`${e}${t}`,class:"header-item",style:x(`\n          height: ${n(m).mergedConfig.headerHeight}px;\n          line-height: ${n(m).mergedConfig.headerHeight}px;\n          width: ${n(m).widths[t]}px;\n        `),align:n(m).aligns[t],innerHTML:e},null,12,Rr)))),128))],4)):v("",!0),n(m).mergedConfig?(i(),a("div",{key:1,class:"rows",style:x(`height: ${n(g)-(n(m).header.length?n(m).mergedConfig.headerHeight:0)}px;`)},[(i(!0),a(p,null,h(n(m).rows,((e,r)=>(i(),a("div",{key:`${e.toString()}${e.scroll}`,class:"row-item",style:x(`\n          height: ${n(m).heights[r]}px;\n          line-height: ${n(m).heights[r]}px;\n          background-color: ${n(m).mergedConfig[e.rowIndex%2==0?"evenRowBGC":"oddRowBGC"]};\n        `)},[(i(!0),a(p,null,h(e.ceils,((o,u)=>(i(),a("div",{key:`${o}${r}${u}`,class:"ceil",style:x(`width: ${n(m).widths[u]}px;`),align:n(m).aligns[u],onClick:t=>function(e,t,n,r){const{ceils:i,rowIndex:a}=n;l("click",{row:i,ceil:r,rowIndex:a,columnIndex:t})}(0,u,e,o),onMouseenter:t=>y(!0,0,u,e,o),onMouseleave:t[0]||(t[0]=e=>y(!1)),innerHTML:o},null,44,zr)))),128))],4)))),128))],4)):v("",!0)],512))}},Ur={install(e){e.component("DvScrollBoard",Hr)}};var Vr,Qr={},qr={},Yr={};function Xr(){if(Vr)return Yr;Vr=1;var e=ot;Object.defineProperty(Yr,"__esModule",{value:!0}),Yr.filterNonNumber=i,Yr.deepMerge=a,Yr.mulAdd=o,Yr.mergeSameStackData=function(e,n){var r=e.stack;if(!r)return(0,t.default)(e.data);var i=n.filter((function(e){return e.stack===r})),a=i.findIndex((function(t){return t.data===e.data})),l=i.splice(0,a+1).map((function(e){return e.data})),u=l[0].length;return new Array(u).fill(0).map((function(e,t){return o(l.map((function(e){return e[t]})))}))},Yr.getTwoPointDistance=l,Yr.getLinearGradientColor=function(e,n,r,i){if(e&&n&&r&&i.length){var a=i;"string"==typeof a&&(a=[i,i]);var o=e.createLinearGradient.apply(e,(0,t.default)(n).concat((0,t.default)(r))),l=1/(a.length-1);return a.forEach((function(e,t){return o.addColorStop(l*t,e)})),o}},Yr.getPolylineLength=function(e){return o(new Array(e.length-1).fill(0).map((function(t,n){return[e[n],e[n+1]]})).map((function(e){return l.apply(void 0,(0,t.default)(e))})))},Yr.getPointToLineDistance=function(e,t,n){var r=l(e,t),i=l(e,n),a=l(t,n);return.5*Math.sqrt((r+i+a)*(r+i-a)*(r+a-i)*(i+a-r))/a},Yr.initNeedSeries=function(e,t,n){return(e=(e=e.filter((function(e){return e.type===n}))).map((function(e){return a((0,r.deepClone)(t,!0),e)}))).filter((function(e){return e.show}))},Yr.radianToAngle=function(e){return e/Math.PI*180};var t=e(kt()),n=e(Jt()),r=Kt();function i(e){return e.filter((function(e){return"number"==typeof e}))}function a(e,t){for(var i in t)e[i]&&"object"===(0,n.default)(e[i])?a(e[i],t[i]):"object"!==(0,n.default)(t[i])?e[i]=t[i]:e[i]=(0,r.deepClone)(t[i],!0);return e}function o(e){return(e=i(e)).reduce((function(e,t){return e+t}),0)}function l(e,t){var n=Math.abs(e[0]-t[0]),r=Math.abs(e[1]-t[1]);return Math.sqrt(n*n+r*r)}return Yr}var Jr=ot,Kr=Jr(st()),Zr=Jr(kt()),ei=rt,ti=rn(),ni=Kt(),ri=Lt,ii=Xr();function ai(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function oi(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ai(Object(n),!0).forEach((function(t){(0,Kr.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ai(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var li={shape:{rx:0,ry:0,r:0,startAngle:0,endAngle:0,gradientStartAngle:null,gradientEndAngle:null},validator:function(e){var t=e.shape;return!["rx","ry","r","startAngle","endAngle"].find((function(e){return"number"!=typeof t[e]}))},draw:function(e,t){var n=e.ctx,r=t.shape,i=t.style.gradient;1===(i=i.map((function(e){return(0,ri.getColorFromRgbValue)(e)}))).length&&(i=[i[0],i[0]]);var a=i.length-1,o=r.gradientStartAngle,l=r.gradientEndAngle,u=r.startAngle,s=r.endAngle,f=r.r,c=r.rx,d=r.ry;null===o&&(o=u),null===l&&(l=s);var p=(l-o)/a;p===2*Math.PI&&(p=2*Math.PI-.001);for(var h=0;h<a;h++){n.beginPath();var g=(0,ni.getCircleRadianPoint)(c,d,f,u+p*h),v=(0,ni.getCircleRadianPoint)(c,d,f,u+p*(h+1)),m=(0,ii.getLinearGradientColor)(n,g,v,[i[h],i[h+1]]),y=u+p*h,b=u+p*(h+1),x=!1;if(b>s&&(b=s,x=!0),n.arc(c,d,f,y,b),n.strokeStyle=m,n.stroke(),x)break}}},ui={shape:{number:[],content:"",position:[0,0],toFixed:0,rowGap:0,formatter:null},validator:function(e){var t=e.shape,n=t.number,r=t.content,i=t.position;return n instanceof Array&&"string"==typeof r&&i instanceof Array},draw:function(e,t){var n=e.ctx,r=t.shape,i=r.number,a=r.content,o=r.toFixed,l=r.rowGap,u=r.formatter,s=a.split("{nt}"),f="";s.forEach((function(e,t){var n=i[t];"number"!=typeof n&&(n=""),"number"==typeof n&&(n=n.toFixed(o),"function"==typeof u&&(n=u(n))),f+=e+(n||"")})),ti.text.draw({ctx:n},{shape:oi(oi({},r),{},{content:f,rowGap:l})})}},si={shape:{x:0,y:0,w:0,h:0},validator:function(e){var t=e.shape,n=t.x,r=t.y,i=t.w,a=t.h;return"number"==typeof n&&"number"==typeof r&&"number"==typeof i&&"number"==typeof a},draw:function(e,t){var n=e.ctx,r=t.shape;n.beginPath();var i=r.x,a=r.y,o=r.w,l=r.h/2;n.strokeStyle=n.fillStyle,n.moveTo(i,a+l),n.lineTo(i+o,a+l),n.lineWidth=1,n.stroke(),n.beginPath();var u=l-10;u<=0&&(u=3),n.arc(i+o/2,a+l,u,0,2*Math.PI),n.lineWidth=5,n.stroke(),n.fillStyle="#fff",n.fill()},hoverCheck:function(e,t){var n=t.shape,r=n.x,i=n.y,a=n.w,o=n.h;return(0,ni.checkPointIsInRect)(e,r,i,a,o)},setGraphCenter:function(e,t){var n=t.shape,r=t.style,i=n.x,a=n.y,o=n.w,l=n.h;r.graphCenter=[i+o/2,a+l/2]}};(0,ei.extendNewGraph)("pie",{shape:{rx:0,ry:0,ir:0,or:0,startAngle:0,endAngle:0,clockWise:!0},validator:function(e){var t=e.shape;return!["rx","ry","ir","or","startAngle","endAngle"].find((function(e){return"number"!=typeof t[e]}))},draw:function(e,t){var n=e.ctx,r=t.shape;n.beginPath();var i=r.rx,a=r.ry,o=r.ir,l=r.or,u=r.startAngle,s=r.endAngle,f=r.clockWise;i=parseInt(i)+.5,a=parseInt(a)+.5,n.arc(i,a,o>0?o:0,u,s,!f);var c=(0,ni.getCircleRadianPoint)(i,a,l,s).map((function(e){return parseInt(e)+.5})),d=(0,ni.getCircleRadianPoint)(i,a,o,u).map((function(e){return parseInt(e)+.5}));n.lineTo.apply(n,(0,Zr.default)(c)),n.arc(i,a,l>0?l:0,s,u,f),n.lineTo.apply(n,(0,Zr.default)(d)),n.closePath(),n.stroke(),n.fill()}}),(0,ei.extendNewGraph)("agArc",li),(0,ei.extendNewGraph)("numberText",ui),(0,ei.extendNewGraph)("lineIcon",si);var fi,ci={},di={},pi={},hi={};var gi,vi={};var mi,yi={};var bi,xi={};var Ci,$i={};var wi,ki={};var Pi,Ai={};function Oi(){if(Pi)return Ai;Pi=1,Object.defineProperty(Ai,"__esModule",{value:!0}),Ai.pieConfig=void 0;var e={show:!0,name:"",radius:"50%",center:["50%","50%"],startAngle:-Math.PI/2,roseType:!1,roseSort:!0,roseIncrement:"auto",data:[],insideLabel:{show:!1,formatter:"{percent}%",style:{fontSize:10,fill:"#fff",textAlign:"center",textBaseline:"middle"}},outsideLabel:{show:!0,formatter:"{name}",style:{fontSize:11},labelLineBendGap:"20%",labelLineEndLength:50,labelLineStyle:{lineWidth:1}},pieStyle:{},percentToFixed:0,rLevel:10,animationDelayGap:60,animationCurve:"easeOutCubic",startAnimationCurve:"easeOutBack",animationFrame:50};return Ai.pieConfig=e,Ai}var Li,Ii={};var Si,Mi={};var ji,Fi={};function _i(){if(ji)return Fi;ji=1,Object.defineProperty(Fi,"__esModule",{value:!0}),Fi.gaugeConfig=void 0;var e={show:!0,name:"",radius:"60%",center:["50%","50%"],startAngle:-Math.PI/4*5,endAngle:Math.PI/4,min:0,max:100,splitNum:5,arcLineWidth:15,data:[],dataItemStyle:{},axisTick:{show:!0,tickLength:6,style:{stroke:"#999",lineWidth:1}},axisLabel:{show:!0,data:[],formatter:null,labelGap:5,style:{}},pointer:{show:!0,valueIndex:0,style:{scale:[1,1],fill:"#fb7293"}},details:{show:!1,formatter:null,offset:[0,0],valueToFixed:0,position:"center",style:{fontSize:20,fontWeight:"bold",textAlign:"center",textBaseline:"middle"}},backgroundArc:{show:!0,style:{stroke:"#e0e0e0"}},rLevel:10,animationCurve:"easeOutCubic",animationFrame:50};return Fi.gaugeConfig=e,Fi}var Gi,Ei,Bi,Di={};function Wi(){return Ei||(Ei=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.changeDefaultConfig=function(e,t){if(!p["".concat(e,"Config")])return;(0,d.deepMerge)(p["".concat(e,"Config")],t)},Object.defineProperty(e,"colorConfig",{enumerable:!0,get:function(){return t.colorConfig}}),Object.defineProperty(e,"gridConfig",{enumerable:!0,get:function(){return n.gridConfig}}),Object.defineProperty(e,"xAxisConfig",{enumerable:!0,get:function(){return r.xAxisConfig}}),Object.defineProperty(e,"yAxisConfig",{enumerable:!0,get:function(){return r.yAxisConfig}}),Object.defineProperty(e,"titleConfig",{enumerable:!0,get:function(){return i.titleConfig}}),Object.defineProperty(e,"lineConfig",{enumerable:!0,get:function(){return a.lineConfig}}),Object.defineProperty(e,"barConfig",{enumerable:!0,get:function(){return o.barConfig}}),Object.defineProperty(e,"pieConfig",{enumerable:!0,get:function(){return l.pieConfig}}),Object.defineProperty(e,"radarAxisConfig",{enumerable:!0,get:function(){return u.radarAxisConfig}}),Object.defineProperty(e,"radarConfig",{enumerable:!0,get:function(){return s.radarConfig}}),Object.defineProperty(e,"gaugeConfig",{enumerable:!0,get:function(){return f.gaugeConfig}}),Object.defineProperty(e,"legendConfig",{enumerable:!0,get:function(){return c.legendConfig}}),e.keys=void 0;var t=(fi||(fi=1,Object.defineProperty(hi,"__esModule",{value:!0}),hi.colorConfig=void 0,hi.colorConfig=["#37a2da","#32c5e9","#67e0e3","#9fe6b8","#ffdb5c","#ff9f7f","#fb7293","#e062ae","#e690d1","#e7bcf3","#9d96f5","#8378ea","#96bfff"]),hi),n=(gi||(gi=1,Object.defineProperty(vi,"__esModule",{value:!0}),vi.gridConfig=void 0,vi.gridConfig={left:"10%",right:"10%",top:60,bottom:60,style:{fill:"rgba(0, 0, 0, 0)"},rLevel:-30,animationCurve:"easeOutCubic",animationFrame:30}),vi),r=(mi||(mi=1,Object.defineProperty(yi,"__esModule",{value:!0}),yi.yAxisConfig=yi.xAxisConfig=void 0,yi.xAxisConfig={name:"",show:!0,position:"bottom",nameGap:15,nameLocation:"end",nameTextStyle:{fill:"#333",fontSize:10},min:"20%",max:"20%",interval:null,minInterval:null,maxInterval:null,boundaryGap:null,splitNumber:5,axisLine:{show:!0,style:{stroke:"#333",lineWidth:1}},axisTick:{show:!0,style:{stroke:"#333",lineWidth:1}},axisLabel:{show:!0,formatter:null,style:{fill:"#333",fontSize:10,rotate:0}},splitLine:{show:!1,style:{stroke:"#d4d4d4",lineWidth:1}},rLevel:-20,animationCurve:"easeOutCubic",animationFrame:50},yi.yAxisConfig={name:"",show:!0,position:"left",nameGap:15,nameLocation:"end",nameTextStyle:{fill:"#333",fontSize:10},min:"20%",max:"20%",interval:null,minInterval:null,maxInterval:null,boundaryGap:null,splitNumber:5,axisLine:{show:!0,style:{stroke:"#333",lineWidth:1}},axisTick:{show:!0,style:{stroke:"#333",lineWidth:1}},axisLabel:{show:!0,formatter:null,style:{fill:"#333",fontSize:10,rotate:0}},splitLine:{show:!0,style:{stroke:"#d4d4d4",lineWidth:1}},rLevel:-20,animationCurve:"easeOutCubic",animationFrame:50}),yi),i=(bi||(bi=1,Object.defineProperty(xi,"__esModule",{value:!0}),xi.titleConfig=void 0,xi.titleConfig={show:!0,text:"",offset:[0,-20],style:{fill:"#333",fontSize:17,fontWeight:"bold",textAlign:"center",textBaseline:"bottom"},rLevel:20,animationCurve:"easeOutCubic",animationFrame:50}),xi),a=(Ci||(Ci=1,Object.defineProperty($i,"__esModule",{value:!0}),$i.lineConfig=void 0,$i.lineConfig={show:!0,name:"",stack:"",smooth:!1,xAxisIndex:0,yAxisIndex:0,data:[],lineStyle:{lineWidth:1},linePoint:{show:!0,radius:2,style:{fill:"#fff",lineWidth:1}},lineArea:{show:!1,gradient:[],style:{opacity:.5}},label:{show:!1,position:"top",offset:[0,-10],formatter:null,style:{fontSize:10}},rLevel:10,animationCurve:"easeOutCubic",animationFrame:50}),$i),o=(wi||(wi=1,Object.defineProperty(ki,"__esModule",{value:!0}),ki.barConfig=void 0,ki.barConfig={show:!0,name:"",stack:"",shapeType:"normal",echelonOffset:10,barWidth:"auto",barGap:"30%",barCategoryGap:"20%",xAxisIndex:0,yAxisIndex:0,data:[],backgroundBar:{show:!1,width:"auto",style:{fill:"rgba(200, 200, 200, .4)"}},label:{show:!1,position:"top",offset:[0,-10],formatter:null,style:{fontSize:10}},gradient:{color:[],local:!0},barStyle:{},independentColor:!1,independentColors:[],rLevel:0,animationCurve:"easeOutCubic",animationFrame:50}),ki),l=Oi(),u=function(){if(Li)return Ii;Li=1,Object.defineProperty(Ii,"__esModule",{value:!0}),Ii.radarAxisConfig=void 0;var e={show:!0,center:["50%","50%"],radius:"65%",startAngle:-Math.PI/2,splitNum:5,polygon:!1,axisLabel:{show:!0,labelGap:15,color:[],style:{fill:"#333"}},axisLine:{show:!0,color:[],style:{stroke:"#999",lineWidth:1}},splitLine:{show:!0,color:[],style:{stroke:"#d4d4d4",lineWidth:1}},splitArea:{show:!1,color:["#f5f5f5","#e6e6e6"],style:{}},rLevel:-10,animationCurve:"easeOutCubic",animationFrane:50};return Ii.radarAxisConfig=e,Ii}(),s=(Si||(Si=1,Object.defineProperty(Mi,"__esModule",{value:!0}),Mi.radarConfig=void 0,Mi.radarConfig={show:!0,name:"",data:[],radarStyle:{lineWidth:1},point:{show:!0,radius:2,style:{fill:"#fff"}},label:{show:!0,offset:[0,0],labelGap:5,formatter:null,style:{fontSize:10}},rLevel:10,animationCurve:"easeOutCubic",animationFrane:50}),Mi),f=_i(),c=(Gi||(Gi=1,Object.defineProperty(Di,"__esModule",{value:!0}),Di.legendConfig=void 0,Di.legendConfig={show:!0,orient:"horizontal",left:"auto",right:"auto",top:"auto",bottom:"auto",itemGap:10,iconWidth:25,iconHeight:10,selectAble:!0,data:[],textStyle:{fontFamily:"Arial",fontSize:13,fill:"#000"},iconStyle:{},textUnselectedStyle:{fontFamily:"Arial",fontSize:13,fill:"#999"},iconUnselectedStyle:{fill:"#999"},rLevel:20,animationCurve:"easeOutCubic",animationFrame:50}),Di),d=Xr(),p={colorConfig:t.colorConfig,gridConfig:n.gridConfig,xAxisConfig:r.xAxisConfig,yAxisConfig:r.yAxisConfig,titleConfig:i.titleConfig,lineConfig:a.lineConfig,barConfig:o.barConfig,pieConfig:l.pieConfig,radarAxisConfig:u.radarAxisConfig,radarConfig:s.radarConfig,gaugeConfig:f.gaugeConfig,legendConfig:c.legendConfig};e.keys=["color","title","legend","xAxis","yAxis","grid","radarAxis","line","bar","pie","radar","gauge"]}(pi)),pi}var Ni,Ti,Ri={},zi={};function Hi(){if(Ni)return zi;Ni=1;var e=ot;Object.defineProperty(zi,"__esModule",{value:!0}),zi.doUpdate=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.chart,n=e.series,r=e.key,a=e.getGraphConfig,o=e.getStartGraphConfig,l=e.beforeChange,u=e.beforeUpdate,s=e.afterAddGraph;t[r]?t[r].update(n):t[r]=new i({chart:t,key:r,getGraphConfig:a,getStartGraphConfig:o,beforeChange:l,beforeUpdate:u,afterAddGraph:s},n)},zi.Updater=void 0;var t=e(kt()),n=e(Jt()),r=e(Ot()),i=function e(t,n){(0,r.default)(this,e);var i=t.chart,a=t.key;"function"==typeof t.getGraphConfig&&(i[a]||(this.graphs=i[a]=[]),Object.assign(this,t),this.update(n))};function a(e,t){Object.keys(t).forEach((function(n){"shape"===n||"style"===n?e.animation(n,t[n],!0):e[n]=t[n]}))}return zi.Updater=i,i.prototype.update=function(e){var r=this,i=this.graphs,o=this.beforeUpdate;if(function(e,t){var n=e.graphs,r=e.chart.render,i=n.length,a=t.length;if(i>a){n.splice(a).forEach((function(e){return e.forEach((function(e){return r.delGraph(e)}))}))}}(this,e),e.length){var l=(0,n.default)(o);e.forEach((function(e,n){"function"===l&&o(i,e,n,r);var u=i[n];u?function(e,n,r,i){var o=i.getGraphConfig,l=i.chart.render,u=i.beforeChange,s=o(n,i);(function(e,n,r){var i=e.length,a=n.length;if(a>i){var o=e.slice(-1)[0],l=new Array(a-i).fill(0).map((function(e){return r.clone(o)}));e.push.apply(e,(0,t.default)(l))}else if(a<i){e.splice(a).forEach((function(e){return r.delGraph(e)}))}})(e,s,l),e.forEach((function(e,t){var n=s[t];"function"==typeof u&&u(e,n),a(e,n)}))}(u,e,0,r):function(e,t,n,r){var i=r.getGraphConfig,o=r.getStartGraphConfig,l=r.chart,u=l.render,s=null;"function"==typeof o&&(s=o(t,r));var f=i(t,r);if(f.length){s?(e[n]=s.map((function(e){return u.add(e)})),e[n].forEach((function(e,t){a(e,f[t])}))):e[n]=f.map((function(e){return u.add(e)}));var c=r.afterAddGraph;"function"==typeof c&&c(e[n])}}(i,e,n,r)}))}},zi}function Ui(){if(Ti)return Ri;Ti=1;var e=ot;Object.defineProperty(Ri,"__esModule",{value:!0}),Ri.title=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},l=[];t.title&&(l[0]=(0,a.deepMerge)((0,r.deepClone)(i.titleConfig,!0),t.title)),(0,n.doUpdate)({chart:e,series:l,key:"title",getGraphConfig:o})};var t=e(Nt()),n=Hi(),r=Kt(),i=Wi(),a=Xr();function o(e,n){var r=i.titleConfig.animationCurve,a=i.titleConfig.animationFrame,o=i.titleConfig.rLevel,l=function(e,n){var r=e.offset,i=e.text,a=n.chart.gridArea,o=a.x,l=a.y,u=a.w,s=(0,t.default)(r,2),f=s[0],c=s[1];return{content:i,position:[o+u/2+f,l+c]}}(e,n),u=function(e){var t=e.style;return t}(e);return[{name:"text",index:o,visible:e.show,animationCurve:r,animationFrame:a,shape:l,style:u}]}return Ri}var Vi,Qi={};function qi(){if(Vi)return Qi;Vi=1;var e=ot;Object.defineProperty(Qi,"__esModule",{value:!0}),Qi.grid=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.grid;n=(0,o.deepMerge)((0,i.deepClone)(a.gridConfig,!0),n||{}),(0,r.doUpdate)({chart:e,series:[n],key:"grid",getGraphConfig:u})};var t=e(Nt()),n=e(st()),r=Hi(),i=Kt(),a=Wi(),o=Xr();function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function u(e,r){var i=e.animationCurve,a=e.animationFrame,o=e.rLevel,u=function(e,n){var r=(0,t.default)(n.chart.render.area,2),i=r[0],a=r[1],o=s(e.left,i),l=s(e.right,i),u=s(e.top,a),f=s(e.bottom,a),c=i-o-l,d=a-u-f;return{x:o,y:u,w:c,h:d}}(e,r),f=function(e){var t=e.style;return t}(e);return r.chart.gridArea=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l(Object(r),!0).forEach((function(t){(0,n.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({},u),[{name:"rect",index:o,animationCurve:i,animationFrame:a,shape:u,style:f}]}function s(e,t){return"number"==typeof e?e:"string"!=typeof e?0:t*parseInt(e)/100}return Qi}var Yi,Xi={};function Ji(){if(Yi)return Xi;Yi=1;var e=ot;Object.defineProperty(Xi,"__esModule",{value:!0}),Xi.axis=function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=r.xAxis,p=r.yAxis,y=r.series,b=[];s&&p&&y&&(b=function(e,t){var r=t.gridArea,a=r.w,o=r.h;return e.map((function(e){var t=e.tickLinePosition,r=e.position,l=e.boundaryGap,u=0,s=a;("top"===r||"bottom"===r)&&(u=1),("top"===r||"bottom"===r)&&(s=o),("right"===r||"bottom"===r)&&(s*=-1);var c=t.map((function(e){var t=(0,n.default)(e,1)[0],r=(0,i.default)(t);return r[u]+=s,[(0,i.default)(t),r]}));return l||c.shift(),f(f({},e),{},{splitLinePosition:c})}))}(b=function(e,t){return e.map((function(e){var t=e.nameGap,r=e.nameLocation,a=e.position,o=e.linePosition,l=(0,n.default)(o,2),u=l[0],s=l[1],c=(0,i.default)(u);"end"===r&&(c=(0,i.default)(s)),"center"===r&&(c[0]=(u[0]+s[0])/2,c[1]=(u[1]+s[1])/2);var d=0;"top"===a&&"center"===r&&(d=1),"bottom"===a&&"center"===r&&(d=1),"left"===a&&"center"!==r&&(d=1),"right"===a&&"center"!==r&&(d=1);var p=t;return"top"===a&&"end"!==r&&(p*=-1),"left"===a&&"start"!==r&&(p*=-1),"bottom"===a&&"start"===r&&(p*=-1),"right"===a&&"end"===r&&(p*=-1),c[d]+=p,f(f({},e),{},{namePosition:c})}))}(b=function(e,t){return e.map((function(e){var t=e.axis,r=e.linePosition,i=e.position,a=e.label,o=e.boundaryGap;"boolean"!=typeof o&&(o=c[t+"AxisConfig"].boundaryGap);var l=a.length,s=(0,n.default)(r,2),d=(0,n.default)(s[0],2),p=d[0],h=d[1],g=(0,n.default)(s[1],2),v=g[0],m=g[1],y=("x"===t?v-p:m-h)/(o?l:l-1),b=new Array(l).fill(0).map((function(e,n){return"x"===t?[p+y*(o?n+.5:n),h]:[p,h+y*(o?n+.5:n)]})),x=function(e,t,r,i,a){var o="x"===e?1:0,l=5;"x"===e&&"top"===r&&(l=-5),"y"===e&&"left"===r&&(l=-5);var s=i.map((function(e){var t=(0,u.deepClone)(e);return t[o]+=l,[(0,u.deepClone)(e),t]}));return t&&(o="x"===e?0:1,l=a/2,s.forEach((function(e){var t=(0,n.default)(e,2),r=t[0],i=t[1];r[o]+=l,i[o]+=l}))),s}(t,o,i,b,y);return f(f({},e),{},{tickPosition:b,tickLinePosition:x,tickGap:y})}))}(b=function(e,t){var n=t.gridArea,r=n.x,i=n.y,a=n.w,o=n.h;return e=e.map((function(e){var t=e.position,n=[];return"left"===t?n=[[r,i],[r,i+o]].reverse():"right"===t?n=[[r+a,i],[r+a,i+o]].reverse():"top"===t?n=[[r,i],[r+a,i]]:"bottom"===t&&(n=[[r,i+o],[r+a,i+o]]),f(f({},e),{},{linePosition:n})})),e}(b=function(e){var t=e.filter((function(e){return"x"===e.axis})),n=e.filter((function(e){return"y"===e.axis}));return t[0]&&!t[0].position&&(t[0].position=o.xAxisConfig.position),t[1]&&!t[1].position&&(t[1].position="bottom"===t[0].position?"top":"bottom"),n[0]&&!n[0].position&&(n[0].position=o.yAxisConfig.position),n[1]&&!n[1].position&&(n[1].position="left"===n[0].position?"right":"left"),[].concat((0,i.default)(t),(0,i.default)(n))}(b=function(e,r){var a=e.filter((function(e){return"value"===e.data})),o=e.filter((function(e){return e.data instanceof Array}));return a=function(e,r){return e.map((function(e){var a=function(e,t){if(t=t.filter((function(e){var t=e.show,n=e.type;return!(!1===t||"pie"===n)})),0===t.length)return[0,0];var n=e.index,r=e.axis;t=function(e){var t=(0,u.deepClone)(e,!0);return e.forEach((function(n,r){var i=(0,l.mergeSameStackData)(n,e);t[r].data=i})),t}(t);var a=r+"Axis",o=t.filter((function(e){return e[a]===n}));return o.length||(o=t),function(e){if(e){var t=Math.min.apply(Math,(0,i.default)(e.map((function(e){var t=e.data;return Math.min.apply(Math,(0,i.default)((0,l.filterNonNumber)(t)))})))),n=Math.max.apply(Math,(0,i.default)(e.map((function(e){var t=e.data;return Math.max.apply(Math,(0,i.default)((0,l.filterNonNumber)(t)))}))));return[t,n]}}(o)}(e,r),o=function(e,r){var i=e.min,a=e.max,o=e.axis,l=(0,n.default)(r,2),u=l[0],s=l[1],f=(0,t.default)(i),p=(0,t.default)(a);if(g(i)||(i=c[o+"AxisConfig"].min,f="string"),g(a)||(a=c[o+"AxisConfig"].max,p="string"),"string"===f){var v=h(i=parseInt(u-d(u*parseFloat(i)/100)));i=parseFloat((i/v-.1).toFixed(1))*v}if("string"===p){var m=h(a=parseInt(s+d(s*parseFloat(a)/100)));a=parseFloat((a/m+.1).toFixed(1))*m}return[i,a]}(e,a),s=(0,n.default)(o,2),p=s[0],m=s[1],y=function(e,t,n){var r=n.interval,i=n.minInterval,a=n.maxInterval,o=n.splitNumber,l=n.axis,u=c[l+"AxisConfig"];if("number"!=typeof r&&(r=u.interval),"number"!=typeof i&&(i=u.minInterval),"number"!=typeof a&&(a=u.maxInterval),"number"!=typeof o&&(o=u.splitNumber),"number"==typeof r)return r;var s=parseInt((t-e)/(o-1));return s.toString().length>1&&(s=parseInt(s.toString().replace(/\d$/,"0"))),0===s&&(s=1),"number"==typeof i&&s<i?i:"number"==typeof a&&s>a?a:s}(p,m,e),b=e.axisLabel.formatter,x=[];return x=p<0&&m>0?function(e,t,n){var r=[],a=[],o=0,l=0;do{r.push(o-=n)}while(o>e);do{a.push(l+=n)}while(l<t);return[].concat((0,i.default)(r.reverse()),[0],(0,i.default)(a))}(p,m,y):function(e,t,n){var r=[e],i=e;do{r.push(i+=n)}while(i<t);return r}(p,m,y),x=x.map((function(e){return parseFloat(e.toFixed(2))})),f(f({},e),{},{maxValue:x.slice(-1)[0],minValue:x[0],label:v(x,b)})}))}(a,r),o=function(e){return e.map((function(e){var t=e.data,n=e.axisLabel.formatter;return f(f({},e),{},{label:v(t,n)})}))}(o),[].concat((0,i.default)(a),(0,i.default)(o))}(b=function(e){var t=e.filter((function(e){return"value"===e.data})),n=e.filter((function(e){return"value"!==e.data}));return t.forEach((function(e){"boolean"!=typeof e.boundaryGap&&(e.boundaryGap=!1)})),n.forEach((function(e){"boolean"!=typeof e.boundaryGap&&(e.boundaryGap=!0)})),[].concat((0,i.default)(t),(0,i.default)(n))}(b=(b=function(e){var t=e.filter((function(e){return"x"===e.axis})),n=e.filter((function(e){return"y"===e.axis}));return t=t.map((function(e){return(0,l.deepMerge)((0,u.deepClone)(o.xAxisConfig),e)})),n=n.map((function(e){return(0,l.deepMerge)((0,u.deepClone)(o.yAxisConfig),e)})),[].concat((0,i.default)(t),(0,i.default)(n))}(b=function(e,t){var n,r,a=[],o=[];e instanceof Array?(n=a).push.apply(n,(0,i.default)(e)):a.push(e);t instanceof Array?(r=o).push.apply(r,(0,i.default)(t)):o.push(t);return a.splice(2),o.splice(2),a=a.map((function(e,t){return f(f({},e),{},{index:t,axis:"x"})})),o=o.map((function(e,t){return f(f({},e),{},{index:t,axis:"y"})})),[].concat((0,i.default)(a),(0,i.default)(o))}(s,p))).filter((function(e){return e.show}))),y)),e))),e)),(0,a.doUpdate)({chart:e,series:b,key:"axisLine",getGraphConfig:m}),(0,a.doUpdate)({chart:e,series:b,key:"axisTick",getGraphConfig:x}),(0,a.doUpdate)({chart:e,series:b,key:"axisLabel",getGraphConfig:C}),(0,a.doUpdate)({chart:e,series:b,key:"axisName",getGraphConfig:w}),(0,a.doUpdate)({chart:e,series:b,key:"splitLine",getGraphConfig:A}),e.axisData=b};var t=e(Jt()),n=e(Nt()),r=e(st()),i=e(kt()),a=Hi(),o=Wi(),l=Xr(),u=Kt();function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function f(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){(0,r.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var c={xAxisConfig:o.xAxisConfig,yAxisConfig:o.yAxisConfig},d=Math.abs,p=Math.pow;function h(e){var t=d(e).toString(),n=t.length,r=t.replace(/0*$/g,"").indexOf("0"),i=n-1;return-1!==r&&(i-=r),p(10,i)}function g(e){var n=(0,t.default)(e);return"string"===n&&/^\d+%$/.test(e)||"number"===n}function v(e,t){return t&&("string"==typeof t&&(e=e.map((function(e){return t.replace("{value}",e)}))),"function"==typeof t&&(e=e.map((function(e,n){return t({value:e,index:n})})))),e}function m(e){var t=e.animationCurve,n=e.animationFrame;return[{name:"polyline",index:e.rLevel,visible:e.axisLine.show,animationCurve:t,animationFrame:n,shape:y(e),style:b(e)}]}function y(e){return{points:e.linePosition}}function b(e){return e.axisLine.style}function x(e){var t=e.animationCurve,n=e.animationFrame,r=e.rLevel,i=function(e){var t=e.tickLinePosition;return t.map((function(e){return{points:e}}))}(e),a=function(e){return e.axisTick.style}(e);return i.map((function(i){return{name:"polyline",index:r,visible:e.axisTick.show,animationCurve:t,animationFrame:n,shape:i,style:a}}))}function C(e){var t=e.animationCurve,n=e.animationFrame,r=e.rLevel,i=function(e){var t=e.label,n=e.tickPosition,r=e.position;return n.map((function(e,n){return{position:$(e,r),content:t[n].toString()}}))}(e),a=function(e,t){var n=e.position,r=e.axisLabel.style,i=function(e){if("left"===e)return{textAlign:"right",textBaseline:"middle"};if("right"===e)return{textAlign:"left",textBaseline:"middle"};if("top"===e)return{textAlign:"center",textBaseline:"bottom"};if("bottom"===e)return{textAlign:"center",textBaseline:"top"}}(n);r=(0,l.deepMerge)(i,r);var a=t.map((function(e){var t=e.position;return f(f({},r),{},{graphCenter:t})}));return a}(e,i);return i.map((function(i,o){return{name:"text",index:r,visible:e.axisLabel.show,animationCurve:t,animationFrame:n,shape:i,style:a[o],setGraphCenter:function(){}}}))}function $(e,t){var n=0,r=10;return("top"===t||"bottom"===t)&&(n=1),("top"===t||"left"===t)&&(r=-10),(e=(0,u.deepClone)(e))[n]+=r,e}function w(e){var t=e.animationCurve,n=e.animationFrame;return[{name:"text",index:e.rLevel,animationCurve:t,animationFrame:n,shape:k(e),style:P(e)}]}function k(e){return{content:e.name,position:e.namePosition}}function P(e){var t=e.nameLocation,n=e.position,r=e.nameTextStyle,i=function(e,t){if("top"===e&&"start"===t||"bottom"===e&&"start"===t||"left"===e&&"center"===t)return{textAlign:"right",textBaseline:"middle"};if("top"===e&&"end"===t||"bottom"===e&&"end"===t||"right"===e&&"center"===t)return{textAlign:"left",textBaseline:"middle"};if("top"===e&&"center"===t||"left"===e&&"end"===t||"right"===e&&"end"===t)return{textAlign:"center",textBaseline:"bottom"};if("bottom"===e&&"center"===t||"left"===e&&"start"===t||"right"===e&&"start"===t)return{textAlign:"center",textBaseline:"top"}}(n,t);return(0,l.deepMerge)(i,r)}function A(e){var t=e.animationCurve,n=e.animationFrame,r=e.rLevel,i=function(e){var t=e.splitLinePosition;return t.map((function(e){return{points:e}}))}(e),a=function(e){return e.splitLine.style}(e);return i.map((function(i){return{name:"polyline",index:r,visible:e.splitLine.show,animationCurve:t,animationFrame:n,shape:i,style:a}}))}return Xi}var Ki,Zi={};function ea(){if(Ki)return Zi;Ki=1;var e=ot;Object.defineProperty(Zi,"__esModule",{value:!0}),Zi.line=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.xAxis,r=t.yAxis,i=t.series,l=[];n&&r&&i&&(l=function(e,t){var n=t.axisData;return e.map((function(t){var r=(0,u.mergeSameStackData)(t,e);r=function(e,t){var n=e.data;return t.map((function(e,t){return"number"==typeof n[t]?e:null}))}(t,r);var i=function(e,t){var n=e.xAxisIndex,r=e.yAxisIndex,i=t.find((function(e){var t=e.axis,r=e.index;return"x"===t&&r===n})),a=t.find((function(e){var t=e.axis,n=e.index;return"y"===t&&n===r}));return[i,a]}(t,n),a=function(e,t){var n=t.findIndex((function(e){return"value"===e.data})),r=t[n],i=t[1-n],a=r.linePosition,o=r.axis,l=i.tickPosition,u=l.length,s="x"===o?0:1,f=a[0][s],c=a[1][s],d=c-f,p=r.maxValue,h=r.minValue,g=p-h,v=new Array(u).fill(0).map((function(t,n){var r=e[n];if("number"!=typeof r)return null;var i=(r-h)/g;return 0===g&&(i=0),i*d+f}));return v.map((function(e,t){if(t>=u||"number"!=typeof e)return null;var n=[e,l[t][1-s]];return 0===s||n.reverse(),n}))}(r,i),o=function(e){var t=e.find((function(e){return"value"===e.data})),n=t.axis,r=t.linePosition,i=t.minValue,a=t.maxValue,o="x"===n?0:1,l=r[0][o];if(i<0&&a>0){var u=a-i,s=Math.abs(r[0][o]-r[1][o]),f=Math.abs(i)/u*s;"y"===n&&(f*=-1),l+=f}return{changeIndex:o,changeValue:l}}(i);return f(f({},t),{},{linePosition:a.filter((function(e){return e})),lineFillBottomPos:o})}))}(l=(0,u.initNeedSeries)(i,o.lineConfig,"line"),e)),(0,a.doUpdate)({chart:e,series:l,key:"lineArea",getGraphConfig:p,getStartGraphConfig:m,beforeUpdate:y,beforeChange:b}),(0,a.doUpdate)({chart:e,series:l,key:"line",getGraphConfig:x,getStartGraphConfig:w,beforeUpdate:y,beforeChange:b}),(0,a.doUpdate)({chart:e,series:l,key:"linePoint",getGraphConfig:k,getStartGraphConfig:P}),(0,a.doUpdate)({chart:e,series:l,key:"lineLabel",getGraphConfig:A})};var t=e(Jt()),n=e(Nt()),r=e(kt()),i=e(st()),a=Hi(),o=Wi(),l=e(Vt()),u=Xr();function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function f(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){(0,i.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var c=l.default.polylineToBezierCurve,d=l.default.getBezierCurveLength;function p(e){var t=e.animationCurve,n=e.animationFrame,r=e.lineFillBottomPos,i=e.rLevel;return[{name:C(e),index:i,animationCurve:t,animationFrame:n,visible:e.lineArea.show,lineFillBottomPos:r,shape:h(e),style:g(e),drawed:v}]}function h(e){return{points:e.linePosition}}function g(e){var t=e.lineArea,n=e.color,i=t.gradient,a=t.style,o=[a.fill||n],l=(0,u.deepMerge)(o,i);1===l.length&&l.push(l[0]);var s=function(e){var t=e.lineFillBottomPos,n=e.linePosition,i=t.changeIndex,a=t.changeValue,o=n.map((function(e){return e[i]})),l=Math.max.apply(Math,(0,r.default)(o)),u=Math.min.apply(Math,(0,r.default)(o)),s=l;return 1===i&&(s=u),1===i?[0,s,0,a]:[s,0,a,0]}(e);return a=f(f({},a),{},{stroke:"rgba(0, 0, 0, 0)"}),(0,u.deepMerge)({gradientColor:l,gradientParams:s,gradientType:"linear",gradientWith:"fill"},a)}function v(e,t){var n=e.lineFillBottomPos,i=e.shape,a=t.ctx,o=i.points,l=n.changeIndex,u=n.changeValue,s=(0,r.default)(o[o.length-1]),f=(0,r.default)(o[0]);s[l]=u,f[l]=u,a.lineTo.apply(a,(0,r.default)(s)),a.lineTo.apply(a,(0,r.default)(f)),a.closePath(),a.fill()}function m(e){var t=p(e)[0],n=f({},t.style);return n.opacity=0,t.style=n,[t]}function y(e,t,n,r){var i=e[n];if(i){var a=C(t),o=r.chart.render;a!==i[0].name&&(i.forEach((function(e){return o.delGraph(e)})),e[n]=null)}}function b(e,t){var n=t.shape.points,i=e.shape.points,a=i.length,o=n.length;if(o>a){var l=i.slice(-1)[0],u=new Array(o-a).fill(0).map((function(e){return(0,r.default)(l)}));i.push.apply(i,(0,r.default)(u))}else o<a&&i.splice(o)}function x(e){var t=e.animationCurve,n=e.animationFrame,r=e.rLevel;return[{name:C(e),index:r+1,animationCurve:t,animationFrame:n,shape:h(e),style:$(e)}]}function C(e){return e.smooth?"smoothline":"polyline"}function $(e){var t=e.lineStyle,n=e.color,r=e.smooth,i=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!t)return(0,u.getPolylineLength)(e);var n=c(e);return d(n)}(e.linePosition,r);return(0,u.deepMerge)({stroke:n,lineDash:[i,0]},t)}function w(e){var t=e.lineStyle.lineDash,n=x(e)[0],i=n.style.lineDash;return i=t?[0,0]:(0,r.default)(i).reverse(),n.style.lineDash=i,[n]}function k(e){var t=e.animationCurve,r=e.animationFrame,i=e.rLevel,a=function(e){var t=e.linePosition,r=e.linePoint.radius;return t.map((function(e){var t=(0,n.default)(e,2),i=t[0],a=t[1];return{r:r,rx:i,ry:a}}))}(e),o=function(e){var t=e.color,n=e.linePoint.style;return(0,u.deepMerge)({stroke:t},n)}(e);return a.map((function(n){return{name:"circle",index:i+2,visible:e.linePoint.show,animationCurve:t,animationFrame:r,shape:n,style:o}}))}function P(e){var t=k(e);return t.forEach((function(e){e.shape.r=.1})),t}function A(e){var i=e.animationCurve,a=e.animationFrame,o=e.rLevel,l=function(e){var i=function(e){var n=e.data,r=e.label.formatter;if(n=n.filter((function(e){return"number"==typeof e})).map((function(e){return e.toString()})),!r)return n;var i=(0,t.default)(r);return"string"===i?n.map((function(e){return r.replace("{value}",e)})):"function"===i?n.map((function(e,t){return r({value:e,index:t})})):n}(e),a=function(e){var t=e.linePosition,i=e.lineFillBottomPos,a=e.label,o=a.position,l=a.offset,u=i.changeIndex,s=i.changeValue;return t.map((function(e){if("bottom"===o&&((e=(0,r.default)(e))[u]=s),"center"===o){var t=(0,r.default)(e);t[u]=s,e=function(e,t){var r=(0,n.default)(e,2),i=r[0],a=r[1],o=(0,n.default)(t,2),l=o[0],u=o[1];return[(i+l)/2,(a+u)/2]}(e,t)}return function(e,t){var r=(0,n.default)(e,2),i=r[0],a=r[1],o=(0,n.default)(t,2),l=o[0],u=o[1];return[i+l,a+u]}(e,l)}))}(e);return i.map((function(e,t){return{content:e,position:a[t]}}))}(e),s=function(e){var t=e.color,n=e.label.style;return(0,u.deepMerge)({fill:t},n)}(e);return l.map((function(t,n){return{name:"text",index:o+3,visible:e.label.show,animationCurve:i,animationFrame:a,shape:t,style:s}}))}return Zi}var ta,na={};function ra(){if(ta)return na;ta=1;var e=ot;Object.defineProperty(na,"__esModule",{value:!0}),na.bar=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.xAxis,l=t.yAxis,s=t.series,h=[];n&&l&&s&&(h=function(e,t){return e=function(e){return e.map((function(t){var n=(0,u.mergeSameStackData)(t,e);n=function(e,t){var n=e.data;return t.map((function(e,t){return"number"==typeof n[t]?e:null})).filter((function(e){return null!==e}))}(t,n);var r=t.valueAxis,i=r.axis,a=r.minValue,o=r.maxValue,l=r.linePosition,s=c(a,o,a<0?0:a,l,i),d=n.map((function(e){return c(a,o,e,l,i)})).map((function(e){return[s,e]}));return f(f({},t),{},{barValueAxisPos:d})}))}(e),e=function(e){return e.map((function(e){var t=e.labelAxis,n=e.barAllWidthAndGap,r=e.barGap,i=e.barWidth,a=e.barIndex,o=t.tickGap,l=t.tickPosition,u="x"===t.axis?0:1,s=l.map((function(e,t){return l[t][u]-o/2+(o-n)/2+(a+.5)*i+a*r}));return f(f({},e),{},{barLabelAxisPos:s})}))}(e),e=function(e){return e.map((function(e){var t=e.barLabelAxisPos;return e.data.forEach((function(e,n){"number"!=typeof e&&(t[n]=null)})),f(f({},e),{},{barLabelAxisPos:t.filter((function(e){return null!==e}))})}))}(e),e=function(e){return e.forEach((function(e){var t=e.data,n=e.barLabelAxisPos,r=e.barValueAxisPos,i=t.filter((function(e){return"number"==typeof e})).length;n.length>i&&(n.splice(i),r.splice(i))})),e}(e),e}(h=function(e,t){var n=function(e){var t=e.map((function(e){var t=e.labelAxis;return t.axis+t.index}));return t=(0,i.default)(new Set(t)),t.map((function(t){return e.filter((function(e){var n=e.labelAxis;return n.axis+n.index===t}))}))}(e);return n.forEach((function(e){(function(e){var t=function(e){var t=[];return e.forEach((function(e){var n=e.stack;n&&t.push(n)})),(0,i.default)(new Set(t))}(e);t=t.map((function(e){return{stack:e,index:-1}}));var n=0;e.forEach((function(e){var r=e.stack;if(r){var i=t.find((function(e){return e.stack===r}));-1===i.index&&(i.index=n,n++),e.barIndex=i.index}else e.barIndex=n,n++}))})(e),function(e){var t=(0,i.default)(new Set(e.map((function(e){return e.barIndex})))).length;e.forEach((function(e){return e.barNum=t}))}(e),function(e){var t=e.slice(-1)[0],n=t.barCategoryGap,r=t.labelAxis.tickGap,i=0;i="number"==typeof n?n:(1-parseInt(n)/100)*r,e.forEach((function(e){return e.barCategoryWidth=i}))}(e),function(e){var t=e.slice(-1)[0],n=t.barCategoryWidth,i=t.barWidth,a=t.barGap,o=t.barNum,l=[];"number"==typeof i||"auto"!==i?l=function(e,t,n){var r=0,i=0;return r="number"==typeof t?t:parseInt(t)/100*e,i="number"==typeof n?n:parseInt(n)/100*r,[r,i]}(n,i,a):"auto"===i&&(l=function(e,t,n,r){var i=0,a=0,o=e/r;if("number"==typeof n)a=n,i=o-a;else{var l=10+parseInt(n)/10;0===l?a=-(i=2*o):a=o-(i=o/l*10)}return[i,a]}(n,0,a,o));var u=l,s=(0,r.default)(u,2),f=s[0],c=s[1];e.forEach((function(e){e.barWidth=f,e.barGap=c}))}(e),function(e){var t=e.slice(-1)[0],n=t.barGap,r=t.barWidth,i=t.barNum,a=(n+r)*i-n;e.forEach((function(e){return e.barAllWidthAndGap=a}))}(e)})),e}(h=function(e,t){var n=t.axisData;return e.forEach((function(e){var t=e.xAxisIndex,r=e.yAxisIndex;"number"!=typeof t&&(t=0),"number"!=typeof r&&(r=0);var i=[n.find((function(e){var n=e.axis,r=e.index;return"".concat(n).concat(r)==="x".concat(t)})),n.find((function(e){var t=e.axis,n=e.index;return"".concat(t).concat(n)==="y".concat(r)}))],a=i.findIndex((function(e){return"value"===e.data}));e.valueAxis=i[a],e.labelAxis=i[1-a]})),e}(h=(0,u.initNeedSeries)(s,o.barConfig,"bar"),e)))),(0,a.doUpdate)({chart:e,series:h.slice(-1),key:"backgroundBar",getGraphConfig:d}),h.reverse(),(0,a.doUpdate)({chart:e,series:h,key:"bar",getGraphConfig:p,getStartGraphConfig:m,beforeUpdate:y}),(0,a.doUpdate)({chart:e,series:h,key:"barLabel",getGraphConfig:b})};var t=e(Jt()),n=e(st()),r=e(Nt()),i=e(kt()),a=Hi(),o=Wi(),l=Kt(),u=Xr();function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach((function(t){(0,n.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function c(e,t,n,r,i){if("number"!=typeof n)return null;var a=t-e,o="x"===i?0:1,l=(n-e)/a;return 0===a&&(l=0),l*(r[1][o]-r[0][o])+r[0][o]}function d(e){var t=e.animationCurve,n=e.animationFrame,r=e.rLevel,i=function(e){var t=e.labelAxis,n=e.valueAxis,r=t.tickPosition,i=n.axis,a=n.linePosition,o=function(e){var t=e.barAllWidthAndGap,n=e.barCategoryWidth,r=e.backgroundBar,i=r.width;return"number"==typeof i?i:"auto"===i?t:parseInt(i)/100*n}(e),l=o/2,u="x"===i?0:1,s=r.map((function(e){return e[1-u]})),f=[a[0][u],a[1][u]],c=f[0],d=f[1];return s.map((function(e){return"x"===i?{x:c,y:e-l,w:d-c,h:o}:{x:e-l,y:d,w:o,h:c-d}}))}(e),a=function(e){return e.backgroundBar.style}(e);return i.map((function(i){return{name:"rect",index:r,visible:e.backgroundBar.show,animationCurve:t,animationFrame:n,shape:i,style:a}}))}function p(e){var t=e.barLabelAxisPos,n=e.animationCurve,r=e.animationFrame,i=e.rLevel,a=h(e);return t.map((function(t,o){return{name:a,index:i,animationCurve:n,animationFrame:r,shape:g(e,o),style:v(e,o)}}))}function h(e){var t=e.shapeType;return"leftEchelon"===t||"rightEchelon"===t?"polyline":"rect"}function g(e,t){var n=e.shapeType;return"leftEchelon"===n?function(e,t){var n=e.barValueAxisPos,i=e.barLabelAxisPos,a=e.barWidth,o=e.echelonOffset,l=(0,r.default)(n[t],2),u=l[0],s=l[1],f=i[t],c=a/2,d=e.valueAxis.axis,p=[];return"x"===d?(p[0]=[s,f-c],p[1]=[s,f+c],p[2]=[u,f+c],p[3]=[u+o,f-c],s-u<o&&p.splice(3,1)):(p[0]=[f-c,s],p[1]=[f+c,s],p[2]=[f+c,u],p[3]=[f-c,u-o],u-s<o&&p.splice(3,1)),{points:p,close:!0}}(e,t):"rightEchelon"===n?function(e,t){var n=e.barValueAxisPos,i=e.barLabelAxisPos,a=e.barWidth,o=e.echelonOffset,l=(0,r.default)(n[t],2),u=l[0],s=l[1],f=i[t],c=a/2,d=e.valueAxis.axis,p=[];return"x"===d?(p[0]=[s,f+c],p[1]=[s,f-c],p[2]=[u,f-c],p[3]=[u+o,f+c],s-u<o&&p.splice(2,1)):(p[0]=[f+c,s],p[1]=[f-c,s],p[2]=[f-c,u],p[3]=[f+c,u-o],u-s<o&&p.splice(2,1)),{points:p,close:!0}}(e,t):function(e,t){var n=e.barValueAxisPos,i=e.barLabelAxisPos,a=e.barWidth,o=(0,r.default)(n[t],2),l=o[0],u=o[1],s=i[t],f=e.valueAxis.axis,c={};return"x"===f?(c.x=l,c.y=s-a/2,c.w=u-l,c.h=a):(c.x=s-a/2,c.y=u,c.w=a,c.h=l-u),c}(e,t)}function v(e,t){var n=e.barStyle,i=e.gradient,a=e.color,o=e.independentColor,l=e.independentColors,s=[n.fill||a],f=(0,u.deepMerge)(s,i.color);if(o){var c=l[t%l.length];f=c instanceof Array?c:[c]}1===f.length&&f.push(f[0]);var d=function(e,t){var n=e.barValueAxisPos,i=e.barLabelAxisPos,a=e.data,o=e.valueAxis,l=o.linePosition,u=o.axis,s=(0,r.default)(n[t],2),f=s[0],c=s[1],d=i[t],p=a[t],h=(0,r.default)(l,2),g=h[0],v=h[1],m="x"===u?0:1,y=c;return e.gradient.local||(y=p<0?g[m]:v[m]),"y"===u?[d,y,d,f]:[y,d,f,d]}(e,t);return(0,u.deepMerge)({gradientColor:f,gradientParams:d,gradientType:"linear",gradientWith:"fill"},n)}function m(e){var t=p(e),n=e.shapeType;return t.forEach((function(t){var r=t.shape;r="leftEchelon"===n?function(e,t){var n=t.valueAxis.axis;e=(0,l.deepClone)(e);var r=e,i=r.points,a="x"===n?0:1,o=i[2][a];return i.forEach((function(e){return e[a]=o})),e}(r,e):"rightEchelon"===n?function(e,t){var n=t.valueAxis.axis;e=(0,l.deepClone)(e);var r=e,i=r.points,a="x"===n?0:1,o=i[2][a];return i.forEach((function(e){return e[a]=o})),e}(r,e):function(e,t){var n=t.valueAxis.axis,r=e.x,i=e.y,a=e.w,o=e.h;return"x"===n?a=0:(i+=o,o=0),{x:r,y:i,w:a,h:o}}(r,e),t.shape=r})),t}function y(e,t,n,r){var i=r.chart.render,a=h(t);e[n]&&e[n][0].name!==a&&(e[n].forEach((function(e){return i.delGraph(e)})),e[n]=null)}function b(e){var n=e.animationCurve,i=e.animationFrame,a=e.rLevel,o=function(e){var n=function(e){var n=e.data,r=e.label,i=r.formatter;if(n=n.filter((function(e){return"number"==typeof e})).map((function(e){return e.toString()})),!i)return n;var a=(0,t.default)(i);return"string"===a?n.map((function(e){return i.replace("{value}",e)})):"function"===a?n.map((function(e,t){return i({value:e,index:t})})):n}(e),i=function(e){var t=e.label,n=e.barValueAxisPos,i=e.barLabelAxisPos,a=t.position,o=t.offset,l=e.valueAxis.axis;return n.map((function(e,t){var n=(0,r.default)(e,2),u=n[0],s=n[1],f=i[t],c=[s,f];return"bottom"===a&&(c=[u,f]),"center"===a&&(c=[(u+s)/2,f]),"y"===l&&c.reverse(),function(e,t){var n=(0,r.default)(e,2),i=n[0],a=n[1],o=(0,r.default)(t,2),l=o[0],u=o[1];return[i+l,a+u]}(c,o)}))}(e);return i.map((function(e,t){return{position:e,content:n[t]}}))}(e),l=function(e){var t=e.color,n=e.label.style,r=e.gradient.color;return r.length&&(t=r[0]),n=(0,u.deepMerge)({fill:t},n),n}(e);return o.map((function(t){return{name:"text",index:a,visible:e.label.show,animationCurve:n,animationFrame:i,shape:t,style:l}}))}return na}var ia,aa={};function oa(){if(ia)return aa;ia=1;var e=ot;Object.defineProperty(aa,"__esModule",{value:!0}),aa.pie=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.series;n||(n=[]);var s=(0,u.initNeedSeries)(n,o.pieConfig,"pie");s=function(e){return e.forEach((function(e){var t=p(e),n=p(e,!1);t=h(t),n=h(n),g(t,e),g(n,e,!1)})),e}(s=function(e){return e.forEach((function(e){var t=e.data,n=e.center;t.forEach((function(e){var t=e.startAngle,r=e.endAngle,a=e.radius,o=(t+r)/2,u=l.getCircleRadianPoint.apply(void 0,(0,i.default)(n).concat([a[1],o]));e.edgeCenterPos=u}))})),e}(s=function(e){return e.forEach((function(e){e.data.forEach((function(t){t.insideLabelPos=function(e,t){var n=e.center,a=t.startAngle,o=t.endAngle,u=(0,r.default)(t.radius,2),s=u[0],f=u[1],c=(s+f)/2,d=(a+o)/2;return l.getCircleRadianPoint.apply(void 0,(0,i.default)(n).concat([c,d]))}(e,t)}))})),e}(s=function(e){return e.forEach((function(e){var t=e.startAngle,n=e.data;n.forEach((function(e,i){var a=function(e,t){var n=2*Math.PI,r=e.slice(0,t+1),i=(0,u.mulAdd)(r.map((function(e){return e.percent}))),a=e[t].percent,o=i-a;return[n*o/100,n*i/100]}(n,i),o=(0,r.default)(a,2),l=o[0],s=o[1];e.startAngle=t+l,e.endAngle=t+s}))})),e}(s=function(e){return e.forEach((function(e){var t=e.data,n=e.percentToFixed,r=function(e){return(0,u.mulAdd)(e.map((function(e){return e.value})))}(t);t.forEach((function(e){var t=e.value;e.percent=t/r*100,e.percentForLabel=c(t/r*100,n)}));var i=(0,u.mulAdd)(t.slice(0,-1).map((function(e){return e.percent})));t.slice(-1)[0].percent=100-i,t.slice(-1)[0].percentForLabel=c(100-i,n)})),e}(s=function(e,t){var n=e.filter((function(e){return e.roseType}));return n.forEach((function(e){var t=e.radius,n=e.data,r=e.roseSort,a=function(e){var t=e.radius,n=e.roseIncrement;if("number"==typeof n)return n;if("auto"===n){var r=e.data,a=r.reduce((function(e,t){var n=t.radius;return[].concat((0,i.default)(e),(0,i.default)(n))}),[]),o=Math.min.apply(Math,(0,i.default)(a));return.6*(Math.max.apply(Math,(0,i.default)(a))-o)/(r.length-1||1)}return parseInt(n)/100*t[1]}(e),o=(0,i.default)(n);n=function(e){return e.sort((function(e,t){var n=e.value,r=t.value;return n===r?0:n>r?-1:n<r?1:void 0}))}(n),n.forEach((function(e,n){e.radius[1]=t[1]-a*n})),r?n.reverse():e.data=o,e.roseIncrement=a})),e}(s=function(e,t){var n=Math.min.apply(Math,(0,i.default)(t.render.area))/2;return e.forEach((function(e){var t=e.radius,r=e.data;t=f(t,n),r.forEach((function(e){var r=e.radius;r||(r=t),r=f(r,n),e.radius=r})),e.radius=t})),e}(s=function(e,t){var n=t.render.area;return e.forEach((function(e){var t=e.center;t=t.map((function(e,t){return"number"==typeof e?e:parseInt(e)/100*n[t]})),e.center=t})),e}(s,e),e))))))),(0,a.doUpdate)({chart:e,series:s,key:"pie",getGraphConfig:v,getStartGraphConfig:m,beforeChange:y}),(0,a.doUpdate)({chart:e,series:s,key:"pieInsideLabel",getGraphConfig:C}),(0,a.doUpdate)({chart:e,series:s,key:"pieOutsideLabelLine",getGraphConfig:k,getStartGraphConfig:P}),(0,a.doUpdate)({chart:e,series:s,key:"pieOutsideLabel",getGraphConfig:L,getStartGraphConfig:I})};var t=e(st()),n=e(Jt()),r=e(Nt()),i=e(kt()),a=Hi(),o=Oi(),l=Kt(),u=Xr();function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function f(e,t){return e instanceof Array||(e=[0,e]),e=e.map((function(e){return"number"==typeof e?e:parseInt(e)/100*t}))}function c(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=e.toString().split("."),r=(n[1]||"0").slice(0,t);return n[1]=r,parseFloat(n.join("."))}function d(e){var t=e.outsideLabel.labelLineBendGap,n=function(e){var t=e.data,n=t.map((function(e){var t=(0,r.default)(e.radius,2);return t[0],t[1]}));return Math.max.apply(Math,(0,i.default)(n))}(e);return"number"!=typeof t&&(t=parseInt(t)/100*n),t+n}function p(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=e.data,r=e.center[0];return n.filter((function(e){var n=e.edgeCenterPos[0];return t?n<=r:n>r}))}function h(e){return e.sort((function(e,t){var n=(0,r.default)(e.edgeCenterPos,2);n[0];var i=n[1],a=(0,r.default)(t.edgeCenterPos,2);a[0];var o=a[1];return i>o?1:i<o?-1:i===o?0:void 0})),e}function g(e,t){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=t.center,a=t.outsideLabel,o=d(t);e.forEach((function(e){var t=e.edgeCenterPos,s=e.startAngle,f=e.endAngle,c=a.labelLineEndLength,d=(s+f)/2,p=l.getCircleRadianPoint.apply(void 0,(0,i.default)(r).concat([o,d])),h=(0,i.default)(p);h[0]+=c*(n?-1:1),e.labelLine=[t,p,h],e.labelLineLength=(0,u.getPolylineLength)(e.labelLine),e.align={textAlign:"left",textBaseline:"middle"},n&&(e.align.textAlign="right")}))}function v(e){var t=e.data,n=e.animationCurve,r=e.animationFrame,i=e.rLevel;return t.map((function(t,a){return{name:"pie",index:i,animationCurve:n,animationFrame:r,shape:b(e,a),style:x(e,a)}}))}function m(e){var t=e.animationDelayGap,n=e.startAnimationCurve,r=v(e);return r.forEach((function(e,r){e.animationCurve=n,e.animationDelay=r*t,e.shape.or=e.shape.ir})),r}function y(e){e.animationDelay=0}function b(e,t){var n=e.center,r=e.data[t],i=r.radius;return{startAngle:r.startAngle,endAngle:r.endAngle,ir:i[0],or:i[1],rx:n[0],ry:n[1]}}function x(e,t){var n=e.pieStyle,r=e.data[t].color;return(0,u.deepMerge)({fill:r},n)}function C(e){var t=e.animationCurve,n=e.animationFrame,r=e.data,i=e.rLevel;return r.map((function(r,a){return{name:"text",index:i,visible:e.insideLabel.show,animationCurve:t,animationFrame:n,shape:$(e,a),style:w(e)}}))}function $(e,t){var r=e.insideLabel,i=e.data,a=r.formatter,o=i[t],l=(0,n.default)(a),u="";return"string"===l&&(u=(u=(u=a.replace("{name}",o.name)).replace("{percent}",o.percentForLabel)).replace("{value}",o.value)),"function"===l&&(u=a(o)),{content:u,position:o.insideLabelPos}}function w(e,t){return e.insideLabel.style}function k(e){var t=e.animationCurve,n=e.animationFrame,r=e.data,i=e.rLevel;return r.map((function(r,a){return{name:"polyline",index:i,visible:e.outsideLabel.show,animationCurve:t,animationFrame:n,shape:A(e,a),style:O(e,a)}}))}function P(e){var t=e.data,n=k(e);return n.forEach((function(e,n){e.style.lineDash=[0,t[n].labelLineLength]})),n}function A(e,t){return{points:e.data[t].labelLine}}function O(e,t){var n=e.outsideLabel,r=e.data,i=n.labelLineStyle,a=r[t].color;return(0,u.deepMerge)({stroke:a,lineDash:[r[t].labelLineLength,0]},i)}function L(e){var t=e.animationCurve,n=e.animationFrame,r=e.data,i=e.rLevel;return r.map((function(r,a){return{name:"text",index:i,visible:e.outsideLabel.show,animationCurve:t,animationFrame:n,shape:S(e,a),style:M(e,a)}}))}function I(e){var t=e.data,n=L(e);return n.forEach((function(e,n){e.shape.position=t[n].labelLine[1]})),n}function S(e,t){var r=e.outsideLabel,i=e.data,a=r.formatter,o=i[t],l=o.labelLine,u=o.name,s=o.percentForLabel,f=o.value,c=(0,n.default)(a),d="";return"string"===c&&(d=(d=(d=a.replace("{name}",u)).replace("{percent}",s)).replace("{value}",f)),"function"===c&&(d=a(i[t])),{content:d,position:l[2]}}function M(e,n){var r=e.outsideLabel,i=e.data[n],a=i.color,o=i.align,l=r.style;return(0,u.deepMerge)(function(e){for(var n=1;n<arguments.length;n++){var r=null!=arguments[n]?arguments[n]:{};n%2?s(Object(r),!0).forEach((function(n){(0,t.default)(e,n,r[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({fill:a},o),l)}return aa}var la,ua={};var sa,fa={};function ca(){if(sa)return fa;sa=1;var e=ot;Object.defineProperty(fa,"__esModule",{value:!0}),fa.radar=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.series;n||(n=[]);var u=(0,s.initNeedSeries)(n,o.radarConfig,"radar");u=function(e,t){var n=t.radarAxis;if(!n)return[];var i=(0,r.default)(n.centerPos,2),a=i[0],o=i[1];return e.forEach((function(e){var t=e.labelPosition.map((function(e){var t=(0,r.default)(e,2),n=t[0],i=t[1];return{textAlign:n>a?"left":"right",textBaseline:i>o?"top":"bottom"}}));e.labelAlign=t})),e}(u=function(e,t){var n=t.radarAxis;if(!n)return[];var r=n.centerPos,a=n.axisLineAngles;return e.forEach((function(e){var t=e.dataRadius,n=e.label.labelGap;e.labelPosition=t.map((function(e,t){return l.getCircleRadianPoint.apply(void 0,(0,i.default)(r).concat([e+n,a[t]]))}))})),e}(u=function(e,t){var n=t.radarAxis;if(!n)return[];var r=n.indicator,a=n.axisLineAngles,o=n.radius,u=n.centerPos;return e.forEach((function(e){var t=e.data;e.dataRadius=[],e.radarPosition=r.map((function(n,r){var s=n.max,f=n.min,c=t[r];"number"!=typeof s&&(s=c),"number"!=typeof f&&(f=0),"number"!=typeof c&&(c=f);var d=(c-f)/(s-f)*o;return e.dataRadius[r]=d,l.getCircleRadianPoint.apply(void 0,(0,i.default)(u).concat([d,a[r]]))}))})),e}(u,e),e),e),(0,a.doUpdate)({chart:e,series:u,key:"radar",getGraphConfig:c,getStartGraphConfig:d,beforeChange:g}),(0,a.doUpdate)({chart:e,series:u,key:"radarPoint",getGraphConfig:v,getStartGraphConfig:m}),(0,a.doUpdate)({chart:e,series:u,key:"radarLabel",getGraphConfig:x})};var t=e(st()),n=e(Jt()),r=e(Nt()),i=e(kt()),a=Hi(),o=Wi(),l=Kt(),u=Lt,s=Xr();function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function c(e){var t=e.animationCurve,n=e.animationFrame;return[{name:"polyline",index:e.rLevel,animationCurve:t,animationFrame:n,shape:p(e),style:h(e)}]}function d(e,t){var n=t.chart.radarAxis.centerPos,r=c(e)[0],a=r.shape.points.length,o=new Array(a).fill(0).map((function(e){return(0,i.default)(n)}));return r.shape.points=o,[r]}function p(e){return{points:e.radarPosition,close:!0}}function h(e){var t=e.radarStyle,n=e.color,r=(0,u.getRgbaValue)(n);r[3]=.5;var i={stroke:n,fill:(0,u.getColorFromRgbValue)(r)};return(0,s.deepMerge)(i,t)}function g(e,t){var n=t.shape,r=e.shape.points,a=r.length,o=n.points.length;if(o>a){var l=r.slice(-1)[0],u=new Array(o-a).fill(0).map((function(e){return(0,i.default)(l)}));r.push.apply(r,(0,i.default)(u))}else o<a&&r.splice(o)}function v(e){var t=e.radarPosition,n=e.animationCurve,r=e.animationFrame,i=e.rLevel;return t.map((function(t,a){return{name:"circle",index:i,animationCurve:n,animationFrame:r,visible:e.point.show,shape:y(e,a),style:b(e)}}))}function m(e){var t=v(e);return t.forEach((function(e){return e.shape.r=.01})),t}function y(e,t){var n=e.radarPosition,r=e.point.radius,i=n[t];return{rx:i[0],ry:i[1],r:r}}function b(e,t){var n=e.point,r=e.color,i=n.style;return(0,s.deepMerge)({stroke:r},i)}function x(e){var t=e.labelPosition,n=e.animationCurve,r=e.animationFrame,i=e.rLevel;return t.map((function(t,a){return{name:"text",index:i,visible:e.label.show,animationCurve:n,animationFrame:r,shape:C(e,a),style:$(e,a)}}))}function C(e,t){var i=e.labelPosition,a=e.label,o=e.data,l=a.offset,u=a.formatter,s=function(e,t){var n=(0,r.default)(e,2),i=n[0],a=n[1],o=(0,r.default)(t,2),l=o[0],u=o[1];return[i+l,a+u]}(i[t],l),f=o[t]?o[t].toString():"0",c=(0,n.default)(u);return"string"===c&&(f=u.replace("{value}",f)),"function"===c&&(f=u(f)),{content:f,position:s}}function $(e,n){var r=e.label,i=e.color,a=e.labelAlign,o=r.style,l=function(e){for(var n=1;n<arguments.length;n++){var r=null!=arguments[n]?arguments[n]:{};n%2?f(Object(r),!0).forEach((function(n){(0,t.default)(e,n,r[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({fill:i},a[n]);return(0,s.deepMerge)(l,o)}return fa}var da,pa={};function ha(){if(da)return pa;da=1;var e=ot;Object.defineProperty(pa,"__esModule",{value:!0}),pa.gauge=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=t.series;s||(s=[]);var f=(0,u.initNeedSeries)(s,o.gaugeConfig,"gauge");f=function(e,t){return e.forEach((function(e){var t=e.data,r=e.details.formatter,i=(0,n.default)(r),a=t.map((function(e){var t=e.value;return"string"===i&&(t=(t=r.replace("{value}","{nt}")).replace("{name}",e.name)),"function"===i&&(t=r(e)),t.toString()}));e.detailsContent=a})),e}(f=function(e,t){return e.forEach((function(e){var t=e.data,n=e.details,a=e.center,o=n.position,u=n.offset,s=t.map((function(e){var t=e.startAngle,n=e.endAngle,s=e.radius,f=null;return"center"===o?f=a:"start"===o?f=l.getCircleRadianPoint.apply(void 0,(0,i.default)(a).concat([s,t])):"end"===o&&(f=l.getCircleRadianPoint.apply(void 0,(0,i.default)(a).concat([s,n]))),function(e,t){var n=(0,r.default)(e,2),i=n[0],a=n[1],o=(0,r.default)(t,2),l=o[0],u=o[1];return[i+l,a+u]}(f,u)}));e.detailsPosition=s})),e}(f=function(e,t){return e.forEach((function(e){var t=e.axisLabel,r=e.min,i=e.max,a=e.splitNum,o=t.data,l=t.formatter,s=(i-r)/(a-1),f=new Array(a).fill(0).map((function(e,t){return parseInt(r+s*t)})),c=(0,n.default)(l);o=(0,u.deepMerge)(f,o).map((function(e,t){var n=e;return"string"===c&&(n=l.replace("{value}",e)),"function"===c&&(n=l({value:e,index:t})),n})),t.data=o})),e}(f=function(e,t){return e.forEach((function(e){var t=e.center,n=e.tickInnerRadius,a=e.tickAngles,o=e.axisLabel.labelGap,u=a.map((function(e,r){return l.getCircleRadianPoint.apply(void 0,(0,i.default)(t).concat([n[r]-o,a[r]]))})),s=u.map((function(e){var n=(0,r.default)(e,2),i=n[0],a=n[1];return{textAlign:i>t[0]?"right":"left",textBaseline:a>t[1]?"bottom":"top"}}));e.labelPosition=u,e.labelAlign=s})),e}(f=function(e,t){return e.forEach((function(e){var t=e.startAngle,n=e.endAngle,r=e.splitNum,a=e.center,o=e.radius,u=e.arcLineWidth,s=e.axisTick,f=s.tickLength,c=s.style.lineWidth,d=n-t,p=o-u/2,h=p-f,g=d/(r-1),v=2*Math.PI*o*d/(2*Math.PI),m=Math.ceil(c/2)/v*d;e.tickAngles=[],e.tickInnerRadius=[],e.tickPosition=new Array(r).fill(0).map((function(n,o){var u=t+g*o;return 0===o&&(u+=m),o===r-1&&(u-=m),e.tickAngles[o]=u,e.tickInnerRadius[o]=h,[l.getCircleRadianPoint.apply(void 0,(0,i.default)(a).concat([p,u])),l.getCircleRadianPoint.apply(void 0,(0,i.default)(a).concat([h,u]))]}))})),e}(f=function(e,t){return e.forEach((function(e){e.data.forEach((function(e){var t=e.color,n=e.gradient;(!n||!n.length)&&(n=t),n instanceof Array||(n=[n]),e.gradient=n}))})),e}(f=function(e,t){return e.forEach((function(e){var t=e.startAngle,n=e.endAngle,r=e.data,i=e.min,a=e.max,o=n-t,l=a-i;r.forEach((function(e){var n=e.value,r=Math.abs((n-i)/l*o);e.startAngle=t,e.endAngle=t+r}))})),e}(f=function(e,t){var n=t.render.area,r=Math.min.apply(Math,(0,i.default)(n))/2;return e.forEach((function(e){var t=e.radius,n=e.data,i=e.arcLineWidth;n.forEach((function(e){var n=e.radius,a=e.lineWidth;n||(n=t),"number"!=typeof n&&(n=parseInt(n)/100*r),e.radius=n,a||(a=i),e.lineWidth=a}))})),e}(f=function(e,t){var n=t.render.area,r=Math.min.apply(Math,(0,i.default)(n))/2;return e.forEach((function(e){var t=e.radius;"number"!=typeof t&&(t=parseInt(t)/100*r),e.radius=t})),e}(f=function(e,t){var n=t.render.area;return e.forEach((function(e){var t=e.center;t=t.map((function(e,t){return"number"==typeof e?e:parseInt(e)/100*n[t]})),e.center=t})),e}(f,e),e),e)))))))),(0,a.doUpdate)({chart:e,series:f,key:"gaugeAxisTick",getGraphConfig:d}),(0,a.doUpdate)({chart:e,series:f,key:"gaugeAxisLabel",getGraphConfig:g}),(0,a.doUpdate)({chart:e,series:f,key:"gaugeBackgroundArc",getGraphConfig:y,getStartGraphConfig:C}),(0,a.doUpdate)({chart:e,series:f,key:"gaugeArc",getGraphConfig:$,getStartGraphConfig:P,beforeChange:A}),(0,a.doUpdate)({chart:e,series:f,key:"gaugePointer",getGraphConfig:O,getStartGraphConfig:M}),(0,a.doUpdate)({chart:e,series:f,key:"gaugeDetails",getGraphConfig:j})};var t=e(st()),n=e(Jt()),r=e(Nt()),i=e(kt()),a=Hi(),o=_i(),l=Kt(),u=Xr(),s=Lt;function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function c(e){for(var n=1;n<arguments.length;n++){var r=null!=arguments[n]?arguments[n]:{};n%2?f(Object(r),!0).forEach((function(n){(0,t.default)(e,n,r[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function d(e){var t=e.tickPosition,n=e.animationCurve,r=e.animationFrame,i=e.rLevel;return t.map((function(t,a){return{name:"polyline",index:i,visible:e.axisTick.show,animationCurve:n,animationFrame:r,shape:p(e,a),style:h(e)}}))}function p(e,t){return{points:e.tickPosition[t]}}function h(e,t){return e.axisTick.style}function g(e){var t=e.labelPosition,n=e.animationCurve,r=e.animationFrame,i=e.rLevel;return t.map((function(t,a){return{name:"text",index:i,visible:e.axisLabel.show,animationCurve:n,animationFrame:r,shape:v(e,a),style:m(e,a)}}))}function v(e,t){var n=e.labelPosition;return{content:e.axisLabel.data[t].toString(),position:n[t]}}function m(e,t){var n=e.labelAlign,r=e.axisLabel.style;return(0,u.deepMerge)(c({},n[t]),r)}function y(e){var t=e.animationCurve,n=e.animationFrame;return[{name:"arc",index:e.rLevel,visible:e.backgroundArc.show,animationCurve:t,animationFrame:n,shape:b(e),style:x(e)}]}function b(e){var t=e.startAngle,n=e.endAngle,r=e.center,i=e.radius;return{rx:r[0],ry:r[1],r:i,startAngle:t,endAngle:n}}function x(e){var t=e.backgroundArc,n=e.arcLineWidth,r=t.style;return(0,u.deepMerge)({lineWidth:n},r)}function C(e){var t=y(e)[0],n=c({},t.shape);return n.endAngle=t.shape.startAngle,t.shape=n,[t]}function $(e){var t=e.data,n=e.animationCurve,r=e.animationFrame,i=e.rLevel;return t.map((function(t,a){return{name:"agArc",index:i,animationCurve:n,animationFrame:r,shape:w(e,a),style:k(e,a)}}))}function w(e,t){var n=e.data,r=e.center,i=e.endAngle,a=n[t],o=a.radius,l=a.startAngle,u=a.endAngle;return a.localGradient&&(i=u),{rx:r[0],ry:r[1],r:o,startAngle:l,endAngle:u,gradientEndAngle:i}}function k(e,t){var n=e.data,r=e.dataItemStyle,i=n[t],a=i.lineWidth,o=i.gradient;return o=o.map((function(e){return(0,s.getRgbaValue)(e)})),(0,u.deepMerge)({lineWidth:a,gradient:o},r)}function P(e){var t=$(e);return t.map((function(e){var t=c({},e.shape);t.endAngle=e.shape.startAngle,e.shape=t})),t}function A(e,t){var n=e.style.gradient,r=n.length,a=t.style.gradient.length;if(r>a)n.splice(a);else{var o=n.slice(-1)[0];n.push.apply(n,(0,i.default)(new Array(a-r).fill(0).map((function(e){return(0,i.default)(o)}))))}}function O(e){var t=e.animationCurve,n=e.animationFrame,r=e.center;return[{name:"polyline",index:e.rLevel,visible:e.pointer.show,animationCurve:t,animationFrame:n,shape:L(e),style:I(e),setGraphCenter:function(e,t){t.style.graphCenter=r}}]}function L(e){return{points:S(e.center),close:!0}}function I(e){var t=e.startAngle,n=e.endAngle,r=e.min,i=e.max,a=e.data,o=e.pointer,l=e.center,s=o.valueIndex,f=o.style,c=((a[s]?a[s].value:0)-r)/(i-r)*(n-t)+t+Math.PI/2;return(0,u.deepMerge)({rotate:(0,u.radianToAngle)(c),scale:[1,1],graphCenter:l},f)}function S(e){var t=(0,r.default)(e,2),n=t[0],i=t[1];return[[n,i-40],[n+5,i],[n,i+10],[n-5,i]]}function M(e){var t=e.startAngle,n=O(e)[0];return n.style.rotate=(0,u.radianToAngle)(t+Math.PI/2),[n]}function j(e){var t=e.detailsPosition,n=e.animationCurve,r=e.animationFrame,i=e.rLevel,a=e.details.show;return t.map((function(t,o){return{name:"numberText",index:i,visible:a,animationCurve:n,animationFrame:r,shape:F(e,o),style:_(e,o)}}))}function F(e,t){var n=e.detailsPosition,r=e.detailsContent,i=e.data,a=e.details,o=n[t],l=r[t];return{number:[i[t].value],content:l,position:o,toFixed:a.valueToFixed}}function _(e,t){var n=e.details,r=e.data,i=n.style,a=r[t].color;return(0,u.deepMerge)({fill:a},i)}return pa}var ga,va,ma,ya={};function ba(){if(ga)return ya;ga=1;var e=ot;Object.defineProperty(ya,"__esModule",{value:!0}),ya.legend=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.legend;n=n?[n=c(n=f(n=s(n=u(n=(0,l.deepMerge)((0,a.deepClone)(o.legendConfig,!0),n)),t,e),e),e)]:[],(0,i.doUpdate)({chart:e,series:n,key:"legendIcon",getGraphConfig:h}),(0,i.doUpdate)({chart:e,series:n,key:"legendText",getGraphConfig:m})};var t=e(st()),n=e(Nt()),r=e(Jt()),i=Hi(),a=Kt(),o=Wi(),l=Xr();function u(e){var t=e.data;return e.data=t.map((function(e){var t=(0,r.default)(e);return"string"===t?{name:e}:"object"===t?e:{name:""}})),e}function s(e,t,n){var r=t.series,i=n.legendStatus,a=e.data.filter((function(e){var t=e.name,n=r.find((function(e){var n=e.name;return t===n}));return!!n&&(e.color||(e.color=n.color),e.icon||(e.icon=n.type),e)}));return(!i||i.length!==e.data.length)&&(i=new Array(e.data.length).fill(!0)),a.forEach((function(e,t){return e.status=i[t]})),e.data=a,n.legendStatus=i,e}function f(e,t){var n=t.render.ctx,r=e.data,i=e.textStyle,a=e.textUnselectedStyle;return r.forEach((function(e){var t=e.status,r=e.name;e.textWidth=function(e,t,n){return e.font=function(e){var t=e.fontFamily,n=e.fontSize;return"".concat(n,"px ").concat(t)}(n),e.measureText(t).width}(n,r,t?i:a)})),e}function c(e,t){return"vertical"===e.orient?function(e,t){var r=function(e,t){var n=e.left,r=e.right,i=t.render.area[0],a=[n,r].findIndex((function(e){return"auto"!==e}));if(-1===a)return[!0,i-10];var o=[n,r][a];return"number"!=typeof o&&(o=parseInt(o)/100*i),[!!a,o]}(e,t),i=(0,n.default)(r,2),a=i[0],o=i[1],l=function(e,t){var n=e.iconHeight,r=e.itemGap,i=e.data,a=e.top,o=e.bottom,l=t.render.area[1],u=i.length,s=u*n+(u-1)*r,f=[a,o].findIndex((function(e){return"auto"!==e}));if(-1===f)return(l-s)/2;var c=[a,o][f];return"number"!=typeof c&&(c=parseInt(c)/100*l),1===f&&(c=l-c-s),c}(e,t);!function(e,t){var n=e.data,r=e.iconWidth,i=e.iconHeight,a=e.itemGap,o=i/2;n.forEach((function(e,n){var l=e.textWidth,u=(i+a)*n+o,s=t?0-r:0,f=t?s-5-l:r+5;e.iconPosition=[s,u],e.textPosition=[f,u]}))}(e,a);var u={textAlign:"left",textBaseline:"middle"};e.data.forEach((function(e){var t=e.textPosition,n=e.iconPosition;e.textPosition=p(t,[o,l]),e.iconPosition=p(n,[o,l]),e.align=u}))}(e,t):function(e,t){var n=e.iconHeight,r=e.itemGap,i=function(e,t){var n=e.data,r=e.iconWidth,i=t.render.area[0],a=0,o=[[]];return n.forEach((function(t,n){var l=d(a,n,e);l+r+5+t.textWidth>=i&&(l=d(a=n,n,e),o.push([])),t.iconPosition=[l,0],t.textPosition=[l+r+5,0],o.slice(-1)[0].push(t)})),o}(e,t),a=i.map((function(n){return function(e,t,n){var r=t.left,i=t.right,a=t.iconWidth,o=t.itemGap,u=n.render.area[0],s=e.length,f=(0,l.mulAdd)(e.map((function(e){return e.textWidth})))+s*(5+a)+(s-1)*o,c=[r,i].findIndex((function(e){return"auto"!==e}));return-1===c?(u-f)/2:0===c?"number"==typeof r?r:parseInt(r)/100*u:("number"!=typeof i&&(i=parseInt(i)/100*u),u-(f+i))}(n,e,t)})),o=function(e,t){var n=e.top,r=e.bottom,i=e.iconHeight,a=t.render.area[1],o=[n,r].findIndex((function(e){return"auto"!==e})),l=i/2;if(-1===o){var u=t.gridArea;return u.y+u.h+45-l}return 0===o?"number"==typeof n?n-l:parseInt(n)/100*a-l:("number"!=typeof r&&(r=parseInt(r)/100*a),a-r-l)}(e,t),u={textAlign:"left",textBaseline:"middle"};i.forEach((function(e,t){return e.forEach((function(e){var i=e.iconPosition,l=e.textPosition,s=a[t],f=o+t*(r+n);e.iconPosition=p(i,[s,f]),e.textPosition=p(l,[s,f]),e.align=u}))}))}(e,t),e}function d(e,t,n){var r=n.data,i=n.iconWidth,a=n.itemGap,o=r.slice(e,t);return(0,l.mulAdd)(o.map((function(e){return e.textWidth})))+(t-e)*(a+5+i)}function p(e,t){var r=(0,n.default)(e,2),i=r[0],a=r[1],o=(0,n.default)(t,2);return[i+o[0],a+o[1]]}function h(e,n){var r=e.data,i=e.selectAble,a=e.animationCurve,o=e.animationFrame,l=e.rLevel;return r.map((function(r,u){return(0,t.default)({name:"line"===r.icon?"lineIcon":"rect",index:l,visible:e.show,hover:i,click:i,animationCurve:a,animationFrame:o,shape:g(e,u),style:v(e,u)},"click",C(e,u,n))}))}function g(e,t){var r=e.data,i=e.iconWidth,a=e.iconHeight,o=(0,n.default)(r[t].iconPosition,2);return{x:o[0],y:o[1]-a/2,w:i,h:a}}function v(e,t){var n=e.data,r=e.iconStyle,i=e.iconUnselectedStyle,a=n[t],o=a.status,u=a.color,s=o?r:i;return(0,l.deepMerge)({fill:u},s)}function m(e,t){var n=e.data,r=e.selectAble,i=e.animationCurve,a=e.animationFrame,o=e.rLevel;return n.map((function(n,l){return{name:"text",index:o,visible:e.show,hover:r,animationCurve:i,animationFrame:a,hoverRect:x(e,l),shape:y(e,l),style:b(e,l),click:C(e,l,t)}}))}function y(e,t){var n=e.data[t],r=n.textPosition;return{content:n.name,position:r}}function b(e,t){var n=e.textStyle,r=e.textUnselectedStyle,i=e.data[t],o=i.status,u=i.align,s=o?n:r;return(0,l.deepMerge)((0,a.deepClone)(s,!0),u)}function x(e,t){var r=e.textStyle,i=e.textUnselectedStyle,a=e.data[t],o=a.status,l=(0,n.default)(a.textPosition,2),u=l[0],s=l[1],f=a.textWidth,c=(o?r:i).fontSize;return[u,s-c/2,f,c]}function C(e,t,n){var r=e.data[t].name;return function(){var e=n.chart,i=e.legendStatus,a=e.option,o=!i[t];a.series.find((function(e){return e.name===r})).show=o,i[t]=o,n.chart.setOption(a)}}return ya}function xa(){return va||(va=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"mergeColor",{enumerable:!0,get:function(){return t.mergeColor}}),Object.defineProperty(e,"title",{enumerable:!0,get:function(){return n.title}}),Object.defineProperty(e,"grid",{enumerable:!0,get:function(){return r.grid}}),Object.defineProperty(e,"axis",{enumerable:!0,get:function(){return i.axis}}),Object.defineProperty(e,"line",{enumerable:!0,get:function(){return a.line}}),Object.defineProperty(e,"bar",{enumerable:!0,get:function(){return o.bar}}),Object.defineProperty(e,"pie",{enumerable:!0,get:function(){return l.pie}}),Object.defineProperty(e,"radarAxis",{enumerable:!0,get:function(){return u.radarAxis}}),Object.defineProperty(e,"radar",{enumerable:!0,get:function(){return s.radar}}),Object.defineProperty(e,"gauge",{enumerable:!0,get:function(){return f.gauge}}),Object.defineProperty(e,"legend",{enumerable:!0,get:function(){return c.legend}});var t=function(){if(Bi)return di;Bi=1,Object.defineProperty(di,"__esModule",{value:!0}),di.mergeColor=function(r){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=(0,t.deepClone)(e.colorConfig,!0),o=i.color,l=i.series;if(l||(l=[]),o||(o=[]),i.color=o=(0,n.deepMerge)(a,o),l.length){var u=o.length;l.forEach((function(e,t){e.color||(e.color=o[t%u])})),l.filter((function(e){return"pie"===e.type})).forEach((function(e){return e.data.forEach((function(e,t){return e.color=o[t%u]}))})),l.filter((function(e){return"gauge"===e.type})).forEach((function(e){return e.data.forEach((function(e,t){return e.color=o[t%u]}))})),l.filter((function(e){var t=e.type,n=e.independentColor;return"bar"===t&&n})).forEach((function(e){e.independentColors||(e.independentColors=o)}))}};var e=Wi(),t=Kt(),n=Xr();return di}(),n=Ui(),r=qi(),i=Ji(),a=ea(),o=ra(),l=oa(),u=function(){if(la)return ua;la=1;var e=ot;Object.defineProperty(ua,"__esModule",{value:!0}),ua.radarAxis=function(e){var t=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).radar,n=[];t&&(n=[n=g(n=h(n=p(n=d(n=c(n=f(t),e),e))))]);var r=n;n.length&&!n[0].show&&(r=[]),(0,i.doUpdate)({chart:e,series:r,key:"radarAxisSplitArea",getGraphConfig:v,beforeUpdate:b,beforeChange:x}),(0,i.doUpdate)({chart:e,series:r,key:"radarAxisSplitLine",getGraphConfig:C,beforeUpdate:k,beforeChange:P}),(0,i.doUpdate)({chart:e,series:r,key:"radarAxisLine",getGraphConfig:A}),(0,i.doUpdate)({chart:e,series:r,key:"radarAxisLable",getGraphConfig:I}),e.radarAxis=n[0]};var t=e(Nt()),n=e(st()),r=e(kt()),i=Hi(),a=Wi(),o=Kt(),l=Xr();function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach((function(t){(0,n.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function f(e){return(0,l.deepMerge)((0,o.deepClone)(a.radarAxisConfig),e)}function c(e,t){var n=t.render.area,r=e.center;return e.centerPos=r.map((function(e,t){return"number"==typeof e?e:parseInt(e)/100*n[t]})),e}function d(e,t){var n=t.render.area,i=e.splitNum,a=e.radius,o=Math.min.apply(Math,(0,r.default)(n))/2;"number"!=typeof a&&(a=parseInt(a)/100*o);var l=a/i;return e.ringRadius=new Array(i).fill(0).map((function(e,t){return l*(t+1)})),e.radius=a,e}function p(e){var t=e.indicator,n=e.centerPos,i=e.radius,a=e.startAngle,l=2*Math.PI,u=t.length,s=l/u,f=new Array(u).fill(0).map((function(e,t){return s*t+a}));return e.axisLineAngles=f,e.axisLinePosition=f.map((function(e){return o.getCircleRadianPoint.apply(void 0,(0,r.default)(n).concat([i,e]))})),e}function h(e){var t=e.ringRadius,n=t[0]/2;return e.areaRadius=t.map((function(e){return e-n})),e}function g(e){var t=e.axisLineAngles,n=e.centerPos,i=e.radius,a=e.axisLabel;return i+=a.labelGap,e.axisLabelPosition=t.map((function(e){return o.getCircleRadianPoint.apply(void 0,(0,r.default)(n).concat([i,e]))})),e}function v(e){var t=e.areaRadius,n=e.polygon,r=e.animationCurve,i=e.animationFrame,a=e.rLevel,o=n?"regPolygon":"ring";return t.map((function(t,n){return{name:o,index:a,visible:e.splitArea.show,animationCurve:r,animationFrame:i,shape:m(e,n),style:y(e,n)}}))}function m(e,t){var n=e.polygon,r=e.areaRadius,i=e.indicator,a=e.centerPos,o=i.length,l={rx:a[0],ry:a[1],r:r[t]};return n&&(l.side=o),l}function y(e,t){var n=e.splitArea,i=e.ringRadius,a=e.axisLineAngles,u=e.polygon,f=e.centerPos,c=n.color,d=n.style;d=s({fill:"rgba(0, 0, 0, 0)"},d);var p=i[0]-0;if(u){var h=o.getCircleRadianPoint.apply(void 0,(0,r.default)(f).concat([i[0],a[0]])),g=o.getCircleRadianPoint.apply(void 0,(0,r.default)(f).concat([i[0],a[1]]));p=(0,l.getPointToLineDistance)(f,h,g)}if(d=(0,l.deepMerge)((0,o.deepClone)(d,!0),{lineWidth:p}),!c.length)return d;var v=c.length;return(0,l.deepMerge)(d,{stroke:c[t%v]})}function b(e,t,n,r){var i=e[n];if(i){var a=r.chart.render;(t.polygon?"regPolygon":"ring")!==i[0].name&&(i.forEach((function(e){return a.delGraph(e)})),e[n]=null)}}function x(e,t){var n=t.shape.side;"number"==typeof n&&(e.shape.side=n)}function C(e){var t=e.ringRadius,n=e.polygon,r=e.animationCurve,i=e.animationFrame,a=e.rLevel,o=n?"regPolygon":"ring";return t.map((function(t,n){return{name:o,index:a,animationCurve:r,animationFrame:i,visible:e.splitLine.show,shape:$(e,n),style:w(e,n)}}))}function $(e,t){var n=e.ringRadius,r=e.centerPos,i=e.indicator,a=e.polygon,o={rx:r[0],ry:r[1],r:n[t]},l=i.length;return a&&(o.side=l),o}function w(e,t){var n=e.splitLine,r=n.color,i=n.style;if(i=s({fill:"rgba(0, 0, 0, 0)"},i),!r.length)return i;var a=r.length;return(0,l.deepMerge)(i,{stroke:r[t%a]})}function k(e,t,n,r){var i=e[n];if(i){var a=r.chart.render;(t.polygon?"regPolygon":"ring")!==i[0].name&&(i.forEach((function(e){return a.delGraph(e)})),e[n]=null)}}function P(e,t){var n=t.shape.side;"number"==typeof n&&(e.shape.side=n)}function A(e){var t=e.axisLinePosition,n=e.animationCurve,r=e.animationFrame,i=e.rLevel;return t.map((function(t,a){return{name:"polyline",index:i,visible:e.axisLine.show,animationCurve:n,animationFrame:r,shape:O(e,a),style:L(e,a)}}))}function O(e,t){return{points:[e.centerPos,e.axisLinePosition[t]]}}function L(e,t){var n=e.axisLine,r=n.color,i=n.style;if(!r.length)return i;var a=r.length;return(0,l.deepMerge)(i,{stroke:r[t%a]})}function I(e){var t=e.axisLabelPosition,n=e.animationCurve,r=e.animationFrame,i=e.rLevel;return t.map((function(t,a){return{name:"text",index:i,visible:e.axisLabel.show,animationCurve:n,animationFrame:r,shape:S(e,a),style:M(e,a)}}))}function S(e,t){var n=e.axisLabelPosition;return{content:e.indicator[t].name,position:n[t]}}function M(e,n){var r=e.axisLabel,i=(0,t.default)(e.centerPos,2),a=i[0],o=i[1],u=e.axisLabelPosition,s=r.color,f=r.style,c=(0,t.default)(u[n],2),d=c[0]>a?"left":"right",p=c[1]>o?"top":"bottom";if(f=(0,l.deepMerge)({textAlign:d,textBaseline:p},f),!s.length)return f;var h=s.length;return(0,l.deepMerge)(f,{fill:s[n%h]})}return ua}(),s=ca(),f=ha(),c=ba()}(ci)),ci}!function(e){var t=ot;Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"changeDefaultConfig",{enumerable:!0,get:function(){return r.changeDefaultConfig}}),e.default=void 0;var n=t((ma||(ma=1,function(e){var t=ot;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=t(Jt()),r=t(Ot()),i=t(rt),a=Kt(),o=xa(),l=function e(t){if((0,r.default)(this,e),!t)return!1;var n=t.clientWidth,a=t.clientHeight,o=document.createElement("canvas");o.setAttribute("width",n),o.setAttribute("height",a),t.appendChild(o);var l={container:t,canvas:o,render:new i.default(o),option:null};Object.assign(this,l)};e.default=l,l.prototype.setOption=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e||"object"!==(0,n.default)(e))return!1;t&&this.render.graphs.forEach((function(e){return e.animationEnd()}));var r=(0,a.deepClone)(e,!0);(0,o.mergeColor)(this,r),(0,o.grid)(this,r),(0,o.axis)(this,r),(0,o.radarAxis)(this,r),(0,o.title)(this,r),(0,o.bar)(this,r),(0,o.line)(this,r),(0,o.pie)(this,r),(0,o.radar)(this,r),(0,o.gauge)(this,r),(0,o.legend)(this,r),this.option=e,this.render.launchAnimation()},l.prototype.resize=function(){var e=this.container,t=this.canvas,n=this.render,r=this.option,i=e.clientWidth,a=e.clientHeight;t.setAttribute("width",i),t.setAttribute("height",a),n.area=[i,a],this.setOption(r)}}(qr)),qr)),r=Wi(),i=n.default;e.default=i}(Qr);const Ca=et(Qr),$a={__name:"index",props:{option:{type:Object,default:()=>({})}},setup(e){const t=e,n=f(null),r=f(null);let l=c({});return Be(r,(function(){l&&l.resize()}),(function(){l=new Ca(r.value),t.option&&l.setOption(t.option)})),d((()=>t.option),(()=>{l&&l.setOption(t.option,!0)}),{deep:!0}),(e,t)=>(i(),a("div",{ref_key:"chartsContainerRef",ref:n,class:"dv-charts-container"},[o("div",{ref_key:"chartRef",ref:r,class:"charts-canvas-container"},null,512)],512))}},wa={install(e){e.component("DvCharts",$a)}},ka={class:"dv-capsule-chart"},Pa={class:"label-column"},Aa=o("div",null," ",-1),Oa={class:"capsule-container"},La={key:0,class:"capsule-item-value"},Ia={class:"unit-label"},Sa={key:0,class:"unit-text"},Ma={__name:"index",props:{config:{type:Object,default:()=>({})}},setup(e){t((e=>({"10ea9b50":n(f),"41ac2896":n(s)})));const l=e,u=c({defaultConfig:{data:[],colors:["#37a2da","#32c5e9","#67e0e3","#9fe6b8","#ffdb5c","#ff9f7f","#fb7293"],unit:"",showValue:!1,textColor:"#fff",fontSize:12,labelNum:6},mergedConfig:null,capsuleLength:[],capsuleValue:[],labelData:[],labelDataLength:[]});d((()=>l.config),(()=>{y()}),{deep:!0});const s=r((()=>`${l.config.fontSize?l.config.fontSize:u.defaultConfig.fontSize}px`)),f=r((()=>l.config.textColor?l.config.textColor:u.defaultConfig.textColor));function y(){u.mergedConfig=Ge(Ee(u.defaultConfig,!0),l.config||{}),function(){const{data:e,labelNum:t}=u.mergedConfig;if(!e.length||0===e.length)return u.labelData=[],void(u.capsuleLength=[]);const n=e.map((({value:e})=>e)),r=Math.max(...n);u.capsuleValue=n,u.capsuleLength=n.map((e=>r?e/r:0));const i=r/5,a=Array.from(new Set(Array.from({length:t}).fill(0).map(((e,t)=>Math.ceil(t*i)))));u.labelData=a,u.labelDataLength=Array.from(a).map((e=>r?e/r:0))}()}return m((()=>{y()})),(e,t)=>(i(),a("div",ka,[n(u).mergedConfig?(i(),a(p,{key:0},[o("div",Pa,[(i(!0),a(p,null,h(n(u).mergedConfig.data,(e=>(i(),a("div",{key:e.name},g(e.name),1)))),128)),Aa]),o("div",Oa,[(i(!0),a(p,null,h(n(u).capsuleLength,((e,t)=>(i(),a("div",{key:t,class:"capsule-item"},[o("div",{class:"capsule-item-column",style:x(`width: ${100*e}%; background-color: ${n(u).mergedConfig.colors[t%n(u).mergedConfig.colors.length]};`)},[n(u).mergedConfig.showValue?(i(),a("div",La,g(n(u).capsuleValue[t]),1)):v("",!0)],4)])))),128)),o("div",Ia,[(i(!0),a(p,null,h(n(u).labelData,((e,t)=>(i(),a("div",{key:e+t},g(e),1)))),128))])]),n(u).mergedConfig.unit?(i(),a("div",Sa,g(n(u).mergedConfig.unit),1)):v("",!0)],64)):v("",!0)]))}},ja={install(e){e.component("DvCapsuleChart",Ma)}},Fa={class:"dv-digital-flop"},_a={__name:"index",props:{config:{type:Object,default:()=>{}}},setup(e){const t=e,n=f(null),r=c({renderer:null,defaultConfig:{number:[],content:"",toFixed:0,textAlign:"center",rowGap:0,style:{fontSize:30,fill:"#3de7c9"},formatter:void 0,animationCurve:"easeOutCubic",animationFrame:50},mergedConfig:null,graph:null});function l(){r.mergedConfig=Ge(Ee(r.defaultConfig,!0),t.config||{})}function u(){const{number:e,content:t,toFixed:n,textAlign:i,rowGap:a,formatter:o}=r.mergedConfig,[l,u]=r.renderer.area,s=[l/2,u/2];return"left"===i&&(s[0]=0),"right"===i&&(s[0]=l),{number:e,content:t,toFixed:n,position:s,rowGap:a,formatter:o}}function s(){const{style:e,textAlign:t}=r.mergedConfig;return Ge(e,{textAlign:t,textBaseline:"middle"})}return d((()=>t.config),(e=>{!function(){if(r.graph.animationEnd(),l(),!r.graph)return;const{animationCurve:e,animationFrame:t}=r.mergedConfig,n=u(),i=s();(function(e,t){const n=e.shape.number.length,r=t.number.length;n!==r&&(e.shape.number=t.number)})(r.graph,n),r.graph.animationCurve=e,r.graph.animationFrame=t,r.graph.animation("style",i,!0),r.graph.animation("shape",n)}()}),{deep:!0}),m((()=>{r.renderer=new Pn(n.value),l(),function(){const e=u(),t=s();r.graph=r.renderer.add({name:"numberText",animationCurve:r.mergedConfig.animationCurve,animationFrame:r.mergedConfig.animationFrame,shape:e,style:t})}()})),(e,t)=>(i(),a("div",Fa,[o("canvas",{ref_key:"digitalFlop",ref:n},null,512)]))}},Ga={class:"dv-active-ring-chart"},Ea={class:"active-ring-info"},Ba={key:0},Da={__name:"index",props:{config:{type:Object,default:()=>({})},isDigitalFlop:{type:Boolean,default:!0}},setup(e){t((e=>({"7d023495":n(C)})));const l=e,u=f(null),s=c({defaultConfig:{radius:"50%",activeRadius:"55%",data:[{name:"",value:0}],lineWidth:20,activeTimeGap:3e3,color:[],textColor:"#fff",digitalFlopStyle:{fontSize:25,fill:"#fff"},digitalFlopToFixed:0,numToFixed:0,digitalFlopUnit:"",animationCurve:"easeOutCubic",animationFrame:50,showOriginValue:!1},mergedConfig:null,chart:null,activeIndex:0,animationHandler:""}),p=r((()=>{if(!s.mergedConfig)return 0;const{data:e,showOriginValue:t}=s.mergedConfig,n=e.map((({value:e})=>e));let r;if(t)r=n[s.activeIndex];else{const e=n.reduce(((e,t)=>e+t),0);r=Number.parseFloat(n[s.activeIndex]/e*100)||0}return r})),h=r((()=>{if(!s.mergedConfig)return p.value.toFixed(s.defaultConfig.numToFixed);const{numToFixed:e,showOriginValue:t}=s.mergedConfig;return`${t?p.value.toFixed(e):`${p.value.toFixed(e)}%`}`})),v=r((()=>{if(!s.mergedConfig)return{};const{digitalFlopStyle:e,digitalFlopToFixed:t,showOriginValue:n,digitalFlopUnit:r}=s.mergedConfig;return{content:n?`{nt}${r}`:`{nt}${r||"%"}`,number:[p.value],style:e,toFixed:t}})),y=r((()=>s.mergedConfig?s.mergedConfig.data[s.activeIndex].name:"")),b=r((()=>s.mergedConfig?`font-size: ${s.mergedConfig.digitalFlopStyle.fontSize}px;`:"")),C=r((()=>l.config.textColor?l.config.textColor:s.defaultConfig.textColor));function k(){s.mergedConfig=Ge(Ee(s.defaultConfig,!0),l.config||{})}function P(){const e=A();s.chart.setOption(e,!0),L()}function A(){const e=O();return s.mergedConfig.data.forEach((t=>{t.radius=e})),{series:[{type:"pie",...s.mergedConfig,outsideLabel:{show:!1}}],color:s.mergedConfig.color}}function O(e=!1){const{radius:t,activeRadius:n,lineWidth:r}=s.mergedConfig,i=Math.min(...s.chart.render.area)/2,a=r/2;let o=e?n:t;"number"!=typeof o&&(o=Number.parseInt(o)/100*i);return[o-a,o+a]}function L(){const e=O(),t=O(!0),n=A(),{data:r}=n.series[0];r.forEach(((n,r)=>{r===s.activeIndex?n.radius=t:n.radius=e})),s.chart.setOption(n,!0);const{activeTimeGap:i}=n.series[0];s.animationHandler=setTimeout((e=>{s.activeIndex+=1,s.activeIndex>=r.length&&(s.activeIndex=0),L()}),i)}return d((()=>l.config),(()=>{clearTimeout(s.animationHandler),s.activeIndex=0,k(),P()}),{deep:!0}),m((()=>{s.chart=new Ca(u.value),k(),P()})),$((()=>{clearTimeout(s.animationHandler)})),(t,r)=>(i(),a("div",Ga,[o("div",{ref_key:"activeRingChart",ref:u,class:"active-ring-chart-container"},null,512),o("div",Ea,[e.isDigitalFlop?(i(),a("div",Ba,[w(_a,{config:n(v)},null,8,["config"])])):(i(),a("div",{key:1,class:"active-ring-name",style:x(n(b))},g(n(h)),5)),o("div",{class:"active-ring-name",style:x(n(b))},g(n(y)),5)])]))}},Wa={install(e){e.component("DvActiveRingChart",Da)}},Na={install(e){e.component("DvDigitalFlop",_a)}},Ta=e({__name:"index",setup(e){const t=f(null),r=c({allWidth:0,scale:0,datavRoot:"",ready:!1}),o=()=>{const e=document.body.clientWidth;t.value&&(t.value.style.transform=`scale(${e/r.allWidth})`)};return Be(t,(()=>{o()}),(()=>{(()=>{const{width:e,height:n}=screen;r.allWidth=e,t.value&&(t.value.style.width=`${e}px`,t.value.style.height=`${n}px`)})(),o(),r.ready=!0})),(e,o)=>(i(),a("div",{id:"dv-full-screen-container",ref_key:"fullScreenContainer",ref:t},[n(r).ready?s(e.$slots,"default",{key:0}):v("",!0)],512))}}),Ra={install(e){e.component("DvFullScreenContainer",Ta)}},za=["width","height"],Ha=["fill","x","y","width","height"],Ua=["values","begin"],Va=["fill","x","y","width","height"],Qa=["values"],qa=["values"],Ya=["values"],Xa=["values"],Ja=["fill","x","y","height"],Ka=o("animate",{attributeName:"width",values:"0;40;0",dur:"2s",repeatCount:"indefinite"},null,-1),Za=["values"],eo=e({__name:"index",props:{color:{type:Array,default:()=>[]}},setup(e){const t=e,r=f(null),l=c([200,50]),u=f(4),s=f(20),g=f(2.5),y=f(g.value/2),b=c(["#fff","#0de7c2"]),C=c({mergedColor:[],rects:[],points:[],svgScale:[1,1]}),{width:$,height:w}=Be(r,(()=>{k()}),(()=>{k()})),k=()=>{(()=>{const[e,t]=l,n=e/(s.value+1),r=t/(u.value+1),i=new Array(u.value).fill(0).map(((e,t)=>new Array(s.value).fill(0).map(((e,i)=>[n*(i+1),r*(t+1)]))));C.points=i.reduce(((e,t)=>[...e,...t]),[])})(),(()=>{const e=C.points[2*s.value-1],t=C.points[2*s.value-3];C.rects=[e,t]})(),(()=>{const[e,t]=l;C.svgScale=[$.value/e,w.value/t]})()},P=()=>{C.mergedColor=Ge(Ee(b,!0),t.color||[])};return d((()=>t.color),(()=>{P()})),m((()=>{P()})),(e,t)=>(i(),a("div",{ref_key:"dvDecoration1",ref:r,class:"dv-decoration-1"},[(i(),a("svg",{width:`${n(l)[0]}px`,height:`${n(l)[1]}px`,style:x(`transform:scale(${n(C).svgScale[0]}, ${n(C).svgScale[1]});`)},[(i(!0),a(p,null,h(n(C).points,(e=>(i(),a(p,{key:e},[Math.random()>.6?(i(),a("rect",{key:0,fill:n(C).mergedColor[0],x:e[0]-n(y),y:e[1]-n(y),width:n(g),height:n(g)},[Math.random()>.6?(i(),a("animate",{key:0,attributeName:"fill",values:`${n(C).mergedColor[0]};transparent`,dur:"1s",begin:2*Math.random(),repeatCount:"indefinite"},null,8,Ua)):v("",!0)],8,Ha)):v("",!0)],64)))),128)),n(C).rects[0]?(i(),a("rect",{key:0,fill:n(C).mergedColor[1],x:n(C).rects[0][0]-n(g),y:n(C).rects[0][1]-n(g),width:2*n(g),height:2*n(g)},[o("animate",{attributeName:"width",values:"0;"+2*n(g),dur:"2s",repeatCount:"indefinite"},null,8,Qa),o("animate",{attributeName:"height",values:"0;"+2*n(g),dur:"2s",repeatCount:"indefinite"},null,8,qa),o("animate",{attributeName:"x",values:`${n(C).rects[0][0]};${n(C).rects[0][0]-n(g)}`,dur:"2s",repeatCount:"indefinite"},null,8,Ya),o("animate",{attributeName:"y",values:`${n(C).rects[0][1]};${n(C).rects[0][1]-n(g)}`,dur:"2s",repeatCount:"indefinite"},null,8,Xa)],8,Va)):v("",!0),n(C).rects[1]?(i(),a("rect",{key:1,fill:n(C).mergedColor[1],x:n(C).rects[1][0]-40,y:n(C).rects[1][1]-n(g),width:40,height:2*n(g)},[Ka,o("animate",{attributeName:"x",values:`${n(C).rects[1][0]};${n(C).rects[1][0]-40};${n(C).rects[1][0]}`,dur:"2s",repeatCount:"indefinite"},null,8,Za)],8,Ja)):v("",!0)],12,za))],512))}}),to={install(e){e.component("DvDecoration1",eo)}},no=["width","height"],ro=["x","y","width","height","fill"],io=["attributeName","to","dur"],ao=["x","y","fill"],oo=["attributeName","to","dur"],lo=e({__name:"index",props:{color:{type:Array,default:()=>[]},reverse:{type:Boolean,default:!1},dur:{type:Number,default:6}},setup(e){const t=e,r=f(null),l=c({x:0,y:0,w:0,h:0,defaultColor:["#3faacb","#fff"],mergedColor:[]}),u=()=>{l.mergedColor=Ge(Ee(l.defaultColor,!0),t.color||[])},{width:s,height:p}=Be(r,(()=>{h()}),(()=>{h()})),h=()=>{t.reverse?(l.w=1,l.h=p.value,l.x=s.value/2,l.y=0):(l.w=s.value,l.h=1,l.x=0,l.y=p.value/2)};return d((()=>t.color),(()=>{u()})),d((()=>t.reverse),(()=>{h()})),m((()=>{u()})),(t,u)=>(i(),a("div",{ref_key:"decoration2",ref:r,class:"dv-decoration-2"},[(i(),a("svg",{width:`${n(s)}px`,height:`${n(p)}px`},[o("rect",{x:n(l).x,y:n(l).y,width:n(l).w,height:n(l).h,fill:n(l).mergedColor[0]},[o("animate",{attributeName:e.reverse?"height":"width",from:"0",to:e.reverse?n(p):n(s),dur:`${e.dur}s`,calcMode:"spline",keyTimes:"0;1",keySplines:".42,0,.58,1",repeatCount:"indefinite"},null,8,io)],8,ro),o("rect",{x:n(l).x,y:n(l).y,width:"1",height:"1",fill:n(l).mergedColor[1]},[o("animate",{attributeName:e.reverse?"y":"x",from:"0",to:e.reverse?n(p):n(s),dur:`${e.dur}s`,calcMode:"spline",keyTimes:"0;1",keySplines:"0.42,0,0.58,1",repeatCount:"indefinite"},null,8,oo)],8,ao)],8,no))],512))}}),uo={install(e){e.component("DvDecoration2",lo)}},so=["width","height"],fo=["fill","x","y"],co=["values","dur","begin"],po=e({__name:"index",props:{color:{type:Array,default:()=>[]}},setup(e){const t=e,r=f(null),o=c({svgWH:[300,35],svgScale:[1,1],rowNum:2,rowPoints:25,pointSideLength:7,halfPointSideLength:3.5,points:[],defaultColor:["#7acaec","transparent"],mergedColor:[]}),l=()=>{(()=>{const[e,t]=o.svgWH,n=e/(o.rowPoints+1),r=t/(o.rowNum+1),i=new Array(o.rowNum).fill(0).map(((e,t)=>new Array(o.rowPoints).fill(0).map(((e,i)=>[n*(i+1),r*(t+1)]))));o.points=i.reduce(((e,t)=>[...e,...t]),[])})(),g()},{width:u,height:s}=Be(r,(()=>{l()}),(()=>{l()})),g=()=>{const[e,t]=o.svgWH;o.svgScale=[u.value/e,s.value/t]},y=()=>{o.mergedColor=Ge(Ee(o.defaultColor,!0),t.color||[])};return d((()=>t.color),(()=>{y()})),m((()=>{y()})),(e,t)=>(i(),a("div",{ref_key:"decoration3",ref:r,class:"dv-decoration-3"},[(i(),a("svg",{width:`${n(o).svgWH[0]}px`,height:`${n(o).svgWH[1]}px`,style:x(`transform:scale(${n(o).svgScale[0]},${n(o).svgScale[1]});`)},[(i(!0),a(p,null,h(n(o).points,(e=>(i(),a("rect",{key:e,fill:n(o).mergedColor[0],x:e[0]-n(o).halfPointSideLength,y:e[1]-n(o).halfPointSideLength,width:7,height:7},[Math.random()>.6?(i(),a("animate",{key:0,attributeName:"fill",values:`${n(o).mergedColor.join(";")}`,dur:Math.random()+1+"s",begin:2*Math.random(),repeatCount:"indefinite"},null,8,co)):v("",!0)],8,fo)))),128))],12,so))],512))}}),ho={install(e){e.component("DvDecoration3",po)}},go=["width","height"],vo=["stroke","points"],mo=["stroke","points"],yo=e({__name:"index",props:{color:{type:Array,default:()=>[]},reverse:{type:Boolean,default:!1},dur:{type:Number,default:3}},setup(e){const t=e,r=f(null),l=c({defaultColor:["rgba(255, 255, 255, 0.3)","rgba(255, 255, 255, 0.3)"],mergedColor:[]}),u=()=>{l.mergedColor=Ge(Ee(l.defaultColor,!0),t.color||[])},{width:s,height:p}=Be(r);return d((()=>t.color),(()=>{u()})),m((()=>{u()})),(t,u)=>(i(),a("div",{ref_key:"decoration3",ref:r,class:"dv-decoration-4"},[o("div",{class:k("container "+(e.reverse?"reverse":"normal")),style:x(e.reverse?`width:${n(s)}px;height:5px;animation-duration:${e.dur}s`:`width:5px;height:${n(p)}px;animation-duration:${e.dur}s`)},[(i(),a("svg",{width:e.reverse?n(s):5,height:e.reverse?5:n(p)},[o("polyline",{stroke:n(l).mergedColor[0],points:e.reverse?`0, 2.5 ${n(s)}, 2.5`:`2.5, 0 2.5, ${n(p)}`},null,8,vo),o("polyline",{class:"bold-line",stroke:n(l).mergedColor[1],"stroke-width":"3","stroke-dasharray":"20, 80","stroke-dashoffset":"-30",points:e.reverse?`0, 2.5 ${n(s)}, 2.5`:`2.5, 0 2.5, ${n(p)}`},null,8,mo)],8,go))],6)],512))}}),bo={install(e){e.component("DvDecoration4",yo)}},xo=["width","height"],Co=["stroke","points"],$o=["from","to","dur"],wo=["stroke","points"],ko=["from","to","dur"],Po=e({__name:"index",props:{color:{type:Array,default:()=>[]},dur:{type:Number,default:1.2}},setup(e){const t=e,r=f(null),l=c({line1Points:"",line2Points:"",line1Length:0,line2Length:0,defaultColor:["#3f96a5","#3f96a5"],mergedColor:[]}),{width:u,height:s}=Be(r,(()=>{p()}),(()=>{p()})),p=()=>{const e=[{x:0,y:.2*s.value},{x:.18*u.value,y:.2*s.value},{x:.2*u.value,y:.4*s.value},{x:.25*u.value,y:.4*s.value},{x:.27*u.value,y:.6*s.value},{x:.72*u.value,y:.6*s.value},{x:.75*u.value,y:.4*s.value},{x:.8*u.value,y:.4*s.value},{x:.82*u.value,y:.2*s.value},{x:u.value,y:.2*s.value}],t=[{x:.3*u.value,y:.8*s.value},{x:.7*u.value,y:.8*s.value}],n=Me(e),r=Me(t);l.line1Points=Fe(e),l.line2Points=Fe(t),l.line1Length=n,l.line2Length=r},h=()=>{l.mergedColor=Ge(Ee(l.defaultColor,!0),t.color||[])};return d((()=>t.color),(()=>{h()})),m((()=>{h()})),(t,f)=>(i(),a("div",{ref_key:"decoration5",ref:r,class:"dv-decoration-5"},[(i(),a("svg",{width:n(u),height:n(s)},[o("polyline",{fill:"transparent",stroke:n(l).mergedColor[0],"stroke-width":"3",points:n(l).line1Points},[o("animate",{attributeName:"stroke-dasharray",attributeType:"XML",from:`0, ${n(l).line1Length/2}, 0, ${n(l).line1Length/2}`,to:`0, 0, ${n(l).line1Length}, 0`,dur:`${e.dur}s`,begin:"0s",calcMode:"spline",keyTimes:"0;1",keySplines:"0.4,1,0.49,0.98",repeatCount:"indefinite"},null,8,$o)],8,Co),o("polyline",{fill:"transparent",stroke:n(l).mergedColor[1],"stroke-width":"2",points:n(l).line2Points},[o("animate",{attributeName:"stroke-dasharray",attributeType:"XML",from:`0, ${n(l).line2Length/2}, 0, ${n(l).line2Length/2}`,to:`0, 0, ${n(l).line2Length}, 0`,dur:`${e.dur}s`,begin:"0s",calcMode:"spline",keyTimes:"0;1",keySplines:".4,1,.49,.98",repeatCount:"indefinite"},null,8,ko)],8,wo)],8,xo))],512))}}),Ao={install(e){e.component("DvDecoration5",Po)}},Oo=["width","height"],Lo=["fill","x","y","height"],Io=["values","dur"],So=["values","dur"],Mo=e({__name:"index",props:{color:{type:Array,default:()=>[]}},setup(e){const t=e,r=f(null),l=c({svgWH:[300,35],svgScale:[1,1],rowNum:1,rowPoints:40,rectWidth:7,halfRectWidth:3.5,points:[],heights:[],minHeights:[],randoms:[],defaultColor:["#7acaec","#7acaec"],mergedColor:[]});d((()=>t.color),(()=>{v()})),m((()=>{v()}));const{width:u,height:s}=Be(r,(function(){g()}),(function(){g()}));function g(){(function(){const[e,t]=l.svgWH,n=e/(l.rowPoints+1),r=t/(l.rowNum+1),i=new Array(l.rowNum).fill(0).map(((e,t)=>new Array(l.rowPoints).fill(0).map(((e,i)=>[n*(i+1),r*(t+1)]))));l.points=i.reduce(((e,t)=>[...e,...t]),[]);const a=l.heights=new Array(l.rowNum*l.rowPoints).fill(0).map((e=>Math.random()>.8?Le(.7*t,t):Le(.2*t,.5*t)));l.minHeights=new Array(l.rowNum*l.rowPoints).fill(0).map(((e,t)=>a[t]*Math.random())),l.randoms=new Array(l.rowNum*l.rowPoints).fill(0).map((e=>Math.random()+1.5))})(),function(){const[e,t]=l.svgWH;l.svgScale=[u.value/e,s.value/t]}()}function v(){l.mergedColor=Ge(Ee(l.defaultColor,!0),t.color||[])}return(e,t)=>(i(),a("div",{ref_key:"decoration6",ref:r,class:"dv-decoration-6"},[(i(),a("svg",{width:`${n(l).svgWH[0]}px`,height:`${n(l).svgWH[1]}px`,style:x(`transform:scale(${n(l).svgScale[0]},${n(l).svgScale[1]});`)},[(i(!0),a(p,null,h(n(l).points,((e,t)=>(i(),a("rect",{key:t,fill:n(l).mergedColor[Math.random()>.5?0:1],x:e[0]-n(l).halfRectWidth,y:e[1]-n(l).heights[t]/2,width:7,height:n(l).heights[t]},[o("animate",{attributeName:"y",values:`${e[1]-n(l).minHeights[t]/2};${e[1]-n(l).heights[t]/2};${e[1]-n(l).minHeights[t]/2}`,dur:`${n(l).randoms[t]}s`,keyTimes:"0;0.5;1",calcMode:"spline",keySplines:"0.42,0,0.58,1;0.42,0,0.58,1",begin:"0s",repeatCount:"indefinite"},null,8,Io),o("animate",{attributeName:"height",values:`${n(l).minHeights[t]};${n(l).heights[t]};${n(l).minHeights[t]}`,dur:`${n(l).randoms[t]}s`,keyTimes:"0;0.5;1",calcMode:"spline",keySplines:"0.42,0,0.58,1;0.42,0,0.58,1",begin:"0s",repeatCount:"indefinite"},null,8,So)],8,Lo)))),128))],12,Oo))],512))}}),jo={install(e){e.component("DvDecoration6",Mo)}},Fo={class:"dv-decoration-7"},_o={width:"21px",height:"20px"},Go=["stroke"],Eo=["stroke"],Bo={width:"21px",height:"20px"},Do=["stroke"],Wo=["stroke"],No=e({__name:"index",props:{color:{type:Array,default:()=>[]}},setup(e){const t=e,r=c({defaultColor:["#1dc1f5","#1dc1f5"],mergedColor:[]});function l(){r.mergedColor=Ge(Ee(r.defaultColor,!0),t.color||[])}return d((()=>t.color),(()=>{l()})),m((()=>{l()})),(e,t)=>(i(),a("div",Fo,[(i(),a("svg",_o,[o("polyline",{"stroke-width":"4",fill:"transparent",stroke:n(r).mergedColor[0],points:"10, 0 19, 10 10, 20"},null,8,Go),o("polyline",{"stroke-width":"2",fill:"transparent",stroke:n(r).mergedColor[1],points:"2, 0 11, 10 2, 20"},null,8,Eo)])),s(e.$slots,"default"),(i(),a("svg",Bo,[o("polyline",{"stroke-width":"4",fill:"transparent",stroke:n(r).mergedColor[0],points:"11, 0 2, 10 11, 20"},null,8,Do),o("polyline",{"stroke-width":"2",fill:"transparent",stroke:n(r).mergedColor[1],points:"19, 0 10, 10 19, 20"},null,8,Wo)]))]))}}),To={install(e){e.component("DvDecoration7",No)}},Ro=["width","height"],zo=["stroke","points"],Ho=["stroke","points"],Uo=["stroke","points"],Vo=e({__name:"index",props:{color:{type:Array,default:()=>[]},reverse:{type:Boolean,default:!1}},setup(e){const t=e,r=f(null),l=c({defaultColor:["#3f96a5","#3f96a5"],mergedColor:[]});d((()=>t.color),(()=>{h()})),m((()=>{h()}));const{width:u,height:s}=Be(r);function p(e){return t.reverse?u.value-e:e}function h(){l.mergedColor=Ge(Ee(l.defaultColor,!0),t.color||[])}return(e,t)=>(i(),a("div",{ref_key:"decoration8",ref:r,class:"dv-decoration-8"},[(i(),a("svg",{width:n(u),height:n(s)},[o("polyline",{stroke:n(l).mergedColor[0],"stroke-width":"2",fill:"transparent",points:`${p(0)}, 0 ${p(30)}, ${n(s)/2}`},null,8,zo),o("polyline",{stroke:n(l).mergedColor[0],"stroke-width":"2",fill:"transparent",points:`${p(20)}, 0 ${p(50)}, ${n(s)/2} ${p(n(u))}, ${n(s)/2}`},null,8,Ho),o("polyline",{stroke:n(l).mergedColor[1],fill:"transparent","stroke-width":"3",points:`${p(0)}, ${n(s)-3}, ${p(200)}, ${n(s)-3}`},null,8,Uo)],8,Ro))],512))}}),Qo={install(e){e.component("DvDecoration8",Vo)}},qo=["width","height"],Yo=["id"],Xo=["stroke"],Jo=["dur"],Ko=["stroke"],Zo=["dur"],el=["stroke"],tl=["xlink:href","stroke","fill"],nl=["dur","begin"],rl=["stroke"],il={__name:"index",props:{color:{type:Array,default:()=>[]},dur:{type:Number,default:3}},setup(e){const t=e,r=_e(),l=f(null),u=c({polygonId:`decoration-9-polygon-${r}`,svgWH:[100,100],svgScale:[1,1],defaultColor:["rgba(3, 166, 224, 0.8)","rgba(3, 166, 224, 0.5)"],mergedColor:[]});d((()=>t.color),(()=>{b()})),m((()=>{b()}));const{width:g,height:v}=Be(l,(function(){y()}),(function(){y()}));function y(){const[e,t]=u.svgWH;u.svgScale=[g.value/e,v.value/t]}function b(){u.mergedColor=Ge(Ee(u.defaultColor,!0),t.color||[])}return(t,r)=>(i(),a("div",{ref_key:"decoration9",ref:l,class:"dv-decoration-9"},[(i(),a("svg",{width:`${n(u).svgWH[0]}px`,height:`${n(u).svgWH[1]}px`,style:x(`transform:scale(${n(u).svgScale[0]},${n(u).svgScale[1]});`)},[o("defs",null,[o("polygon",{id:n(u).polygonId,points:"15, 46.5, 21, 47.5, 21, 52.5, 15, 53.5"},null,8,Yo)]),o("circle",{cx:"50",cy:"50",r:"45",fill:"transparent",stroke:n(u).mergedColor[1],"stroke-width":"10","stroke-dasharray":"80, 100, 30, 100"},[o("animateTransform",{attributeName:"transform",type:"rotate",values:"0 50 50;360 50 50",dur:`${e.dur}s`,repeatCount:"indefinite"},null,8,Jo)],8,Xo),o("circle",{cx:"50",cy:"50",r:"45",fill:"transparent",stroke:n(u).mergedColor[0],"stroke-width":"6","stroke-dasharray":"50, 66, 100, 66"},[o("animateTransform",{attributeName:"transform",type:"rotate",values:"0 50 50;-360 50 50",dur:`${e.dur}s`,repeatCount:"indefinite"},null,8,Zo)],8,Ko),o("circle",{cx:"50",cy:"50",r:"38",fill:"transparent",stroke:n(R)(n(u).mergedColor[1]||n(u).defaultColor[1],30),"stroke-width":"1","stroke-dasharray":"5, 1"},null,8,el),(i(!0),a(p,null,h(new Array(20).fill(0),((t,r)=>(i(),a("use",{key:r,"xlink:href":`#${n(u).polygonId}`,stroke:n(u).mergedColor[1],fill:Math.random()>.4?"transparent":n(u).mergedColor[0]},[o("animateTransform",{attributeName:"transform",type:"rotate",values:"0 50 50;360 50 50",dur:`${e.dur}s`,begin:r*e.dur/20+"s",repeatCount:"indefinite"},null,8,nl)],8,tl)))),128)),o("circle",{cx:"50",cy:"50",r:"26",fill:"transparent",stroke:n(R)(n(u).mergedColor[1]||n(u).defaultColor[1],30),"stroke-width":"1","stroke-dasharray":"5, 1"},null,8,rl)],12,qo)),s(t.$slots,"default")],512))}},al={install(e){e.component("DvDecoration9",il)}},ol=["width","height"],ll=["stroke","points"],ul=["stroke","points","stroke-dasharray"],sl=["id","values","begin"],fl=["values","begin"],cl=["stroke","points","stroke-dasharray"],dl=["id","values","begin"],pl=["values","begin"],hl=["stroke","points","stroke-dasharray"],gl=["id","values","begin"],vl=["values","begin"],ml=["cy","fill"],yl=["id","values","begin"],bl=["cx","cy","fill"],xl=["id","values","begin"],Cl=["values","begin"],$l=["cx","cy","fill"],wl=["id","values","begin"],kl=["values","begin"],Pl=["cx","cy","fill"],Al=["id","values","begin"],Ol=["values","begin"],Ll=e({__name:"index",props:{color:{type:Array,default:()=>[]}},setup(e){const t=e,r=_e(),l=f(null),u=c({animationId1:`d10ani1${r}`,animationId2:`d10ani2${r}`,animationId3:`d10ani3${r}`,animationId4:`d10ani4${r}`,animationId5:`d10ani5${r}`,animationId6:`d10ani6${r}`,animationId7:`d10ani7${r}`,defaultColor:["#00c2ff","rgba(0, 194, 255, 0.3)"],mergedColor:[]}),{width:s,height:p}=Be(l);function h(){u.mergedColor=Ge(Ee(u.defaultColor,!0),t.color||[])}return d((()=>t.color),(()=>{h()})),m((()=>{h()})),(e,t)=>(i(),a("div",{ref_key:"decoration10",ref:l,class:"dv-decoration-10"},[(i(),a("svg",{width:n(s),height:n(p)},[o("polyline",{stroke:n(u).mergedColor[1],"stroke-width":"2",points:`0, ${n(p)/2} ${n(s)}, ${n(p)/2}`},null,8,ll),o("polyline",{stroke:n(u).mergedColor[0],"stroke-width":"2",points:`5, ${n(p)/2} ${.2*n(s)-3}, ${n(p)/2}`,"stroke-dasharray":"0, "+.2*n(s),fill:"freeze"},[o("animate",{id:n(u).animationId2,attributeName:"stroke-dasharray",values:`0, ${.2*n(s)};${.2*n(s)}, 0;`,dur:"3s",begin:`${n(u).animationId1}.end`,fill:"freeze"},null,8,sl),o("animate",{attributeName:"stroke-dasharray",values:`${.2*n(s)}, 0;0, ${.2*n(s)}`,dur:"0.01s",begin:`${n(u).animationId7}.end`,fill:"freeze"},null,8,fl)],8,ul),o("polyline",{stroke:n(u).mergedColor[0],"stroke-width":"2",points:`${.2*n(s)+3}, ${n(p)/2} ${.8*n(s)-3}, ${n(p)/2}`,"stroke-dasharray":"0, "+.6*n(s)},[o("animate",{id:n(u).animationId4,attributeName:"stroke-dasharray",values:`0, ${.6*n(s)};${.6*n(s)}, 0`,dur:"3s",begin:`${n(u).animationId3}.end + 1s`,fill:"freeze"},null,8,dl),o("animate",{attributeName:"stroke-dasharray",values:`${.6*n(s)}, 0;0, ${.6*n(s)}`,dur:"0.01s",begin:`${n(u).animationId7}.end`,fill:"freeze"},null,8,pl)],8,cl),o("polyline",{stroke:n(u).mergedColor[0],"stroke-width":"2",points:`${.8*n(s)+3}, ${n(p)/2} ${n(s)-5}, ${n(p)/2}`,"stroke-dasharray":"0, "+.2*n(s)},[o("animate",{id:n(u).animationId6,attributeName:"stroke-dasharray",values:`0, ${.2*n(s)};${.2*n(s)}, 0`,dur:"3s",begin:`${n(u).animationId5}.end + 1s`,fill:"freeze"},null,8,gl),o("animate",{attributeName:"stroke-dasharray",values:`${.2*n(s)}, 0;0, ${.3*n(s)}`,dur:"0.01s",begin:`${n(u).animationId7}.end`,fill:"freeze"},null,8,vl)],8,hl),o("circle",{cx:"2",cy:n(p)/2,r:"2",fill:n(u).mergedColor[1]},[o("animate",{id:n(u).animationId1,attributeName:"fill",values:`${n(u).mergedColor[1]};${n(u).mergedColor[0]}`,begin:`0s;${n(u).animationId7}.end`,dur:"0.3s",fill:"freeze"},null,8,yl)],8,ml),o("circle",{cx:.2*n(s),cy:n(p)/2,r:"2",fill:n(u).mergedColor[1]},[o("animate",{id:n(u).animationId3,attributeName:"fill",values:`${n(u).mergedColor[1]};${n(u).mergedColor[0]}`,begin:`${n(u).animationId2}.end`,dur:"0.3s",fill:"freeze"},null,8,xl),o("animate",{attributeName:"fill",values:`${n(u).mergedColor[1]};${n(u).mergedColor[1]}`,dur:"0.01s",begin:`${n(u).animationId7}.end`,fill:"freeze"},null,8,Cl)],8,bl),o("circle",{cx:.8*n(s),cy:n(p)/2,r:"2",fill:n(u).mergedColor[1]},[o("animate",{id:n(u).animationId5,attributeName:"fill",values:`${n(u).mergedColor[1]};${n(u).mergedColor[0]}`,begin:`${n(u).animationId4}.end`,dur:"0.3s",fill:"freeze"},null,8,wl),o("animate",{attributeName:"fill",values:`${n(u).mergedColor[1]};${n(u).mergedColor[1]}`,dur:"0.01s",begin:`${n(u).animationId7}.end`,fill:"freeze"},null,8,kl)],8,$l),o("circle",{cx:n(s)-2,cy:n(p)/2,r:"2",fill:n(u).mergedColor[1]},[o("animate",{id:n(u).animationId7,attributeName:"fill",values:`${n(u).mergedColor[1]};${n(u).mergedColor[0]}`,begin:`${n(u).animationId6}.end`,dur:"0.3s",fill:"freeze"},null,8,Al),o("animate",{attributeName:"fill",values:`${n(u).mergedColor[1]};${n(u).mergedColor[1]}`,dur:"0.01s",begin:`${n(u).animationId7}.end`,fill:"freeze"},null,8,Ol)],8,Pl)],8,ol))],512))}}),Il={install(e){e.component("DvDecoration10",Ll)}},Sl=["width","height"],Ml=["fill","stroke"],jl=["fill","stroke","points"],Fl=["fill","stroke","points"],_l=["fill","stroke","points"],Gl=["fill","stroke","points"],El=["stroke","points"],Bl=["stroke","points"],Dl={class:"decoration-content"},Wl={__name:"index",props:{color:{type:Array,default:()=>[]}},setup(e){const t=e,r=f(null),l=c({defaultColor:["#1a98fc","#2cf7fe"],mergedColor:[]}),{width:u,height:p}=Be(r);function h(){l.mergedColor=Ge(Ee(l.defaultColor,!0),t.color||[])}return d((()=>t.color),(()=>{h()})),m((()=>{h()})),(e,t)=>(i(),a("div",{ref_key:"decoration11",ref:r,class:"dv-decoration-11"},[(i(),a("svg",{width:n(u),height:n(p)},[o("polygon",{fill:n(R)(n(l).mergedColor[1]||n(l).defaultColor[1],10),stroke:n(l).mergedColor[1],points:"20 10, 25 4, 55 4 60 10"},null,8,Ml),o("polygon",{fill:n(R)(n(l).mergedColor[1]||n(l).defaultColor[1],10),stroke:n(l).mergedColor[1],points:`20 ${n(p)-10}, 25 ${n(p)-4}, 55 ${n(p)-4} 60 ${n(p)-10}`},null,8,jl),o("polygon",{fill:n(R)(n(l).mergedColor[1]||n(l).defaultColor[1],10),stroke:n(l).mergedColor[1],points:`${n(u)-20} 10, ${n(u)-25} 4, ${n(u)-55} 4 ${n(u)-60} 10`},null,8,Fl),o("polygon",{fill:n(R)(n(l).mergedColor[1]||n(l).defaultColor[1],10),stroke:n(l).mergedColor[1],points:`${n(u)-20} ${n(p)-10}, ${n(u)-25} ${n(p)-4}, ${n(u)-55} ${n(p)-4} ${n(u)-60} ${n(p)-10}`},null,8,_l),o("polygon",{fill:n(R)(n(l).mergedColor[0]||n(l).defaultColor[0],20),stroke:n(l).mergedColor[0],points:`\n          20 10, 5 ${n(p)/2} 20 ${n(p)-10}\n          ${n(u)-20} ${n(p)-10} ${n(u)-5} ${n(p)/2} ${n(u)-20} 10\n        `},null,8,Gl),o("polyline",{fill:"transparent",stroke:n(R)(n(l).mergedColor[0]||n(l).defaultColor[0],70),points:`25 18, 15 ${n(p)/2} 25 ${n(p)-18}`},null,8,El),o("polyline",{fill:"transparent",stroke:n(R)(n(l).mergedColor[0]||n(l).defaultColor[0],70),points:`${n(u)-25} 18, ${n(u)-15} ${n(p)/2} ${n(u)-25} ${n(p)-18}`},null,8,Bl)],8,Sl)),o("div",Dl,[s(e.$slots,"default")])],512))}},Nl={install(e){e.component("DvDecoration11",Wl)}},Tl=["width","height"],Rl=["id"],zl=["stroke","stroke-width","d"],Hl=["id"],Ul=o("stop",{offset:"0%","stop-color":"transparent","stop-opacity":"1"},null,-1),Vl=["stop-color"],Ql=["r","cx","cy","stroke"],ql=["cx","cy","fill"],Yl=["values","dur"],Xl=["dur"],Jl=["cx","cy","fill"],Kl={key:0},Zl=["points","stroke"],eu=["d","stroke"],tu=["xlink:href"],nu=["values","dur"],ru={class:"decoration-content"},iu={__name:"index",props:{color:{type:Array,default:()=>[]},scanDur:{type:Number,default:3},haloDur:{type:Number,default:2}},setup(e){const t=e,l=_e(),u=f(null),{width:g,height:m}=Be(u,(()=>{}),(function(){C(),function(){const e=-Math.PI/2,t=y.sectorAngle/y.segment,n=g.value/4;let r=Se(b.value,x.value,n,e);y.pathD=new Array(y.segment).fill("").map(((i,a)=>{const o=Se(b.value,x.value,n,e-(a+1)*t).map((e=>parseFloat(e.toFixed(5)))),l=`M${r.join(",")} A${n}, ${n} 0 0 0 ${o.join(",")}`;return r=o,l}))}(),$(),function(){const e=(g.value/2-y.ringWidth/2)/y.ringNum;y.circleR=new Array(y.ringNum).fill(0).map(((t,n)=>e*(n+1)))}(),function(){const e=Math.PI/6,t=g.value/2;y.splitLinePoints=new Array(6).fill("").map(((n,r)=>{const i=e*(r+1),a=i+Math.PI,o=Se(b.value,x.value,t,i),l=Se(b.value,x.value,t,a);return`${o.join(",")} ${l.join(",")}`}))}(),function(){const e=Math.PI/6,t=g.value/2-1;y.arcD=new Array(4).fill("").map(((n,r)=>{const i=e*(3*r+1),a=i+e,o=Se(b.value,x.value,t,i),l=Se(b.value,x.value,t,a);return`M${o.join(",")} A${b.value}, ${x.value} 0 0 1 ${l.join(",")}`}))}()})),y=c({gId:`decoration-12-g-${l}`,gradientId:`decoration-12-gradient-${l}`,defaultColor:["#2783ce","#2cf7fe"],mergedColor:[],pathD:[],pathColor:[],circleR:[],splitLinePoints:[],arcD:[],segment:30,sectorAngle:Math.PI/3,ringNum:3,ringWidth:1,showSplitLine:!0}),b=r((()=>g.value/2)),x=r((()=>m.value/2));function C(){y.mergedColor=Ge(Ee(y.defaultColor,!0),t.color||[])}function $(){const e=100/(y.segment-1);y.pathColor=new Array(y.segment).fill(y.mergedColor[0]).map(((t,n)=>R(y.mergedColor[0],100-n*e)))}return d((()=>t.color),(()=>{C(),$()})),(t,r)=>(i(),a("div",{ref_key:"decoration12",ref:u,class:"dv-decoration-12"},[(i(),a("svg",{width:n(g),height:n(m)},[o("defs",null,[o("g",{id:n(y).gId},[(i(!0),a(p,null,h(n(y).pathD,((e,t)=>(i(),a("path",{key:e,stroke:n(y).pathColor[t],"stroke-width":n(g)/2,fill:"transparent",d:e},null,8,zl)))),128))],8,Rl),o("radialGradient",{id:n(y).gradientId,cx:"50%",cy:"50%",r:"50%"},[Ul,o("stop",{offset:"100%","stop-color":n(R)(n(y).mergedColor[1]||n(y).defaultColor[1],30),"stop-opacity":"1"},null,8,Vl)],8,Hl)]),(i(!0),a(p,null,h(n(y).circleR,(e=>(i(),a("circle",{key:e,r:e,cx:n(b),cy:n(x),stroke:n(y).mergedColor[1],"stroke-width":.8,fill:"transparent"},null,8,Ql)))),128)),o("circle",{r:"1",cx:n(b),cy:n(x),stroke:"transparent",fill:`url(#${n(y).gradientId})`},[o("animate",{attributeName:"r",values:"1;"+n(g)/2,dur:`${e.haloDur}s`,repeatCount:"indefinite"},null,8,Yl),o("animate",{attributeName:"opacity",values:"1;0",dur:`${e.haloDur}s`,repeatCount:"indefinite"},null,8,Xl)],8,ql),o("circle",{r:"2",cx:n(b),cy:n(x),fill:n(y).mergedColor[1]},null,8,Jl),n(y).showSplitLine?(i(),a("g",Kl,[(i(!0),a(p,null,h(n(y).splitLinePoints,(e=>(i(),a("polyline",{key:e,points:e,stroke:n(y).mergedColor[1],"stroke-width":.5,opacity:"50"},null,8,Zl)))),128))])):v("",!0),(i(!0),a(p,null,h(n(y).arcD,(e=>(i(),a("path",{key:e,d:e,stroke:n(y).mergedColor[1],"stroke-width":"2.3",fill:"transparent"},null,8,eu)))),128)),o("use",{"xlink:href":`#${n(y).gId}`},[o("animateTransform",{attributeName:"transform",type:"rotate",values:`0, ${n(b)} ${n(x)};360, ${n(b)} ${n(x)}`,dur:`${e.scanDur}s`,repeatCount:"indefinite"},null,8,nu)],8,tu)],8,Tl)),o("div",ru,[s(t.$slots,"default")])],512))}},au={install(e){e.component("DvDecoration12",iu)}},ou={color:{type:Array,default:()=>[]},backgroundColor:{type:String,default:"transparent"}};function lu(e,t){return r((()=>0===t.value.length?e:t.value))}const uu=["left-top","right-top","left-bottom","right-bottom"],su=["#4fd2dd","#235fa7"],fu=e({props:ou,setup(e){const t=f(null),n=lu(su,P(e,"color")),{width:r,height:i,initWH:a}=Be(t);return{width:r,height:i,initWH:a,mergedColor:n,borderBox1:t}},render(){const{backgroundColor:e,width:t,height:n,mergedColor:r,$slots:i}=this;return w("div",{ref:"borderBox1",class:"dv-border-box-1"},[w("svg",{class:"dv-border",width:t,height:n},[w("polygon",{fill:e,points:`10, 27 10, ${n-27} 13, ${n-24} 13, ${n-21} 24, ${n-11}\n      38, ${n-11} 41, ${n-8} 73, ${n-8} 75, ${n-10} 81, ${n-10}\n      85, ${n-6} ${t-85}, ${n-6} ${t-81}, ${n-10} ${t-75}, ${n-10}\n      ${t-73}, ${n-8} ${t-41}, ${n-8} ${t-38}, ${n-11}\n      ${t-10}, ${n-27} ${t-10}, 27 ${t-13}, 25 ${t-13}, 21\n      ${t-24}, 11 ${t-38}, 11 ${t-41}, 8 ${t-73}, 8 ${t-75}, 10\n      ${t-81}, 10 ${t-85}, 6 85, 6 81, 10 75, 10 73, 8 41, 8 38, 11 24, 11 13, 21 13, 24`},null)]),uu.map((e=>w("svg",{key:e,width:"150px",height:"150px",class:`${e} dv-border`},[w("polygon",{fill:r[0],points:"6,66 6,18 12,12 18,12 24,6 27,6 30,9 36,9 39,6 84,6 81,9 75,9 73.2,7 40.8,7 37.8,10.2 24,10.2 12,21 12,24 9,27 9,51 7.8,54 7.8,63"},[w("animate",{attributeName:"fill",values:`${r[0]};${r[1]};${r[0]}`,dur:"0.5s",begin:"0s",repeatCount:"indefinite"},null)]),w("polygon",{fill:r[1],points:"27.599999999999998,4.8 38.4,4.8 35.4,7.8 30.599999999999998,7.8"},[w("animate",{attributeName:"fill",values:`${r[1]};${r[0]};${r[1]}`,dur:"0.5s",begin:"0s",repeatCount:"indefinite"},null)]),w("polygon",{fill:r[0],points:"9,54 9,63 7.199999999999999,66 7.199999999999999,75 7.8,78 7.8,110 8.4,110 8.4,66 9.6,66 9.6,54"},[w("animate",{attributeName:"fill",values:`${r[0]};${r[1]};transparent`,dur:"1s",begin:"0s",repeatCount:"indefinite"},null)])]))),w("div",{class:"border-box-content"},[s(i,"default")])])}}),cu={install(e){e.component("DvBorderBox1",fu)}},du=["#fff","rgba(255, 255, 255, 0.6)"],pu=e({props:ou,setup(e){const t=f(null),n=lu(du,P(e,"color")),{width:r,height:i,initWH:a}=Be(t);return{width:r,height:i,initWH:a,mergedColor:n,borderBox2:t}},render(){const{$slots:e,backgroundColor:t,width:n,height:r,mergedColor:i}=this;return w("div",{ref:"borderBox2",class:"dv-border-box-2"},[w("svg",{class:"dv-border-svg-container",width:n,height:r},[w("polygon",{fill:t,points:`\n        7, 7 ${n-7}, 7 ${n-7}, ${r-7} 7, ${r-7}\n      `},null),w("polyline",{stroke:i[0],points:`2, 2 ${n-2} ,2 ${n-2}, ${r-2} 2, ${r-2} 2, 2`},null),w("polyline",{stroke:i[1],points:`6, 6 ${n-6}, 6 ${n-6}, ${r-6} 6, ${r-6} 6, 6`},null),w("circle",{fill:i[0],cx:"11",cy:"11",r:"1"},null),w("circle",{fill:i[0],cx:n-11,cy:"11",r:"1"},null),w("circle",{fill:i[0],cx:n-11,cy:r-11,r:"1"},null),w("circle",{fill:i[0],cx:"11",cy:r-11,r:"1"},null)]),w("div",{class:"border-box-content"},[s(e,"default")])])}}),hu={install(e){e.component("DvBorderBox2",pu)}},gu=["#2862b7","#2862b7"],vu=e({props:ou,setup(e){const t=f(null),{width:n,height:r,initWH:i}=Be(t);return{width:n,height:r,mergedColor:lu(gu,P(e,"color")),initWH:i,borderBox3:t}},render(){const{$slots:e,width:t,height:n,backgroundColor:r,mergedColor:i}=this;return w("div",{ref:"borderBox3",class:"dv-border-box-3"},[w("svg",{class:"dv-border-svg-container",width:t,height:n},[w("polygon",{fill:r,points:`\n              23, 23 ${t-24}, 23 ${t-24}, ${n-24} 23, ${n-24}\n            `},null),w("polyline",{class:"dv-bb3-line1",stroke:i[0],points:`4, 4 ${t-22} ,4 ${t-22}, ${n-22} 4, ${n-22} 4, 4`},null),w("polyline",{class:"dv-bb3-line2",stroke:i[1],points:`10, 10 ${t-16}, 10 ${t-16}, ${n-16} 10, ${n-16} 10, 10`},null),w("polyline",{class:"dv-bb3-line2",stroke:i[1],points:`16, 16 ${t-10}, 16 ${t-10}, ${n-10} 16, ${n-10} 16, 16`},null),w("polyline",{class:"dv-bb3-line2",stroke:i[1],points:`22, 22 ${t-4}, 22 ${t-4}, ${n-4} 22, ${n-4} 22, 22`},null)]),w("div",{class:"border-box-content"},[s(e,"default")])])}}),mu={install(e){e.component("DvBorderBox3",vu)}},yu={...ou,reverse:{type:Boolean,default:!1}},bu=["red","rgba(0,0,255,0.8)"],xu=e({props:yu,setup(e){const t=f(null),{width:n,height:r,initWH:i}=Be(t);return{width:n,height:r,initWH:i,mergedColor:lu(bu,P(e,"color")),borderBox4:t}},render(){const{$slots:e,backgroundColor:t,reverse:n,width:r,height:i,mergedColor:a}=this;return w("div",{ref:"borderBox4",class:"dv-border-box-4"},[w("svg",{class:`dv-border-svg-container ${n&&"dv-reverse"}`,width:r,height:i},[w("polygon",{fill:t,points:`\n        ${r-15}, 22 170, 22 150, 7 40, 7 28, 21 32, 24\n        16, 42 16, ${i-32} 41, ${i-7} ${r-15}, ${i-7}\n      `},null),w("polyline",{class:"dv-bb4-line-1",stroke:a[0],points:`145, ${i-5} 40, ${i-5} 10, ${i-35}\n          10, 40 40, 5 150, 5 170, 20 ${r-15}, 20`},null),w("polyline",{stroke:a[1],class:"dv-bb4-line-2",points:`245, ${i-1} 36, ${i-1} 14, ${i-23}\n          14, ${i-100}`},null),w("polyline",{class:"dv-bb4-line-3",stroke:a[0],points:`7, ${i-40} 7, ${i-75}`},null),w("polyline",{class:"dv-bb4-line-4",stroke:a[0],points:"28, 24 13, 41 13, 64"},null),w("polyline",{class:"dv-bb4-line-5",stroke:a[0],points:"5, 45 5, 140"},null),w("polyline",{class:"dv-bb4-line-6",stroke:a[1],points:"14, 75 14, 180"},null),w("polyline",{class:"dv-bb4-line-7",stroke:a[1],points:"55, 11 147, 11 167, 26 250, 26"},null),w("polyline",{class:"dv-bb4-line-8",stroke:a[1],points:"158, 5 173, 16"},null),w("polyline",{class:"dv-bb4-line-9",stroke:a[0],points:`200, 17 ${r-10}, 17`},null),w("polyline",{class:"dv-bb4-line-10",stroke:a[1],points:`385, 17 ${r-10}, 17`},null)]),w("div",{class:"border-box-content"},[s(e,"default")])])}}),Cu={install(e){e.component("DvBorderBox4",xu)}},$u={...ou,reverse:{type:Boolean,default:!1}},wu=["rgba(255, 255, 255, 0.35)","rgba(255, 255, 255, 0.20)"],ku=e({props:$u,setup(e){const t=f(null),{width:n,height:r,initWH:i}=Be(t);return{width:n,height:r,initWH:i,mergedColor:lu(wu,P(e,"color")),borderBox5:t}},render(){const{$slots:e,width:t,height:n,mergedColor:r,backgroundColor:i,reverse:a}=this;return w("div",{ref:"borderBox5",class:"dv-border-box-5"},[w("svg",{class:`dv-border-svg-container  ${a&&"dv-reverse"}`,width:t,height:n},[w("polygon",{fill:i,points:`\n            10, 22 ${t-22}, 22 ${t-22}, ${n-86} ${t-84}, ${n-24} 10, ${n-24}\n          `},null),w("polyline",{class:"dv-bb5-line-1",stroke:r[0],points:`8, 5 ${t-5}, 5 ${t-5}, ${n-100}\n          ${t-100}, ${n-5} 8, ${n-5} 8, 5`},null),w("polyline",{class:"dv-bb5-line-2",stroke:r[1],points:`3, 5 ${t-20}, 5 ${t-20}, ${n-60}\n          ${t-74}, ${n-5} 3, ${n-5} 3, 5`},null),w("polyline",{class:"dv-bb5-line-3",stroke:r[1],points:`50, 13 ${t-35}, 13`},null),w("polyline",{class:"dv-bb5-line-4",stroke:r[1],points:`15, 20 ${t-35}, 20`},null),w("polyline",{class:"dv-bb5-line-5",stroke:r[1],points:`15, ${n-20} ${t-110}, ${n-20}`},null),w("polyline",{class:"dv-bb5-line-6",stroke:r[1],points:`15, ${n-13} ${t-110}, ${n-13}`},null)]),w("div",{class:"border-box-content"},[s(e,"default")])])}}),Pu={install(e){e.component("DvBorderBox5",ku)}},Au=["rgba(255, 255, 255, 0.35)","gray"],Ou=e({props:ou,setup(e){const t=f(null),{width:n,height:r,initWH:i}=Be(t);return{width:n,height:r,initWH:i,mergedColor:lu(Au,P(e,"color")),borderBox6:t}},render(){const{$slots:e,width:t,height:n,mergedColor:r,backgroundColor:i}=this;return w("div",{ref:"borderBox6",class:"dv-border-box-6"},[w("svg",{class:"dv-border-svg-container",width:t,height:n},[w("polygon",{fill:i,points:`\n            9, 7 ${t-9}, 7 ${t-9}, ${n-7} 9, ${n-7}\n            `},null),w("circle",{fill:r[1],cx:"5",cy:"5",r:"2"},null),w("circle",{fill:r[1],cx:t-5,cy:"5",r:"2"},null),w("circle",{fill:r[1],cx:t-5,cy:n-5,r:"2"},null),w("circle",{fill:r[1],cx:"5",cy:n-5,r:"2"},null),w("polyline",{stroke:r[0],points:`10, 4 ${t-10}, 4`},null),w("polyline",{stroke:r[0],points:`10, ${n-4} ${t-10}, ${n-4}`},null),w("polyline",{stroke:r[0],points:"5, 70 5, "+(n-70)},null),w("polyline",{stroke:r[0],points:`${t-5}, 70 ${t-5}, ${n-70}`},null),w("polyline",{stroke:r[0],points:"3, 10, 3, 50"},null),w("polyline",{stroke:r[0],points:"7, 30 7, 80"},null),w("polyline",{stroke:r[0],points:`${t-3}, 10 ${t-3}, 50`},null),w("polyline",{stroke:r[0],points:`${t-7}, 30 ${t-7}, 80`},null),w("polyline",{stroke:r[0],points:`3, ${n-10} 3, ${n-50}`},null),w("polyline",{stroke:r[0],points:`7, ${n-30} 7, ${n-80}`},null),w("polyline",{stroke:r[0],points:`${t-3}, ${n-10} ${t-3}, ${n-50}`},null),w("polyline",{stroke:r[0],points:`${t-7}, ${n-30} ${t-7}, ${n-80}`},null)]),w("div",{class:"border-box-content"},[s(e,"default")])])}}),Lu={install(e){e.component("DvBorderBox6",Ou)}},Iu=["rgba(128,128,128,0.3)","rgba(128,128,128,0.5)"],Su=e({props:ou,setup(e){const t=f(null),{width:n,height:r,initWH:i}=Be(t);return{width:n,height:r,initWH:i,mergedColor:lu(Iu,P(e,"color")),borderBox7:t}},render(){const{$slots:e,width:t,height:n,mergedColor:r,backgroundColor:i}=this;return w("div",{ref:"borderBox7",class:"dv-border-box-7",style:`box-shadow: inset 0 0 40px ${r[0]}; border: 1px solid ${r[0]}; background-color: ${i}`},[w("svg",{class:"dv-border-svg-container",width:t,height:n},[w("polyline",{class:"dv-bb7-line-width-2",stroke:r[0],points:"0, 25 0, 0 25, 0"},null),w("polyline",{class:"dv-bb7-line-width-2",stroke:r[0],points:`${t-25}, 0 ${t}, 0 ${t}, 25`},null),w("polyline",{class:"dv-bb7-line-width-2",stroke:r[0],points:`${t-25}, ${n} ${t}, ${n} ${t}, ${n-25}`},null),w("polyline",{class:"dv-bb7-line-width-2",stroke:r[0],points:`0, ${n-25} 0, ${n} 25, ${n}`},null),w("polyline",{class:"dv-bb7-line-width-5",stroke:r[1],points:"0, 10 0, 0 10, 0"},null),w("polyline",{class:"dv-bb7-line-width-5",stroke:r[1],points:`${t-10}, 0 ${t}, 0 ${t}, 10`},null),w("polyline",{class:"dv-bb7-line-width-5",stroke:r[1],points:`${t-10}, ${n} ${t}, ${n} ${t}, ${n-10}`},null),w("polyline",{class:"dv-bb7-line-width-5",stroke:r[1],points:`0, ${n-10} 0, ${n} 10, ${n}`},null)]),w("div",{class:"border-box-content"},[s(e,"default")])])}}),Mu={install(e){e.component("DvBorderBox7",Su)}},ju={...ou,reverse:{type:Boolean,default:!1},dur:{type:Number,default:3}},Fu=["#235fa7","#4fd2dd"],_u=e({props:ju,setup(e){const t=_e(),n=f(null),i=c({path:`border-box-8-path-${t}`,gradient:`border-box-8-gradient-${t}`,mask:`border-box-8-mask-${t}`}),{width:a,height:o,initWH:l}=Be(n),u=r((()=>2*(a.value+o.value-5))),s=r((()=>e.reverse?`M 2.5, 2.5 L 2.5, ${o.value-2.5} L ${a.value-2.5}, ${o.value-2.5} L ${a.value-2.5}, 2.5 L 2.5, 2.5`:`M2.5, 2.5 L${a.value-2.5}, 2.5 L${a.value-2.5}, ${o.value-2.5} L2.5, ${o.value-2.5} L2.5, 2.5`)),d=lu(Fu,P(e,"color"));return{width:a,height:o,initWH:l,state:i,mergedColor:d,pathD:s,length:u,borderBox8:n}},render(){const{$slots:e,width:t,height:n,state:r,mergedColor:i,pathD:a,length:o,backgroundColor:l,dur:u}=this;return w("div",{ref:"borderBox8",class:"dv-border-box-8"},[w("svg",{class:"dv-border-svg-container",width:t,height:n},[w("defs",null,[w("path",{id:r.path,d:a,fill:"transparent"},null),w("radialGradient",{id:r.gradient,cx:"50%",cy:"50%",r:"50%"},[w("stop",{offset:"0%","stop-color":"#fff","stop-opacity":"1"},null),w("stop",{offset:"100%","stop-color":"#fff","stop-opacity":"0"},null)]),w("mask",{id:r.mask},[w("circle",{cx:"0",cy:"0",r:"150",fill:`url(#${r.gradient})`},[A("animateMotion",{dur:`${u}s`,path:a,rotate:"auto",repeatCount:"indefinite"})])])]),w("polygon",{fill:l,points:`5, 5 ${t-5}, 5 ${t-5} ${n-5} 5, ${n-5}`},null),w("use",{stroke:i[0],"stroke-width":"1","xlink:href":`#${r.path}`},null),w("use",{stroke:i[1],"stroke-width":"3","xlink:href":`#${r.path}`,mask:`url(#${r.mask})`},[w("animate",{attributeName:"stroke-dasharray",from:`0, ${o}`,to:`${o}, 0`,dur:`${u}s`,repeatCount:"indefinite"},null)])]),w("div",{class:"border-box-content"},[s(e,"default")])])}}),Gu={install(e){e.component("DvBorderBox8",_u)}},Eu=["#11eefd","#0078d2"],Bu=e({props:ou,setup(e){const t=_e(),n=f(null),{width:r,height:i,initWH:a}=Be(n);return{width:r,height:i,initWH:a,state:c({gradientId:`border-box-9-gradient-${t}`,maskId:`border-box-9-mask-${t}`}),mergedColor:lu(Eu,P(e,"color")),borderBox9:n}},render(){const{$slots:e,width:t,height:n,state:r,mergedColor:i,backgroundColor:a}=this;return w("div",{ref:"borderBox9",class:"dv-border-box-9"},[w("svg",{class:"dv-border-svg-container",width:t,height:n},[w("defs",null,[w("linearGradient",{id:r.gradientId,x1:"0%",y1:"0%",x2:"100%",y2:"100%"},[w("animate",{attributeName:"x1",values:"0%;100%;0%",dur:"10s",begin:"0s",repeatCount:"indefinite"},null),w("animate",{attributeName:"x2",values:"100%;0%;100%",dur:"10s",begin:"0s",repeatCount:"indefinite"},null),w("stop",{offset:"0%","stop-color":i[0]},[w("animate",{attributeName:"stop-color",values:`${i[0]};${i[1]};${i[0]}`,dur:"10s",begin:"0s",repeatCount:"indefinite"},null)]),w("stop",{offset:"100%","stop-color":i[1]},[w("animate",{attributeName:"stop-color",values:`${i[1]};${i[0]};${i[1]}`,dur:"10s",begin:"0s",repeatCount:"indefinite"},null)])]),w("mask",{id:r.maskId},[w("polyline",{stroke:"#fff","stroke-width":"3",fill:"transparent",points:`8, ${.4*n} 8, 3, ${.4*t+7}, 3`},null),w("polyline",{fill:"#fff",points:`8, ${.15*n} 8, 3, ${.1*t+7}, 3\n              ${.1*t}, 8 14, 8 14, ${.15*n-7}\n            `},null),w("polyline",{stroke:"#fff","stroke-width":"3",fill:"transparent",points:`${.5*t}, 3 ${t-3}, 3, ${t-3}, ${.25*n}`},null),w("polyline",{fill:"#fff",points:`\n              ${.52*t}, 3 ${.58*t}, 3\n              ${.58*t-7}, 9 ${.52*t+7}, 9\n            `},null),w("polyline",{fill:"#fff",points:`\n              ${.9*t}, 3 ${t-3}, 3 ${t-3}, ${.1*n}\n              ${t-9}, ${.1*n-7} ${t-9}, 9 ${.9*t+7}, 9\n            `},null),w("polyline",{stroke:"#fff","stroke-width":"3",fill:"transparent",points:`8, ${.5*n} 8, ${n-3} ${.3*t+7}, ${n-3}`},null),w("polyline",{fill:"#fff",points:`\n              8, ${.55*n} 8, ${.7*n}\n              2, ${.7*n-7} 2, ${.55*n+7}\n            `},null),w("polyline",{stroke:"#fff","stroke-width":"3",fill:"transparent",points:`${.35*t}, ${n-3} ${t-3}, ${n-3} ${t-3}, ${.35*n}`},null),w("polyline",{fill:"#fff",points:`\n              ${.92*t}, ${n-3} ${t-3}, ${n-3} ${t-3}, ${.8*n}\n              ${t-9}, ${.8*n+7} ${t-9}, ${n-9} ${.92*t+7}, ${n-9}\n            `},null)])]),w("polygon",{fill:a,points:`\n              15, 9 ${.1*t+1}, 9 ${.1*t+4}, 6 ${.52*t+2}, 6\n              ${.52*t+6}, 10 ${.58*t-7}, 10 ${.58*t-2}, 6\n              ${.9*t+2}, 6 ${.9*t+6}, 10 ${t-10}, 10 ${t-10}, ${.1*n-6}\n              ${t-6}, ${.1*n-1} ${t-6}, ${.8*n+1} ${t-10}, ${.8*n+6}\n              ${t-10}, ${n-10} ${.92*t+7}, ${n-10}  ${.92*t+2}, ${n-6}\n              11, ${n-6} 11, ${.15*n-2} 15, ${.15*n-7}\n            `},null),w("rect",{x:"0",y:"0",width:t,height:n,fill:`url(#${r.gradientId})`,mask:`url(#${r.maskId})`},null)]),w("div",{class:"border-box-content"},[s(e,"default")])])}}),Du={install(e){e.component("DvBorderBox9",Bu)}},Wu=["left-top","right-top","left-bottom","right-bottom"],Nu=["#1d48c4","#d3e1f8"],Tu=e({props:ou,setup(e){const t=f(null),{width:n,height:r,initWH:i}=Be(t);return{width:n,height:r,initWH:i,mergedColor:lu(Nu,P(e,"color")),borderBox10:t}},render(){const{$slots:e,width:t,height:n,mergedColor:r,backgroundColor:i}=this;return w("div",{ref:"borderBox10",class:"dv-border-box-10",style:`box-shadow: inset 0 0 25px 3px ${r[0]}`},[w("svg",{class:"dv-border-svg-container",width:t,height:n},[w("polygon",{fill:i,points:`\n              4, 0 ${t-4}, 0 ${t}, 4 ${t}, ${n-4} ${t-4}, ${n}\n              4, ${n} 0, ${n-4} 0, 4\n            `},null)]),Wu.map((e=>w("svg",{width:"150px",height:"150px",class:`${e} dv-border-svg-container`},[w("polygon",{fill:r[1],points:"40, 0 5, 0 0, 5 0, 16 3, 19 3, 7 7, 3 35, 3"},null)]))),w("div",{class:"border-box-content"},[s(e,"default")])])}}),Ru={install(e){e.component("DvBorderBox10",Tu)}},zu={...ou,title:{type:String,default:""},titleWidth:{type:Number,default:250},animate:{type:Boolean,default:!0}},Hu=["#8aaafb","#1f33a2"],Uu=e({props:zu,setup(e){const t=_e(),n=f(null),{width:r,height:i,initWH:a}=Be(n);return{width:r,height:i,initWH:a,filterId:f(`border-box-11-filterId-${t}`),mergedColor:lu(Hu,P(e,"color")),borderBox11:n}},render(){const{$slots:e,width:t,height:n,filterId:r,mergedColor:i,backgroundColor:a,title:o,titleWidth:l,animate:u}=this;return w("div",{ref:"borderBox11",class:"dv-border-box-11"},[w("svg",{class:"dv-border-svg-container",width:t,height:n},[w("defs",null,[w("filter",{id:r,height:"150%",width:"150%",x:"-25%",y:"-25%"},[w("feMorphology",{operator:"dilate",radius:"2",in:"SourceAlpha",result:"thicken"},null),w("feGaussianBlur",{in:"thicken",stdDeviation:"3",result:"blurred"},null),w("feFlood",{"flood-color":i[1],result:"glowColor"},null),w("feComposite",{in:"glowColor",in2:"blurred",operator:"in",result:"softGlowColored"},null),w("feMerge",null,[w("feMergeNode",{in:"softGlowColored"},null),w("feMergeNode",{in:"SourceGraphic"},null)])])]),w("polygon",{fill:a,points:`\n        20, 32 ${.5*t-l/2}, 32 ${.5*t-l/2+20}, 53\n        ${.5*t+l/2-20}, 53 ${.5*t+l/2}, 32\n        ${t-20}, 32 ${t-8}, 48 ${t-8}, ${n-25} ${t-20}, ${n-8}\n        20, ${n-8} 8, ${n-25} 8, 50\n      `},null),w("polyline",{stroke:i[0],filter:`url(#${r})`,points:`\n          ${(t-l)/2}, 30\n          20, 30 7, 50 7, ${50+(n-167)/2}\n          13, ${55+(n-167)/2} 13, ${135+(n-167)/2}\n          7, ${140+(n-167)/2} 7, ${n-27}\n          20, ${n-7} ${t-20}, ${n-7} ${t-7}, ${n-27}\n          ${t-7}, ${140+(n-167)/2} ${t-13}, ${135+(n-167)/2}\n          ${t-13}, ${55+(n-167)/2} ${t-7}, ${50+(n-167)/2}\n          ${t-7}, 50 ${t-20}, 30 ${(t+l)/2}, 30\n          ${(t+l)/2-20}, 7 ${(t-l)/2+20}, 7\n          ${(t-l)/2}, 30 ${(t-l)/2+20}, 52\n          ${(t+l)/2-20}, 52 ${(t+l)/2}, 30\n        `},null),w("polygon",{stroke:i[0],fill:"transparent",points:`\n          ${(t+l)/2-5}, 30 ${(t+l)/2-21}, 11\n          ${(t+l)/2-27}, 11 ${(t+l)/2-8}, 34\n        `},null),w("polygon",{stroke:i[0],fill:"transparent",points:`\n          ${(t-l)/2+5}, 30 ${(t-l)/2+22}, 49\n          ${(t-l)/2+28}, 49 ${(t-l)/2+8}, 26\n        `},null),w("polygon",{stroke:i[0],fill:R(i[1]||Hu[1],30),filter:`url(#${r})`,points:`\n          ${(t+l)/2-11}, 37 ${(t+l)/2-32}, 11\n          ${(t-l)/2+23}, 11 ${(t-l)/2+11}, 23\n          ${(t-l)/2+33}, 49 ${(t+l)/2-22}, 49\n        `},null),w("polygon",{filter:`url(#${r})`,fill:i[0],opacity:"1",points:`\n          ${(t-l)/2-10}, 37 ${(t-l)/2-31}, 37\n          ${(t-l)/2-25}, 46 ${(t-l)/2-4}, 46\n        `},[u&&w("animate",{attributeName:"opacity",values:"1;0.7;1",dur:"2s",begin:"0s",repeatCount:"indefinite"},null)]),w("polygon",{filter:`url(#${r})`,fill:i[0],opacity:"0.7",points:`\n          ${(t-l)/2-40}, 37 ${(t-l)/2-61}, 37\n          ${(t-l)/2-55}, 46 ${(t-l)/2-34}, 46\n        `},[u&&w("animate",{attributeName:"opacity",values:"0.7;0.4;0.7",dur:"2s",begin:"0s",repeatCount:"indefinite"},null)]),w("polygon",{filter:`url(#${r})`,fill:i[0],opacity:"0.5",points:`\n          ${(t-l)/2-70}, 37 ${(t-l)/2-91}, 37\n          ${(t-l)/2-85}, 46 ${(t-l)/2-64}, 46\n        `},[u&&w("animate",{attributeName:"opacity",values:"0.5;0.2;0.5",dur:"2s",begin:"0s",repeatCount:"indefinite"},null)]),w("polygon",{filter:`url(#${r})`,fill:i[0],opacity:"1",points:`\n          ${(t+l)/2+30}, 37 ${(t+l)/2+9}, 37\n          ${(t+l)/2+3}, 46 ${(t+l)/2+24}, 46\n        `},[u&&w("animate",{attributeName:"opacity",values:"1;0.7;1",dur:"2s",begin:"0s",repeatCount:"indefinite"},null)]),w("polygon",{filter:`url(#${r})`,fill:i[0],opacity:"0.7",points:`\n          ${(t+l)/2+60}, 37 ${(t+l)/2+39}, 37\n          ${(t+l)/2+33}, 46 ${(t+l)/2+54}, 46\n        `},[u&&w("animate",{attributeName:"opacity",values:"0.7;0.4;0.7",dur:"2s",begin:"0s",repeatCount:"indefinite"},null)]),w("polygon",{filter:`url(#${r})`,fill:i[0],opacity:"0.5",points:`\n          ${(t+l)/2+90}, 37 ${(t+l)/2+69}, 37\n          ${(t+l)/2+63}, 46 ${(t+l)/2+84}, 46\n        `},[u&&w("animate",{attributeName:"opacity",values:"0.5;0.2;0.5",dur:"2s",begin:"0s",repeatCount:"indefinite"},null)]),w("text",{class:"dv-border-box-11-title",x:""+t/2,y:"32",fill:"#fff","font-size":"18","text-anchor":"middle","dominant-baseline":"middle"},[o]),w("polygon",{fill:i[0],filter:`url(#${r})`,points:`\n          7, ${53+(n-167)/2} 11, ${57+(n-167)/2}\n          11, ${133+(n-167)/2} 7, ${137+(n-167)/2}\n        `},null),w("polygon",{fill:i[0],filter:`url(#${r})`,points:`\n          ${t-7}, ${53+(n-167)/2} ${t-11}, ${57+(n-167)/2}\n          ${t-11}, ${133+(n-167)/2} ${t-7}, ${137+(n-167)/2}\n        `},null)]),w("div",{class:"border-box-content"},[s(e,"default")])])}}),Vu={install(e){e.component("DvBorderBox11",Uu)}},Qu=["#2e6099","#7ce7fd"],qu=e({props:ou,setup(e){const t=_e(),n=f(null),{width:r,height:i,initWH:a}=Be(n);return{width:r,height:i,filterId:f(`borderr-box-12-filterId-${t}`),mergedColor:lu(Qu,P(e,"color")),initWH:a,borderBox12:n}},render(){const{$slots:e,width:t,height:n,filterId:r,mergedColor:i,backgroundColor:a}=this;return w("div",{ref:"borderBox12",class:"dv-border-box-12"},[w("svg",{class:"dv-border-svg-container",width:t,height:n},[w("defs",null,[w("filter",{id:r,height:"150%",width:"150%",x:"-25%",y:"-25%"},[w("feMorphology",{operator:"dilate",radius:"1",in:"SourceAlpha",result:"thicken"},null),w("feGaussianBlur",{in:"thicken",stdDeviation:"2",result:"blurred"},null),w("feFlood",{"flood-color":R(i[1]||Qu[1],70),result:"glowColor"},[w("animate",{attributeName:"flood-color",values:`\n                ${R(i[1]||Qu[1],70)};\n                ${R(i[1]||Qu[1],30)};\n                ${R(i[1]||Qu[1],70)};\n              `,dur:"3s",begin:"0s",repeatCount:"indefinite"},null)]),w("feComposite",{in:"glowColor",in2:"blurred",operator:"in",result:"softGlowColored"},null),w("feMerge",null,[w("feMergeNode",{in:"softGlowColored"},null),w("feMergeNode",{in:"SourceGraphic"},null)])])]),t&&n&&w("path",{fill:a,"stroke-width":"2",stroke:i[0],d:`\n          M15 5 L ${t-15} 5 Q ${t-5} 5, ${t-5} 15\n          L ${t-5} ${n-15} Q ${t-5} ${n-5}, ${t-15} ${n-5}\n          L 15, ${n-5} Q 5 ${n-5} 5 ${n-15} L 5 15\n          Q 5 5 15 5\n        `},null),w("path",{"stroke-width":"2",fill:"transparent","stroke-linecap":"round",filter:`url(#${r})`,stroke:i[1],d:"M 20 5 L 15 5 Q 5 5 5 15 L 5 20"},null),w("path",{"stroke-width":"2",fill:"transparent","stroke-linecap":"round",filter:`url(#${r})`,stroke:i[1],d:`M ${t-20} 5 L ${t-15} 5 Q ${t-5} 5 ${t-5} 15 L ${t-5} 20`},null),w("path",{"stroke-width":"2",fill:"transparent","stroke-linecap":"round",filter:`url(#${r})`,stroke:i[1],d:`\n          M ${t-20} ${n-5} L ${t-15} ${n-5}\n          Q ${t-5} ${n-5} ${t-5} ${n-15}\n          L ${t-5} ${n-20}\n          `},null),w("path",{"stroke-width":"2",fill:"transparent","stroke-linecap":"round",filter:`url(#${r})`,stroke:i[1],d:`\n          M 20 ${n-5} L 15 ${n-5}\n          Q 5 ${n-5} 5 ${n-15}\n          L 5 ${n-20}\n          `},null)]),w("div",{class:"border-box-content"},[s(e,"default")])])}}),Yu={install(e){e.component("DvBorderBox12",qu)}},Xu=["#6586ec","#2cf7fe"],Ju=e({props:ou,setup(e){const t=f(null),{width:n,height:r,initWH:i}=Be(t);return{width:n,height:r,mergedColor:lu(Xu,P(e,"color")),initWH:i,borderBox13:t}},render(){const{$slots:e,width:t,height:n,mergedColor:r,backgroundColor:i}=this;return w("div",{ref:"borderBox13",class:"dv-border-box-13"},[w("svg",{class:"dv-border-svg-container",width:t,height:n},[w("path",{fill:i,stroke:r[0],d:`\n          M 5 20 L 5 10 L 12 3  L 60 3 L 68 10\n          L ${t-20} 10 L ${t-5} 25\n          L ${t-5} ${n-5} L 20 ${n-5}\n          L 5 ${n-20} L 5 20\n        `},null),w("path",{fill:"transparent","stroke-width":"3","stroke-linecap":"round","stroke-dasharray":"10, 5",stroke:r[0],d:"M 16 9 L 61 9"},null),w("path",{fill:"transparent",stroke:r[1],d:"M 5 20 L 5 10 L 12 3  L 60 3 L 68 10"},null),w("path",{fill:"transparent",stroke:r[1],d:`M ${t-5} ${n-30} L ${t-5} ${n-5} L ${t-30} ${n-5}`},null)]),w("div",{class:"border-box-content"},[s(e,"default")])])}}),Ku={install(e){e.component("DvBorderBox13",Ju)}},Zu={install(e){var t,n,r,i,a,o,l,u,s,f,c,d,p,h,g,v,m,y,b,x,C,$,w,k,P,A,O,L,I,S,M,j,F,_,G,E,B,D,W;null==(t=be.install)||t.call(be,e),null==(n=He.install)||n.call(He,e),null==(r=Ze.install)||r.call(Ze,e),null==(i=_n.install)||i.call(_n,e),null==(a=Nn.install)||a.call(Nn,e),null==(o=ur.install)||o.call(ur,e),null==(l=Mr.install)||l.call(Mr,e),null==(u=Tr.install)||u.call(Tr,e),null==(s=Ur.install)||s.call(Ur,e),null==(f=wa.install)||f.call(wa,e),null==(c=ja.install)||c.call(ja,e),null==(d=Wa.install)||d.call(Wa,e),null==(p=Na.install)||p.call(Na,e),null==(h=Ra.install)||h.call(Ra,e),null==(g=to.install)||g.call(to,e),null==(v=uo.install)||v.call(uo,e),null==(m=ho.install)||m.call(ho,e),null==(y=bo.install)||y.call(bo,e),null==(b=Ao.install)||b.call(Ao,e),null==(x=jo.install)||x.call(jo,e),null==(C=To.install)||C.call(To,e),null==($=Qo.install)||$.call(Qo,e),null==(w=al.install)||w.call(al,e),null==(k=Il.install)||k.call(Il,e),null==(P=Nl.install)||P.call(Nl,e),null==(A=au.install)||A.call(au,e),null==(O=cu.install)||O.call(cu,e),null==(L=hu.install)||L.call(hu,e),null==(I=mu.install)||I.call(mu,e),null==(S=Cu.install)||S.call(Cu,e),null==(M=Pu.install)||M.call(Pu,e),null==(j=Lu.install)||j.call(Lu,e),null==(F=Mu.install)||F.call(Mu,e),null==(_=Gu.install)||_.call(Gu,e),null==(G=Du.install)||G.call(Du,e),null==(E=Ru.install)||E.call(Ru,e),null==(B=Vu.install)||B.call(Vu,e),null==(D=Yu.install)||D.call(Yu,e),null==(W=Ku.install)||W.call(Ku,e)}};export{Zu as M};
