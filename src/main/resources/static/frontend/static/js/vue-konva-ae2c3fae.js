import{k as e,h as n}from"./@babel-f3c0a00c.js";import{r as t}from"./vue-5bfa3a54.js";import{r}from"./konva-5c630d74.js";var o={exports:{}};
/*!
 * vue-konva v3.0.1 - https://github.com/konvajs/vue-konva#readme
 * MIT Licensed
 */const a=n(o.exports=function(e,n){return function(e){var n={};function t(r){if(n[r])return n[r].exports;var o=n[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,t),o.l=!0,o.exports}return t.m=e,t.c=n,t.d=function(e,n,r){t.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:r})},t.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.t=function(e,n){if(1&n&&(e=t(e)),8&n)return e;if(4&n&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(t.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&n&&"string"!=typeof e)for(var o in e)t.d(r,o,function(n){return e[n]}.bind(null,o));return r},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},t.p="",t(t.s=2)}([function(n,t){n.exports=e},function(e,t){e.exports=n},function(e,n,t){e.exports=t(3)},function(e,n,t){t.r(n);var r=t(0),o=t(1),a=t.n(o);function u(e){if(!a.a.autoDrawEnabled){var n=e.getLayer()||e.getStage();n&&n.batchDraw()}}var c={key:!0,style:!0,elm:!0,isRootInsert:!0},i=".vue-konva-event";function f(e,n,t,r){void 0===n&&(n={}),void 0===t&&(t={});var o=e.__konvaNode,a={},f=!1;for(var s in t)if(!c[s]){var p="on"===s.slice(0,2),l=t[s]!==n[s];if(p&&l){var v=s.substr(2).toLowerCase();"content"===v.substr(0,7)&&(v="content"+v.substr(7,1).toUpperCase()+v.substr(8)),o.off(v+i,t[s])}!n.hasOwnProperty(s)&&o.setAttr(s,void 0)}for(var d in n)if(!c[d]){var b="on"===d.slice(0,2),g=t[d]!==n[d];if(b&&g){var h=d.substr(2).toLowerCase();"content"===h.substr(0,7)&&(h="content"+h.substr(7,1).toUpperCase()+h.substr(8)),n[d]&&(o.off(h+i),o.on(h+i,n[d]))}!b&&(n[d]!==t[d]||r&&n[d]!==o.getAttr(d))&&(f=!0,a[d]=n[d])}f&&(o.setAttrs(a),u(o))}function s(e){if(null==e?void 0:e.component)return e.component.__konvaNode||s(e.component.subTree)}function p(e){var n=e.el,t=e.component,r=s(e);return(null==n?void 0:n.tagName)&&t&&!r?(n&&n.tagName.toLowerCase(),null):r}function l(e,n){var t,r,o=(r=[],(t=e).children&&t.children.forEach((function(e){!e.component&&Array.isArray(e.children)&&e.children.forEach((function(e){!e.component&&Array.isArray(e.children)?r.push.apply(r,e.children):r.push(e)})),e.component&&r.push(e)})),r),a=[];o.forEach((function(e){var n=p(e);n&&a.push(n)}));var c=!1;a.forEach((function(e,n){e.getZIndex()!==n&&(e.setZIndex(n),c=!0)})),c&&u(n)}function v(){return v=Object.assign||function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},v.apply(this,arguments)}var d={props:{config:{type:Object,default:function(){return{}}},__useStrictMode:{type:Boolean}},inheritAttrs:!1,setup:function(e,n){var t=n.attrs,o=n.slots,a=n.expose;n.emits;var u=Object(r.getCurrentInstance)(),c=Object(r.reactive)({}),i=Object(r.ref)(null),s=new window.Konva.Stage({width:e.config.width,height:e.config.height,container:document.createElement("div")});function p(){var n=c||{},r=v({},t,e.config);f(u,r,n,e.__useStrictMode),Object.assign(c,r)}return u.__konvaNode=s,p(),Object(r.onMounted)((function(){i.value.innerHTML="",s.container(i.value),p()})),Object(r.onUpdated)((function(){p(),l(u.subTree,s)})),Object(r.onBeforeUnmount)((function(){s.destroy()})),Object(r.watch)((function(){return e.config}),p,{deep:!0}),a({getStage:function(){return u.__konvaNode},getNode:function(){return u.__konvaNode}}),function(){var e;return Object(r.h)("div",{ref:i},null===(e=o.default)||void 0===e?void 0:e.call(o))}}};function b(){return b=Object.assign||function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},b.apply(this,arguments)}var g={Group:!0,Layer:!0,FastLayer:!0,Label:!0};"undefined"==typeof window||window.Konva||t(1);var h=[{name:"Stage",component:d}].concat(["Layer","FastLayer","Group","Label","Rect","Circle","Ellipse","Wedge","Line","Sprite","Image","Text","TextPath","Star","Ring","Arc","Tag","Path","RegularPolygon","Arrow","Shape","Transformer"].map((function(e){return{name:e,component:(n=e,{props:{config:{type:Object,default:function(){return{}}},__useStrictMode:{type:Boolean}},setup:function(e,t){var o=t.attrs,a=t.slots,c=t.expose,i=Object(r.getCurrentInstance)(),s=Object(r.reactive)({}),p=window.Konva[n];if(p){var v=new p;return i.__konvaNode=v,i.vnode.__konvaNode=v,d(),Object(r.onMounted)((function(){var e=function(e){return function e(n){return n.__konvaNode?n:n.parent?e(n.parent):{}}(e.parent)}(i).__konvaNode;e.add(v),u(v)})),Object(r.onUnmounted)((function(){u(v),v.destroy(),v.off(".vue-konva-event")})),Object(r.onUpdated)((function(){d(),l(i.subTree,v)})),Object(r.watch)((function(){return e.config}),d,{deep:!0}),c({getStage:function(){return i.__konvaNode},getNode:function(){return i.__konvaNode}}),g[n]?function(){var e;return Object(r.h)("template",{},null===(e=a.default)||void 0===e?void 0:e.call(a))}:function(){return null}}function d(){var n={};for(var t in i.vnode.props)"on"===t.slice(0,2)&&(n[t]=i.vnode.props[t]);var r=s||{},a=b({},o,e.config,n);f(i,a,r,e.__useStrictMode),Object.assign(s,a)}}})};var n}))),y={install:function(e,n){var t="v";n&&n.prefix&&(t=n.prefix),h.forEach((function(n){e.component(""+t+n.name,n.component)}))}};n.default=y}]).default}(t,r()));export{a as V};
