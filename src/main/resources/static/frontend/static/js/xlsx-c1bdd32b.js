/*! xlsx.js (C) 2013-present SheetJS -- http://sheetjs.com */
var e={version:"0.18.5"},t=1252,r=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],n=function(e){-1!=r.indexOf(e)&&(t=e)};var a=function(e){n(e)};function i(){a(1200),n(1252)}var s,o=function(e){return String.fromCharCode(e)},l=function(e){return String.fromCharCode(e)},f="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function c(e){for(var t="",r=0,n=0,a=0,i=0,s=0,o=0,l=0,c=0;c<e.length;)i=(r=e.charCodeAt(c++))>>2,s=(3&r)<<4|(n=e.charCodeAt(c++))>>4,o=(15&n)<<2|(a=e.charCodeAt(c++))>>6,l=63&a,isNaN(n)?o=l=64:isNaN(a)&&(l=64),t+=f.charAt(i)+f.charAt(s)+f.charAt(o)+f.charAt(l);return t}function h(e){var t="",r=0,n=0,a=0,i=0,s=0,o=0;e=e.replace(/[^\w\+\/\=]/g,"");for(var l=0;l<e.length;)r=f.indexOf(e.charAt(l++))<<2|(i=f.indexOf(e.charAt(l++)))>>4,t+=String.fromCharCode(r),n=(15&i)<<4|(s=f.indexOf(e.charAt(l++)))>>2,64!==s&&(t+=String.fromCharCode(n)),a=(3&s)<<6|(o=f.indexOf(e.charAt(l++))),64!==o&&(t+=String.fromCharCode(a));return t}var u=function(){return"undefined"!=typeof Buffer&&"undefined"!=typeof process&&void 0!==process.versions&&!!process.versions.node}(),p=function(){if("undefined"!=typeof Buffer){var e=!Buffer.from;if(!e)try{Buffer.from("foo","utf8")}catch(t){e=!0}return e?function(e,t){return t?new Buffer(e,t):new Buffer(e)}:Buffer.from.bind(Buffer)}return function(){}}();function d(e){return u?Buffer.alloc?Buffer.alloc(e):new Buffer(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):new Array(e)}function m(e){return u?Buffer.allocUnsafe?Buffer.allocUnsafe(e):new Buffer(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):new Array(e)}var g=function(e){return u?p(e,"binary"):e.split("").map((function(e){return 255&e.charCodeAt(0)}))};function v(e){if("undefined"==typeof ArrayBuffer)return g(e);for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),n=0;n!=e.length;++n)r[n]=255&e.charCodeAt(n);return t}function T(e){if(Array.isArray(e))return e.map((function(e){return String.fromCharCode(e)})).join("");for(var t=[],r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}var w=u?function(e){return Buffer.concat(e.map((function(e){return Buffer.isBuffer(e)?e:p(e)})))}:function(e){if("undefined"!=typeof Uint8Array){var t=0,r=0;for(t=0;t<e.length;++t)r+=e[t].length;var n=new Uint8Array(r),a=0;for(t=0,r=0;t<e.length;r+=a,++t)if(a=e[t].length,e[t]instanceof Uint8Array)n.set(e[t],r);else{if("string"==typeof e[t])throw"wtf";n.set(new Uint8Array(e[t]),r)}return n}return[].concat.apply([],e.map((function(e){return Array.isArray(e)?e:[].slice.call(e)})))};var E=/\u0000/g,b=/[\u0001-\u0006]/g;function S(e){for(var t="",r=e.length-1;r>=0;)t+=e.charAt(r--);return t}function A(e,t){var r=""+e;return r.length>=t?r:Le("0",t-r.length)+r}function _(e,t){var r=""+e;return r.length>=t?r:Le(" ",t-r.length)+r}function y(e,t){var r=""+e;return r.length>=t?r:r+Le(" ",t-r.length)}var O=Math.pow(2,32);function x(e,t){return e>O||e<-O?function(e,t){var r=""+Math.round(e);return r.length>=t?r:Le("0",t-r.length)+r}(e,t):function(e,t){var r=""+e;return r.length>=t?r:Le("0",t-r.length)+r}(Math.round(e),t)}function C(e,t){return t=t||0,e.length>=7+t&&103==(32|e.charCodeAt(t))&&101==(32|e.charCodeAt(t+1))&&110==(32|e.charCodeAt(t+2))&&101==(32|e.charCodeAt(t+3))&&114==(32|e.charCodeAt(t+4))&&97==(32|e.charCodeAt(t+5))&&108==(32|e.charCodeAt(t+6))}var R=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],N=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]];var k={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"上午/下午 "hh"時"mm"分"ss"秒 "'},I={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},D={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function P(e,t,r){for(var n=e<0?-1:1,a=e*n,i=0,s=1,o=0,l=1,f=0,c=0,h=Math.floor(a);f<t&&(o=(h=Math.floor(a))*s+i,c=h*f+l,!(a-h<5e-8));)a=1/(a-h),i=s,s=o,l=f,f=c;if(c>t&&(f>t?(c=l,o=i):(c=f,o=s)),!r)return[0,n*o,c];var u=Math.floor(n*o/c);return[u,n*o-u*c,c]}function L(e,t,r){if(e>2958465||e<0)return null;var n=0|e,a=Math.floor(86400*(e-n)),i=0,s=[],o={D:n,T:a,u:86400*(e-n)-a,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(Math.abs(o.u)<1e-6&&(o.u=0),t&&t.date1904&&(n+=1462),o.u>.9999&&(o.u=0,86400==++a&&(o.T=a=0,++n,++o.D)),60===n)s=r?[1317,10,29]:[1900,2,29],i=3;else if(0===n)s=r?[1317,8,29]:[1900,1,0],i=6;else{n>60&&--n;var l=new Date(1900,0,1);l.setDate(l.getDate()+n-1),s=[l.getFullYear(),l.getMonth()+1,l.getDate()],i=l.getDay(),n<60&&(i=(i+6)%7),r&&(i=function(e,t){t[0]-=581;var r=e.getDay();e<60&&(r=(r+6)%7);return r}(l,s))}return o.y=s[0],o.m=s[1],o.d=s[2],o.S=a%60,a=Math.floor(a/60),o.M=a%60,a=Math.floor(a/60),o.H=a,o.q=i,o}var M=new Date(1899,11,31,0,0,0),F=M.getTime(),U=new Date(1900,2,1,0,0,0);function B(e,t){var r=e.getTime();return t?r-=1262304e5:e>=U&&(r+=864e5),(r-(F+6e4*(e.getTimezoneOffset()-M.getTimezoneOffset())))/864e5}function W(e){return-1==e.indexOf(".")?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function H(e){var t,r=Math.floor(Math.log(Math.abs(e))*Math.LOG10E);return t=r>=-4&&r<=-1?e.toPrecision(10+r):Math.abs(r)<=9?function(e){var t=e<0?12:11,r=W(e.toFixed(12));return r.length<=t||(r=e.toPrecision(10)).length<=t?r:e.toExponential(5)}(e):10===r?e.toFixed(10).substr(0,12):function(e){var t=W(e.toFixed(11));return t.length>(e<0?12:11)||"0"===t||"-0"===t?e.toPrecision(6):t}(e),W(function(e){return-1==e.indexOf("E")?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2")}(t.toUpperCase()))}function G(e,t){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(0|e)===e?e.toString(10):H(e);case"undefined":return"";case"object":if(null==e)return"";if(e instanceof Date)return ce(14,B(e,t&&t.date1904),t)}throw new Error("unsupported value in General format: "+e)}function V(e,t,r,n){var a,i="",s=0,o=0,l=r.y,f=0;switch(e){case 98:l=r.y+543;case 121:switch(t.length){case 1:case 2:a=l%100,f=2;break;default:a=l%1e4,f=4}break;case 109:switch(t.length){case 1:case 2:a=r.m,f=t.length;break;case 3:return N[r.m-1][1];case 5:return N[r.m-1][0];default:return N[r.m-1][2]}break;case 100:switch(t.length){case 1:case 2:a=r.d,f=t.length;break;case 3:return R[r.q][0];default:return R[r.q][1]}break;case 104:switch(t.length){case 1:case 2:a=1+(r.H+11)%12,f=t.length;break;default:throw"bad hour format: "+t}break;case 72:switch(t.length){case 1:case 2:a=r.H,f=t.length;break;default:throw"bad hour format: "+t}break;case 77:switch(t.length){case 1:case 2:a=r.M,f=t.length;break;default:throw"bad minute format: "+t}break;case 115:if("s"!=t&&"ss"!=t&&".0"!=t&&".00"!=t&&".000"!=t)throw"bad second format: "+t;return 0!==r.u||"s"!=t&&"ss"!=t?(o=n>=2?3===n?1e3:100:1===n?10:1,(s=Math.round(o*(r.S+r.u)))>=60*o&&(s=0),"s"===t?0===s?"0":""+s/o:(i=A(s,2+n),"ss"===t?i.substr(0,2):"."+i.substr(2,t.length-1))):A(r.S,t.length);case 90:switch(t){case"[h]":case"[hh]":a=24*r.D+r.H;break;case"[m]":case"[mm]":a=60*(24*r.D+r.H)+r.M;break;case"[s]":case"[ss]":a=60*(60*(24*r.D+r.H)+r.M)+Math.round(r.S+r.u);break;default:throw"bad abstime format: "+t}f=3===t.length?1:2;break;case 101:a=l,f=1}return f>0?A(a,f):""}function j(e){if(e.length<=3)return e;for(var t=e.length%3,r=e.substr(0,t);t!=e.length;t+=3)r+=(r.length>0?",":"")+e.substr(t,3);return r}var z=/%/g;function X(e,t){var r,n=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(0==t)return"0.0E+0";if(t<0)return"-"+X(e,-t);var a=e.indexOf(".");-1===a&&(a=e.indexOf("E"));var i=Math.floor(Math.log(t)*Math.LOG10E)%a;if(i<0&&(i+=a),-1===(r=(t/Math.pow(10,i)).toPrecision(n+1+(a+i)%a)).indexOf("e")){var s=Math.floor(Math.log(t)*Math.LOG10E);for(-1===r.indexOf(".")?r=r.charAt(0)+"."+r.substr(1)+"E+"+(s-r.length+i):r+="E+"+(s-i);"0."===r.substr(0,2);)r=(r=r.charAt(0)+r.substr(2,a)+"."+r.substr(2+a)).replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,(function(e,t,r,n){return t+r+n.substr(0,(a+i)%a)+"."+n.substr(i)+"E"}))}else r=t.toExponential(n);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}var Y=/# (\?+)( ?)\/( ?)(\d+)/;var K=/^#*0*\.([0#]+)/,J=/\).*[0#]/,Z=/\(###\) ###\\?-####/;function q(e){for(var t,r="",n=0;n!=e.length;++n)switch(t=e.charCodeAt(n)){case 35:break;case 63:r+=" ";break;case 48:r+="0";break;default:r+=String.fromCharCode(t)}return r}function Q(e,t){var r=Math.pow(10,t);return""+Math.round(e*r)/r}function ee(e,t){var r=e-Math.floor(e),n=Math.pow(10,t);return t<(""+Math.round(r*n)).length?0:Math.round(r*n)}function te(e,t,r){if(40===e.charCodeAt(0)&&!t.match(J)){var n=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?te("n",n,r):"("+te("n",n,-r)+")"}if(44===t.charCodeAt(t.length-1))return function(e,t,r){for(var n=t.length-1;44===t.charCodeAt(n-1);)--n;return ae(e,t.substr(0,n),r/Math.pow(10,3*(t.length-n)))}(e,t,r);if(-1!==t.indexOf("%"))return function(e,t,r){var n=t.replace(z,""),a=t.length-n.length;return ae(e,n,r*Math.pow(10,2*a))+Le("%",a)}(e,t,r);if(-1!==t.indexOf("E"))return X(t,r);if(36===t.charCodeAt(0))return"$"+te(e,t.substr(" "==t.charAt(1)?2:1),r);var a,i,s,o,l=Math.abs(r),f=r<0?"-":"";if(t.match(/^00+$/))return f+x(l,t.length);if(t.match(/^[#?]+$/))return"0"===(a=x(r,0))&&(a=""),a.length>t.length?a:q(t.substr(0,t.length-a.length))+a;if(i=t.match(Y))return function(e,t,r){var n=parseInt(e[4],10),a=Math.round(t*n),i=Math.floor(a/n),s=a-i*n,o=n;return r+(0===i?"":""+i)+" "+(0===s?Le(" ",e[1].length+1+e[4].length):_(s,e[1].length)+e[2]+"/"+e[3]+A(o,e[4].length))}(i,l,f);if(t.match(/^#+0+$/))return f+x(l,t.length-t.indexOf("0"));if(i=t.match(K))return a=Q(r,i[1].length).replace(/^([^\.]+)$/,"$1."+q(i[1])).replace(/\.$/,"."+q(i[1])).replace(/\.(\d*)$/,(function(e,t){return"."+t+Le("0",q(i[1]).length-t.length)})),-1!==t.indexOf("0.")?a:a.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),i=t.match(/^(0*)\.(#*)$/))return f+Q(l,i[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,i[1].length?"0.":".");if(i=t.match(/^#{1,3},##0(\.?)$/))return f+j(x(l,0));if(i=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+te(e,t,-r):j(""+(Math.floor(r)+function(e,t){return t<(""+Math.round((e-Math.floor(e))*Math.pow(10,t))).length?1:0}(r,i[1].length)))+"."+A(ee(r,i[1].length),i[1].length);if(i=t.match(/^#,#*,#0/))return te(e,t.replace(/^#,#*,/,""),r);if(i=t.match(/^([0#]+)(\\?-([0#]+))+$/))return a=S(te(e,t.replace(/[\\-]/g,""),r)),s=0,S(S(t.replace(/\\/g,"")).replace(/[0#]/g,(function(e){return s<a.length?a.charAt(s++):"0"===e?"0":""})));if(t.match(Z))return"("+(a=te(e,"##########",r)).substr(0,3)+") "+a.substr(3,3)+"-"+a.substr(6);var c="";if(i=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(i[4].length,7),o=P(l,Math.pow(10,s)-1,!1),a=""+f," "==(c=ae("n",i[1],o[1])).charAt(c.length-1)&&(c=c.substr(0,c.length-1)+"0"),a+=c+i[2]+"/"+i[3],(c=y(o[2],s)).length<i[4].length&&(c=q(i[4].substr(i[4].length-c.length))+c),a+=c;if(i=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(Math.max(i[1].length,i[4].length),7),f+((o=P(l,Math.pow(10,s)-1,!0))[0]||(o[1]?"":"0"))+" "+(o[1]?_(o[1],s)+i[2]+"/"+i[3]+y(o[2],s):Le(" ",2*s+1+i[2].length+i[3].length));if(i=t.match(/^[#0?]+$/))return a=x(r,0),t.length<=a.length?a:q(t.substr(0,t.length-a.length))+a;if(i=t.match(/^([#0?]+)\.([#0]+)$/)){a=""+r.toFixed(Math.min(i[2].length,10)).replace(/([^0])0+$/,"$1"),s=a.indexOf(".");var h=t.indexOf(".")-s,u=t.length-a.length-h;return q(t.substr(0,h)+a+t.substr(t.length-u))}if(i=t.match(/^00,000\.([#0]*0)$/))return s=ee(r,i[1].length),r<0?"-"+te(e,t,-r):j(function(e){return e<2147483647&&e>-2147483648?""+(e>=0?0|e:e-1|0):""+Math.floor(e)}(r)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,(function(e){return"00,"+(e.length<3?A(0,3-e.length):"")+e}))+"."+A(s,i[1].length);switch(t){case"###,##0.00":return te(e,"#,##0.00",r);case"###,###":case"##,###":case"#,###":var p=j(x(l,0));return"0"!==p?f+p:"";case"###,###.00":return te(e,"###,##0.00",r).replace(/^0\./,".");case"#,###.00":return te(e,"#,##0.00",r).replace(/^0\./,".")}throw new Error("unsupported format |"+t+"|")}function re(e,t){var r,n=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(0==t)return"0.0E+0";if(t<0)return"-"+re(e,-t);var a=e.indexOf(".");-1===a&&(a=e.indexOf("E"));var i=Math.floor(Math.log(t)*Math.LOG10E)%a;if(i<0&&(i+=a),!(r=(t/Math.pow(10,i)).toPrecision(n+1+(a+i)%a)).match(/[Ee]/)){var s=Math.floor(Math.log(t)*Math.LOG10E);-1===r.indexOf(".")?r=r.charAt(0)+"."+r.substr(1)+"E+"+(s-r.length+i):r+="E+"+(s-i),r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,(function(e,t,r,n){return t+r+n.substr(0,(a+i)%a)+"."+n.substr(i)+"E"}))}else r=t.toExponential(n);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}function ne(e,t,r){if(40===e.charCodeAt(0)&&!t.match(J)){var n=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?ne("n",n,r):"("+ne("n",n,-r)+")"}if(44===t.charCodeAt(t.length-1))return function(e,t,r){for(var n=t.length-1;44===t.charCodeAt(n-1);)--n;return ae(e,t.substr(0,n),r/Math.pow(10,3*(t.length-n)))}(e,t,r);if(-1!==t.indexOf("%"))return function(e,t,r){var n=t.replace(z,""),a=t.length-n.length;return ae(e,n,r*Math.pow(10,2*a))+Le("%",a)}(e,t,r);if(-1!==t.indexOf("E"))return re(t,r);if(36===t.charCodeAt(0))return"$"+ne(e,t.substr(" "==t.charAt(1)?2:1),r);var a,i,s,o,l=Math.abs(r),f=r<0?"-":"";if(t.match(/^00+$/))return f+A(l,t.length);if(t.match(/^[#?]+$/))return a=""+r,0===r&&(a=""),a.length>t.length?a:q(t.substr(0,t.length-a.length))+a;if(i=t.match(Y))return function(e,t,r){return r+(0===t?"":""+t)+Le(" ",e[1].length+2+e[4].length)}(i,l,f);if(t.match(/^#+0+$/))return f+A(l,t.length-t.indexOf("0"));if(i=t.match(K))return a=(a=(""+r).replace(/^([^\.]+)$/,"$1."+q(i[1])).replace(/\.$/,"."+q(i[1]))).replace(/\.(\d*)$/,(function(e,t){return"."+t+Le("0",q(i[1]).length-t.length)})),-1!==t.indexOf("0.")?a:a.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),i=t.match(/^(0*)\.(#*)$/))return f+(""+l).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,i[1].length?"0.":".");if(i=t.match(/^#{1,3},##0(\.?)$/))return f+j(""+l);if(i=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+ne(e,t,-r):j(""+r)+"."+Le("0",i[1].length);if(i=t.match(/^#,#*,#0/))return ne(e,t.replace(/^#,#*,/,""),r);if(i=t.match(/^([0#]+)(\\?-([0#]+))+$/))return a=S(ne(e,t.replace(/[\\-]/g,""),r)),s=0,S(S(t.replace(/\\/g,"")).replace(/[0#]/g,(function(e){return s<a.length?a.charAt(s++):"0"===e?"0":""})));if(t.match(Z))return"("+(a=ne(e,"##########",r)).substr(0,3)+") "+a.substr(3,3)+"-"+a.substr(6);var c="";if(i=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(i[4].length,7),o=P(l,Math.pow(10,s)-1,!1),a=""+f," "==(c=ae("n",i[1],o[1])).charAt(c.length-1)&&(c=c.substr(0,c.length-1)+"0"),a+=c+i[2]+"/"+i[3],(c=y(o[2],s)).length<i[4].length&&(c=q(i[4].substr(i[4].length-c.length))+c),a+=c;if(i=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(Math.max(i[1].length,i[4].length),7),f+((o=P(l,Math.pow(10,s)-1,!0))[0]||(o[1]?"":"0"))+" "+(o[1]?_(o[1],s)+i[2]+"/"+i[3]+y(o[2],s):Le(" ",2*s+1+i[2].length+i[3].length));if(i=t.match(/^[#0?]+$/))return a=""+r,t.length<=a.length?a:q(t.substr(0,t.length-a.length))+a;if(i=t.match(/^([#0]+)\.([#0]+)$/)){a=""+r.toFixed(Math.min(i[2].length,10)).replace(/([^0])0+$/,"$1"),s=a.indexOf(".");var h=t.indexOf(".")-s,u=t.length-a.length-h;return q(t.substr(0,h)+a+t.substr(t.length-u))}if(i=t.match(/^00,000\.([#0]*0)$/))return r<0?"-"+ne(e,t,-r):j(""+r).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,(function(e){return"00,"+(e.length<3?A(0,3-e.length):"")+e}))+"."+A(0,i[1].length);switch(t){case"###,###":case"##,###":case"#,###":var p=j(""+l);return"0"!==p?f+p:"";default:if(t.match(/\.[0#?]*$/))return ne(e,t.slice(0,t.lastIndexOf(".")),r)+q(t.slice(t.lastIndexOf(".")))}throw new Error("unsupported format |"+t+"|")}function ae(e,t,r){return(0|r)===r?ne(e,t,r):te(e,t,r)}var ie=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function se(e){for(var t=0,r="",n="";t<e.length;)switch(r=e.charAt(t)){case"G":C(e,t)&&(t+=6),t++;break;case'"':for(;34!==e.charCodeAt(++t)&&t<e.length;);++t;break;case"\\":case"_":t+=2;break;case"@":++t;break;case"B":case"b":if("1"===e.charAt(t+1)||"2"===e.charAt(t+1))return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"上":if("A/P"===e.substr(t,3).toUpperCase())return!0;if("AM/PM"===e.substr(t,5).toUpperCase())return!0;if("上午/下午"===e.substr(t,5).toUpperCase())return!0;++t;break;case"[":for(n=r;"]"!==e.charAt(t++)&&t<e.length;)n+=e.charAt(t);if(n.match(ie))return!0;break;case".":case"0":case"#":for(;t<e.length&&("0#?.,E+-%".indexOf(r=e.charAt(++t))>-1||"\\"==r&&"-"==e.charAt(t+1)&&"0#".indexOf(e.charAt(t+2))>-1););break;case"?":for(;e.charAt(++t)===r;);break;case"*":++t," "!=e.charAt(t)&&"*"!=e.charAt(t)||++t;break;case"(":case")":++t;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;t<e.length&&"0123456789".indexOf(e.charAt(++t))>-1;);break;default:++t}return!1}var oe=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function le(e,t){if(null==t)return!1;var r=parseFloat(t[2]);switch(t[1]){case"=":if(e==r)return!0;break;case">":if(e>r)return!0;break;case"<":if(e<r)return!0;break;case"<>":if(e!=r)return!0;break;case">=":if(e>=r)return!0;break;case"<=":if(e<=r)return!0}return!1}function fe(e,t){var r=function(e){for(var t=[],r=!1,n=0,a=0;n<e.length;++n)switch(e.charCodeAt(n)){case 34:r=!r;break;case 95:case 42:case 92:++n;break;case 59:t[t.length]=e.substr(a,n-a),a=n+1}if(t[t.length]=e.substr(a),!0===r)throw new Error("Format |"+e+"| unterminated string ");return t}(e),n=r.length,a=r[n-1].indexOf("@");if(n<4&&a>-1&&--n,r.length>4)throw new Error("cannot find right format for |"+r.join("|")+"|");if("number"!=typeof t)return[4,4===r.length||a>-1?r[r.length-1]:"@"];switch(r.length){case 1:r=a>-1?["General","General","General",r[0]]:[r[0],r[0],r[0],"@"];break;case 2:r=a>-1?[r[0],r[0],r[0],r[1]]:[r[0],r[1],r[0],"@"];break;case 3:r=a>-1?[r[0],r[1],r[0],r[2]]:[r[0],r[1],r[2],"@"]}var i=t>0?r[0]:t<0?r[1]:r[2];if(-1===r[0].indexOf("[")&&-1===r[1].indexOf("["))return[n,i];if(null!=r[0].match(/\[[=<>]/)||null!=r[1].match(/\[[=<>]/)){var s=r[0].match(oe),o=r[1].match(oe);return le(t,s)?[n,r[0]]:le(t,o)?[n,r[1]]:[n,r[null!=s&&null!=o?2:1]]}return[n,i]}function ce(e,t,r){null==r&&(r={});var n="";switch(typeof e){case"string":n="m/d/yy"==e&&r.dateNF?r.dateNF:e;break;case"number":null==(n=14==e&&r.dateNF?r.dateNF:(null!=r.table?r.table:k)[e])&&(n=r.table&&r.table[I[e]]||k[I[e]]),null==n&&(n=D[e]||"General")}if(C(n,0))return G(t,r);t instanceof Date&&(t=B(t,r.date1904));var a=fe(n,t);if(C(a[1]))return G(t,r);if(!0===t)t="TRUE";else if(!1===t)t="FALSE";else if(""===t||null==t)return"";return function(e,t,r,n){for(var a,i,s,o=[],l="",f=0,c="",h="t",u="H";f<e.length;)switch(c=e.charAt(f)){case"G":if(!C(e,f))throw new Error("unrecognized character "+c+" in "+e);o[o.length]={t:"G",v:"General"},f+=7;break;case'"':for(l="";34!==(s=e.charCodeAt(++f))&&f<e.length;)l+=String.fromCharCode(s);o[o.length]={t:"t",v:l},++f;break;case"\\":var p=e.charAt(++f),d="("===p||")"===p?p:"t";o[o.length]={t:d,v:p},++f;break;case"_":o[o.length]={t:"t",v:" "},f+=2;break;case"@":o[o.length]={t:"T",v:t},++f;break;case"B":case"b":if("1"===e.charAt(f+1)||"2"===e.charAt(f+1)){if(null==a&&null==(a=L(t,r,"2"===e.charAt(f+1))))return"";o[o.length]={t:"X",v:e.substr(f,2)},h=c,f+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":c=c.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(t<0)return"";if(null==a&&null==(a=L(t,r)))return"";for(l=c;++f<e.length&&e.charAt(f).toLowerCase()===c;)l+=c;"m"===c&&"h"===h.toLowerCase()&&(c="M"),"h"===c&&(c=u),o[o.length]={t:c,v:l},h=c;break;case"A":case"a":case"上":var m={t:c,v:c};if(null==a&&(a=L(t,r)),"A/P"===e.substr(f,3).toUpperCase()?(null!=a&&(m.v=a.H>=12?"P":"A"),m.t="T",u="h",f+=3):"AM/PM"===e.substr(f,5).toUpperCase()?(null!=a&&(m.v=a.H>=12?"PM":"AM"),m.t="T",f+=5,u="h"):"上午/下午"===e.substr(f,5).toUpperCase()?(null!=a&&(m.v=a.H>=12?"下午":"上午"),m.t="T",f+=5,u="h"):(m.t="t",++f),null==a&&"T"===m.t)return"";o[o.length]=m,h=c;break;case"[":for(l=c;"]"!==e.charAt(f++)&&f<e.length;)l+=e.charAt(f);if("]"!==l.slice(-1))throw'unterminated "[" block: |'+l+"|";if(l.match(ie)){if(null==a&&null==(a=L(t,r)))return"";o[o.length]={t:"Z",v:l.toLowerCase()},h=l.charAt(1)}else l.indexOf("$")>-1&&(l=(l.match(/\$([^-\[\]]*)/)||[])[1]||"$",se(e)||(o[o.length]={t:"t",v:l}));break;case".":if(null!=a){for(l=c;++f<e.length&&"0"===(c=e.charAt(f));)l+=c;o[o.length]={t:"s",v:l};break}case"0":case"#":for(l=c;++f<e.length&&"0#?.,E+-%".indexOf(c=e.charAt(f))>-1;)l+=c;o[o.length]={t:"n",v:l};break;case"?":for(l=c;e.charAt(++f)===c;)l+=c;o[o.length]={t:c,v:l},h=c;break;case"*":++f," "!=e.charAt(f)&&"*"!=e.charAt(f)||++f;break;case"(":case")":o[o.length]={t:1===n?"t":c,v:c},++f;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(l=c;f<e.length&&"0123456789".indexOf(e.charAt(++f))>-1;)l+=e.charAt(f);o[o.length]={t:"D",v:l};break;case" ":o[o.length]={t:c,v:c},++f;break;case"$":o[o.length]={t:"t",v:"$"},++f;break;default:if(-1===",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(c))throw new Error("unrecognized character "+c+" in "+e);o[o.length]={t:"t",v:c},++f}var g,v=0,T=0;for(f=o.length-1,h="t";f>=0;--f)switch(o[f].t){case"h":case"H":o[f].t=u,h="h",v<1&&(v=1);break;case"s":(g=o[f].v.match(/\.0+$/))&&(T=Math.max(T,g[0].length-1)),v<3&&(v=3);case"d":case"y":case"M":case"e":h=o[f].t;break;case"m":"s"===h&&(o[f].t="M",v<2&&(v=2));break;case"X":break;case"Z":v<1&&o[f].v.match(/[Hh]/)&&(v=1),v<2&&o[f].v.match(/[Mm]/)&&(v=2),v<3&&o[f].v.match(/[Ss]/)&&(v=3)}switch(v){case 0:break;case 1:a.u>=.5&&(a.u=0,++a.S),a.S>=60&&(a.S=0,++a.M),a.M>=60&&(a.M=0,++a.H);break;case 2:a.u>=.5&&(a.u=0,++a.S),a.S>=60&&(a.S=0,++a.M)}var w,E="";for(f=0;f<o.length;++f)switch(o[f].t){case"t":case"T":case" ":case"D":break;case"X":o[f].v="",o[f].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":o[f].v=V(o[f].t.charCodeAt(0),o[f].v,a,T),o[f].t="t";break;case"n":case"?":for(w=f+1;null!=o[w]&&("?"===(c=o[w].t)||"D"===c||(" "===c||"t"===c)&&null!=o[w+1]&&("?"===o[w+1].t||"t"===o[w+1].t&&"/"===o[w+1].v)||"("===o[f].t&&(" "===c||"n"===c||")"===c)||"t"===c&&("/"===o[w].v||" "===o[w].v&&null!=o[w+1]&&"?"==o[w+1].t));)o[f].v+=o[w].v,o[w]={v:"",t:";"},++w;E+=o[f].v,f=w-1;break;case"G":o[f].t="t",o[f].v=G(t,r)}var b,S,A="";if(E.length>0){40==E.charCodeAt(0)?(b=t<0&&45===E.charCodeAt(0)?-t:t,S=ae("n",E,b)):(S=ae("n",E,b=t<0&&n>1?-t:t),b<0&&o[0]&&"t"==o[0].t&&(S=S.substr(1),o[0].v="-"+o[0].v)),w=S.length-1;var _=o.length;for(f=0;f<o.length;++f)if(null!=o[f]&&"t"!=o[f].t&&o[f].v.indexOf(".")>-1){_=f;break}var y=o.length;if(_===o.length&&-1===S.indexOf("E")){for(f=o.length-1;f>=0;--f)null!=o[f]&&-1!=="n?".indexOf(o[f].t)&&(w>=o[f].v.length-1?(w-=o[f].v.length,o[f].v=S.substr(w+1,o[f].v.length)):w<0?o[f].v="":(o[f].v=S.substr(0,w+1),w=-1),o[f].t="t",y=f);w>=0&&y<o.length&&(o[y].v=S.substr(0,w+1)+o[y].v)}else if(_!==o.length&&-1===S.indexOf("E")){for(w=S.indexOf(".")-1,f=_;f>=0;--f)if(null!=o[f]&&-1!=="n?".indexOf(o[f].t)){for(i=o[f].v.indexOf(".")>-1&&f===_?o[f].v.indexOf(".")-1:o[f].v.length-1,A=o[f].v.substr(i+1);i>=0;--i)w>=0&&("0"===o[f].v.charAt(i)||"#"===o[f].v.charAt(i))&&(A=S.charAt(w--)+A);o[f].v=A,o[f].t="t",y=f}for(w>=0&&y<o.length&&(o[y].v=S.substr(0,w+1)+o[y].v),w=S.indexOf(".")+1,f=_;f<o.length;++f)if(null!=o[f]&&(-1!=="n?(".indexOf(o[f].t)||f===_)){for(i=o[f].v.indexOf(".")>-1&&f===_?o[f].v.indexOf(".")+1:0,A=o[f].v.substr(0,i);i<o[f].v.length;++i)w<S.length&&(A+=S.charAt(w++));o[f].v=A,o[f].t="t",y=f}}}for(f=0;f<o.length;++f)null!=o[f]&&"n?".indexOf(o[f].t)>-1&&(b=n>1&&t<0&&f>0&&"-"===o[f-1].v?-t:t,o[f].v=ae(o[f].t,o[f].v,b),o[f].t="t");var O="";for(f=0;f!==o.length;++f)null!=o[f]&&(O+=o[f].v);return O}(a[1],t,r,a[0])}function he(e,t){if("number"!=typeof t){t=+t||-1;for(var r=0;r<392;++r)if(null!=k[r]){if(k[r]==e){t=r;break}}else t<0&&(t=r);t<0&&(t=391)}return k[t]=e,t}function ue(e){for(var t=0;392!=t;++t)void 0!==e[t]&&he(e[t],t)}function pe(){var e;e||(e={}),e[0]="General",e[1]="0",e[2]="0.00",e[3]="#,##0",e[4]="#,##0.00",e[9]="0%",e[10]="0.00%",e[11]="0.00E+00",e[12]="# ?/?",e[13]="# ??/??",e[14]="m/d/yy",e[15]="d-mmm-yy",e[16]="d-mmm",e[17]="mmm-yy",e[18]="h:mm AM/PM",e[19]="h:mm:ss AM/PM",e[20]="h:mm",e[21]="h:mm:ss",e[22]="m/d/yy h:mm",e[37]="#,##0 ;(#,##0)",e[38]="#,##0 ;[Red](#,##0)",e[39]="#,##0.00;(#,##0.00)",e[40]="#,##0.00;[Red](#,##0.00)",e[45]="mm:ss",e[46]="[h]:mm:ss",e[47]="mmss.0",e[48]="##0.0E+0",e[49]="@",e[56]='"上午/下午 "hh"時"mm"分"ss"秒 "',k=e}var de=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g;var me=function(){var e={};e.version="1.2.0";var t=function(){for(var e=0,t=new Array(256),r=0;256!=r;++r)e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=r)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1,t[r]=e;return"undefined"!=typeof Int32Array?new Int32Array(t):t}();var r=function(e){var t=0,r=0,n=0,a="undefined"!=typeof Int32Array?new Int32Array(4096):new Array(4096);for(n=0;256!=n;++n)a[n]=e[n];for(n=0;256!=n;++n)for(r=e[n],t=256+n;t<4096;t+=256)r=a[t]=r>>>8^e[255&r];var i=[];for(n=1;16!=n;++n)i[n-1]="undefined"!=typeof Int32Array?a.subarray(256*n,256*n+256):a.slice(256*n,256*n+256);return i}(t),n=r[0],a=r[1],i=r[2],s=r[3],o=r[4],l=r[5],f=r[6],c=r[7],h=r[8],u=r[9],p=r[10],d=r[11],m=r[12],g=r[13],v=r[14];return e.table=t,e.bstr=function(e,r){for(var n=~r,a=0,i=e.length;a<i;)n=n>>>8^t[255&(n^e.charCodeAt(a++))];return~n},e.buf=function(e,r){for(var T=~r,w=e.length-15,E=0;E<w;)T=v[e[E++]^255&T]^g[e[E++]^T>>8&255]^m[e[E++]^T>>16&255]^d[e[E++]^T>>>24]^p[e[E++]]^u[e[E++]]^h[e[E++]]^c[e[E++]]^f[e[E++]]^l[e[E++]]^o[e[E++]]^s[e[E++]]^i[e[E++]]^a[e[E++]]^n[e[E++]]^t[e[E++]];for(w+=15;E<w;)T=T>>>8^t[255&(T^e[E++])];return~T},e.str=function(e,r){for(var n=~r,a=0,i=e.length,s=0,o=0;a<i;)(s=e.charCodeAt(a++))<128?n=n>>>8^t[255&(n^s)]:s<2048?n=(n=n>>>8^t[255&(n^(192|s>>6&31))])>>>8^t[255&(n^(128|63&s))]:s>=55296&&s<57344?(s=64+(1023&s),o=1023&e.charCodeAt(a++),n=(n=(n=(n=n>>>8^t[255&(n^(240|s>>8&7))])>>>8^t[255&(n^(128|s>>2&63))])>>>8^t[255&(n^(128|o>>6&15|(3&s)<<4))])>>>8^t[255&(n^(128|63&o))]):n=(n=(n=n>>>8^t[255&(n^(224|s>>12&15))])>>>8^t[255&(n^(128|s>>6&63))])>>>8^t[255&(n^(128|63&s))];return~n},e}(),ge=function(){var e,t={};function r(e){if("/"==e.charAt(e.length-1))return-1===e.slice(0,-1).indexOf("/")?e:r(e.slice(0,-1));var t=e.lastIndexOf("/");return-1===t?e:e.slice(0,t+1)}function n(e){if("/"==e.charAt(e.length-1))return n(e.slice(0,-1));var t=e.lastIndexOf("/");return-1===t?e:e.slice(t+1)}function a(e,t){"string"==typeof t&&(t=new Date(t));var r=t.getHours();r=(r=r<<6|t.getMinutes())<<5|t.getSeconds()>>>1,e.write_shift(2,r);var n=t.getFullYear()-1980;n=(n=n<<4|t.getMonth()+1)<<5|t.getDate(),e.write_shift(2,n)}function i(e){Gt(e,0);for(var t={},r=0;e.l<=e.length-4;){var n=e.read_shift(2),a=e.read_shift(2),i=e.l+a,s={};if(21589===n)1&(r=e.read_shift(1))&&(s.mtime=e.read_shift(4)),a>5&&(2&r&&(s.atime=e.read_shift(4)),4&r&&(s.ctime=e.read_shift(4))),s.mtime&&(s.mt=new Date(1e3*s.mtime));e.l=i,t[n]=s}return t}function s(){return e||(e={})}function o(e,t){if(80==e[0]&&75==e[1])return de(e,t);if(109==(32|e[0])&&105==(32|e[1]))return function(e,t){if("mime-version:"!=I(e.slice(0,13)).toLowerCase())throw new Error("Unsupported MAD header");var r=t&&t.root||"",n=(u&&Buffer.isBuffer(e)?e.toString("binary"):I(e)).split("\r\n"),a=0,i="";for(a=0;a<n.length;++a)if(i=n[a],/^Content-Location:/i.test(i)&&(i=i.slice(i.indexOf("file")),r||(r=i.slice(0,i.lastIndexOf("/")+1)),i.slice(0,r.length)!=r))for(;r.length>0&&(r=(r=r.slice(0,r.length-1)).slice(0,r.lastIndexOf("/")+1),i.slice(0,r.length)!=r););var s=(n[1]||"").match(/boundary="(.*?)"/);if(!s)throw new Error("MAD cannot find boundary");var o="--"+(s[1]||""),l=[],f=[],c={FileIndex:l,FullPaths:f};S(c);var h,p=0;for(a=0;a<n.length;++a){var d=n[a];d!==o&&d!==o+"--"||(p++&&Se(c,n.slice(h,a),r),h=a)}return c}(e,t);if(e.length<512)throw new Error("CFB file size "+e.length+" < 512");var r,n,a,i,s,o,c=512,h=[],p=e.slice(0,512);Gt(p,0);var d=function(e){if(80==e[e.l]&&75==e[e.l+1])return[0,0];e.chk(C,"Header Signature: "),e.l+=16;var t=e.read_shift(2,"u");return[e.read_shift(2,"u"),t]}(p);switch(r=d[0]){case 3:c=512;break;case 4:c=4096;break;case 0:if(0==d[1])return de(e,t);default:throw new Error("Major Version: Expected 3 or 4 saw "+r)}512!==c&&Gt(p=e.slice(0,c),28);var m=e.slice(0,c);!function(e,t){var r=9;switch(e.l+=2,r=e.read_shift(2)){case 9:if(3!=t)throw new Error("Sector Shift: Expected 9 saw "+r);break;case 12:if(4!=t)throw new Error("Sector Shift: Expected 12 saw "+r);break;default:throw new Error("Sector Shift: Expected 9 or 12 saw "+r)}e.chk("0600","Mini Sector Shift: "),e.chk("000000000000","Reserved: ")}(p,r);var g=p.read_shift(4,"i");if(3===r&&0!==g)throw new Error("# Directory Sectors: Expected 0 saw "+g);p.l+=4,i=p.read_shift(4,"i"),p.l+=4,p.chk("00100000","Mini Stream Cutoff Size: "),s=p.read_shift(4,"i"),n=p.read_shift(4,"i"),o=p.read_shift(4,"i"),a=p.read_shift(4,"i");for(var w=-1,E=0;E<109&&!((w=p.read_shift(4,"i"))<0);++E)h[E]=w;var b=function(e,t){for(var r=Math.ceil(e.length/t)-1,n=[],a=1;a<r;++a)n[a-1]=e.slice(a*t,(a+1)*t);return n[r-1]=e.slice(r*t),n}(e,c);f(o,a,b,c,h);var A=function(e,t,r,n){var a=e.length,i=[],s=[],o=[],l=[],f=n-1,c=0,h=0,u=0,p=0;for(c=0;c<a;++c)if(o=[],(u=c+t)>=a&&(u-=a),!s[u]){l=[];var d=[];for(h=u;h>=0;){d[h]=!0,s[h]=!0,o[o.length]=h,l.push(e[h]);var m=r[Math.floor(4*h/n)];if(n<4+(p=4*h&f))throw new Error("FAT boundary crossed: "+h+" 4 "+n);if(!e[m])break;if(d[h=Pt(e[m],p)])break}i[u]={nodes:o,data:ct([l])}}return i}(b,i,h,c);A[i].name="!Directory",n>0&&s!==x&&(A[s].name="!MiniFAT"),A[h[0]].name="!FAT",A.fat_addrs=h,A.ssz=c;var _=[],y=[],O=[];!function(e,t,r,n,a,i,s,o){for(var f,c=0,h=n.length?2:0,u=t[e].data,p=0,d=0;p<u.length;p+=128){var m=u.slice(p,p+128);Gt(m,64),d=m.read_shift(2),f=ut(m,0,d-h),n.push(f);var g={name:f,type:m.read_shift(1),color:m.read_shift(1),L:m.read_shift(4,"i"),R:m.read_shift(4,"i"),C:m.read_shift(4,"i"),clsid:m.read_shift(16),state:m.read_shift(4,"i"),start:0,size:0};0!==m.read_shift(2)+m.read_shift(2)+m.read_shift(2)+m.read_shift(2)&&(g.ct=T(m,m.l-8)),0!==m.read_shift(2)+m.read_shift(2)+m.read_shift(2)+m.read_shift(2)&&(g.mt=T(m,m.l-8)),g.start=m.read_shift(4,"i"),g.size=m.read_shift(4,"i"),g.size<0&&g.start<0&&(g.size=g.type=0,g.start=x,g.name=""),5===g.type?(c=g.start,a>0&&c!==x&&(t[c].name="!StreamData")):g.size>=4096?(g.storage="fat",void 0===t[g.start]&&(t[g.start]=v(r,g.start,t.fat_addrs,t.ssz)),t[g.start].name=g.name,g.content=t[g.start].data.slice(0,g.size)):(g.storage="minifat",g.size<0?g.size=0:c!==x&&g.start!==x&&t[c]&&(g.content=l(g,t[c].data,(t[o]||{}).data))),g.content&&Gt(g.content,0),i[f]=g,s.push(g)}}(i,A,b,_,n,{},y,s),function(e,t,r){for(var n=0,a=0,i=0,s=0,o=0,l=r.length,f=[],c=[];n<l;++n)f[n]=c[n]=n,t[n]=r[n];for(;o<c.length;++o)a=e[n=c[o]].L,i=e[n].R,s=e[n].C,f[n]===n&&(-1!==a&&f[a]!==a&&(f[n]=f[a]),-1!==i&&f[i]!==i&&(f[n]=f[i])),-1!==s&&(f[s]=n),-1!==a&&n!=f[n]&&(f[a]=f[n],c.lastIndexOf(a)<o&&c.push(a)),-1!==i&&n!=f[n]&&(f[i]=f[n],c.lastIndexOf(i)<o&&c.push(i));for(n=1;n<l;++n)f[n]===n&&(-1!==i&&f[i]!==i?f[n]=f[i]:-1!==a&&f[a]!==a&&(f[n]=f[a]));for(n=1;n<l;++n)if(0!==e[n].type){if((o=n)!=f[o])do{o=f[o],t[n]=t[o]+"/"+t[n]}while(0!==o&&-1!==f[o]&&o!=f[o]);f[n]=-1}for(t[0]+="/",n=1;n<l;++n)2!==e[n].type&&(t[n]+="/")}(y,O,_),_.shift();var R={FileIndex:y,FullPaths:O};return t&&t.raw&&(R.raw={header:m,sectors:b}),R}function l(e,t,r){for(var n=e.start,a=e.size,i=[],s=n;r&&a>0&&s>=0;)i.push(t.slice(s*O,s*O+O)),a-=O,s=Pt(r,4*s);return 0===i.length?jt(0):w(i).slice(0,e.size)}function f(e,t,r,n,a){var i=x;if(e===x){if(0!==t)throw new Error("DIFAT chain shorter than expected")}else if(-1!==e){var s=r[e],o=(n>>>2)-1;if(!s)return;for(var l=0;l<o&&(i=Pt(s,4*l))!==x;++l)a.push(i);f(Pt(s,n-4),t-1,r,n,a)}}function v(e,t,r,n,a){var i=[],s=[];a||(a=[]);var o=n-1,l=0,f=0;for(l=t;l>=0;){a[l]=!0,i[i.length]=l,s.push(e[l]);var c=r[Math.floor(4*l/n)];if(n<4+(f=4*l&o))throw new Error("FAT boundary crossed: "+l+" 4 "+n);if(!e[c])break;l=Pt(e[c],f)}return{nodes:i,data:ct([s])}}function T(e,t){return new Date(1e3*(Dt(e,t+4)/1e7*Math.pow(2,32)+Dt(e,t)/1e7-11644473600))}function S(e,t){var r=t||{},n=r.root||"Root Entry";if(e.FullPaths||(e.FullPaths=[]),e.FileIndex||(e.FileIndex=[]),e.FullPaths.length!==e.FileIndex.length)throw new Error("inconsistent CFB structure");0===e.FullPaths.length&&(e.FullPaths[0]=n+"/",e.FileIndex[0]={name:n,type:5}),r.CLSID&&(e.FileIndex[0].clsid=r.CLSID),function(e){var t="Sh33tJ5";if(ge.find(e,"/"+t))return;var r=jt(4);r[0]=55,r[1]=r[3]=50,r[2]=54,e.FileIndex.push({name:t,type:2,content:r,size:4,L:69,R:69,C:69}),e.FullPaths.push(e.FullPaths[0]+t),A(e)}(e)}function A(e,t){S(e);for(var a=!1,i=!1,s=e.FullPaths.length-1;s>=0;--s){var o=e.FileIndex[s];switch(o.type){case 0:i?a=!0:(e.FileIndex.pop(),e.FullPaths.pop());break;case 1:case 2:case 5:i=!0,isNaN(o.R*o.L*o.C)&&(a=!0),o.R>-1&&o.L>-1&&o.R==o.L&&(a=!0);break;default:a=!0}}if(a||t){var l=new Date(1987,1,19),f=0,c=Object.create?Object.create(null):{},h=[];for(s=0;s<e.FullPaths.length;++s)c[e.FullPaths[s]]=!0,0!==e.FileIndex[s].type&&h.push([e.FullPaths[s],e.FileIndex[s]]);for(s=0;s<h.length;++s){var u=r(h[s][0]);(i=c[u])||(h.push([u,{name:n(u).replace("/",""),type:1,clsid:N,ct:l,mt:l,content:null}]),c[u]=!0)}for(h.sort((function(e,t){return function(e,t){for(var r=e.split("/"),n=t.split("/"),a=0,i=0,s=Math.min(r.length,n.length);a<s;++a){if(i=r[a].length-n[a].length)return i;if(r[a]!=n[a])return r[a]<n[a]?-1:1}return r.length-n.length}(e[0],t[0])})),e.FullPaths=[],e.FileIndex=[],s=0;s<h.length;++s)e.FullPaths[s]=h[s][0],e.FileIndex[s]=h[s][1];for(s=0;s<h.length;++s){var p=e.FileIndex[s],d=e.FullPaths[s];if(p.name=n(d).replace("/",""),p.L=p.R=p.C=-(p.color=1),p.size=p.content?p.content.length:0,p.start=0,p.clsid=p.clsid||N,0===s)p.C=h.length>1?1:-1,p.size=0,p.type=5;else if("/"==d.slice(-1)){for(f=s+1;f<h.length&&r(e.FullPaths[f])!=d;++f);for(p.C=f>=h.length?-1:f,f=s+1;f<h.length&&r(e.FullPaths[f])!=r(d);++f);p.R=f>=h.length?-1:f,p.type=1}else r(e.FullPaths[s+1]||"")==r(d)&&(p.R=s+1),p.type=2}}}function _(e,t){var r=t||{};if("mad"==r.fileType)return function(e,t){for(var r=t||{},n=r.boundary||"SheetJS",a=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+(n="------="+n).slice(2)+'"',"","",""],i=e.FullPaths[0],s=i,o=e.FileIndex[0],l=1;l<e.FullPaths.length;++l)if(s=e.FullPaths[l].slice(i.length),(o=e.FileIndex[l]).size&&o.content&&"Sh33tJ5"!=s){s=s.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,(function(e){return"_x"+e.charCodeAt(0).toString(16)+"_"})).replace(/[\u0080-\uFFFF]/g,(function(e){return"_u"+e.charCodeAt(0).toString(16)+"_"}));for(var f=o.content,c=u&&Buffer.isBuffer(f)?f.toString("binary"):I(f),h=0,p=Math.min(1024,c.length),d=0,m=0;m<=p;++m)(d=c.charCodeAt(m))>=32&&d<128&&++h;var g=h>=4*p/5;a.push(n),a.push("Content-Location: "+(r.root||"file:///C:/SheetJS/")+s),a.push("Content-Transfer-Encoding: "+(g?"quoted-printable":"base64")),a.push("Content-Type: "+we(o,s)),a.push(""),a.push(g?be(c):Ee(c))}return a.push(n+"--\r\n"),a.join("\r\n")}(e,r);if(A(e),"zip"===r.fileType)return function(e,t){var r=t||{},n=[],i=[],s=jt(1),o=r.compression?8:0,l=0,f=0,c=0,h=0,u=0,p=e.FullPaths[0],d=p,m=e.FileIndex[0],g=[],v=0;for(f=1;f<e.FullPaths.length;++f)if(d=e.FullPaths[f].slice(p.length),(m=e.FileIndex[f]).size&&m.content&&"Sh33tJ5"!=d){var T=h,E=jt(d.length);for(c=0;c<d.length;++c)E.write_shift(1,127&d.charCodeAt(c));E=E.slice(0,E.l),g[u]=me.buf(m.content,0);var b=m.content;8==o&&(b=D(b)),(s=jt(30)).write_shift(4,67324752),s.write_shift(2,20),s.write_shift(2,l),s.write_shift(2,o),m.mt?a(s,m.mt):s.write_shift(4,0),s.write_shift(-4,g[u]),s.write_shift(4,b.length),s.write_shift(4,m.content.length),s.write_shift(2,E.length),s.write_shift(2,0),h+=s.length,n.push(s),h+=E.length,n.push(E),h+=b.length,n.push(b),(s=jt(46)).write_shift(4,33639248),s.write_shift(2,0),s.write_shift(2,20),s.write_shift(2,l),s.write_shift(2,o),s.write_shift(4,0),s.write_shift(-4,g[u]),s.write_shift(4,b.length),s.write_shift(4,m.content.length),s.write_shift(2,E.length),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(4,0),s.write_shift(4,T),v+=s.l,i.push(s),v+=E.length,i.push(E),++u}return s=jt(22),s.write_shift(4,101010256),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(2,u),s.write_shift(2,u),s.write_shift(4,v),s.write_shift(4,h),s.write_shift(2,0),w([w(n),w(i),s])}(e,r);var n=function(e){for(var t=0,r=0,n=0;n<e.FileIndex.length;++n){var a=e.FileIndex[n];if(a.content){var i=a.content.length;i>0&&(i<4096?t+=i+63>>6:r+=i+511>>9)}}for(var s=e.FullPaths.length+3>>2,o=t+127>>7,l=(t+7>>3)+r+s+o,f=l+127>>7,c=f<=109?0:Math.ceil((f-109)/127);l+f+c+127>>7>f;)c=++f<=109?0:Math.ceil((f-109)/127);var h=[1,c,f,o,s,r,t,0];return e.FileIndex[0].size=t<<6,h[7]=(e.FileIndex[0].start=h[0]+h[1]+h[2]+h[3]+h[4]+h[5])+(h[6]+7>>3),h}(e),i=jt(n[7]<<9),s=0,o=0;for(s=0;s<8;++s)i.write_shift(1,R[s]);for(s=0;s<8;++s)i.write_shift(2,0);for(i.write_shift(2,62),i.write_shift(2,3),i.write_shift(2,65534),i.write_shift(2,9),i.write_shift(2,6),s=0;s<3;++s)i.write_shift(2,0);for(i.write_shift(4,0),i.write_shift(4,n[2]),i.write_shift(4,n[0]+n[1]+n[2]+n[3]-1),i.write_shift(4,0),i.write_shift(4,4096),i.write_shift(4,n[3]?n[0]+n[1]+n[2]-1:x),i.write_shift(4,n[3]),i.write_shift(-4,n[1]?n[0]-1:x),i.write_shift(4,n[1]),s=0;s<109;++s)i.write_shift(-4,s<n[2]?n[1]+s:-1);if(n[1])for(o=0;o<n[1];++o){for(;s<236+127*o;++s)i.write_shift(-4,s<n[2]?n[1]+s:-1);i.write_shift(-4,o===n[1]-1?x:o+1)}var l=function(e){for(o+=e;s<o-1;++s)i.write_shift(-4,s+1);e&&(++s,i.write_shift(-4,x))};for(o=s=0,o+=n[1];s<o;++s)i.write_shift(-4,k.DIFSECT);for(o+=n[2];s<o;++s)i.write_shift(-4,k.FATSECT);l(n[3]),l(n[4]);for(var f=0,c=0,h=e.FileIndex[0];f<e.FileIndex.length;++f)(h=e.FileIndex[f]).content&&((c=h.content.length)<4096||(h.start=o,l(c+511>>9)));for(l(n[6]+7>>3);511&i.l;)i.write_shift(-4,k.ENDOFCHAIN);for(o=s=0,f=0;f<e.FileIndex.length;++f)(h=e.FileIndex[f]).content&&(!(c=h.content.length)||c>=4096||(h.start=o,l(c+63>>6)));for(;511&i.l;)i.write_shift(-4,k.ENDOFCHAIN);for(s=0;s<n[4]<<2;++s){var p=e.FullPaths[s];if(p&&0!==p.length){h=e.FileIndex[s],0===s&&(h.start=h.size?h.start-1:x);var d=0===s&&r.root||h.name;if(c=2*(d.length+1),i.write_shift(64,d,"utf16le"),i.write_shift(2,c),i.write_shift(1,h.type),i.write_shift(1,h.color),i.write_shift(-4,h.L),i.write_shift(-4,h.R),i.write_shift(-4,h.C),h.clsid)i.write_shift(16,h.clsid,"hex");else for(f=0;f<4;++f)i.write_shift(4,0);i.write_shift(4,h.state||0),i.write_shift(4,0),i.write_shift(4,0),i.write_shift(4,0),i.write_shift(4,0),i.write_shift(4,h.start),i.write_shift(4,h.size),i.write_shift(4,0)}else{for(f=0;f<17;++f)i.write_shift(4,0);for(f=0;f<3;++f)i.write_shift(4,-1);for(f=0;f<12;++f)i.write_shift(4,0)}}for(s=1;s<e.FileIndex.length;++s)if((h=e.FileIndex[s]).size>=4096)if(i.l=h.start+1<<9,u&&Buffer.isBuffer(h.content))h.content.copy(i,i.l,0,h.size),i.l+=h.size+511&-512;else{for(f=0;f<h.size;++f)i.write_shift(1,h.content[f]);for(;511&f;++f)i.write_shift(1,0)}for(s=1;s<e.FileIndex.length;++s)if((h=e.FileIndex[s]).size>0&&h.size<4096)if(u&&Buffer.isBuffer(h.content))h.content.copy(i,i.l,0,h.size),i.l+=h.size+63&-64;else{for(f=0;f<h.size;++f)i.write_shift(1,h.content[f]);for(;63&f;++f)i.write_shift(1,0)}if(u)i.l=i.length;else for(;i.l<i.length;)i.write_shift(1,0);return i}t.version="1.2.1";var y,O=64,x=-2,C="d0cf11e0a1b11ae1",R=[208,207,17,224,161,177,26,225],N="00000000000000000000000000000000",k={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:x,FREESECT:-1,HEADER_SIGNATURE:C,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:N,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function I(e){for(var t=new Array(e.length),r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}function D(e){return y?y.deflateRawSync(e):ie(e)}var P=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],L=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],M=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577];for(var F,U,B="undefined"!=typeof Uint8Array,W=B?new Uint8Array(256):[],H=0;H<256;++H)W[H]=(U=void 0,255&((U=139536&((F=H)<<1|F<<11)|558144&(F<<5|F<<15))>>16|U>>8|U));function G(e,t){var r=W[255&e];return t<=8?r>>>8-t:(r=r<<8|W[e>>8&255],t<=16?r>>>16-t:(r=r<<8|W[e>>16&255])>>>24-t)}function V(e,t){var r=7&t,n=t>>>3;return(e[n]|(r<=6?0:e[n+1]<<8))>>>r&3}function j(e,t){var r=7&t,n=t>>>3;return(e[n]|(r<=5?0:e[n+1]<<8))>>>r&7}function z(e,t){var r=7&t,n=t>>>3;return(e[n]|(r<=3?0:e[n+1]<<8))>>>r&31}function $(e,t){var r=7&t,n=t>>>3;return(e[n]|(r<=1?0:e[n+1]<<8))>>>r&127}function X(e,t,r){var n=7&t,a=t>>>3,i=(1<<r)-1,s=e[a]>>>n;return r<8-n?s&i:(s|=e[a+1]<<8-n,r<16-n?s&i:(s|=e[a+2]<<16-n,r<24-n?s&i:(s|=e[a+3]<<24-n)&i))}function Y(e,t,r){var n=7&t,a=t>>>3;return n<=5?e[a]|=(7&r)<<n:(e[a]|=r<<n&255,e[a+1]=(7&r)>>8-n),t+3}function K(e,t,r){return r=(1&r)<<(7&t),e[t>>>3]|=r,t+1}function J(e,t,r){var n=t>>>3;return r<<=7&t,e[n]|=255&r,r>>>=8,e[n+1]=r,t+8}function Z(e,t,r){var n=t>>>3;return r<<=7&t,e[n]|=255&r,r>>>=8,e[n+1]=255&r,e[n+2]=r>>>8,t+16}function q(e,t){var r=e.length,n=2*r>t?2*r:t+5,a=0;if(r>=t)return e;if(u){var i=m(n);if(e.copy)e.copy(i);else for(;a<e.length;++a)i[a]=e[a];return i}if(B){var s=new Uint8Array(n);if(s.set)s.set(e);else for(;a<r;++a)s[a]=e[a];return s}return e.length=n,e}function Q(e){for(var t=new Array(e),r=0;r<e;++r)t[r]=0;return t}function ee(e,t,r){var n=1,a=0,i=0,s=0,o=0,l=e.length,f=B?new Uint16Array(32):Q(32);for(i=0;i<32;++i)f[i]=0;for(i=l;i<r;++i)e[i]=0;l=e.length;var c=B?new Uint16Array(l):Q(l);for(i=0;i<l;++i)f[a=e[i]]++,n<a&&(n=a),c[i]=0;for(f[0]=0,i=1;i<=n;++i)f[i+16]=o=o+f[i-1]<<1;for(i=0;i<l;++i)0!=(o=e[i])&&(c[i]=f[o+16]++);var h=0;for(i=0;i<l;++i)if(0!=(h=e[i]))for(o=G(c[i],n)>>n-h,s=(1<<n+4-h)-1;s>=0;--s)t[o|s<<h]=15&h|i<<4;return n}var te=B?new Uint16Array(512):Q(512),re=B?new Uint16Array(32):Q(32);if(!B){for(var ne=0;ne<512;++ne)te[ne]=0;for(ne=0;ne<32;++ne)re[ne]=0}!function(){for(var e=[],t=0;t<32;t++)e.push(5);ee(e,re,32);var r=[];for(t=0;t<=143;t++)r.push(8);for(;t<=255;t++)r.push(9);for(;t<=279;t++)r.push(7);for(;t<=287;t++)r.push(8);ee(r,te,288)}();var ae=function(){for(var e=B?new Uint8Array(32768):[],t=0,r=0;t<M.length-1;++t)for(;r<M[t+1];++r)e[r]=t;for(;r<32768;++r)e[r]=29;var n=B?new Uint8Array(259):[];for(t=0,r=0;t<L.length-1;++t)for(;r<L[t+1];++r)n[r]=t;return function(t,r){return t.length<8?function(e,t){for(var r=0;r<e.length;){var n=Math.min(65535,e.length-r),a=r+n==e.length;for(t.write_shift(1,+a),t.write_shift(2,n),t.write_shift(2,65535&~n);n-- >0;)t[t.l++]=e[r++]}return t.l}(t,r):function(t,r){for(var a=0,i=0,s=B?new Uint16Array(32768):[];i<t.length;){var o=Math.min(65535,t.length-i);if(o<10){for(7&(a=Y(r,a,+!(i+o!=t.length)))&&(a+=8-(7&a)),r.l=a/8|0,r.write_shift(2,o),r.write_shift(2,65535&~o);o-- >0;)r[r.l++]=t[i++];a=8*r.l}else{a=Y(r,a,+!(i+o!=t.length)+2);for(var l=0;o-- >0;){var f=t[i],c=-1,h=0;if((c=s[l=32767&(l<<5^f)])&&((c|=-32768&i)>i&&(c-=32768),c<i))for(;t[c+h]==t[i+h]&&h<250;)++h;if(h>2){(f=n[h])<=22?a=J(r,a,W[f+1]>>1)-1:(J(r,a,3),J(r,a+=5,W[f-23]>>5),a+=3);var u=f<8?0:f-4>>2;u>0&&(Z(r,a,h-L[f]),a+=u),f=e[i-c],a=J(r,a,W[f]>>3),a-=3;var p=f<4?0:f-2>>1;p>0&&(Z(r,a,i-c-M[f]),a+=p);for(var d=0;d<h;++d)s[l]=32767&i,l=32767&(l<<5^t[i]),++i;o-=h-1}else f<=143?f+=48:a=K(r,a,1),a=J(r,a,W[f]),s[l]=32767&i,++i}a=J(r,a,0)-1}}return r.l=(a+7)/8|0,r.l}(t,r)}}();function ie(e){var t=jt(50+Math.floor(1.1*e.length)),r=ae(e,t);return t.slice(0,r)}var se=B?new Uint16Array(32768):Q(32768),oe=B?new Uint16Array(32768):Q(32768),le=B?new Uint16Array(128):Q(128),fe=1,ce=1;function he(e,t){var r=z(e,t)+257,n=z(e,t+=5)+1,a=function(e,t){var r=7&t,n=t>>>3;return(e[n]|(r<=4?0:e[n+1]<<8))>>>r&15}(e,t+=5)+4;t+=4;for(var i=0,s=B?new Uint8Array(19):Q(19),o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],l=1,f=B?new Uint8Array(8):Q(8),c=B?new Uint8Array(8):Q(8),h=s.length,u=0;u<a;++u)s[P[u]]=i=j(e,t),l<i&&(l=i),f[i]++,t+=3;var p=0;for(f[0]=0,u=1;u<=l;++u)c[u]=p=p+f[u-1]<<1;for(u=0;u<h;++u)0!=(p=s[u])&&(o[u]=c[p]++);var d=0;for(u=0;u<h;++u)if(0!=(d=s[u])){p=W[o[u]]>>8-d;for(var m=(1<<7-d)-1;m>=0;--m)le[p|m<<d]=7&d|u<<3}var g=[];for(l=1;g.length<r+n;)switch(t+=7&(p=le[$(e,t)]),p>>>=3){case 16:for(i=3+V(e,t),t+=2,p=g[g.length-1];i-- >0;)g.push(p);break;case 17:for(i=3+j(e,t),t+=3;i-- >0;)g.push(0);break;case 18:for(i=11+$(e,t),t+=7;i-- >0;)g.push(0);break;default:g.push(p),l<p&&(l=p)}var v=g.slice(0,r),T=g.slice(r);for(u=r;u<286;++u)v[u]=0;for(u=n;u<30;++u)T[u]=0;return fe=ee(v,se,286),ce=ee(T,oe,30),t}function ue(e,t){var r=function(e,t){if(3==e[0]&&!(3&e[1]))return[d(t),2];for(var r=0,n=0,a=m(t||1<<18),i=0,s=a.length>>>0,o=0,l=0;!(1&n);)if(n=j(e,r),r+=3,n>>>1!=0)for(n>>1==1?(o=9,l=5):(r=he(e,r),o=fe,l=ce);;){!t&&s<i+32767&&(s=(a=q(a,i+32767)).length);var f=X(e,r,o),c=n>>>1==1?te[f]:se[f];if(r+=15&c,(c>>>=4)>>>8&255){if(256==c)break;var h=(c-=257)<8?0:c-4>>2;h>5&&(h=0);var u=i+L[c];h>0&&(u+=X(e,r,h),r+=h),f=X(e,r,l),r+=15&(c=n>>>1==1?re[f]:oe[f]);var p=(c>>>=4)<4?0:c-2>>1,g=M[c];for(p>0&&(g+=X(e,r,p),r+=p),!t&&s<u&&(s=(a=q(a,u+100)).length);i<u;)a[i]=a[i-g],++i}else a[i++]=c}else{7&r&&(r+=8-(7&r));var v=e[r>>>3]|e[1+(r>>>3)]<<8;if(r+=32,v>0)for(!t&&s<i+v&&(s=(a=q(a,i+v)).length);v-- >0;)a[i++]=e[r>>>3],r+=8}return t?[a,r+7>>>3]:[a.slice(0,i),r+7>>>3]}(e.slice(e.l||0),t);return e.l+=r[1],r[0]}function pe(e,t){if(!e)throw new Error(t)}function de(e,t){var r=e;Gt(r,0);var n={FileIndex:[],FullPaths:[]};S(n,{root:t.root});for(var a=r.length-4;(80!=r[a]||75!=r[a+1]||5!=r[a+2]||6!=r[a+3])&&a>=0;)--a;r.l=a+4,r.l+=4;var s=r.read_shift(2);r.l+=6;var o=r.read_shift(4);for(r.l=o,a=0;a<s;++a){r.l+=20;var l=r.read_shift(4),f=r.read_shift(4),c=r.read_shift(2),h=r.read_shift(2),u=r.read_shift(2);r.l+=8;var p=r.read_shift(4),d=i(r.slice(r.l+c,r.l+c+h));r.l+=c+h+u;var m=r.l;r.l=p+4,ve(r,l,f,n,d),r.l=m}return n}function ve(e,t,r,n,a){e.l+=2;var s=e.read_shift(2),o=e.read_shift(2),l=function(e){var t=65535&e.read_shift(2),r=65535&e.read_shift(2),n=new Date,a=31&r,i=15&(r>>>=5);r>>>=4,n.setMilliseconds(0),n.setFullYear(r+1980),n.setMonth(i-1),n.setDate(a);var s=31&t,o=63&(t>>>=5);return t>>>=6,n.setHours(t),n.setMinutes(o),n.setSeconds(s<<1),n}(e);if(8257&s)throw new Error("Unsupported ZIP encryption");e.read_shift(4);for(var f=e.read_shift(4),c=e.read_shift(4),h=e.read_shift(2),u=e.read_shift(2),p="",d=0;d<h;++d)p+=String.fromCharCode(e[e.l++]);if(u){var m=i(e.slice(e.l,e.l+u));(m[21589]||{}).mt&&(l=m[21589].mt),((a||{})[21589]||{}).mt&&(l=a[21589].mt)}e.l+=u;var g=e.slice(e.l,e.l+f);switch(o){case 8:g=function(e,t){if(!y)return ue(e,t);var r=new(0,y.InflateRaw),n=r._processChunk(e.slice(e.l),r._finishFlushFlag);return e.l+=r.bytesRead,n}(e,c);break;case 0:break;default:throw new Error("Unsupported ZIP Compression method "+o)}var v=!1;8&s&&(134695760==e.read_shift(4)&&(e.read_shift(4),v=!0),f=e.read_shift(4),c=e.read_shift(4)),f!=t&&pe(v,"Bad compressed size: "+t+" != "+f),c!=r&&pe(v,"Bad uncompressed size: "+r+" != "+c),Ae(n,p,g,{unsafe:!0,mt:l})}var Te={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function we(e,t){if(e.ctype)return e.ctype;var r=e.name||"",n=r.match(/\.([^\.]+)$/);return n&&Te[n[1]]||t&&(n=(r=t).match(/[\.\\]([^\.\\])+$/))&&Te[n[1]]?Te[n[1]]:"application/octet-stream"}function Ee(e){for(var t=c(e),r=[],n=0;n<t.length;n+=76)r.push(t.slice(n,n+76));return r.join("\r\n")+"\r\n"}function be(e){var t=e.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,(function(e){var t=e.charCodeAt(0).toString(16).toUpperCase();return"="+(1==t.length?"0"+t:t)}));"\n"==(t=t.replace(/ $/gm,"=20").replace(/\t$/gm,"=09")).charAt(0)&&(t="=0D"+t.slice(1));for(var r=[],n=(t=t.replace(/\r(?!\n)/gm,"=0D").replace(/\n\n/gm,"\n=0A").replace(/([^\r\n])\n/gm,"$1=0A")).split("\r\n"),a=0;a<n.length;++a){var i=n[a];if(0!=i.length)for(var s=0;s<i.length;){var o=76,l=i.slice(s,s+o);"="==l.charAt(o-1)?o--:"="==l.charAt(o-2)?o-=2:"="==l.charAt(o-3)&&(o-=3),l=i.slice(s,s+o),(s+=o)<i.length&&(l+="="),r.push(l)}else r.push("")}return r.join("\r\n")}function Se(e,t,r){for(var n,a="",i="",s="",o=0;o<10;++o){var l=t[o];if(!l||l.match(/^\s*$/))break;var f=l.match(/^(.*?):\s*([^\s].*)$/);if(f)switch(f[1].toLowerCase()){case"content-location":a=f[2].trim();break;case"content-type":s=f[2].trim();break;case"content-transfer-encoding":i=f[2].trim()}}switch(++o,i.toLowerCase()){case"base64":n=g(h(t.slice(o).join("")));break;case"quoted-printable":n=function(e){for(var t=[],r=0;r<e.length;++r){for(var n=e[r];r<=e.length&&"="==n.charAt(n.length-1);)n=n.slice(0,n.length-1)+e[++r];t.push(n)}for(var a=0;a<t.length;++a)t[a]=t[a].replace(/[=][0-9A-Fa-f]{2}/g,(function(e){return String.fromCharCode(parseInt(e.slice(1),16))}));return g(t.join("\r\n"))}(t.slice(o));break;default:throw new Error("Unsupported Content-Transfer-Encoding "+i)}var c=Ae(e,a.slice(r.length),n,{unsafe:!0});s&&(c.ctype=s)}function Ae(e,t,r,a){var i=a&&a.unsafe;i||S(e);var s=!i&&ge.find(e,t);if(!s){var o=e.FullPaths[0];t.slice(0,o.length)==o?o=t:("/"!=o.slice(-1)&&(o+="/"),o=(o+t).replace("//","/")),s={name:n(t),type:2},e.FileIndex.push(s),e.FullPaths.push(o),i||ge.utils.cfb_gc(e)}return s.content=r,s.size=r?r.length:0,a&&(a.CLSID&&(s.clsid=a.CLSID),a.mt&&(s.mt=a.mt),a.ct&&(s.ct=a.ct)),s}return t.find=function(e,t){var r=e.FullPaths.map((function(e){return e.toUpperCase()})),n=r.map((function(e){var t=e.split("/");return t[t.length-("/"==e.slice(-1)?2:1)]})),a=!1;47===t.charCodeAt(0)?(a=!0,t=r[0].slice(0,-1)+t):a=-1!==t.indexOf("/");var i=t.toUpperCase(),s=!0===a?r.indexOf(i):n.indexOf(i);if(-1!==s)return e.FileIndex[s];var o=!i.match(b);for(i=i.replace(E,""),o&&(i=i.replace(b,"!")),s=0;s<r.length;++s){if((o?r[s].replace(b,"!"):r[s]).replace(E,"")==i)return e.FileIndex[s];if((o?n[s].replace(b,"!"):n[s]).replace(E,"")==i)return e.FileIndex[s]}return null},t.read=function(t,r){var n=r&&r.type;switch(n||u&&Buffer.isBuffer(t)&&(n="buffer"),n||"base64"){case"file":return function(t,r){return s(),o(e.readFileSync(t),r)}(t,r);case"base64":return o(g(h(t)),r);case"binary":return o(g(t),r)}return o(t,r)},t.parse=o,t.write=function(t,r){var n=_(t,r);switch(r&&r.type||"buffer"){case"file":return s(),e.writeFileSync(r.filename,n),n;case"binary":return"string"==typeof n?n:I(n);case"base64":return c("string"==typeof n?n:I(n));case"buffer":if(u)return Buffer.isBuffer(n)?n:p(n);case"array":return"string"==typeof n?g(n):n}return n},t.writeFile=function(t,r,n){s();var a=_(t,n);e.writeFileSync(r,a)},t.utils={cfb_new:function(e){var t={};return S(t,e),t},cfb_add:Ae,cfb_del:function(e,t){S(e);var r=ge.find(e,t);if(r)for(var n=0;n<e.FileIndex.length;++n)if(e.FileIndex[n]==r)return e.FileIndex.splice(n,1),e.FullPaths.splice(n,1),!0;return!1},cfb_mov:function(e,t,r){S(e);var a=ge.find(e,t);if(a)for(var i=0;i<e.FileIndex.length;++i)if(e.FileIndex[i]==a)return e.FileIndex[i].name=n(r),e.FullPaths[i]=r,!0;return!1},cfb_gc:function(e){A(e,!0)},ReadShift:Mt,CheckField:Ht,prep_blob:Gt,bconcat:w,use_zlib:function(e){try{var t=new(0,e.InflateRaw);if(t._processChunk(new Uint8Array([3,0]),t._finishFlushFlag),!t.bytesRead)throw new Error("zlib does not expose bytesRead");y=e}catch(r){}},_deflateRaw:ie,_inflateRaw:ue,consts:k},t}();function ve(e){return"string"==typeof e?v(e):Array.isArray(e)?function(e){if("undefined"==typeof Uint8Array)throw new Error("Unsupported");return new Uint8Array(e)}(e):e}function Te(e,t,r){if("undefined"!=typeof Deno){if(r&&"string"==typeof t)switch(r){case"utf8":t=new TextEncoder(r).encode(t);break;case"binary":t=v(t);break;default:throw new Error("Unsupported encoding "+r)}return Deno.writeFileSync(e,t)}var n="utf8"==r?Qe(t):t;if("undefined"!=typeof IE_SaveFile)return IE_SaveFile(n,e);if("undefined"!=typeof Blob){var a=new Blob([ve(n)],{type:"application/octet-stream"});if("undefined"!=typeof navigator&&navigator.msSaveBlob)return navigator.msSaveBlob(a,e);if("undefined"!=typeof saveAs)return saveAs(a,e);if("undefined"!=typeof URL&&"undefined"!=typeof document&&document.createElement&&URL.createObjectURL){var i=URL.createObjectURL(a);if("object"==typeof chrome&&"function"==typeof(chrome.downloads||{}).download)return URL.revokeObjectURL&&"undefined"!=typeof setTimeout&&setTimeout((function(){URL.revokeObjectURL(i)}),6e4),chrome.downloads.download({url:i,filename:e,saveAs:!0});var s=document.createElement("a");if(null!=s.download)return s.download=e,s.href=i,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL&&"undefined"!=typeof setTimeout&&setTimeout((function(){URL.revokeObjectURL(i)}),6e4),i}}if("undefined"!=typeof $&&"undefined"!=typeof File&&"undefined"!=typeof Folder)try{var o=File(e);return o.open("w"),o.encoding="binary",Array.isArray(t)&&(t=T(t)),o.write(t),o.close(),t}catch(l){if(!l.message||!l.message.match(/onstruct/))throw l}throw new Error("cannot save file "+e)}function we(e){for(var t=Object.keys(e),r=[],n=0;n<t.length;++n)Object.prototype.hasOwnProperty.call(e,t[n])&&r.push(t[n]);return r}function Ee(e,t){for(var r=[],n=we(e),a=0;a!==n.length;++a)null==r[e[n[a]][t]]&&(r[e[n[a]][t]]=n[a]);return r}function be(e){for(var t=[],r=we(e),n=0;n!==r.length;++n)t[e[r[n]]]=r[n];return t}function Se(e){for(var t=[],r=we(e),n=0;n!==r.length;++n)t[e[r[n]]]=parseInt(r[n],10);return t}var Ae=new Date(1899,11,30,0,0,0);function _e(e,t){var r=e.getTime();return t&&(r-=1263168e5),(r-(Ae.getTime()+6e4*(e.getTimezoneOffset()-Ae.getTimezoneOffset())))/864e5}var ye=new Date,Oe=Ae.getTime()+6e4*(ye.getTimezoneOffset()-Ae.getTimezoneOffset()),xe=ye.getTimezoneOffset();function Ce(e){var t=new Date;return t.setTime(24*e*60*60*1e3+Oe),t.getTimezoneOffset()!==xe&&t.setTime(t.getTime()+6e4*(t.getTimezoneOffset()-xe)),t}var Re=new Date("2017-02-19T19:06:09.000Z"),Ne=isNaN(Re.getFullYear())?new Date("2/19/17"):Re,ke=2017==Ne.getFullYear();function Ie(e,t){var r=new Date(e);if(ke)return t>0?r.setTime(r.getTime()+60*r.getTimezoneOffset()*1e3):t<0&&r.setTime(r.getTime()-60*r.getTimezoneOffset()*1e3),r;if(e instanceof Date)return e;if(1917==Ne.getFullYear()&&!isNaN(r.getFullYear())){var n=r.getFullYear();return e.indexOf(""+n)>-1||r.setFullYear(r.getFullYear()+100),r}var a=e.match(/\d+/g)||["2017","2","19","0","0","0"],i=new Date(+a[0],+a[1]-1,+a[2],+a[3]||0,+a[4]||0,+a[5]||0);return e.indexOf("Z")>-1&&(i=new Date(i.getTime()-60*i.getTimezoneOffset()*1e3)),i}function De(e,t){if(u&&Buffer.isBuffer(e)){if(t){if(255==e[0]&&254==e[1])return Qe(e.slice(2).toString("utf16le"));if(254==e[1]&&255==e[2])return Qe(function(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r+1)+(e.charCodeAt(2*r)<<8));return t.join("")}(e.slice(2).toString("binary")))}return e.toString("binary")}if("undefined"!=typeof TextDecoder)try{if(t){if(255==e[0]&&254==e[1])return Qe(new TextDecoder("utf-16le").decode(e.slice(2)));if(254==e[0]&&255==e[1])return Qe(new TextDecoder("utf-16be").decode(e.slice(2)))}var r={"€":"","‚":"","ƒ":"","„":"","…":"","†":"","‡":"","ˆ":"","‰":"","Š":"","‹":"","Œ":"","Ž":"","‘":"","’":"","“":"","”":"","•":"","–":"","—":"","˜":"","™":"","š":"","›":"","œ":"","ž":"","Ÿ":""};return Array.isArray(e)&&(e=new Uint8Array(e)),new TextDecoder("latin1").decode(e).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,(function(e){return r[e]||e}))}catch(i){}for(var n=[],a=0;a!=e.length;++a)n.push(String.fromCharCode(e[a]));return n.join("")}function Pe(e){if("undefined"!=typeof JSON&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if("object"!=typeof e||null==e)return e;if(e instanceof Date)return new Date(e.getTime());var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=Pe(e[r]));return t}function Le(e,t){for(var r="";r.length<t;)r+=e;return r}function Me(e){var t=Number(e);if(!isNaN(t))return isFinite(t)?t:NaN;if(!/\d/.test(e))return t;var r=1,n=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,(function(){return r*=100,""}));return isNaN(t=Number(n))?(n=n.replace(/[(](.*)[)]/,(function(e,t){return r=-r,t})),isNaN(t=Number(n))?t:t/r):t/r}var Fe=["january","february","march","april","may","june","july","august","september","october","november","december"];function Ue(e){var t=new Date(e),r=new Date(NaN),n=t.getYear(),a=t.getMonth(),i=t.getDate();if(isNaN(i))return r;var s=e.toLowerCase();if(s.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if((s=s.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,"")).length>3&&-1==Fe.indexOf(s))return r}else if(s.match(/[a-z]/))return r;return n<0||n>8099?r:(a>0||i>1)&&101!=n?t:e.match(/[^-0-9:,\/\\]/)?r:t}function Be(e,t,r){if(e.FullPaths){var n;if("string"==typeof r)return n=u?p(r):function(e){for(var t=[],r=0,n=e.length+250,a=d(e.length+255),i=0;i<e.length;++i){var s=e.charCodeAt(i);if(s<128)a[r++]=s;else if(s<2048)a[r++]=192|s>>6&31,a[r++]=128|63&s;else if(s>=55296&&s<57344){s=64+(1023&s);var o=1023&e.charCodeAt(++i);a[r++]=240|s>>8&7,a[r++]=128|s>>2&63,a[r++]=128|o>>6&15|(3&s)<<4,a[r++]=128|63&o}else a[r++]=224|s>>12&15,a[r++]=128|s>>6&63,a[r++]=128|63&s;r>n&&(t.push(a.slice(0,r)),r=0,a=d(65535),n=65530)}return t.push(a.slice(0,r)),w(t)}(r),ge.utils.cfb_add(e,t,n);ge.utils.cfb_add(e,t,r)}else e.file(t,r)}function We(){return ge.utils.cfb_new()}var He='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r\n',Ge=be({"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"}),Ve=/[&<>'"]/g,je=/[\u0000-\u0008\u000b-\u001f]/g;function ze(e){return(e+"").replace(Ve,(function(e){return Ge[e]})).replace(je,(function(e){return"_x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+"_"}))}function $e(e){return ze(e).replace(/ /g,"_x0020_")}var Xe=/[\u0000-\u001f]/g;function Ye(e){for(var t="",r=0,n=0,a=0,i=0,s=0,o=0;r<e.length;)(n=e.charCodeAt(r++))<128?t+=String.fromCharCode(n):(a=e.charCodeAt(r++),n>191&&n<224?(s=(31&n)<<6,s|=63&a,t+=String.fromCharCode(s)):(i=e.charCodeAt(r++),n<240?t+=String.fromCharCode((15&n)<<12|(63&a)<<6|63&i):(o=((7&n)<<18|(63&a)<<12|(63&i)<<6|63&(s=e.charCodeAt(r++)))-65536,t+=String.fromCharCode(55296+(o>>>10&1023)),t+=String.fromCharCode(56320+(1023&o)))));return t}function Ke(e){var t,r,n,a=d(2*e.length),i=1,s=0,o=0;for(r=0;r<e.length;r+=i)i=1,(n=e.charCodeAt(r))<128?t=n:n<224?(t=64*(31&n)+(63&e.charCodeAt(r+1)),i=2):n<240?(t=4096*(15&n)+64*(63&e.charCodeAt(r+1))+(63&e.charCodeAt(r+2)),i=3):(i=4,t=262144*(7&n)+4096*(63&e.charCodeAt(r+1))+64*(63&e.charCodeAt(r+2))+(63&e.charCodeAt(r+3)),o=55296+((t-=65536)>>>10&1023),t=56320+(1023&t)),0!==o&&(a[s++]=255&o,a[s++]=o>>>8,o=0),a[s++]=t%256,a[s++]=t>>>8;return a.slice(0,s).toString("ucs2")}function Je(e){return p(e,"binary").toString("utf8")}var Ze="foo bar bazâð£",qe=u&&(Je(Ze)==Ye(Ze)&&Je||Ke(Ze)==Ye(Ze)&&Ke)||Ye,Qe=u?function(e){return p(e,"utf8").toString("binary")}:function(e){for(var t=[],r=0,n=0,a=0;r<e.length;)switch(n=e.charCodeAt(r++),!0){case n<128:t.push(String.fromCharCode(n));break;case n<2048:t.push(String.fromCharCode(192+(n>>6))),t.push(String.fromCharCode(128+(63&n)));break;case n>=55296&&n<57344:n-=55296,a=e.charCodeAt(r++)-56320+(n<<10),t.push(String.fromCharCode(240+(a>>18&7))),t.push(String.fromCharCode(144+(a>>12&63))),t.push(String.fromCharCode(128+(a>>6&63))),t.push(String.fromCharCode(128+(63&a)));break;default:t.push(String.fromCharCode(224+(n>>12))),t.push(String.fromCharCode(128+(n>>6&63))),t.push(String.fromCharCode(128+(63&n)))}return t.join("")},et=function(){var e=[["nbsp"," "],["middot","·"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map((function(e){return[new RegExp("&"+e[0]+";","ig"),e[1]]}));return function(t){for(var r=t.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,"\n").replace(/<[^>]*>/g,""),n=0;n<e.length;++n)r=r.replace(e[n][0],e[n][1]);return r}}(),tt=/(^\s|\s$|\n)/;function rt(e,t){return"<"+e+(t.match(tt)?' xml:space="preserve"':"")+">"+t+"</"+e+">"}function nt(e){return we(e).map((function(t){return" "+t+'="'+e[t]+'"'})).join("")}function at(e,t,r){return"<"+e+(null!=r?nt(r):"")+(null!=t?(t.match(tt)?' xml:space="preserve"':"")+">"+t+"</"+e:"/")+">"}function it(e,t){try{return e.toISOString().replace(/\.\d*/,"")}catch(r){if(t)throw r}return""}var st={CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/metadata/core-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/custom-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/extended-properties",CT:"http://schemas.openxmlformats.org/package/2006/content-types",RELS:"http://schemas.openxmlformats.org/package/2006/relationships",TCMNT:"http://schemas.microsoft.com/office/spreadsheetml/2018/threadedcomments",dc:"http://purl.org/dc/elements/1.1/",dcterms:"http://purl.org/dc/terms/",dcmitype:"http://purl.org/dc/dcmitype/",mx:"http://schemas.microsoft.com/office/mac/excel/2008/main",r:"http://schemas.openxmlformats.org/officeDocument/2006/relationships",sjs:"http://schemas.openxmlformats.org/package/2006/sheetjs/core-properties",vt:"http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes",xsi:"http://www.w3.org/2001/XMLSchema-instance",xsd:"http://www.w3.org/2001/XMLSchema"},ot=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"],lt={o:"urn:schemas-microsoft-com:office:office",x:"urn:schemas-microsoft-com:office:excel",ss:"urn:schemas-microsoft-com:office:spreadsheet",dt:"uuid:C2F41010-65B3-11d1-A29F-00AA00C14882",mv:"http://macVmlSchemaUri",v:"urn:schemas-microsoft-com:vml",html:"http://www.w3.org/TR/REC-html40"};var ft=function(e){for(var t=[],r=0;r<e[0].length;++r)if(e[0][r])for(var n=0,a=e[0][r].length;n<a;n+=10240)t.push.apply(t,e[0][r].slice(n,n+10240));return t},ct=u?function(e){return e[0].length>0&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0].map((function(e){return Buffer.isBuffer(e)?e:p(e)}))):ft(e)}:ft,ht=function(e,t,r){for(var n=[],a=t;a<r;a+=2)n.push(String.fromCharCode(kt(e,a)));return n.join("").replace(E,"")},ut=u?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf16le",t,r).replace(E,""):ht(e,t,r)}:ht,pt=function(e,t,r){for(var n=[],a=t;a<t+r;++a)n.push(("0"+e[a].toString(16)).slice(-2));return n.join("")},dt=u?function(e,t,r){return Buffer.isBuffer(e)?e.toString("hex",t,t+r):pt(e,t,r)}:pt,mt=function(e,t,r){for(var n=[],a=t;a<r;a++)n.push(String.fromCharCode(Nt(e,a)));return n.join("")},gt=u?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf8",t,r):mt(e,t,r)}:mt,vt=function(e,t){var r=Dt(e,t);return r>0?gt(e,t+4,t+4+r-1):""},Tt=vt,wt=function(e,t){var r=Dt(e,t);return r>0?gt(e,t+4,t+4+r-1):""},Et=wt,bt=function(e,t){var r=2*Dt(e,t);return r>0?gt(e,t+4,t+4+r-1):""},St=bt,At=function(e,t){var r=Dt(e,t);return r>0?ut(e,t+4,t+4+r):""},_t=At,yt=function(e,t){var r=Dt(e,t);return r>0?gt(e,t+4,t+4+r):""},Ot=yt,xt=function(e,t){return function(e,t){for(var r=1-2*(e[t+7]>>>7),n=((127&e[t+7])<<4)+(e[t+6]>>>4&15),a=15&e[t+6],i=5;i>=0;--i)a=256*a+e[t+i];return 2047==n?0==a?r*(1/0):NaN:(0==n?n=-1022:(n-=1023,a+=Math.pow(2,52)),r*Math.pow(2,n-52)*a)}(e,t)},Ct=xt,Rt=function(e){return Array.isArray(e)||"undefined"!=typeof Uint8Array&&e instanceof Uint8Array};u&&(Tt=function(e,t){if(!Buffer.isBuffer(e))return vt(e,t);var r=e.readUInt32LE(t);return r>0?e.toString("utf8",t+4,t+4+r-1):""},Et=function(e,t){if(!Buffer.isBuffer(e))return wt(e,t);var r=e.readUInt32LE(t);return r>0?e.toString("utf8",t+4,t+4+r-1):""},St=function(e,t){if(!Buffer.isBuffer(e))return bt(e,t);var r=2*e.readUInt32LE(t);return e.toString("utf16le",t+4,t+4+r-1)},_t=function(e,t){if(!Buffer.isBuffer(e))return At(e,t);var r=e.readUInt32LE(t);return e.toString("utf16le",t+4,t+4+r)},Ot=function(e,t){if(!Buffer.isBuffer(e))return yt(e,t);var r=e.readUInt32LE(t);return e.toString("utf8",t+4,t+4+r)},Ct=function(e,t){return Buffer.isBuffer(e)?e.readDoubleLE(t):xt(e,t)},Rt=function(e){return Buffer.isBuffer(e)||Array.isArray(e)||"undefined"!=typeof Uint8Array&&e instanceof Uint8Array});var Nt=function(e,t){return e[t]},kt=function(e,t){return 256*e[t+1]+e[t]},It=function(e,t){var r=256*e[t+1]+e[t];return r<32768?r:-1*(65535-r+1)},Dt=function(e,t){return e[t+3]*(1<<24)+(e[t+2]<<16)+(e[t+1]<<8)+e[t]},Pt=function(e,t){return e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]},Lt=function(e,t){return e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3]};function Mt(e,t){var r,n,a,i,s,l,f="",c=[];switch(t){case"dbcs":if(l=this.l,u&&Buffer.isBuffer(this))f=this.slice(this.l,this.l+2*e).toString("utf16le");else for(s=0;s<e;++s)f+=String.fromCharCode(kt(this,l)),l+=2;e*=2;break;case"utf8":f=gt(this,this.l,this.l+e);break;case"utf16le":e*=2,f=ut(this,this.l,this.l+e);break;case"wstr":return Mt.call(this,e,"dbcs");case"lpstr-ansi":f=Tt(this,this.l),e=4+Dt(this,this.l);break;case"lpstr-cp":f=Et(this,this.l),e=4+Dt(this,this.l);break;case"lpwstr":f=St(this,this.l),e=4+2*Dt(this,this.l);break;case"lpp4":e=4+Dt(this,this.l),f=_t(this,this.l),2&e&&(e+=2);break;case"8lpp4":e=4+Dt(this,this.l),f=Ot(this,this.l),3&e&&(e+=4-(3&e));break;case"cstr":for(e=0,f="";0!==(a=Nt(this,this.l+e++));)c.push(o(a));f=c.join("");break;case"_wstr":for(e=0,f="";0!==(a=kt(this,this.l+e));)c.push(o(a)),e+=2;e+=2,f=c.join("");break;case"dbcs-cont":for(f="",l=this.l,s=0;s<e;++s){if(this.lens&&-1!==this.lens.indexOf(l))return a=Nt(this,l),this.l=l+1,i=Mt.call(this,e-s,a?"dbcs-cont":"sbcs-cont"),c.join("")+i;c.push(o(kt(this,l))),l+=2}f=c.join(""),e*=2;break;case"cpstr":case"sbcs-cont":for(f="",l=this.l,s=0;s!=e;++s){if(this.lens&&-1!==this.lens.indexOf(l))return a=Nt(this,l),this.l=l+1,i=Mt.call(this,e-s,a?"dbcs-cont":"sbcs-cont"),c.join("")+i;c.push(o(Nt(this,l))),l+=1}f=c.join("");break;default:switch(e){case 1:return r=Nt(this,this.l),this.l++,r;case 2:return r=("i"===t?It:kt)(this,this.l),this.l+=2,r;case 4:case-4:return"i"!==t&&128&this[this.l+3]?(n=Dt(this,this.l),this.l+=4,n):(r=(e>0?Pt:Lt)(this,this.l),this.l+=4,r);case 8:case-8:if("f"===t)return n=8==e?Ct(this,this.l):Ct([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,n;e=8;case 16:f=dt(this,this.l,e)}}return this.l+=e,f}var Ft=function(e,t,r){e[r]=255&t,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24&255},Ut=function(e,t,r){e[r]=255&t,e[r+1]=t>>8&255,e[r+2]=t>>16&255,e[r+3]=t>>24&255},Bt=function(e,t,r){e[r]=255&t,e[r+1]=t>>>8&255};function Wt(e,t,r){var n=0,a=0;if("dbcs"===r){for(a=0;a!=t.length;++a)Bt(this,t.charCodeAt(a),this.l+2*a);n=2*t.length}else if("sbcs"===r){for(t=t.replace(/[^\x00-\x7F]/g,"_"),a=0;a!=t.length;++a)this[this.l+a]=255&t.charCodeAt(a);n=t.length}else{if("hex"===r){for(;a<e;++a)this[this.l++]=parseInt(t.slice(2*a,2*a+2),16)||0;return this}if("utf16le"===r){var i=Math.min(this.l+e,this.length);for(a=0;a<Math.min(t.length,e);++a){var s=t.charCodeAt(a);this[this.l++]=255&s,this[this.l++]=s>>8}for(;this.l<i;)this[this.l++]=0;return this}switch(e){case 1:n=1,this[this.l]=255&t;break;case 2:n=2,this[this.l]=255&t,t>>>=8,this[this.l+1]=255&t;break;case 3:n=3,this[this.l]=255&t,t>>>=8,this[this.l+1]=255&t,t>>>=8,this[this.l+2]=255&t;break;case 4:n=4,Ft(this,t,this.l);break;case 8:if(n=8,"f"===r){!function(e,t,r){var n=(t<0||1/t==-1/0?1:0)<<7,a=0,i=0,s=n?-t:t;isFinite(s)?0==s?a=i=0:(a=Math.floor(Math.log(s)/Math.LN2),i=s*Math.pow(2,52-a),a<=-1023&&(!isFinite(i)||i<Math.pow(2,52))?a=-1022:(i-=Math.pow(2,52),a+=1023)):(a=2047,i=isNaN(t)?26985:0);for(var o=0;o<=5;++o,i/=256)e[r+o]=255&i;e[r+6]=(15&a)<<4|15&i,e[r+7]=a>>4|n}(this,t,this.l);break}case 16:break;case-4:n=4,Ut(this,t,this.l)}}return this.l+=n,this}function Ht(e,t){var r=dt(this,this.l,e.length>>1);if(r!==e)throw new Error(t+"Expected "+e+" saw "+r);this.l+=e.length>>1}function Gt(e,t){e.l=t,e.read_shift=Mt,e.chk=Ht,e.write_shift=Wt}function Vt(e,t){e.l+=t}function jt(e){var t=d(e);return Gt(t,0),t}function zt(){var e=[],t=u?256:2048,r=function(e){var t=jt(e);return Gt(t,0),t},n=r(t),a=function(){n&&(n.length>n.l&&((n=n.slice(0,n.l)).l=n.length),n.length>0&&e.push(n),n=null)},i=function(e){return n&&e<n.length-n.l?n:(a(),n=r(Math.max(e+1,t)))};return{next:i,push:function(e){a(),null==(n=e).l&&(n.l=n.length),i(t)},end:function(){return a(),w(e)},_bufs:e}}function $t(e,t,r,n){var a,i=+t;if(!isNaN(i)){n||(n=$i[i].p||(r||[]).length||0),a=1+(i>=128?1:0)+1,n>=128&&++a,n>=16384&&++a,n>=2097152&&++a;var s=e.next(a);i<=127?s.write_shift(1,i):(s.write_shift(1,128+(127&i)),s.write_shift(1,i>>7));for(var o=0;4!=o;++o){if(!(n>=128)){s.write_shift(1,n);break}s.write_shift(1,128+(127&n)),n>>=7}n>0&&Rt(r)&&e.push(r)}}function Xt(e,t,r){var n=Pe(e);if(t.s?(n.cRel&&(n.c+=t.s.c),n.rRel&&(n.r+=t.s.r)):(n.cRel&&(n.c+=t.c),n.rRel&&(n.r+=t.r)),!r||r.biff<12){for(;n.c>=256;)n.c-=256;for(;n.r>=65536;)n.r-=65536}return n}function Yt(e,t,r){var n=Pe(e);return n.s=Xt(n.s,t.s,r),n.e=Xt(n.e,t.s,r),n}function Kt(e,t){if(e.cRel&&e.c<0)for(e=Pe(e);e.c<0;)e.c+=t>8?16384:256;if(e.rRel&&e.r<0)for(e=Pe(e);e.r<0;)e.r+=t>8?1048576:t>5?65536:16384;var r=rr(e);return e.cRel||null==e.cRel||(r=r.replace(/^([A-Z])/,"$$$1")),e.rRel||null==e.rRel||(r=function(e){return e.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")}(r)),r}function Jt(e,t){return 0!=e.s.r||e.s.rRel||e.e.r!=(t.biff>=12?1048575:t.biff>=8?65536:16384)||e.e.rRel?0!=e.s.c||e.s.cRel||e.e.c!=(t.biff>=12?16383:255)||e.e.cRel?Kt(e.s,t.biff)+":"+Kt(e.e,t.biff):(e.s.rRel?"":"$")+qt(e.s.r)+":"+(e.e.rRel?"":"$")+qt(e.e.r):(e.s.cRel?"":"$")+er(e.s.c)+":"+(e.e.cRel?"":"$")+er(e.e.c)}function Zt(e){return parseInt(e.replace(/\$(\d+)$/,"$1"),10)-1}function qt(e){return""+(e+1)}function Qt(e){for(var t=e.replace(/^\$([A-Z])/,"$1"),r=0,n=0;n!==t.length;++n)r=26*r+t.charCodeAt(n)-64;return r-1}function er(e){if(e<0)throw new Error("invalid column "+e);var t="";for(++e;e;e=Math.floor((e-1)/26))t=String.fromCharCode((e-1)%26+65)+t;return t}function tr(e){for(var t=0,r=0,n=0;n<e.length;++n){var a=e.charCodeAt(n);a>=48&&a<=57?t=10*t+(a-48):a>=65&&a<=90&&(r=26*r+(a-64))}return{c:r-1,r:t-1}}function rr(e){for(var t=e.c+1,r="";t;t=(t-1)/26|0)r=String.fromCharCode((t-1)%26+65)+r;return r+(e.r+1)}function nr(e){var t=e.indexOf(":");return-1==t?{s:tr(e),e:tr(e)}:{s:tr(e.slice(0,t)),e:tr(e.slice(t+1))}}function ar(e,t){return void 0===t||"number"==typeof t?ar(e.s,e.e):("string"!=typeof e&&(e=rr(e)),"string"!=typeof t&&(t=rr(t)),e==t?e:e+":"+t)}function ir(e){var t={s:{c:0,r:0},e:{c:0,r:0}},r=0,n=0,a=0,i=e.length;for(r=0;n<i&&!((a=e.charCodeAt(n)-64)<1||a>26);++n)r=26*r+a;for(t.s.c=--r,r=0;n<i&&!((a=e.charCodeAt(n)-48)<0||a>9);++n)r=10*r+a;if(t.s.r=--r,n===i||10!=a)return t.e.c=t.s.c,t.e.r=t.s.r,t;for(++n,r=0;n!=i&&!((a=e.charCodeAt(n)-64)<1||a>26);++n)r=26*r+a;for(t.e.c=--r,r=0;n!=i&&!((a=e.charCodeAt(n)-48)<0||a>9);++n)r=10*r+a;return t.e.r=--r,t}function sr(e,t){var r="d"==e.t&&t instanceof Date;if(null!=e.z)try{return e.w=ce(e.z,r?_e(t):t)}catch(n){}try{return e.w=ce((e.XF||{}).numFmtId||(r?14:0),r?_e(t):t)}catch(n){return""+t}}function or(e,t,r){return null==e||null==e.t||"z"==e.t?"":void 0!==e.w?e.w:("d"==e.t&&!e.z&&r&&r.dateNF&&(e.z=r.dateNF),"e"==e.t?Gr[e.v]||e.v:sr(e,null==t?e.v:t))}function lr(e,t){var r=t&&t.sheet?t.sheet:"Sheet1",n={};return n[r]=e,{SheetNames:[r],Sheets:n}}function fr(e,t,r){var n=r||{},a=e?Array.isArray(e):n.dense,i=e||(a?[]:{}),s=0,o=0;if(i&&null!=n.origin){if("number"==typeof n.origin)s=n.origin;else{var l="string"==typeof n.origin?tr(n.origin):n.origin;s=l.r,o=l.c}i["!ref"]||(i["!ref"]="A1:A1")}var f={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(i["!ref"]){var c=ir(i["!ref"]);f.s.c=c.s.c,f.s.r=c.s.r,f.e.c=Math.max(f.e.c,c.e.c),f.e.r=Math.max(f.e.r,c.e.r),-1==s&&(f.e.r=s=c.e.r+1)}for(var h=0;h!=t.length;++h)if(t[h]){if(!Array.isArray(t[h]))throw new Error("aoa_to_sheet expects an array of arrays");for(var u=0;u!=t[h].length;++u)if(void 0!==t[h][u]){var p={v:t[h][u]},d=s+h,m=o+u;if(f.s.r>d&&(f.s.r=d),f.s.c>m&&(f.s.c=m),f.e.r<d&&(f.e.r=d),f.e.c<m&&(f.e.c=m),!t[h][u]||"object"!=typeof t[h][u]||Array.isArray(t[h][u])||t[h][u]instanceof Date)if(Array.isArray(p.v)&&(p.f=t[h][u][1],p.v=p.v[0]),null===p.v)if(p.f)p.t="n";else if(n.nullError)p.t="e",p.v=0;else{if(!n.sheetStubs)continue;p.t="z"}else"number"==typeof p.v?p.t="n":"boolean"==typeof p.v?p.t="b":p.v instanceof Date?(p.z=n.dateNF||k[14],n.cellDates?(p.t="d",p.w=ce(p.z,_e(p.v))):(p.t="n",p.v=_e(p.v),p.w=ce(p.z,p.v))):p.t="s";else p=t[h][u];if(a)i[d]||(i[d]=[]),i[d][m]&&i[d][m].z&&(p.z=i[d][m].z),i[d][m]=p;else{var g=rr({c:m,r:d});i[g]&&i[g].z&&(p.z=i[g].z),i[g]=p}}}return f.s.c<1e7&&(i["!ref"]=ar(f)),i}function cr(e,t){return fr(null,e,t)}function hr(e,t){return t||(t=jt(4)),t.write_shift(4,e),t}function ur(e){var t=e.read_shift(4);return 0===t?"":e.read_shift(t,"dbcs")}function pr(e,t){var r=!1;return null==t&&(r=!0,t=jt(4+2*e.length)),t.write_shift(4,e.length),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}function dr(e){return{ich:e.read_shift(2),ifnt:e.read_shift(2)}}function mr(e,t){var r=e.l,n=e.read_shift(1),a=ur(e),i=[],s={t:a,h:a};if(1&n){for(var o=e.read_shift(4),l=0;l!=o;++l)i.push(dr(e));s.r=i}else s.r=[{ich:0,ifnt:0}];return e.l=r+t,s}var gr=mr;function vr(e,t){var r=!1;return null==t&&(r=!0,t=jt(23+4*e.t.length)),t.write_shift(1,1),pr(e.t,t),t.write_shift(4,1),function(e,t){t||(t=jt(4)),t.write_shift(2,e.ich||0),t.write_shift(2,e.ifnt||0)}({ich:0,ifnt:0},t),r?t.slice(0,t.l):t}function Tr(e){var t=e.read_shift(4),r=e.read_shift(2);return r+=e.read_shift(1)<<16,e.l++,{c:t,iStyleRef:r}}function wr(e,t){return null==t&&(t=jt(8)),t.write_shift(-4,e.c),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}function Er(e){var t=e.read_shift(2);return t+=e.read_shift(1)<<16,e.l++,{c:-1,iStyleRef:t}}function br(e,t){return null==t&&(t=jt(4)),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}var Sr=ur,Ar=pr;function _r(e){var t=e.read_shift(4);return 0===t||4294967295===t?"":e.read_shift(t,"dbcs")}function yr(e,t){var r=!1;return null==t&&(r=!0,t=jt(127)),t.write_shift(4,e.length>0?e.length:4294967295),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}var Or=ur,xr=_r,Cr=yr;function Rr(e){var t=e.slice(e.l,e.l+4),r=1&t[0],n=2&t[0];e.l+=4;var a=0===n?Ct([0,0,0,0,252&t[0],t[1],t[2],t[3]],0):Pt(t,0)>>2;return r?a/100:a}function Nr(e,t){null==t&&(t=jt(4));var r=0,n=0,a=100*e;if(e==(0|e)&&e>=-(1<<29)&&e<1<<29?n=1:a==(0|a)&&a>=-(1<<29)&&a<1<<29&&(n=1,r=1),!n)throw new Error("unsupported RkNumber "+e);t.write_shift(-4,((r?a:e)<<2)+(r+2))}function kr(e){var t={s:{},e:{}};return t.s.r=e.read_shift(4),t.e.r=e.read_shift(4),t.s.c=e.read_shift(4),t.e.c=e.read_shift(4),t}var Ir=kr,Dr=function(e,t){return t||(t=jt(16)),t.write_shift(4,e.s.r),t.write_shift(4,e.e.r),t.write_shift(4,e.s.c),t.write_shift(4,e.e.c),t};function Pr(e){if(e.length-e.l<8)throw"XLS Xnum Buffer underflow";return e.read_shift(8,"f")}function Lr(e,t){return(t||jt(8)).write_shift(8,e,"f")}function Mr(e,t){if(t||(t=jt(8)),!e||e.auto)return t.write_shift(4,0),t.write_shift(4,0),t;null!=e.index?(t.write_shift(1,2),t.write_shift(1,e.index)):null!=e.theme?(t.write_shift(1,6),t.write_shift(1,e.theme)):(t.write_shift(1,5),t.write_shift(1,0));var r=e.tint||0;if(r>0?r*=32767:r<0&&(r*=32768),t.write_shift(2,r),e.rgb&&null==e.theme){var n=e.rgb||"FFFFFF";"number"==typeof n&&(n=("000000"+n.toString(16)).slice(-6)),t.write_shift(1,parseInt(n.slice(0,2),16)),t.write_shift(1,parseInt(n.slice(2,4),16)),t.write_shift(1,parseInt(n.slice(4,6),16)),t.write_shift(1,255)}else t.write_shift(2,0),t.write_shift(1,0),t.write_shift(1,0);return t}var Fr=80,Ur={1:{n:"CodePage",t:2},2:{n:"Category",t:Fr},3:{n:"PresentationFormat",t:Fr},4:{n:"ByteCount",t:3},5:{n:"LineCount",t:3},6:{n:"ParagraphCount",t:3},7:{n:"SlideCount",t:3},8:{n:"NoteCount",t:3},9:{n:"HiddenCount",t:3},10:{n:"MultimediaClipCount",t:3},11:{n:"ScaleCrop",t:11},12:{n:"HeadingPairs",t:4108},13:{n:"TitlesOfParts",t:4126},14:{n:"Manager",t:Fr},15:{n:"Company",t:Fr},16:{n:"LinksUpToDate",t:11},17:{n:"CharacterCount",t:3},19:{n:"SharedDoc",t:11},22:{n:"HyperlinksChanged",t:11},23:{n:"AppVersion",t:3,p:"version"},24:{n:"DigSig",t:65},26:{n:"ContentType",t:Fr},27:{n:"ContentStatus",t:Fr},28:{n:"Language",t:Fr},29:{n:"Version",t:Fr},255:{},2147483648:{n:"Locale",t:19},2147483651:{n:"Behavior",t:19},1919054434:{}},Br={1:{n:"CodePage",t:2},2:{n:"Title",t:Fr},3:{n:"Subject",t:Fr},4:{n:"Author",t:Fr},5:{n:"Keywords",t:Fr},6:{n:"Comments",t:Fr},7:{n:"Template",t:Fr},8:{n:"LastAuthor",t:Fr},9:{n:"RevNumber",t:Fr},10:{n:"EditTime",t:64},11:{n:"LastPrinted",t:64},12:{n:"CreatedDate",t:64},13:{n:"ModifiedDate",t:64},14:{n:"PageCount",t:3},15:{n:"WordCount",t:3},16:{n:"CharCount",t:3},17:{n:"Thumbnail",t:71},18:{n:"Application",t:Fr},19:{n:"DocSecurity",t:3},255:{},2147483648:{n:"Locale",t:19},2147483651:{n:"Behavior",t:19},1919054434:{}};function Wr(e){return e.map((function(e){return[e>>16&255,e>>8&255,255&e]}))}var Hr=Pe(Wr([0,16777215,16711680,65280,255,16776960,16711935,65535,0,16777215,16711680,65280,255,16776960,16711935,65535,8388608,32768,128,8421376,8388736,32896,12632256,8421504,10066431,10040166,16777164,13434879,6684774,16744576,26316,13421823,128,16711935,16776960,65535,8388736,8388608,32896,255,52479,13434879,13434828,16777113,10079487,16751052,13408767,16764057,3368703,3394764,10079232,16763904,16750848,16737792,6710937,9868950,13158,3381606,13056,3355392,10040064,10040166,3355545,3355443,16777215,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0])),Gr={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},Vr={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"},jr={workbooks:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml",xlsm:"application/vnd.ms-excel.sheet.macroEnabled.main+xml",xlsb:"application/vnd.ms-excel.sheet.binary.macroEnabled.main",xlam:"application/vnd.ms-excel.addin.macroEnabled.main+xml",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml"},strs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml",xlsb:"application/vnd.ms-excel.sharedStrings"},comments:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml",xlsb:"application/vnd.ms-excel.comments"},sheets:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml",xlsb:"application/vnd.ms-excel.worksheet"},charts:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml",xlsb:"application/vnd.ms-excel.chartsheet"},dialogs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml",xlsb:"application/vnd.ms-excel.dialogsheet"},macros:{xlsx:"application/vnd.ms-excel.macrosheet+xml",xlsb:"application/vnd.ms-excel.macrosheet"},metadata:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml",xlsb:"application/vnd.ms-excel.sheetMetadata"},styles:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml",xlsb:"application/vnd.ms-excel.styles"}};function zr(e,t){var r,n=function(e){for(var t=[],r=we(e),n=0;n!==r.length;++n)null==t[e[r[n]]]&&(t[e[r[n]]]=[]),t[e[r[n]]].push(r[n]);return t}(Vr),a=[];a[a.length]=He,a[a.length]=at("Types",null,{xmlns:st.CT,"xmlns:xsd":st.xsd,"xmlns:xsi":st.xsi}),a=a.concat([["xml","application/xml"],["bin","application/vnd.ms-excel.sheet.binary.macroEnabled.main"],["vml","application/vnd.openxmlformats-officedocument.vmlDrawing"],["data","application/vnd.openxmlformats-officedocument.model+data"],["bmp","image/bmp"],["png","image/png"],["gif","image/gif"],["emf","image/x-emf"],["wmf","image/x-wmf"],["jpg","image/jpeg"],["jpeg","image/jpeg"],["tif","image/tiff"],["tiff","image/tiff"],["pdf","application/pdf"],["rels","application/vnd.openxmlformats-package.relationships+xml"]].map((function(e){return at("Default",null,{Extension:e[0],ContentType:e[1]})})));var i=function(n){e[n]&&e[n].length>0&&(r=e[n][0],a[a.length]=at("Override",null,{PartName:("/"==r[0]?"":"/")+r,ContentType:jr[n][t.bookType]||jr[n].xlsx}))},s=function(r){(e[r]||[]).forEach((function(e){a[a.length]=at("Override",null,{PartName:("/"==e[0]?"":"/")+e,ContentType:jr[r][t.bookType]||jr[r].xlsx})}))},o=function(t){(e[t]||[]).forEach((function(e){a[a.length]=at("Override",null,{PartName:("/"==e[0]?"":"/")+e,ContentType:n[t][0]})}))};return i("workbooks"),s("sheets"),s("charts"),o("themes"),["strs","styles"].forEach(i),["coreprops","extprops","custprops"].forEach(o),o("vba"),o("comments"),o("threadedcomments"),o("drawings"),s("metadata"),o("people"),a.length>2&&(a[a.length]="</Types>",a[1]=a[1].replace("/>",">")),a.join("")}var $r={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",SHEET:"http://sheetjs.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",XLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLink",CXML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXml",CXMLP:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXmlProps",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",CHART:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chart",CHARTEX:"http://schemas.microsoft.com/office/2014/relationships/chartEx",CS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chartsheet",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/dialogsheet",MS:"http://schemas.microsoft.com/office/2006/relationships/xlMacrosheet",IMG:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image",DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function Xr(e){var t=e.lastIndexOf("/");return e.slice(0,t+1)+"_rels/"+e.slice(t+1)+".rels"}function Yr(e){var t=[He,at("Relationships",null,{xmlns:st.RELS})];return we(e["!id"]).forEach((function(r){t[t.length]=at("Relationship",null,e["!id"][r])})),t.length>2&&(t[t.length]="</Relationships>",t[1]=t[1].replace("/>",">")),t.join("")}function Kr(e,t,r,n,a,i){if(a||(a={}),e["!id"]||(e["!id"]={}),e["!idx"]||(e["!idx"]=1),t<0)for(t=e["!idx"];e["!id"]["rId"+t];++t);if(e["!idx"]=t+1,a.Id="rId"+t,a.Type=n,a.Target=r,i?a.TargetMode=i:[$r.HLINK,$r.XPATH,$r.XMISS].indexOf(a.Type)>-1&&(a.TargetMode="External"),e["!id"][a.Id])throw new Error("Cannot rewrite rId "+t);return e["!id"][a.Id]=a,e[("/"+a.Target).replace("//","/")]=a,t}function Jr(e,t,r){return['  <rdf:Description rdf:about="'+e+'">\n','    <rdf:type rdf:resource="http://docs.oasis-open.org/ns/office/1.2/meta/'+(r||"odf")+"#"+t+'"/>\n',"  </rdf:Description>\n"].join("")}function Zr(){return'<office:document-meta xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:xlink="http://www.w3.org/1999/xlink" office:version="1.2"><office:meta><meta:generator>SheetJS '+e.version+"</meta:generator></office:meta></office:document-meta>"}var qr=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]];function Qr(e,t,r,n,a){null==a[e]&&null!=t&&""!==t&&(a[e]=t,t=ze(t),n[n.length]=r?at(e,t,r):rt(e,t))}function en(e,t){var r=t||{},n=[He,at("cp:coreProperties",null,{"xmlns:cp":st.CORE_PROPS,"xmlns:dc":st.dc,"xmlns:dcterms":st.dcterms,"xmlns:dcmitype":st.dcmitype,"xmlns:xsi":st.xsi})],a={};if(!e&&!r.Props)return n.join("");e&&(null!=e.CreatedDate&&Qr("dcterms:created","string"==typeof e.CreatedDate?e.CreatedDate:it(e.CreatedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},n,a),null!=e.ModifiedDate&&Qr("dcterms:modified","string"==typeof e.ModifiedDate?e.ModifiedDate:it(e.ModifiedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},n,a));for(var i=0;i!=qr.length;++i){var s=qr[i],o=r.Props&&null!=r.Props[s[1]]?r.Props[s[1]]:e?e[s[1]]:null;!0===o?o="1":!1===o?o="0":"number"==typeof o&&(o=String(o)),null!=o&&Qr(s[0],o,null,n,a)}return n.length>2&&(n[n.length]="</cp:coreProperties>",n[1]=n[1].replace("/>",">")),n.join("")}var tn=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]],rn=["Worksheets","SheetNames","NamedRanges","DefinedNames","Chartsheets","ChartNames"];function nn(e){var t=[],r=at;return e||(e={}),e.Application="SheetJS",t[t.length]=He,t[t.length]=at("Properties",null,{xmlns:st.EXT_PROPS,"xmlns:vt":st.vt}),tn.forEach((function(n){if(void 0!==e[n[1]]){var a;switch(n[2]){case"string":a=ze(String(e[n[1]]));break;case"bool":a=e[n[1]]?"true":"false"}void 0!==a&&(t[t.length]=r(n[0],a))}})),t[t.length]=r("HeadingPairs",r("vt:vector",r("vt:variant","<vt:lpstr>Worksheets</vt:lpstr>")+r("vt:variant",r("vt:i4",String(e.Worksheets))),{size:2,baseType:"variant"})),t[t.length]=r("TitlesOfParts",r("vt:vector",e.SheetNames.map((function(e){return"<vt:lpstr>"+ze(e)+"</vt:lpstr>"})).join(""),{size:e.Worksheets,baseType:"lpstr"})),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}function an(e){var t=[He,at("Properties",null,{xmlns:st.CUST_PROPS,"xmlns:vt":st.vt})];if(!e)return t.join("");var r=1;return we(e).forEach((function(n){++r,t[t.length]=at("property",function(e,t){switch(typeof e){case"string":var r=at("vt:lpwstr",ze(e));return t&&(r=r.replace(/&quot;/g,"_x0022_")),r;case"number":return at((0|e)==e?"vt:i4":"vt:r8",ze(String(e)));case"boolean":return at("vt:bool",e?"true":"false")}if(e instanceof Date)return at("vt:filetime",it(e));throw new Error("Unable to serialize "+e)}(e[n],!0),{fmtid:"{D5CDD505-2E9C-101B-9397-08002B2CF9AE}",pid:r,name:ze(n)})})),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}var sn={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"};function on(e,t){var r=jt(4),n=jt(4);switch(r.write_shift(4,80==e?31:e),e){case 3:n.write_shift(-4,t);break;case 5:(n=jt(8)).write_shift(8,t,"f");break;case 11:n.write_shift(4,t?1:0);break;case 64:n=function(e){var t=("string"==typeof e?new Date(Date.parse(e)):e).getTime()/1e3+11644473600,r=t%Math.pow(2,32),n=(t-r)/Math.pow(2,32);n*=1e7;var a=(r*=1e7)/Math.pow(2,32)|0;a>0&&(r%=Math.pow(2,32),n+=a);var i=jt(8);return i.write_shift(4,r),i.write_shift(4,n),i}(t);break;case 31:case 80:for((n=jt(4+2*(t.length+1)+(t.length%2?0:2))).write_shift(4,t.length+1),n.write_shift(0,t,"dbcs");n.l!=n.length;)n.write_shift(1,0);break;default:throw new Error("TypedPropertyValue unrecognized type "+e+" "+t)}return w([r,n])}var ln=["CodePage","Thumbnail","_PID_LINKBASE","_PID_HLINKS","SystemIdentifier","FMTID"];function fn(e){switch(typeof e){case"boolean":return 11;case"number":return(0|e)==e?3:5;case"string":return 31;case"object":if(e instanceof Date)return 64}return-1}function cn(e,t,r){var n=jt(8),a=[],i=[],s=8,o=0,l=jt(8),f=jt(8);if(l.write_shift(4,2),l.write_shift(4,1200),f.write_shift(4,1),i.push(l),a.push(f),s+=8+l.length,!t){(f=jt(8)).write_shift(4,0),a.unshift(f);var c=[jt(4)];for(c[0].write_shift(4,e.length),o=0;o<e.length;++o){var h=e[o][0];for((l=jt(8+2*(h.length+1)+(h.length%2?0:2))).write_shift(4,o+2),l.write_shift(4,h.length+1),l.write_shift(0,h,"dbcs");l.l!=l.length;)l.write_shift(1,0);c.push(l)}l=w(c),i.unshift(l),s+=8+l.length}for(o=0;o<e.length;++o)if((!t||t[e[o][0]])&&!(ln.indexOf(e[o][0])>-1||rn.indexOf(e[o][0])>-1)&&null!=e[o][1]){var u=e[o][1],p=0;if(t){var d=r[p=+t[e[o][0]]];if("version"==d.p&&"string"==typeof u){var m=u.split(".");u=(+m[0]<<16)+(+m[1]||0)}l=on(d.t,u)}else{var g=fn(u);-1==g&&(g=31,u=String(u)),l=on(g,u)}i.push(l),(f=jt(8)).write_shift(4,t?p:2+o),a.push(f),s+=8+l.length}var v=8*(i.length+1);for(o=0;o<i.length;++o)a[o].write_shift(4,v),v+=i[o].length;return n.write_shift(4,s),n.write_shift(4,i.length),w([n].concat(a).concat(i))}function hn(e,t,r,n,a,i){var s=jt(a?68:48),o=[s];s.write_shift(2,65534),s.write_shift(2,0),s.write_shift(4,842412599),s.write_shift(16,ge.utils.consts.HEADER_CLSID,"hex"),s.write_shift(4,a?2:1),s.write_shift(16,t,"hex"),s.write_shift(4,a?68:48);var l=cn(e,r,n);if(o.push(l),a){var f=cn(a,null,null);s.write_shift(16,i,"hex"),s.write_shift(4,68+l.length),o.push(f)}return w(o)}function un(e,t){return t||(t=jt(2)),t.write_shift(2,+!!e),t}function pn(e){return e.read_shift(2,"u")}function dn(e,t){return t||(t=jt(2)),t.write_shift(2,e),t}function mn(e,t,r){return r||(r=jt(2)),r.write_shift(1,"e"==t?+e:+!!e),r.write_shift(1,"e"==t?1:0),r}function gn(e,t,r){var n=e.read_shift(r&&r.biff>=12?2:1),a="sbcs-cont";(r&&r.biff,r&&8!=r.biff)?12==r.biff&&(a="wstr"):e.read_shift(1)&&(a="dbcs-cont");return r.biff>=2&&r.biff<=5&&(a="cpstr"),n?e.read_shift(n,a):""}function vn(e){var t=e.t||"",r=jt(3);r.write_shift(2,t.length),r.write_shift(1,1);var n=jt(2*t.length);return n.write_shift(2*t.length,t,"utf16le"),w([r,n])}function Tn(e,t,r){return r||(r=jt(3+2*e.length)),r.write_shift(2,e.length),r.write_shift(1,1),r.write_shift(31,e,"utf16le"),r}function wn(e,t){t||(t=jt(6+2*e.length)),t.write_shift(4,1+e.length);for(var r=0;r<e.length;++r)t.write_shift(2,e.charCodeAt(r));return t.write_shift(2,0),t}function En(e){var t=jt(512),r=0,n=e.Target;"file://"==n.slice(0,7)&&(n=n.slice(7));var a=n.indexOf("#"),i=a>-1?31:23;switch(n.charAt(0)){case"#":i=28;break;case".":i&=-3}t.write_shift(4,2),t.write_shift(4,i);var s=[8,6815827,6619237,4849780,83];for(r=0;r<s.length;++r)t.write_shift(4,s[r]);if(28==i)wn(n=n.slice(1),t);else if(2&i){for(s="e0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),r=0;r<s.length;++r)t.write_shift(1,parseInt(s[r],16));var o=a>-1?n.slice(0,a):n;for(t.write_shift(4,2*(o.length+1)),r=0;r<o.length;++r)t.write_shift(2,o.charCodeAt(r));t.write_shift(2,0),8&i&&wn(a>-1?n.slice(a+1):"",t)}else{for(s="03 03 00 00 00 00 00 00 c0 00 00 00 00 00 00 46".split(" "),r=0;r<s.length;++r)t.write_shift(1,parseInt(s[r],16));for(var l=0;"../"==n.slice(3*l,3*l+3)||"..\\"==n.slice(3*l,3*l+3);)++l;for(t.write_shift(2,l),t.write_shift(4,n.length-3*l+1),r=0;r<n.length-3*l;++r)t.write_shift(1,255&n.charCodeAt(r+3*l));for(t.write_shift(1,0),t.write_shift(2,65535),t.write_shift(2,57005),r=0;r<6;++r)t.write_shift(4,0)}return t.slice(0,t.l)}function bn(e,t,r,n){return n||(n=jt(6)),n.write_shift(2,e),n.write_shift(2,t),n.write_shift(2,r||0),n}function Sn(e,t,r){var n=r.biff>8?4:2;return[e.read_shift(n),e.read_shift(n,"i"),e.read_shift(n,"i")]}function An(e){var t=e.read_shift(2),r=e.read_shift(2);return{s:{c:e.read_shift(2),r:t},e:{c:e.read_shift(2),r:r}}}function _n(e,t){return t||(t=jt(8)),t.write_shift(2,e.s.r),t.write_shift(2,e.e.r),t.write_shift(2,e.s.c),t.write_shift(2,e.e.c),t}function yn(e,t,r){var n=1536,a=16;switch(r.bookType){case"biff8":case"xla":break;case"biff5":n=1280,a=8;break;case"biff4":n=4,a=6;break;case"biff3":n=3,a=6;break;case"biff2":n=2,a=4;break;default:throw new Error("unsupported BIFF version")}var i=jt(a);return i.write_shift(2,n),i.write_shift(2,t),a>4&&i.write_shift(2,29282),a>6&&i.write_shift(2,1997),a>8&&(i.write_shift(2,49161),i.write_shift(2,1),i.write_shift(2,1798),i.write_shift(2,0)),i}function On(e,t){var r=!t||t.biff>=8?2:1,n=jt(8+r*e.name.length);n.write_shift(4,e.pos),n.write_shift(1,e.hs||0),n.write_shift(1,e.dt),n.write_shift(1,e.name.length),t.biff>=8&&n.write_shift(1,1),n.write_shift(r*e.name.length,e.name,t.biff<8?"sbcs":"utf16le");var a=n.slice(0,n.l);return a.l=n.l,a}function xn(e,t,r,n){var a=r&&5==r.biff;n||(n=jt(a?3+t.length:5+2*t.length)),n.write_shift(2,e),n.write_shift(a?1:2,t.length),a||n.write_shift(1,1),n.write_shift((a?1:2)*t.length,t,a?"sbcs":"utf16le");var i=n.length>n.l?n.slice(0,n.l):n;return null==i.l&&(i.l=i.length),i}function Cn(e,t,r,n){var a=r&&5==r.biff;n||(n=jt(a?16:20)),n.write_shift(2,0),e.style?(n.write_shift(2,e.numFmtId||0),n.write_shift(2,65524)):(n.write_shift(2,e.numFmtId||0),n.write_shift(2,t<<4));var i=0;return e.numFmtId>0&&a&&(i|=1024),n.write_shift(4,i),n.write_shift(4,0),a||n.write_shift(4,0),n.write_shift(2,0),n}function Rn(e){var t=jt(24),r=tr(e[0]);t.write_shift(2,r.r),t.write_shift(2,r.r),t.write_shift(2,r.c),t.write_shift(2,r.c);for(var n="d0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),a=0;a<16;++a)t.write_shift(1,parseInt(n[a],16));return w([t,En(e[1])])}function Nn(e){var t=e[1].Tooltip,r=jt(10+2*(t.length+1));r.write_shift(2,2048);var n=tr(e[0]);r.write_shift(2,n.r),r.write_shift(2,n.r),r.write_shift(2,n.c),r.write_shift(2,n.c);for(var a=0;a<t.length;++a)r.write_shift(2,t.charCodeAt(a));return r.write_shift(2,0),r}var kn=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},r=be({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function n(t,r){var n=r||{};n.dateNF||(n.dateNF="yyyymmdd");var a=cr(function(t,r){var n=[],a=d(1);switch(r.type){case"base64":a=g(h(t));break;case"binary":a=g(t);break;case"buffer":case"array":a=t}Gt(a,0);var i=a.read_shift(1),o=!!(136&i),l=!1,f=!1;switch(i){case 2:case 3:case 131:case 139:case 245:break;case 48:case 49:l=!0,o=!0;break;case 140:f=!0;break;default:throw new Error("DBF Unsupported Version: "+i.toString(16))}var c=0,u=521;2==i&&(c=a.read_shift(2)),a.l+=3,2!=i&&(c=a.read_shift(4)),c>1048576&&(c=1e6),2!=i&&(u=a.read_shift(2));var p=a.read_shift(2),m=r.codepage||1252;2!=i&&(a.l+=16,a.read_shift(1),0!==a[a.l]&&(m=e[a[a.l]]),a.l+=1,a.l+=2),f&&(a.l+=36);for(var v=[],T={},w=Math.min(a.length,2==i?521:u-10-(l?264:0)),E=f?32:11;a.l<w&&13!=a[a.l];)switch((T={}).name=s.utils.decode(m,a.slice(a.l,a.l+E)).replace(/[\u0000\r\n].*$/g,""),a.l+=E,T.type=String.fromCharCode(a.read_shift(1)),2==i||f||(T.offset=a.read_shift(4)),T.len=a.read_shift(1),2==i&&(T.offset=a.read_shift(2)),T.dec=a.read_shift(1),T.name.length&&v.push(T),2!=i&&(a.l+=f?13:14),T.type){case"B":(!l||8!=T.len)&&r.WTF;break;case"G":case"P":r.WTF;break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw new Error("Unknown Field Type: "+T.type)}if(13!==a[a.l]&&(a.l=u-1),13!==a.read_shift(1))throw new Error("DBF Terminator not found "+a.l+" "+a[a.l]);a.l=u;var b=0,S=0;for(n[0]=[],S=0;S!=v.length;++S)n[0][S]=v[S].name;for(;c-- >0;)if(42!==a[a.l])for(++a.l,n[++b]=[],S=0,S=0;S!=v.length;++S){var A=a.slice(a.l,a.l+v[S].len);a.l+=v[S].len,Gt(A,0);var _=s.utils.decode(m,A);switch(v[S].type){case"C":_.trim().length&&(n[b][S]=_.replace(/\s+$/,""));break;case"D":8===_.length?n[b][S]=new Date(+_.slice(0,4),+_.slice(4,6)-1,+_.slice(6,8)):n[b][S]=_;break;case"F":n[b][S]=parseFloat(_.trim());break;case"+":case"I":n[b][S]=f?2147483648^A.read_shift(-4,"i"):A.read_shift(4,"i");break;case"L":switch(_.trim().toUpperCase()){case"Y":case"T":n[b][S]=!0;break;case"N":case"F":n[b][S]=!1;break;case"":case"?":break;default:throw new Error("DBF Unrecognized L:|"+_+"|")}break;case"M":if(!o)throw new Error("DBF Unexpected MEMO for type "+i.toString(16));n[b][S]="##MEMO##"+(f?parseInt(_.trim(),10):A.read_shift(4));break;case"N":(_=_.replace(/\u0000/g,"").trim())&&"."!=_&&(n[b][S]=+_||0);break;case"@":n[b][S]=new Date(A.read_shift(-8,"f")-621356832e5);break;case"T":n[b][S]=new Date(864e5*(A.read_shift(4)-2440588)+A.read_shift(4));break;case"Y":n[b][S]=A.read_shift(4,"i")/1e4+A.read_shift(4,"i")/1e4*Math.pow(2,32);break;case"O":n[b][S]=-A.read_shift(-8,"f");break;case"B":if(l&&8==v[S].len){n[b][S]=A.read_shift(8,"f");break}case"G":case"P":A.l+=v[S].len;break;case"0":if("_NullFlags"===v[S].name)break;default:throw new Error("DBF Unsupported data type "+v[S].type)}}else a.l+=p;if(2!=i&&a.l<a.length&&26!=a[a.l++])throw new Error("DBF EOF Marker missing "+(a.l-1)+" of "+a.length+" "+a[a.l-1].toString(16));return r&&r.sheetRows&&(n=n.slice(0,r.sheetRows)),r.DBF=v,n}(t,n),n);return a["!cols"]=n.DBF.map((function(e){return{wch:e.len,DBF:e}})),delete n.DBF,a}var i={B:8,C:250,L:1,D:8,"?":0,"":0};return{to_workbook:function(e,t){try{return lr(n(e,t),t)}catch(r){if(t&&t.WTF)throw r}return{SheetNames:[],Sheets:{}}},to_sheet:n,from_sheet:function(e,n){var s=n||{};if(+s.codepage>=0&&a(+s.codepage),"string"==s.type)throw new Error("Cannot write DBF to JS string");var o=zt(),l=Bs(e,{header:1,raw:!0,cellDates:!0}),f=l[0],c=l.slice(1),h=e["!cols"]||[],u=0,p=0,d=0,m=1;for(u=0;u<f.length;++u)if(((h[u]||{}).DBF||{}).name)f[u]=h[u].DBF.name,++d;else if(null!=f[u]){if(++d,"number"==typeof f[u]&&(f[u]=f[u].toString(10)),"string"!=typeof f[u])throw new Error("DBF Invalid column name "+f[u]+" |"+typeof f[u]+"|");if(f.indexOf(f[u])!==u)for(p=0;p<1024;++p)if(-1==f.indexOf(f[u]+"_"+p)){f[u]+="_"+p;break}}var g=ir(e["!ref"]),v=[],T=[],w=[];for(u=0;u<=g.e.c-g.s.c;++u){var E="",b="",S=0,A=[];for(p=0;p<c.length;++p)null!=c[p][u]&&A.push(c[p][u]);if(0!=A.length&&null!=f[u]){for(p=0;p<A.length;++p){switch(typeof A[p]){case"number":b="B";break;case"string":default:b="C";break;case"boolean":b="L";break;case"object":b=A[p]instanceof Date?"D":"C"}S=Math.max(S,String(A[p]).length),E=E&&E!=b?"C":b}S>250&&(S=250),"C"==(b=((h[u]||{}).DBF||{}).type)&&h[u].DBF.len>S&&(S=h[u].DBF.len),"B"==E&&"N"==b&&(E="N",w[u]=h[u].DBF.dec,S=h[u].DBF.len),T[u]="C"==E||"N"==b?S:i[E]||0,m+=T[u],v[u]=E}else v[u]="?"}var _=o.next(32);for(_.write_shift(4,318902576),_.write_shift(4,c.length),_.write_shift(2,296+32*d),_.write_shift(2,m),u=0;u<4;++u)_.write_shift(4,0);for(_.write_shift(4,(+r[t]||3)<<8),u=0,p=0;u<f.length;++u)if(null!=f[u]){var y=o.next(32),O=(f[u].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);y.write_shift(1,O,"sbcs"),y.write_shift(1,"?"==v[u]?"C":v[u],"sbcs"),y.write_shift(4,p),y.write_shift(1,T[u]||i[v[u]]||0),y.write_shift(1,w[u]||0),y.write_shift(1,2),y.write_shift(4,0),y.write_shift(1,0),y.write_shift(4,0),y.write_shift(4,0),p+=T[u]||i[v[u]]||0}var x=o.next(264);for(x.write_shift(4,13),u=0;u<65;++u)x.write_shift(4,0);for(u=0;u<c.length;++u){var C=o.next(m);for(C.write_shift(1,0),p=0;p<f.length;++p)if(null!=f[p])switch(v[p]){case"L":C.write_shift(1,null==c[u][p]?63:c[u][p]?84:70);break;case"B":C.write_shift(8,c[u][p]||0,"f");break;case"N":var R="0";for("number"==typeof c[u][p]&&(R=c[u][p].toFixed(w[p]||0)),d=0;d<T[p]-R.length;++d)C.write_shift(1,32);C.write_shift(1,R,"sbcs");break;case"D":c[u][p]?(C.write_shift(4,("0000"+c[u][p].getFullYear()).slice(-4),"sbcs"),C.write_shift(2,("00"+(c[u][p].getMonth()+1)).slice(-2),"sbcs"),C.write_shift(2,("00"+c[u][p].getDate()).slice(-2),"sbcs")):C.write_shift(8,"00000000","sbcs");break;case"C":var N=String(null!=c[u][p]?c[u][p]:"").slice(0,T[p]);for(C.write_shift(1,N,"sbcs"),d=0;d<T[p]-N.length;++d)C.write_shift(1,32)}}return o.next(1).write_shift(1,26),o.end()}}}(),In=function(){var e={AA:"À",BA:"Á",CA:"Â",DA:195,HA:"Ä",JA:197,AE:"È",BE:"É",CE:"Ê",HE:"Ë",AI:"Ì",BI:"Í",CI:"Î",HI:"Ï",AO:"Ò",BO:"Ó",CO:"Ô",DO:213,HO:"Ö",AU:"Ù",BU:"Ú",CU:"Û",HU:"Ü",Aa:"à",Ba:"á",Ca:"â",Da:227,Ha:"ä",Ja:229,Ae:"è",Be:"é",Ce:"ê",He:"ë",Ai:"ì",Bi:"í",Ci:"î",Hi:"ï",Ao:"ò",Bo:"ó",Co:"ô",Do:245,Ho:"ö",Au:"ù",Bu:"ú",Cu:"û",Hu:"ü",KC:"Ç",Kc:"ç",q:"æ",z:"œ",a:"Æ",j:"Œ",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},t=new RegExp("N("+we(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm"),r=function(t,r){var n=e[r];return"number"==typeof n?l(n):n},n=function(e,t,r){var n=t.charCodeAt(0)-32<<4|r.charCodeAt(0)-48;return 59==n?e:l(n)};function i(e,i){var s,o=e.split(/[\n\r]+/),l=-1,f=-1,c=0,h=0,u=[],p=[],d=null,m={},g=[],v=[],T=[],w=0;for(+i.codepage>=0&&a(+i.codepage);c!==o.length;++c){w=0;var E,b=o[c].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,n).replace(t,r),S=b.replace(/;;/g,"\0").split(";").map((function(e){return e.replace(/\u0000/g,";")})),A=S[0];if(b.length>0)switch(A){case"ID":case"E":case"B":case"O":case"W":break;case"P":"P"==S[1].charAt(0)&&p.push(b.slice(3).replace(/;;/g,";"));break;case"C":var _=!1,y=!1,O=!1,x=!1,C=-1,R=-1;for(h=1;h<S.length;++h)switch(S[h].charAt(0)){case"A":case"G":break;case"X":f=parseInt(S[h].slice(1))-1,y=!0;break;case"Y":for(l=parseInt(S[h].slice(1))-1,y||(f=0),s=u.length;s<=l;++s)u[s]=[];break;case"K":'"'===(E=S[h].slice(1)).charAt(0)?E=E.slice(1,E.length-1):"TRUE"===E?E=!0:"FALSE"===E?E=!1:isNaN(Me(E))?isNaN(Ue(E).getDate())||(E=Ie(E)):(E=Me(E),null!==d&&se(d)&&(E=Ce(E))),_=!0;break;case"E":x=!0;var N=Sa(S[h].slice(1),{r:l,c:f});u[l][f]=[u[l][f],N];break;case"S":O=!0,u[l][f]=[u[l][f],"S5S"];break;case"R":C=parseInt(S[h].slice(1))-1;break;case"C":R=parseInt(S[h].slice(1))-1;break;default:if(i&&i.WTF)throw new Error("SYLK bad record "+b)}if(_&&(u[l][f]&&2==u[l][f].length?u[l][f][0]=E:u[l][f]=E,d=null),O){if(x)throw new Error("SYLK shared formula cannot have own formula");var k=C>-1&&u[C][R];if(!k||!k[1])throw new Error("SYLK shared formula cannot find base");u[l][f][1]=ya(k[1],{r:l-C,c:f-R})}break;case"F":var I=0;for(h=1;h<S.length;++h)switch(S[h].charAt(0)){case"X":f=parseInt(S[h].slice(1))-1,++I;break;case"Y":for(l=parseInt(S[h].slice(1))-1,s=u.length;s<=l;++s)u[s]=[];break;case"M":w=parseInt(S[h].slice(1))/20;break;case"F":case"G":case"S":case"D":case"N":break;case"P":d=p[parseInt(S[h].slice(1))];break;case"W":for(T=S[h].slice(1).split(" "),s=parseInt(T[0],10);s<=parseInt(T[1],10);++s)w=parseInt(T[2],10),v[s-1]=0===w?{hidden:!0}:{wch:w},Yn(v[s-1]);break;case"C":v[f=parseInt(S[h].slice(1))-1]||(v[f]={});break;case"R":g[l=parseInt(S[h].slice(1))-1]||(g[l]={}),w>0?(g[l].hpt=w,g[l].hpx=Zn(w)):0===w&&(g[l].hidden=!0);break;default:if(i&&i.WTF)throw new Error("SYLK bad record "+b)}I<1&&(d=null);break;default:if(i&&i.WTF)throw new Error("SYLK bad record "+b)}}return g.length>0&&(m["!rows"]=g),v.length>0&&(m["!cols"]=v),i&&i.sheetRows&&(u=u.slice(0,i.sheetRows)),[u,m]}function s(e,t){var r=function(e,t){switch(t.type){case"base64":return i(h(e),t);case"binary":return i(e,t);case"buffer":return i(u&&Buffer.isBuffer(e)?e.toString("binary"):T(e),t);case"array":return i(De(e),t)}throw new Error("Unrecognized type "+t.type)}(e,t),n=r[0],a=r[1],s=cr(n,t);return we(a).forEach((function(e){s[e]=a[e]})),s}function o(e,t,r,n){var a="C;Y"+(r+1)+";X"+(n+1)+";K";switch(e.t){case"n":a+=e.v||0,e.f&&!e.F&&(a+=";E"+_a(e.f,{r:r,c:n}));break;case"b":a+=e.v?"TRUE":"FALSE";break;case"e":a+=e.w||e.v;break;case"d":a+='"'+(e.w||e.v)+'"';break;case"s":a+='"'+e.v.replace(/"/g,"").replace(/;/g,";;")+'"'}return a}return e["|"]=254,{to_workbook:function(e,t){return lr(s(e,t),t)},to_sheet:s,from_sheet:function(e,t){var r,n,a=["ID;PWXL;N;E"],i=[],s=ir(e["!ref"]),l=Array.isArray(e),f="\r\n";a.push("P;PGeneral"),a.push("F;P0;DG0G8;M255"),e["!cols"]&&(n=a,e["!cols"].forEach((function(e,t){var r="F;W"+(t+1)+" "+(t+1)+" ";e.hidden?r+="0":("number"!=typeof e.width||e.wpx||(e.wpx=zn(e.width)),"number"!=typeof e.wpx||e.wch||(e.wch=$n(e.wpx)),"number"==typeof e.wch&&(r+=Math.round(e.wch)))," "!=r.charAt(r.length-1)&&n.push(r)}))),e["!rows"]&&function(e,t){t.forEach((function(t,r){var n="F;";t.hidden?n+="M0;":t.hpt?n+="M"+20*t.hpt+";":t.hpx&&(n+="M"+20*Jn(t.hpx)+";"),n.length>2&&e.push(n+"R"+(r+1))}))}(a,e["!rows"]),a.push("B;Y"+(s.e.r-s.s.r+1)+";X"+(s.e.c-s.s.c+1)+";D"+[s.s.c,s.s.r,s.e.c,s.e.r].join(" "));for(var c=s.s.r;c<=s.e.r;++c)for(var h=s.s.c;h<=s.e.c;++h){var u=rr({r:c,c:h});(r=l?(e[c]||[])[h]:e[u])&&(null!=r.v||r.f&&!r.F)&&i.push(o(r,0,c,h))}return a.join(f)+f+i.join(f)+f+"E"+f}}}(),Dn=function(){function e(e,t){for(var r=e.split("\n"),n=-1,a=-1,i=0,s=[];i!==r.length;++i)if("BOT"!==r[i].trim()){if(!(n<0)){for(var o=r[i].trim().split(","),l=o[0],f=o[1],c=r[++i]||"";1&(c.match(/["]/g)||[]).length&&i<r.length-1;)c+="\n"+r[++i];switch(c=c.trim(),+l){case-1:if("BOT"===c){s[++n]=[],a=0;continue}if("EOD"!==c)throw new Error("Unrecognized DIF special command "+c);break;case 0:"TRUE"===c?s[n][a]=!0:"FALSE"===c?s[n][a]=!1:isNaN(Me(f))?isNaN(Ue(f).getDate())?s[n][a]=f:s[n][a]=Ie(f):s[n][a]=Me(f),++a;break;case 1:(c=(c=c.slice(1,c.length-1)).replace(/""/g,'"'))&&c.match(/^=".*"$/)&&(c=c.slice(2,-1)),s[n][a++]=""!==c?c:null}if("EOD"===c)break}}else s[++n]=[],a=0;return t&&t.sheetRows&&(s=s.slice(0,t.sheetRows)),s}function t(t,r){return cr(function(t,r){switch(r.type){case"base64":return e(h(t),r);case"binary":return e(t,r);case"buffer":return e(u&&Buffer.isBuffer(t)?t.toString("binary"):T(t),r);case"array":return e(De(t),r)}throw new Error("Unrecognized type "+r.type)}(t,r),r)}return{to_workbook:function(e,r){return lr(t(e,r),r)},to_sheet:t,from_sheet:function(){var e=function(e,t,r,n,a){e.push(t),e.push(r+","+n),e.push('"'+a.replace(/"/g,'""')+'"')},t=function(e,t,r,n){e.push(t+","+r),e.push(1==t?'"'+n.replace(/"/g,'""')+'"':n)};return function(r){var n,a=[],i=ir(r["!ref"]),s=Array.isArray(r);e(a,"TABLE",0,1,"sheetjs"),e(a,"VECTORS",0,i.e.r-i.s.r+1,""),e(a,"TUPLES",0,i.e.c-i.s.c+1,""),e(a,"DATA",0,0,"");for(var o=i.s.r;o<=i.e.r;++o){t(a,-1,0,"BOT");for(var l=i.s.c;l<=i.e.c;++l){var f=rr({r:o,c:l});if(n=s?(r[o]||[])[l]:r[f])switch(n.t){case"n":var c=n.w;c||null==n.v||(c=n.v),null==c?n.f&&!n.F?t(a,1,0,"="+n.f):t(a,1,0,""):t(a,0,c,"V");break;case"b":t(a,0,n.v?1:0,n.v?"TRUE":"FALSE");break;case"s":t(a,1,0,isNaN(n.v)?n.v:'="'+n.v+'"');break;case"d":n.w||(n.w=ce(n.z||k[14],_e(Ie(n.v)))),t(a,0,n.w,"V");break;default:t(a,1,0,"")}else t(a,1,0,"")}}t(a,-1,0,"EOD");return a.join("\r\n")}}()}}(),Pn=function(){function e(e){return e.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function t(e,t){return cr(function(e,t){for(var r=e.split("\n"),n=-1,a=-1,i=0,s=[];i!==r.length;++i){var o=r[i].trim().split(":");if("cell"===o[0]){var l=tr(o[1]);if(s.length<=l.r)for(n=s.length;n<=l.r;++n)s[n]||(s[n]=[]);switch(n=l.r,a=l.c,o[2]){case"t":s[n][a]=o[3].replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,"\n");break;case"v":s[n][a]=+o[3];break;case"vtf":var f=o[o.length-1];case"vtc":"nl"===o[3]?s[n][a]=!!+o[4]:s[n][a]=+o[4],"vtf"==o[2]&&(s[n][a]=[s[n][a],f])}}}return t&&t.sheetRows&&(s=s.slice(0,t.sheetRows)),s}(e,t),t)}var r=["socialcalc:version:1.5","MIME-Version: 1.0","Content-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave"].join("\n"),n=["--SocialCalcSpreadsheetControlSave","Content-type: text/plain; charset=UTF-8"].join("\n")+"\n",a=["# SocialCalc Spreadsheet Control Save","part:sheet"].join("\n"),i="--SocialCalcSpreadsheetControlSave--";function s(t){if(!t||!t["!ref"])return"";for(var r,n=[],a=[],i="",s=nr(t["!ref"]),o=Array.isArray(t),l=s.s.r;l<=s.e.r;++l)for(var f=s.s.c;f<=s.e.c;++f)if(i=rr({r:l,c:f}),(r=o?(t[l]||[])[f]:t[i])&&null!=r.v&&"z"!==r.t){switch(a=["cell",i,"t"],r.t){case"s":case"str":a.push(e(r.v));break;case"n":r.f?(a[2]="vtf",a[3]="n",a[4]=r.v,a[5]=e(r.f)):(a[2]="v",a[3]=r.v);break;case"b":a[2]="vt"+(r.f?"f":"c"),a[3]="nl",a[4]=r.v?"1":"0",a[5]=e(r.f||(r.v?"TRUE":"FALSE"));break;case"d":var c=_e(Ie(r.v));a[2]="vtc",a[3]="nd",a[4]=""+c,a[5]=r.w||ce(r.z||k[14],c);break;case"e":continue}n.push(a.join(":"))}return n.push("sheet:c:"+(s.e.c-s.s.c+1)+":r:"+(s.e.r-s.s.r+1)+":tvf:1"),n.push("valueformat:1:text-wiki"),n.join("\n")}return{to_workbook:function(e,r){return lr(t(e,r),r)},to_sheet:t,from_sheet:function(e){return[r,n,a,n,s(e),i].join("\n")}}}(),Ln=function(){function e(e,t,r,n,a){a.raw?t[r][n]=e:""===e||("TRUE"===e?t[r][n]=!0:"FALSE"===e?t[r][n]=!1:isNaN(Me(e))?isNaN(Ue(e).getDate())?t[r][n]=e:t[r][n]=Ie(e):t[r][n]=Me(e))}var t={44:",",9:"\t",59:";",124:"|"},r={44:3,9:2,59:1,124:0};function n(e){for(var n={},a=!1,i=0,s=0;i<e.length;++i)34==(s=e.charCodeAt(i))?a=!a:!a&&s in t&&(n[s]=(n[s]||0)+1);for(i in s=[],n)Object.prototype.hasOwnProperty.call(n,i)&&s.push([n[i],i]);if(!s.length)for(i in n=r)Object.prototype.hasOwnProperty.call(n,i)&&s.push([n[i],i]);return s.sort((function(e,t){return e[0]-t[0]||r[e[1]]-r[t[1]]})),t[s.pop()[1]]||44}function a(e,t){var r=t||{},a="",i=r.dense?[]:{},s={s:{c:0,r:0},e:{c:0,r:0}};"sep="==e.slice(0,4)?13==e.charCodeAt(5)&&10==e.charCodeAt(6)?(a=e.charAt(4),e=e.slice(7)):13==e.charCodeAt(5)||10==e.charCodeAt(5)?(a=e.charAt(4),e=e.slice(6)):a=n(e.slice(0,1024)):a=r&&r.FS?r.FS:n(e.slice(0,1024));var o=0,l=0,f=0,c=0,h=0,u=a.charCodeAt(0),p=!1,d=0,m=e.charCodeAt(0);e=e.replace(/\r\n/gm,"\n");var g,v,T=null!=r.dateNF?(g=r.dateNF,v=(v="number"==typeof g?k[g]:g).replace(de,"(\\d+)"),new RegExp("^"+v+"$")):null;function w(){var t=e.slice(c,h),n={};if('"'==t.charAt(0)&&'"'==t.charAt(t.length-1)&&(t=t.slice(1,-1).replace(/""/g,'"')),0===t.length)n.t="z";else if(r.raw)n.t="s",n.v=t;else if(0===t.trim().length)n.t="s",n.v=t;else if(61==t.charCodeAt(0))34==t.charCodeAt(1)&&34==t.charCodeAt(t.length-1)?(n.t="s",n.v=t.slice(2,-1).replace(/""/g,'"')):1!=t.length?(n.t="n",n.f=t.slice(1)):(n.t="s",n.v=t);else if("TRUE"==t)n.t="b",n.v=!0;else if("FALSE"==t)n.t="b",n.v=!1;else if(isNaN(f=Me(t)))if(!isNaN(Ue(t).getDate())||T&&t.match(T)){n.z=r.dateNF||k[14];var a=0;T&&t.match(T)&&(t=function(e,t,r){var n=-1,a=-1,i=-1,s=-1,o=-1,l=-1;(t.match(de)||[]).forEach((function(e,t){var f=parseInt(r[t+1],10);switch(e.toLowerCase().charAt(0)){case"y":n=f;break;case"d":i=f;break;case"h":s=f;break;case"s":l=f;break;case"m":s>=0?o=f:a=f}})),l>=0&&-1==o&&a>=0&&(o=a,a=-1);var f=(""+(n>=0?n:(new Date).getFullYear())).slice(-4)+"-"+("00"+(a>=1?a:1)).slice(-2)+"-"+("00"+(i>=1?i:1)).slice(-2);7==f.length&&(f="0"+f),8==f.length&&(f="20"+f);var c=("00"+(s>=0?s:0)).slice(-2)+":"+("00"+(o>=0?o:0)).slice(-2)+":"+("00"+(l>=0?l:0)).slice(-2);return-1==s&&-1==o&&-1==l?f:-1==n&&-1==a&&-1==i?c:f+"T"+c}(0,r.dateNF,t.match(T)||[]),a=1),r.cellDates?(n.t="d",n.v=Ie(t,a)):(n.t="n",n.v=_e(Ie(t,a))),!1!==r.cellText&&(n.w=ce(n.z,n.v instanceof Date?_e(n.v):n.v)),r.cellNF||delete n.z}else n.t="s",n.v=t;else n.t="n",!1!==r.cellText&&(n.w=t),n.v=f;if("z"==n.t||(r.dense?(i[o]||(i[o]=[]),i[o][l]=n):i[rr({c:l,r:o})]=n),c=h+1,m=e.charCodeAt(c),s.e.c<l&&(s.e.c=l),s.e.r<o&&(s.e.r=o),d==u)++l;else if(l=0,++o,r.sheetRows&&r.sheetRows<=o)return!0}e:for(;h<e.length;++h)switch(d=e.charCodeAt(h)){case 34:34===m&&(p=!p);break;case u:case 10:case 13:if(!p&&w())break e}return h-c>0&&w(),i["!ref"]=ar(s),i}function i(t,r){return r&&r.PRN?r.FS||"sep="==t.slice(0,4)||t.indexOf("\t")>=0||t.indexOf(",")>=0||t.indexOf(";")>=0?a(t,r):cr(function(t,r){var n=r||{},a=[];if(!t||0===t.length)return a;for(var i=t.split(/[\r\n]/),s=i.length-1;s>=0&&0===i[s].length;)--s;for(var o=10,l=0,f=0;f<=s;++f)-1==(l=i[f].indexOf(" "))?l=i[f].length:l++,o=Math.max(o,l);for(f=0;f<=s;++f){a[f]=[];var c=0;for(e(i[f].slice(0,o).trim(),a,f,c,n),c=1;c<=(i[f].length-o)/10+1;++c)e(i[f].slice(o+10*(c-1),o+10*c).trim(),a,f,c,n)}return n.sheetRows&&(a=a.slice(0,n.sheetRows)),a}(t,r),r):a(t,r)}function s(e,t){var r="",n="string"==t.type?[0,0,0,0]:function(e,t){var r="";switch((t||{}).type||"base64"){case"buffer":case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":r=h(e.slice(0,12));break;case"binary":r=e;break;default:throw new Error("Unrecognized type "+(t&&t.type||"undefined"))}return[r.charCodeAt(0),r.charCodeAt(1),r.charCodeAt(2),r.charCodeAt(3),r.charCodeAt(4),r.charCodeAt(5),r.charCodeAt(6),r.charCodeAt(7)]}(e,t);switch(t.type){case"base64":r=h(e);break;case"binary":case"string":r=e;break;case"buffer":65001==t.codepage?r=e.toString("utf8"):(t.codepage,r=u&&Buffer.isBuffer(e)?e.toString("binary"):T(e));break;case"array":r=De(e);break;default:throw new Error("Unrecognized type "+t.type)}return 239==n[0]&&187==n[1]&&191==n[2]?r=qe(r.slice(3)):"string"!=t.type&&"buffer"!=t.type&&65001==t.codepage?r=qe(r):t.type,"socialcalc:version:"==r.slice(0,19)?Pn.to_sheet("string"==t.type?r:qe(r),t):i(r,t)}return{to_workbook:function(e,t){return lr(s(e,t),t)},to_sheet:s,from_sheet:function(e){for(var t,r=[],n=ir(e["!ref"]),a=Array.isArray(e),i=n.s.r;i<=n.e.r;++i){for(var s=[],o=n.s.c;o<=n.e.c;++o){var l=rr({r:i,c:o});if((t=a?(e[i]||[])[o]:e[l])&&null!=t.v){for(var f=(t.w||(or(t),t.w)||"").slice(0,10);f.length<10;)f+=" ";s.push(f+(0===o?" ":""))}else s.push("          ")}r.push(s.join(""))}return r.join("\n")}}}(),Mn=function(){function e(e,t,r){if(e){Gt(e,e.l||0);for(var n=r.Enum||E;e.l<e.length;){var a=e.read_shift(2),i=n[a]||n[65535],s=e.read_shift(2),o=e.l+s,l=i.f&&i.f(e,s,r);if(e.l=o,t(l,i,a))return}}}function t(t,r){if(!t)return t;var n=r||{},a=n.dense?[]:{},i="Sheet1",s="",o=0,l={},f=[],c=[],h={s:{r:0,c:0},e:{r:0,c:0}},u=n.sheetRows||0;if(0==t[2]&&(8==t[3]||9==t[3])&&t.length>=16&&5==t[14]&&108===t[15])throw new Error("Unsupported Works 3 for Mac file");if(2==t[2])n.Enum=E,e(t,(function(e,t,r){switch(r){case 0:n.vers=e,e>=4096&&(n.qpro=!0);break;case 6:h=e;break;case 204:e&&(s=e);break;case 222:s=e;break;case 15:case 51:n.qpro||(e[1].v=e[1].v.slice(1));case 13:case 14:case 16:14==r&&!(112&~e[2])&&(15&e[2])>1&&(15&e[2])<15&&(e[1].z=n.dateNF||k[14],n.cellDates&&(e[1].t="d",e[1].v=Ce(e[1].v))),n.qpro&&e[3]>o&&(a["!ref"]=ar(h),l[i]=a,f.push(i),a=n.dense?[]:{},h={s:{r:0,c:0},e:{r:0,c:0}},o=e[3],i=s||"Sheet"+(o+1),s="");var c=n.dense?(a[e[0].r]||[])[e[0].c]:a[rr(e[0])];if(c){c.t=e[1].t,c.v=e[1].v,null!=e[1].z&&(c.z=e[1].z),null!=e[1].f&&(c.f=e[1].f);break}n.dense?(a[e[0].r]||(a[e[0].r]=[]),a[e[0].r][e[0].c]=e[1]):a[rr(e[0])]=e[1]}}),n);else{if(26!=t[2]&&14!=t[2])throw new Error("Unrecognized LOTUS BOF "+t[2]);n.Enum=b,14==t[2]&&(n.qpro=!0,t.l=0),e(t,(function(e,t,r){switch(r){case 204:i=e;break;case 22:e[1].v=e[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(e[3]>o&&(a["!ref"]=ar(h),l[i]=a,f.push(i),a=n.dense?[]:{},h={s:{r:0,c:0},e:{r:0,c:0}},o=e[3],i="Sheet"+(o+1)),u>0&&e[0].r>=u)break;n.dense?(a[e[0].r]||(a[e[0].r]=[]),a[e[0].r][e[0].c]=e[1]):a[rr(e[0])]=e[1],h.e.c<e[0].c&&(h.e.c=e[0].c),h.e.r<e[0].r&&(h.e.r=e[0].r);break;case 27:e[14e3]&&(c[e[14e3][0]]=e[14e3][1]);break;case 1537:c[e[0]]=e[1],e[0]==o&&(i=e[1])}}),n)}if(a["!ref"]=ar(h),l[s||i]=a,f.push(s||i),!c.length)return{SheetNames:f,Sheets:l};for(var p={},d=[],m=0;m<c.length;++m)l[f[m]]?(d.push(c[m]||f[m]),p[c[m]]=l[c[m]]||l[f[m]]):(d.push(c[m]),p[c[m]]={"!ref":"A1"});return{SheetNames:d,Sheets:p}}function r(e,t,r){var n=[{c:0,r:0},{t:"n",v:0},0,0];return r.qpro&&20768!=r.vers?(n[0].c=e.read_shift(1),n[3]=e.read_shift(1),n[0].r=e.read_shift(2),e.l+=2):(n[2]=e.read_shift(1),n[0].c=e.read_shift(2),n[0].r=e.read_shift(2)),n}function n(e,t,n){var a=e.l+t,i=r(e,0,n);if(i[1].t="s",20768==n.vers){e.l++;var s=e.read_shift(1);return i[1].v=e.read_shift(s,"utf8"),i}return n.qpro&&e.l++,i[1].v=e.read_shift(a-e.l,"cstr"),i}function i(e,t,r){var n=jt(7+r.length);n.write_shift(1,255),n.write_shift(2,t),n.write_shift(2,e),n.write_shift(1,39);for(var a=0;a<n.length;++a){var i=r.charCodeAt(a);n.write_shift(1,i>=128?95:i)}return n.write_shift(1,0),n}function s(e,t,r){var n=jt(7);return n.write_shift(1,255),n.write_shift(2,t),n.write_shift(2,e),n.write_shift(2,r,"i"),n}function o(e,t,r){var n=jt(13);return n.write_shift(1,255),n.write_shift(2,t),n.write_shift(2,e),n.write_shift(8,r,"f"),n}function l(e,t,r){var n=32768&t;return t=(n?e:0)+((t&=-32769)>=8192?t-16384:t),(n?"":"$")+(r?er(t):qt(t))}var f={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]},c=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function u(e){var t=[{c:0,r:0},{t:"n",v:0},0];return t[0].r=e.read_shift(2),t[3]=e[e.l++],t[0].c=e[e.l++],t}function p(e,t,r,n){var a=jt(6+n.length);a.write_shift(2,e),a.write_shift(1,r),a.write_shift(1,t),a.write_shift(1,39);for(var i=0;i<n.length;++i){var s=n.charCodeAt(i);a.write_shift(1,s>=128?95:s)}return a.write_shift(1,0),a}function d(e,t){var r=u(e),n=e.read_shift(4),a=e.read_shift(4),i=e.read_shift(2);if(65535==i)return 0===n&&3221225472===a?(r[1].t="e",r[1].v=15):0===n&&3489660928===a?(r[1].t="e",r[1].v=42):r[1].v=0,r;var s=32768&i;return i=(32767&i)-16446,r[1].v=(1-2*s)*(a*Math.pow(2,i+32)+n*Math.pow(2,i)),r}function m(e,t,r,n){var a=jt(14);if(a.write_shift(2,e),a.write_shift(1,r),a.write_shift(1,t),0==n)return a.write_shift(4,0),a.write_shift(4,0),a.write_shift(2,65535),a;var i,s=0,o=0,l=0;return n<0&&(s=1,n=-n),o=0|Math.log2(n),2147483648&(l=(n/=Math.pow(2,o-31))>>>0)||(++o,l=(n/=2)>>>0),n-=l,l|=2147483648,l>>>=0,i=(n*=Math.pow(2,32))>>>0,a.write_shift(4,i),a.write_shift(4,l),o+=16383+(s?32768:0),a.write_shift(2,o),a}function v(e,t){var r=u(e),n=e.read_shift(8,"f");return r[1].v=n,r}function T(e,t){return 0==e[e.l+t-1]?e.read_shift(t,"cstr"):""}function w(e,t){var r=jt(5+e.length);r.write_shift(2,14e3),r.write_shift(2,t);for(var n=0;n<e.length;++n){var a=e.charCodeAt(n);r[r.l++]=a>127?95:a}return r[r.l++]=0,r}var E={0:{n:"BOF",f:pn},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f:function(e,t,r){var n={s:{c:0,r:0},e:{c:0,r:0}};return 8==t&&r.qpro?(n.s.c=e.read_shift(1),e.l++,n.s.r=e.read_shift(2),n.e.c=e.read_shift(1),e.l++,n.e.r=e.read_shift(2),n):(n.s.c=e.read_shift(2),n.s.r=e.read_shift(2),12==t&&r.qpro&&(e.l+=2),n.e.c=e.read_shift(2),n.e.r=e.read_shift(2),12==t&&r.qpro&&(e.l+=2),65535==n.s.c&&(n.s.c=n.e.c=n.s.r=n.e.r=0),n)}},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:function(e,t,n){var a=r(e,0,n);return a[1].v=e.read_shift(2,"i"),a}},14:{n:"NUMBER",f:function(e,t,n){var a=r(e,0,n);return a[1].v=e.read_shift(8,"f"),a}},15:{n:"LABEL",f:n},16:{n:"FORMULA",f:function(e,t,n){var a=e.l+t,i=r(e,0,n);if(i[1].v=e.read_shift(8,"f"),n.qpro)e.l=a;else{var s=e.read_shift(2);!function(e,t){Gt(e,0);var r=[],n=0,a="",i="",s="",o="";for(;e.l<e.length;){var h=e[e.l++];switch(h){case 0:r.push(e.read_shift(8,"f"));break;case 1:i=l(t[0].c,e.read_shift(2),!0),a=l(t[0].r,e.read_shift(2),!1),r.push(i+a);break;case 2:var u=l(t[0].c,e.read_shift(2),!0),p=l(t[0].r,e.read_shift(2),!1);i=l(t[0].c,e.read_shift(2),!0),a=l(t[0].r,e.read_shift(2),!1),r.push(u+p+":"+i+a);break;case 3:if(e.l<e.length)return;break;case 4:r.push("("+r.pop()+")");break;case 5:r.push(e.read_shift(2));break;case 6:for(var d="";h=e[e.l++];)d+=String.fromCharCode(h);r.push('"'+d.replace(/"/g,'""')+'"');break;case 8:r.push("-"+r.pop());break;case 23:r.push("+"+r.pop());break;case 22:r.push("NOT("+r.pop()+")");break;case 20:case 21:o=r.pop(),s=r.pop(),r.push(["AND","OR"][h-20]+"("+s+","+o+")");break;default:if(h<32&&c[h])o=r.pop(),s=r.pop(),r.push(s+c[h]+o);else{if(!f[h])return;if(69==(n=f[h][1])&&(n=e[e.l++]),n>r.length)return;var m=r.slice(-n);r.length-=n,r.push(f[h][0]+"("+m.join(",")+")")}}}1==r.length&&(t[1].f=""+r[0])}(e.slice(e.l,e.l+s),i),e.l+=s}return i}},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:n},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:T},222:{n:"SHEETNAMELP",f:function(e,t){var r=e[e.l++];r>t-1&&(r=t-1);for(var n="";n.length<r;)n+=String.fromCharCode(e[e.l++]);return n}},65535:{n:""}},b={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:function(e,t){var r=u(e);return r[1].t="s",r[1].v=e.read_shift(t-4,"cstr"),r}},23:{n:"NUMBER17",f:d},24:{n:"NUMBER18",f:function(e,t){var r=u(e);r[1].v=e.read_shift(2);var n=r[1].v>>1;if(1&r[1].v)switch(7&n){case 0:n=5e3*(n>>3);break;case 1:n=500*(n>>3);break;case 2:n=(n>>3)/20;break;case 3:n=(n>>3)/200;break;case 4:n=(n>>3)/2e3;break;case 5:n=(n>>3)/2e4;break;case 6:n=(n>>3)/16;break;case 7:n=(n>>3)/64}return r[1].v=n,r}},25:{n:"FORMULA19",f:function(e,t){var r=d(e);return e.l+=t-14,r}},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:function(e,t){for(var r={},n=e.l+t;e.l<n;){var a=e.read_shift(2);if(14e3==a){for(r[a]=[0,""],r[a][0]=e.read_shift(2);e[e.l];)r[a][1]+=String.fromCharCode(e[e.l]),e.l++;e.l++}}return r}},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:function(e,t){var r=u(e),n=e.read_shift(4);return r[1].v=n>>6,r}},38:{n:"??"},39:{n:"NUMBER27",f:v},40:{n:"FORMULA28",f:function(e,t){var r=v(e);return e.l+=t-10,r}},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:T},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:function(e,t,r){if(r.qpro&&!(t<21)){var n=e.read_shift(1);return e.l+=17,e.l+=1,e.l+=2,[n,e.read_shift(t-21,"cstr")]}}},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:function(e,t){var r=t||{};if(+r.codepage>=0&&a(+r.codepage),"string"==r.type)throw new Error("Cannot write WK1 to JS string");var n,l,f=zt(),c=ir(e["!ref"]),h=Array.isArray(e),u=[];Xi(f,0,(n=1030,(l=jt(2)).write_shift(2,n),l)),Xi(f,6,function(e){var t=jt(8);return t.write_shift(2,e.s.c),t.write_shift(2,e.s.r),t.write_shift(2,e.e.c),t.write_shift(2,e.e.r),t}(c));for(var p=Math.min(c.e.r,8191),d=c.s.r;d<=p;++d)for(var m=qt(d),g=c.s.c;g<=c.e.c;++g){d===c.s.r&&(u[g]=er(g));var v=u[g]+m,T=h?(e[d]||[])[g]:e[v];if(T&&"z"!=T.t)if("n"==T.t)(0|T.v)==T.v&&T.v>=-32768&&T.v<=32767?Xi(f,13,s(d,g,T.v)):Xi(f,14,o(d,g,T.v));else Xi(f,15,i(d,g,or(T).slice(0,239)))}return Xi(f,1),f.end()},book_to_wk3:function(e,t){var r=t||{};if(+r.codepage>=0&&a(+r.codepage),"string"==r.type)throw new Error("Cannot write WK3 to JS string");var n=zt();Xi(n,0,function(e){var t=jt(26);t.write_shift(2,4096),t.write_shift(2,4),t.write_shift(4,0);for(var r=0,n=0,a=0,i=0;i<e.SheetNames.length;++i){var s=e.SheetNames[i],o=e.Sheets[s];if(o&&o["!ref"]){++a;var l=nr(o["!ref"]);r<l.e.r&&(r=l.e.r),n<l.e.c&&(n=l.e.c)}}r>8191&&(r=8191);return t.write_shift(2,r),t.write_shift(1,a),t.write_shift(1,n),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(1,1),t.write_shift(1,2),t.write_shift(4,0),t.write_shift(4,0),t}(e));for(var i=0,s=0;i<e.SheetNames.length;++i)(e.Sheets[e.SheetNames[i]]||{})["!ref"]&&Xi(n,27,w(e.SheetNames[i],s++));var o=0;for(i=0;i<e.SheetNames.length;++i){var l=e.Sheets[e.SheetNames[i]];if(l&&l["!ref"]){for(var f=ir(l["!ref"]),c=Array.isArray(l),h=[],u=Math.min(f.e.r,8191),d=f.s.r;d<=u;++d)for(var g=qt(d),v=f.s.c;v<=f.e.c;++v){d===f.s.r&&(h[v]=er(v));var T=h[v]+g,E=c?(l[d]||[])[v]:l[T];if(E&&"z"!=E.t)if("n"==E.t)Xi(n,23,m(d,v,o,E.v));else Xi(n,22,p(d,v,o,or(E).slice(0,239)))}++o}}return Xi(n,1),n.end()},to_workbook:function(e,r){switch(r.type){case"base64":return t(g(h(e)),r);case"binary":return t(g(e),r);case"buffer":case"array":return t(e,r)}throw"Unsupported type "+r.type}}}(),Fn=/^\s|\s$|[\t\n\r]/;function Un(e,t){if(!t.bookSST)return"";var r=[He];r[r.length]=at("sst",null,{xmlns:ot[0],count:e.Count,uniqueCount:e.Unique});for(var n=0;n!=e.length;++n)if(null!=e[n]){var a=e[n],i="<si>";a.r?i+=a.r:(i+="<t",a.t||(a.t=""),a.t.match(Fn)&&(i+=' xml:space="preserve"'),i+=">"+ze(a.t)+"</t>"),i+="</si>",r[r.length]=i}return r.length>2&&(r[r.length]="</sst>",r[1]=r[1].replace("/>",">")),r.join("")}var Bn=function(e,t){var r=!1;return null==t&&(r=!0,t=jt(15+4*e.t.length)),t.write_shift(1,0),pr(e.t,t),r?t.slice(0,t.l):t};function Wn(e){var t=zt();$t(t,159,function(e,t){return t||(t=jt(8)),t.write_shift(4,e.Count),t.write_shift(4,e.Unique),t}(e));for(var r=0;r<e.length;++r)$t(t,19,Bn(e[r]));return $t(t,160),t.end()}function Hn(e){var t,r,n=0,a=function(e){for(var t=[],r=e.split(""),n=0;n<r.length;++n)t[n]=r[n].charCodeAt(0);return t}(e),i=a.length+1;for((t=d(i))[0]=a.length,r=1;r!=i;++r)t[r]=a[r-1];for(r=i-1;r>=0;--r)n=((16384&n?1:0)|n<<1&32767)^t[r];return 52811^n}var Gn=function(){function e(e,r){switch(r.type){case"base64":return t(h(e),r);case"binary":return t(e,r);case"buffer":return t(u&&Buffer.isBuffer(e)?e.toString("binary"):T(e),r);case"array":return t(De(e),r)}throw new Error("Unrecognized type "+r.type)}function t(e,t){var r=(t||{}).dense?[]:{},n=e.match(/\\trowd.*?\\row\b/g);if(!n.length)throw new Error("RTF missing table");var a={s:{c:0,r:0},e:{c:0,r:n.length-1}};return n.forEach((function(e,t){Array.isArray(r)&&(r[t]=[]);for(var n,i=/\\\w+\b/g,s=0,o=-1;n=i.exec(e);){if("\\cell"===n[0]){var l=e.slice(s,i.lastIndex-n[0].length);if(" "==l[0]&&(l=l.slice(1)),++o,l.length){var f={v:l,t:"s"};Array.isArray(r)?r[t][o]=f:r[rr({r:t,c:o})]=f}}s=i.lastIndex}o>a.e.c&&(a.e.c=o)})),r["!ref"]=ar(a),r}return{to_workbook:function(t,r){return lr(e(t,r),r)},to_sheet:e,from_sheet:function(e){for(var t,r=["{\\rtf1\\ansi"],n=ir(e["!ref"]),a=Array.isArray(e),i=n.s.r;i<=n.e.r;++i){r.push("\\trowd\\trautofit1");for(var s=n.s.c;s<=n.e.c;++s)r.push("\\cellx"+(s+1));for(r.push("\\pard\\intbl"),s=n.s.c;s<=n.e.c;++s){var o=rr({r:i,c:s});(t=a?(e[i]||[])[s]:e[o])&&(null!=t.v||t.f&&!t.F)&&(r.push(" "+(t.w||(or(t),t.w))),r.push("\\cell"))}r.push("\\pard\\intbl\\row")}return r.join("")+"}"}}}();function Vn(e){for(var t=0,r=1;3!=t;++t)r=256*r+(e[t]>255?255:e[t]<0?0:e[t]);return r.toString(16).toUpperCase().slice(1)}var jn=6;function zn(e){return Math.floor((e+Math.round(128/jn)/256)*jn)}function $n(e){return Math.floor((e-5)/jn*100+.5)/100}function Xn(e){return Math.round((e*jn+5)/jn*256)/256}function Yn(e){e.width?(e.wpx=zn(e.width),e.wch=$n(e.wpx),e.MDW=jn):e.wpx?(e.wch=$n(e.wpx),e.width=Xn(e.wch),e.MDW=jn):"number"==typeof e.wch&&(e.width=Xn(e.wch),e.wpx=zn(e.width),e.MDW=jn),e.customWidth&&delete e.customWidth}var Kn=96;function Jn(e){return 96*e/Kn}function Zn(e){return e*Kn/96}function qn(e,t){var r,n=[He,at("styleSheet",null,{xmlns:ot[0],"xmlns:vt":st.vt})];return e.SSF&&null!=(r=function(e){var t=["<numFmts>"];return[[5,8],[23,26],[41,44],[50,392]].forEach((function(r){for(var n=r[0];n<=r[1];++n)null!=e[n]&&(t[t.length]=at("numFmt",null,{numFmtId:n,formatCode:ze(e[n])}))})),1===t.length?"":(t[t.length]="</numFmts>",t[0]=at("numFmts",null,{count:t.length-2}).replace("/>",">"),t.join(""))}(e.SSF))&&(n[n.length]=r),n[n.length]='<fonts count="1"><font><sz val="12"/><color theme="1"/><name val="Calibri"/><family val="2"/><scheme val="minor"/></font></fonts>',n[n.length]='<fills count="2"><fill><patternFill patternType="none"/></fill><fill><patternFill patternType="gray125"/></fill></fills>',n[n.length]='<borders count="1"><border><left/><right/><top/><bottom/><diagonal/></border></borders>',n[n.length]='<cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0"/></cellStyleXfs>',(r=function(e){var t=[];return t[t.length]=at("cellXfs",null),e.forEach((function(e){t[t.length]=at("xf",null,e)})),t[t.length]="</cellXfs>",2===t.length?"":(t[0]=at("cellXfs",null,{count:t.length-2}).replace("/>",">"),t.join(""))}(t.cellXfs))&&(n[n.length]=r),n[n.length]='<cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0"/></cellStyles>',n[n.length]='<dxfs count="0"/>',n[n.length]='<tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4"/>',n.length>2&&(n[n.length]="</styleSheet>",n[1]=n[1].replace("/>",">")),n.join("")}function Qn(e,t,r){r||(r=jt(6+4*t.length)),r.write_shift(2,e),pr(t,r);var n=r.length>r.l?r.slice(0,r.l):r;return null==r.l&&(r.l=r.length),n}function ea(e,t){t||(t=jt(153)),t.write_shift(2,20*e.sz),function(e,t){t||(t=jt(2));var r=(e.italic?2:0)|(e.strike?8:0)|(e.outline?16:0)|(e.shadow?32:0)|(e.condense?64:0)|(e.extend?128:0);t.write_shift(1,r),t.write_shift(1,0)}(e,t),t.write_shift(2,e.bold?700:400);var r=0;"superscript"==e.vertAlign?r=1:"subscript"==e.vertAlign&&(r=2),t.write_shift(2,r),t.write_shift(1,e.underline||0),t.write_shift(1,e.family||0),t.write_shift(1,e.charset||0),t.write_shift(1,0),Mr(e.color,t);var n=0;return"major"==e.scheme&&(n=1),"minor"==e.scheme&&(n=2),t.write_shift(1,n),pr(e.name,t),t.length>t.l?t.slice(0,t.l):t}var ta,ra=["none","solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"],na=Vt;function aa(e,t){t||(t=jt(84)),ta||(ta=be(ra));var r=ta[e.patternType];null==r&&(r=40),t.write_shift(4,r);var n=0;if(40!=r)for(Mr({auto:1},t),Mr({auto:1},t);n<12;++n)t.write_shift(4,0);else{for(;n<4;++n)t.write_shift(4,0);for(;n<12;++n)t.write_shift(4,0)}return t.length>t.l?t.slice(0,t.l):t}function ia(e,t,r){r||(r=jt(16)),r.write_shift(2,t||0),r.write_shift(2,e.numFmtId||0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(1,0),r.write_shift(1,0);return r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r}function sa(e,t){return t||(t=jt(10)),t.write_shift(1,0),t.write_shift(1,0),t.write_shift(4,0),t.write_shift(4,0),t}var oa=Vt;function la(e){var t;$t(e,613,hr(1)),$t(e,46,(t||(t=jt(51)),t.write_shift(1,0),sa(0,t),sa(0,t),sa(0,t),sa(0,t),sa(0,t),t.length>t.l?t.slice(0,t.l):t)),$t(e,614)}function fa(e){var t,r;$t(e,619,hr(1)),$t(e,48,(t={xfId:0,builtinId:0,name:"Normal"},r||(r=jt(52)),r.write_shift(4,t.xfId),r.write_shift(2,1),r.write_shift(1,+t.builtinId),r.write_shift(1,0),yr(t.name||"",r),r.length>r.l?r.slice(0,r.l):r)),$t(e,620)}function ca(e){$t(e,508,function(e,t,r){var n=jt(2052);return n.write_shift(4,e),yr(t,n),yr(r,n),n.length>n.l?n.slice(0,n.l):n}(0,"TableStyleMedium9","PivotStyleMedium4")),$t(e,509)}function ha(e,t){var r=zt();return $t(r,278),function(e,t){if(t){var r=0;[[5,8],[23,26],[41,44],[50,392]].forEach((function(e){for(var n=e[0];n<=e[1];++n)null!=t[n]&&++r})),0!=r&&($t(e,615,hr(r)),[[5,8],[23,26],[41,44],[50,392]].forEach((function(r){for(var n=r[0];n<=r[1];++n)null!=t[n]&&$t(e,44,Qn(n,t[n]))})),$t(e,616))}}(r,e.SSF),function(e){$t(e,611,hr(1)),$t(e,43,ea({sz:12,color:{theme:1},name:"Calibri",family:2,scheme:"minor"})),$t(e,612)}(r),function(e){$t(e,603,hr(2)),$t(e,45,aa({patternType:"none"})),$t(e,45,aa({patternType:"gray125"})),$t(e,604)}(r),la(r),function(e){$t(e,626,hr(1)),$t(e,47,ia({numFmtId:0,fontId:0,fillId:0,borderId:0},65535)),$t(e,627)}(r),function(e,t){$t(e,617,hr(t.length)),t.forEach((function(t){$t(e,47,ia(t,0))})),$t(e,618)}(r,t.cellXfs),fa(r),function(e){$t(e,505,hr(0)),$t(e,506)}(r),ca(r),$t(r,279),r.end()}function ua(e,t){if(t&&t.themeXLSX)return t.themeXLSX;if(e&&"string"==typeof e.raw)return e.raw;var r=[He];return r[r.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',r[r.length]="<a:themeElements>",r[r.length]='<a:clrScheme name="Office">',r[r.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',r[r.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',r[r.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',r[r.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',r[r.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',r[r.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',r[r.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',r[r.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',r[r.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',r[r.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',r[r.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',r[r.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',r[r.length]="</a:clrScheme>",r[r.length]='<a:fontScheme name="Office">',r[r.length]="<a:majorFont>",r[r.length]='<a:latin typeface="Cambria"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Times New Roman"/>',r[r.length]='<a:font script="Hebr" typeface="Times New Roman"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="MoolBoran"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Times New Roman"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:majorFont>",r[r.length]="<a:minorFont>",r[r.length]='<a:latin typeface="Calibri"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Arial"/>',r[r.length]='<a:font script="Hebr" typeface="Arial"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="DaunPenh"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Arial"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:minorFont>",r[r.length]="</a:fontScheme>",r[r.length]='<a:fmtScheme name="Office">',r[r.length]="<a:fillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="1"/>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="0"/>',r[r.length]="</a:gradFill>",r[r.length]="</a:fillStyleLst>",r[r.length]="<a:lnStyleLst>",r[r.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]="</a:lnStyleLst>",r[r.length]="<a:effectStyleLst>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',r[r.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',r[r.length]="</a:effectStyle>",r[r.length]="</a:effectStyleLst>",r[r.length]="<a:bgFillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]="</a:bgFillStyleLst>",r[r.length]="</a:fmtScheme>",r[r.length]="</a:themeElements>",r[r.length]="<a:objectDefaults>",r[r.length]="<a:spDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',r[r.length]="</a:spDef>",r[r.length]="<a:lnDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',r[r.length]="</a:lnDef>",r[r.length]="</a:objectDefaults>",r[r.length]="<a:extraClrSchemeLst/>",r[r.length]="</a:theme>",r.join("")}function pa(){var e,t,r=zt();return $t(r,332),$t(r,334,hr(1)),$t(r,335,((t=jt(12+2*(e={name:"XLDAPR",version:12e4,flags:3496657072}).name.length)).write_shift(4,e.flags),t.write_shift(4,e.version),pr(e.name,t),t.slice(0,t.l))),$t(r,336),$t(r,339,function(e,t){var r=jt(8+2*t.length);return r.write_shift(4,e),pr(t,r),r.slice(0,r.l)}(1,"XLDAPR")),$t(r,52),$t(r,35,hr(514)),$t(r,4096,hr(0)),$t(r,4097,dn(1)),$t(r,36),$t(r,53),$t(r,340),$t(r,337,function(e,t){var r=jt(8);return r.write_shift(4,e),r.write_shift(4,t?1:0),r}(1,!0)),$t(r,51,function(e){var t=jt(4+8*e.length);t.write_shift(4,e.length);for(var r=0;r<e.length;++r)t.write_shift(4,e[r][0]),t.write_shift(4,e[r][1]);return t}([[1,0]])),$t(r,338),$t(r,333),r.end()}function da(){var e=[He];return e.push('<metadata xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:xlrd="http://schemas.microsoft.com/office/spreadsheetml/2017/richdata" xmlns:xda="http://schemas.microsoft.com/office/spreadsheetml/2017/dynamicarray">\n  <metadataTypes count="1">\n    <metadataType name="XLDAPR" minSupportedVersion="120000" copy="1" pasteAll="1" pasteValues="1" merge="1" splitFirst="1" rowColShift="1" clearFormats="1" clearComments="1" assign="1" coerce="1" cellMeta="1"/>\n  </metadataTypes>\n  <futureMetadata name="XLDAPR" count="1">\n    <bk>\n      <extLst>\n        <ext uri="{bdbb8cdc-fa1e-496e-a857-3c3f30c029c3}">\n          <xda:dynamicArrayProperties fDynamic="1" fCollapsed="0"/>\n        </ext>\n      </extLst>\n    </bk>\n  </futureMetadata>\n  <cellMetadata count="1">\n    <bk>\n      <rc t="1" v="0"/>\n    </bk>\n  </cellMetadata>\n</metadata>'),e.join("")}var ma=1024;function ga(e,t){for(var r=[21600,21600],n=["m0,0l0",r[1],r[0],r[1],r[0],"0xe"].join(","),a=[at("xml",null,{"xmlns:v":lt.v,"xmlns:o":lt.o,"xmlns:x":lt.x,"xmlns:mv":lt.mv}).replace(/\/>/,">"),at("o:shapelayout",at("o:idmap",null,{"v:ext":"edit",data:e}),{"v:ext":"edit"}),at("v:shapetype",[at("v:stroke",null,{joinstyle:"miter"}),at("v:path",null,{gradientshapeok:"t","o:connecttype":"rect"})].join(""),{id:"_x0000_t202","o:spt":202,coordsize:r.join(","),path:n})];ma<1e3*e;)ma+=1e3;return t.forEach((function(e){var t=tr(e[0]),r={color2:"#BEFF82",type:"gradient"};"gradient"==r.type&&(r.angle="-180");var n="gradient"==r.type?at("o:fill",null,{type:"gradientUnscaled","v:ext":"view"}):null,i=at("v:fill",n,r);++ma,a=a.concat(["<v:shape"+nt({id:"_x0000_s"+ma,type:"#_x0000_t202",style:"position:absolute; margin-left:80pt;margin-top:5pt;width:104pt;height:64pt;z-index:10"+(e[1].hidden?";visibility:hidden":""),fillcolor:"#ECFAD4",strokecolor:"#edeaa1"})+">",i,at("v:shadow",null,{on:"t",obscured:"t"}),at("v:path",null,{"o:connecttype":"none"}),'<v:textbox><div style="text-align:left"></div></v:textbox>','<x:ClientData ObjectType="Note">',"<x:MoveWithCells/>","<x:SizeWithCells/>",rt("x:Anchor",[t.c+1,0,t.r+1,0,t.c+3,20,t.r+5,20].join(",")),rt("x:AutoFill","False"),rt("x:Row",String(t.r)),rt("x:Column",String(t.c)),e[1].hidden?"":"<x:Visible/>","</x:ClientData>","</v:shape>"])})),a.push("</xml>"),a.join("")}function va(e){var t=[He,at("comments",null,{xmlns:ot[0]})],r=[];return t.push("<authors>"),e.forEach((function(e){e[1].forEach((function(e){var n=ze(e.a);-1==r.indexOf(n)&&(r.push(n),t.push("<author>"+n+"</author>")),e.T&&e.ID&&-1==r.indexOf("tc="+e.ID)&&(r.push("tc="+e.ID),t.push("<author>tc="+e.ID+"</author>"))}))})),0==r.length&&(r.push("SheetJ5"),t.push("<author>SheetJ5</author>")),t.push("</authors>"),t.push("<commentList>"),e.forEach((function(e){var n=0,a=[];if(e[1][0]&&e[1][0].T&&e[1][0].ID?n=r.indexOf("tc="+e[1][0].ID):e[1].forEach((function(e){e.a&&(n=r.indexOf(ze(e.a))),a.push(e.t||"")})),t.push('<comment ref="'+e[0]+'" authorId="'+n+'"><text>'),a.length<=1)t.push(rt("t",ze(a[0]||"")));else{for(var i="Comment:\n    "+a[0]+"\n",s=1;s<a.length;++s)i+="Reply:\n    "+a[s]+"\n";t.push(rt("t",ze(i)))}t.push("</text></comment>")})),t.push("</commentList>"),t.length>2&&(t[t.length]="</comments>",t[1]=t[1].replace("/>",">")),t.join("")}function Ta(e,t,r){var n=[He,at("ThreadedComments",null,{xmlns:st.TCMNT}).replace(/[\/]>/,">")];return e.forEach((function(e){var a="";(e[1]||[]).forEach((function(i,s){if(i.T){i.a&&-1==t.indexOf(i.a)&&t.push(i.a);var o={ref:e[0],id:"{54EE7951-7262-4200-6969-"+("000000000000"+r.tcid++).slice(-12)+"}"};0==s?a=o.id:o.parentId=a,i.ID=o.id,i.a&&(o.personId="{54EE7950-7262-4200-6969-"+("000000000000"+t.indexOf(i.a)).slice(-12)+"}"),n.push(at("threadedComment",rt("text",i.t||""),o))}else delete i.ID}))})),n.push("</ThreadedComments>"),n.join("")}var wa=ur;function Ea(e){var t=zt(),r=[];return $t(t,628),$t(t,630),e.forEach((function(e){e[1].forEach((function(e){r.indexOf(e.a)>-1||(r.push(e.a.slice(0,54)),$t(t,632,function(e){return pr(e.slice(0,54))}(e.a)))}))})),$t(t,631),$t(t,633),e.forEach((function(e){e[1].forEach((function(n){n.iauthor=r.indexOf(n.a);var a={s:tr(e[0]),e:tr(e[0])};$t(t,635,function(e,t){return null==t&&(t=jt(36)),t.write_shift(4,e[1].iauthor),Dr(e[0],t),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t}([a,n])),n.t&&n.t.length>0&&$t(t,637,vr(n)),$t(t,636),delete n.iauthor}))})),$t(t,634),$t(t,629),t.end()}var ba=["xlsb","xlsm","xlam","biff8","xla"],Sa=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,t={r:0,c:0};function r(e,r,n,a){var i=!1,s=!1;0==n.length?s=!0:"["==n.charAt(0)&&(s=!0,n=n.slice(1,-1)),0==a.length?i=!0:"["==a.charAt(0)&&(i=!0,a=a.slice(1,-1));var o=n.length>0?0|parseInt(n,10):0,l=a.length>0?0|parseInt(a,10):0;return i?l+=t.c:--l,s?o+=t.r:--o,r+(i?"":"$")+er(l)+(s?"":"$")+qt(o)}return function(n,a){return t=a,n.replace(e,r)}}(),Aa=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,_a=function(){return function(e,t){return e.replace(Aa,(function(e,r,n,a,i,s){var o=Qt(a)-(n?0:t.c),l=Zt(s)-(i?0:t.r);return r+"R"+(0==l?"":i?l+1:"["+l+"]")+"C"+(0==o?"":n?o+1:"["+o+"]")}))}}();function ya(e,t){return e.replace(Aa,(function(e,r,n,a,i,s){return r+("$"==n?n+a:er(Qt(a)+t.c))+("$"==i?i+s:qt(Zt(s)+t.r))}))}function Oa(e){e.l+=1}function xa(e,t){var r=e.read_shift(1==t?1:2);return[16383&r,r>>14&1,r>>15&1]}function Ca(e,t,r){var n=2;if(r){if(r.biff>=2&&r.biff<=5)return Ra(e);12==r.biff&&(n=4)}var a=e.read_shift(n),i=e.read_shift(n),s=xa(e,2),o=xa(e,2);return{s:{r:a,c:s[0],cRel:s[1],rRel:s[2]},e:{r:i,c:o[0],cRel:o[1],rRel:o[2]}}}function Ra(e){var t=xa(e,2),r=xa(e,2),n=e.read_shift(1),a=e.read_shift(1);return{s:{r:t[0],c:n,cRel:t[1],rRel:t[2]},e:{r:r[0],c:a,cRel:r[1],rRel:r[2]}}}function Na(e,t,r){if(r&&r.biff>=2&&r.biff<=5)return function(e){var t=xa(e,2),r=e.read_shift(1);return{r:t[0],c:r,cRel:t[1],rRel:t[2]}}(e);var n=e.read_shift(r&&12==r.biff?4:2),a=xa(e,2);return{r:n,c:a[0],cRel:a[1],rRel:a[2]}}function ka(e){var t=e.read_shift(2),r=e.read_shift(2);return{r:t,c:255&r,fQuoted:!!(16384&r),cRel:r>>15,rRel:r>>15}}function Ia(e){var t=1&e[e.l+1];return e.l+=4,[t,1]}function Da(e){return[e.read_shift(1),e.read_shift(1)]}function Pa(e,t){var r=[e.read_shift(1)];if(12==t)switch(r[0]){case 2:r[0]=4;break;case 4:r[0]=16;break;case 0:r[0]=1;break;case 1:r[0]=2}switch(r[0]){case 4:r[1]=function(e,t){return 1===e.read_shift(t)}(e,1)?"TRUE":"FALSE",12!=t&&(e.l+=7);break;case 37:case 16:r[1]=Gr[e[e.l]],e.l+=12==t?4:8;break;case 0:e.l+=8;break;case 1:r[1]=Pr(e);break;case 2:r[1]=function(e,t,r){if(r.biff>5)return function(e,t,r){var n=e.read_shift(r&&2==r.biff?1:2);return 0===n?(e.l++,""):function(e,t,r){if(r){if(r.biff>=2&&r.biff<=5)return e.read_shift(t,"cpstr");if(r.biff>=12)return e.read_shift(t,"dbcs-cont")}return 0===e.read_shift(1)?e.read_shift(t,"sbcs-cont"):e.read_shift(t,"dbcs-cont")}(e,n,r)}(e,0,r);var n=e.read_shift(1);return 0===n?(e.l++,""):e.read_shift(n,r.biff<=4||!e.lens?"cpstr":"sbcs-cont")}(e,0,{biff:t>0&&t<8?2:t});break;default:throw new Error("Bad SerAr: "+r[0])}return r}function La(e,t,r){for(var n=e.read_shift(12==r.biff?4:2),a=[],i=0;i!=n;++i)a.push((12==r.biff?Ir:An)(e));return a}function Ma(e,t,r){var n=0,a=0;12==r.biff?(n=e.read_shift(4),a=e.read_shift(4)):(a=1+e.read_shift(1),n=1+e.read_shift(2)),r.biff>=2&&r.biff<8&&(--n,0==--a&&(a=256));for(var i=0,s=[];i!=n&&(s[i]=[]);++i)for(var o=0;o!=a;++o)s[i][o]=Pa(e,r.biff);return s}function Fa(e,t,r){return e.l+=2,[ka(e)]}function Ua(e){return e.l+=6,[]}function Ba(e){return e.l+=2,[pn(e),1&e.read_shift(2)]}var Wa=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"];var Ha={1:{n:"PtgExp",f:function(e,t,r){return e.l++,r&&12==r.biff?[e.read_shift(4,"i"),0]:[e.read_shift(2),e.read_shift(r&&2==r.biff?1:2)]}},2:{n:"PtgTbl",f:Vt},3:{n:"PtgAdd",f:Oa},4:{n:"PtgSub",f:Oa},5:{n:"PtgMul",f:Oa},6:{n:"PtgDiv",f:Oa},7:{n:"PtgPower",f:Oa},8:{n:"PtgConcat",f:Oa},9:{n:"PtgLt",f:Oa},10:{n:"PtgLe",f:Oa},11:{n:"PtgEq",f:Oa},12:{n:"PtgGe",f:Oa},13:{n:"PtgGt",f:Oa},14:{n:"PtgNe",f:Oa},15:{n:"PtgIsect",f:Oa},16:{n:"PtgUnion",f:Oa},17:{n:"PtgRange",f:Oa},18:{n:"PtgUplus",f:Oa},19:{n:"PtgUminus",f:Oa},20:{n:"PtgPercent",f:Oa},21:{n:"PtgParen",f:Oa},22:{n:"PtgMissArg",f:Oa},23:{n:"PtgStr",f:function(e,t,r){return e.l++,gn(e,0,r)}},26:{n:"PtgSheet",f:function(e,t,r){return e.l+=5,e.l+=2,e.l+=2==r.biff?1:4,["PTGSHEET"]}},27:{n:"PtgEndSheet",f:function(e,t,r){return e.l+=2==r.biff?4:5,["PTGENDSHEET"]}},28:{n:"PtgErr",f:function(e){return e.l++,Gr[e.read_shift(1)]}},29:{n:"PtgBool",f:function(e){return e.l++,0!==e.read_shift(1)}},30:{n:"PtgInt",f:function(e){return e.l++,e.read_shift(2)}},31:{n:"PtgNum",f:function(e){return e.l++,Pr(e)}},32:{n:"PtgArray",f:function(e,t,r){var n=(96&e[e.l++])>>5;return e.l+=2==r.biff?6:12==r.biff?14:7,[n]}},33:{n:"PtgFunc",f:function(e,t,r){var n=(96&e[e.l])>>5;e.l+=1;var a=e.read_shift(r&&r.biff<=3?1:2);return[ai[a],ni[a],n]}},34:{n:"PtgFuncVar",f:function(e,t,r){var n=e[e.l++],a=e.read_shift(1),i=r&&r.biff<=3?[88==n?-1:0,e.read_shift(1)]:function(e){return[e[e.l+1]>>7,32767&e.read_shift(2)]}(e);return[a,(0===i[0]?ni:ri)[i[1]]]}},35:{n:"PtgName",f:function(e,t,r){var n=e.read_shift(1)>>>5&3,a=!r||r.biff>=8?4:2,i=e.read_shift(a);switch(r.biff){case 2:e.l+=5;break;case 3:case 4:e.l+=8;break;case 5:e.l+=12}return[n,0,i]}},36:{n:"PtgRef",f:function(e,t,r){var n=(96&e[e.l])>>5;return e.l+=1,[n,Na(e,0,r)]}},37:{n:"PtgArea",f:function(e,t,r){return[(96&e[e.l++])>>5,Ca(e,r.biff>=2&&r.biff,r)]}},38:{n:"PtgMemArea",f:function(e,t,r){var n=e.read_shift(1)>>>5&3;return e.l+=r&&2==r.biff?3:4,[n,e.read_shift(r&&2==r.biff?1:2)]}},39:{n:"PtgMemErr",f:Vt},40:{n:"PtgMemNoMem",f:Vt},41:{n:"PtgMemFunc",f:function(e,t,r){return[e.read_shift(1)>>>5&3,e.read_shift(r&&2==r.biff?1:2)]}},42:{n:"PtgRefErr",f:function(e,t,r){var n=e.read_shift(1)>>>5&3;return e.l+=4,r.biff<8&&e.l--,12==r.biff&&(e.l+=2),[n]}},43:{n:"PtgAreaErr",f:function(e,t,r){var n=(96&e[e.l++])>>5;return e.l+=r&&r.biff>8?12:r.biff<8?6:8,[n]}},44:{n:"PtgRefN",f:function(e,t,r){var n=(96&e[e.l])>>5;e.l+=1;var a=function(e,t,r){var n=r&&r.biff?r.biff:8;if(n>=2&&n<=5)return function(e){var t=e.read_shift(2),r=e.read_shift(1),n=(32768&t)>>15,a=(16384&t)>>14;return t&=16383,1==n&&t>=8192&&(t-=16384),1==a&&r>=128&&(r-=256),{r:t,c:r,cRel:a,rRel:n}}(e);var a=e.read_shift(n>=12?4:2),i=e.read_shift(2),s=(16384&i)>>14,o=(32768&i)>>15;if(i&=16383,1==o)for(;a>524287;)a-=1048576;if(1==s)for(;i>8191;)i-=16384;return{r:a,c:i,cRel:s,rRel:o}}(e,0,r);return[n,a]}},45:{n:"PtgAreaN",f:function(e,t,r){var n=(96&e[e.l++])>>5,a=function(e,t,r){if(r.biff<8)return Ra(e);var n=e.read_shift(12==r.biff?4:2),a=e.read_shift(12==r.biff?4:2),i=xa(e,2),s=xa(e,2);return{s:{r:n,c:i[0],cRel:i[1],rRel:i[2]},e:{r:a,c:s[0],cRel:s[1],rRel:s[2]}}}(e,0,r);return[n,a]}},46:{n:"PtgMemAreaN",f:function(e){return[e.read_shift(1)>>>5&3,e.read_shift(2)]}},47:{n:"PtgMemNoMemN",f:function(e){return[e.read_shift(1)>>>5&3,e.read_shift(2)]}},57:{n:"PtgNameX",f:function(e,t,r){return 5==r.biff?function(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2,"i");e.l+=8;var n=e.read_shift(2);return e.l+=12,[t,r,n]}(e):[e.read_shift(1)>>>5&3,e.read_shift(2),e.read_shift(4)]}},58:{n:"PtgRef3d",f:function(e,t,r){var n=(96&e[e.l])>>5;e.l+=1;var a=e.read_shift(2);return r&&5==r.biff&&(e.l+=12),[n,a,Na(e,0,r)]}},59:{n:"PtgArea3d",f:function(e,t,r){var n=(96&e[e.l++])>>5,a=e.read_shift(2,"i");if(r)switch(r.biff){case 5:e.l+=12,6;break;case 12:12}return[n,a,Ca(e,0,r)]}},60:{n:"PtgRefErr3d",f:function(e,t,r){var n=(96&e[e.l++])>>5,a=e.read_shift(2),i=4;if(r)switch(r.biff){case 5:i=15;break;case 12:i=6}return e.l+=i,[n,a]}},61:{n:"PtgAreaErr3d",f:function(e,t,r){var n=(96&e[e.l++])>>5,a=e.read_shift(2),i=8;if(r)switch(r.biff){case 5:e.l+=12,i=6;break;case 12:i=12}return e.l+=i,[n,a]}},255:{}},Ga={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},Va={1:{n:"PtgElfLel",f:Ba},2:{n:"PtgElfRw",f:Fa},3:{n:"PtgElfCol",f:Fa},6:{n:"PtgElfRwV",f:Fa},7:{n:"PtgElfColV",f:Fa},10:{n:"PtgElfRadical",f:Fa},11:{n:"PtgElfRadicalS",f:Ua},13:{n:"PtgElfColS",f:Ua},15:{n:"PtgElfColSV",f:Ua},16:{n:"PtgElfRadicalLel",f:Ba},25:{n:"PtgList",f:function(e){e.l+=2;var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(4),a=e.read_shift(2),i=e.read_shift(2);return{ixti:t,coltype:3&r,rt:Wa[r>>2&31],idx:n,c:a,C:i}}},29:{n:"PtgSxName",f:function(e){return e.l+=2,[e.read_shift(4)]}},255:{}},ja={0:{n:"PtgAttrNoop",f:function(e){return e.l+=4,[0,0]}},1:{n:"PtgAttrSemi",f:function(e,t,r){var n=255&e[e.l+1]?1:0;return e.l+=r&&2==r.biff?3:4,[n]}},2:{n:"PtgAttrIf",f:function(e,t,r){var n=255&e[e.l+1]?1:0;return e.l+=2,[n,e.read_shift(r&&2==r.biff?1:2)]}},4:{n:"PtgAttrChoose",f:function(e,t,r){e.l+=2;for(var n=e.read_shift(r&&2==r.biff?1:2),a=[],i=0;i<=n;++i)a.push(e.read_shift(r&&2==r.biff?1:2));return a}},8:{n:"PtgAttrGoto",f:function(e,t,r){var n=255&e[e.l+1]?1:0;return e.l+=2,[n,e.read_shift(r&&2==r.biff?1:2)]}},16:{n:"PtgAttrSum",f:function(e,t,r){e.l+=r&&2==r.biff?3:4}},32:{n:"PtgAttrBaxcel",f:Ia},33:{n:"PtgAttrBaxcel",f:Ia},64:{n:"PtgAttrSpace",f:function(e){return e.read_shift(2),Da(e)}},65:{n:"PtgAttrSpaceSemi",f:function(e){return e.read_shift(2),Da(e)}},128:{n:"PtgAttrIfError",f:function(e){var t=255&e[e.l+1]?1:0;return e.l+=2,[t,e.read_shift(2)]}},255:{}};function za(e){for(var t=[],r=0;r<e.length;++r){for(var n=e[r],a=[],i=0;i<n.length;++i){var s=n[i];if(s)if(2===s[0])a.push('"'+s[1].replace(/"/g,'""')+'"');else a.push(s[1]);else a.push("")}t.push(a.join(","))}return t.join(";")}var $a={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function Xa(e,t,r){if(!e)return"SH33TJSERR0";if(r.biff>8&&(!e.XTI||!e.XTI[t]))return e.SheetNames[t];if(!e.XTI)return"SH33TJSERR6";var n=e.XTI[t];if(r.biff<8)return t>1e4&&(t-=65536),t<0&&(t=-t),0==t?"":e.XTI[t-1];if(!n)return"SH33TJSERR1";var a="";if(r.biff>8)switch(e[n[0]][0]){case 357:return a=-1==n[1]?"#REF":e.SheetNames[n[1]],n[1]==n[2]?a:a+":"+e.SheetNames[n[2]];case 358:return null!=r.SID?e.SheetNames[r.SID]:"SH33TJSSAME"+e[n[0]][0];default:return"SH33TJSSRC"+e[n[0]][0]}switch(e[n[0]][0][0]){case 1025:return a=-1==n[1]?"#REF":e.SheetNames[n[1]]||"SH33TJSERR3",n[1]==n[2]?a:a+":"+e.SheetNames[n[2]];case 14849:return e[n[0]].slice(1).map((function(e){return e.Name})).join(";;");default:return e[n[0]][0][3]?(a=-1==n[1]?"#REF":e[n[0]][0][3][n[1]]||"SH33TJSERR4",n[1]==n[2]?a:a+":"+e[n[0]][0][3][n[2]]):"SH33TJSERR2"}}function Ya(e,t,r){var n=Xa(e,t,r);return"#REF"==n?n:function(e,t){if(!(e||t&&t.biff<=5&&t.biff>=2))throw new Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e)?"'"+e+"'":e}(n,r)}function Ka(e,t,r,n,a){var i,s,o,l,f=a&&a.biff||8,c={s:{c:0,r:0},e:{c:0,r:0}},h=[],u=0,p=0,d="";if(!e[0]||!e[0][0])return"";for(var m=-1,g="",v=0,T=e[0].length;v<T;++v){var w=e[0][v];switch(w[0]){case"PtgUminus":h.push("-"+h.pop());break;case"PtgUplus":h.push("+"+h.pop());break;case"PtgPercent":h.push(h.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(i=h.pop(),s=h.pop(),m>=0){switch(e[0][m][1][0]){case 0:g=Le(" ",e[0][m][1][1]);break;case 1:g=Le("\r",e[0][m][1][1]);break;default:if(g="",a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}s+=g,m=-1}h.push(s+$a[w[0]]+i);break;case"PtgIsect":i=h.pop(),s=h.pop(),h.push(s+" "+i);break;case"PtgUnion":i=h.pop(),s=h.pop(),h.push(s+","+i);break;case"PtgRange":i=h.pop(),s=h.pop(),h.push(s+":"+i);break;case"PtgAttrChoose":case"PtgAttrGoto":case"PtgAttrIf":case"PtgAttrIfError":case"PtgAttrBaxcel":case"PtgAttrSemi":case"PtgMemArea":case"PtgTbl":case"PtgMemErr":case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":case"PtgMemFunc":case"PtgMemNoMem":break;case"PtgRef":o=Xt(w[1][1],c,a),h.push(Kt(o,f));break;case"PtgRefN":o=r?Xt(w[1][1],r,a):w[1][1],h.push(Kt(o,f));break;case"PtgRef3d":u=w[1][1],o=Xt(w[1][2],c,a),d=Ya(n,u,a),h.push(d+"!"+Kt(o,f));break;case"PtgFunc":case"PtgFuncVar":var E=w[1][0],b=w[1][1];E||(E=0);var S=0==(E&=127)?[]:h.slice(-E);h.length-=E,"User"===b&&(b=S.shift()),h.push(b+"("+S.join(",")+")");break;case"PtgBool":h.push(w[1]?"TRUE":"FALSE");break;case"PtgInt":case"PtgErr":h.push(w[1]);break;case"PtgNum":h.push(String(w[1]));break;case"PtgStr":h.push('"'+w[1].replace(/"/g,'""')+'"');break;case"PtgAreaN":l=Yt(w[1][1],r?{s:r}:c,a),h.push(Jt(l,a));break;case"PtgArea":l=Yt(w[1][1],c,a),h.push(Jt(l,a));break;case"PtgArea3d":u=w[1][1],l=w[1][2],d=Ya(n,u,a),h.push(d+"!"+Jt(l,a));break;case"PtgAttrSum":h.push("SUM("+h.pop()+")");break;case"PtgName":p=w[1][2];var A=(n.names||[])[p-1]||(n[0]||[])[p],_=A?A.Name:"SH33TJSNAME"+String(p);_&&"_xlfn."==_.slice(0,6)&&!a.xlfn&&(_=_.slice(6)),h.push(_);break;case"PtgNameX":var y,O=w[1][1];if(p=w[1][2],!(a.biff<=5)){var x="";if(14849==((n[O]||[])[0]||[])[0]||(1025==((n[O]||[])[0]||[])[0]?n[O][p]&&n[O][p].itab>0&&(x=n.SheetNames[n[O][p].itab-1]+"!"):x=n.SheetNames[p-1]+"!"),n[O]&&n[O][p])x+=n[O][p].Name;else if(n[0]&&n[0][p])x+=n[0][p].Name;else{var C=(Xa(n,O,a)||"").split(";;");C[p-1]?x=C[p-1]:x+="SH33TJSERRX"}h.push(x);break}O<0&&(O=-O),n[O]&&(y=n[O][p]),y||(y={Name:"SH33TJSERRY"}),h.push(y.Name);break;case"PtgParen":var R="(",N=")";if(m>=0){switch(g="",e[0][m][1][0]){case 2:R=Le(" ",e[0][m][1][1])+R;break;case 3:R=Le("\r",e[0][m][1][1])+R;break;case 4:N=Le(" ",e[0][m][1][1])+N;break;case 5:N=Le("\r",e[0][m][1][1])+N;break;default:if(a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}m=-1}h.push(R+h.pop()+N);break;case"PtgRefErr":case"PtgRefErr3d":case"PtgAreaErr":case"PtgAreaErr3d":h.push("#REF!");break;case"PtgExp":o={c:w[1][1],r:w[1][0]};var k={c:r.c,r:r.r};if(n.sharedf[rr(o)]){var I=n.sharedf[rr(o)];h.push(Ka(I,c,k,n,a))}else{var D=!1;for(i=0;i!=n.arrayf.length;++i)if(s=n.arrayf[i],!(o.c<s[0].s.c||o.c>s[0].e.c||o.r<s[0].s.r||o.r>s[0].e.r)){h.push(Ka(s[1],c,k,n,a)),D=!0;break}D||h.push(w[1])}break;case"PtgArray":h.push("{"+za(w[1])+"}");break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":m=v;break;case"PtgMissArg":h.push("");break;case"PtgList":h.push("Table"+w[1].idx+"[#"+w[1].rt+"]");break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw new Error("Unsupported ELFs");default:throw new Error("Unrecognized Formula Token: "+String(w))}if(3!=a.biff&&m>=0&&-1==["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"].indexOf(e[0][v][0])){var P=!0;switch((w=e[0][m])[1][0]){case 4:P=!1;case 0:g=Le(" ",w[1][1]);break;case 5:P=!1;case 1:g=Le("\r",w[1][1]);break;default:if(g="",a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+w[1][0])}h.push((P?g:"")+h.pop()+(P?"":g)),m=-1}}if(h.length>1&&a.WTF)throw new Error("bad formula stack");return h[0]}function Ja(e,t,r,n,a){var i=bn(t,r,a),s=function(e){if(null==e){var t=jt(8);return t.write_shift(1,3),t.write_shift(1,0),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(2,65535),t}return Lr("number"==typeof e?e:0)}(e.v),o=jt(6);o.write_shift(2,33),o.write_shift(4,0);for(var l=jt(e.bf.length),f=0;f<e.bf.length;++f)l[f]=e.bf[f];return w([i,s,o,l])}function Za(e,t,r){var n=e.read_shift(4),a=function(e,t,r){for(var n,a,i=e.l+t,s=[];i!=e.l;)t=i-e.l,a=e[e.l],n=Ha[a]||Ha[Ga[a]],24!==a&&25!==a||(n=(24===a?Va:ja)[e[e.l+1]]),n&&n.f?s.push([n.n,n.f(e,t,r)]):Vt(e,t);return s}(e,n,r),i=e.read_shift(4),s=i>0?function(e,t,r,n){if(n.biff<8)return Vt(e,t);for(var a=e.l+t,i=[],s=0;s!==r.length;++s)switch(r[s][0]){case"PtgArray":r[s][1]=Ma(e,0,n),i.push(r[s][1]);break;case"PtgMemArea":r[s][2]=La(e,r[s][1],n),i.push(r[s][2]);break;case"PtgExp":n&&12==n.biff&&(r[s][1][1]=e.read_shift(4),i.push(r[s][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+r[s][0]}return 0!=(t=a-e.l)&&i.push(Vt(e,t)),i}(e,i,a,r):null;return[a,s]}var qa=Za,Qa=Za,ei=Za,ti=Za,ri={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},ni={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},ai={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};var ii="undefined"!=typeof Map;function si(e,t,r){var n=0,a=e.length;if(r){if(ii?r.has(t):Object.prototype.hasOwnProperty.call(r,t))for(var i=ii?r.get(t):r[t];n<i.length;++n)if(e[i[n]].t===t)return e.Count++,i[n]}else for(;n<a;++n)if(e[n].t===t)return e.Count++,n;return e[a]={t:t},e.Count++,e.Unique++,r&&(ii?(r.has(t)||r.set(t,[]),r.get(t).push(a)):(Object.prototype.hasOwnProperty.call(r,t)||(r[t]=[]),r[t].push(a))),a}function oi(e,t){var r={min:e+1,max:e+1},n=-1;return t.MDW&&(jn=t.MDW),null!=t.width?r.customWidth=1:null!=t.wpx?n=$n(t.wpx):null!=t.wch&&(n=t.wch),n>-1?(r.width=Xn(n),r.customWidth=1):null!=t.width&&(r.width=t.width),t.hidden&&(r.hidden=!0),null!=t.level&&(r.outlineLevel=r.level=t.level),r}function li(e,t){if(e){var r=[.7,.7,.75,.75,.3,.3];"xlml"==t&&(r=[1,1,1,1,.5,.5]),null==e.left&&(e.left=r[0]),null==e.right&&(e.right=r[1]),null==e.top&&(e.top=r[2]),null==e.bottom&&(e.bottom=r[3]),null==e.header&&(e.header=r[4]),null==e.footer&&(e.footer=r[5])}}function fi(e,t,r){var n=r.revssf[null!=t.z?t.z:"General"],a=60,i=e.length;if(null==n&&r.ssf)for(;a<392;++a)if(null==r.ssf[a]){he(t.z,a),r.ssf[a]=t.z,r.revssf[t.z]=n=a;break}for(a=0;a!=i;++a)if(e[a].numFmtId===n)return a;return e[i]={numFmtId:n,fontId:0,fillId:0,borderId:0,xfId:0,applyNumberFormat:1},i}function ci(e,t,r){if(e&&e["!ref"]){var n=ir(e["!ref"]);if(n.e.c<n.s.c||n.e.r<n.s.r)throw new Error("Bad range ("+r+"): "+e["!ref"])}}var hi=["objects","scenarios","selectLockedCells","selectUnlockedCells"],ui=["formatColumns","formatRows","formatCells","insertColumns","insertRows","insertHyperlinks","deleteColumns","deleteRows","sort","autoFilter","pivotTables"];function pi(e,t,r,n){if(e.c&&r["!comments"].push([t,e.c]),void 0===e.v&&"string"!=typeof e.f||"z"===e.t&&!e.f)return"";var a="",i=e.t,s=e.v;if("z"!==e.t)switch(e.t){case"b":a=e.v?"1":"0";break;case"n":a=""+e.v;break;case"e":a=Gr[e.v];break;case"d":n&&n.cellDates?a=Ie(e.v,-1).toISOString():((e=Pe(e)).t="n",a=""+(e.v=_e(Ie(e.v)))),void 0===e.z&&(e.z=k[14]);break;default:a=e.v}var o=rt("v",ze(a)),l={r:t},f=fi(n.cellXfs,e,n);switch(0!==f&&(l.s=f),e.t){case"n":case"z":break;case"d":l.t="d";break;case"b":l.t="b";break;case"e":l.t="e";break;default:if(null==e.v){delete e.t;break}if(e.v.length>32767)throw new Error("Text length must not exceed 32767 characters");if(n&&n.bookSST){o=rt("v",""+si(n.Strings,e.v,n.revStrings)),l.t="s";break}l.t="str"}if(e.t!=i&&(e.t=i,e.v=s),"string"==typeof e.f&&e.f){var c=e.F&&e.F.slice(0,t.length)==t?{t:"array",ref:e.F}:null;o=at("f",ze(e.f),c)+(null!=e.v?o:"")}return e.l&&r["!links"].push([t,e.l]),e.D&&(l.cm=1),at("c",o,l)}function di(e,t,r,n){var a,i=[He,at("worksheet",null,{xmlns:ot[0],"xmlns:r":st.r})],s=r.SheetNames[e],o="",l=r.Sheets[s];null==l&&(l={});var f=l["!ref"]||"A1",c=ir(f);if(c.e.c>16383||c.e.r>1048575){if(t.WTF)throw new Error("Range "+f+" exceeds format limit A1:XFD1048576");c.e.c=Math.min(c.e.c,16383),c.e.r=Math.min(c.e.c,1048575),f=ar(c)}n||(n={}),l["!comments"]=[];var h=[];!function(e,t,r,n,a){var i=!1,s={},o=null;if("xlsx"!==n.bookType&&t.vbaraw){var l=t.SheetNames[r];try{t.Workbook&&(l=t.Workbook.Sheets[r].CodeName||l)}catch(c){}i=!0,s.codeName=Qe(ze(l))}if(e&&e["!outline"]){var f={summaryBelow:1,summaryRight:1};e["!outline"].above&&(f.summaryBelow=0),e["!outline"].left&&(f.summaryRight=0),o=(o||"")+at("outlinePr",null,f)}(i||o)&&(a[a.length]=at("sheetPr",o,s))}(l,r,e,t,i),i[i.length]=at("dimension",null,{ref:f}),i[i.length]=function(e,t,r,n){var a={workbookViewId:"0"};return(((n||{}).Workbook||{}).Views||[])[0]&&(a.rightToLeft=n.Workbook.Views[0].RTL?"1":"0"),at("sheetViews",at("sheetView",null,a),{})}(0,0,0,r),t.sheetFormat&&(i[i.length]=at("sheetFormatPr",null,{defaultRowHeight:t.sheetFormat.defaultRowHeight||"16",baseColWidth:t.sheetFormat.baseColWidth||"10",outlineLevelRow:t.sheetFormat.outlineLevelRow||"7"})),null!=l["!cols"]&&l["!cols"].length>0&&(i[i.length]=function(e,t){for(var r,n=["<cols>"],a=0;a!=t.length;++a)(r=t[a])&&(n[n.length]=at("col",null,oi(a,r)));return n[n.length]="</cols>",n.join("")}(0,l["!cols"])),i[a=i.length]="<sheetData/>",l["!links"]=[],null!=l["!ref"]&&(o=function(e,t,r,n){var a,i,s=[],o=[],l=ir(e["!ref"]),f="",c="",h=[],u=0,p=0,d=e["!rows"],m=Array.isArray(e),g={r:c},v=-1;for(p=l.s.c;p<=l.e.c;++p)h[p]=er(p);for(u=l.s.r;u<=l.e.r;++u){for(o=[],c=qt(u),p=l.s.c;p<=l.e.c;++p){a=h[p]+c;var T=m?(e[u]||[])[p]:e[a];void 0!==T&&null!=(f=pi(T,a,e,t))&&o.push(f)}(o.length>0||d&&d[u])&&(g={r:c},d&&d[u]&&((i=d[u]).hidden&&(g.hidden=1),v=-1,i.hpx?v=Jn(i.hpx):i.hpt&&(v=i.hpt),v>-1&&(g.ht=v,g.customHeight=1),i.level&&(g.outlineLevel=i.level)),s[s.length]=at("row",o.join(""),g))}if(d)for(;u<d.length;++u)d&&d[u]&&(g={r:u+1},(i=d[u]).hidden&&(g.hidden=1),v=-1,i.hpx?v=Jn(i.hpx):i.hpt&&(v=i.hpt),v>-1&&(g.ht=v,g.customHeight=1),i.level&&(g.outlineLevel=i.level),s[s.length]=at("row","",g));return s.join("")}(l,t),o.length>0&&(i[i.length]=o)),i.length>a+1&&(i[i.length]="</sheetData>",i[a]=i[a].replace("/>",">")),l["!protect"]&&(i[i.length]=function(e){var t={sheet:1};return hi.forEach((function(r){null!=e[r]&&e[r]&&(t[r]="1")})),ui.forEach((function(r){null==e[r]||e[r]||(t[r]="0")})),e.password&&(t.password=Hn(e.password).toString(16).toUpperCase()),at("sheetProtection",null,t)}(l["!protect"])),null!=l["!autofilter"]&&(i[i.length]=function(e,t,r,n){var a="string"==typeof e.ref?e.ref:ar(e.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var i=r.Workbook.Names,s=nr(a);s.s.r==s.e.r&&(s.e.r=nr(t["!ref"]).e.r,a=ar(s));for(var o=0;o<i.length;++o){var l=i[o];if("_xlnm._FilterDatabase"==l.Name&&l.Sheet==n){l.Ref="'"+r.SheetNames[n]+"'!"+a;break}}return o==i.length&&i.push({Name:"_xlnm._FilterDatabase",Sheet:n,Ref:"'"+r.SheetNames[n]+"'!"+a}),at("autoFilter",null,{ref:a})}(l["!autofilter"],l,r,e)),null!=l["!merges"]&&l["!merges"].length>0&&(i[i.length]=function(e){if(0===e.length)return"";for(var t='<mergeCells count="'+e.length+'">',r=0;r!=e.length;++r)t+='<mergeCell ref="'+ar(e[r])+'"/>';return t+"</mergeCells>"}(l["!merges"]));var u,p,d=-1,m=-1;return l["!links"].length>0&&(i[i.length]="<hyperlinks>",l["!links"].forEach((function(e){e[1].Target&&(u={ref:e[0]},"#"!=e[1].Target.charAt(0)&&(m=Kr(n,-1,ze(e[1].Target).replace(/#.*$/,""),$r.HLINK),u["r:id"]="rId"+m),(d=e[1].Target.indexOf("#"))>-1&&(u.location=ze(e[1].Target.slice(d+1))),e[1].Tooltip&&(u.tooltip=ze(e[1].Tooltip)),i[i.length]=at("hyperlink",null,u))})),i[i.length]="</hyperlinks>"),delete l["!links"],null!=l["!margins"]&&(i[i.length]=(li(p=l["!margins"]),at("pageMargins",null,p))),t&&!t.ignoreEC&&null!=t.ignoreEC||(i[i.length]=rt("ignoredErrors",at("ignoredError",null,{numberStoredAsText:1,sqref:f}))),h.length>0&&(m=Kr(n,-1,"../drawings/drawing"+(e+1)+".xml",$r.DRAW),i[i.length]=at("drawing",null,{"r:id":"rId"+m}),l["!drawing"]=h),l["!comments"].length>0&&(m=Kr(n,-1,"../drawings/vmlDrawing"+(e+1)+".vml",$r.VML),i[i.length]=at("legacyDrawing",null,{"r:id":"rId"+m}),l["!legacy"]=m),i.length>1&&(i[i.length]="</worksheet>",i[1]=i[1].replace("/>",">")),i.join("")}function mi(e,t,r,n){var a=function(e,t,r){var n=jt(145),a=(r["!rows"]||[])[e]||{};n.write_shift(4,e),n.write_shift(4,0);var i=320;a.hpx?i=20*Jn(a.hpx):a.hpt&&(i=20*a.hpt),n.write_shift(2,i),n.write_shift(1,0);var s=0;a.level&&(s|=a.level),a.hidden&&(s|=16),(a.hpx||a.hpt)&&(s|=32),n.write_shift(1,s),n.write_shift(1,0);var o=0,l=n.l;n.l+=4;for(var f={r:e,c:0},c=0;c<16;++c)if(!(t.s.c>c+1<<10||t.e.c<c<<10)){for(var h=-1,u=-1,p=c<<10;p<c+1<<10;++p)f.c=p,(Array.isArray(r)?(r[f.r]||[])[f.c]:r[rr(f)])&&(h<0&&(h=p),u=p);h<0||(++o,n.write_shift(4,h),n.write_shift(4,u))}var d=n.l;return n.l=l,n.write_shift(4,o),n.l=d,n.length>n.l?n.slice(0,n.l):n}(n,r,t);(a.length>17||(t["!rows"]||[])[n])&&$t(e,0,a)}var gi=Ir,vi=Dr;var Ti=Ir,wi=Dr;var Ei=["left","right","top","bottom","header","footer"];function bi(e,t,r,n,a,i,s){if(void 0===t.v)return!1;var o="";switch(t.t){case"b":o=t.v?"1":"0";break;case"d":(t=Pe(t)).z=t.z||k[14],t.v=_e(Ie(t.v)),t.t="n";break;case"n":case"e":o=""+t.v;break;default:o=t.v}var l={r:r,c:n};switch(l.s=fi(a.cellXfs,t,a),t.l&&i["!links"].push([rr(l),t.l]),t.c&&i["!comments"].push([rr(l),t.c]),t.t){case"s":case"str":return a.bookSST?(o=si(a.Strings,t.v,a.revStrings),l.t="s",l.v=o,s?$t(e,18,function(e,t,r){return null==r&&(r=jt(8)),br(t,r),r.write_shift(4,t.v),r}(0,l)):$t(e,7,function(e,t,r){return null==r&&(r=jt(12)),wr(t,r),r.write_shift(4,t.v),r}(0,l))):(l.t="str",s?$t(e,17,function(e,t,r){return null==r&&(r=jt(8+4*e.v.length)),br(t,r),pr(e.v,r),r.length>r.l?r.slice(0,r.l):r}(t,l)):$t(e,6,function(e,t,r){return null==r&&(r=jt(12+4*e.v.length)),wr(t,r),pr(e.v,r),r.length>r.l?r.slice(0,r.l):r}(t,l))),!0;case"n":return t.v==(0|t.v)&&t.v>-1e3&&t.v<1e3?s?$t(e,13,function(e,t,r){return null==r&&(r=jt(8)),br(t,r),Nr(e.v,r),r}(t,l)):$t(e,2,function(e,t,r){return null==r&&(r=jt(12)),wr(t,r),Nr(e.v,r),r}(t,l)):s?$t(e,16,function(e,t,r){return null==r&&(r=jt(12)),br(t,r),Lr(e.v,r),r}(t,l)):$t(e,5,function(e,t,r){return null==r&&(r=jt(16)),wr(t,r),Lr(e.v,r),r}(t,l)),!0;case"b":return l.t="b",s?$t(e,15,function(e,t,r){return null==r&&(r=jt(5)),br(t,r),r.write_shift(1,e.v?1:0),r}(t,l)):$t(e,4,function(e,t,r){return null==r&&(r=jt(9)),wr(t,r),r.write_shift(1,e.v?1:0),r}(t,l)),!0;case"e":return l.t="e",s?$t(e,14,function(e,t,r){return null==r&&(r=jt(8)),br(t,r),r.write_shift(1,e.v),r.write_shift(2,0),r.write_shift(1,0),r}(t,l)):$t(e,3,function(e,t,r){return null==r&&(r=jt(9)),wr(t,r),r.write_shift(1,e.v),r}(t,l)),!0}return s?$t(e,12,function(e,t,r){return null==r&&(r=jt(4)),br(t,r)}(0,l)):$t(e,1,function(e,t,r){return null==r&&(r=jt(8)),wr(t,r)}(0,l)),!0}function Si(e,t){var r,n;t&&t["!merges"]&&($t(e,177,(r=t["!merges"].length,null==n&&(n=jt(4)),n.write_shift(4,r),n)),t["!merges"].forEach((function(t){$t(e,176,wi(t))})),$t(e,178))}function Ai(e,t){t&&t["!cols"]&&($t(e,390),t["!cols"].forEach((function(t,r){t&&$t(e,60,function(e,t,r){null==r&&(r=jt(18));var n=oi(e,t);r.write_shift(-4,e),r.write_shift(-4,e),r.write_shift(4,256*(n.width||10)),r.write_shift(4,0);var a=0;return t.hidden&&(a|=1),"number"==typeof n.width&&(a|=2),t.level&&(a|=t.level<<8),r.write_shift(2,a),r}(r,t))})),$t(e,391))}function _i(e,t){var r,n;t&&t["!ref"]&&($t(e,648),$t(e,649,(r=ir(t["!ref"]),(n=jt(24)).write_shift(4,4),n.write_shift(4,1),Dr(r,n),n)),$t(e,650))}function yi(e,t,r){t["!links"].forEach((function(t){if(t[1].Target){var n=Kr(r,-1,t[1].Target.replace(/#.*$/,""),$r.HLINK);$t(e,494,function(e,t){var r=jt(50+4*(e[1].Target.length+(e[1].Tooltip||"").length));Dr({s:tr(e[0]),e:tr(e[0])},r),Cr("rId"+t,r);var n=e[1].Target.indexOf("#");return pr((-1==n?"":e[1].Target.slice(n+1))||"",r),pr(e[1].Tooltip||"",r),pr("",r),r.slice(0,r.l)}(t,n))}})),delete t["!links"]}function Oi(e,t,r){$t(e,133),$t(e,137,function(e,t,r){null==r&&(r=jt(30));var n=924;return(((t||{}).Views||[])[0]||{}).RTL&&(n|=32),r.write_shift(2,n),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(2,0),r.write_shift(2,100),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(4,0),r}(0,r)),$t(e,138),$t(e,134)}function xi(e,t){var r,n;t["!protect"]&&$t(e,535,(r=t["!protect"],null==n&&(n=jt(66)),n.write_shift(2,r.password?Hn(r.password):0),n.write_shift(4,1),[["objects",!1],["scenarios",!1],["formatCells",!0],["formatColumns",!0],["formatRows",!0],["insertColumns",!0],["insertRows",!0],["insertHyperlinks",!0],["deleteColumns",!0],["deleteRows",!0],["selectLockedCells",!1],["sort",!0],["autoFilter",!0],["pivotTables",!0],["selectUnlockedCells",!1]].forEach((function(e){e[1]?n.write_shift(4,null==r[e[0]]||r[e[0]]?0:1):n.write_shift(4,null!=r[e[0]]&&r[e[0]]?0:1)})),n))}function Ci(e,t,r,n){var a=zt(),i=r.SheetNames[e],s=r.Sheets[i]||{},o=i;try{r&&r.Workbook&&(o=r.Workbook.Sheets[e].CodeName||o)}catch(h){}var l,f,c=ir(s["!ref"]||"A1");if(c.e.c>16383||c.e.r>1048575){if(t.WTF)throw new Error("Range "+(s["!ref"]||"A1")+" exceeds format limit A1:XFD1048576");c.e.c=Math.min(c.e.c,16383),c.e.r=Math.min(c.e.c,1048575)}return s["!links"]=[],s["!comments"]=[],$t(a,129),(r.vbaraw||s["!outline"])&&$t(a,147,function(e,t,r){null==r&&(r=jt(84+4*e.length));var n=192;t&&(t.above&&(n&=-65),t.left&&(n&=-129)),r.write_shift(1,n);for(var a=1;a<3;++a)r.write_shift(1,0);return Mr({auto:1},r),r.write_shift(-4,-1),r.write_shift(-4,-1),Ar(e,r),r.slice(0,r.l)}(o,s["!outline"])),$t(a,148,vi(c)),Oi(a,0,r.Workbook),Ai(a,s),function(e,t,r,n){var a,i=ir(t["!ref"]||"A1"),s="",o=[];$t(e,145);var l=Array.isArray(t),f=i.e.r;t["!rows"]&&(f=Math.max(i.e.r,t["!rows"].length-1));for(var c=i.s.r;c<=f;++c){s=qt(c),mi(e,t,i,c);var h=!1;if(c<=i.e.r)for(var u=i.s.c;u<=i.e.c;++u){c===i.s.r&&(o[u]=er(u)),a=o[u]+s;var p=l?(t[c]||[])[u]:t[a];h=!!p&&bi(e,p,c,u,n,t,h)}}$t(e,146)}(a,s,0,t),xi(a,s),function(e,t,r,n){if(t["!autofilter"]){var a=t["!autofilter"],i="string"==typeof a.ref?a.ref:ar(a.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var s=r.Workbook.Names,o=nr(i);o.s.r==o.e.r&&(o.e.r=nr(t["!ref"]).e.r,i=ar(o));for(var l=0;l<s.length;++l){var f=s[l];if("_xlnm._FilterDatabase"==f.Name&&f.Sheet==n){f.Ref="'"+r.SheetNames[n]+"'!"+i;break}}l==s.length&&s.push({Name:"_xlnm._FilterDatabase",Sheet:n,Ref:"'"+r.SheetNames[n]+"'!"+i}),$t(e,161,Dr(ir(i))),$t(e,162)}}(a,s,r,e),Si(a,s),yi(a,s,n),s["!margins"]&&$t(a,476,(l=s["!margins"],null==f&&(f=jt(48)),li(l),Ei.forEach((function(e){Lr(l[e],f)})),f)),t&&!t.ignoreEC&&null!=t.ignoreEC||_i(a,s),function(e,t,r,n){if(t["!comments"].length>0){var a=Kr(n,-1,"../drawings/vmlDrawing"+(r+1)+".vml",$r.VML);$t(e,551,Cr("rId"+a)),t["!legacy"]=a}}(a,s,e,n),$t(a,130),a.end()}var Ri=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]];var Ni="][*?/\\".split("");function ki(e,t){if(e.length>31){if(t)return!1;throw new Error("Sheet names cannot exceed 31 chars")}var r=!0;return Ni.forEach((function(n){if(-1!=e.indexOf(n)){if(!t)throw new Error("Sheet name cannot contain : \\ / ? * [ ]");r=!1}})),r}function Ii(e){if(!e||!e.SheetNames||!e.Sheets)throw new Error("Invalid Workbook");if(!e.SheetNames.length)throw new Error("Workbook is empty");var t,r,n,a=e.Workbook&&e.Workbook.Sheets||[];t=e.SheetNames,r=a,n=!!e.vbaraw,t.forEach((function(e,a){ki(e);for(var i=0;i<a;++i)if(e==t[i])throw new Error("Duplicate Sheet Name: "+e);if(n){var s=r&&r[a]&&r[a].CodeName||e;if(95==s.charCodeAt(0)&&s.length>22)throw new Error("Bad Code Name: Worksheet"+s)}}));for(var i=0;i<e.SheetNames.length;++i)ci(e.Sheets[e.SheetNames[i]],e.SheetNames[i],i)}function Di(e){var t=[He];t[t.length]=at("workbook",null,{xmlns:ot[0],"xmlns:r":st.r});var r=e.Workbook&&(e.Workbook.Names||[]).length>0,n={codeName:"ThisWorkbook"};e.Workbook&&e.Workbook.WBProps&&(Ri.forEach((function(t){null!=e.Workbook.WBProps[t[0]]&&e.Workbook.WBProps[t[0]]!=t[1]&&(n[t[0]]=e.Workbook.WBProps[t[0]])})),e.Workbook.WBProps.CodeName&&(n.codeName=e.Workbook.WBProps.CodeName,delete n.CodeName)),t[t.length]=at("workbookPr",null,n);var a=e.Workbook&&e.Workbook.Sheets||[],i=0;if(a&&a[0]&&a[0].Hidden){for(t[t.length]="<bookViews>",i=0;i!=e.SheetNames.length&&a[i]&&a[i].Hidden;++i);i==e.SheetNames.length&&(i=0),t[t.length]='<workbookView firstSheet="'+i+'" activeTab="'+i+'"/>',t[t.length]="</bookViews>"}for(t[t.length]="<sheets>",i=0;i!=e.SheetNames.length;++i){var s={name:ze(e.SheetNames[i].slice(0,31))};if(s.sheetId=""+(i+1),s["r:id"]="rId"+(i+1),a[i])switch(a[i].Hidden){case 1:s.state="hidden";break;case 2:s.state="veryHidden"}t[t.length]=at("sheet",null,s)}return t[t.length]="</sheets>",r&&(t[t.length]="<definedNames>",e.Workbook&&e.Workbook.Names&&e.Workbook.Names.forEach((function(e){var r={name:e.Name};e.Comment&&(r.comment=e.Comment),null!=e.Sheet&&(r.localSheetId=""+e.Sheet),e.Hidden&&(r.hidden="1"),e.Ref&&(t[t.length]=at("definedName",ze(e.Ref),r))})),t[t.length]="</definedNames>"),t.length>2&&(t[t.length]="</workbook>",t[1]=t[1].replace("/>",">")),t.join("")}function Pi(e,t){if(t.Workbook&&t.Workbook.Sheets){for(var r,n,a=t.Workbook.Sheets,i=0,s=-1,o=-1;i<a.length;++i)!a[i]||!a[i].Hidden&&-1==s?s=i:1==a[i].Hidden&&-1==o&&(o=i);if(!(o>s))$t(e,135),$t(e,158,(r=s,n||(n=jt(29)),n.write_shift(-4,0),n.write_shift(-4,460),n.write_shift(4,28800),n.write_shift(4,17600),n.write_shift(4,500),n.write_shift(4,r),n.write_shift(4,r),n.write_shift(1,120),n.length>n.l?n.slice(0,n.l):n)),$t(e,136)}}function Li(t,r){var n=zt();return $t(n,131),$t(n,128,function(t,r){r||(r=jt(127));for(var n=0;4!=n;++n)r.write_shift(4,0);return pr("SheetJS",r),pr(e.version,r),pr(e.version,r),pr("7262",r),r.length>r.l?r.slice(0,r.l):r}()),$t(n,153,function(e,t){t||(t=jt(72));var r=0;return e&&e.filterPrivacy&&(r|=8),t.write_shift(4,r),t.write_shift(4,0),Ar(e&&e.CodeName||"ThisWorkbook",t),t.slice(0,t.l)}(t.Workbook&&t.Workbook.WBProps||null)),Pi(n,t),function(e,t){$t(e,143);for(var r=0;r!=t.SheetNames.length;++r){$t(e,156,(n={Hidden:t.Workbook&&t.Workbook.Sheets&&t.Workbook.Sheets[r]&&t.Workbook.Sheets[r].Hidden||0,iTabID:r+1,strRelID:"rId"+(r+1),name:t.SheetNames[r]},(a=void 0)||(a=jt(127)),a.write_shift(4,n.Hidden),a.write_shift(4,n.iTabID),Cr(n.strRelID,a),pr(n.name.slice(0,31),a),a.length>a.l?a.slice(0,a.l):a))}var n,a;$t(e,144)}(n,t),$t(n,132),n.end()}function Mi(e,t,r,n,a){return(".bin"===t.slice(-4)?Ci:di)(e,r,n,a)}function Fi(e,t,r){return(".bin"===t.slice(-4)?Ea:va)(e)}function Ui(e,t){var r=[];return e.Props&&r.push(function(e,t){var r=[];return we(sn).map((function(e){for(var t=0;t<qr.length;++t)if(qr[t][1]==e)return qr[t];for(t=0;t<tn.length;++t)if(tn[t][1]==e)return tn[t];throw e})).forEach((function(n){if(null!=e[n[1]]){var a=t&&t.Props&&null!=t.Props[n[1]]?t.Props[n[1]]:e[n[1]];"date"===n[2]&&(a=new Date(a).toISOString().replace(/\.\d*Z/,"Z")),"number"==typeof a?a=String(a):!0===a||!1===a?a=a?"1":"0":a instanceof Date&&(a=new Date(a).toISOString().replace(/\.\d*Z/,"")),r.push(rt(sn[n[1]]||n[1],a))}})),at("DocumentProperties",r.join(""),{xmlns:lt.o})}(e.Props,t)),e.Custprops&&r.push(function(e,t){var r=["Worksheets","SheetNames"],n="CustomDocumentProperties",a=[];return e&&we(e).forEach((function(t){if(Object.prototype.hasOwnProperty.call(e,t)){for(var n=0;n<qr.length;++n)if(t==qr[n][1])return;for(n=0;n<tn.length;++n)if(t==tn[n][1])return;for(n=0;n<r.length;++n)if(t==r[n])return;var i=e[t],s="string";"number"==typeof i?(s="float",i=String(i)):!0===i||!1===i?(s="boolean",i=i?"1":"0"):i=String(i),a.push(at($e(t),i,{"dt:dt":s}))}})),t&&we(t).forEach((function(r){if(Object.prototype.hasOwnProperty.call(t,r)&&(!e||!Object.prototype.hasOwnProperty.call(e,r))){var n=t[r],i="string";"number"==typeof n?(i="float",n=String(n)):!0===n||!1===n?(i="boolean",n=n?"1":"0"):n instanceof Date?(i="dateTime.tz",n=n.toISOString()):n=String(n),a.push(at($e(r),n,{"dt:dt":i}))}})),"<"+n+' xmlns="'+lt.o+'">'+a.join("")+"</"+n+">"}(e.Props,e.Custprops)),r.join("")}function Bi(e){return at("NamedRange",null,{"ss:Name":e.Name,"ss:RefersTo":"="+_a(e.Ref,{r:0,c:0})})}function Wi(e,t,r,n,a,i,s){if(!e||null==e.v&&null==e.f)return"";var o={};if(e.f&&(o["ss:Formula"]="="+ze(_a(e.f,s))),e.F&&e.F.slice(0,t.length)==t){var l=tr(e.F.slice(t.length+1));o["ss:ArrayRange"]="RC:R"+(l.r==s.r?"":"["+(l.r-s.r)+"]")+"C"+(l.c==s.c?"":"["+(l.c-s.c)+"]")}if(e.l&&e.l.Target&&(o["ss:HRef"]=ze(e.l.Target),e.l.Tooltip&&(o["x:HRefScreenTip"]=ze(e.l.Tooltip))),r["!merges"])for(var f=r["!merges"],c=0;c!=f.length;++c)f[c].s.c==s.c&&f[c].s.r==s.r&&(f[c].e.c>f[c].s.c&&(o["ss:MergeAcross"]=f[c].e.c-f[c].s.c),f[c].e.r>f[c].s.r&&(o["ss:MergeDown"]=f[c].e.r-f[c].s.r));var h="",u="";switch(e.t){case"z":if(!n.sheetStubs)return"";break;case"n":h="Number",u=String(e.v);break;case"b":h="Boolean",u=e.v?"1":"0";break;case"e":h="Error",u=Gr[e.v];break;case"d":h="DateTime",u=new Date(e.v).toISOString(),null==e.z&&(e.z=e.z||k[14]);break;case"s":h="String",u=((e.v||"")+"").replace(Ve,(function(e){return Ge[e]})).replace(Xe,(function(e){return"&#x"+e.charCodeAt(0).toString(16).toUpperCase()+";"}))}var p=fi(n.cellXfs,e,n);o["ss:StyleID"]="s"+(21+p),o["ss:Index"]=s.c+1;var d=null!=e.v?u:"",m="z"==e.t?"":'<Data ss:Type="'+h+'">'+d+"</Data>";return(e.c||[]).length>0&&(m+=e.c.map((function(e){var t=at("ss:Data",(e.t||"").replace(/(\r\n|[\r\n])/g,"&#10;"),{xmlns:"http://www.w3.org/TR/REC-html40"});return at("Comment",t,{"ss:Author":e.a})})).join("")),at("Cell",m,o)}function Hi(e,t){var r='<Row ss:Index="'+(e+1)+'"';return t&&(t.hpt&&!t.hpx&&(t.hpx=Zn(t.hpt)),t.hpx&&(r+=' ss:AutoFitHeight="0" ss:Height="'+t.hpx+'"'),t.hidden&&(r+=' ss:Hidden="1"')),r+">"}function Gi(e,t,r){var n=[],a=r.SheetNames[e],i=r.Sheets[a],s=i?function(e,t,r,n){if(!e)return"";if(!((n||{}).Workbook||{}).Names)return"";for(var a=n.Workbook.Names,i=[],s=0;s<a.length;++s){var o=a[s];o.Sheet==r&&(o.Name.match(/^_xlfn\./)||i.push(Bi(o)))}return i.join("")}(i,0,e,r):"";return s.length>0&&n.push("<Names>"+s+"</Names>"),s=i?function(e,t,r,n){if(!e["!ref"])return"";var a=ir(e["!ref"]),i=e["!merges"]||[],s=0,o=[];e["!cols"]&&e["!cols"].forEach((function(e,t){Yn(e);var r=!!e.width,n=oi(t,e),a={"ss:Index":t+1};r&&(a["ss:Width"]=zn(n.width)),e.hidden&&(a["ss:Hidden"]="1"),o.push(at("Column",null,a))}));for(var l=Array.isArray(e),f=a.s.r;f<=a.e.r;++f){for(var c=[Hi(f,(e["!rows"]||[])[f])],h=a.s.c;h<=a.e.c;++h){var u=!1;for(s=0;s!=i.length;++s)if(!(i[s].s.c>h||i[s].s.r>f||i[s].e.c<h||i[s].e.r<f)){i[s].s.c==h&&i[s].s.r==f||(u=!0);break}if(!u){var p={r:f,c:h},d=rr(p),m=l?(e[f]||[])[h]:e[d];c.push(Wi(m,d,e,t,0,0,p))}}c.push("</Row>"),c.length>2&&o.push(c.join(""))}return o.join("")}(i,t):"",s.length>0&&n.push("<Table>"+s+"</Table>"),n.push(function(e,t,r,n){if(!e)return"";var a=[];if(e["!margins"]&&(a.push("<PageSetup>"),e["!margins"].header&&a.push(at("Header",null,{"x:Margin":e["!margins"].header})),e["!margins"].footer&&a.push(at("Footer",null,{"x:Margin":e["!margins"].footer})),a.push(at("PageMargins",null,{"x:Bottom":e["!margins"].bottom||"0.75","x:Left":e["!margins"].left||"0.7","x:Right":e["!margins"].right||"0.7","x:Top":e["!margins"].top||"0.75"})),a.push("</PageSetup>")),n&&n.Workbook&&n.Workbook.Sheets&&n.Workbook.Sheets[r])if(n.Workbook.Sheets[r].Hidden)a.push(at("Visible",1==n.Workbook.Sheets[r].Hidden?"SheetHidden":"SheetVeryHidden",{}));else{for(var i=0;i<r&&(!n.Workbook.Sheets[i]||n.Workbook.Sheets[i].Hidden);++i);i==r&&a.push("<Selected/>")}return((((n||{}).Workbook||{}).Views||[])[0]||{}).RTL&&a.push("<DisplayRightToLeft/>"),e["!protect"]&&(a.push(rt("ProtectContents","True")),e["!protect"].objects&&a.push(rt("ProtectObjects","True")),e["!protect"].scenarios&&a.push(rt("ProtectScenarios","True")),null==e["!protect"].selectLockedCells||e["!protect"].selectLockedCells?null==e["!protect"].selectUnlockedCells||e["!protect"].selectUnlockedCells||a.push(rt("EnableSelection","UnlockedCells")):a.push(rt("EnableSelection","NoSelection")),[["formatCells","AllowFormatCells"],["formatColumns","AllowSizeCols"],["formatRows","AllowSizeRows"],["insertColumns","AllowInsertCols"],["insertRows","AllowInsertRows"],["insertHyperlinks","AllowInsertHyperlinks"],["deleteColumns","AllowDeleteCols"],["deleteRows","AllowDeleteRows"],["sort","AllowSort"],["autoFilter","AllowFilter"],["pivotTables","AllowUsePivotTables"]].forEach((function(t){e["!protect"][t[0]]&&a.push("<"+t[1]+"/>")}))),0==a.length?"":at("WorksheetOptions",a.join(""),{xmlns:lt.x})}(i,0,e,r)),n.join("")}function Vi(e,t){t||(t={}),e.SSF||(e.SSF=Pe(k)),e.SSF&&(pe(),ue(e.SSF),t.revssf=Se(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF,t.cellXfs=[],fi(t.cellXfs,{},{revssf:{General:0}}));var r=[];r.push(Ui(e,t)),r.push(""),r.push(""),r.push("");for(var n=0;n<e.SheetNames.length;++n)r.push(at("Worksheet",Gi(n,t,e),{"ss:Name":ze(e.SheetNames[n])}));return r[2]=function(e,t){var r=['<Style ss:ID="Default" ss:Name="Normal"><NumberFormat/></Style>'];return t.cellXfs.forEach((function(e,t){var n=[];n.push(at("NumberFormat",null,{"ss:Format":ze(k[e.numFmtId])}));var a={"ss:ID":"s"+(21+t)};r.push(at("Style",n.join(""),a))})),at("Styles",r.join(""))}(0,t),r[3]=function(e){if(!((e||{}).Workbook||{}).Names)return"";for(var t=e.Workbook.Names,r=[],n=0;n<t.length;++n){var a=t[n];null==a.Sheet&&(a.Name.match(/^_xlfn\./)||r.push(Bi(a)))}return at("Names",r.join(""))}(e),He+at("Workbook",r.join(""),{xmlns:lt.ss,"xmlns:o":lt.o,"xmlns:x":lt.x,"xmlns:ss":lt.ss,"xmlns:dt":lt.dt,"xmlns:html":lt.html})}var ji={SI:"e0859ff2f94f6810ab9108002b27b3d9",DSI:"02d5cdd59c2e1b10939708002b2cf9ae",UDI:"05d5cdd59c2e1b10939708002b2cf9ae"};function zi(e,t){var r=t||{},n=ge.utils.cfb_new({root:"R"}),a="/Workbook";switch(r.bookType||"xls"){case"xls":r.bookType="biff8";case"xla":r.bookType||(r.bookType="xla");case"biff8":a="/Workbook",r.biff=8;break;case"biff5":a="/Book",r.biff=5;break;default:throw new Error("invalid type "+r.bookType+" for XLS CFB")}return ge.utils.cfb_add(n,a,rs(e,r)),8==r.biff&&(e.Props||e.Custprops)&&function(e,t){var r,n=[],a=[],i=[],s=0,o=Ee(Ur,"n"),l=Ee(Br,"n");if(e.Props)for(r=we(e.Props),s=0;s<r.length;++s)(Object.prototype.hasOwnProperty.call(o,r[s])?n:Object.prototype.hasOwnProperty.call(l,r[s])?a:i).push([r[s],e.Props[r[s]]]);if(e.Custprops)for(r=we(e.Custprops),s=0;s<r.length;++s)Object.prototype.hasOwnProperty.call(e.Props||{},r[s])||(Object.prototype.hasOwnProperty.call(o,r[s])?n:Object.prototype.hasOwnProperty.call(l,r[s])?a:i).push([r[s],e.Custprops[r[s]]]);var f=[];for(s=0;s<i.length;++s)ln.indexOf(i[s][0])>-1||rn.indexOf(i[s][0])>-1||null!=i[s][1]&&f.push(i[s]);a.length&&ge.utils.cfb_add(t,"/SummaryInformation",hn(a,ji.SI,l,Br)),(n.length||f.length)&&ge.utils.cfb_add(t,"/DocumentSummaryInformation",hn(n,ji.DSI,o,Ur,f.length?f:null,ji.UDI))}(e,n),8==r.biff&&e.vbaraw&&function(e,t){t.FullPaths.forEach((function(r,n){if(0!=n){var a=r.replace(/[^\/]*[\/]/,"/_VBA_PROJECT_CUR/");"/"!==a.slice(-1)&&ge.utils.cfb_add(e,a,t.FileIndex[n].content)}}))}(n,ge.read(e.vbaraw,{type:"string"==typeof e.vbaraw?"binary":"buffer"})),n}var $i={0:{f:function(e,t){var r={},n=e.l+t;r.r=e.read_shift(4),e.l+=4;var a=e.read_shift(2);e.l+=1;var i=e.read_shift(1);return e.l=n,7&i&&(r.level=7&i),16&i&&(r.hidden=!0),32&i&&(r.hpt=a/20),r}},1:{f:function(e){return[Tr(e)]}},2:{f:function(e){return[Tr(e),Rr(e),"n"]}},3:{f:function(e){return[Tr(e),e.read_shift(1),"e"]}},4:{f:function(e){return[Tr(e),e.read_shift(1),"b"]}},5:{f:function(e){return[Tr(e),Pr(e),"n"]}},6:{f:function(e){return[Tr(e),ur(e),"str"]}},7:{f:function(e){return[Tr(e),e.read_shift(4),"s"]}},8:{f:function(e,t,r){var n=e.l+t,a=Tr(e);a.r=r["!row"];var i=[a,ur(e),"str"];if(r.cellFormula){e.l+=2;var s=Qa(e,n-e.l,r);i[3]=Ka(s,0,a,r.supbooks,r)}else e.l=n;return i}},9:{f:function(e,t,r){var n=e.l+t,a=Tr(e);a.r=r["!row"];var i=[a,Pr(e),"n"];if(r.cellFormula){e.l+=2;var s=Qa(e,n-e.l,r);i[3]=Ka(s,0,a,r.supbooks,r)}else e.l=n;return i}},10:{f:function(e,t,r){var n=e.l+t,a=Tr(e);a.r=r["!row"];var i=[a,e.read_shift(1),"b"];if(r.cellFormula){e.l+=2;var s=Qa(e,n-e.l,r);i[3]=Ka(s,0,a,r.supbooks,r)}else e.l=n;return i}},11:{f:function(e,t,r){var n=e.l+t,a=Tr(e);a.r=r["!row"];var i=[a,e.read_shift(1),"e"];if(r.cellFormula){e.l+=2;var s=Qa(e,n-e.l,r);i[3]=Ka(s,0,a,r.supbooks,r)}else e.l=n;return i}},12:{f:function(e){return[Er(e)]}},13:{f:function(e){return[Er(e),Rr(e),"n"]}},14:{f:function(e){return[Er(e),e.read_shift(1),"e"]}},15:{f:function(e){return[Er(e),e.read_shift(1),"b"]}},16:{f:function(e){return[Er(e),Pr(e),"n"]}},17:{f:function(e){return[Er(e),ur(e),"str"]}},18:{f:function(e){return[Er(e),e.read_shift(4),"s"]}},19:{f:mr},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:function(e,t,r){var n=e.l+t;e.l+=4,e.l+=1;var a=e.read_shift(4),i=Or(e),s=ei(e,0,r),o=_r(e);e.l=n;var l={Name:i,Ptg:s};return a<268435455&&(l.Sheet=a),o&&(l.Comment=o),l}},40:{},42:{},43:{f:function(e,t,r){var n={};n.sz=e.read_shift(2)/20;var a=function(e){var t=e.read_shift(1);return e.l++,{fBold:1&t,fItalic:2&t,fUnderline:4&t,fStrikeout:8&t,fOutline:16&t,fShadow:32&t,fCondense:64&t,fExtend:128&t}}(e);switch(a.fItalic&&(n.italic=1),a.fCondense&&(n.condense=1),a.fExtend&&(n.extend=1),a.fShadow&&(n.shadow=1),a.fOutline&&(n.outline=1),a.fStrikeout&&(n.strike=1),700===e.read_shift(2)&&(n.bold=1),e.read_shift(2)){case 1:n.vertAlign="superscript";break;case 2:n.vertAlign="subscript"}var i=e.read_shift(1);0!=i&&(n.underline=i);var s=e.read_shift(1);s>0&&(n.family=s);var o=e.read_shift(1);switch(o>0&&(n.charset=o),e.l++,n.color=function(e){var t={},r=e.read_shift(1)>>>1,n=e.read_shift(1),a=e.read_shift(2,"i"),i=e.read_shift(1),s=e.read_shift(1),o=e.read_shift(1);switch(e.l++,r){case 0:t.auto=1;break;case 1:t.index=n;var l=Hr[n];l&&(t.rgb=Vn(l));break;case 2:t.rgb=Vn([i,s,o]);break;case 3:t.theme=n}return 0!=a&&(t.tint=a>0?a/32767:a/32768),t}(e),e.read_shift(1)){case 1:n.scheme="major";break;case 2:n.scheme="minor"}return n.name=ur(e),n}},44:{f:function(e,t){return[e.read_shift(2),ur(e)]}},45:{f:na},46:{f:oa},47:{f:function(e,t){var r=e.l+t,n=e.read_shift(2),a=e.read_shift(2);return e.l=r,{ixfe:n,numFmtId:a}}},48:{},49:{f:function(e){return e.read_shift(4,"i")}},50:{},51:{f:function(e){for(var t=[],r=e.read_shift(4);r-- >0;)t.push([e.read_shift(4),e.read_shift(4)]);return t}},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:function(e,t,r){if(!r.cellStyles)return Vt(e,t);var n=r&&r.biff>=12?4:2,a=e.read_shift(n),i=e.read_shift(n),s=e.read_shift(n),o=e.read_shift(n),l=e.read_shift(2);2==n&&(e.l+=2);var f={s:a,e:i,w:s,ixfe:o,flags:l};return(r.biff>=5||!r.biff)&&(f.level=l>>8&7),f}},62:{f:function(e){return[Tr(e),mr(e),"is"]}},63:{f:function(e){var t={};t.i=e.read_shift(4);var r={};r.r=e.read_shift(4),r.c=e.read_shift(4),t.r=rr(r);var n=e.read_shift(1);return 2&n&&(t.l="1"),8&n&&(t.a="1"),t}},64:{f:function(){}},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:Vt,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:function(e){var t=e.read_shift(2);return e.l+=28,{RTL:32&t}}},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:function(e,t){var r={},n=e[e.l];return++e.l,r.above=!(64&n),r.left=!(128&n),e.l+=18,r.name=Sr(e),r}},148:{f:gi,p:16},151:{f:function(){}},152:{},153:{f:function(e,t){var r={},n=e.read_shift(4);r.defaultThemeVersion=e.read_shift(4);var a=t>8?ur(e):"";return a.length>0&&(r.CodeName=a),r.autoCompressPictures=!!(65536&n),r.backupFile=!!(64&n),r.checkCompatibility=!!(4096&n),r.date1904=!!(1&n),r.filterPrivacy=!!(8&n),r.hidePivotFieldList=!!(1024&n),r.promptedSolutions=!!(16&n),r.publishItems=!!(2048&n),r.refreshAllConnections=!!(262144&n),r.saveExternalLinkValues=!!(128&n),r.showBorderUnselectedTables=!!(4&n),r.showInkAnnotation=!!(32&n),r.showObjects=["all","placeholders","none"][n>>13&3],r.showPivotChartFilter=!!(32768&n),r.updateLinks=["userSet","never","always"][n>>8&3],r}},154:{},155:{},156:{f:function(e,t){var r={};return r.Hidden=e.read_shift(4),r.iTabID=e.read_shift(4),r.strRelID=xr(e),r.name=ur(e),r}},157:{},158:{},159:{T:1,f:function(e){return[e.read_shift(4),e.read_shift(4)]}},160:{T:-1},161:{T:1,f:Ir},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:Ti},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:function(e,t){return{flags:e.read_shift(4),version:e.read_shift(4),name:ur(e)}}},336:{T:-1},337:{f:function(e){return e.l+=4,0!=e.read_shift(4)},T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:xr},357:{},358:{},359:{},360:{T:1},361:{},362:{f:function(e,t,r){if(r.biff<8)return function(e,t,r){3==e[e.l+1]&&e[e.l]++;var n=gn(e,0,r);return 3==n.charCodeAt(0)?n.slice(1):n}(e,0,r);for(var n=[],a=e.l+t,i=e.read_shift(r.biff>8?4:2);0!=i--;)n.push(Sn(e,r.biff,r));if(e.l!=a)throw new Error("Bad ExternSheet: "+e.l+" != "+a);return n}},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:function(e,t,r){var n=e.l+t,a=kr(e),i=e.read_shift(1),s=[a];if(s[2]=i,r.cellFormula){var o=qa(e,n-e.l,r);s[1]=o}else e.l=n;return s}},427:{f:function(e,t,r){var n=e.l+t,a=[Ir(e)];if(r.cellFormula){var i=ti(e,n-e.l,r);a[1]=i,e.l=n}else e.l=n;return a}},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:function(e){var t={};return Ei.forEach((function(r){t[r]=Pr(e)})),t}},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:function(){}},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:function(e,t){var r=e.l+t,n=Ir(e),a=_r(e),i=ur(e),s=ur(e),o=ur(e);e.l=r;var l={rfx:n,relId:a,loc:i,display:o};return s&&(l.Tooltip=s),l}},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:xr},551:{},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:wa},633:{T:1},634:{T:-1},635:{T:1,f:function(e){var t={};t.iauthor=e.read_shift(4);var r=Ir(e);return t.rfx=r.s,t.ref=rr(r.s),e.l+=16,t}},636:{T:-1},637:{f:gr},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:function(e,t){return e.l+=10,{name:ur(e)}}},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:function(){}},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}};function Xi(e,t,r,n){var a=t;if(!isNaN(a)){var i=n||(r||[]).length||0,s=e.next(4);s.write_shift(2,a),s.write_shift(2,i),i>0&&Rt(r)&&e.push(r)}}function Yi(e,t,r){return e||(e=jt(7)),e.write_shift(2,t),e.write_shift(2,r),e.write_shift(2,0),e.write_shift(1,0),e}function Ki(e,t,r,n){if(null!=t.v)switch(t.t){case"d":case"n":var a="d"==t.t?_e(Ie(t.v)):t.v;return void(a==(0|a)&&a>=0&&a<65536?Xi(e,2,(i=r,s=n,o=a,l=jt(9),Yi(l,i,s),l.write_shift(2,o),l)):Xi(e,3,function(e,t,r){var n=jt(15);return Yi(n,e,t),n.write_shift(8,r,"f"),n}(r,n,a)));case"b":case"e":return void Xi(e,5,function(e,t,r,n){var a=jt(9);return Yi(a,e,t),mn(r,n||"b",a),a}(r,n,t.v,t.t));case"s":case"str":return void Xi(e,4,function(e,t,r){var n=jt(8+2*r.length);return Yi(n,e,t),n.write_shift(1,r.length),n.write_shift(r.length,r,"sbcs"),n.l<n.length?n.slice(0,n.l):n}(r,n,(t.v||"").slice(0,255)))}var i,s,o,l;Xi(e,1,Yi(null,r,n))}function Ji(e,t){for(var r=t||{},n=zt(),a=0,i=0;i<e.SheetNames.length;++i)e.SheetNames[i]==r.sheet&&(a=i);if(0==a&&r.sheet&&e.SheetNames[0]!=r.sheet)throw new Error("Sheet not found: "+r.sheet);return Xi(n,4==r.biff?1033:3==r.biff?521:9,yn(0,16,r)),function(e,t,r,n){var a,i=Array.isArray(t),s=ir(t["!ref"]||"A1"),o="",l=[];if(s.e.c>255||s.e.r>16383){if(n.WTF)throw new Error("Range "+(t["!ref"]||"A1")+" exceeds format limit A1:IV16384");s.e.c=Math.min(s.e.c,255),s.e.r=Math.min(s.e.c,16383),a=ar(s)}for(var f=s.s.r;f<=s.e.r;++f){o=qt(f);for(var c=s.s.c;c<=s.e.c;++c){f===s.s.r&&(l[c]=er(c)),a=l[c]+o;var h=i?(t[f]||[])[c]:t[a];h&&Ki(e,h,f,c)}}}(n,e.Sheets[e.SheetNames[a]],0,r),Xi(n,10),n.end()}function Zi(e,t,r){Xi(e,49,function(e,t){var r=e.name||"Arial",n=t&&5==t.biff,a=jt(n?15+r.length:16+2*r.length);return a.write_shift(2,20*(e.sz||12)),a.write_shift(4,0),a.write_shift(2,400),a.write_shift(4,0),a.write_shift(2,0),a.write_shift(1,r.length),n||a.write_shift(1,1),a.write_shift((n?1:2)*r.length,r,n?"sbcs":"utf16le"),a}({sz:12,color:{theme:1},name:"Arial",family:2,scheme:"minor"},r))}function qi(e,t){if(t){var r=0;t.forEach((function(t,n){++r<=256&&t&&Xi(e,125,function(e,t){var r=jt(12);r.write_shift(2,t),r.write_shift(2,t),r.write_shift(2,256*e.width),r.write_shift(2,0);var n=0;return e.hidden&&(n|=1),r.write_shift(1,n),n=e.level||0,r.write_shift(1,n),r.write_shift(2,0),r}(oi(n,t),n))}))}}function Qi(e,t,r,n,a){var i=16+fi(a.cellXfs,t,a);if(null!=t.v||t.bf)if(t.bf)Xi(e,6,Ja(t,r,n,0,i));else switch(t.t){case"d":case"n":Xi(e,515,function(e,t,r,n){var a=jt(14);return bn(e,t,n,a),Lr(r,a),a}(r,n,"d"==t.t?_e(Ie(t.v)):t.v,i));break;case"b":case"e":Xi(e,517,function(e,t,r,n,a,i){var s=jt(8);return bn(e,t,n,s),mn(r,i,s),s}(r,n,t.v,i,0,t.t));break;case"s":case"str":if(a.bookSST)Xi(e,253,function(e,t,r,n){var a=jt(10);return bn(e,t,n,a),a.write_shift(4,r),a}(r,n,si(a.Strings,t.v,a.revStrings),i));else Xi(e,516,function(e,t,r,n,a){var i=!a||8==a.biff,s=jt(+i+8+(1+i)*r.length);return bn(e,t,n,s),s.write_shift(2,r.length),i&&s.write_shift(1,1),s.write_shift((1+i)*r.length,r,i?"utf16le":"sbcs"),s}(r,n,(t.v||"").slice(0,255),i,a));break;default:Xi(e,513,bn(r,n,i))}else Xi(e,513,bn(r,n,i))}function es(e,t,r){var n,a,i,s=zt(),o=r.SheetNames[e],l=r.Sheets[o]||{},f=(r||{}).Workbook||{},c=(f.Sheets||[])[e]||{},h=Array.isArray(l),u=8==t.biff,p="",d=[],m=ir(l["!ref"]||"A1"),g=u?65536:16384;if(m.e.c>255||m.e.r>=g){if(t.WTF)throw new Error("Range "+(l["!ref"]||"A1")+" exceeds format limit A1:IV16384");m.e.c=Math.min(m.e.c,255),m.e.r=Math.min(m.e.c,g-1)}Xi(s,2057,yn(0,16,t)),Xi(s,13,dn(1)),Xi(s,12,dn(100)),Xi(s,15,un(!0)),Xi(s,17,un(!1)),Xi(s,16,Lr(.001)),Xi(s,95,un(!0)),Xi(s,42,un(!1)),Xi(s,43,un(!1)),Xi(s,130,dn(1)),Xi(s,128,(a=[0,0],(i=jt(8)).write_shift(4,0),i.write_shift(2,a[0]?a[0]+1:0),i.write_shift(2,a[1]?a[1]+1:0),i)),Xi(s,131,un(!1)),Xi(s,132,un(!1)),u&&qi(s,l["!cols"]),Xi(s,512,function(e,t){var r=8!=t.biff&&t.biff?2:4,n=jt(2*r+6);return n.write_shift(r,e.s.r),n.write_shift(r,e.e.r+1),n.write_shift(2,e.s.c),n.write_shift(2,e.e.c+1),n.write_shift(2,0),n}(m,t)),u&&(l["!links"]=[]);for(var v=m.s.r;v<=m.e.r;++v){p=qt(v);for(var T=m.s.c;T<=m.e.c;++T){v===m.s.r&&(d[T]=er(T)),n=d[T]+p;var w=h?(l[v]||[])[T]:l[n];w&&(Qi(s,w,v,T,t),u&&w.l&&l["!links"].push([n,w.l]))}}var E=c.CodeName||c.name||o;return u&&Xi(s,574,function(e){var t=jt(18),r=1718;return e&&e.RTL&&(r|=64),t.write_shift(2,r),t.write_shift(4,0),t.write_shift(4,64),t.write_shift(4,0),t.write_shift(4,0),t}((f.Views||[])[0])),u&&(l["!merges"]||[]).length&&Xi(s,229,function(e){var t=jt(2+8*e.length);t.write_shift(2,e.length);for(var r=0;r<e.length;++r)_n(e[r],t);return t}(l["!merges"])),u&&function(e,t){for(var r=0;r<t["!links"].length;++r){var n=t["!links"][r];Xi(e,440,Rn(n)),n[1].Tooltip&&Xi(e,2048,Nn(n))}delete t["!links"]}(s,l),Xi(s,442,Tn(E)),u&&function(e,t){var r=jt(19);r.write_shift(4,2151),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,1),r.write_shift(4,0),Xi(e,2151,r),(r=jt(39)).write_shift(4,2152),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,0),r.write_shift(4,0),r.write_shift(2,1),r.write_shift(4,4),r.write_shift(2,0),_n(ir(t["!ref"]||"A1"),r),r.write_shift(4,4),Xi(e,2152,r)}(s,l),Xi(s,10),s.end()}function ts(e,t,r){var n,a=zt(),i=(e||{}).Workbook||{},s=i.Sheets||[],o=i.WBProps||{},l=8==r.biff,f=5==r.biff;(Xi(a,2057,yn(0,5,r)),"xla"==r.bookType&&Xi(a,135),Xi(a,225,l?dn(1200):null),Xi(a,193,function(e,t){t||(t=jt(e));for(var r=0;r<e;++r)t.write_shift(1,0);return t}(2)),f&&Xi(a,191),f&&Xi(a,192),Xi(a,226),Xi(a,92,function(e,t){var r=!t||8==t.biff,n=jt(r?112:54);for(n.write_shift(8==t.biff?2:1,7),r&&n.write_shift(1,0),n.write_shift(4,859007059),n.write_shift(4,5458548|(r?0:536870912));n.l<n.length;)n.write_shift(1,r?0:32);return n}(0,r)),Xi(a,66,dn(l?1200:1252)),l&&Xi(a,353,dn(0)),l&&Xi(a,448),Xi(a,317,function(e){for(var t=jt(2*e),r=0;r<e;++r)t.write_shift(2,r+1);return t}(e.SheetNames.length)),l&&e.vbaraw&&Xi(a,211),l&&e.vbaraw)&&Xi(a,442,Tn(o.CodeName||"ThisWorkbook"));Xi(a,156,dn(17)),Xi(a,25,un(!1)),Xi(a,18,un(!1)),Xi(a,19,dn(0)),l&&Xi(a,431,un(!1)),l&&Xi(a,444,dn(0)),Xi(a,61,((n=jt(18)).write_shift(2,0),n.write_shift(2,0),n.write_shift(2,29280),n.write_shift(2,17600),n.write_shift(2,56),n.write_shift(2,0),n.write_shift(2,0),n.write_shift(2,1),n.write_shift(2,500),n)),Xi(a,64,un(!1)),Xi(a,141,dn(0)),Xi(a,34,un("true"==function(e){return e.Workbook&&e.Workbook.WBProps&&function(e){switch(e){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}(e.Workbook.WBProps.date1904)?"true":"false"}(e))),Xi(a,14,un(!0)),l&&Xi(a,439,un(!1)),Xi(a,218,dn(0)),Zi(a,0,r),function(e,t,r){t&&[[5,8],[23,26],[41,44],[50,392]].forEach((function(n){for(var a=n[0];a<=n[1];++a)null!=t[a]&&Xi(e,1054,xn(a,t[a],r))}))}(a,e.SSF,r),function(e,t){for(var r=0;r<16;++r)Xi(e,224,Cn({numFmtId:0,style:!0},0,t));t.cellXfs.forEach((function(r){Xi(e,224,Cn(r,0,t))}))}(a,r),l&&Xi(a,352,un(!1));var c=a.end(),h=zt();l&&Xi(h,140,function(e){return e||(e=jt(4)),e.write_shift(2,1),e.write_shift(2,1),e}()),l&&r.Strings&&function(e,t,r,n){var a=n||(r||[]).length||0;if(a<=8224)return Xi(e,t,r,a);var i=t;if(!isNaN(i)){for(var s=r.parts||[],o=0,l=0,f=0;f+(s[o]||8224)<=8224;)f+=s[o]||8224,o++;var c=e.next(4);for(c.write_shift(2,i),c.write_shift(2,f),e.push(r.slice(l,l+f)),l+=f;l<a;){for((c=e.next(4)).write_shift(2,60),f=0;f+(s[o]||8224)<=8224;)f+=s[o]||8224,o++;c.write_shift(2,f),e.push(r.slice(l,l+f)),l+=f}}}(h,252,function(e,t){var r=jt(8);r.write_shift(4,e.Count),r.write_shift(4,e.Unique);for(var n=[],a=0;a<e.length;++a)n[a]=vn(e[a]);var i=w([r].concat(n));return i.parts=[r.length].concat(n.map((function(e){return e.length}))),i}(r.Strings)),Xi(h,10);var u=h.end(),p=zt(),d=0,m=0;for(m=0;m<e.SheetNames.length;++m)d+=(l?12:11)+(l?2:1)*e.SheetNames[m].length;var g=c.length+d+u.length;for(m=0;m<e.SheetNames.length;++m){Xi(p,133,On({pos:g,hs:(s[m]||{}).Hidden||0,dt:0,name:e.SheetNames[m]},r)),g+=t[m].length}var v=p.end();if(d!=v.length)throw new Error("BS8 "+d+" != "+v.length);var T=[];return c.length&&T.push(c),v.length&&T.push(v),u.length&&T.push(u),w(T)}function rs(e,t){for(var r=0;r<=e.SheetNames.length;++r){var n=e.Sheets[e.SheetNames[r]];if(n&&n["!ref"])nr(n["!ref"]).e.c>255&&"undefined"!=typeof console&&console.error}var a=t||{};switch(a.biff||2){case 8:case 5:return function(e,t){var r=t||{},n=[];e&&!e.SSF&&(e.SSF=Pe(k)),e&&e.SSF&&(pe(),ue(e.SSF),r.revssf=Se(e.SSF),r.revssf[e.SSF[65535]]=0,r.ssf=e.SSF),r.Strings=[],r.Strings.Count=0,r.Strings.Unique=0,Ns(r),r.cellXfs=[],fi(r.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={});for(var a=0;a<e.SheetNames.length;++a)n[n.length]=es(a,r,e);return n.unshift(ts(e,n,r)),w(n)}(e,t);case 4:case 3:case 2:return Ji(e,t)}throw new Error("invalid type "+a.bookType+" for BIFF")}function ns(e,t,r,n){for(var a=e["!merges"]||[],i=[],s=t.s.c;s<=t.e.c;++s){for(var o=0,l=0,f=0;f<a.length;++f)if(!(a[f].s.r>r||a[f].s.c>s||a[f].e.r<r||a[f].e.c<s)){if(a[f].s.r<r||a[f].s.c<s){o=-1;break}o=a[f].e.r-a[f].s.r+1,l=a[f].e.c-a[f].s.c+1;break}if(!(o<0)){var c=rr({r:r,c:s}),h=n.dense?(e[r]||[])[s]:e[c],u=h&&null!=h.v&&(h.h||((h.w||(or(h),h.w)||"")+"").replace(Ve,(function(e){return Ge[e]})).replace(/\n/g,"<br/>").replace(Xe,(function(e){return"&#x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+";"})))||"",p={};o>1&&(p.rowspan=o),l>1&&(p.colspan=l),n.editable?u='<span contenteditable="true">'+u+"</span>":h&&(p["data-t"]=h&&h.t||"z",null!=h.v&&(p["data-v"]=h.v),null!=h.z&&(p["data-z"]=h.z),h.l&&"#"!=(h.l.Target||"#").charAt(0)&&(u='<a href="'+h.l.Target+'">'+u+"</a>")),p.id=(n.id||"sjs")+"-"+c,i.push(at("td",u,p))}}return"<tr>"+i.join("")+"</tr>"}var as='<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',is="</body></html>";function ss(e,t){var r=t||{},n=null!=r.header?r.header:as,a=null!=r.footer?r.footer:is,i=[n],s=nr(e["!ref"]);r.dense=Array.isArray(e),i.push(function(e,t,r){return[].join("")+"<table"+(r&&r.id?' id="'+r.id+'"':"")+">"}(0,0,r));for(var o=s.s.r;o<=s.e.r;++o)i.push(ns(e,s,o,r));return i.push("</table>"+a),i.join("")}function os(e,t,r){var n=r||{},a=0,i=0;if(null!=n.origin)if("number"==typeof n.origin)a=n.origin;else{var s="string"==typeof n.origin?tr(n.origin):n.origin;a=s.r,i=s.c}var o=t.getElementsByTagName("tr"),l=Math.min(n.sheetRows||1e7,o.length),f={s:{r:0,c:0},e:{r:a,c:i}};if(e["!ref"]){var c=nr(e["!ref"]);f.s.r=Math.min(f.s.r,c.s.r),f.s.c=Math.min(f.s.c,c.s.c),f.e.r=Math.max(f.e.r,c.e.r),f.e.c=Math.max(f.e.c,c.e.c),-1==a&&(f.e.r=a=c.e.r+1)}var h=[],u=0,p=e["!rows"]||(e["!rows"]=[]),d=0,m=0,g=0,v=0,T=0,w=0;for(e["!cols"]||(e["!cols"]=[]);d<o.length&&m<l;++d){var E=o[d];if(fs(E)){if(n.display)continue;p[m]={hidden:!0}}var b=E.children;for(g=v=0;g<b.length;++g){var S=b[g];if(!n.display||!fs(S)){var A=S.hasAttribute("data-v")?S.getAttribute("data-v"):S.hasAttribute("v")?S.getAttribute("v"):et(S.innerHTML),_=S.getAttribute("data-z")||S.getAttribute("z");for(u=0;u<h.length;++u){var y=h[u];y.s.c==v+i&&y.s.r<m+a&&m+a<=y.e.r&&(v=y.e.c+1-i,u=-1)}w=+S.getAttribute("colspan")||1,((T=+S.getAttribute("rowspan")||1)>1||w>1)&&h.push({s:{r:m+a,c:v+i},e:{r:m+a+(T||1)-1,c:v+i+(w||1)-1}});var O={t:"s",v:A},x=S.getAttribute("data-t")||S.getAttribute("t")||"";null!=A&&(0==A.length?O.t=x||"z":n.raw||0==A.trim().length||"s"==x||("TRUE"===A?O={t:"b",v:!0}:"FALSE"===A?O={t:"b",v:!1}:isNaN(Me(A))?isNaN(Ue(A).getDate())||(O={t:"d",v:Ie(A)},n.cellDates||(O={t:"n",v:_e(O.v)}),O.z=n.dateNF||k[14]):O={t:"n",v:Me(A)})),void 0===O.z&&null!=_&&(O.z=_);var C="",R=S.getElementsByTagName("A");if(R&&R.length)for(var N=0;N<R.length&&(!R[N].hasAttribute("href")||"#"==(C=R[N].getAttribute("href")).charAt(0));++N);C&&"#"!=C.charAt(0)&&(O.l={Target:C}),n.dense?(e[m+a]||(e[m+a]=[]),e[m+a][v+i]=O):e[rr({c:v+i,r:m+a})]=O,f.e.c<v+i&&(f.e.c=v+i),v+=w}}++m}return h.length&&(e["!merges"]=(e["!merges"]||[]).concat(h)),f.e.r=Math.max(f.e.r,m-1+a),e["!ref"]=ar(f),m>=l&&(e["!fullref"]=ar((f.e.r=o.length-d+m-1+a,f))),e}function ls(e,t){return os((t||{}).dense?[]:{},e,t)}function fs(e){var t="",r=function(e){return e.ownerDocument.defaultView&&"function"==typeof e.ownerDocument.defaultView.getComputedStyle?e.ownerDocument.defaultView.getComputedStyle:"function"==typeof getComputedStyle?getComputedStyle:null}(e);return r&&(t=r(e).getPropertyValue("display")),t||(t=e.style&&e.style.display),"none"===t}var cs=function(){var e=["<office:master-styles>",'<style:master-page style:name="mp1" style:page-layout-name="mp1">',"<style:header/>",'<style:header-left style:display="false"/>',"<style:footer/>",'<style:footer-left style:display="false"/>',"</style:master-page>","</office:master-styles>"].join(""),t="<office:document-styles "+nt({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","office:version":"1.2"})+">"+e+"</office:document-styles>";return function(){return He+t}}(),hs=function(){var e="          <table:table-cell />\n",t=function(t,r,n){var a=[];a.push('      <table:table table:name="'+ze(r.SheetNames[n])+'" table:style-name="ta1">\n');var i=0,s=0,o=nr(t["!ref"]||"A1"),l=t["!merges"]||[],f=0,c=Array.isArray(t);if(t["!cols"])for(s=0;s<=o.e.c;++s)a.push("        <table:table-column"+(t["!cols"][s]?' table:style-name="co'+t["!cols"][s].ods+'"':"")+"></table:table-column>\n");var h="",u=t["!rows"]||[];for(i=0;i<o.s.r;++i)h=u[i]?' table:style-name="ro'+u[i].ods+'"':"",a.push("        <table:table-row"+h+"></table:table-row>\n");for(;i<=o.e.r;++i){for(h=u[i]?' table:style-name="ro'+u[i].ods+'"':"",a.push("        <table:table-row"+h+">\n"),s=0;s<o.s.c;++s)a.push(e);for(;s<=o.e.c;++s){var p=!1,d={},m="";for(f=0;f!=l.length;++f)if(!(l[f].s.c>s||l[f].s.r>i||l[f].e.c<s||l[f].e.r<i)){l[f].s.c==s&&l[f].s.r==i||(p=!0),d["table:number-columns-spanned"]=l[f].e.c-l[f].s.c+1,d["table:number-rows-spanned"]=l[f].e.r-l[f].s.r+1;break}if(p)a.push("          <table:covered-table-cell/>\n");else{var g=rr({r:i,c:s}),v=c?(t[i]||[])[s]:t[g];if(v&&v.f&&(d["table:formula"]=ze(("of:="+v.f.replace(Aa,"$1[.$2$3$4$5]").replace(/\]:\[/g,":")).replace(/;/g,"|").replace(/,/g,";")),v.F&&v.F.slice(0,g.length)==g)){var T=nr(v.F);d["table:number-matrix-columns-spanned"]=T.e.c-T.s.c+1,d["table:number-matrix-rows-spanned"]=T.e.r-T.s.r+1}if(v){switch(v.t){case"b":m=v.v?"TRUE":"FALSE",d["office:value-type"]="boolean",d["office:boolean-value"]=v.v?"true":"false";break;case"n":m=v.w||String(v.v||0),d["office:value-type"]="float",d["office:value"]=v.v||0;break;case"s":case"str":m=null==v.v?"":v.v,d["office:value-type"]="string";break;case"d":m=v.w||Ie(v.v).toISOString(),d["office:value-type"]="date",d["office:date-value"]=Ie(v.v).toISOString(),d["table:style-name"]="ce1";break;default:a.push(e);continue}var w=ze(m).replace(/  +/g,(function(e){return'<text:s text:c="'+e.length+'"/>'})).replace(/\t/g,"<text:tab/>").replace(/\n/g,"</text:p><text:p>").replace(/^ /,"<text:s/>").replace(/ $/,"<text:s/>");if(v.l&&v.l.Target){var E=v.l.Target;"#"==(E="#"==E.charAt(0)?"#"+E.slice(1).replace(/\./,"!"):E).charAt(0)||E.match(/^\w+:/)||(E="../"+E),w=at("text:a",w,{"xlink:href":E.replace(/&/g,"&amp;")})}a.push("          "+at("table:table-cell",at("text:p",w,{}),d)+"\n")}else a.push(e)}}a.push("        </table:table-row>\n")}return a.push("      </table:table>\n"),a.join("")};return function(e,r){var n=[He],a=nt({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:meta":"urn:oasis:names:tc:opendocument:xmlns:meta:1.0","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:presentation":"urn:oasis:names:tc:opendocument:xmlns:presentation:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:chart":"urn:oasis:names:tc:opendocument:xmlns:chart:1.0","xmlns:dr3d":"urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0","xmlns:math":"http://www.w3.org/1998/Math/MathML","xmlns:form":"urn:oasis:names:tc:opendocument:xmlns:form:1.0","xmlns:script":"urn:oasis:names:tc:opendocument:xmlns:script:1.0","xmlns:ooo":"http://openoffice.org/2004/office","xmlns:ooow":"http://openoffice.org/2004/writer","xmlns:oooc":"http://openoffice.org/2004/calc","xmlns:dom":"http://www.w3.org/2001/xml-events","xmlns:xforms":"http://www.w3.org/2002/xforms","xmlns:xsd":"http://www.w3.org/2001/XMLSchema","xmlns:xsi":"http://www.w3.org/2001/XMLSchema-instance","xmlns:sheet":"urn:oasis:names:tc:opendocument:sh33tjs:1.0","xmlns:rpt":"http://openoffice.org/2005/report","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","xmlns:xhtml":"http://www.w3.org/1999/xhtml","xmlns:grddl":"http://www.w3.org/2003/g/data-view#","xmlns:tableooo":"http://openoffice.org/2009/table","xmlns:drawooo":"http://openoffice.org/2010/draw","xmlns:calcext":"urn:org:documentfoundation:names:experimental:calc:xmlns:calcext:1.0","xmlns:loext":"urn:org:documentfoundation:names:experimental:office:xmlns:loext:1.0","xmlns:field":"urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0","xmlns:formx":"urn:openoffice:names:experimental:ooxml-odf-interop:xmlns:form:1.0","xmlns:css3t":"http://www.w3.org/TR/css3-text/","office:version":"1.2"}),i=nt({"xmlns:config":"urn:oasis:names:tc:opendocument:xmlns:config:1.0","office:mimetype":"application/vnd.oasis.opendocument.spreadsheet"});"fods"==r.bookType?(n.push("<office:document"+a+i+">\n"),n.push(Zr().replace(/office:document-meta/g,"office:meta"))):n.push("<office:document-content"+a+">\n"),function(e,t){e.push(" <office:automatic-styles>\n"),e.push('  <number:date-style style:name="N37" number:automatic-order="true">\n'),e.push('   <number:month number:style="long"/>\n'),e.push("   <number:text>/</number:text>\n"),e.push('   <number:day number:style="long"/>\n'),e.push("   <number:text>/</number:text>\n"),e.push("   <number:year/>\n"),e.push("  </number:date-style>\n");var r=0;t.SheetNames.map((function(e){return t.Sheets[e]})).forEach((function(t){if(t&&t["!cols"])for(var n=0;n<t["!cols"].length;++n)if(t["!cols"][n]){var a=t["!cols"][n];if(null==a.width&&null==a.wpx&&null==a.wch)continue;Yn(a),a.ods=r;var i=t["!cols"][n].wpx+"px";e.push('  <style:style style:name="co'+r+'" style:family="table-column">\n'),e.push('   <style:table-column-properties fo:break-before="auto" style:column-width="'+i+'"/>\n'),e.push("  </style:style>\n"),++r}}));var n=0;t.SheetNames.map((function(e){return t.Sheets[e]})).forEach((function(t){if(t&&t["!rows"])for(var r=0;r<t["!rows"].length;++r)if(t["!rows"][r]){t["!rows"][r].ods=n;var a=t["!rows"][r].hpx+"px";e.push('  <style:style style:name="ro'+n+'" style:family="table-row">\n'),e.push('   <style:table-row-properties fo:break-before="auto" style:row-height="'+a+'"/>\n'),e.push("  </style:style>\n"),++n}})),e.push('  <style:style style:name="ta1" style:family="table" style:master-page-name="mp1">\n'),e.push('   <style:table-properties table:display="true" style:writing-mode="lr-tb"/>\n'),e.push("  </style:style>\n"),e.push('  <style:style style:name="ce1" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N37"/>\n'),e.push(" </office:automatic-styles>\n")}(n,e),n.push("  <office:body>\n"),n.push("    <office:spreadsheet>\n");for(var s=0;s!=e.SheetNames.length;++s)n.push(t(e.Sheets[e.SheetNames[s]],e,s));return n.push("    </office:spreadsheet>\n"),n.push("  </office:body>\n"),"fods"==r.bookType?n.push("</office:document>"):n.push("</office:document-content>"),n.join("")}}();function us(e,t){if("fods"==t.bookType)return hs(e,t);var r=We(),n="",a=[],i=[];return Be(r,n="mimetype","application/vnd.oasis.opendocument.spreadsheet"),Be(r,n="content.xml",hs(e,t)),a.push([n,"text/xml"]),i.push([n,"ContentFile"]),Be(r,n="styles.xml",cs(e,t)),a.push([n,"text/xml"]),i.push([n,"StylesFile"]),Be(r,n="meta.xml",He+Zr()),a.push([n,"text/xml"]),i.push([n,"MetadataFile"]),Be(r,n="manifest.rdf",function(e){var t,r,n=[He];n.push('<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">\n');for(var a=0;a!=e.length;++a)n.push(Jr(e[a][0],e[a][1])),n.push((t="",r=e[a][0],['  <rdf:Description rdf:about="'+t+'">\n','    <ns0:hasPart xmlns:ns0="http://docs.oasis-open.org/ns/office/1.2/meta/pkg#" rdf:resource="'+r+'"/>\n',"  </rdf:Description>\n"].join("")));return n.push(Jr("","Document","pkg")),n.push("</rdf:RDF>"),n.join("")}(i)),a.push([n,"application/rdf+xml"]),Be(r,n="META-INF/manifest.xml",function(e){var t=[He];t.push('<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0" manifest:version="1.2">\n'),t.push('  <manifest:file-entry manifest:full-path="/" manifest:version="1.2" manifest:media-type="application/vnd.oasis.opendocument.spreadsheet"/>\n');for(var r=0;r<e.length;++r)t.push('  <manifest:file-entry manifest:full-path="'+e[r][0]+'" manifest:media-type="'+e[r][1]+'"/>\n');return t.push("</manifest:manifest>"),t.join("")}(a)),r}
/*! sheetjs (C) 2013-present SheetJS -- http://sheetjs.com */function ps(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function ds(e){return"undefined"!=typeof TextEncoder?(new TextEncoder).encode(e):g(Qe(e))}function ms(e){var t=e.reduce((function(e,t){return e+t.length}),0),r=new Uint8Array(t),n=0;return e.forEach((function(e){r.set(e,n),n+=e.length})),r}function gs(e,t){var r=t?t[0]:0,n=127&e[r];e:if(e[r++]>=128){if(n|=(127&e[r])<<7,e[r++]<128)break e;if(n|=(127&e[r])<<14,e[r++]<128)break e;if(n|=(127&e[r])<<21,e[r++]<128)break e;if(n+=(127&e[r])*Math.pow(2,28),++r,e[r++]<128)break e;if(n+=(127&e[r])*Math.pow(2,35),++r,e[r++]<128)break e;if(n+=(127&e[r])*Math.pow(2,42),++r,e[r++]<128)break e}return t&&(t[0]=r),n}function vs(e){var t=new Uint8Array(7);t[0]=127&e;var r=1;e:if(e>127){if(t[r-1]|=128,t[r]=e>>7&127,++r,e<=16383)break e;if(t[r-1]|=128,t[r]=e>>14&127,++r,e<=2097151)break e;if(t[r-1]|=128,t[r]=e>>21&127,++r,e<=268435455)break e;if(t[r-1]|=128,t[r]=e/256>>>21&127,++r,e<=34359738367)break e;if(t[r-1]|=128,t[r]=e/65536>>>21&127,++r,e<=4398046511103)break e;t[r-1]|=128,t[r]=e/16777216>>>21&127,++r}return t.slice(0,r)}function Ts(e){var t=0,r=127&e[t];e:if(e[t++]>=128){if(r|=(127&e[t])<<7,e[t++]<128)break e;if(r|=(127&e[t])<<14,e[t++]<128)break e;if(r|=(127&e[t])<<21,e[t++]<128)break e;r|=(127&e[t])<<28}return r}function ws(e){for(var t=[],r=[0];r[0]<e.length;){var n,a=r[0],i=gs(e,r),s=7&i,o=0;if(0==(i=Math.floor(i/8)))break;switch(s){case 0:for(var l=r[0];e[r[0]++]>=128;);n=e.slice(l,r[0]);break;case 5:o=4,n=e.slice(r[0],r[0]+o),r[0]+=o;break;case 1:o=8,n=e.slice(r[0],r[0]+o),r[0]+=o;break;case 2:o=gs(e,r),n=e.slice(r[0],r[0]+o),r[0]+=o;break;default:throw new Error("PB Type ".concat(s," for Field ").concat(i," at offset ").concat(a))}var f={data:n,type:s};null==t[i]?t[i]=[f]:t[i].push(f)}return t}function Es(e){var t=[];return e.forEach((function(e,r){e.forEach((function(e){e.data&&(t.push(vs(8*r+e.type)),2==e.type&&t.push(vs(e.data.length)),t.push(e.data))}))})),ms(t)}function bs(e){for(var t,r=[],n=[0];n[0]<e.length;){var a=gs(e,n),i=ws(e.slice(n[0],n[0]+a));n[0]+=a;var s={id:Ts(i[1][0].data),messages:[]};i[2].forEach((function(t){var r=ws(t.data),a=Ts(r[3][0].data);s.messages.push({meta:r,data:e.slice(n[0],n[0]+a)}),n[0]+=a})),(null==(t=i[3])?void 0:t[0])&&(s.merge=Ts(i[3][0].data)>>>0>0),r.push(s)}return r}function Ss(e){var t=[];return e.forEach((function(e){var r=[];r[1]=[{data:vs(e.id),type:0}],r[2]=[],null!=e.merge&&(r[3]=[{data:vs(+!!e.merge),type:0}]);var n=[];e.messages.forEach((function(e){n.push(e.data),e.meta[3]=[{type:0,data:vs(e.data.length)}],r[2].push({data:Es(e.meta),type:2})}));var a=Es(r);t.push(vs(a.length)),t.push(a),n.forEach((function(e){return t.push(e)}))})),ms(t)}function As(e,t){if(0!=e)throw new Error("Unexpected Snappy chunk type ".concat(e));for(var r=[0],n=gs(t,r),a=[];r[0]<t.length;){var i=3&t[r[0]];if(0!=i){var s=0,o=0;if(1==i?(o=4+(t[r[0]]>>2&7),s=(224&t[r[0]++])<<3,s|=t[r[0]++]):(o=1+(t[r[0]++]>>2),2==i?(s=t[r[0]]|t[r[0]+1]<<8,r[0]+=2):(s=(t[r[0]]|t[r[0]+1]<<8|t[r[0]+2]<<16|t[r[0]+3]<<24)>>>0,r[0]+=4)),a=[ms(a)],0==s)throw new Error("Invalid offset 0");if(s>a[0].length)throw new Error("Invalid offset beyond length");if(o>=s)for(a.push(a[0].slice(-s)),o-=s;o>=a[a.length-1].length;)a.push(a[a.length-1]),o-=a[a.length-1].length;a.push(a[0].slice(-s,-s+o))}else{var l=t[r[0]++]>>2;if(l<60)++l;else{var f=l-59;l=t[r[0]],f>1&&(l|=t[r[0]+1]<<8),f>2&&(l|=t[r[0]+2]<<16),f>3&&(l|=t[r[0]+3]<<24),l>>>=0,l++,r[0]+=f}a.push(t.slice(r[0],r[0]+l)),r[0]+=l}}var c=ms(a);if(c.length!=n)throw new Error("Unexpected length: ".concat(c.length," != ").concat(n));return c}function _s(e){for(var t=[],r=0;r<e.length;){var n=e[r++],a=e[r]|e[r+1]<<8|e[r+2]<<16;r+=3,t.push(As(n,e.slice(r,r+a))),r+=a}if(r!==e.length)throw new Error("data is not a valid framed stream!");return ms(t)}function ys(e){for(var t=[],r=0;r<e.length;){var n=Math.min(e.length-r,268435455),a=new Uint8Array(4);t.push(a);var i=vs(n),s=i.length;t.push(i),n<=60?(s++,t.push(new Uint8Array([n-1<<2]))):n<=256?(s+=2,t.push(new Uint8Array([240,n-1&255]))):n<=65536?(s+=3,t.push(new Uint8Array([244,n-1&255,n-1>>8&255]))):n<=16777216?(s+=4,t.push(new Uint8Array([248,n-1&255,n-1>>8&255,n-1>>16&255]))):n<=4294967296&&(s+=5,t.push(new Uint8Array([252,n-1&255,n-1>>8&255,n-1>>16&255,n-1>>>24&255]))),t.push(e.slice(r,r+n)),s+=n,a[0]=0,a[1]=255&s,a[2]=s>>8&255,a[3]=s>>16&255,r+=n}return ms(t)}function Os(e,t){var r=new Uint8Array(32),n=ps(r),a=12,i=0;switch(r[0]=5,e.t){case"n":r[1]=2,function(e,t,r){var n=Math.floor(0==r?0:Math.LOG10E*Math.log(Math.abs(r)))+6176-20,a=r/Math.pow(10,n-6176);e[t+15]|=n>>7,e[t+14]|=(127&n)<<1;for(var i=0;a>=1;++i,a/=256)e[t+i]=255&a;e[t+15]|=r>=0?0:128}(r,a,e.v),i|=1,a+=16;break;case"b":r[1]=6,n.setFloat64(a,e.v?1:0,!0),i|=2,a+=8;break;case"s":if(-1==t.indexOf(e.v))throw new Error("Value ".concat(e.v," missing from SST!"));r[1]=3,n.setUint32(a,t.indexOf(e.v),!0),i|=8,a+=4;break;default:throw"unsupported cell type "+e.t}return n.setUint32(8,i,!0),r.slice(0,a)}function xs(e,t){var r=new Uint8Array(32),n=ps(r),a=12,i=0;switch(r[0]=3,e.t){case"n":r[2]=2,n.setFloat64(a,e.v,!0),i|=32,a+=8;break;case"b":r[2]=6,n.setFloat64(a,e.v?1:0,!0),i|=32,a+=8;break;case"s":if(-1==t.indexOf(e.v))throw new Error("Value ".concat(e.v," missing from SST!"));r[2]=3,n.setUint32(a,t.indexOf(e.v),!0),i|=16,a+=4;break;default:throw"unsupported cell type "+e.t}return n.setUint32(4,i,!0),r.slice(0,a)}function Cs(e){return gs(ws(e)[1][0].data)}function Rs(e,t,r){var n,a,i,s;if(!(null==(n=e[6])?void 0:n[0])||!(null==(a=e[7])?void 0:a[0]))throw"Mutation only works on post-BNC storages!";if((null==(s=null==(i=e[8])?void 0:i[0])?void 0:s.data)&&Ts(e[8][0].data)>0||!1)throw"Math only works with normal offsets";for(var o=0,l=ps(e[7][0].data),f=0,c=[],h=ps(e[4][0].data),u=0,p=[],d=0;d<t.length;++d)if(null!=t[d]){var m,g;switch(l.setUint16(2*d,f,!0),h.setUint16(2*d,u,!0),typeof t[d]){case"string":m=Os({t:"s",v:t[d]},r),g=xs({t:"s",v:t[d]},r);break;case"number":m=Os({t:"n",v:t[d]},r),g=xs({t:"n",v:t[d]},r);break;case"boolean":m=Os({t:"b",v:t[d]},r),g=xs({t:"b",v:t[d]},r);break;default:throw new Error("Unsupported value "+t[d])}c.push(m),f+=m.length,p.push(g),u+=g.length,++o}else l.setUint16(2*d,65535,!0),h.setUint16(2*d,65535);for(e[2][0].data=vs(o);d<e[7][0].data.length/2;++d)l.setUint16(2*d,65535,!0),h.setUint16(2*d,65535,!0);return e[6][0].data=ms(c),e[3][0].data=ms(p),o}function Ns(e){var t;(t=[["cellDates",!1],["bookSST",!1],["bookType","xlsx"],["compression",!1],["WTF",!1]],function(e){for(var r=0;r!=t.length;++r){var n=t[r];void 0===e[n[0]]&&(e[n[0]]=n[1]),"n"===n[2]&&(e[n[0]]=Number(e[n[0]]))}})(e)}function ks(e,t){return"ods"==t.bookType?us(e,t):"numbers"==t.bookType?function(e,t){if(!t||!t.numbers)throw new Error("Must pass a `numbers` option -- check the README");var r=e.Sheets[e.SheetNames[0]];e.SheetNames.length;var n=nr(r["!ref"]);n.s.r=n.s.c=0,n.e.c>9&&(n.e.c=9),n.e.r>49&&(n.e.r=49);var a=Bs(r,{range:n,header:1}),i=["~Sh33tJ5~"];a.forEach((function(e){return e.forEach((function(e){"string"==typeof e&&i.push(e)}))}));var s={},o=[],l=ge.read(t.numbers,{type:"base64"});l.FileIndex.map((function(e,t){return[e,l.FullPaths[t]]})).forEach((function(e){var t=e[0],r=e[1];2==t.type&&t.name.match(/\.iwa/)&&bs(_s(t.content)).forEach((function(e){o.push(e.id),s[e.id]={deps:[],location:r,type:Ts(e.messages[0].meta[1][0].data)}}))})),o.sort((function(e,t){return e-t}));var f=o.filter((function(e){return e>1})).map((function(e){return[e,vs(e)]}));l.FileIndex.map((function(e,t){return[e,l.FullPaths[t]]})).forEach((function(e){var t=e[0];e[1],t.name.match(/\.iwa/)&&bs(_s(t.content)).forEach((function(e){e.messages.forEach((function(t){f.forEach((function(t){e.messages.some((function(e){return 11006!=Ts(e.meta[1][0].data)&&function(e,t){e:for(var r=0;r<=e.length-t.length;++r){for(var n=0;n<t.length;++n)if(e[r+n]!=t[n])continue e;return!0}return!1}(e.data,t[1])}))&&s[t[0]].deps.push(e.id)}))}))}))}));for(var c,h=ge.find(l,s[1].location),u=bs(_s(h.content)),p=0;p<u.length;++p){var d=u[p];1==d.id&&(c=d)}var m=Cs(ws(c.messages[0].data)[1][0].data);for(u=bs(_s((h=ge.find(l,s[m].location)).content)),p=0;p<u.length;++p)(d=u[p]).id==m&&(c=d);for(m=Cs(ws(c.messages[0].data)[2][0].data),u=bs(_s((h=ge.find(l,s[m].location)).content)),p=0;p<u.length;++p)(d=u[p]).id==m&&(c=d);for(m=Cs(ws(c.messages[0].data)[2][0].data),u=bs(_s((h=ge.find(l,s[m].location)).content)),p=0;p<u.length;++p)(d=u[p]).id==m&&(c=d);var g=ws(c.messages[0].data);g[6][0].data=vs(n.e.r+1),g[7][0].data=vs(n.e.c+1);for(var v=Cs(g[46][0].data),T=ge.find(l,s[v].location),w=bs(_s(T.content)),E=0;E<w.length&&w[E].id!=v;++E);if(w[E].id!=v)throw"Bad ColumnRowUIDMapArchive";var b=ws(w[E].messages[0].data);b[1]=[],b[2]=[],b[3]=[];for(var S=0;S<=n.e.c;++S){var A=[];A[1]=A[2]=[{type:0,data:vs(S+420690)}],b[1].push({type:2,data:Es(A)}),b[2].push({type:0,data:vs(S)}),b[3].push({type:0,data:vs(S)})}b[4]=[],b[5]=[],b[6]=[];for(var _=0;_<=n.e.r;++_)(A=[])[1]=A[2]=[{type:0,data:vs(_+726270)}],b[4].push({type:2,data:Es(A)}),b[5].push({type:0,data:vs(_)}),b[6].push({type:0,data:vs(_)});w[E].messages[0].data=Es(b),T.content=ys(Ss(w)),T.size=T.content.length,delete g[46];var y=ws(g[4][0].data);y[7][0].data=vs(n.e.r+1);var O=Cs(ws(y[1][0].data)[2][0].data);if((w=bs(_s((T=ge.find(l,s[O].location)).content)))[0].id!=O)throw"Bad HeaderStorageBucket";var x=ws(w[0].messages[0].data);for(_=0;_<a.length;++_){var C=ws(x[2][0].data);C[1][0].data=vs(_),C[4][0].data=vs(a[_].length),x[2][_]={type:x[2][0].type,data:Es(C)}}w[0].messages[0].data=Es(x),T.content=ys(Ss(w)),T.size=T.content.length;var R=Cs(y[2][0].data);if((w=bs(_s((T=ge.find(l,s[R].location)).content)))[0].id!=R)throw"Bad HeaderStorageBucket";for(x=ws(w[0].messages[0].data),S=0;S<=n.e.c;++S)(C=ws(x[2][0].data))[1][0].data=vs(S),C[4][0].data=vs(n.e.r+1),x[2][S]={type:x[2][0].type,data:Es(C)};w[0].messages[0].data=Es(x),T.content=ys(Ss(w)),T.size=T.content.length;var N=Cs(y[4][0].data);!function(){for(var e,t=ge.find(l,s[N].location),r=bs(_s(t.content)),n=0;n<r.length;++n){var a=r[n];a.id==N&&(e=a)}var o=ws(e.messages[0].data);o[3]=[];var f=[];i.forEach((function(e,t){f[1]=[{type:0,data:vs(t)}],f[2]=[{type:0,data:vs(1)}],f[3]=[{type:2,data:ds(e)}],o[3].push({type:2,data:Es(f)})})),e.messages[0].data=Es(o);var c=ys(Ss(r));t.content=c,t.size=t.content.length}();var k=ws(y[3][0].data),I=k[1][0];delete k[2];var D=ws(I.data),P=Cs(D[2][0].data);!function(){for(var e,t=ge.find(l,s[P].location),r=bs(_s(t.content)),o=0;o<r.length;++o){var f=r[o];f.id==P&&(e=f)}var c=ws(e.messages[0].data);delete c[6],delete k[7];var h=new Uint8Array(c[5][0].data);c[5]=[];for(var u=0,p=0;p<=n.e.r;++p){var d=ws(h);u+=Rs(d,a[p],i),d[1][0].data=vs(p),c[5].push({data:Es(d),type:2})}c[1]=[{type:0,data:vs(n.e.c+1)}],c[2]=[{type:0,data:vs(n.e.r+1)}],c[3]=[{type:0,data:vs(u)}],c[4]=[{type:0,data:vs(n.e.r+1)}],e.messages[0].data=Es(c);var m=ys(Ss(r));t.content=m,t.size=t.content.length}(),I.data=Es(D),y[3][0].data=Es(k),g[4][0].data=Es(y),c.messages[0].data=Es(g);var L=ys(Ss(u));return h.content=L,h.size=h.content.length,l}(e,t):"xlsb"==t.bookType?function(e,t){ma=1024,e&&!e.SSF&&(e.SSF=Pe(k));e&&e.SSF&&(pe(),ue(e.SSF),t.revssf=Se(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF);t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,ii?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r="xlsb"==t.bookType?"bin":"xml",n=ba.indexOf(t.bookType)>-1,a={workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""};Ns(t=t||{});var i=We(),s="",o=0;t.cellXfs=[],fi(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={});if(Be(i,s="docProps/core.xml",en(e.Props,t)),a.coreprops.push(s),Kr(t.rels,2,s,$r.CORE_PROPS),s="docProps/app.xml",e.Props&&e.Props.SheetNames);else if(e.Workbook&&e.Workbook.Sheets){for(var l=[],f=0;f<e.SheetNames.length;++f)2!=(e.Workbook.Sheets[f]||{}).Hidden&&l.push(e.SheetNames[f]);e.Props.SheetNames=l}else e.Props.SheetNames=e.SheetNames;e.Props.Worksheets=e.Props.SheetNames.length,Be(i,s,nn(e.Props)),a.extprops.push(s),Kr(t.rels,3,s,$r.EXT_PROPS),e.Custprops!==e.Props&&we(e.Custprops||{}).length>0&&(Be(i,s="docProps/custom.xml",an(e.Custprops)),a.custprops.push(s),Kr(t.rels,4,s,$r.CUST_PROPS));for(o=1;o<=e.SheetNames.length;++o){var c={"!id":{}},h=e.Sheets[e.SheetNames[o-1]];(h||{})["!type"];if(Be(i,s="xl/worksheets/sheet"+o+"."+r,Mi(o-1,s,t,e,c)),a.sheets.push(s),Kr(t.wbrels,-1,"worksheets/sheet"+o+"."+r,$r.WS[0]),h){var u=h["!comments"],p=!1,d="";u&&u.length>0&&(Be(i,d="xl/comments"+o+"."+r,Fi(u,d)),a.comments.push(d),Kr(c,-1,"../comments"+o+"."+r,$r.CMNT),p=!0),h["!legacy"]&&p&&Be(i,"xl/drawings/vmlDrawing"+o+".vml",ga(o,h["!comments"])),delete h["!comments"],delete h["!legacy"]}c["!id"].rId1&&Be(i,Xr(s),Yr(c))}null!=t.Strings&&t.Strings.length>0&&(Be(i,s="xl/sharedStrings."+r,function(e,t,r){return(".bin"===t.slice(-4)?Wn:Un)(e,r)}(t.Strings,s,t)),a.strs.push(s),Kr(t.wbrels,-1,"sharedStrings."+r,$r.SST));Be(i,s="xl/workbook."+r,function(e,t,r){return(".bin"===t.slice(-4)?Li:Di)(e)}(e,s)),a.workbooks.push(s),Kr(t.rels,1,s,$r.WB),Be(i,s="xl/theme/theme1.xml",ua(e.Themes,t)),a.themes.push(s),Kr(t.wbrels,-1,"theme/theme1.xml",$r.THEME),Be(i,s="xl/styles."+r,function(e,t,r){return(".bin"===t.slice(-4)?ha:qn)(e,r)}(e,s,t)),a.styles.push(s),Kr(t.wbrels,-1,"styles."+r,$r.STY),e.vbaraw&&n&&(Be(i,s="xl/vbaProject.bin",e.vbaraw),a.vba.push(s),Kr(t.wbrels,-1,"vbaProject.bin",$r.VBA));return Be(i,s="xl/metadata."+r,function(e){return(".bin"===e.slice(-4)?pa:da)()}(s)),a.metadata.push(s),Kr(t.wbrels,-1,"metadata."+r,$r.XLMETA),Be(i,"[Content_Types].xml",zr(a,t)),Be(i,"_rels/.rels",Yr(t.rels)),Be(i,"xl/_rels/workbook."+r+".rels",Yr(t.wbrels)),delete t.revssf,delete t.ssf,i}(e,t):function(e,t){ma=1024,e&&!e.SSF&&(e.SSF=Pe(k));e&&e.SSF&&(pe(),ue(e.SSF),t.revssf=Se(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF);t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,ii?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r="xml",n=ba.indexOf(t.bookType)>-1,a={workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""};Ns(t=t||{});var i=We(),s="",o=0;t.cellXfs=[],fi(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={});if(Be(i,s="docProps/core.xml",en(e.Props,t)),a.coreprops.push(s),Kr(t.rels,2,s,$r.CORE_PROPS),s="docProps/app.xml",e.Props&&e.Props.SheetNames);else if(e.Workbook&&e.Workbook.Sheets){for(var l=[],f=0;f<e.SheetNames.length;++f)2!=(e.Workbook.Sheets[f]||{}).Hidden&&l.push(e.SheetNames[f]);e.Props.SheetNames=l}else e.Props.SheetNames=e.SheetNames;e.Props.Worksheets=e.Props.SheetNames.length,Be(i,s,nn(e.Props)),a.extprops.push(s),Kr(t.rels,3,s,$r.EXT_PROPS),e.Custprops!==e.Props&&we(e.Custprops||{}).length>0&&(Be(i,s="docProps/custom.xml",an(e.Custprops)),a.custprops.push(s),Kr(t.rels,4,s,$r.CUST_PROPS));var c=["SheetJ5"];for(t.tcid=0,o=1;o<=e.SheetNames.length;++o){var h={"!id":{}},u=e.Sheets[e.SheetNames[o-1]];(u||{})["!type"];if(Be(i,s="xl/worksheets/sheet"+o+"."+r,di(o-1,t,e,h)),a.sheets.push(s),Kr(t.wbrels,-1,"worksheets/sheet"+o+"."+r,$r.WS[0]),u){var p=u["!comments"],d=!1,m="";if(p&&p.length>0){var g=!1;p.forEach((function(e){e[1].forEach((function(e){1==e.T&&(g=!0)}))})),g&&(Be(i,m="xl/threadedComments/threadedComment"+o+"."+r,Ta(p,c,t)),a.threadedcomments.push(m),Kr(h,-1,"../threadedComments/threadedComment"+o+"."+r,$r.TCMNT)),Be(i,m="xl/comments"+o+"."+r,va(p)),a.comments.push(m),Kr(h,-1,"../comments"+o+"."+r,$r.CMNT),d=!0}u["!legacy"]&&d&&Be(i,"xl/drawings/vmlDrawing"+o+".vml",ga(o,u["!comments"])),delete u["!comments"],delete u["!legacy"]}h["!id"].rId1&&Be(i,Xr(s),Yr(h))}null!=t.Strings&&t.Strings.length>0&&(Be(i,s="xl/sharedStrings."+r,Un(t.Strings,t)),a.strs.push(s),Kr(t.wbrels,-1,"sharedStrings."+r,$r.SST));Be(i,s="xl/workbook."+r,Di(e)),a.workbooks.push(s),Kr(t.rels,1,s,$r.WB),Be(i,s="xl/theme/theme1.xml",ua(e.Themes,t)),a.themes.push(s),Kr(t.wbrels,-1,"theme/theme1.xml",$r.THEME),Be(i,s="xl/styles."+r,qn(e,t)),a.styles.push(s),Kr(t.wbrels,-1,"styles."+r,$r.STY),e.vbaraw&&n&&(Be(i,s="xl/vbaProject.bin",e.vbaraw),a.vba.push(s),Kr(t.wbrels,-1,"vbaProject.bin",$r.VBA));Be(i,s="xl/metadata."+r,da()),a.metadata.push(s),Kr(t.wbrels,-1,"metadata."+r,$r.XLMETA),c.length>1&&(Be(i,s="xl/persons/person.xml",function(e){var t=[He,at("personList",null,{xmlns:st.TCMNT,"xmlns:x":ot[0]}).replace(/[\/]>/,">")];return e.forEach((function(e,r){t.push(at("person",null,{displayName:e,id:"{54EE7950-7262-4200-6969-"+("000000000000"+r).slice(-12)+"}",userId:e,providerId:"None"}))})),t.push("</personList>"),t.join("")}(c)),a.people.push(s),Kr(t.wbrels,-1,"persons/person.xml",$r.PEOPLE));return Be(i,"[Content_Types].xml",zr(a,t)),Be(i,"_rels/.rels",Yr(t.rels)),Be(i,"xl/_rels/workbook."+r+".rels",Yr(t.wbrels)),delete t.revssf,delete t.ssf,i}(e,t)}function Is(e,t){switch(t.type){case"base64":case"binary":break;case"buffer":case"array":t.type="";break;case"file":return Te(t.file,ge.write(e,{type:u?"buffer":""}));case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");default:throw new Error("Unrecognized type "+t.type)}return ge.write(e,t)}function Ds(e,t){var r=Pe(t||{});return function(e,t){var r={},n=u?"nodebuffer":"undefined"!=typeof Uint8Array?"array":"string";t.compression&&(r.compression="DEFLATE");if(t.password)r.type=n;else switch(t.type){case"base64":r.type="base64";break;case"binary":r.type="string";break;case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");case"buffer":case"file":r.type=n;break;default:throw new Error("Unrecognized type "+t.type)}var a=e.FullPaths?ge.write(e,{fileType:"zip",type:{nodebuffer:"buffer",string:"binary"}[r.type]||r.type,compression:!!t.compression}):e.generate(r);if("undefined"!=typeof Deno&&"string"==typeof a){if("binary"==t.type||"base64"==t.type)return a;a=new Uint8Array(v(a))}return t.password&&"undefined"!=typeof encrypt_agile?Is(encrypt_agile(a,t.password),t):"file"===t.type?Te(t.file,a):"string"==t.type?qe(a):a}(ks(e,r),r)}function Ps(e,t,r){r||(r="");var n=r+e;switch(t.type){case"base64":return c(Qe(n));case"binary":return Qe(n);case"string":return e;case"file":return Te(t.file,n,"utf8");case"buffer":return u?p(n,"utf8"):"undefined"!=typeof TextEncoder?(new TextEncoder).encode(n):Ps(n,{type:"binary"}).split("").map((function(e){return e.charCodeAt(0)}))}throw new Error("Unrecognized type "+t.type)}function Ls(e,t){switch(t.type){case"string":case"base64":case"binary":for(var r="",n=0;n<e.length;++n)r+=String.fromCharCode(e[n]);return"base64"==t.type?c(r):"string"==t.type?qe(r):r;case"file":return Te(t.file,e);case"buffer":return e;default:throw new Error("Unrecognized type "+t.type)}}function Ms(e,t){i(),Ii(e);var r=Pe(t||{});if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),"array"==r.type){r.type="binary";var n=Ms(e,r);return r.type="array",v(n)}var a=0;if(r.sheet&&(a="number"==typeof r.sheet?r.sheet:e.SheetNames.indexOf(r.sheet),!e.SheetNames[a]))throw new Error("Sheet not found: "+r.sheet+" : "+typeof r.sheet);switch(r.bookType||"xlsb"){case"xml":case"xlml":return Ps(Vi(e,r),r);case"slk":case"sylk":return Ps(In.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"htm":case"html":return Ps(ss(e.Sheets[e.SheetNames[a]],r),r);case"txt":return function(e,t){switch(t.type){case"base64":return c(e);case"binary":case"string":return e;case"file":return Te(t.file,e,"binary");case"buffer":return u?p(e,"binary"):e.split("").map((function(e){return e.charCodeAt(0)}))}throw new Error("Unrecognized type "+t.type)}(Vs(e.Sheets[e.SheetNames[a]],r),r);case"csv":return Ps(Gs(e.Sheets[e.SheetNames[a]],r),r,"\ufeff");case"dif":return Ps(Dn.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"dbf":return Ls(kn.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"prn":return Ps(Ln.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"rtf":return Ps(Gn.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"eth":return Ps(Pn.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"fods":return Ps(us(e,r),r);case"wk1":return Ls(Mn.sheet_to_wk1(e.Sheets[e.SheetNames[a]],r),r);case"wk3":return Ls(Mn.book_to_wk3(e,r),r);case"biff2":r.biff||(r.biff=2);case"biff3":r.biff||(r.biff=3);case"biff4":return r.biff||(r.biff=4),Ls(rs(e,r),r);case"biff5":r.biff||(r.biff=5);case"biff8":case"xla":case"xls":return r.biff||(r.biff=8),function(e,t){var r=t||{};return Is(zi(e,r),r)}(e,r);case"xlsx":case"xlsm":case"xlam":case"xlsb":case"numbers":case"ods":return Ds(e,r);default:throw new Error("Unrecognized bookType |"+r.bookType+"|")}}function Fs(e,t,r){var n=r||{};return n.type="file",n.file=t,function(e){if(!e.bookType){var t=e.file.slice(e.file.lastIndexOf(".")).toLowerCase();t.match(/^\.[a-z]+$/)&&(e.bookType=t.slice(1)),e.bookType={xls:"biff8",htm:"html",slk:"sylk",socialcalc:"eth",Sh33tJS:"WTF"}[e.bookType]||e.bookType}}(n),Ms(e,n)}function Us(e,t,r,n,a,i,s,o){var l=qt(r),f=o.defval,c=o.raw||!Object.prototype.hasOwnProperty.call(o,"raw"),h=!0,u=1===a?[]:{};if(1!==a)if(Object.defineProperty)try{Object.defineProperty(u,"__rowNum__",{value:r,enumerable:!1})}catch(g){u.__rowNum__=r}else u.__rowNum__=r;if(!s||e[r])for(var p=t.s.c;p<=t.e.c;++p){var d=s?e[r][p]:e[n[p]+l];if(void 0!==d&&void 0!==d.t){var m=d.v;switch(d.t){case"z":if(null==m)break;continue;case"e":m=0==m?null:void 0;break;case"s":case"d":case"b":case"n":break;default:throw new Error("unrecognized type "+d.t)}if(null!=i[p]){if(null==m)if("e"==d.t&&null===m)u[i[p]]=null;else if(void 0!==f)u[i[p]]=f;else{if(!c||null!==m)continue;u[i[p]]=null}else u[i[p]]=c&&("n"!==d.t||"n"===d.t&&!1!==o.rawNumbers)?m:or(d,m,o);null!=m&&(h=!1)}}else{if(void 0===f)continue;null!=i[p]&&(u[i[p]]=f)}}return{row:u,isempty:h}}function Bs(e,t){if(null==e||null==e["!ref"])return[];var r={t:"n",v:0},n=0,a=1,i=[],s=0,o="",l={s:{r:0,c:0},e:{r:0,c:0}},f=t||{},c=null!=f.range?f.range:e["!ref"];switch(1===f.header?n=1:"A"===f.header?n=2:Array.isArray(f.header)?n=3:null==f.header&&(n=0),typeof c){case"string":l=ir(c);break;case"number":(l=ir(e["!ref"])).s.r=c;break;default:l=c}n>0&&(a=0);var h=qt(l.s.r),u=[],p=[],d=0,m=0,g=Array.isArray(e),v=l.s.r,T=0,w={};g&&!e[v]&&(e[v]=[]);var E=f.skipHidden&&e["!cols"]||[],b=f.skipHidden&&e["!rows"]||[];for(T=l.s.c;T<=l.e.c;++T)if(!(E[T]||{}).hidden)switch(u[T]=er(T),r=g?e[v][T]:e[u[T]+h],n){case 1:i[T]=T-l.s.c;break;case 2:i[T]=u[T];break;case 3:i[T]=f.header[T-l.s.c];break;default:if(null==r&&(r={w:"__EMPTY",t:"s"}),o=s=or(r,null,f),m=w[s]||0){do{o=s+"_"+m++}while(w[o]);w[s]=m,w[o]=1}else w[s]=1;i[T]=o}for(v=l.s.r+a;v<=l.e.r;++v)if(!(b[v]||{}).hidden){var S=Us(e,l,v,u,n,i,g,f);(!1===S.isempty||(1===n?!1!==f.blankrows:f.blankrows))&&(p[d++]=S.row)}return p.length=d,p}var Ws=/"/g;function Hs(e,t,r,n,a,i,s,o){for(var l=!0,f=[],c="",h=qt(r),u=t.s.c;u<=t.e.c;++u)if(n[u]){var p=o.dense?(e[r]||[])[u]:e[n[u]+h];if(null==p)c="";else if(null!=p.v){l=!1,c=""+(o.rawNumbers&&"n"==p.t?p.v:or(p,null,o));for(var d=0,m=0;d!==c.length;++d)if((m=c.charCodeAt(d))===a||m===i||34===m||o.forceQuotes){c='"'+c.replace(Ws,'""')+'"';break}"ID"==c&&(c='"ID"')}else null==p.f||p.F?c="":(l=!1,(c="="+p.f).indexOf(",")>=0&&(c='"'+c.replace(Ws,'""')+'"'));f.push(c)}return!1===o.blankrows&&l?null:f.join(s)}function Gs(e,t){var r=[],n=null==t?{}:t;if(null==e||null==e["!ref"])return"";var a=ir(e["!ref"]),i=void 0!==n.FS?n.FS:",",s=i.charCodeAt(0),o=void 0!==n.RS?n.RS:"\n",l=o.charCodeAt(0),f=new RegExp(("|"==i?"\\|":i)+"+$"),c="",h=[];n.dense=Array.isArray(e);for(var u=n.skipHidden&&e["!cols"]||[],p=n.skipHidden&&e["!rows"]||[],d=a.s.c;d<=a.e.c;++d)(u[d]||{}).hidden||(h[d]=er(d));for(var m=0,g=a.s.r;g<=a.e.r;++g)(p[g]||{}).hidden||null!=(c=Hs(e,a,g,h,s,l,i,n))&&(n.strip&&(c=c.replace(f,"")),(c||!1!==n.blankrows)&&r.push((m++?o:"")+c));return delete n.dense,r.join("")}function Vs(e,t){return t||(t={}),t.FS="\t",t.RS="\n",Gs(e,t)}function js(e,t,r){var n,a=r||{},i=+!a.skipHeader,s=e||{},o=0,l=0;if(s&&null!=a.origin)if("number"==typeof a.origin)o=a.origin;else{var f="string"==typeof a.origin?tr(a.origin):a.origin;o=f.r,l=f.c}var c={s:{c:0,r:0},e:{c:l,r:o+t.length-1+i}};if(s["!ref"]){var h=ir(s["!ref"]);c.e.c=Math.max(c.e.c,h.e.c),c.e.r=Math.max(c.e.r,h.e.r),-1==o&&(o=h.e.r+1,c.e.r=o+t.length-1+i)}else-1==o&&(o=0,c.e.r=t.length-1+i);var u=a.header||[],p=0;t.forEach((function(e,t){we(e).forEach((function(r){-1==(p=u.indexOf(r))&&(u[p=u.length]=r);var f=e[r],c="z",h="",d=rr({c:l+p,r:o+t+i});n=zs(s,d),!f||"object"!=typeof f||f instanceof Date?("number"==typeof f?c="n":"boolean"==typeof f?c="b":"string"==typeof f?c="s":f instanceof Date?(c="d",a.cellDates||(c="n",f=_e(f)),h=a.dateNF||k[14]):null===f&&a.nullError&&(c="e",f=0),n?(n.t=c,n.v=f,delete n.w,delete n.R,h&&(n.z=h)):s[d]=n={t:c,v:f},h&&(n.z=h)):s[d]=f}))})),c.e.c=Math.max(c.e.c,l+u.length-1);var d=qt(o);if(i)for(p=0;p<u.length;++p)s[er(p+l)+d]={t:"s",v:u[p]};return s["!ref"]=ar(c),s}function zs(e,t,r){if("string"==typeof t){if(Array.isArray(e)){var n=tr(t);return e[n.r]||(e[n.r]=[]),e[n.r][n.c]||(e[n.r][n.c]={t:"z"})}return e[t]||(e[t]={t:"z"})}return zs(e,rr("number"!=typeof t?t:{r:t,c:r||0}))}function $s(e,t,r){return t?(e.l={Target:t},r&&(e.l.Tooltip=r)):delete e.l,e}var Xs={encode_col:er,encode_row:qt,encode_cell:rr,encode_range:ar,decode_col:Qt,decode_row:Zt,split_cell:function(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")},decode_cell:tr,decode_range:nr,format_cell:or,sheet_add_aoa:fr,sheet_add_json:js,sheet_add_dom:os,aoa_to_sheet:cr,json_to_sheet:function(e,t){return js(null,e,t)},table_to_sheet:ls,table_to_book:function(e,t){return lr(ls(e,t),t)},sheet_to_csv:Gs,sheet_to_txt:Vs,sheet_to_json:Bs,sheet_to_html:ss,sheet_to_formulae:function(e){var t,r="",n="";if(null==e||null==e["!ref"])return[];var a,i=ir(e["!ref"]),s="",o=[],l=[],f=Array.isArray(e);for(a=i.s.c;a<=i.e.c;++a)o[a]=er(a);for(var c=i.s.r;c<=i.e.r;++c)for(s=qt(c),a=i.s.c;a<=i.e.c;++a)if(r=o[a]+s,n="",void 0!==(t=f?(e[c]||[])[a]:e[r])){if(null!=t.F){if(r=t.F,!t.f)continue;n=t.f,-1==r.indexOf(":")&&(r=r+":"+r)}if(null!=t.f)n=t.f;else{if("z"==t.t)continue;if("n"==t.t&&null!=t.v)n=""+t.v;else if("b"==t.t)n=t.v?"TRUE":"FALSE";else if(void 0!==t.w)n="'"+t.w;else{if(void 0===t.v)continue;n="s"==t.t?"'"+t.v:""+t.v}}l[l.length]=r+"="+n}return l},sheet_to_row_object_array:Bs,sheet_get_cell:zs,book_new:function(){return{SheetNames:[],Sheets:{}}},book_append_sheet:function(e,t,r,n){var a=1;if(!r)for(;a<=65535&&-1!=e.SheetNames.indexOf(r="Sheet"+a);++a,r=void 0);if(!r||e.SheetNames.length>=65535)throw new Error("Too many worksheets");if(n&&e.SheetNames.indexOf(r)>=0){var i=r.match(/(^.*?)(\d+)$/);a=i&&+i[2]||0;var s=i&&i[1]||r;for(++a;a<=65535&&-1!=e.SheetNames.indexOf(r=s+a);++a);}if(ki(r),e.SheetNames.indexOf(r)>=0)throw new Error("Worksheet with name |"+r+"| already exists!");return e.SheetNames.push(r),e.Sheets[r]=t,r},book_set_sheet_visibility:function(e,t,r){e.Workbook||(e.Workbook={}),e.Workbook.Sheets||(e.Workbook.Sheets=[]);var n=function(e,t){if("number"==typeof t){if(t>=0&&e.SheetNames.length>t)return t;throw new Error("Cannot find sheet # "+t)}if("string"==typeof t){var r=e.SheetNames.indexOf(t);if(r>-1)return r;throw new Error("Cannot find sheet name |"+t+"|")}throw new Error("Cannot find sheet |"+t+"|")}(e,t);switch(e.Workbook.Sheets[n]||(e.Workbook.Sheets[n]={}),r){case 0:case 1:case 2:break;default:throw new Error("Bad sheet visibility setting "+r)}e.Workbook.Sheets[n].Hidden=r},cell_set_number_format:function(e,t){return e.z=t,e},cell_set_hyperlink:$s,cell_set_internal_link:function(e,t,r){return $s(e,"#"+t,r)},cell_add_comment:function(e,t,r){e.c||(e.c=[]),e.c.push({t:t,a:r||"SheetJS"})},sheet_set_array_formula:function(e,t,r,n){for(var a="string"!=typeof t?t:ir(t),i="string"==typeof t?t:ar(t),s=a.s.r;s<=a.e.r;++s)for(var o=a.s.c;o<=a.e.c;++o){var l=zs(e,s,o);l.t="n",l.F=i,delete l.v,s==a.s.r&&o==a.s.c&&(l.f=r,n&&(l.D=!0))}return e},consts:{SHEET_VISIBLE:0,SHEET_HIDDEN:1,SHEET_VERY_HIDDEN:2}};export{Xs as u,Fs as w};
