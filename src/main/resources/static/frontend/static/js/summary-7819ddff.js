import{W as t}from"./quasar-b3f06d8a.js";import"./vue-5bfa3a54.js";import{c as e}from"./statisticReportApi-dc9fa149.js";import{g as s}from"./api-b858041e.js";import{d as r}from"./dayjs-d60cc07f.js";import{u as o,w as i}from"./xlsx-c1bdd32b.js";import{h as l,o as a,c as n,a as c,t as p,b as m,x as u,a8 as d,aa as j}from"./@vue-5e5cdef9.js";import"./@babel-f3c0a00c.js";import"./index-8cc8d4b8.js";import"./element-plus-d975be09.js";import"./lodash-es-ea7deab5.js";import"./@vueuse-af86c621.js";import"./vue-router-6159329f.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./xe-utils-fe99d42a.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-7bbbb435.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./lodash-6d99edc3.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./menuStore-26f8ddd8.js";import"./icons-95011f8c.js";import"./naive-ui-0ee0b8c3.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./date-fns-tz-0cc073c8.js";import"./@vicons-f32a0bdb.js";import"./notification-950a5f80.js";function x(t,e){const s=o.table_to_sheet(t),r=function(t){const e=t.querySelectorAll("col"),s=[];return e.forEach((t=>{const e=t.style.width||"auto";"auto"===e?s.push({width:o.measure_col_width(t)}):s.push({width:parseInt(e)})})),s}(t);s["!cols"]=r;const l=function(t){const e=t.querySelectorAll("col"),s=[];return e.forEach(((t,e)=>{const r=t.getAttribute("data-type");"date"!==r&&"datetime"!==r||s.push(e)})),s}(t);l.forEach((t=>{const e=o.decode_range(s["!ref"]);for(let r=e.s.r;r<=e.e.r;r++){const e=o.encode_cell({r:r,c:t}),i=s[e];i&&"number"==typeof i.v&&"n"===i.t&&o.is_date(i)&&(i.t="s",i.z="yyyy-mm-dd",i.w=o.format_cell(i))}}));const a=o.book_new();o.book_append_sheet(a,s,e),i(a,`${e}.xlsx`)}const v={class:"tw-text-xl tw-flex tw-justify-between tw-w-[500px]"},f=c("span",{class:"small-title"},"汇总统计",-1),y={class:"small-title"},w=c("td",{class:"text-center"},"统计时间",-1),b={class:"text-center"},h=c("td",{class:"text-center"},"总电站数",-1),g={class:"text-center"},_=c("td",{class:"text-center"},"总装机容量 MWp",-1),k={class:"text-center"},z=c("td",{class:"text-center"},"效率(等效小时)",-1),q={class:"text-center"},M=c("td",{class:"text-center"},"总发电量 MWh",-1),S={class:"text-center"},A=c("td",{class:"text-center"},"总收益 万元",-1),C={class:"text-center"},E=c("td",{class:"text-center"},"期间发电量 MWh",-1),F={class:"text-center"},W=c("td",{class:"text-center"},"节约标准煤 wt",-1),Y={class:"text-center"},D=c("td",{class:"text-center"},"等效植树 棵",-1),N={class:"text-center"},O=c("td",{class:"text-center"},[j(" CO"),c("sub",{class:"tw-text-xs"},"2"),j("累计减排 wt ")],-1),P={class:"text-center"},R={__name:"summary",props:{data:{type:Object},form:{type:Object}},setup(o,{expose:i}){const j=l({}),R=l([]);return i({getData:async function(t){R.value=t.date;const r=await e(t.id,t.electricityPrice,...t.date,...t.power),o=s(r);j.value=o.data},exportFile:async function(){x(document.querySelector("table"),"电站日常统计(汇总统计)"+r().format("YYYY-MM-DD"))}}),(e,s)=>{const r=t;return a(),n("div",null,[c("header",v,[f,c("span",y,p(m(R).join(" 至 ")),1)]),u(r,{separator:"cell",class:"tw-rounded-none tw-w-[500px]"},{default:d((()=>{var t,e,s,r,o;return[c("tbody",null,[c("tr",null,[w,c("td",b,p(m(j).statisticalTime||"-"),1)]),c("tr",null,[h,c("td",g,p(m(j).plantNum||"0"),1)]),c("tr",null,[_,c("td",k,p((((null==(t=m(j))?void 0:t.plantCapacity)||0)/1e3).toFixed(3)||"0"),1)]),c("tr",null,[z,c("td",q,p((null==(e=m(j))?void 0:e.efficiencyPerHours)||"0"),1)]),c("tr",null,[M,c("td",S,p((((null==(s=m(j))?void 0:s.electricity)||0)/1e3).toFixed(3)||"0"),1)]),c("tr",null,[A,c("td",C,p((((null==(r=m(j))?void 0:r.income)||0)/1e4).toFixed(3)||"0"),1)]),c("tr",null,[E,c("td",F,p((((null==(o=m(j))?void 0:o.periodElectricity)||0)/1e3).toFixed(3)),1)]),c("tr",null,[W,c("td",Y,p(m(j).reduceCoal||"0"),1)]),c("tr",null,[D,c("td",N,p(m(j).treeNum||"0"),1)]),c("tr",null,[O,c("td",P,p(m(j).reduceCo2||"0"),1)])])]})),_:1})])}}};export{R as default};
