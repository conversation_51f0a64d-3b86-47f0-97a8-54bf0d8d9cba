import{k as t,h as e}from"./@babel-f3c0a00c.js";var i,r={exports:{}},a={},n={},s={};function o(){return i||(i=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e._registerNode=e.Konva=e.glob=void 0;const i=Math.PI/180;e.glob=void 0!==t?t:"undefined"!=typeof window?window:"undefined"!=typeof WorkerGlobalScope?self:{},e.Konva={_global:e.glob,version:"9.3.13",isBrowser:"undefined"!=typeof window&&("[object Window]"==={}.toString.call(window)||"[object global]"==={}.toString.call(window)),isUnminified:/param/.test(function(t){}.toString()),dblClickWindow:400,getAngle:t=>e.Konva.angleDeg?t*i:t,enableTrace:!1,pointerEventsEnabled:!0,autoDrawEnabled:!0,hitOnDragEnabled:!1,capturePointerEventsEnabled:!1,_mouseListenClick:!1,_touchListenClick:!1,_pointerListenClick:!1,_mouseInDblClickWindow:!1,_touchInDblClickWindow:!1,_pointerInDblClickWindow:!1,_mouseDblClickPointerId:null,_touchDblClickPointerId:null,_pointerDblClickPointerId:null,_fixTextRendering:!1,pixelRatio:"undefined"!=typeof window&&window.devicePixelRatio||1,dragDistance:3,angleDeg:!0,showWarnings:!0,dragButtons:[0,1],isDragging:()=>e.Konva.DD.isDragging,isTransforming(){var t;return null===(t=e.Konva.Transformer)||void 0===t?void 0:t.isTransforming()},isDragReady:()=>!!e.Konva.DD.node,releaseCanvasOnDestroy:!0,document:e.glob.document,_injectGlobal(t){e.glob.Konva=t}};e._registerNode=t=>{e.Konva[t.prototype.getClassName()]=t},e.Konva._injectGlobal(e.Konva)}(s)),s}var h,l={};function d(){return h||(h=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.Util=t.Transform=void 0;const e=o();class i{constructor(t=[1,0,0,1,0,0]){this.dirty=!1,this.m=t&&t.slice()||[1,0,0,1,0,0]}reset(){this.m[0]=1,this.m[1]=0,this.m[2]=0,this.m[3]=1,this.m[4]=0,this.m[5]=0}copy(){return new i(this.m)}copyInto(t){t.m[0]=this.m[0],t.m[1]=this.m[1],t.m[2]=this.m[2],t.m[3]=this.m[3],t.m[4]=this.m[4],t.m[5]=this.m[5]}point(t){var e=this.m;return{x:e[0]*t.x+e[2]*t.y+e[4],y:e[1]*t.x+e[3]*t.y+e[5]}}translate(t,e){return this.m[4]+=this.m[0]*t+this.m[2]*e,this.m[5]+=this.m[1]*t+this.m[3]*e,this}scale(t,e){return this.m[0]*=t,this.m[1]*=t,this.m[2]*=e,this.m[3]*=e,this}rotate(t){var e=Math.cos(t),i=Math.sin(t),r=this.m[0]*e+this.m[2]*i,a=this.m[1]*e+this.m[3]*i,n=this.m[0]*-i+this.m[2]*e,s=this.m[1]*-i+this.m[3]*e;return this.m[0]=r,this.m[1]=a,this.m[2]=n,this.m[3]=s,this}getTranslation(){return{x:this.m[4],y:this.m[5]}}skew(t,e){var i=this.m[0]+this.m[2]*e,r=this.m[1]+this.m[3]*e,a=this.m[2]+this.m[0]*t,n=this.m[3]+this.m[1]*t;return this.m[0]=i,this.m[1]=r,this.m[2]=a,this.m[3]=n,this}multiply(t){var e=this.m[0]*t.m[0]+this.m[2]*t.m[1],i=this.m[1]*t.m[0]+this.m[3]*t.m[1],r=this.m[0]*t.m[2]+this.m[2]*t.m[3],a=this.m[1]*t.m[2]+this.m[3]*t.m[3],n=this.m[0]*t.m[4]+this.m[2]*t.m[5]+this.m[4],s=this.m[1]*t.m[4]+this.m[3]*t.m[5]+this.m[5];return this.m[0]=e,this.m[1]=i,this.m[2]=r,this.m[3]=a,this.m[4]=n,this.m[5]=s,this}invert(){var t=1/(this.m[0]*this.m[3]-this.m[1]*this.m[2]),e=this.m[3]*t,i=-this.m[1]*t,r=-this.m[2]*t,a=this.m[0]*t,n=t*(this.m[2]*this.m[5]-this.m[3]*this.m[4]),s=t*(this.m[1]*this.m[4]-this.m[0]*this.m[5]);return this.m[0]=e,this.m[1]=i,this.m[2]=r,this.m[3]=a,this.m[4]=n,this.m[5]=s,this}getMatrix(){return this.m}decompose(){var e=this.m[0],i=this.m[1],r=this.m[2],a=this.m[3],n=e*a-i*r;let s={x:this.m[4],y:this.m[5],rotation:0,scaleX:0,scaleY:0,skewX:0,skewY:0};if(0!=e||0!=i){var o=Math.sqrt(e*e+i*i);s.rotation=i>0?Math.acos(e/o):-Math.acos(e/o),s.scaleX=o,s.scaleY=n/o,s.skewX=(e*r+i*a)/n,s.skewY=0}else if(0!=r||0!=a){var h=Math.sqrt(r*r+a*a);s.rotation=Math.PI/2-(a>0?Math.acos(-r/h):-Math.acos(r/h)),s.scaleX=n/h,s.scaleY=h,s.skewX=0,s.skewY=(e*r+i*a)/n}return s.rotation=t.Util._getRotation(s.rotation),s}}t.Transform=i;var r=Math.PI/180,a=180/Math.PI,n={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,132,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,255,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,203],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[119,128,144],slategrey:[119,128,144],snow:[255,255,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],transparent:[255,255,255,0],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,5]},s=/rgb\((\d{1,3}),(\d{1,3}),(\d{1,3})\)/,h=[];const l="undefined"!=typeof requestAnimationFrame&&requestAnimationFrame||function(t){setTimeout(t,60)};t.Util={_isElement:t=>!(!t||1!=t.nodeType),_isFunction:t=>!!(t&&t.constructor&&t.call&&t.apply),_isPlainObject:t=>!!t&&t.constructor===Object,_isArray:t=>"[object Array]"===Object.prototype.toString.call(t),_isNumber:t=>"[object Number]"===Object.prototype.toString.call(t)&&!isNaN(t)&&isFinite(t),_isString:t=>"[object String]"===Object.prototype.toString.call(t),_isBoolean:t=>"[object Boolean]"===Object.prototype.toString.call(t),isObject:t=>t instanceof Object,isValidSelector(t){if("string"!=typeof t)return!1;var e=t[0];return"#"===e||"."===e||e===e.toUpperCase()},_sign:t=>0===t||t>0?1:-1,requestAnimFrame(t){h.push(t),1===h.length&&l((function(){const t=h;h=[],t.forEach((function(t){t()}))}))},createCanvasElement(){var t=document.createElement("canvas");try{t.style=t.style||{}}catch(e){}return t},createImageElement:()=>document.createElement("img"),_isInDocument(t){for(;t=t.parentNode;)if(t==document)return!0;return!1},_urlToImage(e,i){var r=t.Util.createImageElement();r.onload=function(){i(r)},r.src=e},_rgbToHex:(t,e,i)=>((1<<24)+(t<<16)+(e<<8)+i).toString(16).slice(1),_hexToRgb(t){t=t.replace("#","");var e=parseInt(t,16);return{r:e>>16&255,g:e>>8&255,b:255&e}},getRandomColor(){for(var t=(16777215*Math.random()|0).toString(16);t.length<6;)t="0"+t;return"#"+t},getRGB(t){var e;return t in n?{r:(e=n[t])[0],g:e[1],b:e[2]}:"#"===t[0]?this._hexToRgb(t.substring(1)):"rgb("===t.substr(0,4)?(e=s.exec(t.replace(/ /g,"")),{r:parseInt(e[1],10),g:parseInt(e[2],10),b:parseInt(e[3],10)}):{r:0,g:0,b:0}},colorToRGBA:e=>(e=e||"black",t.Util._namedColorToRBA(e)||t.Util._hex3ColorToRGBA(e)||t.Util._hex4ColorToRGBA(e)||t.Util._hex6ColorToRGBA(e)||t.Util._hex8ColorToRGBA(e)||t.Util._rgbColorToRGBA(e)||t.Util._rgbaColorToRGBA(e)||t.Util._hslColorToRGBA(e)),_namedColorToRBA(t){var e=n[t.toLowerCase()];return e?{r:e[0],g:e[1],b:e[2],a:1}:null},_rgbColorToRGBA(t){if(0===t.indexOf("rgb(")){var e=(t=t.match(/rgb\(([^)]+)\)/)[1]).split(/ *, */).map(Number);return{r:e[0],g:e[1],b:e[2],a:1}}},_rgbaColorToRGBA(t){if(0===t.indexOf("rgba(")){var e=(t=t.match(/rgba\(([^)]+)\)/)[1]).split(/ *, */).map(((t,e)=>"%"===t.slice(-1)?3===e?parseInt(t)/100:parseInt(t)/100*255:Number(t)));return{r:e[0],g:e[1],b:e[2],a:e[3]}}},_hex8ColorToRGBA(t){if("#"===t[0]&&9===t.length)return{r:parseInt(t.slice(1,3),16),g:parseInt(t.slice(3,5),16),b:parseInt(t.slice(5,7),16),a:parseInt(t.slice(7,9),16)/255}},_hex6ColorToRGBA(t){if("#"===t[0]&&7===t.length)return{r:parseInt(t.slice(1,3),16),g:parseInt(t.slice(3,5),16),b:parseInt(t.slice(5,7),16),a:1}},_hex4ColorToRGBA(t){if("#"===t[0]&&5===t.length)return{r:parseInt(t[1]+t[1],16),g:parseInt(t[2]+t[2],16),b:parseInt(t[3]+t[3],16),a:parseInt(t[4]+t[4],16)/255}},_hex3ColorToRGBA(t){if("#"===t[0]&&4===t.length)return{r:parseInt(t[1]+t[1],16),g:parseInt(t[2]+t[2],16),b:parseInt(t[3]+t[3],16),a:1}},_hslColorToRGBA(t){if(/hsl\((\d+),\s*([\d.]+)%,\s*([\d.]+)%\)/g.test(t)){const[e,...i]=/hsl\((\d+),\s*([\d.]+)%,\s*([\d.]+)%\)/g.exec(t),r=Number(i[0])/360,a=Number(i[1])/100,n=Number(i[2])/100;let s,o,h;if(0===a)return h=255*n,{r:Math.round(h),g:Math.round(h),b:Math.round(h),a:1};s=n<.5?n*(1+a):n+a-n*a;const l=2*n-s,d=[0,0,0];for(let t=0;t<3;t++)o=r+1/3*-(t-1),o<0&&o++,o>1&&o--,h=6*o<1?l+6*(s-l)*o:2*o<1?s:3*o<2?l+(s-l)*(2/3-o)*6:l,d[t]=255*h;return{r:Math.round(d[0]),g:Math.round(d[1]),b:Math.round(d[2]),a:1}}},haveIntersection:(t,e)=>!(e.x>t.x+t.width||e.x+e.width<t.x||e.y>t.y+t.height||e.y+e.height<t.y),cloneObject(t){var e={};for(var i in t)this._isPlainObject(t[i])?e[i]=this.cloneObject(t[i]):this._isArray(t[i])?e[i]=this.cloneArray(t[i]):e[i]=t[i];return e},cloneArray:t=>t.slice(0),degToRad:t=>t*r,radToDeg:t=>t*a,_degToRad:e=>(t.Util.warn("Util._degToRad is removed. Please use public Util.degToRad instead."),t.Util.degToRad(e)),_radToDeg:e=>(t.Util.warn("Util._radToDeg is removed. Please use public Util.radToDeg instead."),t.Util.radToDeg(e)),_getRotation:i=>e.Konva.angleDeg?t.Util.radToDeg(i):i,_capitalize:t=>t.charAt(0).toUpperCase()+t.slice(1),throw(t){throw new Error("Konva error: "+t)},error(t){},warn(t){e.Konva.showWarnings},each(t,e){for(var i in t)e(i,t[i])},_inRange:(t,e,i)=>e<=t&&t<i,_getProjectionToSegment(t,e,i,r,a,n){var s,o,h,l=(t-i)*(t-i)+(e-r)*(e-r);if(0==l)s=t,o=e,h=(a-i)*(a-i)+(n-r)*(n-r);else{var d=((a-t)*(i-t)+(n-e)*(r-e))/l;d<0?(s=t,o=e,h=(t-a)*(t-a)+(e-n)*(e-n)):d>1?(s=i,o=r,h=(i-a)*(i-a)+(r-n)*(r-n)):h=((s=t+d*(i-t))-a)*(s-a)+((o=e+d*(r-e))-n)*(o-n)}return[s,o,h]},_getProjectionToLine(e,i,r){var a=t.Util.cloneObject(e),n=Number.MAX_VALUE;return i.forEach((function(s,o){if(r||o!==i.length-1){var h=i[(o+1)%i.length],l=t.Util._getProjectionToSegment(s.x,s.y,h.x,h.y,e.x,e.y),d=l[0],c=l[1],g=l[2];g<n&&(a.x=d,a.y=c,n=g)}})),a},_prepareArrayForTween(e,i,r){var a,n=[],s=[];if(e.length>i.length){var o=i;i=e,e=o}for(a=0;a<e.length;a+=2)n.push({x:e[a],y:e[a+1]});for(a=0;a<i.length;a+=2)s.push({x:i[a],y:i[a+1]});var h=[];return s.forEach((function(e){var i=t.Util._getProjectionToLine(e,n,r);h.push(i.x),h.push(i.y)})),h},_prepareToStringify(e){var i;for(var r in e.visitedByCircularReferenceRemoval=!0,e)if(e.hasOwnProperty(r)&&e[r]&&"object"==typeof e[r])if(i=Object.getOwnPropertyDescriptor(e,r),e[r].visitedByCircularReferenceRemoval||t.Util._isElement(e[r])){if(!i.configurable)return null;delete e[r]}else if(null===t.Util._prepareToStringify(e[r])){if(!i.configurable)return null;delete e[r]}return delete e.visitedByCircularReferenceRemoval,e},_assign(t,e){for(var i in e)t[i]=e[i];return t},_getFirstPointerId:t=>t.touches?t.changedTouches[0].identifier:t.pointerId||999,releaseCanvas(...t){e.Konva.releaseCanvasOnDestroy&&t.forEach((t=>{t.width=0,t.height=0}))},drawRoundedRectPath(t,e,i,r){let a=0,n=0,s=0,o=0;"number"==typeof r?a=n=s=o=Math.min(r,e/2,i/2):(a=Math.min(r[0]||0,e/2,i/2),n=Math.min(r[1]||0,e/2,i/2),o=Math.min(r[2]||0,e/2,i/2),s=Math.min(r[3]||0,e/2,i/2)),t.moveTo(a,0),t.lineTo(e-n,0),t.arc(e-n,n,n,3*Math.PI/2,0,!1),t.lineTo(e,i-o),t.arc(e-o,i-o,o,0,Math.PI/2,!1),t.lineTo(s,i),t.arc(s,i-s,s,Math.PI/2,Math.PI,!1),t.lineTo(0,a),t.arc(a,a,a,Math.PI,3*Math.PI/2,!1)}}}(l)),l}var c,g,u={},f={},p={};function v(){if(c)return p;c=1,Object.defineProperty(p,"__esModule",{value:!0}),p.getComponentValidator=p.getBooleanValidator=p.getNumberArrayValidator=p.getFunctionValidator=p.getStringOrGradientValidator=p.getStringValidator=p.getNumberOrAutoValidator=p.getNumberOrArrayOfNumbersValidator=p.getNumberValidator=p.alphaComponent=p.RGBComponent=void 0;const t=o(),e=d();function i(t){return e.Util._isString(t)?'"'+t+'"':"[object Number]"===Object.prototype.toString.call(t)||e.Util._isBoolean(t)?t:Object.prototype.toString.call(t)}return p.RGBComponent=function(t){return t>255?255:t<0?0:Math.round(t)},p.alphaComponent=function(t){return t>1?1:t<1e-4?1e-4:t},p.getNumberValidator=function(){if(t.Konva.isUnminified)return function(t,r){return e.Util._isNumber(t)||e.Util.warn(i(t)+' is a not valid value for "'+r+'" attribute. The value should be a number.'),t}},p.getNumberOrArrayOfNumbersValidator=function(r){if(t.Konva.isUnminified)return function(t,a){let n=e.Util._isNumber(t),s=e.Util._isArray(t)&&t.length==r;return n||s||e.Util.warn(i(t)+' is a not valid value for "'+a+'" attribute. The value should be a number or Array<number>('+r+")"),t}},p.getNumberOrAutoValidator=function(){if(t.Konva.isUnminified)return function(t,r){return e.Util._isNumber(t)||"auto"===t||e.Util.warn(i(t)+' is a not valid value for "'+r+'" attribute. The value should be a number or "auto".'),t}},p.getStringValidator=function(){if(t.Konva.isUnminified)return function(t,r){return e.Util._isString(t)||e.Util.warn(i(t)+' is a not valid value for "'+r+'" attribute. The value should be a string.'),t}},p.getStringOrGradientValidator=function(){if(t.Konva.isUnminified)return function(t,r){const a=e.Util._isString(t),n="[object CanvasGradient]"===Object.prototype.toString.call(t)||t&&t.addColorStop;return a||n||e.Util.warn(i(t)+' is a not valid value for "'+r+'" attribute. The value should be a string or a native gradient.'),t}},p.getFunctionValidator=function(){if(t.Konva.isUnminified)return function(t,r){return e.Util._isFunction(t)||e.Util.warn(i(t)+' is a not valid value for "'+r+'" attribute. The value should be a function.'),t}},p.getNumberArrayValidator=function(){if(t.Konva.isUnminified)return function(t,r){const a=Int8Array?Object.getPrototypeOf(Int8Array):null;return a&&t instanceof a||(e.Util._isArray(t)?t.forEach((function(t){e.Util._isNumber(t)||e.Util.warn('"'+r+'" attribute has non numeric element '+t+". Make sure that all elements are numbers.")})):e.Util.warn(i(t)+' is a not valid value for "'+r+'" attribute. The value should be a array of numbers.')),t}},p.getBooleanValidator=function(){if(t.Konva.isUnminified)return function(t,r){return!0===t||!1===t||e.Util.warn(i(t)+' is a not valid value for "'+r+'" attribute. The value should be a boolean.'),t}},p.getComponentValidator=function(r){if(t.Konva.isUnminified)return function(t,a){return null==t||e.Util.isObject(t)||e.Util.warn(i(t)+' is a not valid value for "'+a+'" attribute. The value should be an object with properties '+r),t}},p}function m(){return g||(g=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.Factory=void 0;const e=d(),i=v();var r="get",a="set";t.Factory={addGetterSetter(e,i,r,a,n){t.Factory.addGetter(e,i,r),t.Factory.addSetter(e,i,a,n),t.Factory.addOverloadedGetterSetter(e,i)},addGetter(t,i,a){var n=r+e.Util._capitalize(i);t.prototype[n]=t.prototype[n]||function(){var t=this.attrs[i];return void 0===t?a:t}},addSetter(i,r,n,s){var o=a+e.Util._capitalize(r);i.prototype[o]||t.Factory.overWriteSetter(i,r,n,s)},overWriteSetter(t,i,r,n){var s=a+e.Util._capitalize(i);t.prototype[s]=function(t){return r&&null!=t&&(t=r.call(this,t,i)),this._setAttr(i,t),n&&n.call(this),this}},addComponentsGetterSetter(n,s,o,h,l){var d,c,g=o.length,u=e.Util._capitalize,f=r+u(s),p=a+u(s);n.prototype[f]=function(){var t={};for(d=0;d<g;d++)t[c=o[d]]=this.getAttr(s+u(c));return t};var v=(0,i.getComponentValidator)(o);n.prototype[p]=function(t){var e,i=this.attrs[s];for(e in h&&(t=h.call(this,t)),v&&v.call(this,t,s),t)t.hasOwnProperty(e)&&this._setAttr(s+u(e),t[e]);return t||o.forEach((t=>{this._setAttr(s+u(t),void 0)})),this._fireChangeEvent(s,i,t),l&&l.call(this),this},t.Factory.addOverloadedGetterSetter(n,s)},addOverloadedGetterSetter(t,i){var n=e.Util._capitalize(i),s=a+n,o=r+n;t.prototype[i]=function(){return arguments.length?(this[s](arguments[0]),this):this[o]()}},addDeprecatedGetterSetter(i,a,n,s){e.Util.error("Adding deprecated "+a);var o=r+e.Util._capitalize(a),h=a+" property is deprecated and will be removed soon. Look at Konva change log for more information.";i.prototype[o]=function(){e.Util.error(h);var t=this.attrs[a];return void 0===t?n:t},t.Factory.addSetter(i,a,s,(function(){e.Util.error(h)})),t.Factory.addOverloadedGetterSetter(i,a)},backCompat(t,i){e.Util.each(i,(function(i,n){var s=t.prototype[n],o=r+e.Util._capitalize(i),h=a+e.Util._capitalize(i);function l(){s.apply(this,arguments),e.Util.error('"'+i+'" method is deprecated and will be removed soon. Use ""'+n+'" instead.')}t.prototype[i]=l,t.prototype[o]=l,t.prototype[h]=l}))},afterSetFilter(){this._filterUpToDate=!1}}}(f)),f}var _,y,b={},x={};function S(){if(_)return x;_=1,Object.defineProperty(x,"__esModule",{value:!0}),x.HitContext=x.SceneContext=x.Context=void 0;const t=d(),e=o();var i=["arc","arcTo","beginPath","bezierCurveTo","clearRect","clip","closePath","createLinearGradient","createPattern","createRadialGradient","drawImage","ellipse","fill","fillText","getImageData","createImageData","lineTo","moveTo","putImageData","quadraticCurveTo","rect","roundRect","restore","rotate","save","scale","setLineDash","setTransform","stroke","strokeText","transform","translate"];let r=class{constructor(t){this.canvas=t,e.Konva.enableTrace&&(this.traceArr=[],this._enableTrace())}fillShape(t){t.fillEnabled()&&this._fill(t)}_fill(t){}strokeShape(t){t.hasStroke()&&this._stroke(t)}_stroke(t){}fillStrokeShape(t){t.attrs.fillAfterStrokeEnabled?(this.strokeShape(t),this.fillShape(t)):(this.fillShape(t),this.strokeShape(t))}getTrace(e,i){var r,a,n,s,o=this.traceArr,h=o.length,l="";for(r=0;r<h;r++)(n=(a=o[r]).method)?(s=a.args,l+=n,e?l+="()":t.Util._isArray(s[0])?l+="(["+s.join(",")+"])":(i&&(s=s.map((t=>"number"==typeof t?Math.floor(t):t))),l+="("+s.join(",")+")")):(l+=a.property,e||(l+="="+a.val)),l+=";";return l}clearTrace(){this.traceArr=[]}_trace(t){var e=this.traceArr;e.push(t),e.length>=100&&e.shift()}reset(){var t=this.getCanvas().getPixelRatio();this.setTransform(1*t,0,0,1*t,0,0)}getCanvas(){return this.canvas}clear(t){var e=this.getCanvas();t?this.clearRect(t.x||0,t.y||0,t.width||0,t.height||0):this.clearRect(0,0,e.getWidth()/e.pixelRatio,e.getHeight()/e.pixelRatio)}_applyLineCap(t){const e=t.attrs.lineCap;e&&this.setAttr("lineCap",e)}_applyOpacity(t){var e=t.getAbsoluteOpacity();1!==e&&this.setAttr("globalAlpha",e)}_applyLineJoin(t){const e=t.attrs.lineJoin;e&&this.setAttr("lineJoin",e)}setAttr(t,e){this._context[t]=e}arc(t,e,i,r,a,n){this._context.arc(t,e,i,r,a,n)}arcTo(t,e,i,r,a){this._context.arcTo(t,e,i,r,a)}beginPath(){this._context.beginPath()}bezierCurveTo(t,e,i,r,a,n){this._context.bezierCurveTo(t,e,i,r,a,n)}clearRect(t,e,i,r){this._context.clearRect(t,e,i,r)}clip(...t){this._context.clip.apply(this._context,t)}closePath(){this._context.closePath()}createImageData(t,e){var i=arguments;return 2===i.length?this._context.createImageData(t,e):1===i.length?this._context.createImageData(t):void 0}createLinearGradient(t,e,i,r){return this._context.createLinearGradient(t,e,i,r)}createPattern(t,e){return this._context.createPattern(t,e)}createRadialGradient(t,e,i,r,a,n){return this._context.createRadialGradient(t,e,i,r,a,n)}drawImage(t,e,i,r,a,n,s,o,h){var l=arguments,d=this._context;3===l.length?d.drawImage(t,e,i):5===l.length?d.drawImage(t,e,i,r,a):9===l.length&&d.drawImage(t,e,i,r,a,n,s,o,h)}ellipse(t,e,i,r,a,n,s,o){this._context.ellipse(t,e,i,r,a,n,s,o)}isPointInPath(t,e,i,r){return i?this._context.isPointInPath(i,t,e,r):this._context.isPointInPath(t,e,r)}fill(...t){this._context.fill.apply(this._context,t)}fillRect(t,e,i,r){this._context.fillRect(t,e,i,r)}strokeRect(t,e,i,r){this._context.strokeRect(t,e,i,r)}fillText(t,e,i,r){r?this._context.fillText(t,e,i,r):this._context.fillText(t,e,i)}measureText(t){return this._context.measureText(t)}getImageData(t,e,i,r){return this._context.getImageData(t,e,i,r)}lineTo(t,e){this._context.lineTo(t,e)}moveTo(t,e){this._context.moveTo(t,e)}rect(t,e,i,r){this._context.rect(t,e,i,r)}roundRect(t,e,i,r,a){this._context.roundRect(t,e,i,r,a)}putImageData(t,e,i){this._context.putImageData(t,e,i)}quadraticCurveTo(t,e,i,r){this._context.quadraticCurveTo(t,e,i,r)}restore(){this._context.restore()}rotate(t){this._context.rotate(t)}save(){this._context.save()}scale(t,e){this._context.scale(t,e)}setLineDash(t){this._context.setLineDash?this._context.setLineDash(t):"mozDash"in this._context?this._context.mozDash=t:"webkitLineDash"in this._context&&(this._context.webkitLineDash=t)}getLineDash(){return this._context.getLineDash()}setTransform(t,e,i,r,a,n){this._context.setTransform(t,e,i,r,a,n)}stroke(t){t?this._context.stroke(t):this._context.stroke()}strokeText(t,e,i,r){this._context.strokeText(t,e,i,r)}transform(t,e,i,r,a,n){this._context.transform(t,e,i,r,a,n)}translate(t,e){this._context.translate(t,e)}_enableTrace(){var e,r,a=this,n=i.length,s=this.setAttr,o=function(e){var i,n=a[e];a[e]=function(){return r=function(e){var i,r,a=[],n=e.length,s=t.Util;for(i=0;i<n;i++)r=e[i],s._isNumber(r)?r=Math.round(1e3*r)/1e3:s._isString(r)||(r+=""),a.push(r);return a}(Array.prototype.slice.call(arguments,0)),i=n.apply(a,arguments),a._trace({method:e,args:r}),i}};for(e=0;e<n;e++)o(i[e]);a.setAttr=function(){s.apply(a,arguments);var t=arguments[0],e=arguments[1];"shadowOffsetX"!==t&&"shadowOffsetY"!==t&&"shadowBlur"!==t||(e/=this.canvas.getPixelRatio()),a._trace({property:t,val:e})}}_applyGlobalCompositeOperation(t){const e=t.attrs.globalCompositeOperation;!e||"source-over"===e||this.setAttr("globalCompositeOperation",e)}};x.Context=r,["fillStyle","strokeStyle","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","letterSpacing","lineCap","lineDashOffset","lineJoin","lineWidth","miterLimit","direction","font","textAlign","textBaseline","globalAlpha","globalCompositeOperation","imageSmoothingEnabled"].forEach((function(t){Object.defineProperty(r.prototype,t,{get(){return this._context[t]},set(e){this._context[t]=e}})}));x.SceneContext=class extends r{constructor(t,{willReadFrequently:e=!1}={}){super(t),this._context=t._canvas.getContext("2d",{willReadFrequently:e})}_fillColor(t){var e=t.fill();this.setAttr("fillStyle",e),t._fillFunc(this)}_fillPattern(t){this.setAttr("fillStyle",t._getFillPattern()),t._fillFunc(this)}_fillLinearGradient(t){var e=t._getLinearGradient();e&&(this.setAttr("fillStyle",e),t._fillFunc(this))}_fillRadialGradient(t){const e=t._getRadialGradient();e&&(this.setAttr("fillStyle",e),t._fillFunc(this))}_fill(t){const e=t.fill(),i=t.getFillPriority();if(e&&"color"===i)return void this._fillColor(t);const r=t.getFillPatternImage();if(r&&"pattern"===i)return void this._fillPattern(t);const a=t.getFillLinearGradientColorStops();if(a&&"linear-gradient"===i)return void this._fillLinearGradient(t);const n=t.getFillRadialGradientColorStops();n&&"radial-gradient"===i?this._fillRadialGradient(t):e?this._fillColor(t):r?this._fillPattern(t):a?this._fillLinearGradient(t):n&&this._fillRadialGradient(t)}_strokeLinearGradient(t){const e=t.getStrokeLinearGradientStartPoint(),i=t.getStrokeLinearGradientEndPoint(),r=t.getStrokeLinearGradientColorStops(),a=this.createLinearGradient(e.x,e.y,i.x,i.y);if(r){for(var n=0;n<r.length;n+=2)a.addColorStop(r[n],r[n+1]);this.setAttr("strokeStyle",a)}}_stroke(t){var e=t.dash(),i=t.getStrokeScaleEnabled();if(t.hasStroke()){if(!i){this.save();var r=this.getCanvas().getPixelRatio();this.setTransform(r,0,0,r,0,0)}this._applyLineCap(t),e&&t.dashEnabled()&&(this.setLineDash(e),this.setAttr("lineDashOffset",t.dashOffset())),this.setAttr("lineWidth",t.strokeWidth()),t.getShadowForStrokeEnabled()||this.setAttr("shadowColor","rgba(0,0,0,0)"),t.getStrokeLinearGradientColorStops()?this._strokeLinearGradient(t):this.setAttr("strokeStyle",t.stroke()),t._strokeFunc(this),i||this.restore()}}_applyShadow(t){var e,i,r,a=null!==(e=t.getShadowRGBA())&&void 0!==e?e:"black",n=null!==(i=t.getShadowBlur())&&void 0!==i?i:5,s=null!==(r=t.getShadowOffset())&&void 0!==r?r:{x:0,y:0},o=t.getAbsoluteScale(),h=this.canvas.getPixelRatio(),l=o.x*h,d=o.y*h;this.setAttr("shadowColor",a),this.setAttr("shadowBlur",n*Math.min(Math.abs(l),Math.abs(d))),this.setAttr("shadowOffsetX",s.x*l),this.setAttr("shadowOffsetY",s.y*d)}};return x.HitContext=class extends r{constructor(t){super(t),this._context=t._canvas.getContext("2d",{willReadFrequently:!0})}_fill(t){this.save(),this.setAttr("fillStyle",t.colorKey),t._fillFuncHit(this),this.restore()}strokeShape(t){t.hasHitStroke()&&this._stroke(t)}_stroke(t){if(t.hasHitStroke()){const a=t.getStrokeScaleEnabled();if(!a){this.save();var e=this.getCanvas().getPixelRatio();this.setTransform(e,0,0,e,0,0)}this._applyLineCap(t);var i=t.hitStrokeWidth(),r="auto"===i?t.strokeWidth():i;this.setAttr("lineWidth",r),this.setAttr("strokeStyle",t.colorKey),t._strokeFuncHit(this),a||this.restore()}}},x}function C(){if(y)return b;y=1,Object.defineProperty(b,"__esModule",{value:!0}),b.HitCanvas=b.SceneCanvas=b.Canvas=void 0;const t=d(),e=S(),i=o(),r=m(),a=v();var n;let s=class{constructor(e){this.pixelRatio=1,this.width=0,this.height=0,this.isCache=!1;var r=(e||{}).pixelRatio||i.Konva.pixelRatio||function(){if(n)return n;var e=t.Util.createCanvasElement(),r=e.getContext("2d");return n=(i.Konva._global.devicePixelRatio||1)/(r.webkitBackingStorePixelRatio||r.mozBackingStorePixelRatio||r.msBackingStorePixelRatio||r.oBackingStorePixelRatio||r.backingStorePixelRatio||1),t.Util.releaseCanvas(e),n}();this.pixelRatio=r,this._canvas=t.Util.createCanvasElement(),this._canvas.style.padding="0",this._canvas.style.margin="0",this._canvas.style.border="0",this._canvas.style.background="transparent",this._canvas.style.position="absolute",this._canvas.style.top="0",this._canvas.style.left="0"}getContext(){return this.context}getPixelRatio(){return this.pixelRatio}setPixelRatio(t){var e=this.pixelRatio;this.pixelRatio=t,this.setSize(this.getWidth()/e,this.getHeight()/e)}setWidth(t){this.width=this._canvas.width=t*this.pixelRatio,this._canvas.style.width=t+"px";var e=this.pixelRatio;this.getContext()._context.scale(e,e)}setHeight(t){this.height=this._canvas.height=t*this.pixelRatio,this._canvas.style.height=t+"px";var e=this.pixelRatio;this.getContext()._context.scale(e,e)}getWidth(){return this.width}getHeight(){return this.height}setSize(t,e){this.setWidth(t||0),this.setHeight(e||0)}toDataURL(e,i){try{return this._canvas.toDataURL(e,i)}catch(r){try{return this._canvas.toDataURL()}catch(a){return t.Util.error("Unable to get data URL. "+a.message+" For more info read https://konvajs.org/docs/posts/Tainted_Canvas.html."),""}}}};b.Canvas=s,r.Factory.addGetterSetter(s,"pixelRatio",void 0,(0,a.getNumberValidator)());b.SceneCanvas=class extends s{constructor(t={width:0,height:0,willReadFrequently:!1}){super(t),this.context=new e.SceneContext(this,{willReadFrequently:t.willReadFrequently}),this.setSize(t.width,t.height)}};return b.HitCanvas=class extends s{constructor(t={width:0,height:0}){super(t),this.hitCanvas=!0,this.context=new e.HitContext(this),this.setSize(t.width,t.height)}},b}var w,P,k={};function A(){return w||(w=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.DD=void 0;const e=o(),i=d();t.DD={get isDragging(){var e=!1;return t.DD._dragElements.forEach((t=>{"dragging"===t.dragStatus&&(e=!0)})),e},justDragged:!1,get node(){var e;return t.DD._dragElements.forEach((t=>{e=t.node})),e},_dragElements:new Map,_drag(e){const r=[];t.DD._dragElements.forEach(((t,a)=>{const{node:n}=t,s=n.getStage();s.setPointersPositions(e),void 0===t.pointerId&&(t.pointerId=i.Util._getFirstPointerId(e));const o=s._changedPointerPositions.find((e=>e.id===t.pointerId));if(o){if("dragging"!==t.dragStatus){var h=n.dragDistance();if(Math.max(Math.abs(o.x-t.startPointerPos.x),Math.abs(o.y-t.startPointerPos.y))<h)return;if(n.startDrag({evt:e}),!n.isDragging())return}n._setDragPosition(e,t),r.push(n)}})),r.forEach((t=>{t.fire("dragmove",{type:"dragmove",target:t,evt:e},!0)}))},_endDragBefore(i){const r=[];t.DD._dragElements.forEach((a=>{const{node:n}=a,s=n.getStage();i&&s.setPointersPositions(i);if(!s._changedPointerPositions.find((t=>t.id===a.pointerId)))return;"dragging"!==a.dragStatus&&"stopped"!==a.dragStatus||(t.DD.justDragged=!0,e.Konva._mouseListenClick=!1,e.Konva._touchListenClick=!1,e.Konva._pointerListenClick=!1,a.dragStatus="stopped");const o=a.node.getLayer()||a.node instanceof e.Konva.Stage&&a.node;o&&-1===r.indexOf(o)&&r.push(o)})),r.forEach((t=>{t.draw()}))},_endDragAfter(e){t.DD._dragElements.forEach(((i,r)=>{"stopped"===i.dragStatus&&i.node.fire("dragend",{type:"dragend",target:i.node,evt:e},!0),"dragging"!==i.dragStatus&&t.DD._dragElements.delete(r)}))}},e.Konva.isBrowser&&(window.addEventListener("mouseup",t.DD._endDragBefore,!0),window.addEventListener("touchend",t.DD._endDragBefore,!0),window.addEventListener("mousemove",t.DD._drag),window.addEventListener("touchmove",t.DD._drag),window.addEventListener("mouseup",t.DD._endDragAfter,!1),window.addEventListener("touchend",t.DD._endDragAfter,!1))}(k)),k}function T(){if(P)return u;P=1,Object.defineProperty(u,"__esModule",{value:!0}),u.Node=void 0;const t=d(),e=m(),i=C(),r=o(),a=A(),n=v();var s="absoluteOpacity",h="allEventListeners",l="absoluteTransform",c="absoluteScale",g="canvas",f="listening",p="mouseenter",_="mouseleave",y="Shape",b=" ",x="stage",S="transform",w="visible",k=["xChange.konva","yChange.konva","scaleXChange.konva","scaleYChange.konva","skewXChange.konva","skewYChange.konva","rotationChange.konva","offsetXChange.konva","offsetYChange.konva","transformsEnabledChange.konva"].join(b);let T=1,F=class e{constructor(t){this._id=T++,this.eventListeners={},this.attrs={},this.index=0,this._allEventListeners=null,this.parent=null,this._cache=new Map,this._attachedDepsListeners=new Map,this._lastPos=null,this._batchingTransformChange=!1,this._needClearTransformCache=!1,this._filterUpToDate=!1,this._isUnderCache=!1,this._dragEventId=null,this._shouldFireChangeEvents=!1,this.setAttrs(t),this._shouldFireChangeEvents=!0}hasChildren(){return!1}_clearCache(t){t!==S&&t!==l||!this._cache.get(t)?t?this._cache.delete(t):this._cache.clear():this._cache.get(t).dirty=!0}_getCache(t,e){var i=this._cache.get(t);return(void 0===i||(t===S||t===l)&&!0===i.dirty)&&(i=e.call(this),this._cache.set(t,i)),i}_calculate(t,e,i){if(!this._attachedDepsListeners.get(t)){const i=e.map((t=>t+"Change.konva")).join(b);this.on(i,(()=>{this._clearCache(t)})),this._attachedDepsListeners.set(t,!0)}return this._getCache(t,i)}_getCanvasCache(){return this._cache.get(g)}_clearSelfAndDescendantCache(t){this._clearCache(t),t===l&&this.fire("absoluteTransformChange")}clearCache(){if(this._cache.has(g)){const{scene:e,filter:i,hit:r}=this._cache.get(g);t.Util.releaseCanvas(e,i,r),this._cache.delete(g)}return this._clearSelfAndDescendantCache(),this._requestDraw(),this}cache(e){var r=e||{},a={};void 0!==r.x&&void 0!==r.y&&void 0!==r.width&&void 0!==r.height||(a=this.getClientRect({skipTransform:!0,relativeTo:this.getParent()||void 0}));var n=Math.ceil(r.width||a.width),o=Math.ceil(r.height||a.height),h=r.pixelRatio,l=void 0===r.x?Math.floor(a.x):r.x,d=void 0===r.y?Math.floor(a.y):r.y,u=r.offset||0,f=r.drawBorder||!1,p=r.hitCanvasPixelRatio||1;if(!n||!o)return void t.Util.error("Can not cache the node. Width or height of the node equals 0. Caching is skipped.");n+=2*u+(Math.abs(Math.round(a.x)-l)>.5?1:0),o+=2*u+(Math.abs(Math.round(a.y)-d)>.5?1:0),l-=u,d-=u;var v=new i.SceneCanvas({pixelRatio:h,width:n,height:o}),m=new i.SceneCanvas({pixelRatio:h,width:0,height:0,willReadFrequently:!0}),_=new i.HitCanvas({pixelRatio:p,width:n,height:o}),y=v.getContext(),b=_.getContext();return _.isCache=!0,v.isCache=!0,this._cache.delete(g),this._filterUpToDate=!1,!1===r.imageSmoothingEnabled&&(v.getContext()._context.imageSmoothingEnabled=!1,m.getContext()._context.imageSmoothingEnabled=!1),y.save(),b.save(),y.translate(-l,-d),b.translate(-l,-d),this._isUnderCache=!0,this._clearSelfAndDescendantCache(s),this._clearSelfAndDescendantCache(c),this.drawScene(v,this),this.drawHit(_,this),this._isUnderCache=!1,y.restore(),b.restore(),f&&(y.save(),y.beginPath(),y.rect(0,0,n,o),y.closePath(),y.setAttr("strokeStyle","red"),y.setAttr("lineWidth",5),y.stroke(),y.restore()),this._cache.set(g,{scene:v,filter:m,hit:_,x:l,y:d}),this._requestDraw(),this}isCached(){return this._cache.has(g)}getClientRect(t){throw new Error('abstract "getClientRect" method call')}_transformedRect(t,e){var i=[{x:t.x,y:t.y},{x:t.x+t.width,y:t.y},{x:t.x+t.width,y:t.y+t.height},{x:t.x,y:t.y+t.height}],r=1/0,a=1/0,n=-1/0,s=-1/0,o=this.getAbsoluteTransform(e);return i.forEach((function(t){var e=o.point(t);void 0===r&&(r=n=e.x,a=s=e.y),r=Math.min(r,e.x),a=Math.min(a,e.y),n=Math.max(n,e.x),s=Math.max(s,e.y)})),{x:r,y:a,width:n-r,height:s-a}}_drawCachedSceneCanvas(t){t.save(),t._applyOpacity(this),t._applyGlobalCompositeOperation(this);const e=this._getCanvasCache();t.translate(e.x,e.y);var i=this._getCachedSceneCanvas(),r=i.pixelRatio;t.drawImage(i._canvas,0,0,i.width/r,i.height/r),t.restore()}_drawCachedHitCanvas(t){var e=this._getCanvasCache(),i=e.hit;t.save(),t.translate(e.x,e.y),t.drawImage(i._canvas,0,0,i.width/i.pixelRatio,i.height/i.pixelRatio),t.restore()}_getCachedSceneCanvas(){var e,i,r,a,n=this.filters(),s=this._getCanvasCache(),o=s.scene,h=s.filter,l=h.getContext();if(n){if(!this._filterUpToDate){var d=o.pixelRatio;h.setSize(o.width/o.pixelRatio,o.height/o.pixelRatio);try{for(e=n.length,l.clear(),l.drawImage(o._canvas,0,0,o.getWidth()/d,o.getHeight()/d),i=l.getImageData(0,0,h.getWidth(),h.getHeight()),r=0;r<e;r++)"function"==typeof(a=n[r])?(a.call(this,i),l.putImageData(i,0,0)):t.Util.error("Filter should be type of function, but got "+typeof a+" instead. Please check correct filters")}catch(c){t.Util.error("Unable to apply filter. "+c.message+" This post my help you https://konvajs.org/docs/posts/Tainted_Canvas.html.")}this._filterUpToDate=!0}return h}return o}on(t,e){if(this._cache&&this._cache.delete(h),3===arguments.length)return this._delegate.apply(this,arguments);var i,r,a,n,s=t.split(b),o=s.length;for(i=0;i<o;i++)a=(r=s[i].split("."))[0],n=r[1]||"",this.eventListeners[a]||(this.eventListeners[a]=[]),this.eventListeners[a].push({name:n,handler:e});return this}off(t,e){var i,r,a,n,s,o=(t||"").split(b),l=o.length;if(this._cache&&this._cache.delete(h),!t)for(r in this.eventListeners)this._off(r);for(i=0;i<l;i++)if(n=(a=o[i].split("."))[0],s=a[1],n)this.eventListeners[n]&&this._off(n,s,e);else for(r in this.eventListeners)this._off(r,s,e);return this}dispatchEvent(t){var e={target:this,type:t.type,evt:t};return this.fire(t.type,e),this}addEventListener(t,e){return this.on(t,(function(t){e.call(this,t.evt)})),this}removeEventListener(t){return this.off(t),this}_delegate(e,i,r){var a=this;this.on(e,(function(e){for(var n=e.target.findAncestors(i,!0,a),s=0;s<n.length;s++)(e=t.Util.cloneObject(e)).currentTarget=n[s],r.call(n[s],e)}))}remove(){return this.isDragging()&&this.stopDrag(),a.DD._dragElements.delete(this._id),this._remove(),this}_clearCaches(){this._clearSelfAndDescendantCache(l),this._clearSelfAndDescendantCache(s),this._clearSelfAndDescendantCache(c),this._clearSelfAndDescendantCache(x),this._clearSelfAndDescendantCache(w),this._clearSelfAndDescendantCache(f)}_remove(){this._clearCaches();var t=this.getParent();t&&t.children&&(t.children.splice(this.index,1),t._setChildrenIndices(),this.parent=null)}destroy(){return this.remove(),this.clearCache(),this}getAttr(e){var i="get"+t.Util._capitalize(e);return t.Util._isFunction(this[i])?this[i]():this.attrs[e]}getAncestors(){for(var t=this.getParent(),e=[];t;)e.push(t),t=t.getParent();return e}getAttrs(){return this.attrs||{}}setAttrs(e){return this._batchTransformChanges((()=>{var i,r;if(!e)return this;for(i in e)"children"!==i&&(r="set"+t.Util._capitalize(i),t.Util._isFunction(this[r])?this[r](e[i]):this._setAttr(i,e[i]))})),this}isListening(){return this._getCache(f,this._isListening)}_isListening(t){if(!this.listening())return!1;const e=this.getParent();return!e||e===t||this===t||e._isListening(t)}isVisible(){return this._getCache(w,this._isVisible)}_isVisible(t){if(!this.visible())return!1;const e=this.getParent();return!e||e===t||this===t||e._isVisible(t)}shouldDrawHit(t,e=!1){if(t)return this._isVisible(t)&&this._isListening(t);var i=this.getLayer(),n=!1;a.DD._dragElements.forEach((t=>{"dragging"===t.dragStatus&&("Stage"===t.node.nodeType||t.node.getLayer()===i)&&(n=!0)}));var s=!e&&!r.Konva.hitOnDragEnabled&&(n||r.Konva.isTransforming());return this.isListening()&&this.isVisible()&&!s}show(){return this.visible(!0),this}hide(){return this.visible(!1),this}getZIndex(){return this.index||0}getAbsoluteZIndex(){var t,e,i,r,a=this.getDepth(),n=this,s=0;const o=this.getStage();return"Stage"!==n.nodeType&&o&&function o(h){for(t=[],e=h.length,i=0;i<e;i++)r=h[i],s++,r.nodeType!==y&&(t=t.concat(r.getChildren().slice())),r._id===n._id&&(i=e);t.length>0&&t[0].getDepth()<=a&&o(t)}(o.getChildren()),s}getDepth(){for(var t=0,e=this.parent;e;)t++,e=e.parent;return t}_batchTransformChanges(t){this._batchingTransformChange=!0,t(),this._batchingTransformChange=!1,this._needClearTransformCache&&(this._clearCache(S),this._clearSelfAndDescendantCache(l)),this._needClearTransformCache=!1}setPosition(t){return this._batchTransformChanges((()=>{this.x(t.x),this.y(t.y)})),this}getPosition(){return{x:this.x(),y:this.y()}}getRelativePointerPosition(){const t=this.getStage();if(!t)return null;var e=t.getPointerPosition();if(!e)return null;var i=this.getAbsoluteTransform().copy();return i.invert(),i.point(e)}getAbsolutePosition(e){let i=!1,r=this.parent;for(;r;){if(r.isCached()){i=!0;break}r=r.parent}i&&!e&&(e=!0);var a=this.getAbsoluteTransform(e).getMatrix(),n=new t.Transform,s=this.offset();return n.m=a.slice(),n.translate(s.x,s.y),n.getTranslation()}setAbsolutePosition(t){const{x:e,y:i,...r}=this._clearTransform();this.attrs.x=e,this.attrs.y=i,this._clearCache(S);var a=this._getAbsoluteTransform().copy();return a.invert(),a.translate(t.x,t.y),t={x:this.attrs.x+a.getTranslation().x,y:this.attrs.y+a.getTranslation().y},this._setTransform(r),this.setPosition({x:t.x,y:t.y}),this._clearCache(S),this._clearSelfAndDescendantCache(l),this}_setTransform(t){var e;for(e in t)this.attrs[e]=t[e]}_clearTransform(){var t={x:this.x(),y:this.y(),rotation:this.rotation(),scaleX:this.scaleX(),scaleY:this.scaleY(),offsetX:this.offsetX(),offsetY:this.offsetY(),skewX:this.skewX(),skewY:this.skewY()};return this.attrs.x=0,this.attrs.y=0,this.attrs.rotation=0,this.attrs.scaleX=1,this.attrs.scaleY=1,this.attrs.offsetX=0,this.attrs.offsetY=0,this.attrs.skewX=0,this.attrs.skewY=0,t}move(t){var e=t.x,i=t.y,r=this.x(),a=this.y();return void 0!==e&&(r+=e),void 0!==i&&(a+=i),this.setPosition({x:r,y:a}),this}_eachAncestorReverse(t,e){var i,r,a=[],n=this.getParent();if(!e||e._id!==this._id){for(a.unshift(this);n&&(!e||n._id!==e._id);)a.unshift(n),n=n.parent;for(i=a.length,r=0;r<i;r++)t(a[r])}}rotate(t){return this.rotation(this.rotation()+t),this}moveToTop(){if(!this.parent)return t.Util.warn("Node has no parent. moveToTop function is ignored."),!1;var e=this.index;return e<this.parent.getChildren().length-1&&(this.parent.children.splice(e,1),this.parent.children.push(this),this.parent._setChildrenIndices(),!0)}moveUp(){if(!this.parent)return t.Util.warn("Node has no parent. moveUp function is ignored."),!1;var e=this.index;return e<this.parent.getChildren().length-1&&(this.parent.children.splice(e,1),this.parent.children.splice(e+1,0,this),this.parent._setChildrenIndices(),!0)}moveDown(){if(!this.parent)return t.Util.warn("Node has no parent. moveDown function is ignored."),!1;var e=this.index;return e>0&&(this.parent.children.splice(e,1),this.parent.children.splice(e-1,0,this),this.parent._setChildrenIndices(),!0)}moveToBottom(){if(!this.parent)return t.Util.warn("Node has no parent. moveToBottom function is ignored."),!1;var e=this.index;return e>0&&(this.parent.children.splice(e,1),this.parent.children.unshift(this),this.parent._setChildrenIndices(),!0)}setZIndex(e){if(!this.parent)return t.Util.warn("Node has no parent. zIndex parameter is ignored."),this;(e<0||e>=this.parent.children.length)&&t.Util.warn("Unexpected value "+e+" for zIndex property. zIndex is just index of a node in children of its parent. Expected value is from 0 to "+(this.parent.children.length-1)+".");var i=this.index;return this.parent.children.splice(i,1),this.parent.children.splice(e,0,this),this.parent._setChildrenIndices(),this}getAbsoluteOpacity(){return this._getCache(s,this._getAbsoluteOpacity)}_getAbsoluteOpacity(){var t=this.opacity(),e=this.getParent();return e&&!e._isUnderCache&&(t*=e.getAbsoluteOpacity()),t}moveTo(t){return this.getParent()!==t&&(this._remove(),t.add(this)),this}toObject(){var e,i,r,a,n=this.getAttrs();const s={attrs:{},className:this.getClassName()};for(e in n)i=n[e],t.Util.isObject(i)&&!t.Util._isPlainObject(i)&&!t.Util._isArray(i)||(r="function"==typeof this[e]&&this[e],delete n[e],a=r?r.call(this):null,n[e]=i,a!==i&&(s.attrs[e]=i));return t.Util._prepareToStringify(s)}toJSON(){return JSON.stringify(this.toObject())}getParent(){return this.parent}findAncestors(t,e,i){var r=[];e&&this._isMatch(t)&&r.push(this);for(var a=this.parent;a;){if(a===i)return r;a._isMatch(t)&&r.push(a),a=a.parent}return r}isAncestorOf(t){return!1}findAncestor(t,e,i){return this.findAncestors(t,e,i)[0]}_isMatch(e){if(!e)return!1;if("function"==typeof e)return e(this);var i,r,a=e.replace(/ /g,"").split(","),n=a.length;for(i=0;i<n;i++)if(r=a[i],t.Util.isValidSelector(r)||(t.Util.warn('Selector "'+r+'" is invalid. Allowed selectors examples are "#foo", ".bar" or "Group".'),t.Util.warn('If you have a custom shape with such className, please change it to start with upper letter like "Triangle".'),t.Util.warn("Konva is awesome, right?")),"#"===r.charAt(0)){if(this.id()===r.slice(1))return!0}else if("."===r.charAt(0)){if(this.hasName(r.slice(1)))return!0}else if(this.className===r||this.nodeType===r)return!0;return!1}getLayer(){var t=this.getParent();return t?t.getLayer():null}getStage(){return this._getCache(x,this._getStage)}_getStage(){var t=this.getParent();return t?t.getStage():null}fire(t,e={},i){return e.target=e.target||this,i?this._fireAndBubble(t,e):this._fire(t,e),this}getAbsoluteTransform(t){return t?this._getAbsoluteTransform(t):this._getCache(l,this._getAbsoluteTransform)}_getAbsoluteTransform(e){var i;if(e)return i=new t.Transform,this._eachAncestorReverse((function(t){var e=t.transformsEnabled();"all"===e?i.multiply(t.getTransform()):"position"===e&&i.translate(t.x()-t.offsetX(),t.y()-t.offsetY())}),e),i;i=this._cache.get(l)||new t.Transform,this.parent?this.parent.getAbsoluteTransform().copyInto(i):i.reset();var r=this.transformsEnabled();if("all"===r)i.multiply(this.getTransform());else if("position"===r){const t=this.attrs.x||0,e=this.attrs.y||0,r=this.attrs.offsetX||0,a=this.attrs.offsetY||0;i.translate(t-r,e-a)}return i.dirty=!1,i}getAbsoluteScale(t){for(var e=this;e;)e._isUnderCache&&(t=e),e=e.getParent();const i=this.getAbsoluteTransform(t).decompose();return{x:i.scaleX,y:i.scaleY}}getAbsoluteRotation(){return this.getAbsoluteTransform().decompose().rotation}getTransform(){return this._getCache(S,this._getTransform)}_getTransform(){var e,i,a=this._cache.get(S)||new t.Transform;a.reset();var n=this.x(),s=this.y(),o=r.Konva.getAngle(this.rotation()),h=null!==(e=this.attrs.scaleX)&&void 0!==e?e:1,l=null!==(i=this.attrs.scaleY)&&void 0!==i?i:1,d=this.attrs.skewX||0,c=this.attrs.skewY||0,g=this.attrs.offsetX||0,u=this.attrs.offsetY||0;return 0===n&&0===s||a.translate(n,s),0!==o&&a.rotate(o),0===d&&0===c||a.skew(d,c),1===h&&1===l||a.scale(h,l),0===g&&0===u||a.translate(-1*g,-1*u),a.dirty=!1,a}clone(e){var i,r,a,n,s,o=t.Util.cloneObject(this.attrs);for(i in e)o[i]=e[i];var h=new this.constructor(o);for(i in this.eventListeners)for(a=(r=this.eventListeners[i]).length,n=0;n<a;n++)(s=r[n]).name.indexOf("konva")<0&&(h.eventListeners[i]||(h.eventListeners[i]=[]),h.eventListeners[i].push(s));return h}_toKonvaCanvas(t){t=t||{};var e=this.getClientRect(),r=this.getStage(),a=void 0!==t.x?t.x:Math.floor(e.x),n=void 0!==t.y?t.y:Math.floor(e.y),s=t.pixelRatio||1,o=new i.SceneCanvas({width:t.width||Math.ceil(e.width)||(r?r.width():0),height:t.height||Math.ceil(e.height)||(r?r.height():0),pixelRatio:s}),h=o.getContext();const l=new i.SceneCanvas({width:o.width/o.pixelRatio+Math.abs(a),height:o.height/o.pixelRatio+Math.abs(n),pixelRatio:o.pixelRatio});return!1===t.imageSmoothingEnabled&&(h._context.imageSmoothingEnabled=!1),h.save(),(a||n)&&h.translate(-1*a,-1*n),this.drawScene(o,void 0,l),h.restore(),o}toCanvas(t){return this._toKonvaCanvas(t)._canvas}toDataURL(t){var e=(t=t||{}).mimeType||null,i=t.quality||null,r=this._toKonvaCanvas(t).toDataURL(e,i);return t.callback&&t.callback(r),r}toImage(e){return new Promise(((i,r)=>{try{const r=null==e?void 0:e.callback;r&&delete e.callback,t.Util._urlToImage(this.toDataURL(e),(function(t){i(t),null==r||r(t)}))}catch(a){r(a)}}))}toBlob(t){return new Promise(((e,i)=>{try{const i=null==t?void 0:t.callback;i&&delete t.callback,this.toCanvas(t).toBlob((t=>{e(t),null==i||i(t)}),null==t?void 0:t.mimeType,null==t?void 0:t.quality)}catch(r){i(r)}}))}setSize(t){return this.width(t.width),this.height(t.height),this}getSize(){return{width:this.width(),height:this.height()}}getClassName(){return this.className||this.nodeType}getType(){return this.nodeType}getDragDistance(){return void 0!==this.attrs.dragDistance?this.attrs.dragDistance:this.parent?this.parent.getDragDistance():r.Konva.dragDistance}_off(t,e,i){var r,a,n,s=this.eventListeners[t];for(r=0;r<s.length;r++)if(a=s[r].name,n=s[r].handler,!("konva"===a&&"konva"!==e||e&&a!==e||i&&i!==n)){if(s.splice(r,1),0===s.length){delete this.eventListeners[t];break}r--}}_fireChangeEvent(t,e,i){this._fire(t+"Change",{oldVal:e,newVal:i})}addName(t){if(!this.hasName(t)){var e=this.name(),i=e?e+" "+t:t;this.name(i)}return this}hasName(t){if(!t)return!1;const e=this.name();return!!e&&-1!==(e||"").split(/\s/g).indexOf(t)}removeName(t){var e=(this.name()||"").split(/\s/g),i=e.indexOf(t);return-1!==i&&(e.splice(i,1),this.name(e.join(" "))),this}setAttr(e,i){var r=this["set"+t.Util._capitalize(e)];return t.Util._isFunction(r)?r.call(this,i):this._setAttr(e,i),this}_requestDraw(){if(r.Konva.autoDrawEnabled){const t=this.getLayer()||this.getStage();null==t||t.batchDraw()}}_setAttr(e,i){var r=this.attrs[e];(r!==i||t.Util.isObject(i))&&(null==i?delete this.attrs[e]:this.attrs[e]=i,this._shouldFireChangeEvents&&this._fireChangeEvent(e,r,i),this._requestDraw())}_setComponentAttr(t,e,i){var r;void 0!==i&&((r=this.attrs[t])||(this.attrs[t]=this.getAttr(t)),this.attrs[t][e]=i,this._fireChangeEvent(t,r,i))}_fireAndBubble(t,e,i){if(e&&this.nodeType===y&&(e.target=this),!((t===p||t===_)&&(i&&(this===i||this.isAncestorOf&&this.isAncestorOf(i))||"Stage"===this.nodeType&&!i))){this._fire(t,e);var r=(t===p||t===_)&&i&&i.isAncestorOf&&i.isAncestorOf(this)&&!i.isAncestorOf(this.parent);(e&&!e.cancelBubble||!e)&&this.parent&&this.parent.isListening()&&!r&&(i&&i.parent?this._fireAndBubble.call(this.parent,t,e,i):this._fireAndBubble.call(this.parent,t,e))}}_getProtoListeners(t){var e,i,r;const a=null!==(e=this._cache.get(h))&&void 0!==e?e:{};let n=null==a?void 0:a[t];if(void 0===n){n=[];let e=Object.getPrototypeOf(this);for(;e;){const a=null!==(r=null===(i=e.eventListeners)||void 0===i?void 0:i[t])&&void 0!==r?r:[];n.push(...a),e=Object.getPrototypeOf(e)}a[t]=n,this._cache.set(h,a)}return n}_fire(t,e){(e=e||{}).currentTarget=this,e.type=t;const i=this._getProtoListeners(t);if(i)for(var r=0;r<i.length;r++)i[r].handler.call(this,e);const a=this.eventListeners[t];if(a)for(r=0;r<a.length;r++)a[r].handler.call(this,e)}draw(){return this.drawScene(),this.drawHit(),this}_createDragElement(t){var e=t?t.pointerId:void 0,i=this.getStage(),r=this.getAbsolutePosition();if(i){var n=i._getPointerById(e)||i._changedPointerPositions[0]||r;a.DD._dragElements.set(this._id,{node:this,startPointerPos:n,offset:{x:n.x-r.x,y:n.y-r.y},dragStatus:"ready",pointerId:e})}}startDrag(t,e=!0){a.DD._dragElements.has(this._id)||this._createDragElement(t);a.DD._dragElements.get(this._id).dragStatus="dragging",this.fire("dragstart",{type:"dragstart",target:this,evt:t&&t.evt},e)}_setDragPosition(e,i){const r=this.getStage()._getPointerById(i.pointerId);if(r){var a={x:r.x-i.offset.x,y:r.y-i.offset.y},n=this.dragBoundFunc();if(void 0!==n){const i=n.call(this,a,e);i?a=i:t.Util.warn("dragBoundFunc did not return any value. That is unexpected behavior. You must return new absolute position from dragBoundFunc.")}this._lastPos&&this._lastPos.x===a.x&&this._lastPos.y===a.y||(this.setAbsolutePosition(a),this._requestDraw()),this._lastPos=a}}stopDrag(t){const e=a.DD._dragElements.get(this._id);e&&(e.dragStatus="stopped"),a.DD._endDragBefore(t),a.DD._endDragAfter(t)}setDraggable(t){this._setAttr("draggable",t),this._dragChange()}isDragging(){const t=a.DD._dragElements.get(this._id);return!!t&&"dragging"===t.dragStatus}_listenDrag(){this._dragCleanup(),this.on("mousedown.konva touchstart.konva",(function(t){if((!(void 0!==t.evt.button)||r.Konva.dragButtons.indexOf(t.evt.button)>=0)&&!this.isDragging()){var e=!1;a.DD._dragElements.forEach((t=>{this.isAncestorOf(t.node)&&(e=!0)})),e||this._createDragElement(t)}}))}_dragChange(){if(this.attrs.draggable)this._listenDrag();else{if(this._dragCleanup(),!this.getStage())return;const t=a.DD._dragElements.get(this._id),e=t&&"dragging"===t.dragStatus,i=t&&"ready"===t.dragStatus;e?this.stopDrag():i&&a.DD._dragElements.delete(this._id)}}_dragCleanup(){this.off("mousedown.konva"),this.off("touchstart.konva")}isClientRectOnScreen(e={x:0,y:0}){const i=this.getStage();if(!i)return!1;const r={x:-e.x,y:-e.y,width:i.width()+2*e.x,height:i.height()+2*e.y};return t.Util.haveIntersection(r,this.getClientRect())}static create(e,i){return t.Util._isString(e)&&(e=JSON.parse(e)),this._createNode(e,i)}static _createNode(i,a){var n,s,o,h=e.prototype.getClassName.call(i),l=i.children;a&&(i.attrs.container=a),r.Konva[h]||(t.Util.warn('Can not find a node with class name "'+h+'". Fallback to "Shape".'),h="Shape");if(n=new(0,r.Konva[h])(i.attrs),l)for(s=l.length,o=0;o<s;o++)n.add(e._createNode(l[o]));return n}};u.Node=F,F.prototype.nodeType="Node",F.prototype._attrsAffectingSize=[],F.prototype.eventListeners={},F.prototype.on.call(F.prototype,k,(function(){this._batchingTransformChange?this._needClearTransformCache=!0:(this._clearCache(S),this._clearSelfAndDescendantCache(l))})),F.prototype.on.call(F.prototype,"visibleChange.konva",(function(){this._clearSelfAndDescendantCache(w)})),F.prototype.on.call(F.prototype,"listeningChange.konva",(function(){this._clearSelfAndDescendantCache(f)})),F.prototype.on.call(F.prototype,"opacityChange.konva",(function(){this._clearSelfAndDescendantCache(s)}));const M=e.Factory.addGetterSetter;return M(F,"zIndex"),M(F,"absolutePosition"),M(F,"position"),M(F,"x",0,(0,n.getNumberValidator)()),M(F,"y",0,(0,n.getNumberValidator)()),M(F,"globalCompositeOperation","source-over",(0,n.getStringValidator)()),M(F,"opacity",1,(0,n.getNumberValidator)()),M(F,"name","",(0,n.getStringValidator)()),M(F,"id","",(0,n.getStringValidator)()),M(F,"rotation",0,(0,n.getNumberValidator)()),e.Factory.addComponentsGetterSetter(F,"scale",["x","y"]),M(F,"scaleX",1,(0,n.getNumberValidator)()),M(F,"scaleY",1,(0,n.getNumberValidator)()),e.Factory.addComponentsGetterSetter(F,"skew",["x","y"]),M(F,"skewX",0,(0,n.getNumberValidator)()),M(F,"skewY",0,(0,n.getNumberValidator)()),e.Factory.addComponentsGetterSetter(F,"offset",["x","y"]),M(F,"offsetX",0,(0,n.getNumberValidator)()),M(F,"offsetY",0,(0,n.getNumberValidator)()),M(F,"dragDistance",null,(0,n.getNumberValidator)()),M(F,"width",0,(0,n.getNumberValidator)()),M(F,"height",0,(0,n.getNumberValidator)()),M(F,"listening",!0,(0,n.getBooleanValidator)()),M(F,"preventDefault",!0,(0,n.getBooleanValidator)()),M(F,"filters",null,(function(t){return this._filterUpToDate=!1,t})),M(F,"visible",!0,(0,n.getBooleanValidator)()),M(F,"transformsEnabled","all",(0,n.getStringValidator)()),M(F,"size"),M(F,"dragBoundFunc"),M(F,"draggable",!1,(0,n.getBooleanValidator)()),e.Factory.backCompat(F,{rotateDeg:"rotate",setRotationDeg:"setRotation",getRotationDeg:"getRotation"}),u}var F,M={};function G(){if(F)return M;F=1,Object.defineProperty(M,"__esModule",{value:!0}),M.Container=void 0;const t=m(),e=T(),i=v();let r=class extends e.Node{constructor(){super(...arguments),this.children=[]}getChildren(t){if(!t)return this.children||[];const e=this.children||[];var i=[];return e.forEach((function(e){t(e)&&i.push(e)})),i}hasChildren(){return this.getChildren().length>0}removeChildren(){return this.getChildren().forEach((t=>{t.parent=null,t.index=0,t.remove()})),this.children=[],this._requestDraw(),this}destroyChildren(){return this.getChildren().forEach((t=>{t.parent=null,t.index=0,t.destroy()})),this.children=[],this._requestDraw(),this}add(...t){if(0===t.length)return this;if(t.length>1){for(var e=0;e<t.length;e++)this.add(t[e]);return this}const i=t[0];return i.getParent()?(i.moveTo(this),this):(this._validateAdd(i),i.index=this.getChildren().length,i.parent=this,i._clearCaches(),this.getChildren().push(i),this._fire("add",{child:i}),this._requestDraw(),this)}destroy(){return this.hasChildren()&&this.destroyChildren(),super.destroy(),this}find(t){return this._generalFind(t,!1)}findOne(t){var e=this._generalFind(t,!0);return e.length>0?e[0]:void 0}_generalFind(t,e){var i=[];return this._descendants((r=>{const a=r._isMatch(t);return a&&i.push(r),!(!a||!e)})),i}_descendants(t){let e=!1;const i=this.getChildren();for(const r of i){if(e=t(r),e)return!0;if(r.hasChildren()&&(e=r._descendants(t),e))return!0}return!1}toObject(){var t=e.Node.prototype.toObject.call(this);return t.children=[],this.getChildren().forEach((e=>{t.children.push(e.toObject())})),t}isAncestorOf(t){for(var e=t.getParent();e;){if(e._id===this._id)return!0;e=e.getParent()}return!1}clone(t){var i=e.Node.prototype.clone.call(this,t);return this.getChildren().forEach((function(t){i.add(t.clone())})),i}getAllIntersections(t){var e=[];return this.find("Shape").forEach((i=>{i.isVisible()&&i.intersects(t)&&e.push(i)})),e}_clearSelfAndDescendantCache(t){var e;super._clearSelfAndDescendantCache(t),this.isCached()||null===(e=this.children)||void 0===e||e.forEach((function(e){e._clearSelfAndDescendantCache(t)}))}_setChildrenIndices(){var t;null===(t=this.children)||void 0===t||t.forEach((function(t,e){t.index=e})),this._requestDraw()}drawScene(t,e,i){var r=this.getLayer(),a=t||r&&r.getCanvas(),n=a&&a.getContext(),s=this._getCanvasCache(),o=s&&s.scene,h=a&&a.isCache;if(!this.isVisible()&&!h)return this;if(o){n.save();var l=this.getAbsoluteTransform(e).getMatrix();n.transform(l[0],l[1],l[2],l[3],l[4],l[5]),this._drawCachedSceneCanvas(n),n.restore()}else this._drawChildren("drawScene",a,e,i);return this}drawHit(t,e){if(!this.shouldDrawHit(e))return this;var i=this.getLayer(),r=t||i&&i.hitCanvas,a=r&&r.getContext(),n=this._getCanvasCache();if(n&&n.hit){a.save();var s=this.getAbsoluteTransform(e).getMatrix();a.transform(s[0],s[1],s[2],s[3],s[4],s[5]),this._drawCachedHitCanvas(a),a.restore()}else this._drawChildren("drawHit",r,e);return this}_drawChildren(t,e,i,r){var a,n=e&&e.getContext(),s=this.clipWidth(),o=this.clipHeight(),h=this.clipFunc(),l="number"==typeof s&&"number"==typeof o||h;const d=i===this;if(l){n.save();var c=this.getAbsoluteTransform(i),g=c.getMatrix();let t;if(n.transform(g[0],g[1],g[2],g[3],g[4],g[5]),n.beginPath(),h)t=h.call(this,n,this);else{var u=this.clipX(),f=this.clipY();n.rect(u||0,f||0,s,o)}n.clip.apply(n,t),g=c.copy().invert().getMatrix(),n.transform(g[0],g[1],g[2],g[3],g[4],g[5])}var p=!d&&"source-over"!==this.globalCompositeOperation()&&"drawScene"===t;p&&(n.save(),n._applyGlobalCompositeOperation(this)),null===(a=this.children)||void 0===a||a.forEach((function(a){a[t](e,i,r)})),p&&n.restore(),l&&n.restore()}getClientRect(t={}){var e,i,r,a,n,s=t.skipTransform,o=t.relativeTo,h={x:1/0,y:1/0,width:0,height:0},l=this;null===(e=this.children)||void 0===e||e.forEach((function(e){if(e.visible()){var s=e.getClientRect({relativeTo:l,skipShadow:t.skipShadow,skipStroke:t.skipStroke});0===s.width&&0===s.height||(void 0===i?(i=s.x,r=s.y,a=s.x+s.width,n=s.y+s.height):(i=Math.min(i,s.x),r=Math.min(r,s.y),a=Math.max(a,s.x+s.width),n=Math.max(n,s.y+s.height)))}}));for(var d=this.find("Shape"),c=!1,g=0;g<d.length;g++){if(d[g]._isVisible(this)){c=!0;break}}return h=c&&void 0!==i?{x:i,y:r,width:a-i,height:n-r}:{x:0,y:0,width:0,height:0},s?h:this._transformedRect(h,o)}};return M.Container=r,t.Factory.addComponentsGetterSetter(r,"clip",["x","y","width","height"]),t.Factory.addGetterSetter(r,"clipX",void 0,(0,i.getNumberValidator)()),t.Factory.addGetterSetter(r,"clipY",void 0,(0,i.getNumberValidator)()),t.Factory.addGetterSetter(r,"clipWidth",void 0,(0,i.getNumberValidator)()),t.Factory.addGetterSetter(r,"clipHeight",void 0,(0,i.getNumberValidator)()),t.Factory.addGetterSetter(r,"clipFunc"),M}var R,D,N={},E={};function O(){if(R)return E;R=1,Object.defineProperty(E,"__esModule",{value:!0}),E.releaseCapture=E.setPointerCapture=E.hasPointerCapture=E.createEvent=E.getCapturedShape=void 0;const t=o(),e=new Map,i=void 0!==t.Konva._global.PointerEvent;function r(t){return{evt:t,pointerId:t.pointerId}}function a(t,a){const n=e.get(t);if(!n)return;const s=n.getStage();s&&s.content,e.delete(t),i&&n._fire("lostpointercapture",r(new PointerEvent("lostpointercapture")))}return E.getCapturedShape=function(t){return e.get(t)},E.createEvent=r,E.hasPointerCapture=function(t,i){return e.get(t)===i},E.setPointerCapture=function(t,n){a(t),n.getStage()&&(e.set(t,n),i&&n._fire("gotpointercapture",r(new PointerEvent("gotpointercapture"))))},E.releaseCapture=a,E}var L,I,U={},B={};function V(){return L||(L=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.Shape=t.shapes=void 0;const e=o(),i=d(),r=m(),a=T(),n=v(),s=o(),h=O();var l="hasShadow",c="shadowRGBA",g="patternImage",u="linearGradient",f="radialGradient";let p;function _(){return p||(p=i.Util.createCanvasElement().getContext("2d"),p)}t.shapes={};class y extends a.Node{constructor(e){let r;for(super(e);r=i.Util.getRandomColor(),!r||r in t.shapes;);this.colorKey=r,t.shapes[r]=this}getContext(){return i.Util.warn("shape.getContext() method is deprecated. Please do not use it."),this.getLayer().getContext()}getCanvas(){return i.Util.warn("shape.getCanvas() method is deprecated. Please do not use it."),this.getLayer().getCanvas()}getSceneFunc(){return this.attrs.sceneFunc||this._sceneFunc}getHitFunc(){return this.attrs.hitFunc||this._hitFunc}hasShadow(){return this._getCache(l,this._hasShadow)}_hasShadow(){return this.shadowEnabled()&&0!==this.shadowOpacity()&&!!(this.shadowColor()||this.shadowBlur()||this.shadowOffsetX()||this.shadowOffsetY())}_getFillPattern(){return this._getCache(g,this.__getFillPattern)}__getFillPattern(){if(this.fillPatternImage()){const t=_().createPattern(this.fillPatternImage(),this.fillPatternRepeat()||"repeat");if(t&&t.setTransform){const r=new i.Transform;r.translate(this.fillPatternX(),this.fillPatternY()),r.rotate(e.Konva.getAngle(this.fillPatternRotation())),r.scale(this.fillPatternScaleX(),this.fillPatternScaleY()),r.translate(-1*this.fillPatternOffsetX(),-1*this.fillPatternOffsetY());const a=r.getMatrix(),n="undefined"==typeof DOMMatrix?{a:a[0],b:a[1],c:a[2],d:a[3],e:a[4],f:a[5]}:new DOMMatrix(a);t.setTransform(n)}return t}}_getLinearGradient(){return this._getCache(u,this.__getLinearGradient)}__getLinearGradient(){var t=this.fillLinearGradientColorStops();if(t){for(var e=_(),i=this.fillLinearGradientStartPoint(),r=this.fillLinearGradientEndPoint(),a=e.createLinearGradient(i.x,i.y,r.x,r.y),n=0;n<t.length;n+=2)a.addColorStop(t[n],t[n+1]);return a}}_getRadialGradient(){return this._getCache(f,this.__getRadialGradient)}__getRadialGradient(){var t=this.fillRadialGradientColorStops();if(t){for(var e=_(),i=this.fillRadialGradientStartPoint(),r=this.fillRadialGradientEndPoint(),a=e.createRadialGradient(i.x,i.y,this.fillRadialGradientStartRadius(),r.x,r.y,this.fillRadialGradientEndRadius()),n=0;n<t.length;n+=2)a.addColorStop(t[n],t[n+1]);return a}}getShadowRGBA(){return this._getCache(c,this._getShadowRGBA)}_getShadowRGBA(){if(this.hasShadow()){var t=i.Util.colorToRGBA(this.shadowColor());return t?"rgba("+t.r+","+t.g+","+t.b+","+t.a*(this.shadowOpacity()||1)+")":void 0}}hasFill(){return this._calculate("hasFill",["fillEnabled","fill","fillPatternImage","fillLinearGradientColorStops","fillRadialGradientColorStops"],(()=>this.fillEnabled()&&!!(this.fill()||this.fillPatternImage()||this.fillLinearGradientColorStops()||this.fillRadialGradientColorStops())))}hasStroke(){return this._calculate("hasStroke",["strokeEnabled","strokeWidth","stroke","strokeLinearGradientColorStops"],(()=>this.strokeEnabled()&&this.strokeWidth()&&!(!this.stroke()&&!this.strokeLinearGradientColorStops())))}hasHitStroke(){const t=this.hitStrokeWidth();return"auto"===t?this.hasStroke():this.strokeEnabled()&&!!t}intersects(t){var e=this.getStage();if(!e)return!1;const i=e.bufferHitCanvas;i.getContext().clear(),this.drawHit(i,void 0,!0);return i.context.getImageData(Math.round(t.x),Math.round(t.y),1,1).data[3]>0}destroy(){return a.Node.prototype.destroy.call(this),delete t.shapes[this.colorKey],delete this.colorKey,this}_useBufferCanvas(t){var e;if(!(null===(e=this.attrs.perfectDrawEnabled)||void 0===e||e))return!1;const i=t||this.hasFill(),r=this.hasStroke(),a=1!==this.getAbsoluteOpacity();if(i&&r&&a)return!0;const n=this.hasShadow(),s=this.shadowForStrokeEnabled();return!!(i&&r&&n&&s)}setStrokeHitEnabled(t){i.Util.warn("strokeHitEnabled property is deprecated. Please use hitStrokeWidth instead."),t?this.hitStrokeWidth("auto"):this.hitStrokeWidth(0)}getStrokeHitEnabled(){return 0!==this.hitStrokeWidth()}getSelfRect(){var t=this.size();return{x:this._centroid?-t.width/2:0,y:this._centroid?-t.height/2:0,width:t.width,height:t.height}}getClientRect(t={}){let e=!1,i=this.getParent();for(;i;){if(i.isCached()){e=!0;break}i=i.getParent()}const r=t.skipTransform,a=t.relativeTo||e&&this.getStage()||void 0,n=this.getSelfRect(),s=!t.skipStroke&&this.hasStroke()&&this.strokeWidth()||0,o=n.width+s,h=n.height+s,l=!t.skipShadow&&this.hasShadow(),d=l?this.shadowOffsetX():0,c=l?this.shadowOffsetY():0,g=o+Math.abs(d),u=h+Math.abs(c),f=l&&this.shadowBlur()||0,p={width:g+2*f,height:u+2*f,x:-(s/2+f)+Math.min(d,0)+n.x,y:-(s/2+f)+Math.min(c,0)+n.y};return r?p:this._transformedRect(p,a)}drawScene(t,e,i){var r,a,n=this.getLayer(),s=t||n.getCanvas(),o=s.getContext(),h=this._getCanvasCache(),l=this.getSceneFunc(),d=this.hasShadow(),c=s.isCache,g=e===this;if(!this.isVisible()&&!g)return this;if(h){o.save();var u=this.getAbsoluteTransform(e).getMatrix();return o.transform(u[0],u[1],u[2],u[3],u[4],u[5]),this._drawCachedSceneCanvas(o),o.restore(),this}if(!l)return this;if(o.save(),this._useBufferCanvas()&&!c){r=this.getStage();const t=i||r.bufferCanvas;(a=t.getContext()).clear(),a.save(),a._applyLineJoin(this);var f=this.getAbsoluteTransform(e).getMatrix();a.transform(f[0],f[1],f[2],f[3],f[4],f[5]),l.call(this,a,this),a.restore();var p=t.pixelRatio;d&&o._applyShadow(this),o._applyOpacity(this),o._applyGlobalCompositeOperation(this),o.drawImage(t._canvas,0,0,t.width/p,t.height/p)}else{if(o._applyLineJoin(this),!g){f=this.getAbsoluteTransform(e).getMatrix();o.transform(f[0],f[1],f[2],f[3],f[4],f[5]),o._applyOpacity(this),o._applyGlobalCompositeOperation(this)}d&&o._applyShadow(this),l.call(this,o,this)}return o.restore(),this}drawHit(t,e,r=!1){if(!this.shouldDrawHit(e,r))return this;var a=this.getLayer(),n=t||a.hitCanvas,s=n&&n.getContext(),o=this.hitFunc()||this.sceneFunc(),h=this._getCanvasCache(),l=h&&h.hit;if(this.colorKey||i.Util.warn("Looks like your canvas has a destroyed shape in it. Do not reuse shape after you destroyed it. If you want to reuse shape you should call remove() instead of destroy()"),l){s.save();var d=this.getAbsoluteTransform(e).getMatrix();return s.transform(d[0],d[1],d[2],d[3],d[4],d[5]),this._drawCachedHitCanvas(s),s.restore(),this}if(!o)return this;s.save(),s._applyLineJoin(this);if(!(this===e)){var c=this.getAbsoluteTransform(e).getMatrix();s.transform(c[0],c[1],c[2],c[3],c[4],c[5])}return o.call(this,s,this),s.restore(),this}drawHitFromCache(t=0){var e,r,a,n,s,o=this._getCanvasCache(),h=this._getCachedSceneCanvas(),l=o.hit,d=l.getContext(),c=l.getWidth(),g=l.getHeight();d.clear(),d.drawImage(h._canvas,0,0,c,g);try{for(a=(r=(e=d.getImageData(0,0,c,g)).data).length,n=i.Util._hexToRgb(this.colorKey),s=0;s<a;s+=4)r[s+3]>t?(r[s]=n.r,r[s+1]=n.g,r[s+2]=n.b,r[s+3]=255):r[s+3]=0;d.putImageData(e,0,0)}catch(u){i.Util.error("Unable to draw hit graph from cached scene canvas. "+u.message)}return this}hasPointerCapture(t){return h.hasPointerCapture(t,this)}setPointerCapture(t){h.setPointerCapture(t,this)}releaseCapture(t){h.releaseCapture(t,this)}}t.Shape=y,y.prototype._fillFunc=function(t){const e=this.attrs.fillRule;e?t.fill(e):t.fill()},y.prototype._strokeFunc=function(t){t.stroke()},y.prototype._fillFuncHit=function(t){t.fill()},y.prototype._strokeFuncHit=function(t){t.stroke()},y.prototype._centroid=!1,y.prototype.nodeType="Shape",(0,s._registerNode)(y),y.prototype.eventListeners={},y.prototype.on.call(y.prototype,"shadowColorChange.konva shadowBlurChange.konva shadowOffsetChange.konva shadowOpacityChange.konva shadowEnabledChange.konva",(function(){this._clearCache(l)})),y.prototype.on.call(y.prototype,"shadowColorChange.konva shadowOpacityChange.konva shadowEnabledChange.konva",(function(){this._clearCache(c)})),y.prototype.on.call(y.prototype,"fillPriorityChange.konva fillPatternImageChange.konva fillPatternRepeatChange.konva fillPatternScaleXChange.konva fillPatternScaleYChange.konva fillPatternOffsetXChange.konva fillPatternOffsetYChange.konva fillPatternXChange.konva fillPatternYChange.konva fillPatternRotationChange.konva",(function(){this._clearCache(g)})),y.prototype.on.call(y.prototype,"fillPriorityChange.konva fillLinearGradientColorStopsChange.konva fillLinearGradientStartPointXChange.konva fillLinearGradientStartPointYChange.konva fillLinearGradientEndPointXChange.konva fillLinearGradientEndPointYChange.konva",(function(){this._clearCache(u)})),y.prototype.on.call(y.prototype,"fillPriorityChange.konva fillRadialGradientColorStopsChange.konva fillRadialGradientStartPointXChange.konva fillRadialGradientStartPointYChange.konva fillRadialGradientEndPointXChange.konva fillRadialGradientEndPointYChange.konva fillRadialGradientStartRadiusChange.konva fillRadialGradientEndRadiusChange.konva",(function(){this._clearCache(f)})),r.Factory.addGetterSetter(y,"stroke",void 0,(0,n.getStringOrGradientValidator)()),r.Factory.addGetterSetter(y,"strokeWidth",2,(0,n.getNumberValidator)()),r.Factory.addGetterSetter(y,"fillAfterStrokeEnabled",!1),r.Factory.addGetterSetter(y,"hitStrokeWidth","auto",(0,n.getNumberOrAutoValidator)()),r.Factory.addGetterSetter(y,"strokeHitEnabled",!0,(0,n.getBooleanValidator)()),r.Factory.addGetterSetter(y,"perfectDrawEnabled",!0,(0,n.getBooleanValidator)()),r.Factory.addGetterSetter(y,"shadowForStrokeEnabled",!0,(0,n.getBooleanValidator)()),r.Factory.addGetterSetter(y,"lineJoin"),r.Factory.addGetterSetter(y,"lineCap"),r.Factory.addGetterSetter(y,"sceneFunc"),r.Factory.addGetterSetter(y,"hitFunc"),r.Factory.addGetterSetter(y,"dash"),r.Factory.addGetterSetter(y,"dashOffset",0,(0,n.getNumberValidator)()),r.Factory.addGetterSetter(y,"shadowColor",void 0,(0,n.getStringValidator)()),r.Factory.addGetterSetter(y,"shadowBlur",0,(0,n.getNumberValidator)()),r.Factory.addGetterSetter(y,"shadowOpacity",1,(0,n.getNumberValidator)()),r.Factory.addComponentsGetterSetter(y,"shadowOffset",["x","y"]),r.Factory.addGetterSetter(y,"shadowOffsetX",0,(0,n.getNumberValidator)()),r.Factory.addGetterSetter(y,"shadowOffsetY",0,(0,n.getNumberValidator)()),r.Factory.addGetterSetter(y,"fillPatternImage"),r.Factory.addGetterSetter(y,"fill",void 0,(0,n.getStringOrGradientValidator)()),r.Factory.addGetterSetter(y,"fillPatternX",0,(0,n.getNumberValidator)()),r.Factory.addGetterSetter(y,"fillPatternY",0,(0,n.getNumberValidator)()),r.Factory.addGetterSetter(y,"fillLinearGradientColorStops"),r.Factory.addGetterSetter(y,"strokeLinearGradientColorStops"),r.Factory.addGetterSetter(y,"fillRadialGradientStartRadius",0),r.Factory.addGetterSetter(y,"fillRadialGradientEndRadius",0),r.Factory.addGetterSetter(y,"fillRadialGradientColorStops"),r.Factory.addGetterSetter(y,"fillPatternRepeat","repeat"),r.Factory.addGetterSetter(y,"fillEnabled",!0),r.Factory.addGetterSetter(y,"strokeEnabled",!0),r.Factory.addGetterSetter(y,"shadowEnabled",!0),r.Factory.addGetterSetter(y,"dashEnabled",!0),r.Factory.addGetterSetter(y,"strokeScaleEnabled",!0),r.Factory.addGetterSetter(y,"fillPriority","color"),r.Factory.addComponentsGetterSetter(y,"fillPatternOffset",["x","y"]),r.Factory.addGetterSetter(y,"fillPatternOffsetX",0,(0,n.getNumberValidator)()),r.Factory.addGetterSetter(y,"fillPatternOffsetY",0,(0,n.getNumberValidator)()),r.Factory.addComponentsGetterSetter(y,"fillPatternScale",["x","y"]),r.Factory.addGetterSetter(y,"fillPatternScaleX",1,(0,n.getNumberValidator)()),r.Factory.addGetterSetter(y,"fillPatternScaleY",1,(0,n.getNumberValidator)()),r.Factory.addComponentsGetterSetter(y,"fillLinearGradientStartPoint",["x","y"]),r.Factory.addComponentsGetterSetter(y,"strokeLinearGradientStartPoint",["x","y"]),r.Factory.addGetterSetter(y,"fillLinearGradientStartPointX",0),r.Factory.addGetterSetter(y,"strokeLinearGradientStartPointX",0),r.Factory.addGetterSetter(y,"fillLinearGradientStartPointY",0),r.Factory.addGetterSetter(y,"strokeLinearGradientStartPointY",0),r.Factory.addComponentsGetterSetter(y,"fillLinearGradientEndPoint",["x","y"]),r.Factory.addComponentsGetterSetter(y,"strokeLinearGradientEndPoint",["x","y"]),r.Factory.addGetterSetter(y,"fillLinearGradientEndPointX",0),r.Factory.addGetterSetter(y,"strokeLinearGradientEndPointX",0),r.Factory.addGetterSetter(y,"fillLinearGradientEndPointY",0),r.Factory.addGetterSetter(y,"strokeLinearGradientEndPointY",0),r.Factory.addComponentsGetterSetter(y,"fillRadialGradientStartPoint",["x","y"]),r.Factory.addGetterSetter(y,"fillRadialGradientStartPointX",0),r.Factory.addGetterSetter(y,"fillRadialGradientStartPointY",0),r.Factory.addComponentsGetterSetter(y,"fillRadialGradientEndPoint",["x","y"]),r.Factory.addGetterSetter(y,"fillRadialGradientEndPointX",0),r.Factory.addGetterSetter(y,"fillRadialGradientEndPointY",0),r.Factory.addGetterSetter(y,"fillPatternRotation",0),r.Factory.addGetterSetter(y,"fillRule",void 0,(0,n.getStringValidator)()),r.Factory.backCompat(y,{dashArray:"dash",getDashArray:"getDash",setDashArray:"getDash",drawFunc:"sceneFunc",getDrawFunc:"getSceneFunc",setDrawFunc:"setSceneFunc",drawHitFunc:"hitFunc",getDrawHitFunc:"getHitFunc",setDrawHitFunc:"setHitFunc"})}(B)),B}function H(){if(I)return U;I=1,Object.defineProperty(U,"__esModule",{value:!0}),U.Layer=void 0;const t=d(),e=G(),i=T(),r=m(),a=C(),n=v(),s=V(),h=o();var l=[{x:0,y:0},{x:-1,y:-1},{x:1,y:-1},{x:1,y:1},{x:-1,y:1}],c=l.length;let g=class extends e.Container{constructor(t){super(t),this.canvas=new a.SceneCanvas,this.hitCanvas=new a.HitCanvas({pixelRatio:1}),this._waitingForDraw=!1,this.on("visibleChange.konva",this._checkVisibility),this._checkVisibility(),this.on("imageSmoothingEnabledChange.konva",this._setSmoothEnabled),this._setSmoothEnabled()}createPNGStream(){return this.canvas._canvas.createPNGStream()}getCanvas(){return this.canvas}getNativeCanvasElement(){return this.canvas._canvas}getHitCanvas(){return this.hitCanvas}getContext(){return this.getCanvas().getContext()}clear(t){return this.getContext().clear(t),this.getHitCanvas().getContext().clear(t),this}setZIndex(t){super.setZIndex(t);var e=this.getStage();return e&&e.content&&(e.content.removeChild(this.getNativeCanvasElement()),t<e.children.length-1?e.content.insertBefore(this.getNativeCanvasElement(),e.children[t+1].getCanvas()._canvas):e.content.appendChild(this.getNativeCanvasElement())),this}moveToTop(){i.Node.prototype.moveToTop.call(this);var t=this.getStage();return t&&t.content&&(t.content.removeChild(this.getNativeCanvasElement()),t.content.appendChild(this.getNativeCanvasElement())),!0}moveUp(){if(!i.Node.prototype.moveUp.call(this))return!1;var t=this.getStage();return!(!t||!t.content)&&(t.content.removeChild(this.getNativeCanvasElement()),this.index<t.children.length-1?t.content.insertBefore(this.getNativeCanvasElement(),t.children[this.index+1].getCanvas()._canvas):t.content.appendChild(this.getNativeCanvasElement()),!0)}moveDown(){if(i.Node.prototype.moveDown.call(this)){var t=this.getStage();if(t){var e=t.children;t.content&&(t.content.removeChild(this.getNativeCanvasElement()),t.content.insertBefore(this.getNativeCanvasElement(),e[this.index+1].getCanvas()._canvas))}return!0}return!1}moveToBottom(){if(i.Node.prototype.moveToBottom.call(this)){var t=this.getStage();if(t){var e=t.children;t.content&&(t.content.removeChild(this.getNativeCanvasElement()),t.content.insertBefore(this.getNativeCanvasElement(),e[1].getCanvas()._canvas))}return!0}return!1}getLayer(){return this}remove(){var e=this.getNativeCanvasElement();return i.Node.prototype.remove.call(this),e&&e.parentNode&&t.Util._isInDocument(e)&&e.parentNode.removeChild(e),this}getStage(){return this.parent}setSize({width:t,height:e}){return this.canvas.setSize(t,e),this.hitCanvas.setSize(t,e),this._setSmoothEnabled(),this}_validateAdd(e){var i=e.getType();"Group"!==i&&"Shape"!==i&&t.Util.throw("You may only add groups and shapes to a layer.")}_toKonvaCanvas(t){return(t=t||{}).width=t.width||this.getWidth(),t.height=t.height||this.getHeight(),t.x=void 0!==t.x?t.x:this.x(),t.y=void 0!==t.y?t.y:this.y(),i.Node.prototype._toKonvaCanvas.call(this,t)}_checkVisibility(){const t=this.visible();this.canvas._canvas.style.display=t?"block":"none"}_setSmoothEnabled(){this.getContext()._context.imageSmoothingEnabled=this.imageSmoothingEnabled()}getWidth(){if(this.parent)return this.parent.width()}setWidth(){t.Util.warn('Can not change width of layer. Use "stage.width(value)" function instead.')}getHeight(){if(this.parent)return this.parent.height()}setHeight(){t.Util.warn('Can not change height of layer. Use "stage.height(value)" function instead.')}batchDraw(){return this._waitingForDraw||(this._waitingForDraw=!0,t.Util.requestAnimFrame((()=>{this.draw(),this._waitingForDraw=!1}))),this}getIntersection(t){if(!this.isListening()||!this.isVisible())return null;for(var e=1,i=!1;;){for(let r=0;r<c;r++){const a=l[r],n=this._getIntersection({x:t.x+a.x*e,y:t.y+a.y*e}),s=n.shape;if(s)return s;if(i=!!n.antialiased,!n.antialiased)break}if(!i)return null;e+=1}}_getIntersection(e){const i=this.hitCanvas.pixelRatio,r=this.hitCanvas.context.getImageData(Math.round(e.x*i),Math.round(e.y*i),1,1).data,a=r[3];if(255===a){const e=t.Util._rgbToHex(r[0],r[1],r[2]),i=s.shapes["#"+e];return i?{shape:i}:{antialiased:!0}}return a>0?{antialiased:!0}:{}}drawScene(t,i){var r=this.getLayer(),a=t||r&&r.getCanvas();return this._fire("beforeDraw",{node:this}),this.clearBeforeDraw()&&a.getContext().clear(),e.Container.prototype.drawScene.call(this,a,i),this._fire("draw",{node:this}),this}drawHit(t,i){var r=this.getLayer(),a=t||r&&r.hitCanvas;return r&&r.clearBeforeDraw()&&r.getHitCanvas().getContext().clear(),e.Container.prototype.drawHit.call(this,a,i),this}enableHitGraph(){return this.hitGraphEnabled(!0),this}disableHitGraph(){return this.hitGraphEnabled(!1),this}setHitGraphEnabled(e){t.Util.warn("hitGraphEnabled method is deprecated. Please use layer.listening() instead."),this.listening(e)}getHitGraphEnabled(e){return t.Util.warn("hitGraphEnabled method is deprecated. Please use layer.listening() instead."),this.listening()}toggleHitCanvas(){if(this.parent&&this.parent.content){var t=this.parent;!!this.hitCanvas._canvas.parentNode?t.content.removeChild(this.hitCanvas._canvas):t.content.appendChild(this.hitCanvas._canvas)}}destroy(){return t.Util.releaseCanvas(this.getNativeCanvasElement(),this.getHitCanvas()._canvas),super.destroy()}};return U.Layer=g,g.prototype.nodeType="Layer",(0,h._registerNode)(g),r.Factory.addGetterSetter(g,"imageSmoothingEnabled",!0),r.Factory.addGetterSetter(g,"clearBeforeDraw",!0),r.Factory.addGetterSetter(g,"hitGraphEnabled",!0,(0,n.getBooleanValidator)()),U}var j,z={};var W,K={};function Y(){if(W)return K;W=1,Object.defineProperty(K,"__esModule",{value:!0}),K.Group=void 0;const t=d(),e=G(),i=o();let r=class extends e.Container{_validateAdd(e){var i=e.getType();"Group"!==i&&"Shape"!==i&&t.Util.throw("You may only add groups and shapes to groups.")}};return K.Group=r,r.prototype.nodeType="Group",(0,i._registerNode)(r),K}var X,q={};function Q(){if(X)return q;X=1,Object.defineProperty(q,"__esModule",{value:!0}),q.Animation=void 0;const t=o(),e=d(),i=t.glob.performance&&t.glob.performance.now?function(){return t.glob.performance.now()}:function(){return(new Date).getTime()};let r=class t{constructor(e,r){this.id=t.animIdCounter++,this.frame={time:0,timeDiff:0,lastTime:i(),frameRate:0},this.func=e,this.setLayers(r)}setLayers(t){let e=[];return t&&(e=Array.isArray(t)?t:[t]),this.layers=e,this}getLayers(){return this.layers}addLayer(t){const e=this.layers,i=e.length;for(let r=0;r<i;r++)if(e[r]._id===t._id)return!1;return this.layers.push(t),!0}isRunning(){const e=t.animations,i=e.length;for(let t=0;t<i;t++)if(e[t].id===this.id)return!0;return!1}start(){return this.stop(),this.frame.timeDiff=0,this.frame.lastTime=i(),t._addAnimation(this),this}stop(){return t._removeAnimation(this),this}_updateFrameObject(t){this.frame.timeDiff=t-this.frame.lastTime,this.frame.lastTime=t,this.frame.time+=this.frame.timeDiff,this.frame.frameRate=1e3/this.frame.timeDiff}static _addAnimation(t){this.animations.push(t),this._handleAnimation()}static _removeAnimation(t){const e=t.id,i=this.animations,r=i.length;for(let a=0;a<r;a++)if(i[a].id===e){this.animations.splice(a,1);break}}static _runFrames(){const t={},e=this.animations;for(let r=0;r<e.length;r++){const a=e[r],n=a.layers,s=a.func;a._updateFrameObject(i());const o=n.length;let h;if(h=!s||!1!==s.call(a,a.frame),h)for(let e=0;e<o;e++){const i=n[e];void 0!==i._id&&(t[i._id]=i)}}for(let i in t)t.hasOwnProperty(i)&&t[i].batchDraw()}static _animationLoop(){const i=t;i.animations.length?(i._runFrames(),e.Util.requestAnimFrame(i._animationLoop)):i.animRunning=!1}static _handleAnimation(){this.animRunning||(this.animRunning=!0,e.Util.requestAnimFrame(this._animationLoop))}};return q.Animation=r,r.animations=[],r.animIdCounter=0,r.animRunning=!1,q}var J,$,Z={};function tt(){return $||($=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.Konva=void 0;const e=o(),i=d(),r=T(),a=G(),n=(D||(D=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.Stage=t.stages=void 0;const e=d(),i=m(),r=G(),a=o(),n=C(),s=A(),h=o(),l=O();var c="mouseleave",g="mouseover",u="mouseenter",f="mousemove",p="mousedown",v="mouseup",_="pointermove",y="pointerdown",b="pointerup",x="pointercancel",S="pointerout",w="pointerleave",P="pointerover",k="pointerenter",T="contextmenu",F="touchstart",M="touchend",R="touchmove",D="touchcancel",N="wheel",E=[[u,"_pointerenter"],[p,"_pointerdown"],[f,"_pointermove"],[v,"_pointerup"],[c,"_pointerleave"],[F,"_pointerdown"],[R,"_pointermove"],[M,"_pointerup"],[D,"_pointercancel"],[g,"_pointerover"],[N,"_wheel"],[T,"_contextmenu"],[y,"_pointerdown"],[_,"_pointermove"],[b,"_pointerup"],[x,"_pointercancel"],["lostpointercapture","_lostpointercapture"]];const L={mouse:{[S]:"mouseout",[w]:c,[P]:g,[k]:u,[_]:f,[y]:p,[b]:v,[x]:"mousecancel",pointerclick:"click",pointerdblclick:"dblclick"},touch:{[S]:"touchout",[w]:"touchleave",[P]:"touchover",[k]:"touchenter",[_]:R,[y]:F,[b]:M,[x]:D,pointerclick:"tap",pointerdblclick:"dbltap"},pointer:{[S]:S,[w]:w,[P]:P,[k]:k,[_]:_,[y]:y,[b]:b,[x]:x,pointerclick:"pointerclick",pointerdblclick:"pointerdblclick"}},I=t=>t.indexOf("pointer")>=0?"pointer":t.indexOf("touch")>=0?"touch":"mouse",U=t=>{const e=I(t);return"pointer"===e?a.Konva.pointerEventsEnabled&&L.pointer:"touch"===e?L.touch:"mouse"===e?L.mouse:void 0};function B(t={}){return(t.clipFunc||t.clipWidth||t.clipHeight)&&e.Util.warn("Stage does not support clipping. Please use clip for Layers or Groups."),t}t.stages=[];class V extends r.Container{constructor(e){super(B(e)),this._pointerPositions=[],this._changedPointerPositions=[],this._buildDOM(),this._bindContentEvents(),t.stages.push(this),this.on("widthChange.konva heightChange.konva",this._resizeDOM),this.on("visibleChange.konva",this._checkVisibility),this.on("clipWidthChange.konva clipHeightChange.konva clipFuncChange.konva",(()=>{B(this.attrs)})),this._checkVisibility()}_validateAdd(t){const i="Layer"===t.getType(),r="FastLayer"===t.getType();i||r||e.Util.throw("You may only add layers to the stage.")}_checkVisibility(){if(!this.content)return;const t=this.visible()?"":"none";this.content.style.display=t}setContainer(t){if("string"==typeof t){if("."===t.charAt(0)){var e=t.slice(1);t=document.getElementsByClassName(e)[0]}else{var i;i="#"!==t.charAt(0)?t:t.slice(1),t=document.getElementById(i)}if(!t)throw"Can not find container in document with id "+i}return this._setAttr("container",t),this.content&&(this.content.parentElement&&this.content.parentElement.removeChild(this.content),t.appendChild(this.content)),this}shouldDrawHit(){return!0}clear(){var t,e=this.children,i=e.length;for(t=0;t<i;t++)e[t].clear();return this}clone(t){return t||(t={}),t.container="undefined"!=typeof document&&document.createElement("div"),r.Container.prototype.clone.call(this,t)}destroy(){super.destroy();var i=this.content;i&&e.Util._isInDocument(i)&&this.container().removeChild(i);var r=t.stages.indexOf(this);return r>-1&&t.stages.splice(r,1),e.Util.releaseCanvas(this.bufferCanvas._canvas,this.bufferHitCanvas._canvas),this}getPointerPosition(){const t=this._pointerPositions[0]||this._changedPointerPositions[0];return t?{x:t.x,y:t.y}:(e.Util.warn("Pointer position is missing and not registered by the stage. Looks like it is outside of the stage container. You can set it manually from event: stage.setPointersPositions(event);"),null)}_getPointerById(t){return this._pointerPositions.find((e=>e.id===t))}getPointersPositions(){return this._pointerPositions}getStage(){return this}getContent(){return this.content}_toKonvaCanvas(t){(t=t||{}).x=t.x||0,t.y=t.y||0,t.width=t.width||this.width(),t.height=t.height||this.height();var e=new n.SceneCanvas({width:t.width,height:t.height,pixelRatio:t.pixelRatio||1}),i=e.getContext()._context,r=this.children;return(t.x||t.y)&&i.translate(-1*t.x,-1*t.y),r.forEach((function(e){if(e.isVisible()){var r=e._toKonvaCanvas(t);i.drawImage(r._canvas,t.x,t.y,r.getWidth()/r.getPixelRatio(),r.getHeight()/r.getPixelRatio())}})),e}getIntersection(t){if(!t)return null;var e,i=this.children;for(e=i.length-1;e>=0;e--){const r=i[e].getIntersection(t);if(r)return r}return null}_resizeDOM(){var t=this.width(),e=this.height();this.content&&(this.content.style.width=t+"px",this.content.style.height=e+"px"),this.bufferCanvas.setSize(t,e),this.bufferHitCanvas.setSize(t,e),this.children.forEach((i=>{i.setSize({width:t,height:e}),i.draw()}))}add(t,...i){if(arguments.length>1){for(var r=0;r<arguments.length;r++)this.add(arguments[r]);return this}super.add(t);var n=this.children.length;return n>5&&e.Util.warn("The stage has "+n+" layers. Recommended maximum number of layers is 3-5. Adding more layers into the stage may drop the performance. Rethink your tree structure, you can use Konva.Group."),t.setSize({width:this.width(),height:this.height()}),t.draw(),a.Konva.isBrowser&&this.content.appendChild(t.canvas._canvas),this}getParent(){return null}getLayer(){return null}hasPointerCapture(t){return l.hasPointerCapture(t,this)}setPointerCapture(t){l.setPointerCapture(t,this)}releaseCapture(t){l.releaseCapture(t,this)}getLayers(){return this.children}_bindContentEvents(){a.Konva.isBrowser&&E.forEach((([t,e])=>{this.content.addEventListener(t,(t=>{this[e](t)}),{passive:!1})}))}_pointerenter(t){this.setPointersPositions(t);const e=U(t.type);e&&this._fire(e.pointerenter,{evt:t,target:this,currentTarget:this})}_pointerover(t){this.setPointersPositions(t);const e=U(t.type);e&&this._fire(e.pointerover,{evt:t,target:this,currentTarget:this})}_getTargetShape(t){let e=this[t+"targetShape"];return e&&!e.getStage()&&(e=null),e}_pointerleave(t){const e=U(t.type),i=I(t.type);if(e){this.setPointersPositions(t);var r=this._getTargetShape(i),n=!(a.Konva.isDragging()||a.Konva.isTransforming())||a.Konva.hitOnDragEnabled;r&&n?(r._fireAndBubble(e.pointerout,{evt:t}),r._fireAndBubble(e.pointerleave,{evt:t}),this._fire(e.pointerleave,{evt:t,target:this,currentTarget:this}),this[i+"targetShape"]=null):n&&(this._fire(e.pointerleave,{evt:t,target:this,currentTarget:this}),this._fire(e.pointerout,{evt:t,target:this,currentTarget:this})),this.pointerPos=null,this._pointerPositions=[]}}_pointerdown(t){const e=U(t.type),i=I(t.type);if(e){this.setPointersPositions(t);var r=!1;this._changedPointerPositions.forEach((n=>{var o=this.getIntersection(n);if(s.DD.justDragged=!1,a.Konva["_"+i+"ListenClick"]=!0,!o||!o.isListening())return void(this[i+"ClickStartShape"]=void 0);a.Konva.capturePointerEventsEnabled&&o.setPointerCapture(n.id),this[i+"ClickStartShape"]=o,o._fireAndBubble(e.pointerdown,{evt:t,pointerId:n.id}),r=!0;const h=t.type.indexOf("touch")>=0;o.preventDefault()&&t.cancelable&&h&&t.preventDefault()})),r||this._fire(e.pointerdown,{evt:t,target:this,currentTarget:this,pointerId:this._pointerPositions[0].id})}}_pointermove(t){const e=U(t.type),i=I(t.type);if(!e)return;if(a.Konva.isDragging()&&s.DD.node.preventDefault()&&t.cancelable&&t.preventDefault(),this.setPointersPositions(t),(a.Konva.isDragging()||a.Konva.isTransforming())&&!a.Konva.hitOnDragEnabled)return;var r={};let n=!1;var o=this._getTargetShape(i);this._changedPointerPositions.forEach((a=>{const s=l.getCapturedShape(a.id)||this.getIntersection(a),h=a.id,d={evt:t,pointerId:h};var c=o!==s;if(c&&o&&(o._fireAndBubble(e.pointerout,{...d},s),o._fireAndBubble(e.pointerleave,{...d},s)),s){if(r[s._id])return;r[s._id]=!0}s&&s.isListening()?(n=!0,c&&(s._fireAndBubble(e.pointerover,{...d},o),s._fireAndBubble(e.pointerenter,{...d},o),this[i+"targetShape"]=s),s._fireAndBubble(e.pointermove,{...d})):o&&(this._fire(e.pointerover,{evt:t,target:this,currentTarget:this,pointerId:h}),this[i+"targetShape"]=null)})),n||this._fire(e.pointermove,{evt:t,target:this,currentTarget:this,pointerId:this._changedPointerPositions[0].id})}_pointerup(t){const e=U(t.type),i=I(t.type);if(!e)return;this.setPointersPositions(t);const r=this[i+"ClickStartShape"],n=this[i+"ClickEndShape"];var o={};let h=!1;this._changedPointerPositions.forEach((d=>{const c=l.getCapturedShape(d.id)||this.getIntersection(d);if(c){if(c.releaseCapture(d.id),o[c._id])return;o[c._id]=!0}const g=d.id,u={evt:t,pointerId:g};let f=!1;a.Konva["_"+i+"InDblClickWindow"]?(f=!0,clearTimeout(this[i+"DblTimeout"])):s.DD.justDragged||(a.Konva["_"+i+"InDblClickWindow"]=!0,clearTimeout(this[i+"DblTimeout"])),this[i+"DblTimeout"]=setTimeout((function(){a.Konva["_"+i+"InDblClickWindow"]=!1}),a.Konva.dblClickWindow),c&&c.isListening()?(h=!0,this[i+"ClickEndShape"]=c,c._fireAndBubble(e.pointerup,{...u}),a.Konva["_"+i+"ListenClick"]&&r&&r===c&&(c._fireAndBubble(e.pointerclick,{...u}),f&&n&&n===c&&c._fireAndBubble(e.pointerdblclick,{...u}))):(this[i+"ClickEndShape"]=null,a.Konva["_"+i+"ListenClick"]&&this._fire(e.pointerclick,{evt:t,target:this,currentTarget:this,pointerId:g}),f&&this._fire(e.pointerdblclick,{evt:t,target:this,currentTarget:this,pointerId:g}))})),h||this._fire(e.pointerup,{evt:t,target:this,currentTarget:this,pointerId:this._changedPointerPositions[0].id}),a.Konva["_"+i+"ListenClick"]=!1,t.cancelable&&"touch"!==i&&t.preventDefault()}_contextmenu(t){this.setPointersPositions(t);var e=this.getIntersection(this.getPointerPosition());e&&e.isListening()?e._fireAndBubble(T,{evt:t}):this._fire(T,{evt:t,target:this,currentTarget:this})}_wheel(t){this.setPointersPositions(t);var e=this.getIntersection(this.getPointerPosition());e&&e.isListening()?e._fireAndBubble(N,{evt:t}):this._fire(N,{evt:t,target:this,currentTarget:this})}_pointercancel(t){this.setPointersPositions(t);const e=l.getCapturedShape(t.pointerId)||this.getIntersection(this.getPointerPosition());e&&e._fireAndBubble(b,l.createEvent(t)),l.releaseCapture(t.pointerId)}_lostpointercapture(t){l.releaseCapture(t.pointerId)}setPointersPositions(t){var i=this._getContentPosition(),r=null,a=null;void 0!==(t=t||window.event).touches?(this._pointerPositions=[],this._changedPointerPositions=[],Array.prototype.forEach.call(t.touches,(t=>{this._pointerPositions.push({id:t.identifier,x:(t.clientX-i.left)/i.scaleX,y:(t.clientY-i.top)/i.scaleY})})),Array.prototype.forEach.call(t.changedTouches||t.touches,(t=>{this._changedPointerPositions.push({id:t.identifier,x:(t.clientX-i.left)/i.scaleX,y:(t.clientY-i.top)/i.scaleY})}))):(r=(t.clientX-i.left)/i.scaleX,a=(t.clientY-i.top)/i.scaleY,this.pointerPos={x:r,y:a},this._pointerPositions=[{x:r,y:a,id:e.Util._getFirstPointerId(t)}],this._changedPointerPositions=[{x:r,y:a,id:e.Util._getFirstPointerId(t)}])}_setPointerPosition(t){e.Util.warn('Method _setPointerPosition is deprecated. Use "stage.setPointersPositions(event)" instead.'),this.setPointersPositions(t)}_getContentPosition(){if(!this.content||!this.content.getBoundingClientRect)return{top:0,left:0,scaleX:1,scaleY:1};var t=this.content.getBoundingClientRect();return{top:t.top,left:t.left,scaleX:t.width/this.content.clientWidth||1,scaleY:t.height/this.content.clientHeight||1}}_buildDOM(){if(this.bufferCanvas=new n.SceneCanvas({width:this.width(),height:this.height()}),this.bufferHitCanvas=new n.HitCanvas({pixelRatio:1,width:this.width(),height:this.height()}),a.Konva.isBrowser){var t=this.container();if(!t)throw"Stage has no container. A container is required.";t.innerHTML="",this.content=document.createElement("div"),this.content.style.position="relative",this.content.style.userSelect="none",this.content.className="konvajs-content",this.content.setAttribute("role","presentation"),t.appendChild(this.content),this._resizeDOM()}}cache(){return e.Util.warn("Cache function is not allowed for stage. You may use cache only for layers, groups and shapes."),this}clearCache(){return this}batchDraw(){return this.getChildren().forEach((function(t){t.batchDraw()})),this}}t.Stage=V,V.prototype.nodeType="Stage",(0,h._registerNode)(V),i.Factory.addGetterSetter(V,"container"),a.Konva.isBrowser&&document.addEventListener("visibilitychange",(()=>{t.stages.forEach((t=>{t.batchDraw()}))}))}(N)),N),s=H(),h=function(){if(j)return z;j=1,Object.defineProperty(z,"__esModule",{value:!0}),z.FastLayer=void 0;const t=d(),e=H(),i=o();let r=class extends e.Layer{constructor(e){super(e),this.listening(!1),t.Util.warn('Konva.Fast layer is deprecated. Please use "new Konva.Layer({ listening: false })" instead.')}};return z.FastLayer=r,r.prototype.nodeType="FastLayer",(0,i._registerNode)(r),z}(),l=Y(),c=A(),g=V(),u=Q(),f=(J||(J=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.Easings=t.Tween=void 0;const e=d(),i=Q(),r=T(),a=o();var n={node:1,duration:1,easing:1,onFinish:1,yoyo:1},s=0,h=["fill","stroke","shadowColor"];class l{constructor(t,e,i,r,a,n,s){this.prop=t,this.propFunc=e,this.begin=r,this._pos=r,this.duration=n,this._change=0,this.prevPos=0,this.yoyo=s,this._time=0,this._position=0,this._startTime=0,this._finish=0,this.func=i,this._change=a-this.begin,this.pause()}fire(t){var e=this[t];e&&e()}setTime(t){t>this.duration?this.yoyo?(this._time=this.duration,this.reverse()):this.finish():t<0?this.yoyo?(this._time=0,this.play()):this.reset():(this._time=t,this.update())}getTime(){return this._time}setPosition(t){this.prevPos=this._pos,this.propFunc(t),this._pos=t}getPosition(t){return void 0===t&&(t=this._time),this.func(t,this.begin,this._change,this.duration)}play(){this.state=2,this._startTime=this.getTimer()-this._time,this.onEnterFrame(),this.fire("onPlay")}reverse(){this.state=3,this._time=this.duration-this._time,this._startTime=this.getTimer()-this._time,this.onEnterFrame(),this.fire("onReverse")}seek(t){this.pause(),this._time=t,this.update(),this.fire("onSeek")}reset(){this.pause(),this._time=0,this.update(),this.fire("onReset")}finish(){this.pause(),this._time=this.duration,this.update(),this.fire("onFinish")}update(){this.setPosition(this.getPosition(this._time)),this.fire("onUpdate")}onEnterFrame(){var t=this.getTimer()-this._startTime;2===this.state?this.setTime(t):3===this.state&&this.setTime(this.duration-t)}pause(){this.state=1,this.fire("onPause")}getTimer(){return(new Date).getTime()}}class c{constructor(r){var o,h,d=this,g=r.node,u=g._id,f=r.easing||t.Easings.Linear,p=!!r.yoyo;o=void 0===r.duration?.3:0===r.duration?.001:r.duration,this.node=g,this._id=s++;var v=g.getLayer()||(g instanceof a.Konva.Stage?g.getLayers():null);for(h in v||e.Util.error("Tween constructor have `node` that is not in a layer. Please add node into layer first."),this.anim=new i.Animation((function(){d.tween.onEnterFrame()}),v),this.tween=new l(h,(function(t){d._tweenFunc(t)}),f,0,1,1e3*o,p),this._addListeners(),c.attrs[u]||(c.attrs[u]={}),c.attrs[u][this._id]||(c.attrs[u][this._id]={}),c.tweens[u]||(c.tweens[u]={}),r)void 0===n[h]&&this._addAttr(h,r[h]);this.reset(),this.onFinish=r.onFinish,this.onReset=r.onReset,this.onUpdate=r.onUpdate}_addAttr(t,i){var r,a,n,s,o,l,d,g,u=this.node,f=u._id;if((n=c.tweens[f][t])&&delete c.attrs[f][n][t],r=u.getAttr(t),e.Util._isArray(i))if(a=[],o=Math.max(i.length,r.length),"points"===t&&i.length!==r.length&&(i.length>r.length?(d=r,r=e.Util._prepareArrayForTween(r,i,u.closed())):(l=i,i=e.Util._prepareArrayForTween(i,r,u.closed()))),0===t.indexOf("fill"))for(s=0;s<o;s++)if(s%2==0)a.push(i[s]-r[s]);else{var p=e.Util.colorToRGBA(r[s]);g=e.Util.colorToRGBA(i[s]),r[s]=p,a.push({r:g.r-p.r,g:g.g-p.g,b:g.b-p.b,a:g.a-p.a})}else for(s=0;s<o;s++)a.push(i[s]-r[s]);else-1!==h.indexOf(t)?(r=e.Util.colorToRGBA(r),a={r:(g=e.Util.colorToRGBA(i)).r-r.r,g:g.g-r.g,b:g.b-r.b,a:g.a-r.a}):a=i-r;c.attrs[f][this._id][t]={start:r,diff:a,end:i,trueEnd:l,trueStart:d},c.tweens[f][t]=this._id}_tweenFunc(t){var i,r,a,n,s,o,l,d,g=this.node,u=c.attrs[g._id][this._id];for(i in u){if(a=(r=u[i]).start,n=r.diff,d=r.end,e.Util._isArray(a))if(s=[],l=Math.max(a.length,d.length),0===i.indexOf("fill"))for(o=0;o<l;o++)o%2==0?s.push((a[o]||0)+n[o]*t):s.push("rgba("+Math.round(a[o].r+n[o].r*t)+","+Math.round(a[o].g+n[o].g*t)+","+Math.round(a[o].b+n[o].b*t)+","+(a[o].a+n[o].a*t)+")");else for(o=0;o<l;o++)s.push((a[o]||0)+n[o]*t);else s=-1!==h.indexOf(i)?"rgba("+Math.round(a.r+n.r*t)+","+Math.round(a.g+n.g*t)+","+Math.round(a.b+n.b*t)+","+(a.a+n.a*t)+")":a+n*t;g.setAttr(i,s)}}_addListeners(){this.tween.onPlay=()=>{this.anim.start()},this.tween.onReverse=()=>{this.anim.start()},this.tween.onPause=()=>{this.anim.stop()},this.tween.onFinish=()=>{var t=this.node,e=c.attrs[t._id][this._id];e.points&&e.points.trueEnd&&t.setAttr("points",e.points.trueEnd),this.onFinish&&this.onFinish.call(this)},this.tween.onReset=()=>{var t=this.node,e=c.attrs[t._id][this._id];e.points&&e.points.trueStart&&t.points(e.points.trueStart),this.onReset&&this.onReset()},this.tween.onUpdate=()=>{this.onUpdate&&this.onUpdate.call(this)}}play(){return this.tween.play(),this}reverse(){return this.tween.reverse(),this}reset(){return this.tween.reset(),this}seek(t){return this.tween.seek(1e3*t),this}pause(){return this.tween.pause(),this}finish(){return this.tween.finish(),this}destroy(){var t,e=this.node._id,i=this._id,r=c.tweens[e];for(t in this.pause(),r)delete c.tweens[e][t];delete c.attrs[e][i]}}t.Tween=c,c.attrs={},c.tweens={},r.Node.prototype.to=function(t){var e=t.onFinish;t.node=this,t.onFinish=function(){this.destroy(),e&&e()},new c(t).play()},t.Easings={BackEaseIn(t,e,i,r){var a=1.70158;return i*(t/=r)*t*((a+1)*t-a)+e},BackEaseOut(t,e,i,r){var a=1.70158;return i*((t=t/r-1)*t*((a+1)*t+a)+1)+e},BackEaseInOut(t,e,i,r){var a=1.70158;return(t/=r/2)<1?i/2*(t*t*((1+(a*=1.525))*t-a))+e:i/2*((t-=2)*t*((1+(a*=1.525))*t+a)+2)+e},ElasticEaseIn(t,e,i,r,a,n){var s=0;return 0===t?e:1==(t/=r)?e+i:(n||(n=.3*r),!a||a<Math.abs(i)?(a=i,s=n/4):s=n/(2*Math.PI)*Math.asin(i/a),-a*Math.pow(2,10*(t-=1))*Math.sin((t*r-s)*(2*Math.PI)/n)+e)},ElasticEaseOut(t,e,i,r,a,n){var s=0;return 0===t?e:1==(t/=r)?e+i:(n||(n=.3*r),!a||a<Math.abs(i)?(a=i,s=n/4):s=n/(2*Math.PI)*Math.asin(i/a),a*Math.pow(2,-10*t)*Math.sin((t*r-s)*(2*Math.PI)/n)+i+e)},ElasticEaseInOut(t,e,i,r,a,n){var s=0;return 0===t?e:2==(t/=r/2)?e+i:(n||(n=r*(.3*1.5)),!a||a<Math.abs(i)?(a=i,s=n/4):s=n/(2*Math.PI)*Math.asin(i/a),t<1?a*Math.pow(2,10*(t-=1))*Math.sin((t*r-s)*(2*Math.PI)/n)*-.5+e:a*Math.pow(2,-10*(t-=1))*Math.sin((t*r-s)*(2*Math.PI)/n)*.5+i+e)},BounceEaseOut:(t,e,i,r)=>(t/=r)<1/2.75?i*(7.5625*t*t)+e:t<2/2.75?i*(7.5625*(t-=1.5/2.75)*t+.75)+e:t<2.5/2.75?i*(7.5625*(t-=2.25/2.75)*t+.9375)+e:i*(7.5625*(t-=2.625/2.75)*t+.984375)+e,BounceEaseIn:(e,i,r,a)=>r-t.Easings.BounceEaseOut(a-e,0,r,a)+i,BounceEaseInOut:(e,i,r,a)=>e<a/2?.5*t.Easings.BounceEaseIn(2*e,0,r,a)+i:.5*t.Easings.BounceEaseOut(2*e-a,0,r,a)+.5*r+i,EaseIn:(t,e,i,r)=>i*(t/=r)*t+e,EaseOut:(t,e,i,r)=>-i*(t/=r)*(t-2)+e,EaseInOut:(t,e,i,r)=>(t/=r/2)<1?i/2*t*t+e:-i/2*(--t*(t-2)-1)+e,StrongEaseIn:(t,e,i,r)=>i*(t/=r)*t*t*t*t+e,StrongEaseOut:(t,e,i,r)=>i*((t=t/r-1)*t*t*t*t+1)+e,StrongEaseInOut:(t,e,i,r)=>(t/=r/2)<1?i/2*t*t*t*t*t+e:i/2*((t-=2)*t*t*t*t+2)+e,Linear:(t,e,i,r)=>i*t/r+e}}(Z)),Z),p=S(),v=C();t.Konva=i.Util._assign(e.Konva,{Util:i.Util,Transform:i.Transform,Node:r.Node,Container:a.Container,Stage:n.Stage,stages:n.stages,Layer:s.Layer,FastLayer:h.FastLayer,Group:l.Group,DD:c.DD,Shape:g.Shape,shapes:g.shapes,Animation:u.Animation,Tween:f.Tween,Easings:f.Easings,Context:p.Context,Canvas:v.Canvas}),t.default=t.Konva}(n)),n}var et,it={};var rt,at={},nt={};function st(){if(rt)return nt;rt=1,Object.defineProperty(nt,"__esModule",{value:!0}),nt.Line=void 0;const t=m(),e=V(),i=v(),r=o();function a(t,e,i,r,a,n,s){var o=Math.sqrt(Math.pow(i-t,2)+Math.pow(r-e,2)),h=Math.sqrt(Math.pow(a-i,2)+Math.pow(n-r,2)),l=s*o/(o+h),d=s*h/(o+h);return[i-l*(a-t),r-l*(n-e),i+d*(a-t),r+d*(n-e)]}function n(t,e){var i,r,n=t.length,s=[];for(i=2;i<n-2;i+=2)r=a(t[i-2],t[i-1],t[i],t[i+1],t[i+2],t[i+3],e),isNaN(r[0])||(s.push(r[0]),s.push(r[1]),s.push(t[i]),s.push(t[i+1]),s.push(r[2]),s.push(r[3]));return s}let s=class extends e.Shape{constructor(t){super(t),this.on("pointsChange.konva tensionChange.konva closedChange.konva bezierChange.konva",(function(){this._clearCache("tensionPoints")}))}_sceneFunc(t){var e,i,r,a=this.points(),n=a.length,s=this.tension(),o=this.closed(),h=this.bezier();if(n){if(t.beginPath(),t.moveTo(a[0],a[1]),0!==s&&n>4){for(i=(e=this.getTensionPoints()).length,r=o?0:4,o||t.quadraticCurveTo(e[0],e[1],e[2],e[3]);r<i-2;)t.bezierCurveTo(e[r++],e[r++],e[r++],e[r++],e[r++],e[r++]);o||t.quadraticCurveTo(e[i-2],e[i-1],a[n-2],a[n-1])}else if(h)for(r=2;r<n;)t.bezierCurveTo(a[r++],a[r++],a[r++],a[r++],a[r++],a[r++]);else for(r=2;r<n;r+=2)t.lineTo(a[r],a[r+1]);o?(t.closePath(),t.fillStrokeShape(this)):t.strokeShape(this)}}getTensionPoints(){return this._getCache("tensionPoints",this._getTensionPoints)}_getTensionPoints(){return this.closed()?this._getTensionPointsClosed():n(this.points(),this.tension())}_getTensionPointsClosed(){var t=this.points(),e=t.length,i=this.tension(),r=a(t[e-2],t[e-1],t[0],t[1],t[2],t[3],i),s=a(t[e-4],t[e-3],t[e-2],t[e-1],t[0],t[1],i),o=n(t,i);return[r[2],r[3]].concat(o).concat([s[0],s[1],t[e-2],t[e-1],s[2],s[3],r[0],r[1],t[0],t[1]])}getWidth(){return this.getSelfRect().width}getHeight(){return this.getSelfRect().height}getSelfRect(){var t=this.points();if(t.length<4)return{x:t[0]||0,y:t[1]||0,width:0,height:0};for(var e,i,r=(t=0!==this.tension()?[t[0],t[1],...this._getTensionPoints(),t[t.length-2],t[t.length-1]]:this.points())[0],a=t[0],n=t[1],s=t[1],o=0;o<t.length/2;o++)e=t[2*o],i=t[2*o+1],r=Math.min(r,e),a=Math.max(a,e),n=Math.min(n,i),s=Math.max(s,i);return{x:r,y:n,width:a-r,height:s-n}}};return nt.Line=s,s.prototype.className="Line",s.prototype._attrsAffectingSize=["points","bezier","tension"],(0,r._registerNode)(s),t.Factory.addGetterSetter(s,"closed",!1),t.Factory.addGetterSetter(s,"bezier",!1),t.Factory.addGetterSetter(s,"tension",0,(0,i.getNumberValidator)()),t.Factory.addGetterSetter(s,"points",[],(0,i.getNumberArrayValidator)()),nt}var ot,ht,lt,dt={},ct={};function gt(){if(ht)return dt;ht=1,Object.defineProperty(dt,"__esModule",{value:!0}),dt.Path=void 0;const t=m(),e=V(),i=o(),r=(ot||(ot=1,function(t){function e(t,e,r){const a=i(1,r,t),n=i(1,r,e),s=a*a+n*n;return Math.sqrt(s)}Object.defineProperty(t,"__esModule",{value:!0}),t.t2length=t.getQuadraticArcLength=t.getCubicArcLength=t.binomialCoefficients=t.cValues=t.tValues=void 0,t.tValues=[[],[],[-.5773502691896257,.5773502691896257],[0,-.7745966692414834,.7745966692414834],[-.33998104358485626,.33998104358485626,-.8611363115940526,.8611363115940526],[0,-.5384693101056831,.5384693101056831,-.906179845938664,.906179845938664],[.6612093864662645,-.6612093864662645,-.2386191860831969,.2386191860831969,-.932469514203152,.932469514203152],[0,.4058451513773972,-.4058451513773972,-.7415311855993945,.7415311855993945,-.9491079123427585,.9491079123427585],[-.1834346424956498,.1834346424956498,-.525532409916329,.525532409916329,-.7966664774136267,.7966664774136267,-.9602898564975363,.9602898564975363],[0,-.8360311073266358,.8360311073266358,-.9681602395076261,.9681602395076261,-.3242534234038089,.3242534234038089,-.6133714327005904,.6133714327005904],[-.14887433898163122,.14887433898163122,-.4333953941292472,.4333953941292472,-.6794095682990244,.6794095682990244,-.8650633666889845,.8650633666889845,-.9739065285171717,.9739065285171717],[0,-.26954315595234496,.26954315595234496,-.5190961292068118,.5190961292068118,-.7301520055740494,.7301520055740494,-.8870625997680953,.8870625997680953,-.978228658146057,.978228658146057],[-.1252334085114689,.1252334085114689,-.3678314989981802,.3678314989981802,-.5873179542866175,.5873179542866175,-.7699026741943047,.7699026741943047,-.9041172563704749,.9041172563704749,-.9815606342467192,.9815606342467192],[0,-.2304583159551348,.2304583159551348,-.44849275103644687,.44849275103644687,-.6423493394403402,.6423493394403402,-.8015780907333099,.8015780907333099,-.9175983992229779,.9175983992229779,-.9841830547185881,.9841830547185881],[-.10805494870734367,.10805494870734367,-.31911236892788974,.31911236892788974,-.5152486363581541,.5152486363581541,-.6872929048116855,.6872929048116855,-.827201315069765,.827201315069765,-.9284348836635735,.9284348836635735,-.9862838086968123,.9862838086968123],[0,-.20119409399743451,.20119409399743451,-.3941513470775634,.3941513470775634,-.5709721726085388,.5709721726085388,-.7244177313601701,.7244177313601701,-.8482065834104272,.8482065834104272,-.937273392400706,.937273392400706,-.9879925180204854,.9879925180204854],[-.09501250983763744,.09501250983763744,-.2816035507792589,.2816035507792589,-.45801677765722737,.45801677765722737,-.6178762444026438,.6178762444026438,-.755404408355003,.755404408355003,-.8656312023878318,.8656312023878318,-.9445750230732326,.9445750230732326,-.9894009349916499,.9894009349916499],[0,-.17848418149584785,.17848418149584785,-.3512317634538763,.3512317634538763,-.5126905370864769,.5126905370864769,-.6576711592166907,.6576711592166907,-.7815140038968014,.7815140038968014,-.8802391537269859,.8802391537269859,-.9506755217687678,.9506755217687678,-.9905754753144174,.9905754753144174],[-.0847750130417353,.0847750130417353,-.2518862256915055,.2518862256915055,-.41175116146284263,.41175116146284263,-.5597708310739475,.5597708310739475,-.6916870430603532,.6916870430603532,-.8037049589725231,.8037049589725231,-.8926024664975557,.8926024664975557,-.9558239495713977,.9558239495713977,-.9915651684209309,.9915651684209309],[0,-.16035864564022537,.16035864564022537,-.31656409996362983,.31656409996362983,-.46457074137596094,.46457074137596094,-.600545304661681,.600545304661681,-.7209661773352294,.7209661773352294,-.8227146565371428,.8227146565371428,-.9031559036148179,.9031559036148179,-.96020815213483,.96020815213483,-.9924068438435844,.9924068438435844],[-.07652652113349734,.07652652113349734,-.22778585114164507,.22778585114164507,-.37370608871541955,.37370608871541955,-.5108670019508271,.5108670019508271,-.636053680726515,.636053680726515,-.7463319064601508,.7463319064601508,-.8391169718222188,.8391169718222188,-.912234428251326,.912234428251326,-.9639719272779138,.9639719272779138,-.9931285991850949,.9931285991850949],[0,-.1455618541608951,.1455618541608951,-.2880213168024011,.2880213168024011,-.4243421202074388,.4243421202074388,-.5516188358872198,.5516188358872198,-.6671388041974123,.6671388041974123,-.7684399634756779,.7684399634756779,-.8533633645833173,.8533633645833173,-.9200993341504008,.9200993341504008,-.9672268385663063,.9672268385663063,-.9937521706203895,.9937521706203895],[-.06973927331972223,.06973927331972223,-.20786042668822127,.20786042668822127,-.34193582089208424,.34193582089208424,-.469355837986757,.469355837986757,-.5876404035069116,.5876404035069116,-.6944872631866827,.6944872631866827,-.7878168059792081,.7878168059792081,-.8658125777203002,.8658125777203002,-.926956772187174,.926956772187174,-.9700604978354287,.9700604978354287,-.9942945854823992,.9942945854823992],[0,-.1332568242984661,.1332568242984661,-.26413568097034495,.26413568097034495,-.3903010380302908,.3903010380302908,-.5095014778460075,.5095014778460075,-.6196098757636461,.6196098757636461,-.7186613631319502,.7186613631319502,-.8048884016188399,.8048884016188399,-.8767523582704416,.8767523582704416,-.9329710868260161,.9329710868260161,-.9725424712181152,.9725424712181152,-.9947693349975522,.9947693349975522],[-.06405689286260563,.06405689286260563,-.1911188674736163,.1911188674736163,-.3150426796961634,.3150426796961634,-.4337935076260451,.4337935076260451,-.5454214713888396,.5454214713888396,-.6480936519369755,.6480936519369755,-.7401241915785544,.7401241915785544,-.820001985973903,.820001985973903,-.8864155270044011,.8864155270044011,-.9382745520027328,.9382745520027328,-.9747285559713095,.9747285559713095,-.9951872199970213,.9951872199970213]],t.cValues=[[],[],[1,1],[.8888888888888888,.5555555555555556,.5555555555555556],[.6521451548625461,.6521451548625461,.34785484513745385,.34785484513745385],[.5688888888888889,.47862867049936647,.47862867049936647,.23692688505618908,.23692688505618908],[.3607615730481386,.3607615730481386,.46791393457269104,.46791393457269104,.17132449237917036,.17132449237917036],[.4179591836734694,.3818300505051189,.3818300505051189,.27970539148927664,.27970539148927664,.1294849661688697,.1294849661688697],[.362683783378362,.362683783378362,.31370664587788727,.31370664587788727,.22238103445337448,.22238103445337448,.10122853629037626,.10122853629037626],[.3302393550012598,.1806481606948574,.1806481606948574,.08127438836157441,.08127438836157441,.31234707704000286,.31234707704000286,.26061069640293544,.26061069640293544],[.29552422471475287,.29552422471475287,.26926671930999635,.26926671930999635,.21908636251598204,.21908636251598204,.1494513491505806,.1494513491505806,.06667134430868814,.06667134430868814],[.2729250867779006,.26280454451024665,.26280454451024665,.23319376459199048,.23319376459199048,.18629021092773426,.18629021092773426,.1255803694649046,.1255803694649046,.05566856711617366,.05566856711617366],[.24914704581340277,.24914704581340277,.2334925365383548,.2334925365383548,.20316742672306592,.20316742672306592,.16007832854334622,.16007832854334622,.10693932599531843,.10693932599531843,.04717533638651183,.04717533638651183],[.2325515532308739,.22628318026289723,.22628318026289723,.2078160475368885,.2078160475368885,.17814598076194574,.17814598076194574,.13887351021978725,.13887351021978725,.09212149983772845,.09212149983772845,.04048400476531588,.04048400476531588],[.2152638534631578,.2152638534631578,.2051984637212956,.2051984637212956,.18553839747793782,.18553839747793782,.15720316715819355,.15720316715819355,.12151857068790319,.12151857068790319,.08015808715976021,.08015808715976021,.03511946033175186,.03511946033175186],[.2025782419255613,.19843148532711158,.19843148532711158,.1861610000155622,.1861610000155622,.16626920581699392,.16626920581699392,.13957067792615432,.13957067792615432,.10715922046717194,.10715922046717194,.07036604748810812,.07036604748810812,.03075324199611727,.03075324199611727],[.1894506104550685,.1894506104550685,.18260341504492358,.18260341504492358,.16915651939500254,.16915651939500254,.14959598881657674,.14959598881657674,.12462897125553388,.12462897125553388,.09515851168249279,.09515851168249279,.062253523938647894,.062253523938647894,.027152459411754096,.027152459411754096],[.17944647035620653,.17656270536699264,.17656270536699264,.16800410215645004,.16800410215645004,.15404576107681028,.15404576107681028,.13513636846852548,.13513636846852548,.11188384719340397,.11188384719340397,.08503614831717918,.08503614831717918,.0554595293739872,.0554595293739872,.02414830286854793,.02414830286854793],[.1691423829631436,.1691423829631436,.16427648374583273,.16427648374583273,.15468467512626524,.15468467512626524,.14064291467065065,.14064291467065065,.12255520671147846,.12255520671147846,.10094204410628717,.10094204410628717,.07642573025488905,.07642573025488905,.0497145488949698,.0497145488949698,.02161601352648331,.02161601352648331],[.1610544498487837,.15896884339395434,.15896884339395434,.15276604206585967,.15276604206585967,.1426067021736066,.1426067021736066,.12875396253933621,.12875396253933621,.11156664554733399,.11156664554733399,.09149002162245,.09149002162245,.06904454273764123,.06904454273764123,.0448142267656996,.0448142267656996,.019461788229726478,.019461788229726478],[.15275338713072584,.15275338713072584,.14917298647260374,.14917298647260374,.14209610931838204,.14209610931838204,.13168863844917664,.13168863844917664,.11819453196151841,.11819453196151841,.10193011981724044,.10193011981724044,.08327674157670475,.08327674157670475,.06267204833410907,.06267204833410907,.04060142980038694,.04060142980038694,.017614007139152118,.017614007139152118],[.14608113364969041,.14452440398997005,.14452440398997005,.13988739479107315,.13988739479107315,.13226893863333747,.13226893863333747,.12183141605372853,.12183141605372853,.10879729916714838,.10879729916714838,.09344442345603386,.09344442345603386,.0761001136283793,.0761001136283793,.057134425426857205,.057134425426857205,.036953789770852494,.036953789770852494,.016017228257774335,.016017228257774335],[.13925187285563198,.13925187285563198,.13654149834601517,.13654149834601517,.13117350478706238,.13117350478706238,.12325237681051242,.12325237681051242,.11293229608053922,.11293229608053922,.10041414444288096,.10041414444288096,.08594160621706773,.08594160621706773,.06979646842452049,.06979646842452049,.052293335152683286,.052293335152683286,.03377490158481415,.03377490158481415,.0146279952982722,.0146279952982722],[.13365457218610619,.1324620394046966,.1324620394046966,.12890572218808216,.12890572218808216,.12304908430672953,.12304908430672953,.11499664022241136,.11499664022241136,.10489209146454141,.10489209146454141,.09291576606003515,.09291576606003515,.07928141177671895,.07928141177671895,.06423242140852585,.06423242140852585,.04803767173108467,.04803767173108467,.030988005856979445,.030988005856979445,.013411859487141771,.013411859487141771],[.12793819534675216,.12793819534675216,.1258374563468283,.1258374563468283,.12167047292780339,.12167047292780339,.1155056680537256,.1155056680537256,.10744427011596563,.10744427011596563,.09761865210411388,.09761865210411388,.08619016153195327,.08619016153195327,.0733464814110803,.0733464814110803,.05929858491543678,.05929858491543678,.04427743881741981,.04427743881741981,.028531388628933663,.028531388628933663,.0123412297999872,.0123412297999872]],t.binomialCoefficients=[[1],[1,1],[1,2,1],[1,3,3,1]],t.getCubicArcLength=(i,r,a)=>{let n,s,o;n=a/2,s=0;for(let h=0;h<20;h++)o=n*t.tValues[20][h]+n,s+=t.cValues[20][h]*e(i,r,o);return n*s},t.getQuadraticArcLength=(t,e,i)=>{void 0===i&&(i=1);const r=t[0]-2*t[1]+t[2],a=e[0]-2*e[1]+e[2],n=2*t[1]-2*t[0],s=2*e[1]-2*e[0],o=4*(r*r+a*a),h=4*(r*n+a*s),l=n*n+s*s;if(0===o)return i*Math.sqrt(Math.pow(t[2]-t[0],2)+Math.pow(e[2]-e[0],2));const d=h/(2*o),c=i+d,g=l/o-d*d,u=c*c+g>0?Math.sqrt(c*c+g):0,f=d*d+g>0?Math.sqrt(d*d+g):0,p=d+Math.sqrt(d*d+g)!==0?g*Math.log(Math.abs((c+u)/(d+f))):0;return Math.sqrt(o)/2*(c*u-d*f+p)};const i=(e,r,a)=>{const n=a.length-1;let s,o;if(0===n)return 0;if(0===e){o=0;for(let e=0;e<=n;e++)o+=t.binomialCoefficients[n][e]*Math.pow(1-r,n-e)*Math.pow(r,e)*a[e];return o}s=new Array(n);for(let t=0;t<n;t++)s[t]=n*(a[t+1]-a[t]);return i(e-1,r,s)};t.t2length=(t,e,i)=>{let r=1,a=t/e,n=(t-i(a))/e,s=0;for(;r>.001;){const o=i(a+n),h=Math.abs(t-o)/e;if(h<r)r=h,a+=n;else{const s=i(a-n),o=Math.abs(t-s)/e;o<r?(r=o,a-=n):n/=2}if(s++,s>500)break}return a}}(ct)),ct);let a=class t extends e.Shape{constructor(t){super(t),this.dataArray=[],this.pathLength=0,this._readDataAttribute(),this.on("dataChange.konva",(function(){this._readDataAttribute()}))}_readDataAttribute(){this.dataArray=t.parsePathData(this.data()),this.pathLength=t.getPathLength(this.dataArray)}_sceneFunc(t){var e=this.dataArray;t.beginPath();for(var i=!1,r=0;r<e.length;r++){var a=e[r].command,n=e[r].points;switch(a){case"L":t.lineTo(n[0],n[1]);break;case"M":t.moveTo(n[0],n[1]);break;case"C":t.bezierCurveTo(n[0],n[1],n[2],n[3],n[4],n[5]);break;case"Q":t.quadraticCurveTo(n[0],n[1],n[2],n[3]);break;case"A":var s=n[0],o=n[1],h=n[2],l=n[3],d=n[4],c=n[5],g=n[6],u=n[7],f=h>l?h:l,p=h>l?1:h/l,v=h>l?l/h:1;t.translate(s,o),t.rotate(g),t.scale(p,v),t.arc(0,0,f,d,d+c,1-u),t.scale(1/p,1/v),t.rotate(-g),t.translate(-s,-o);break;case"z":i=!0,t.closePath()}}i||this.hasFill()?t.fillStrokeShape(this):t.strokeShape(this)}getSelfRect(){var e=[];this.dataArray.forEach((function(i){if("A"===i.command){var r=i.points[4],a=i.points[5],n=i.points[4]+a,s=Math.PI/180;if(Math.abs(r-n)<s&&(s=Math.abs(r-n)),a<0)for(let a=r-s;a>n;a-=s){const r=t.getPointOnEllipticalArc(i.points[0],i.points[1],i.points[2],i.points[3],a,0);e.push(r.x,r.y)}else for(let a=r+s;a<n;a+=s){const r=t.getPointOnEllipticalArc(i.points[0],i.points[1],i.points[2],i.points[3],a,0);e.push(r.x,r.y)}}else if("C"===i.command)for(let o=0;o<=1;o+=.01){const r=t.getPointOnCubicBezier(o,i.start.x,i.start.y,i.points[0],i.points[1],i.points[2],i.points[3],i.points[4],i.points[5]);e.push(r.x,r.y)}else e=e.concat(i.points)}));for(var i,r,a=e[0],n=e[0],s=e[1],o=e[1],h=0;h<e.length/2;h++)i=e[2*h],r=e[2*h+1],isNaN(i)||(a=Math.min(a,i),n=Math.max(n,i)),isNaN(r)||(s=Math.min(s,r),o=Math.max(o,r));return{x:a,y:s,width:n-a,height:o-s}}getLength(){return this.pathLength}getPointAtLength(e){return t.getPointAtLengthOfDataArray(e,this.dataArray)}static getLineLength(t,e,i,r){return Math.sqrt((i-t)*(i-t)+(r-e)*(r-e))}static getPathLength(t){let e=0;for(var i=0;i<t.length;++i)e+=t[i].pathLength;return e}static getPointAtLengthOfDataArray(e,i){var a,n=0,s=i.length;if(!s)return null;for(;n<s&&e>i[n].pathLength;)e-=i[n].pathLength,++n;if(n===s)return{x:(a=i[n-1].points.slice(-2))[0],y:a[1]};if(e<.01)return{x:(a=i[n].points.slice(0,2))[0],y:a[1]};var o=i[n],h=o.points;switch(o.command){case"L":return t.getPointOnLine(e,o.start.x,o.start.y,h[0],h[1]);case"C":return t.getPointOnCubicBezier((0,r.t2length)(e,t.getPathLength(i),(t=>(0,r.getCubicArcLength)([o.start.x,h[0],h[2],h[4]],[o.start.y,h[1],h[3],h[5]],t))),o.start.x,o.start.y,h[0],h[1],h[2],h[3],h[4],h[5]);case"Q":return t.getPointOnQuadraticBezier((0,r.t2length)(e,t.getPathLength(i),(t=>(0,r.getQuadraticArcLength)([o.start.x,h[0],h[2]],[o.start.y,h[1],h[3]],t))),o.start.x,o.start.y,h[0],h[1],h[2],h[3]);case"A":var l=h[0],d=h[1],c=h[2],g=h[3],u=h[4],f=h[5],p=h[6];return u+=f*e/o.pathLength,t.getPointOnEllipticalArc(l,d,c,g,u,p)}return null}static getPointOnLine(t,e,i,r,a,n,s){n=null!=n?n:e,s=null!=s?s:i;const o=this.getLineLength(e,i,r,a);if(o<1e-10)return{x:e,y:i};if(r===e)return{x:n,y:s+(a>i?t:-t)};const h=(a-i)/(r-e),l=Math.sqrt(t*t/(1+h*h))*(r<e?-1:1),d=h*l;if(Math.abs(s-i-h*(n-e))<1e-10)return{x:n+l,y:s+d};const c=((n-e)*(r-e)+(s-i)*(a-i))/(o*o),g=e+c*(r-e),u=i+c*(a-i),f=this.getLineLength(n,s,g,u),p=Math.sqrt(t*t-f*f),v=Math.sqrt(p*p/(1+h*h))*(r<e?-1:1);return{x:g+v,y:u+h*v}}static getPointOnCubicBezier(t,e,i,r,a,n,s,o,h){function l(t){return t*t*t}function d(t){return 3*t*t*(1-t)}function c(t){return 3*t*(1-t)*(1-t)}function g(t){return(1-t)*(1-t)*(1-t)}return{x:o*l(t)+n*d(t)+r*c(t)+e*g(t),y:h*l(t)+s*d(t)+a*c(t)+i*g(t)}}static getPointOnQuadraticBezier(t,e,i,r,a,n,s){function o(t){return t*t}function h(t){return 2*t*(1-t)}function l(t){return(1-t)*(1-t)}return{x:n*o(t)+r*h(t)+e*l(t),y:s*o(t)+a*h(t)+i*l(t)}}static getPointOnEllipticalArc(t,e,i,r,a,n){var s=Math.cos(n),o=Math.sin(n),h=i*Math.cos(a),l=r*Math.sin(a);return{x:t+(h*s-l*o),y:e+(h*o+l*s)}}static parsePathData(t){if(!t)return[];var e=t,i=["m","M","l","L","v","V","h","H","z","Z","c","C","q","Q","t","T","s","S","a","A"];e=e.replace(new RegExp(" ","g"),",");for(var r=0;r<i.length;r++)e=e.replace(new RegExp(i[r],"g"),"|"+i[r]);var a,n=e.split("|"),s=[],o=[],h=0,l=0,d=/([-+]?((\d+\.\d+)|((\d+)|(\.\d+)))(?:e[-+]?\d+)?)/gi;for(r=1;r<n.length;r++){var c=n[r],g=c.charAt(0);for(c=c.slice(1),o.length=0;a=d.exec(c);)o.push(a[0]);for(var u=[],f=0,p=o.length;f<p;f++)if("00"!==o[f]){var v=parseFloat(o[f]);isNaN(v)?u.push(0):u.push(v)}else u.push(0,0);for(;u.length>0&&!isNaN(u[0]);){var m,_,y,b,x,S,C,w,P,k,A="",T=[],F=h,M=l;switch(g){case"l":h+=u.shift(),l+=u.shift(),A="L",T.push(h,l);break;case"L":h=u.shift(),l=u.shift(),T.push(h,l);break;case"m":var G=u.shift(),R=u.shift();if(h+=G,l+=R,A="M",s.length>2&&"z"===s[s.length-1].command)for(var D=s.length-2;D>=0;D--)if("M"===s[D].command){h=s[D].points[0]+G,l=s[D].points[1]+R;break}T.push(h,l),g="l";break;case"M":h=u.shift(),l=u.shift(),A="M",T.push(h,l),g="L";break;case"h":h+=u.shift(),A="L",T.push(h,l);break;case"H":h=u.shift(),A="L",T.push(h,l);break;case"v":l+=u.shift(),A="L",T.push(h,l);break;case"V":l=u.shift(),A="L",T.push(h,l);break;case"C":T.push(u.shift(),u.shift(),u.shift(),u.shift()),h=u.shift(),l=u.shift(),T.push(h,l);break;case"c":T.push(h+u.shift(),l+u.shift(),h+u.shift(),l+u.shift()),h+=u.shift(),l+=u.shift(),A="C",T.push(h,l);break;case"S":_=h,y=l,"C"===(m=s[s.length-1]).command&&(_=h+(h-m.points[2]),y=l+(l-m.points[3])),T.push(_,y,u.shift(),u.shift()),h=u.shift(),l=u.shift(),A="C",T.push(h,l);break;case"s":_=h,y=l,"C"===(m=s[s.length-1]).command&&(_=h+(h-m.points[2]),y=l+(l-m.points[3])),T.push(_,y,h+u.shift(),l+u.shift()),h+=u.shift(),l+=u.shift(),A="C",T.push(h,l);break;case"Q":T.push(u.shift(),u.shift()),h=u.shift(),l=u.shift(),T.push(h,l);break;case"q":T.push(h+u.shift(),l+u.shift()),h+=u.shift(),l+=u.shift(),A="Q",T.push(h,l);break;case"T":_=h,y=l,"Q"===(m=s[s.length-1]).command&&(_=h+(h-m.points[0]),y=l+(l-m.points[1])),h=u.shift(),l=u.shift(),A="Q",T.push(_,y,h,l);break;case"t":_=h,y=l,"Q"===(m=s[s.length-1]).command&&(_=h+(h-m.points[0]),y=l+(l-m.points[1])),h+=u.shift(),l+=u.shift(),A="Q",T.push(_,y,h,l);break;case"A":b=u.shift(),x=u.shift(),S=u.shift(),C=u.shift(),w=u.shift(),P=h,k=l,h=u.shift(),l=u.shift(),A="A",T=this.convertEndpointToCenterParameterization(P,k,h,l,C,w,b,x,S);break;case"a":b=u.shift(),x=u.shift(),S=u.shift(),C=u.shift(),w=u.shift(),P=h,k=l,h+=u.shift(),l+=u.shift(),A="A",T=this.convertEndpointToCenterParameterization(P,k,h,l,C,w,b,x,S)}s.push({command:A||g,points:T,start:{x:F,y:M},pathLength:this.calcLength(F,M,A||g,T)})}"z"!==g&&"Z"!==g||s.push({command:"z",points:[],start:void 0,pathLength:0})}return s}static calcLength(e,i,a,n){var s,o,h,l,d=t;switch(a){case"L":return d.getLineLength(e,i,n[0],n[1]);case"C":return(0,r.getCubicArcLength)([e,n[0],n[2],n[4]],[i,n[1],n[3],n[5]],1);case"Q":return(0,r.getQuadraticArcLength)([e,n[0],n[2]],[i,n[1],n[3]],1);case"A":s=0;var c=n[4],g=n[5],u=n[4]+g,f=Math.PI/180;if(Math.abs(c-u)<f&&(f=Math.abs(c-u)),o=d.getPointOnEllipticalArc(n[0],n[1],n[2],n[3],c,0),g<0)for(l=c-f;l>u;l-=f)h=d.getPointOnEllipticalArc(n[0],n[1],n[2],n[3],l,0),s+=d.getLineLength(o.x,o.y,h.x,h.y),o=h;else for(l=c+f;l<u;l+=f)h=d.getPointOnEllipticalArc(n[0],n[1],n[2],n[3],l,0),s+=d.getLineLength(o.x,o.y,h.x,h.y),o=h;return h=d.getPointOnEllipticalArc(n[0],n[1],n[2],n[3],u,0),s+=d.getLineLength(o.x,o.y,h.x,h.y)}return 0}static convertEndpointToCenterParameterization(t,e,i,r,a,n,s,o,h){var l=h*(Math.PI/180),d=Math.cos(l)*(t-i)/2+Math.sin(l)*(e-r)/2,c=-1*Math.sin(l)*(t-i)/2+Math.cos(l)*(e-r)/2,g=d*d/(s*s)+c*c/(o*o);g>1&&(s*=Math.sqrt(g),o*=Math.sqrt(g));var u=Math.sqrt((s*s*(o*o)-s*s*(c*c)-o*o*(d*d))/(s*s*(c*c)+o*o*(d*d)));a===n&&(u*=-1),isNaN(u)&&(u=0);var f=u*s*c/o,p=u*-o*d/s,v=(t+i)/2+Math.cos(l)*f-Math.sin(l)*p,m=(e+r)/2+Math.sin(l)*f+Math.cos(l)*p,_=function(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])},y=function(t,e){return(t[0]*e[0]+t[1]*e[1])/(_(t)*_(e))},b=function(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(y(t,e))},x=b([1,0],[(d-f)/s,(c-p)/o]),S=[(d-f)/s,(c-p)/o],C=[(-1*d-f)/s,(-1*c-p)/o],w=b(S,C);return y(S,C)<=-1&&(w=Math.PI),y(S,C)>=1&&(w=0),0===n&&w>0&&(w-=2*Math.PI),1===n&&w<0&&(w+=2*Math.PI),[v,m,s,o,x,w,l,n]}};return dt.Path=a,a.prototype.className="Path",a.prototype._attrsAffectingSize=["data"],(0,i._registerNode)(a),t.Factory.addGetterSetter(a,"data"),dt}var ut,ft={};var pt,vt={};var mt,_t={};var yt,bt={};var xt,St={};function Ct(){if(xt)return St;xt=1,Object.defineProperty(St,"__esModule",{value:!0}),St.Rect=void 0;const t=m(),e=V(),i=o(),r=d(),a=v();let n=class extends e.Shape{_sceneFunc(t){var e=this.cornerRadius(),i=this.width(),a=this.height();t.beginPath(),e?r.Util.drawRoundedRectPath(t,i,a,e):t.rect(0,0,i,a),t.closePath(),t.fillStrokeShape(this)}};return St.Rect=n,n.prototype.className="Rect",(0,i._registerNode)(n),t.Factory.addGetterSetter(n,"cornerRadius",0,(0,a.getNumberOrArrayOfNumbersValidator)(4)),St}var wt,Pt={};var kt,At={};var Tt,Ft={};var Mt,Gt={};var Rt,Dt={};function Nt(){if(Rt)return Dt;Rt=1,Object.defineProperty(Dt,"__esModule",{value:!0}),Dt.Text=Dt.stringToArray=void 0;const t=d(),e=m(),i=V(),r=o(),a=v(),n=o();function s(t){return Array.from(t)}Dt.stringToArray=s;var h,l="auto",c="inherit",g="justify",u="left",f="middle",p="normal",_=" ",y="none",b=["direction","fontFamily","fontSize","fontStyle","fontVariant","padding","align","verticalAlign","lineHeight","text","width","height","wrap","ellipsis","letterSpacing"],x=b.length;function S(){return h||(h=t.Util.createCanvasElement().getContext("2d"))}let C=class extends i.Shape{constructor(t){super(function(t){return(t=t||{}).fillLinearGradientColorStops||t.fillRadialGradientColorStops||t.fillPatternImage||(t.fill=t.fill||"black"),t}(t)),this._partialTextX=0,this._partialTextY=0;for(var e=0;e<x;e++)this.on(b[e]+"Change.konva",this._setTextData);this._setTextData()}_sceneFunc(t){var e=this.textArr,i=e.length;if(this.text()){var a,n=this.padding(),o=this.fontSize(),h=this.lineHeight()*o,l=this.verticalAlign(),d=this.direction(),p=0,v=this.align(),m=this.getWidth(),_=this.letterSpacing(),y=this.fill(),b=this.textDecoration(),x=-1!==b.indexOf("underline"),S=-1!==b.indexOf("line-through");d=d===c?t.direction:d;var C=h/2,w=f;if(r.Konva._fixTextRendering){var P=this.measureSize("M");w="alphabetic",C=(P.fontBoundingBoxAscent-P.fontBoundingBoxDescent)/2+h/2}var k=0,A=0;for("rtl"===d&&t.setAttr("direction",d),t.setAttr("font",this._getContextFont()),t.setAttr("textBaseline",w),t.setAttr("textAlign",u),l===f?p=(this.getHeight()-i*h-2*n)/2:"bottom"===l&&(p=this.getHeight()-i*h-2*n),t.translate(n,p+n),a=0;a<i;a++){k=0,A=0;var T,F,M,G=e[a],R=G.text,D=G.width,N=G.lastInParagraph;if(t.save(),"right"===v?k+=m-D-2*n:"center"===v&&(k+=(m-D-2*n)/2),x){t.save(),t.beginPath();const e=k,i=C+A+(r.Konva._fixTextRendering?Math.round(o/4):Math.round(o/2));t.moveTo(e,i),F=0===(T=R.split(" ").length-1),M=v!==g||N?D:m-2*n,t.lineTo(e+Math.round(M),i),t.lineWidth=o/15;const a=this._getLinearGradient();t.strokeStyle=a||y,t.stroke(),t.restore()}if(S){t.save(),t.beginPath();let e=r.Konva._fixTextRendering?-Math.round(o/4):0;t.moveTo(k,C+A+e),F=0===(T=R.split(" ").length-1),M=v===g&&N&&!F?m-2*n:D,t.lineTo(k+Math.round(M),C+A+e),t.lineWidth=o/15;const i=this._getLinearGradient();t.strokeStyle=i||y,t.stroke(),t.restore()}if("rtl"===d||0===_&&v!==g)0!==_&&t.setAttr("letterSpacing",`${_}px`),this._partialTextX=k,this._partialTextY=C+A,this._partialText=R,t.fillStrokeShape(this);else{T=R.split(" ").length-1;for(var E=s(R),O=0;O<E.length;O++){var L=E[O];" "!==L||N||v!==g||(k+=(m-2*n-D)/T),this._partialTextX=k,this._partialTextY=C+A,this._partialText=L,t.fillStrokeShape(this),k+=this.measureSize(L).width+_}}t.restore(),i>1&&(C+=h)}}}_hitFunc(t){var e=this.getWidth(),i=this.getHeight();t.beginPath(),t.rect(0,0,e,i),t.closePath(),t.fillStrokeShape(this)}setText(e){var i=t.Util._isString(e)?e:null==e?"":e+"";return this._setAttr("text",i),this}getWidth(){return this.attrs.width===l||void 0===this.attrs.width?this.getTextWidth()+2*this.padding():this.attrs.width}getHeight(){return this.attrs.height===l||void 0===this.attrs.height?this.fontSize()*this.textArr.length*this.lineHeight()+2*this.padding():this.attrs.height}getTextWidth(){return this.textWidth}getTextHeight(){return t.Util.warn("text.getTextHeight() method is deprecated. Use text.height() - for full height and text.fontSize() - for one line height."),this.textHeight}measureSize(t){var e,i,r,a,n,s,o,h,l,d,c,g,u=S(),f=this.fontSize();u.save(),u.font=this._getContextFont(),g=u.measureText(t),u.restore();const p=f/100;return{actualBoundingBoxAscent:null!==(e=g.actualBoundingBoxAscent)&&void 0!==e?e:71.58203125*p,actualBoundingBoxDescent:null!==(i=g.actualBoundingBoxDescent)&&void 0!==i?i:0,actualBoundingBoxLeft:null!==(r=g.actualBoundingBoxLeft)&&void 0!==r?r:-7.421875*p,actualBoundingBoxRight:null!==(a=g.actualBoundingBoxRight)&&void 0!==a?a:75.732421875*p,alphabeticBaseline:null!==(n=g.alphabeticBaseline)&&void 0!==n?n:0,emHeightAscent:null!==(s=g.emHeightAscent)&&void 0!==s?s:100*p,emHeightDescent:null!==(o=g.emHeightDescent)&&void 0!==o?o:-20*p,fontBoundingBoxAscent:null!==(h=g.fontBoundingBoxAscent)&&void 0!==h?h:91*p,fontBoundingBoxDescent:null!==(l=g.fontBoundingBoxDescent)&&void 0!==l?l:21*p,hangingBaseline:null!==(d=g.hangingBaseline)&&void 0!==d?d:72.80000305175781*p,ideographicBaseline:null!==(c=g.ideographicBaseline)&&void 0!==c?c:-21*p,width:g.width,height:f}}_getContextFont(){return this.fontStyle()+_+this.fontVariant()+_+(this.fontSize()+"px ")+this.fontFamily().split(",").map((t=>{const e=(t=t.trim()).indexOf(" ")>=0,i=t.indexOf('"')>=0||t.indexOf("'")>=0;return e&&!i&&(t=`"${t}"`),t})).join(", ")}_addTextLine(t){this.align()===g&&(t=t.trim());var e=this._getTextWidth(t);return this.textArr.push({text:t,width:e,lastInParagraph:!1})}_getTextWidth(t){var e=this.letterSpacing(),i=t.length;return S().measureText(t).width+(i?e*(i-1):0)}_setTextData(){var t=this.text().split("\n"),e=+this.fontSize(),i=0,r=this.lineHeight()*e,a=this.attrs.width,n=this.attrs.height,s=a!==l&&void 0!==a,o=n!==l&&void 0!==n,h=this.padding(),d=a-2*h,c=n-2*h,g=0,u=this.wrap(),f="char"!==u&&u!==y,p=this.ellipsis();this.textArr=[],S().font=this._getContextFont();for(var v=p?this._getTextWidth("…"):0,m=0,b=t.length;m<b;++m){var x=t[m],C=this._getTextWidth(x);if(s&&C>d)for(;x.length>0;){for(var w=0,P=x.length,k="",A=0;w<P;){var T=w+P>>>1,F=x.slice(0,T+1),M=this._getTextWidth(F)+v;M<=d?(w=T+1,k=F,A=M):P=T}if(!k)break;if(f){var G,R=x[k.length];(G=(R===_||"-"===R)&&A<=d?k.length:Math.max(k.lastIndexOf(_),k.lastIndexOf("-"))+1)>0&&(w=G,k=k.slice(0,w),A=this._getTextWidth(k))}if(k=k.trimRight(),this._addTextLine(k),i=Math.max(i,A),g+=r,this._shouldHandleEllipsis(g)){this._tryToAddEllipsisToLastLine();break}if((x=(x=x.slice(w)).trimLeft()).length>0&&(C=this._getTextWidth(x))<=d){this._addTextLine(x),g+=r,i=Math.max(i,C);break}}else this._addTextLine(x),g+=r,i=Math.max(i,C),this._shouldHandleEllipsis(g)&&m<b-1&&this._tryToAddEllipsisToLastLine();if(this.textArr[this.textArr.length-1]&&(this.textArr[this.textArr.length-1].lastInParagraph=!0),o&&g+r>c)break}this.textHeight=e,this.textWidth=i}_shouldHandleEllipsis(t){var e=+this.fontSize(),i=this.lineHeight()*e,r=this.attrs.height,a=r!==l&&void 0!==r,n=r-2*this.padding();return!(this.wrap()!==y)||a&&t+i>n}_tryToAddEllipsisToLastLine(){var t=this.attrs.width,e=t!==l&&void 0!==t,i=t-2*this.padding(),r=this.ellipsis(),a=this.textArr[this.textArr.length-1];if(a&&r){if(e)this._getTextWidth(a.text+"…")<i||(a.text=a.text.slice(0,a.text.length-3));this.textArr.splice(this.textArr.length-1,1),this._addTextLine(a.text+"…")}}getStrokeScaleEnabled(){return!0}_useBufferCanvas(){const t=-1!==this.textDecoration().indexOf("underline")||-1!==this.textDecoration().indexOf("line-through"),e=this.hasShadow();return!(!t||!e)||super._useBufferCanvas()}};return Dt.Text=C,C.prototype._fillFunc=function(t){t.fillText(this._partialText,this._partialTextX,this._partialTextY)},C.prototype._strokeFunc=function(t){t.setAttr("miterLimit",2),t.strokeText(this._partialText,this._partialTextX,this._partialTextY)},C.prototype.className="Text",C.prototype._attrsAffectingSize=["text","fontSize","padding","wrap","lineHeight","letterSpacing"],(0,n._registerNode)(C),e.Factory.overWriteSetter(C,"width",(0,a.getNumberOrAutoValidator)()),e.Factory.overWriteSetter(C,"height",(0,a.getNumberOrAutoValidator)()),e.Factory.addGetterSetter(C,"direction",c),e.Factory.addGetterSetter(C,"fontFamily","Arial"),e.Factory.addGetterSetter(C,"fontSize",12,(0,a.getNumberValidator)()),e.Factory.addGetterSetter(C,"fontStyle",p),e.Factory.addGetterSetter(C,"fontVariant",p),e.Factory.addGetterSetter(C,"padding",0,(0,a.getNumberValidator)()),e.Factory.addGetterSetter(C,"align",u),e.Factory.addGetterSetter(C,"verticalAlign","top"),e.Factory.addGetterSetter(C,"lineHeight",1,(0,a.getNumberValidator)()),e.Factory.addGetterSetter(C,"wrap","word"),e.Factory.addGetterSetter(C,"ellipsis",!1,(0,a.getBooleanValidator)()),e.Factory.addGetterSetter(C,"letterSpacing",0,(0,a.getNumberValidator)()),e.Factory.addGetterSetter(C,"text","",(0,a.getStringValidator)()),e.Factory.addGetterSetter(C,"textDecoration",""),Dt}var Et,Ot={};var Lt,It={};function Ut(){if(Lt)return It;Lt=1,Object.defineProperty(It,"__esModule",{value:!0}),It.Transformer=void 0;const t=d(),e=m(),i=T(),r=V(),a=Ct(),n=Y(),s=o(),h=v(),l=o();var c="tr-konva",g=["resizeEnabledChange","rotateAnchorOffsetChange","rotateEnabledChange","enabledAnchorsChange","anchorSizeChange","borderEnabledChange","borderStrokeChange","borderStrokeWidthChange","borderDashChange","anchorStrokeChange","anchorStrokeWidthChange","anchorFillChange","anchorCornerRadiusChange","ignoreStrokeChange","anchorStyleFuncChange"].map((t=>t+`.${c}`)).join(" "),u="nodesRect",f=["widthChange","heightChange","scaleXChange","scaleYChange","skewXChange","skewYChange","rotationChange","offsetXChange","offsetYChange","transformsEnabledChange","strokeWidthChange"],p={"top-left":-45,"top-center":0,"top-right":45,"middle-right":-90,"middle-left":90,"bottom-left":-135,"bottom-center":180,"bottom-right":135};const _="ontouchstart"in s.Konva._global;var y=["top-left","top-center","top-right","middle-right","middle-left","bottom-left","bottom-center","bottom-right"];function b(t,e,i){const r=i.x+(t.x-i.x)*Math.cos(e)-(t.y-i.y)*Math.sin(e),a=i.y+(t.x-i.x)*Math.sin(e)+(t.y-i.y)*Math.cos(e);return{...t,rotation:t.rotation+e,x:r,y:a}}function x(t,e){const i=function(t){return{x:t.x+t.width/2*Math.cos(t.rotation)+t.height/2*Math.sin(-t.rotation),y:t.y+t.height/2*Math.cos(t.rotation)+t.width/2*Math.sin(t.rotation)}}(t);return b(t,e,i)}let S=0,C=class extends n.Group{constructor(t){super(t),this._movingAnchorName=null,this._transforming=!1,this._createElements(),this._handleMouseMove=this._handleMouseMove.bind(this),this._handleMouseUp=this._handleMouseUp.bind(this),this.update=this.update.bind(this),this.on(g,this.update),this.getNode()&&this.update()}attachTo(t){return this.setNode(t),this}setNode(e){return t.Util.warn("tr.setNode(shape), tr.node(shape) and tr.attachTo(shape) methods are deprecated. Please use tr.nodes(nodesArray) instead."),this.setNodes([e])}getNode(){return this._nodes&&this._nodes[0]}_getEventNamespace(){return c+this._id}setNodes(e=[]){this._nodes&&this._nodes.length&&this.detach();const i=e.filter((e=>!e.isAncestorOf(this)||(t.Util.error("Konva.Transformer cannot be an a child of the node you are trying to attach"),!1)));return this._nodes=e=i,1===e.length&&this.useSingleNodeRotation()?this.rotation(e[0].getAbsoluteRotation()):this.rotation(0),this._nodes.forEach((t=>{const e=()=>{1===this.nodes().length&&this.useSingleNodeRotation()&&this.rotation(this.nodes()[0].getAbsoluteRotation()),this._resetTransformCache(),this._transforming||this.isDragging()||this.update()},i=t._attrsAffectingSize.map((t=>t+"Change."+this._getEventNamespace())).join(" ");t.on(i,e),t.on(f.map((t=>t+`.${this._getEventNamespace()}`)).join(" "),e),t.on(`absoluteTransformChange.${this._getEventNamespace()}`,e),this._proxyDrag(t)})),this._resetTransformCache(),!!this.findOne(".top-left")&&this.update(),this}_proxyDrag(t){let e;t.on(`dragstart.${this._getEventNamespace()}`,(i=>{e=t.getAbsolutePosition(),this.isDragging()||t===this.findOne(".back")||this.startDrag(i,!1)})),t.on(`dragmove.${this._getEventNamespace()}`,(i=>{if(!e)return;const r=t.getAbsolutePosition(),a=r.x-e.x,n=r.y-e.y;this.nodes().forEach((e=>{if(e===t)return;if(e.isDragging())return;const r=e.getAbsolutePosition();e.setAbsolutePosition({x:r.x+a,y:r.y+n}),e.startDrag(i)})),e=null}))}getNodes(){return this._nodes||[]}getActiveAnchor(){return this._movingAnchorName}detach(){this._nodes&&this._nodes.forEach((t=>{t.off("."+this._getEventNamespace())})),this._nodes=[],this._resetTransformCache()}_resetTransformCache(){this._clearCache(u),this._clearCache("transform"),this._clearSelfAndDescendantCache("absoluteTransform")}_getNodeRect(){return this._getCache(u,this.__getNodeRect)}__getNodeShape(t,e=this.rotation(),i){var r=t.getClientRect({skipTransform:!0,skipShadow:!0,skipStroke:this.ignoreStroke()}),a=t.getAbsoluteScale(i),n=t.getAbsolutePosition(i),o=r.x*a.x-t.offsetX()*a.x,h=r.y*a.y-t.offsetY()*a.y;const l=(s.Konva.getAngle(t.getAbsoluteRotation())+2*Math.PI)%(2*Math.PI);return b({x:n.x+o*Math.cos(l)+h*Math.sin(-l),y:n.y+h*Math.cos(l)+o*Math.sin(l),width:r.width*a.x,height:r.height*a.y,rotation:l},-s.Konva.getAngle(e),{x:0,y:0})}__getNodeRect(){if(!this.getNode())return{x:-1e8,y:-1e8,width:0,height:0,rotation:0};const e=[];this.nodes().map((t=>{const i=t.getClientRect({skipTransform:!0,skipShadow:!0,skipStroke:this.ignoreStroke()});var r=[{x:i.x,y:i.y},{x:i.x+i.width,y:i.y},{x:i.x+i.width,y:i.y+i.height},{x:i.x,y:i.y+i.height}],a=t.getAbsoluteTransform();r.forEach((function(t){var i=a.point(t);e.push(i)}))}));const i=new t.Transform;i.rotate(-s.Konva.getAngle(this.rotation()));var r=1/0,a=1/0,n=-1/0,o=-1/0;e.forEach((function(t){var e=i.point(t);void 0===r&&(r=n=e.x,a=o=e.y),r=Math.min(r,e.x),a=Math.min(a,e.y),n=Math.max(n,e.x),o=Math.max(o,e.y)})),i.invert();const h=i.point({x:r,y:a});return{x:h.x,y:h.y,width:n-r,height:o-a,rotation:s.Konva.getAngle(this.rotation())}}getX(){return this._getNodeRect().x}getY(){return this._getNodeRect().y}getWidth(){return this._getNodeRect().width}getHeight(){return this._getNodeRect().height}_createElements(){this._createBack(),y.forEach((t=>{this._createAnchor(t)})),this._createAnchor("rotater")}_createAnchor(e){var i=new a.Rect({stroke:"rgb(0, 161, 255)",fill:"white",strokeWidth:1,name:e+" _anchor",dragDistance:0,draggable:!0,hitStrokeWidth:_?10:"auto"}),r=this;i.on("mousedown touchstart",(function(t){r._handleMouseDown(t)})),i.on("dragstart",(t=>{i.stopDrag(),t.cancelBubble=!0})),i.on("dragend",(t=>{t.cancelBubble=!0})),i.on("mouseenter",(()=>{var r=s.Konva.getAngle(this.rotation()),a=this.rotateAnchorCursor(),n=function(e,i,r){if("rotater"===e)return r;i+=t.Util.degToRad(p[e]||0);var a=(t.Util.radToDeg(i)%360+360)%360;return t.Util._inRange(a,337.5,360)||t.Util._inRange(a,0,22.5)?"ns-resize":t.Util._inRange(a,22.5,67.5)?"nesw-resize":t.Util._inRange(a,67.5,112.5)?"ew-resize":t.Util._inRange(a,112.5,157.5)?"nwse-resize":t.Util._inRange(a,157.5,202.5)?"ns-resize":t.Util._inRange(a,202.5,247.5)?"nesw-resize":t.Util._inRange(a,247.5,292.5)?"ew-resize":t.Util._inRange(a,292.5,337.5)?"nwse-resize":(t.Util.error("Transformer has unknown angle for cursor detection: "+a),"pointer")}(e,r,a);i.getStage().content&&(i.getStage().content.style.cursor=n),this._cursorChange=!0})),i.on("mouseout",(()=>{i.getStage().content&&(i.getStage().content.style.cursor=""),this._cursorChange=!1})),this.add(i)}_createBack(){var e=new r.Shape({name:"back",width:0,height:0,draggable:!0,sceneFunc(e,i){var r=i.getParent(),a=r.padding();e.beginPath(),e.rect(-a,-a,i.width()+2*a,i.height()+2*a),e.moveTo(i.width()/2,-a),r.rotateEnabled()&&r.rotateLineVisible()&&e.lineTo(i.width()/2,-r.rotateAnchorOffset()*t.Util._sign(i.height())-a),e.fillStrokeShape(i)},hitFunc:(t,e)=>{if(this.shouldOverdrawWholeArea()){var i=this.padding();t.beginPath(),t.rect(-i,-i,e.width()+2*i,e.height()+2*i),t.fillStrokeShape(e)}}});this.add(e),this._proxyDrag(e),e.on("dragstart",(t=>{t.cancelBubble=!0})),e.on("dragmove",(t=>{t.cancelBubble=!0})),e.on("dragend",(t=>{t.cancelBubble=!0})),this.on("dragmove",(t=>{this.update()}))}_handleMouseDown(t){if(!this._transforming){this._movingAnchorName=t.target.name().split(" ")[0];var e=this._getNodeRect(),i=e.width,r=e.height,a=Math.sqrt(Math.pow(i,2)+Math.pow(r,2));this.sin=Math.abs(r/a),this.cos=Math.abs(i/a),"undefined"!=typeof window&&(window.addEventListener("mousemove",this._handleMouseMove),window.addEventListener("touchmove",this._handleMouseMove),window.addEventListener("mouseup",this._handleMouseUp,!0),window.addEventListener("touchend",this._handleMouseUp,!0)),this._transforming=!0;var n=t.target.getAbsolutePosition(),s=t.target.getStage().getPointerPosition();this._anchorDragOffset={x:s.x-n.x,y:s.y-n.y},S++,this._fire("transformstart",{evt:t.evt,target:this.getNode()}),this._nodes.forEach((e=>{e._fire("transformstart",{evt:t.evt,target:e})}))}}_handleMouseMove(t){var e,i,r,a=this.findOne("."+this._movingAnchorName),n=a.getStage();n.setPointersPositions(t);const o=n.getPointerPosition();let h={x:o.x-this._anchorDragOffset.x,y:o.y-this._anchorDragOffset.y};const l=a.getAbsolutePosition();this.anchorDragBoundFunc()&&(h=this.anchorDragBoundFunc()(l,h,t)),a.setAbsolutePosition(h);const d=a.getAbsolutePosition();if(l.x!==d.x||l.y!==d.y)if("rotater"!==this._movingAnchorName){var c,g=this.shiftBehavior();c="inverted"===g?this.keepRatio()&&!t.shiftKey:"none"===g?this.keepRatio():this.keepRatio()||t.shiftKey;var u=this.centeredScaling()||t.altKey;if("top-left"===this._movingAnchorName){if(c){var f=u?{x:this.width()/2,y:this.height()/2}:{x:this.findOne(".bottom-right").x(),y:this.findOne(".bottom-right").y()};r=Math.sqrt(Math.pow(f.x-a.x(),2)+Math.pow(f.y-a.y(),2));var p=this.findOne(".top-left").x()>f.x?-1:1,v=this.findOne(".top-left").y()>f.y?-1:1;e=r*this.cos*p,i=r*this.sin*v,this.findOne(".top-left").x(f.x-e),this.findOne(".top-left").y(f.y-i)}}else if("top-center"===this._movingAnchorName)this.findOne(".top-left").y(a.y());else if("top-right"===this._movingAnchorName){if(c){f=u?{x:this.width()/2,y:this.height()/2}:{x:this.findOne(".bottom-left").x(),y:this.findOne(".bottom-left").y()};r=Math.sqrt(Math.pow(a.x()-f.x,2)+Math.pow(f.y-a.y(),2));p=this.findOne(".top-right").x()<f.x?-1:1,v=this.findOne(".top-right").y()>f.y?-1:1;e=r*this.cos*p,i=r*this.sin*v,this.findOne(".top-right").x(f.x+e),this.findOne(".top-right").y(f.y-i)}var m=a.position();this.findOne(".top-left").y(m.y),this.findOne(".bottom-right").x(m.x)}else if("middle-left"===this._movingAnchorName)this.findOne(".top-left").x(a.x());else if("middle-right"===this._movingAnchorName)this.findOne(".bottom-right").x(a.x());else if("bottom-left"===this._movingAnchorName){if(c){f=u?{x:this.width()/2,y:this.height()/2}:{x:this.findOne(".top-right").x(),y:this.findOne(".top-right").y()};r=Math.sqrt(Math.pow(f.x-a.x(),2)+Math.pow(a.y()-f.y,2));p=f.x<a.x()?-1:1,v=a.y()<f.y?-1:1;e=r*this.cos*p,i=r*this.sin*v,a.x(f.x-e),a.y(f.y+i)}m=a.position(),this.findOne(".top-left").x(m.x),this.findOne(".bottom-right").y(m.y)}else if("bottom-center"===this._movingAnchorName)this.findOne(".bottom-right").y(a.y());else if("bottom-right"===this._movingAnchorName&&c){f=u?{x:this.width()/2,y:this.height()/2}:{x:this.findOne(".top-left").x(),y:this.findOne(".top-left").y()};r=Math.sqrt(Math.pow(a.x()-f.x,2)+Math.pow(a.y()-f.y,2));p=this.findOne(".bottom-right").x()<f.x?-1:1,v=this.findOne(".bottom-right").y()<f.y?-1:1;e=r*this.cos*p,i=r*this.sin*v,this.findOne(".bottom-right").x(f.x+e),this.findOne(".bottom-right").y(f.y+i)}if(u=this.centeredScaling()||t.altKey){var _=this.findOne(".top-left"),y=this.findOne(".bottom-right"),b=_.x(),S=_.y(),C=this.getWidth()-y.x(),w=this.getHeight()-y.y();y.move({x:-b,y:-S}),_.move({x:C,y:w})}var P=this.findOne(".top-left").getAbsolutePosition();e=P.x,i=P.y;var k=this.findOne(".bottom-right").x()-this.findOne(".top-left").x(),A=this.findOne(".bottom-right").y()-this.findOne(".top-left").y();this._fitNodesInto({x:e,y:i,width:k,height:A,rotation:s.Konva.getAngle(this.rotation())},t)}else{var T=this._getNodeRect();e=a.x()-T.width/2,i=-a.y()+T.height/2;let r=Math.atan2(-i,e)+Math.PI/2;T.height<0&&(r-=Math.PI);const n=s.Konva.getAngle(this.rotation())+r,o=s.Konva.getAngle(this.rotationSnapTolerance()),h=x(T,function(t,e,i){let r=e;for(let a=0;a<t.length;a++){const n=s.Konva.getAngle(t[a]),o=Math.abs(n-e)%(2*Math.PI);Math.min(o,2*Math.PI-o)<i&&(r=n)}return r}(this.rotationSnaps(),n,o)-T.rotation);this._fitNodesInto(h,t)}}_handleMouseUp(t){this._removeEvents(t)}getAbsoluteTransform(){return this.getTransform()}_removeEvents(t){var e;if(this._transforming){this._transforming=!1,"undefined"!=typeof window&&(window.removeEventListener("mousemove",this._handleMouseMove),window.removeEventListener("touchmove",this._handleMouseMove),window.removeEventListener("mouseup",this._handleMouseUp,!0),window.removeEventListener("touchend",this._handleMouseUp,!0));var i=this.getNode();S--,this._fire("transformend",{evt:t,target:i}),null===(e=this.getLayer())||void 0===e||e.batchDraw(),i&&this._nodes.forEach((e=>{var i;e._fire("transformend",{evt:t,target:e}),null===(i=e.getLayer())||void 0===i||i.batchDraw()})),this._movingAnchorName=null}}_fitNodesInto(e,i){var r=this._getNodeRect();if(t.Util._inRange(e.width,2*-this.padding()-1,1))return void this.update();if(t.Util._inRange(e.height,2*-this.padding()-1,1))return void this.update();var a=new t.Transform;if(a.rotate(s.Konva.getAngle(this.rotation())),this._movingAnchorName&&e.width<0&&this._movingAnchorName.indexOf("left")>=0){const t=a.point({x:2*-this.padding(),y:0});e.x+=t.x,e.y+=t.y,e.width+=2*this.padding(),this._movingAnchorName=this._movingAnchorName.replace("left","right"),this._anchorDragOffset.x-=t.x,this._anchorDragOffset.y-=t.y}else if(this._movingAnchorName&&e.width<0&&this._movingAnchorName.indexOf("right")>=0){const t=a.point({x:2*this.padding(),y:0});this._movingAnchorName=this._movingAnchorName.replace("right","left"),this._anchorDragOffset.x-=t.x,this._anchorDragOffset.y-=t.y,e.width+=2*this.padding()}if(this._movingAnchorName&&e.height<0&&this._movingAnchorName.indexOf("top")>=0){const t=a.point({x:0,y:2*-this.padding()});e.x+=t.x,e.y+=t.y,this._movingAnchorName=this._movingAnchorName.replace("top","bottom"),this._anchorDragOffset.x-=t.x,this._anchorDragOffset.y-=t.y,e.height+=2*this.padding()}else if(this._movingAnchorName&&e.height<0&&this._movingAnchorName.indexOf("bottom")>=0){const t=a.point({x:0,y:2*this.padding()});this._movingAnchorName=this._movingAnchorName.replace("bottom","top"),this._anchorDragOffset.x-=t.x,this._anchorDragOffset.y-=t.y,e.height+=2*this.padding()}if(this.boundBoxFunc()){const i=this.boundBoxFunc()(r,e);i?e=i:t.Util.warn("boundBoxFunc returned falsy. You should return new bound rect from it!")}const n=1e7,o=new t.Transform;o.translate(r.x,r.y),o.rotate(r.rotation),o.scale(r.width/n,r.height/n);const h=new t.Transform,l=e.width/n,d=e.height/n;!1===this.flipEnabled()?(h.translate(e.x,e.y),h.rotate(e.rotation),h.translate(e.width<0?e.width:0,e.height<0?e.height:0),h.scale(Math.abs(l),Math.abs(d))):(h.translate(e.x,e.y),h.rotate(e.rotation),h.scale(l,d));const c=h.multiply(o.invert());this._nodes.forEach((e=>{var i;const r=e.getParent().getAbsoluteTransform(),a=e.getTransform().copy();a.translate(e.offsetX(),e.offsetY());const n=new t.Transform;n.multiply(r.copy().invert()).multiply(c).multiply(r).multiply(a);const s=n.decompose();e.setAttrs(s),null===(i=e.getLayer())||void 0===i||i.batchDraw()})),this.rotation(t.Util._getRotation(e.rotation)),this._nodes.forEach((t=>{this._fire("transform",{evt:i,target:t}),t._fire("transform",{evt:i,target:t})})),this._resetTransformCache(),this.update(),this.getLayer().batchDraw()}forceUpdate(){this._resetTransformCache(),this.update()}_batchChangeChild(t,e){this.findOne(t).setAttrs(e)}update(){var e,i=this._getNodeRect();this.rotation(t.Util._getRotation(i.rotation));var r=i.width,a=i.height,n=this.enabledAnchors(),s=this.resizeEnabled(),o=this.padding(),h=this.anchorSize();const l=this.find("._anchor");l.forEach((t=>{t.setAttrs({width:h,height:h,offsetX:h/2,offsetY:h/2,stroke:this.anchorStroke(),strokeWidth:this.anchorStrokeWidth(),fill:this.anchorFill(),cornerRadius:this.anchorCornerRadius()})})),this._batchChangeChild(".top-left",{x:0,y:0,offsetX:h/2+o,offsetY:h/2+o,visible:s&&n.indexOf("top-left")>=0}),this._batchChangeChild(".top-center",{x:r/2,y:0,offsetY:h/2+o,visible:s&&n.indexOf("top-center")>=0}),this._batchChangeChild(".top-right",{x:r,y:0,offsetX:h/2-o,offsetY:h/2+o,visible:s&&n.indexOf("top-right")>=0}),this._batchChangeChild(".middle-left",{x:0,y:a/2,offsetX:h/2+o,visible:s&&n.indexOf("middle-left")>=0}),this._batchChangeChild(".middle-right",{x:r,y:a/2,offsetX:h/2-o,visible:s&&n.indexOf("middle-right")>=0}),this._batchChangeChild(".bottom-left",{x:0,y:a,offsetX:h/2+o,offsetY:h/2-o,visible:s&&n.indexOf("bottom-left")>=0}),this._batchChangeChild(".bottom-center",{x:r/2,y:a,offsetY:h/2-o,visible:s&&n.indexOf("bottom-center")>=0}),this._batchChangeChild(".bottom-right",{x:r,y:a,offsetX:h/2-o,offsetY:h/2-o,visible:s&&n.indexOf("bottom-right")>=0}),this._batchChangeChild(".rotater",{x:r/2,y:-this.rotateAnchorOffset()*t.Util._sign(a)-o,visible:this.rotateEnabled()}),this._batchChangeChild(".back",{width:r,height:a,visible:this.borderEnabled(),stroke:this.borderStroke(),strokeWidth:this.borderStrokeWidth(),dash:this.borderDash(),x:0,y:0});const d=this.anchorStyleFunc();d&&l.forEach((t=>{d(t)})),null===(e=this.getLayer())||void 0===e||e.batchDraw()}isTransforming(){return this._transforming}stopTransform(){if(this._transforming){this._removeEvents();var t=this.findOne("."+this._movingAnchorName);t&&t.stopDrag()}}destroy(){return this.getStage()&&this._cursorChange&&this.getStage().content&&(this.getStage().content.style.cursor=""),n.Group.prototype.destroy.call(this),this.detach(),this._removeEvents(),this}toObject(){return i.Node.prototype.toObject.call(this)}clone(t){return i.Node.prototype.clone.call(this,t)}getClientRect(){return this.nodes().length>0?super.getClientRect():{x:0,y:0,width:0,height:0}}};return It.Transformer=C,C.isTransforming=()=>S>0,C.prototype.className="Transformer",(0,l._registerNode)(C),e.Factory.addGetterSetter(C,"enabledAnchors",y,(function(e){return e instanceof Array||t.Util.warn("enabledAnchors value should be an array"),e instanceof Array&&e.forEach((function(e){-1===y.indexOf(e)&&t.Util.warn("Unknown anchor name: "+e+". Available names are: "+y.join(", "))})),e||[]})),e.Factory.addGetterSetter(C,"flipEnabled",!0,(0,h.getBooleanValidator)()),e.Factory.addGetterSetter(C,"resizeEnabled",!0),e.Factory.addGetterSetter(C,"anchorSize",10,(0,h.getNumberValidator)()),e.Factory.addGetterSetter(C,"rotateEnabled",!0),e.Factory.addGetterSetter(C,"rotateLineVisible",!0),e.Factory.addGetterSetter(C,"rotationSnaps",[]),e.Factory.addGetterSetter(C,"rotateAnchorOffset",50,(0,h.getNumberValidator)()),e.Factory.addGetterSetter(C,"rotateAnchorCursor","crosshair"),e.Factory.addGetterSetter(C,"rotationSnapTolerance",5,(0,h.getNumberValidator)()),e.Factory.addGetterSetter(C,"borderEnabled",!0),e.Factory.addGetterSetter(C,"anchorStroke","rgb(0, 161, 255)"),e.Factory.addGetterSetter(C,"anchorStrokeWidth",1,(0,h.getNumberValidator)()),e.Factory.addGetterSetter(C,"anchorFill","white"),e.Factory.addGetterSetter(C,"anchorCornerRadius",0,(0,h.getNumberValidator)()),e.Factory.addGetterSetter(C,"borderStroke","rgb(0, 161, 255)"),e.Factory.addGetterSetter(C,"borderStrokeWidth",1,(0,h.getNumberValidator)()),e.Factory.addGetterSetter(C,"borderDash"),e.Factory.addGetterSetter(C,"keepRatio",!0),e.Factory.addGetterSetter(C,"shiftBehavior","default"),e.Factory.addGetterSetter(C,"centeredScaling",!1),e.Factory.addGetterSetter(C,"ignoreStroke",!1),e.Factory.addGetterSetter(C,"padding",0,(0,h.getNumberValidator)()),e.Factory.addGetterSetter(C,"node"),e.Factory.addGetterSetter(C,"nodes"),e.Factory.addGetterSetter(C,"boundBoxFunc"),e.Factory.addGetterSetter(C,"anchorDragBoundFunc"),e.Factory.addGetterSetter(C,"anchorStyleFunc"),e.Factory.addGetterSetter(C,"shouldOverdrawWholeArea",!1),e.Factory.addGetterSetter(C,"useSingleNodeRotation",!0),e.Factory.backCompat(C,{lineEnabled:"borderEnabled",rotateHandlerOffset:"rotateAnchorOffset",enabledHandlers:"enabledAnchors"}),It}var Bt,Vt={};var Ht,jt={};function zt(){if(Ht)return jt;Ht=1,Object.defineProperty(jt,"__esModule",{value:!0}),jt.Blur=void 0;const t=m(),e=T(),i=v();function r(){this.r=0,this.g=0,this.b=0,this.a=0,this.next=null}var a=[512,512,456,512,328,456,335,512,405,328,271,456,388,335,292,512,454,405,364,328,298,271,496,456,420,388,360,335,312,292,273,512,482,454,428,405,383,364,345,328,312,298,284,271,259,496,475,456,437,420,404,388,374,360,347,335,323,312,302,292,282,273,265,512,497,482,468,454,441,428,417,405,394,383,373,364,354,345,337,328,320,312,305,298,291,284,278,271,265,259,507,496,485,475,465,456,446,437,428,420,412,404,396,388,381,374,367,360,354,347,341,335,329,323,318,312,307,302,297,292,287,282,278,273,269,265,261,512,505,497,489,482,475,468,461,454,447,441,435,428,422,417,411,405,399,394,389,383,378,373,368,364,359,354,350,345,341,337,332,328,324,320,316,312,309,305,301,298,294,291,287,284,281,278,274,271,268,265,262,259,257,507,501,496,491,485,480,475,470,465,460,456,451,446,442,437,433,428,424,420,416,412,408,404,400,396,392,388,385,381,377,374,370,367,363,360,357,354,350,347,344,341,338,335,332,329,326,323,320,318,315,312,310,307,304,302,299,297,294,292,289,287,285,282,280,278,275,273,271,269,267,265,263,261,259],n=[9,11,12,13,13,14,14,15,15,15,15,16,16,16,16,17,17,17,17,17,17,17,18,18,18,18,18,18,18,18,18,19,19,19,19,19,19,19,19,19,19,19,19,19,19,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24];return jt.Blur=function(t){var e=Math.round(this.blurRadius());e>0&&function(t,e){var i,s,o,h,l,d,c,g,u,f,p,v,m,_,y,b,x,S,C,w,P,k,A,T,F=t.data,M=t.width,G=t.height,R=e+e+1,D=M-1,N=G-1,E=e+1,O=E*(E+1)/2,L=new r,I=null,U=L,B=null,V=null,H=a[e],j=n[e];for(o=1;o<R;o++)U=U.next=new r,o===E&&(I=U);for(U.next=L,c=d=0,s=0;s<G;s++){for(b=x=S=C=g=u=f=p=0,v=E*(w=F[d]),m=E*(P=F[d+1]),_=E*(k=F[d+2]),y=E*(A=F[d+3]),g+=O*w,u+=O*P,f+=O*k,p+=O*A,U=L,o=0;o<E;o++)U.r=w,U.g=P,U.b=k,U.a=A,U=U.next;for(o=1;o<E;o++)h=d+((D<o?D:o)<<2),g+=(U.r=w=F[h])*(T=E-o),u+=(U.g=P=F[h+1])*T,f+=(U.b=k=F[h+2])*T,p+=(U.a=A=F[h+3])*T,b+=w,x+=P,S+=k,C+=A,U=U.next;for(B=L,V=I,i=0;i<M;i++)F[d+3]=A=p*H>>j,0!==A?(A=255/A,F[d]=(g*H>>j)*A,F[d+1]=(u*H>>j)*A,F[d+2]=(f*H>>j)*A):F[d]=F[d+1]=F[d+2]=0,g-=v,u-=m,f-=_,p-=y,v-=B.r,m-=B.g,_-=B.b,y-=B.a,h=c+((h=i+e+1)<D?h:D)<<2,g+=b+=B.r=F[h],u+=x+=B.g=F[h+1],f+=S+=B.b=F[h+2],p+=C+=B.a=F[h+3],B=B.next,v+=w=V.r,m+=P=V.g,_+=k=V.b,y+=A=V.a,b-=w,x-=P,S-=k,C-=A,V=V.next,d+=4;c+=M}for(i=0;i<M;i++){for(x=S=C=b=u=f=p=g=0,v=E*(w=F[d=i<<2]),m=E*(P=F[d+1]),_=E*(k=F[d+2]),y=E*(A=F[d+3]),g+=O*w,u+=O*P,f+=O*k,p+=O*A,U=L,o=0;o<E;o++)U.r=w,U.g=P,U.b=k,U.a=A,U=U.next;for(l=M,o=1;o<=e;o++)d=l+i<<2,g+=(U.r=w=F[d])*(T=E-o),u+=(U.g=P=F[d+1])*T,f+=(U.b=k=F[d+2])*T,p+=(U.a=A=F[d+3])*T,b+=w,x+=P,S+=k,C+=A,U=U.next,o<N&&(l+=M);for(d=i,B=L,V=I,s=0;s<G;s++)F[3+(h=d<<2)]=A=p*H>>j,A>0?(A=255/A,F[h]=(g*H>>j)*A,F[h+1]=(u*H>>j)*A,F[h+2]=(f*H>>j)*A):F[h]=F[h+1]=F[h+2]=0,g-=v,u-=m,f-=_,p-=y,v-=B.r,m-=B.g,_-=B.b,y-=B.a,h=i+((h=s+E)<N?h:N)*M<<2,g+=b+=B.r=F[h],u+=x+=B.g=F[h+1],f+=S+=B.b=F[h+2],p+=C+=B.a=F[h+3],B=B.next,v+=w=V.r,m+=P=V.g,_+=k=V.b,y+=A=V.a,b-=w,x-=P,S-=k,C-=A,V=V.next,d+=M}}(t,e)},t.Factory.addGetterSetter(e.Node,"blurRadius",0,(0,i.getNumberValidator)(),t.Factory.afterSetFilter),jt}var Wt,Kt={};var Yt,Xt={};var qt,Qt={};var Jt,$t={};var Zt,te={};var ee,ie={};var re,ae={};var ne,se={};var oe,he={};function le(){if(oe)return he;oe=1,Object.defineProperty(he,"__esModule",{value:!0}),he.Kaleidoscope=void 0;const t=m(),e=T(),i=d(),r=v();return he.Kaleidoscope=function(t){var e,r,a,n,s,o,h,l,d,c=t.width,g=t.height,u=Math.round(this.kaleidoscopePower()),f=Math.round(this.kaleidoscopeAngle()),p=Math.floor(c*(f%360)/360);if(!(u<1)){var v=i.Util.createCanvasElement();v.width=c,v.height=g;var m=v.getContext("2d").getImageData(0,0,c,g);i.Util.releaseCanvas(v),function(t,e,i){var r,a,n,s,o=t.data,h=e.data,l=t.width,d=t.height,c=i.polarCenterX||l/2,g=i.polarCenterY||d/2,u=0,f=0,p=0,v=0,m=Math.sqrt(c*c+g*g);a=l-c,n=d-g,m=(s=Math.sqrt(a*a+n*n))>m?s:m;var _,y,b,x,S=d,C=l,w=360/C*Math.PI/180;for(y=0;y<C;y+=1)for(b=Math.sin(y*w),x=Math.cos(y*w),_=0;_<S;_+=1)a=Math.floor(c+m*_/S*x),u=o[0+(r=4*((n=Math.floor(g+m*_/S*b))*l+a))],f=o[r+1],p=o[r+2],v=o[r+3],h[0+(r=4*(y+_*l))]=u,h[r+1]=f,h[r+2]=p,h[r+3]=v}(t,m,{polarCenterX:c/2,polarCenterY:g/2});for(var _=c/Math.pow(2,u);_<=8;)_*=2,u-=1;var y=_=Math.ceil(_),b=0,x=y,S=1;for(p+_>c&&(b=y,x=0,S=-1),r=0;r<g;r+=1)for(e=b;e!==x;e+=S)l=4*(c*r+Math.round(e+p)%c),n=m.data[l+0],s=m.data[l+1],o=m.data[l+2],h=m.data[l+3],d=4*(c*r+e),m.data[d+0]=n,m.data[d+1]=s,m.data[d+2]=o,m.data[d+3]=h;for(r=0;r<g;r+=1)for(y=Math.floor(_),a=0;a<u;a+=1){for(e=0;e<y+1;e+=1)l=4*(c*r+e),n=m.data[l+0],s=m.data[l+1],o=m.data[l+2],h=m.data[l+3],d=4*(c*r+2*y-e-1),m.data[d+0]=n,m.data[d+1]=s,m.data[d+2]=o,m.data[d+3]=h;y*=2}!function(t,e,i){var r,a,n,s,o,h,l=t.data,d=e.data,c=t.width,g=t.height,u=i.polarCenterX||c/2,f=i.polarCenterY||g/2,p=0,v=0,m=0,_=0,y=Math.sqrt(u*u+f*f);a=c-u,n=g-f,y=(h=Math.sqrt(a*a+n*n))>y?h:y;var b,x,S,C=g,w=c,P=i.polarRotation||0;for(a=0;a<c;a+=1)for(n=0;n<g;n+=1)s=a-u,o=n-f,b=Math.sqrt(s*s+o*o)*C/y,x=(x=(180*Math.atan2(o,s)/Math.PI+360+P)%360)*w/360,S=Math.floor(x),p=l[0+(r=4*(Math.floor(b)*c+S))],v=l[r+1],m=l[r+2],_=l[r+3],d[0+(r=4*(n*c+a))]=p,d[r+1]=v,d[r+2]=m,d[r+3]=_}(m,t,{polarRotation:0})}},t.Factory.addGetterSetter(e.Node,"kaleidoscopePower",2,(0,r.getNumberValidator)(),t.Factory.afterSetFilter),t.Factory.addGetterSetter(e.Node,"kaleidoscopeAngle",0,(0,r.getNumberValidator)(),t.Factory.afterSetFilter),he}var de,ce={};function ge(){if(de)return ce;de=1,Object.defineProperty(ce,"__esModule",{value:!0}),ce.Mask=void 0;const t=m(),e=T(),i=v();function r(t,e,i){var r=4*(i*t.width+e),a=[];return a.push(t.data[r++],t.data[r++],t.data[r++],t.data[r++]),a}function a(t,e){return Math.sqrt(Math.pow(t[0]-e[0],2)+Math.pow(t[1]-e[1],2)+Math.pow(t[2]-e[2],2))}return ce.Mask=function(t){var e=function(t,e){var i=r(t,0,0),n=r(t,t.width-1,0),s=r(t,0,t.height-1),o=r(t,t.width-1,t.height-1),h=e||10;if(a(i,n)<h&&a(n,o)<h&&a(o,s)<h&&a(s,i)<h){for(var l=function(t){for(var e=[0,0,0],i=0;i<t.length;i++)e[0]+=t[i][0],e[1]+=t[i][1],e[2]+=t[i][2];return e[0]/=t.length,e[1]/=t.length,e[2]/=t.length,e}([n,i,o,s]),d=[],c=0;c<t.width*t.height;c++){var g=a(l,[t.data[4*c],t.data[4*c+1],t.data[4*c+2]]);d[c]=g<h?0:255}return d}}(t,this.threshold());return e&&function(t,e){for(var i=0;i<t.width*t.height;i++)t.data[4*i+3]=e[i]}(t,e=function(t,e,i){for(var r=[1/9,1/9,1/9,1/9,1/9,1/9,1/9,1/9,1/9],a=Math.round(Math.sqrt(r.length)),n=Math.floor(a/2),s=[],o=0;o<i;o++)for(var h=0;h<e;h++){for(var l=o*e+h,d=0,c=0;c<a;c++)for(var g=0;g<a;g++){var u=o+c-n,f=h+g-n;if(u>=0&&u<i&&f>=0&&f<e){var p=r[c*a+g];d+=t[u*e+f]*p}}s[l]=d}return s}(e=function(t,e,i){for(var r=[1,1,1,1,1,1,1,1,1],a=Math.round(Math.sqrt(r.length)),n=Math.floor(a/2),s=[],o=0;o<i;o++)for(var h=0;h<e;h++){for(var l=o*e+h,d=0,c=0;c<a;c++)for(var g=0;g<a;g++){var u=o+c-n,f=h+g-n;if(u>=0&&u<i&&f>=0&&f<e){var p=r[c*a+g];d+=t[u*e+f]*p}}s[l]=d>=1020?255:0}return s}(e=function(t,e,i){for(var r=[1,1,1,1,0,1,1,1,1],a=Math.round(Math.sqrt(r.length)),n=Math.floor(a/2),s=[],o=0;o<i;o++)for(var h=0;h<e;h++){for(var l=o*e+h,d=0,c=0;c<a;c++)for(var g=0;g<a;g++){var u=o+c-n,f=h+g-n;if(u>=0&&u<i&&f>=0&&f<e){var p=r[c*a+g];d+=t[u*e+f]*p}}s[l]=2040===d?255:0}return s}(e,t.width,t.height),t.width,t.height),t.width,t.height)),t},t.Factory.addGetterSetter(e.Node,"threshold",0,(0,i.getNumberValidator)(),t.Factory.afterSetFilter),ce}var ue,fe={};var pe,ve={};var me,_e={};var ye,be={};var xe,Se={};var Ce,we={};var Pe,ke={};var Ae,Te,Fe={};function Me(){if(Te)return a;Te=1,Object.defineProperty(a,"__esModule",{value:!0}),a.Konva=void 0;const t=tt(),e=function(){if(et)return it;et=1,Object.defineProperty(it,"__esModule",{value:!0}),it.Arc=void 0;const t=m(),e=V(),i=o(),r=v(),a=o();let n=class extends e.Shape{_sceneFunc(t){var e=i.Konva.getAngle(this.angle()),r=this.clockwise();t.beginPath(),t.arc(0,0,this.outerRadius(),0,e,r),t.arc(0,0,this.innerRadius(),e,0,!r),t.closePath(),t.fillStrokeShape(this)}getWidth(){return 2*this.outerRadius()}getHeight(){return 2*this.outerRadius()}setWidth(t){this.outerRadius(t/2)}setHeight(t){this.outerRadius(t/2)}getSelfRect(){const t=this.innerRadius(),e=this.outerRadius(),r=this.clockwise(),a=i.Konva.getAngle(r?360-this.angle():this.angle()),n=Math.cos(Math.min(a,Math.PI)),s=Math.sin(Math.min(Math.max(Math.PI,a),3*Math.PI/2)),o=Math.sin(Math.min(a,Math.PI/2)),h=n*(n>0?t:e),l=s*(s>0?t:e),d=o*(o>0?e:t);return{x:h,y:r?-1*d:l,width:1*e-h,height:d-l}}};return it.Arc=n,n.prototype._centroid=!0,n.prototype.className="Arc",n.prototype._attrsAffectingSize=["innerRadius","outerRadius"],(0,a._registerNode)(n),t.Factory.addGetterSetter(n,"innerRadius",0,(0,r.getNumberValidator)()),t.Factory.addGetterSetter(n,"outerRadius",0,(0,r.getNumberValidator)()),t.Factory.addGetterSetter(n,"angle",0,(0,r.getNumberValidator)()),t.Factory.addGetterSetter(n,"clockwise",!1,(0,r.getBooleanValidator)()),it}(),i=function(){if(lt)return at;lt=1,Object.defineProperty(at,"__esModule",{value:!0}),at.Arrow=void 0;const t=m(),e=st(),i=v(),r=o(),a=gt();let n=class extends e.Line{_sceneFunc(t){super._sceneFunc(t);var e=2*Math.PI,i=this.points(),r=i,n=0!==this.tension()&&i.length>4;n&&(r=this.getTensionPoints());var s,o,h=this.pointerLength(),l=i.length;if(n){const t=[r[r.length-4],r[r.length-3],r[r.length-2],r[r.length-1],i[l-2],i[l-1]],e=a.Path.calcLength(r[r.length-4],r[r.length-3],"C",t),n=a.Path.getPointOnQuadraticBezier(Math.min(1,1-h/e),t[0],t[1],t[2],t[3],t[4],t[5]);s=i[l-2]-n.x,o=i[l-1]-n.y}else s=i[l-2]-i[l-4],o=i[l-1]-i[l-3];var d=(Math.atan2(o,s)+e)%e,c=this.pointerWidth();this.pointerAtEnding()&&(t.save(),t.beginPath(),t.translate(i[l-2],i[l-1]),t.rotate(d),t.moveTo(0,0),t.lineTo(-h,c/2),t.lineTo(-h,-c/2),t.closePath(),t.restore(),this.__fillStroke(t)),this.pointerAtBeginning()&&(t.save(),t.beginPath(),t.translate(i[0],i[1]),n?(s=(r[0]+r[2])/2-i[0],o=(r[1]+r[3])/2-i[1]):(s=i[2]-i[0],o=i[3]-i[1]),t.rotate((Math.atan2(-o,-s)+e)%e),t.moveTo(0,0),t.lineTo(-h,c/2),t.lineTo(-h,-c/2),t.closePath(),t.restore(),this.__fillStroke(t))}__fillStroke(t){var e=this.dashEnabled();e&&(this.attrs.dashEnabled=!1,t.setLineDash([])),t.fillStrokeShape(this),e&&(this.attrs.dashEnabled=!0)}getSelfRect(){const t=super.getSelfRect(),e=this.pointerWidth()/2;return{x:t.x-e,y:t.y-e,width:t.width+2*e,height:t.height+2*e}}};return at.Arrow=n,n.prototype.className="Arrow",(0,r._registerNode)(n),t.Factory.addGetterSetter(n,"pointerLength",10,(0,i.getNumberValidator)()),t.Factory.addGetterSetter(n,"pointerWidth",10,(0,i.getNumberValidator)()),t.Factory.addGetterSetter(n,"pointerAtBeginning",!1),t.Factory.addGetterSetter(n,"pointerAtEnding",!0),at}(),r=function(){if(ut)return ft;ut=1,Object.defineProperty(ft,"__esModule",{value:!0}),ft.Circle=void 0;const t=m(),e=V(),i=v(),r=o();let a=class extends e.Shape{_sceneFunc(t){t.beginPath(),t.arc(0,0,this.attrs.radius||0,0,2*Math.PI,!1),t.closePath(),t.fillStrokeShape(this)}getWidth(){return 2*this.radius()}getHeight(){return 2*this.radius()}setWidth(t){this.radius()!==t/2&&this.radius(t/2)}setHeight(t){this.radius()!==t/2&&this.radius(t/2)}};return ft.Circle=a,a.prototype._centroid=!0,a.prototype.className="Circle",a.prototype._attrsAffectingSize=["radius"],(0,r._registerNode)(a),t.Factory.addGetterSetter(a,"radius",0,(0,i.getNumberValidator)()),ft}(),n=function(){if(pt)return vt;pt=1,Object.defineProperty(vt,"__esModule",{value:!0}),vt.Ellipse=void 0;const t=m(),e=V(),i=v(),r=o();let a=class extends e.Shape{_sceneFunc(t){var e=this.radiusX(),i=this.radiusY();t.beginPath(),t.save(),e!==i&&t.scale(1,i/e),t.arc(0,0,e,0,2*Math.PI,!1),t.restore(),t.closePath(),t.fillStrokeShape(this)}getWidth(){return 2*this.radiusX()}getHeight(){return 2*this.radiusY()}setWidth(t){this.radiusX(t/2)}setHeight(t){this.radiusY(t/2)}};return vt.Ellipse=a,a.prototype.className="Ellipse",a.prototype._centroid=!0,a.prototype._attrsAffectingSize=["radiusX","radiusY"],(0,r._registerNode)(a),t.Factory.addComponentsGetterSetter(a,"radius",["x","y"]),t.Factory.addGetterSetter(a,"radiusX",0,(0,i.getNumberValidator)()),t.Factory.addGetterSetter(a,"radiusY",0,(0,i.getNumberValidator)()),vt}(),s=function(){if(mt)return _t;mt=1,Object.defineProperty(_t,"__esModule",{value:!0}),_t.Image=void 0;const t=d(),e=m(),i=V(),r=o(),a=v();let n=class e extends i.Shape{constructor(t){super(t),this.on("imageChange.konva",(()=>{this._setImageLoad()})),this._setImageLoad()}_setImageLoad(){const t=this.image();t&&t.complete||t&&4===t.readyState||t&&t.addEventListener&&t.addEventListener("load",(()=>{this._requestDraw()}))}_useBufferCanvas(){return super._useBufferCanvas(!0)}_sceneFunc(e){const i=this.getWidth(),r=this.getHeight(),a=this.cornerRadius(),n=this.attrs.image;let s;if(n){const t=this.attrs.cropWidth,e=this.attrs.cropHeight;s=t&&e?[n,this.cropX(),this.cropY(),t,e,0,0,i,r]:[n,0,0,i,r]}(this.hasFill()||this.hasStroke()||a)&&(e.beginPath(),a?t.Util.drawRoundedRectPath(e,i,r,a):e.rect(0,0,i,r),e.closePath(),e.fillStrokeShape(this)),n&&(a&&e.clip(),e.drawImage.apply(e,s))}_hitFunc(e){var i=this.width(),r=this.height(),a=this.cornerRadius();e.beginPath(),a?t.Util.drawRoundedRectPath(e,i,r,a):e.rect(0,0,i,r),e.closePath(),e.fillStrokeShape(this)}getWidth(){var t,e;return null!==(t=this.attrs.width)&&void 0!==t?t:null===(e=this.image())||void 0===e?void 0:e.width}getHeight(){var t,e;return null!==(t=this.attrs.height)&&void 0!==t?t:null===(e=this.image())||void 0===e?void 0:e.height}static fromURL(i,r,a=null){var n=t.Util.createImageElement();n.onload=function(){var t=new e({image:n});r(t)},n.onerror=a,n.crossOrigin="Anonymous",n.src=i}};return _t.Image=n,n.prototype.className="Image",(0,r._registerNode)(n),e.Factory.addGetterSetter(n,"cornerRadius",0,(0,a.getNumberOrArrayOfNumbersValidator)(4)),e.Factory.addGetterSetter(n,"image"),e.Factory.addComponentsGetterSetter(n,"crop",["x","y","width","height"]),e.Factory.addGetterSetter(n,"cropX",0,(0,a.getNumberValidator)()),e.Factory.addGetterSetter(n,"cropY",0,(0,a.getNumberValidator)()),e.Factory.addGetterSetter(n,"cropWidth",0,(0,a.getNumberValidator)()),e.Factory.addGetterSetter(n,"cropHeight",0,(0,a.getNumberValidator)()),_t}(),h=function(){if(yt)return bt;yt=1,Object.defineProperty(bt,"__esModule",{value:!0}),bt.Tag=bt.Label=void 0;const t=m(),e=V(),i=Y(),r=v(),a=o();var n=["fontFamily","fontSize","fontStyle","padding","lineHeight","text","width","height","pointerDirection","pointerWidth","pointerHeight"],s="up",h="right",l="down",d="left",c=n.length;let g=class extends i.Group{constructor(t){super(t),this.on("add.konva",(function(t){this._addListeners(t.child),this._sync()}))}getText(){return this.find("Text")[0]}getTag(){return this.find("Tag")[0]}_addListeners(t){var e,i=this,r=function(){i._sync()};for(e=0;e<c;e++)t.on(n[e]+"Change.konva",r)}getWidth(){return this.getText().width()}getHeight(){return this.getText().height()}_sync(){var t,e,i,r,a,n,o,c=this.getText(),g=this.getTag();if(c&&g){switch(t=c.width(),e=c.height(),i=g.pointerDirection(),r=g.pointerWidth(),o=g.pointerHeight(),a=0,n=0,i){case s:a=t/2,n=-1*o;break;case h:a=t+r,n=e/2;break;case l:a=t/2,n=e+o;break;case d:a=-1*r,n=e/2}g.setAttrs({x:-1*a,y:-1*n,width:t,height:e}),c.setAttrs({x:-1*a,y:-1*n})}}};bt.Label=g,g.prototype.className="Label",(0,a._registerNode)(g);class u extends e.Shape{_sceneFunc(t){var e=this.width(),i=this.height(),r=this.pointerDirection(),a=this.pointerWidth(),n=this.pointerHeight(),o=this.cornerRadius();let c=0,g=0,u=0,f=0;"number"==typeof o?c=g=u=f=Math.min(o,e/2,i/2):(c=Math.min(o[0]||0,e/2,i/2),g=Math.min(o[1]||0,e/2,i/2),f=Math.min(o[2]||0,e/2,i/2),u=Math.min(o[3]||0,e/2,i/2)),t.beginPath(),t.moveTo(c,0),r===s&&(t.lineTo((e-a)/2,0),t.lineTo(e/2,-1*n),t.lineTo((e+a)/2,0)),t.lineTo(e-g,0),t.arc(e-g,g,g,3*Math.PI/2,0,!1),r===h&&(t.lineTo(e,(i-n)/2),t.lineTo(e+a,i/2),t.lineTo(e,(i+n)/2)),t.lineTo(e,i-f),t.arc(e-f,i-f,f,0,Math.PI/2,!1),r===l&&(t.lineTo((e+a)/2,i),t.lineTo(e/2,i+n),t.lineTo((e-a)/2,i)),t.lineTo(u,i),t.arc(u,i-u,u,Math.PI/2,Math.PI,!1),r===d&&(t.lineTo(0,(i+n)/2),t.lineTo(-1*a,i/2),t.lineTo(0,(i-n)/2)),t.lineTo(0,c),t.arc(c,c,c,Math.PI,3*Math.PI/2,!1),t.closePath(),t.fillStrokeShape(this)}getSelfRect(){var t=0,e=0,i=this.pointerWidth(),r=this.pointerHeight(),a=this.pointerDirection(),n=this.width(),o=this.height();return a===s?(e-=r,o+=r):a===l?o+=r:a===d?(t-=1.5*i,n+=i):a===h&&(n+=1.5*i),{x:t,y:e,width:n,height:o}}}return bt.Tag=u,u.prototype.className="Tag",(0,a._registerNode)(u),t.Factory.addGetterSetter(u,"pointerDirection","none"),t.Factory.addGetterSetter(u,"pointerWidth",0,(0,r.getNumberValidator)()),t.Factory.addGetterSetter(u,"pointerHeight",0,(0,r.getNumberValidator)()),t.Factory.addGetterSetter(u,"cornerRadius",0,(0,r.getNumberOrArrayOfNumbersValidator)(4)),bt}(),l=st(),c=gt(),g=Ct(),u=function(){if(wt)return Pt;wt=1,Object.defineProperty(Pt,"__esModule",{value:!0}),Pt.RegularPolygon=void 0;const t=m(),e=V(),i=v(),r=o();let a=class extends e.Shape{_sceneFunc(t){const e=this._getPoints();t.beginPath(),t.moveTo(e[0].x,e[0].y);for(var i=1;i<e.length;i++)t.lineTo(e[i].x,e[i].y);t.closePath(),t.fillStrokeShape(this)}_getPoints(){const t=this.attrs.sides,e=this.attrs.radius||0,i=[];for(var r=0;r<t;r++)i.push({x:e*Math.sin(2*r*Math.PI/t),y:-1*e*Math.cos(2*r*Math.PI/t)});return i}getSelfRect(){const t=this._getPoints();var e=t[0].x,i=t[0].y,r=t[0].x,a=t[0].y;return t.forEach((t=>{e=Math.min(e,t.x),i=Math.max(i,t.x),r=Math.min(r,t.y),a=Math.max(a,t.y)})),{x:e,y:r,width:i-e,height:a-r}}getWidth(){return 2*this.radius()}getHeight(){return 2*this.radius()}setWidth(t){this.radius(t/2)}setHeight(t){this.radius(t/2)}};return Pt.RegularPolygon=a,a.prototype.className="RegularPolygon",a.prototype._centroid=!0,a.prototype._attrsAffectingSize=["radius"],(0,r._registerNode)(a),t.Factory.addGetterSetter(a,"radius",0,(0,i.getNumberValidator)()),t.Factory.addGetterSetter(a,"sides",0,(0,i.getNumberValidator)()),Pt}(),f=function(){if(kt)return At;kt=1,Object.defineProperty(At,"__esModule",{value:!0}),At.Ring=void 0;const t=m(),e=V(),i=v(),r=o();var a=2*Math.PI;let n=class extends e.Shape{_sceneFunc(t){t.beginPath(),t.arc(0,0,this.innerRadius(),0,a,!1),t.moveTo(this.outerRadius(),0),t.arc(0,0,this.outerRadius(),a,0,!0),t.closePath(),t.fillStrokeShape(this)}getWidth(){return 2*this.outerRadius()}getHeight(){return 2*this.outerRadius()}setWidth(t){this.outerRadius(t/2)}setHeight(t){this.outerRadius(t/2)}};return At.Ring=n,n.prototype.className="Ring",n.prototype._centroid=!0,n.prototype._attrsAffectingSize=["innerRadius","outerRadius"],(0,r._registerNode)(n),t.Factory.addGetterSetter(n,"innerRadius",0,(0,i.getNumberValidator)()),t.Factory.addGetterSetter(n,"outerRadius",0,(0,i.getNumberValidator)()),At}(),p=function(){if(Tt)return Ft;Tt=1,Object.defineProperty(Ft,"__esModule",{value:!0}),Ft.Sprite=void 0;const t=m(),e=V(),i=Q(),r=v(),a=o();let n=class extends e.Shape{constructor(t){super(t),this._updated=!0,this.anim=new i.Animation((()=>{var t=this._updated;return this._updated=!1,t})),this.on("animationChange.konva",(function(){this.frameIndex(0)})),this.on("frameIndexChange.konva",(function(){this._updated=!0})),this.on("frameRateChange.konva",(function(){this.anim.isRunning()&&(clearInterval(this.interval),this._setInterval())}))}_sceneFunc(t){var e=this.animation(),i=this.frameIndex(),r=4*i,a=this.animations()[e],n=this.frameOffsets(),s=a[r+0],o=a[r+1],h=a[r+2],l=a[r+3],d=this.image();if((this.hasFill()||this.hasStroke())&&(t.beginPath(),t.rect(0,0,h,l),t.closePath(),t.fillStrokeShape(this)),d)if(n){var c=n[e],g=2*i;t.drawImage(d,s,o,h,l,c[g+0],c[g+1],h,l)}else t.drawImage(d,s,o,h,l,0,0,h,l)}_hitFunc(t){var e=this.animation(),i=this.frameIndex(),r=4*i,a=this.animations()[e],n=this.frameOffsets(),s=a[r+2],o=a[r+3];if(t.beginPath(),n){var h=n[e],l=2*i;t.rect(h[l+0],h[l+1],s,o)}else t.rect(0,0,s,o);t.closePath(),t.fillShape(this)}_useBufferCanvas(){return super._useBufferCanvas(!0)}_setInterval(){var t=this;this.interval=setInterval((function(){t._updateIndex()}),1e3/this.frameRate())}start(){if(!this.isRunning()){var t=this.getLayer();this.anim.setLayers(t),this._setInterval(),this.anim.start()}}stop(){this.anim.stop(),clearInterval(this.interval)}isRunning(){return this.anim.isRunning()}_updateIndex(){var t=this.frameIndex(),e=this.animation();t<this.animations()[e].length/4-1?this.frameIndex(t+1):this.frameIndex(0)}};return Ft.Sprite=n,n.prototype.className="Sprite",(0,a._registerNode)(n),t.Factory.addGetterSetter(n,"animation"),t.Factory.addGetterSetter(n,"animations"),t.Factory.addGetterSetter(n,"frameOffsets"),t.Factory.addGetterSetter(n,"image"),t.Factory.addGetterSetter(n,"frameIndex",0,(0,r.getNumberValidator)()),t.Factory.addGetterSetter(n,"frameRate",17,(0,r.getNumberValidator)()),t.Factory.backCompat(n,{index:"frameIndex",getIndex:"getFrameIndex",setIndex:"setFrameIndex"}),Ft}(),_=function(){if(Mt)return Gt;Mt=1,Object.defineProperty(Gt,"__esModule",{value:!0}),Gt.Star=void 0;const t=m(),e=V(),i=v(),r=o();let a=class extends e.Shape{_sceneFunc(t){var e=this.innerRadius(),i=this.outerRadius(),r=this.numPoints();t.beginPath(),t.moveTo(0,0-i);for(var a=1;a<2*r;a++){var n=a%2==0?i:e,s=n*Math.sin(a*Math.PI/r),o=-1*n*Math.cos(a*Math.PI/r);t.lineTo(s,o)}t.closePath(),t.fillStrokeShape(this)}getWidth(){return 2*this.outerRadius()}getHeight(){return 2*this.outerRadius()}setWidth(t){this.outerRadius(t/2)}setHeight(t){this.outerRadius(t/2)}};return Gt.Star=a,a.prototype.className="Star",a.prototype._centroid=!0,a.prototype._attrsAffectingSize=["innerRadius","outerRadius"],(0,r._registerNode)(a),t.Factory.addGetterSetter(a,"numPoints",5,(0,i.getNumberValidator)()),t.Factory.addGetterSetter(a,"innerRadius",0,(0,i.getNumberValidator)()),t.Factory.addGetterSetter(a,"outerRadius",0,(0,i.getNumberValidator)()),Gt}(),y=Nt(),b=function(){if(Et)return Ot;Et=1,Object.defineProperty(Ot,"__esModule",{value:!0}),Ot.TextPath=void 0;const t=d(),e=m(),i=V(),r=gt(),a=Nt(),n=v(),s=o();var h="normal";function l(t){t.fillText(this.partialText,0,0)}function c(t){t.strokeText(this.partialText,0,0)}let g=class extends i.Shape{constructor(e){super(e),this.dummyCanvas=t.Util.createCanvasElement(),this.dataArray=[],this._readDataAttribute(),this.on("dataChange.konva",(function(){this._readDataAttribute(),this._setTextData()})),this.on("textChange.konva alignChange.konva letterSpacingChange.konva kerningFuncChange.konva fontSizeChange.konva fontFamilyChange.konva",this._setTextData),this._setTextData()}_getTextPathLength(){return r.Path.getPathLength(this.dataArray)}_getPointAtLength(t){return this.attrs.data?t-1>this.pathLength?null:r.Path.getPointAtLengthOfDataArray(t,this.dataArray):null}_readDataAttribute(){this.dataArray=r.Path.parsePathData(this.attrs.data),this.pathLength=this._getTextPathLength()}_sceneFunc(t){t.setAttr("font",this._getContextFont()),t.setAttr("textBaseline",this.textBaseline()),t.setAttr("textAlign","left"),t.save();var e=this.textDecoration(),i=this.fill(),r=this.fontSize(),a=this.glyphInfo;"underline"===e&&t.beginPath();for(var n=0;n<a.length;n++){t.save();var s=a[n].p0;t.translate(s.x,s.y),t.rotate(a[n].rotation),this.partialText=a[n].text,t.fillStrokeShape(this),"underline"===e&&(0===n&&t.moveTo(0,r/2+1),t.lineTo(r,r/2+1)),t.restore()}"underline"===e&&(t.strokeStyle=i,t.lineWidth=r/20,t.stroke()),t.restore()}_hitFunc(t){t.beginPath();var e=this.glyphInfo;if(e.length>=1){var i=e[0].p0;t.moveTo(i.x,i.y)}for(var r=0;r<e.length;r++){var a=e[r].p1;t.lineTo(a.x,a.y)}t.setAttr("lineWidth",this.fontSize()),t.setAttr("strokeStyle",this.colorKey),t.stroke()}getTextWidth(){return this.textWidth}getTextHeight(){return t.Util.warn("text.getTextHeight() method is deprecated. Use text.height() - for full height and text.fontSize() - for one line height."),this.textHeight}setText(t){return a.Text.prototype.setText.call(this,t)}_getContextFont(){return a.Text.prototype._getContextFont.call(this)}_getTextSize(t){var e=this.dummyCanvas.getContext("2d");e.save(),e.font=this._getContextFont();var i=e.measureText(t);return e.restore(),{width:i.width,height:parseInt(`${this.fontSize()}`,10)}}_setTextData(){const{width:t,height:e}=this._getTextSize(this.attrs.text);if(this.textWidth=t,this.textHeight=e,this.glyphInfo=[],!this.attrs.data)return null;const i=this.letterSpacing(),n=this.align(),s=this.kerningFunc(),o=Math.max(this.textWidth+((this.attrs.text||"").length-1)*i,0);let h=0;"center"===n&&(h=Math.max(0,this.pathLength/2-o/2)),"right"===n&&(h=Math.max(0,this.pathLength-o));const l=(0,a.stringToArray)(this.text());let d=h;for(var c=0;c<l.length;c++){const t=this._getPointAtLength(d);if(!t)return;let e=this._getTextSize(l[c]).width+i;if(" "===l[c]&&"justify"===n){const t=this.text().split(" ").length-1;e+=(this.pathLength-o)/t}const a=this._getPointAtLength(d+e);if(!a)return;const h=r.Path.getLineLength(t.x,t.y,a.x,a.y);let u=0;if(s)try{u=s(l[c-1],l[c])*this.fontSize()}catch(g){u=0}t.x+=u,a.x+=u,this.textWidth+=u;const f=r.Path.getPointOnLine(u+h/2,t.x,t.y,a.x,a.y),p=Math.atan2(a.y-t.y,a.x-t.x);this.glyphInfo.push({transposeX:f.x,transposeY:f.y,text:l[c],rotation:p,p0:t,p1:a}),d+=e}}getSelfRect(){if(!this.glyphInfo.length)return{x:0,y:0,width:0,height:0};var t=[];this.glyphInfo.forEach((function(e){t.push(e.p0.x),t.push(e.p0.y),t.push(e.p1.x),t.push(e.p1.y)}));for(var e,i,r=t[0]||0,a=t[0]||0,n=t[1]||0,s=t[1]||0,o=0;o<t.length/2;o++)e=t[2*o],i=t[2*o+1],r=Math.min(r,e),a=Math.max(a,e),n=Math.min(n,i),s=Math.max(s,i);var h=this.fontSize();return{x:r-h/2,y:n-h/2,width:a-r+h,height:s-n+h}}destroy(){return t.Util.releaseCanvas(this.dummyCanvas),super.destroy()}};return Ot.TextPath=g,g.prototype._fillFunc=l,g.prototype._strokeFunc=c,g.prototype._fillFuncHit=l,g.prototype._strokeFuncHit=c,g.prototype.className="TextPath",g.prototype._attrsAffectingSize=["text","fontSize","data"],(0,s._registerNode)(g),e.Factory.addGetterSetter(g,"data"),e.Factory.addGetterSetter(g,"fontFamily","Arial"),e.Factory.addGetterSetter(g,"fontSize",12,(0,n.getNumberValidator)()),e.Factory.addGetterSetter(g,"fontStyle",h),e.Factory.addGetterSetter(g,"align","left"),e.Factory.addGetterSetter(g,"letterSpacing",0,(0,n.getNumberValidator)()),e.Factory.addGetterSetter(g,"textBaseline","middle"),e.Factory.addGetterSetter(g,"fontVariant",h),e.Factory.addGetterSetter(g,"text",""),e.Factory.addGetterSetter(g,"textDecoration",null),e.Factory.addGetterSetter(g,"kerningFunc",null),Ot}(),x=Ut(),S=function(){if(Bt)return Vt;Bt=1,Object.defineProperty(Vt,"__esModule",{value:!0}),Vt.Wedge=void 0;const t=m(),e=V(),i=o(),r=v(),a=o();let n=class extends e.Shape{_sceneFunc(t){t.beginPath(),t.arc(0,0,this.radius(),0,i.Konva.getAngle(this.angle()),this.clockwise()),t.lineTo(0,0),t.closePath(),t.fillStrokeShape(this)}getWidth(){return 2*this.radius()}getHeight(){return 2*this.radius()}setWidth(t){this.radius(t/2)}setHeight(t){this.radius(t/2)}};return Vt.Wedge=n,n.prototype.className="Wedge",n.prototype._centroid=!0,n.prototype._attrsAffectingSize=["radius"],(0,a._registerNode)(n),t.Factory.addGetterSetter(n,"radius",0,(0,r.getNumberValidator)()),t.Factory.addGetterSetter(n,"angle",0,(0,r.getNumberValidator)()),t.Factory.addGetterSetter(n,"clockwise",!1),t.Factory.backCompat(n,{angleDeg:"angle",getAngleDeg:"getAngle",setAngleDeg:"setAngle"}),Vt}(),C=zt(),w=function(){if(Wt)return Kt;Wt=1,Object.defineProperty(Kt,"__esModule",{value:!0}),Kt.Brighten=void 0;const t=m(),e=T(),i=v();return Kt.Brighten=function(t){var e,i=255*this.brightness(),r=t.data,a=r.length;for(e=0;e<a;e+=4)r[e]+=i,r[e+1]+=i,r[e+2]+=i},t.Factory.addGetterSetter(e.Node,"brightness",0,(0,i.getNumberValidator)(),t.Factory.afterSetFilter),Kt}(),P=function(){if(Yt)return Xt;Yt=1,Object.defineProperty(Xt,"__esModule",{value:!0}),Xt.Contrast=void 0;const t=m(),e=T(),i=v();return Xt.Contrast=function(t){var e,i=Math.pow((this.contrast()+100)/100,2),r=t.data,a=r.length,n=150,s=150,o=150;for(e=0;e<a;e+=4)n=r[e],s=r[e+1],o=r[e+2],n/=255,n-=.5,n*=i,n+=.5,s/=255,s-=.5,s*=i,s+=.5,o/=255,o-=.5,o*=i,o+=.5,n=(n*=255)<0?0:n>255?255:n,s=(s*=255)<0?0:s>255?255:s,o=(o*=255)<0?0:o>255?255:o,r[e]=n,r[e+1]=s,r[e+2]=o},t.Factory.addGetterSetter(e.Node,"contrast",0,(0,i.getNumberValidator)(),t.Factory.afterSetFilter),Xt}(),k=function(){if(qt)return Qt;qt=1,Object.defineProperty(Qt,"__esModule",{value:!0}),Qt.Emboss=void 0;const t=m(),e=T(),i=d(),r=v();return Qt.Emboss=function(t){var e=10*this.embossStrength(),r=255*this.embossWhiteLevel(),a=this.embossDirection(),n=this.embossBlend(),s=0,o=0,h=t.data,l=t.width,d=t.height,c=4*l,g=d;switch(a){case"top-left":s=-1,o=-1;break;case"top":s=-1,o=0;break;case"top-right":s=-1,o=1;break;case"right":s=0,o=1;break;case"bottom-right":s=1,o=1;break;case"bottom":s=1,o=0;break;case"bottom-left":s=1,o=-1;break;case"left":s=0,o=-1;break;default:i.Util.error("Unknown emboss direction: "+a)}do{var u=(g-1)*c,f=s;g+f<1&&(f=0),g+f>d&&(f=0);var p=(g-1+f)*l*4,v=l;do{var m=u+4*(v-1),_=o;v+_<1&&(_=0),v+_>l&&(_=0);var y=p+4*(v-1+_),b=h[m]-h[y],x=h[m+1]-h[y+1],S=h[m+2]-h[y+2],C=b,w=C>0?C:-C;if((x>0?x:-x)>w&&(C=x),(S>0?S:-S)>w&&(C=S),C*=e,n){var P=h[m]+C,k=h[m+1]+C,A=h[m+2]+C;h[m]=P>255?255:P<0?0:P,h[m+1]=k>255?255:k<0?0:k,h[m+2]=A>255?255:A<0?0:A}else{var T=r-C;T<0?T=0:T>255&&(T=255),h[m]=h[m+1]=h[m+2]=T}}while(--v)}while(--g)},t.Factory.addGetterSetter(e.Node,"embossStrength",.5,(0,r.getNumberValidator)(),t.Factory.afterSetFilter),t.Factory.addGetterSetter(e.Node,"embossWhiteLevel",.5,(0,r.getNumberValidator)(),t.Factory.afterSetFilter),t.Factory.addGetterSetter(e.Node,"embossDirection","top-left",null,t.Factory.afterSetFilter),t.Factory.addGetterSetter(e.Node,"embossBlend",!1,null,t.Factory.afterSetFilter),Qt}(),A=function(){if(Jt)return $t;Jt=1,Object.defineProperty($t,"__esModule",{value:!0}),$t.Enhance=void 0;const t=m(),e=T(),i=v();function r(t,e,i,r,a){var n=i-e,s=a-r;return 0===n?r+s/2:0===s?r:s*((t-e)/n)+r}return $t.Enhance=function(t){var e,i,a,n,s=t.data,o=s.length,h=s[0],l=h,d=s[1],c=d,g=s[2],u=g,f=this.enhance();if(0!==f){for(n=0;n<o;n+=4)(e=s[n+0])<h?h=e:e>l&&(l=e),(i=s[n+1])<d?d=i:i>c&&(c=i),(a=s[n+2])<g?g=a:a>u&&(u=a);var p,v,m,_,y,b,x,S,C;for(l===h&&(l=255,h=0),c===d&&(c=255,d=0),u===g&&(u=255,g=0),f>0?(v=l+f*(255-l),m=h-f*(h-0),y=c+f*(255-c),b=d-f*(d-0),S=u+f*(255-u),C=g-f*(g-0)):(v=l+f*(l-(p=.5*(l+h))),m=h+f*(h-p),y=c+f*(c-(_=.5*(c+d))),b=d+f*(d-_),S=u+f*(u-(x=.5*(u+g))),C=g+f*(g-x)),n=0;n<o;n+=4)s[n+0]=r(s[n+0],h,l,m,v),s[n+1]=r(s[n+1],d,c,b,y),s[n+2]=r(s[n+2],g,u,C,S)}},t.Factory.addGetterSetter(e.Node,"enhance",0,(0,i.getNumberValidator)(),t.Factory.afterSetFilter),$t}(),F=(Zt||(Zt=1,Object.defineProperty(te,"__esModule",{value:!0}),te.Grayscale=void 0,te.Grayscale=function(t){var e,i,r=t.data,a=r.length;for(e=0;e<a;e+=4)i=.34*r[e]+.5*r[e+1]+.16*r[e+2],r[e]=i,r[e+1]=i,r[e+2]=i}),te),M=function(){if(ee)return ie;ee=1,Object.defineProperty(ie,"__esModule",{value:!0}),ie.HSL=void 0;const t=m(),e=T(),i=v();return t.Factory.addGetterSetter(e.Node,"hue",0,(0,i.getNumberValidator)(),t.Factory.afterSetFilter),t.Factory.addGetterSetter(e.Node,"saturation",0,(0,i.getNumberValidator)(),t.Factory.afterSetFilter),t.Factory.addGetterSetter(e.Node,"luminance",0,(0,i.getNumberValidator)(),t.Factory.afterSetFilter),ie.HSL=function(t){var e,i,r,a,n,s=t.data,o=s.length,h=Math.pow(2,this.saturation()),l=Math.abs(this.hue()+360)%360,d=127*this.luminance(),c=1*h*Math.cos(l*Math.PI/180),g=1*h*Math.sin(l*Math.PI/180),u=.299+.701*c+.167*g,f=.587-.587*c+.33*g,p=.114-.114*c-.497*g,v=.299-.299*c-.328*g,m=.587+.413*c+.035*g,_=.114-.114*c+.293*g,y=.299-.3*c+1.25*g,b=.587-.586*c-1.05*g,x=.114+.886*c-.2*g;for(e=0;e<o;e+=4)i=s[e+0],r=s[e+1],a=s[e+2],n=s[e+3],s[e+0]=u*i+f*r+p*a+d,s[e+1]=v*i+m*r+_*a+d,s[e+2]=y*i+b*r+x*a+d,s[e+3]=n},ie}(),G=function(){if(re)return ae;re=1,Object.defineProperty(ae,"__esModule",{value:!0}),ae.HSV=void 0;const t=m(),e=T(),i=v();return ae.HSV=function(t){var e,i,r,a,n,s=t.data,o=s.length,h=Math.pow(2,this.value()),l=Math.pow(2,this.saturation()),d=Math.abs(this.hue()+360)%360,c=h*l*Math.cos(d*Math.PI/180),g=h*l*Math.sin(d*Math.PI/180),u=.299*h+.701*c+.167*g,f=.587*h-.587*c+.33*g,p=.114*h-.114*c-.497*g,v=.299*h-.299*c-.328*g,m=.587*h+.413*c+.035*g,_=.114*h-.114*c+.293*g,y=.299*h-.3*c+1.25*g,b=.587*h-.586*c-1.05*g,x=.114*h+.886*c-.2*g;for(e=0;e<o;e+=4)i=s[e+0],r=s[e+1],a=s[e+2],n=s[e+3],s[e+0]=u*i+f*r+p*a,s[e+1]=v*i+m*r+_*a,s[e+2]=y*i+b*r+x*a,s[e+3]=n},t.Factory.addGetterSetter(e.Node,"hue",0,(0,i.getNumberValidator)(),t.Factory.afterSetFilter),t.Factory.addGetterSetter(e.Node,"saturation",0,(0,i.getNumberValidator)(),t.Factory.afterSetFilter),t.Factory.addGetterSetter(e.Node,"value",0,(0,i.getNumberValidator)(),t.Factory.afterSetFilter),ae}(),R=(ne||(ne=1,Object.defineProperty(se,"__esModule",{value:!0}),se.Invert=void 0,se.Invert=function(t){var e,i=t.data,r=i.length;for(e=0;e<r;e+=4)i[e]=255-i[e],i[e+1]=255-i[e+1],i[e+2]=255-i[e+2]}),se),D=le(),N=ge(),E=function(){if(ue)return fe;ue=1,Object.defineProperty(fe,"__esModule",{value:!0}),fe.Noise=void 0;const t=m(),e=T(),i=v();return fe.Noise=function(t){var e,i=255*this.noise(),r=t.data,a=r.length,n=i/2;for(e=0;e<a;e+=4)r[e+0]+=n-2*n*Math.random(),r[e+1]+=n-2*n*Math.random(),r[e+2]+=n-2*n*Math.random()},t.Factory.addGetterSetter(e.Node,"noise",.2,(0,i.getNumberValidator)(),t.Factory.afterSetFilter),fe}(),O=function(){if(pe)return ve;pe=1,Object.defineProperty(ve,"__esModule",{value:!0}),ve.Pixelate=void 0;const t=m(),e=d(),i=T(),r=v();return ve.Pixelate=function(t){var i,r,a,n,s,o,h,l,d,c,g,u,f,p,v=Math.ceil(this.pixelSize()),m=t.width,_=t.height,y=Math.ceil(m/v),b=Math.ceil(_/v),x=t.data;if(v<=0)e.Util.error("pixelSize value can not be <= 0");else for(u=0;u<y;u+=1)for(f=0;f<b;f+=1){for(n=0,s=0,o=0,h=0,d=(l=u*v)+v,g=(c=f*v)+v,p=0,i=l;i<d;i+=1)if(!(i>=m))for(r=c;r<g;r+=1)r>=_||(n+=x[0+(a=4*(m*r+i))],s+=x[a+1],o+=x[a+2],h+=x[a+3],p+=1);for(n/=p,s/=p,o/=p,h/=p,i=l;i<d;i+=1)if(!(i>=m))for(r=c;r<g;r+=1)r>=_||(x[0+(a=4*(m*r+i))]=n,x[a+1]=s,x[a+2]=o,x[a+3]=h)}},t.Factory.addGetterSetter(i.Node,"pixelSize",8,(0,r.getNumberValidator)(),t.Factory.afterSetFilter),ve}(),L=function(){if(me)return _e;me=1,Object.defineProperty(_e,"__esModule",{value:!0}),_e.Posterize=void 0;const t=m(),e=T(),i=v();return _e.Posterize=function(t){var e,i=Math.round(254*this.levels())+1,r=t.data,a=r.length,n=255/i;for(e=0;e<a;e+=1)r[e]=Math.floor(r[e]/n)*n},t.Factory.addGetterSetter(e.Node,"levels",.5,(0,i.getNumberValidator)(),t.Factory.afterSetFilter),_e}(),I=function(){if(ye)return be;ye=1,Object.defineProperty(be,"__esModule",{value:!0}),be.RGB=void 0;const t=m(),e=T(),i=v();return be.RGB=function(t){var e,i,r=t.data,a=r.length,n=this.red(),s=this.green(),o=this.blue();for(e=0;e<a;e+=4)i=(.34*r[e]+.5*r[e+1]+.16*r[e+2])/255,r[e]=i*n,r[e+1]=i*s,r[e+2]=i*o,r[e+3]=r[e+3]},t.Factory.addGetterSetter(e.Node,"red",0,(function(t){return this._filterUpToDate=!1,t>255?255:t<0?0:Math.round(t)})),t.Factory.addGetterSetter(e.Node,"green",0,(function(t){return this._filterUpToDate=!1,t>255?255:t<0?0:Math.round(t)})),t.Factory.addGetterSetter(e.Node,"blue",0,i.RGBComponent,t.Factory.afterSetFilter),be}(),U=function(){if(xe)return Se;xe=1,Object.defineProperty(Se,"__esModule",{value:!0}),Se.RGBA=void 0;const t=m(),e=T(),i=v();return Se.RGBA=function(t){var e,i,r=t.data,a=r.length,n=this.red(),s=this.green(),o=this.blue(),h=this.alpha();for(e=0;e<a;e+=4)i=1-h,r[e]=n*h+r[e]*i,r[e+1]=s*h+r[e+1]*i,r[e+2]=o*h+r[e+2]*i},t.Factory.addGetterSetter(e.Node,"red",0,(function(t){return this._filterUpToDate=!1,t>255?255:t<0?0:Math.round(t)})),t.Factory.addGetterSetter(e.Node,"green",0,(function(t){return this._filterUpToDate=!1,t>255?255:t<0?0:Math.round(t)})),t.Factory.addGetterSetter(e.Node,"blue",0,i.RGBComponent,t.Factory.afterSetFilter),t.Factory.addGetterSetter(e.Node,"alpha",1,(function(t){return this._filterUpToDate=!1,t>1?1:t<0?0:t})),Se}(),B=(Ce||(Ce=1,Object.defineProperty(we,"__esModule",{value:!0}),we.Sepia=void 0,we.Sepia=function(t){var e,i,r,a,n=t.data,s=n.length;for(e=0;e<s;e+=4)i=n[e+0],r=n[e+1],a=n[e+2],n[e+0]=Math.min(255,.393*i+.769*r+.189*a),n[e+1]=Math.min(255,.349*i+.686*r+.168*a),n[e+2]=Math.min(255,.272*i+.534*r+.131*a)}),we),H=(Pe||(Pe=1,Object.defineProperty(ke,"__esModule",{value:!0}),ke.Solarize=void 0,ke.Solarize=function(t){var e=t.data,i=t.width,r=4*i,a=t.height;do{var n=(a-1)*r,s=i;do{var o=n+4*(s-1),h=e[o],l=e[o+1],d=e[o+2];h>127&&(h=255-h),l>127&&(l=255-l),d>127&&(d=255-d),e[o]=h,e[o+1]=l,e[o+2]=d}while(--s)}while(--a)}),ke),j=function(){if(Ae)return Fe;Ae=1,Object.defineProperty(Fe,"__esModule",{value:!0}),Fe.Threshold=void 0;const t=m(),e=T(),i=v();return Fe.Threshold=function(t){var e,i=255*this.threshold(),r=t.data,a=r.length;for(e=0;e<a;e+=1)r[e]=r[e]<i?0:255},t.Factory.addGetterSetter(e.Node,"threshold",.5,(0,i.getNumberValidator)(),t.Factory.afterSetFilter),Fe}();return a.Konva=t.Konva.Util._assign(t.Konva,{Arc:e.Arc,Arrow:i.Arrow,Circle:r.Circle,Ellipse:n.Ellipse,Image:s.Image,Label:h.Label,Tag:h.Tag,Line:l.Line,Path:c.Path,Rect:g.Rect,RegularPolygon:u.RegularPolygon,Ring:f.Ring,Sprite:p.Sprite,Star:_.Star,Text:y.Text,TextPath:b.TextPath,Transformer:x.Transformer,Wedge:S.Wedge,Filters:{Blur:C.Blur,Brighten:w.Brighten,Contrast:P.Contrast,Emboss:k.Emboss,Enhance:A.Enhance,Grayscale:F.Grayscale,HSL:M.HSL,HSV:G.HSV,Invert:R.Invert,Kaleidoscope:D.Kaleidoscope,Mask:N.Mask,Noise:E.Noise,Pixelate:O.Pixelate,Posterize:L.Posterize,RGB:I.RGB,RGBA:U.RGBA,Sepia:B.Sepia,Solarize:H.Solarize,Threshold:j.Threshold}}),a}var Ge,Re=r.exports;function De(){if(Ge)return r.exports;Ge=1,Object.defineProperty(Re,"__esModule",{value:!0});const t=Me();return r.exports=t.Konva,r.exports}const Ne=e(De());export{Ne as K,De as r};
