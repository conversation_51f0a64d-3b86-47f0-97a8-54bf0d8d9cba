var n={exports:{}};(n.exports={}).getOption=function(n,t,r){var o=n[t];if(null==o&&void 0!==r)return r;return o};var t=n.exports,r=function(n){var r=(n=n||{}).reporter,e=t.getOption(n,"async",!0),i=t.getOption(n,"auto",!0);i&&!e&&(r&&r.warn("Invalid options combination. auto=true and async=false is invalid. Setting async=true."),e=!0);var a,u=o(),s=!1;function c(){for(s=!0;u.size();){var n=u;u=o(),n.process()}s=!1}function f(){var n;n=c,a=setTimeout(n,0)}return{add:function(n,t){!s&&i&&e&&0===u.size()&&f(),u.add(n,t)},force:function(n){s||(void 0===n&&(n=e),a&&(clearTimeout(a),a=null),n?f():c())}}};function o(){var n={},t=0,r=0,o=0;return{add:function(e,i){i||(i=e,e=0),e>r?r=e:e<o&&(o=e),n[e]||(n[e]=[]),n[e].push(i),t++},process:function(){for(var t=o;t<=r;t++)for(var e=n[t],i=0;i<e.length;i++){(0,e[i])()}},size:function(){return t}}}export{r as b};
