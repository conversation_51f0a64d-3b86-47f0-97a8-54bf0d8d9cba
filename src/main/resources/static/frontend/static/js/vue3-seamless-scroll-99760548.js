import"./vue-5bfa3a54.js";import{d as e,h as l,e as a,w as t,n as u,a4 as n,m as i,x as o,s,F as v}from"./@vue-5e5cdef9.js";const r={modelValue:{type:Boolean,default:!0},list:{type:Array,required:!0,default:[]},step:{type:Number,default:1},limitScrollNum:{type:Number,default:3},hover:{type:Boolean,default:!1},direction:{type:String,default:"up"},singleHeight:{type:Number,default:0},singleWidth:{type:Number,default:0},singleWaitTime:{type:Number,default:1e3},isRemUnit:{type:Boolean,default:!1},isWatch:{type:Boolean,default:!0},delay:{type:Number,default:0},ease:{type:[String,Object],default:"ease-in"},count:{type:Number,default:-1},copyNum:{type:Number,default:1},wheel:{type:Boolean,default:!1},singleLine:{type:Boolean,default:!1}};globalThis.window.cancelAnimationFrame=globalThis.window.cancelAnimationFrame||globalThis.window.webkitCancelAnimationFrame||globalThis.window.mozCancelAnimationFrame||globalThis.window.oCancelAnimationFrame||globalThis.window.msCancelAnimationFrame||function(e){return globalThis.window.clearTimeout(e)},globalThis.window.requestAnimationFrame=globalThis.window.requestAnimationFrame||globalThis.window.webkitRequestAnimationFrame||globalThis.window.mozRequestAnimationFrame||globalThis.window.oRequestAnimationFrame||globalThis.window.msRequestAnimationFrame||function(e){return globalThis.window.setTimeout(e,1e3/60)};const m=e({name:"vue3-seamless-scroll",inheritAttrs:!1,props:r,emits:["stop","count","move"],setup(e,{slots:r,emit:m,attrs:d}){const c=e,f=l(null),p=l(null),h=l(null),w=l(null),g=l(null),y=l(0),b=l(0),T=l(0),A=l(0),F=l(!1),N=l(0),x=a((()=>!!c.list&&c.list.length>=c.limitScrollNum)),q=a((()=>({width:y.value?`${y.value}px`:"auto",transform:`translate(${T.value}px,${A.value}px)`,transition:`all ${"string"==typeof c.ease?c.ease:"cubic-bezier("+c.ease.x1+","+c.ease.y1+","+c.ease.x2+","+c.ease.y2+")"} ${c.delay}ms`,overflow:"hidden",display:c.singleLine?"flex":"block"}))),M=a((()=>"left"==c.direction||"right"==c.direction)),W=a((()=>M.value?{float:"left",overflow:"hidden",display:c.singleLine?"flex":"block",flexShrink:c.singleLine?0:1}:{overflow:"hidden"})),R=a((()=>c.isRemUnit?parseInt(globalThis.window.getComputedStyle(globalThis.document.documentElement,null).fontSize):1)),S=a((()=>c.singleWidth*R.value)),B=a((()=>c.singleHeight*R.value)),k=a((()=>{let e,l=c.step;return e=M.value?S.value:B.value,l})),C=()=>{cancelAnimationFrame(w.value),w.value=null},$=(e,l,a)=>{w.value=requestAnimationFrame((function(){const t=b.value/2,u=y.value/2;if("up"===e?(Math.abs(A.value)>=t&&(A.value=0,N.value+=1,m("count",N.value)),A.value-=l):"down"===e?(A.value>=0&&(A.value=-1*t,N.value+=1,m("count",N.value)),A.value+=l):"left"===e?(Math.abs(T.value)>=u&&(T.value=0,N.value+=1,m("count",N.value)),T.value-=l):"right"===e&&(T.value>=0&&(T.value=-1*u,N.value+=1,m("count",N.value)),T.value+=l),a)return;let{singleWaitTime:n}=c;g.value&&clearTimeout(g.value),B.value?Math.abs(A.value)%B.value<l?g.value=setTimeout((()=>{j()}),n):j():S.value&&Math.abs(T.value)%S.value<l?g.value=setTimeout((()=>{j()}),n):j()}))},j=()=>{if(C(),F.value||!x.value||N.value===c.count)return m("stop",N.value),void(N.value=0);$(c.direction,k.value,!1)},z=()=>{var e;if((e=c.list)&&"boolean"!=typeof e&&e.length,M.value){let e=p.value.offsetWidth;e=2*e+1,y.value=e}x.value?(b.value=h.value.offsetHeight,c.modelValue&&j()):(C(),A.value=T.value=0)},L=()=>{F.value=!1,j()},V=()=>{F.value=!0,g.value&&clearTimeout(g.value),C()},H=a((()=>c.hover&&c.modelValue&&x.value)),D=function(e,l,a,t){var u,n=!1,i=0;function o(){u&&clearTimeout(u)}function s(){for(var s=arguments.length,v=new Array(s),r=0;r<s;r++)v[r]=arguments[r];var m=this,d=Date.now()-i;function c(){i=Date.now(),a.apply(m,v)}n||(t&&!u&&c(),o(),void 0===t&&d>e?c():!0!==l&&(u=setTimeout(t?function(){u=void 0}:c,void 0===t?e-d:e)))}return"boolean"!=typeof l&&(t=a,a=l,l=void 0),s.cancel=function(){o(),n=!0},s}(30,(e=>{C();const l=B.value?B.value:15;e.deltaY<0&&$("down",l,!0),e.deltaY>0&&$("up",l,!0)})),O=()=>{C(),F.value=!1,z()};!function(e){const l=s();l&&Object.assign(l.proxy,e)}({Reset:()=>{O()}}),t((()=>c.list),(()=>{c.isWatch&&u((()=>{O()}))}),{deep:!0}),t((()=>c.modelValue),(e=>{e?L():V()})),t((()=>c.count),(e=>{0!==e&&L()})),n((()=>{C(),clearTimeout(g.value)})),i((()=>{x.value&&z()}));const{default:U,html:Y}=r,E=new Array(c.copyNum).fill(null),I=()=>o(v,null,[o("div",{ref:p,style:W.value},[U&&U()]),x.value?E.map((()=>o("div",{style:W.value},Y&&"function"==typeof Y?[Y()]:[U&&U()]))):null]);return()=>o("div",{ref:f,class:d.class},[c.wheel&&c.hover?o("div",{ref:h,style:q.value,onMouseenter:()=>{H.value&&V()},onMouseleave:()=>{H.value&&L()},onWheel:e=>{H.value&&(e=>{D(e)})(e)}},[I()]):o("div",{ref:h,style:q.value,onMouseenter:()=>{H.value&&V()},onMouseleave:()=>{H.value&&L()}},[I()])])}}),d=function(e,l={}){e.component(l.name||m.name,m)};function c(e){e.use(d)}export{c as i};
