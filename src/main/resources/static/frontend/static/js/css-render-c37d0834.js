import{m as n}from"./@emotion-b17c1a96.js";const t=/\s*,(?![^(]*\))\s*/g,e=/\s+/g;function r(n){let r=[""];return n.forEach((n=>{(n=n&&n.trim())&&(r=n.includes("&")?function(n,e){const r=[];return e.split(t).forEach((t=>{let e=function(n){let t=0;for(let e=0;e<n.length;++e)"&"===n[e]&&++t;return t}(t);if(!e)return void n.forEach((n=>{r.push((n&&n+" ")+t)}));if(1===e)return void n.forEach((n=>{r.push(t.replace("&",n))}));let o=[t];for(;e--;){const t=[];o.forEach((e=>{n.forEach((n=>{t.push(e.replace("&",n))}))})),o=t}o.forEach((n=>r.push(n)))})),r}(r,n):function(n,e){const r=[];return e.split(t).forEach((t=>{n.forEach((n=>{r.push((n&&n+" ")+t)}))})),r}(r,n))})),r.join(", ").replace(e," ")}function o(n){if(!n)return;const t=n.parentElement;t&&t.removeChild(n)}function s(n){return document.querySelector(`style[cssr-id="${n}"]`)}function c(n){return!!n&&/^\s*@(s|m)/.test(n)}const i=/[A-Z]/g;function u(n){return n.replace(i,(n=>"-"+n.toLowerCase()))}function f(n,t,e,r){if(!t)return"";const o=function(n,t,e){return"function"==typeof n?n({context:t.context,props:e}):n}(t,e,r);if(!o)return"";if("string"==typeof o)return`${n} {\n${o}\n}`;const s=Object.keys(o);if(0===s.length)return e.config.keepEmptyBlock?n+" {\n}":"";const c=n?[n+" {"]:[];return s.forEach((n=>{const t=o[n];"raw"!==n?(n=u(n),null!=t&&c.push(`  ${n}${function(n,t="  "){return"object"==typeof n&&null!==n?" {\n"+Object.entries(n).map((n=>t+`  ${u(n[0])}: ${n[1]};`)).join("\n")+"\n"+t+"}":`: ${n};`}(t)}`)):c.push("\n"+t+"\n")})),n&&c.push("}"),c.join("\n")}function l(n,t,e){n&&n.forEach((n=>{if(Array.isArray(n))l(n,t,e);else if("function"==typeof n){const r=n(t);Array.isArray(r)?l(r,t,e):r&&e(r)}else n&&e(n)}))}function a(n,t,e,o,s,i){const u=n.$;let p="";if(u&&"string"!=typeof u)if("function"==typeof u){const n=u({context:o.context,props:s});c(n)?p=n:t.push(n)}else if(u.before&&u.before(o.context),u.$&&"string"!=typeof u.$){if(u.$){const n=u.$({context:o.context,props:s});c(n)?p=n:t.push(n)}}else c(u.$)?p=u.$:t.push(u.$);else c(u)?p=u:t.push(u);const h=r(t),d=f(h,n.props,o,s);p?(e.push(`${p} {`),i&&d&&i.insertRule(`${p} {\n${d}\n}\n`)):(i&&d&&i.insertRule(d),!i&&d.length&&e.push(d)),n.children&&l(n.children,{context:o.context,props:s},(n=>{if("string"==typeof n){const t=f(h,{raw:n},o,s);i?i.insertRule(t):e.push(t)}else a(n,t,e,o,s,i)})),t.pop(),p&&e.push("}"),u&&u.after&&u.after(o.context)}function p(n,t,e,r=!1){const o=[];return a(n,[],o,t,e,r?n.instance.__styleSheet:void 0),r?"":o.join("\n\n")}function h(n,t){n.push(t)}function d(t,e,r,o,c,i,u,f,l){if(i&&!l){if(void 0===r)return;const n=window.__cssrContext;return void(n[r]||(n[r]=!0,p(e,t,o,i)))}let a;if(void 0===r&&(a=e.render(o),r=n(a)),l)return void l.adapter(r,null!=a?a:e.render(o));const d=s(r);if(null!==d&&!u)return d;const y=null!=d?d:function(n){const t=document.createElement("style");return t.setAttribute("cssr-id",n),t}(r);if(void 0===a&&(a=e.render(o)),y.textContent=a,null!==d)return d;if(f){const n=document.head.querySelector(`meta[name="${f}"]`);if(n)return document.head.insertBefore(y,n),h(e.els,y),y}return c?document.head.insertBefore(y,document.head.querySelector("style, link")):document.head.appendChild(y),h(e.els,y),y}function y(n){return p(this,this.instance,n)}function m(n={}){const{id:t,ssr:e,props:r,head:o=!1,silent:s=!1,force:c=!1,anchorMetaName:i}=n;return d(this.instance,this,t,r,o,s,c,i,e)}function $(n={}){const{id:t}=n;!function(n,t,e){const{els:r}=t;if(void 0===e)r.forEach(o),t.els=[];else{const n=s(e);n&&r.includes(n)&&(o(n),t.els=r.filter((t=>t!==n)))}}(this.instance,this,t)}"undefined"!=typeof window&&(window.__cssrContext={});const x=function(n,t,e,r){return{instance:n,$:t,props:e,children:r,els:[],render:y,mount:m,unmount:$}};function E(n={}){let t=null;const e={c:(...n)=>function(n,t,e,r){return Array.isArray(t)?x(n,{$:null},null,t):Array.isArray(e)?x(n,t,null,e):Array.isArray(r)?x(n,t,e,r):x(n,t,e,null)}(e,...n),use:(n,...t)=>n.install(e,...t),find:s,context:{},config:n,get __styleSheet(){if(!t){const n=document.createElement("style");return document.head.appendChild(n),t=document.styleSheets[document.styleSheets.length-1],t}return t}};return e}function g(n,t){if(void 0===n)return!1;if(t){const{context:{ids:e}}=t;return e.has(n)}return null!==s(n)}export{E as C,g as e};
