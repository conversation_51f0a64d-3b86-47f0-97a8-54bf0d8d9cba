import{W as t}from"./quasar-df1bac18.js";import"./vue-5bfa3a54.js";import{x as s}from"./paramsStore-0ce8c7b5.js";import{p as e}from"./@vueuse-5227c686.js";import{d as r}from"./deviceMonitorApi-849244fa.js";import{d as i}from"./dayjs-67f8ddef.js";import{g as o}from"./api-360ec627.js";import{j as a,m,o as p,c as l,x as j,a8 as n,a as c,F as d,k as u,t as v,b as x}from"./@vue-5e5cdef9.js";import"./@babel-f3c0a00c.js";import"./menuStore-30bf76d3.js";import"./vue-router-6159329f.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./lodash-6d99edc3.js";import"./icons-95011f8c.js";import"./naive-ui-0ee0b8c3.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./lodash-es-ea7deab5.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./date-fns-tz-0cc073c8.js";import"./async-validator-cf877c1f.js";import"./@vicons-f32a0bdb.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./index-a5df0f75.js";import"./element-plus-95e0b914.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./xe-utils-fe99d42a.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-be30dbb6.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./notification-950a5f80.js";const w={class:"tw-h-full tw-w-full tw-pt-2"},f=c("thead",null,[c("tr",null,[c("th",{class:"tw-text-center"},"逆变器SN"),c("th",{class:"tw-text-center"},"模块软件版本号"),c("th",{class:"tw-text-center"},"显示版本号"),c("th",{class:"tw-text-center"},"控制版本号"),c("th",{class:"tw-text-center"},"厂家"),c("th",{class:"tw-text-center"},"型号")])],-1),b={class:"tw-text-center"},h={class:"tw-text-center"},g={class:"tw-text-center"},k={class:"tw-text-center"},y={class:"tw-text-center"},z={class:"tw-text-center"},U={__name:"detail",setup(U){const L={plantUid:e("plantUid").value||s().cur.plantUid};let M=a({detailList:[]});return m((async()=>{const t=await r(L.plantUid,i().format("YYYY-MM-DD")),s=o(t);M.detailList=s.data.slice(0,s.data.length-1).map((t=>t.device))})),(s,e)=>{const r=t;return p(),l("div",w,[j(r,{separator:"cell",flat:"",bordered:"",class:"tw-rounded-sm"},{default:n((()=>[f,c("tbody",null,[(p(!0),l(d,null,u(x(M).detailList,(t=>(p(),l("tr",{key:t.deviceId},[c("td",b,v(t.deviceId),1),c("td",h,v(t.softwareVersion),1),c("td",g,v(t.displayVersion),1),c("td",k,v(t.controlVersion),1),c("td",y,v(t.manufacturer),1),c("td",z,v(t.module),1)])))),128))])])),_:1})])}}};export{U as default};
