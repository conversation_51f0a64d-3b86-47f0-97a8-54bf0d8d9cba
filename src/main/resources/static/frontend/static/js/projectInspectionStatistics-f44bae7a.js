import{a as t}from"./api-360ec627.js";const a=(a,e)=>t("/statistics/integrativeStatistics/projectInspectionStatistics",{startDate:a,endDate:e},{},"post"),e=(a,e)=>t("/statistics/export/exportProjectInspectionInfo",{startDate:a,endDate:e},{},"post","blob"),s=(a,e="alarm",s="",r="",o="",i="",n="",p="",l="")=>t("/alarm/export/getInverterAlarmInfo",{},{columnsList:a,startTime:s,endTime:r,alarmType:e,alarmMean:i,plantName:o,alarmLevel:n,alarmStatus:p,plantUid:l},"post","blob");export{s as X,a,e as b};
