import"./vue-5bfa3a54.js";import{h as l}from"./@vue-5e5cdef9.js";const a=l([{label:"光精灵离线",value:"0"},{label:"光精灵在线",value:"1"},{label:"正常",value:"2"},{label:"告警",value:"3"},{label:"逆变器夜间离线",value:"4"},{label:"自检提示",value:"5"}]),e={0:[0],1:[1,2,3,4],2:[1,3],3:[2],4:[4],5:[3],"":""},u=l([{label:"正常",value:1},{label:"配电箱开关故障",value:2},{label:"电表箱开关故障",value:3},{label:"市电停电",value:4},{label:"电表箱开关采样异常",value:5},{label:"市电采样异常",value:6},{label:"市电、电表箱开关采样异常",value:7},{label:"过流开关故障",value:8},{label:"失压开关故障",value:9},{label:"失压开关采样异常",value:10},{label:"市电、失压开关采样异常",value:11}]);export{u as a,e as b,a as p};
