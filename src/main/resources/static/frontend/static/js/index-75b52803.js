import{_ as e}from"./date-33a67ff0.js";import{V as t}from"./quasar-b3f06d8a.js";import"./vue-5bfa3a54.js";import{x as l}from"./menuStore-26f8ddd8.js";import"./echarts-f30da64f.js";import{V as s}from"./zrender-c058db04.js";import{c as i,d as a,e as o,b as n}from"./deviceMonitorApi-d99c20c5.js";import{x as r}from"./paramsStore-8a185cc9.js";import{u as c}from"./vue-router-6159329f.js";import{F as d}from"./@vueuse-af86c621.js";import{l as m}from"./lodash-6d99edc3.js";import{g as u}from"./api-b858041e.js";import{a as f}from"./plantManageApi-ea9fcaaf.js";import{e as p}from"./echartsInit-0067e609.js";import{d as v}from"./dayjs-d60cc07f.js";import{_ as w}from"./index-8cc8d4b8.js";import{E as x}from"./@vicons-f32a0bdb.js";import{j as b,e as y,m as j,o as h,c as g,a as I,t as L,b as S,x as k,F as D,k as _,f as A,l as T,y as z,C as N,D as U}from"./@vue-5e5cdef9.js";import{e as E}from"./naive-ui-0ee0b8c3.js";import"./notification-950a5f80.js";import"./@babel-f3c0a00c.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./icons-95011f8c.js";import"./tslib-a4e99503.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./element-plus-d975be09.js";import"./lodash-es-ea7deab5.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./xe-utils-fe99d42a.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-7bbbb435.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./date-fns-tz-0cc073c8.js";const F={title:{left:"50%",textAlign:"center"},grid:{left:"0%",right:"10%",bottom:"13%",top:"3%",containLabel:!0},dataZoom:[{show:!0,type:"slider",y:"93%"}],tooltip:{trigger:"axis",confine:!0,enterable:!0,formatter:function(e,t,l){for(var s=e[0].axisValue+"<br />",i=0,a=e.length;i<a;i++)s+='<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background:'+e[i].color+'"></span>',s+='<span style="text-align:right">'+e[i].seriesName+"："+e[i].value+"   "+W(e[i])+"</span><br />";return s}},legend:{right:0,orient:"vertical",data:[]},xAxis:{type:"category",data:[],boundaryGap:!0,splitLine:{show:!0,lineStyle:{color:["#D4DFF5"]}},axisTick:{show:!1},axisLine:{lineStyle:{color:"#609ee9"}},axisLabel:{fontSize:14}},yAxis:[{type:"value",splitLine:{lineStyle:{color:["#D4DFF5"]}},axisTick:{show:!0,inside:!0},axisLine:{show:!0,lineStyle:{color:"#609ee9"}},position:"left",axisLabel:{fontSize:14}}],series:[{name:"今日",type:"line",smooth:!0,barMaxWidth:"30px",showSymbol:!1,symbol:"circle",symbolSize:6,data:["1200","1400","1008","1411","1026","1288","1300","800","1100","1000","1118","1322"],itemStyle:{normal:{}},lineStyle:{normal:{width:3}}},{name:"昨日",type:"line",smooth:!0,showSymbol:!1,symbol:"circle",symbolSize:6,data:["1200","1400","808","811","626","488","1600","1100","500","300","1998","822"],areaStyle:{normal:{color:new s(0,0,0,1,[{offset:0,color:"rgba(216, 244, 247,1)"},{offset:1,color:"rgba(216, 244, 247,1)"}],!1)}},itemStyle:{normal:{color:"#58c8da"}},lineStyle:{normal:{width:3}}}]};function W(e){return e.seriesName.includes("电压")?"V":e.seriesName.includes("电流")?"A":e.seriesName.includes("电量")?"KWh":e.seriesName.includes("温度")?"℃":e.seriesName.includes("频率")?"Hz":e.seriesName.includes("功率")?"W":""}const C=e=>(N("data-v-23827e32"),e=e(),U(),e),O={class:"index-box u-flex-column u-gap-10 u-wh-full box-border"},M={class:"header-info u-flex-center-no u-gap-10"},q={class:"left-info h-full tw-grid tw-bg-white tw-p-2"},V={class:"tw-flex tw-flex-col tw-justify-center"},P={class:"body-text text-center"},Y=C((()=>I("article",{class:"small-title text-center"},"工作效率",-1))),$={class:"tw-flex tw-flex-col tw-justify-center"},B={class:"body-text text-center"},Z=C((()=>I("article",{class:"small-title text-center"},"电站容量",-1))),G={class:"tw-flex tw-flex-col tw-justify-center"},H={class:"body-text text-center"},K=C((()=>I("article",{class:"small-title text-center"},"等效植树",-1))),Q={class:"tw-flex tw-flex-col tw-justify-center"},R={class:"body-text text-center"},J=C((()=>I("article",{class:"small-title text-center"},"累计减排CO2",-1))),X={class:"right-info h-full tw-flex-1 tw-grid tw-grid-cols-4 tw-gap-5"},ee={class:"u-flex-column tw-bg-white tw-text-[#24c0b0]"},te=C((()=>I("div",{class:"small-title tw-text-[#24c0b0]"},"功率(W)",-1))),le={class:"tw-rounded-full tw-h-[120px] tw-w-[120px] tw-border-solid tw-border-[9px] tw-border-[#24c0b0]-500 tw-m-auto tw-flex tw-flex-col tw-items-center tw-justify-center"},se={class:"body-text"},ie=C((()=>I("div",{class:"body-small-text tw-text-[#24c0b0]"},"当前",-1))),ae={class:"tw-inline-flex tw-w-full tw-items-center tw-justify-center"},oe=C((()=>I("div",{class:"small-title"},"峰值",-1))),ne={class:"body-text"},re={class:"u-flex-column tw-bg-white tw-text-yellow-500"},ce=C((()=>I("div",{class:"small-title tw-text-yellow-500"},"发电(kWh)",-1))),de={class:"tw-rounded-full tw-h-[120px] tw-w-[120px] tw-border-solid tw-border-[9px] tw-border-yellow-500 tw-m-auto tw-flex tw-flex-col tw-items-center tw-justify-center"},me={class:"body-text"},ue={class:"body-small-text tw-text-yellow-500"},fe={class:"tw-inline-flex tw-w-full tw-items-center tw-justify-center"},pe=C((()=>I("div",{class:"small-title"},"累计",-1))),ve={class:"body-text"},we={class:"u-flex-column tw-bg-white tw-text-blue-500"},xe=C((()=>I("div",{class:"small-title tw-text-blue-500"},"收益(¥)",-1))),be={class:"tw-rounded-full tw-h-[120px] tw-w-[120px] tw-border-solid tw-border-[9px] tw-border-blue-500 tw-m-auto tw-flex tw-flex-col tw-items-center tw-justify-center"},ye={class:"body-text"},je={class:"body-small-text tw-text-blue-500"},he={class:"tw-inline-flex tw-w-full tw-items-center tw-justify-center"},ge=C((()=>I("div",{class:"small-title"},"累计",-1))),Ie={class:"body-text"},Le={class:"u-flex-column tw-bg-white tw-text-green-500"},Se=C((()=>I("div",{class:"small-title tw-text-green-500"},"逆变器",-1))),ke={class:"tw-rounded-full tw-h-[120px] tw-w-[120px] tw-border-solid tw-border-[9px] tw-border-green-500 tw-m-auto tw-flex tw-flex-col tw-items-center tw-justify-center"},De={class:"body-text"},_e={class:"body-small-text tw-text-green-500"},Ae={class:"tw-inline-flex tw-w-full tw-px-20 u-flex-center-no"},Te=C((()=>I("div",{class:"small-title"},"在线逆变器",-1))),ze={class:"body-text"},Ne={class:"content u-flex-1 u-flex-center-no u-gap-10"},Ue={class:"content-left h-full tw-bg-white u-flex-column u-gap-5"},Ee={class:"u-flex-1 w-full u-flex-column"},Fe={class:"u-flex-1 flex item-center justify-between"},We=C((()=>I("div",{class:"small-title"},"创建时间",-1))),Ce={class:"body-text"},Oe={class:"u-flex-1 flex item-center justify-between"},Me=C((()=>I("div",{class:"small-title"},"质保时间",-1))),qe={class:"body-text"},Ve={class:"u-flex-1 flex item-center justify-between flex-nowrap"},Pe=C((()=>I("div",{class:"small-title"},"电站地址",-1))),Ye={class:"body-text"},$e={class:"u-flex-1 flex item-center justify-between"},Be={class:"small-title"},Ze={class:"body-text"},Ge={class:"u-flex-1 flex item-center justify-between"},He=C((()=>I("div",{class:"small-title"},"厂家",-1))),Ke={class:"body-text"},Qe={class:"u-flex-1 flex item-center justify-between"},Re=C((()=>I("div",{class:"small-title"},"型号",-1))),Je={class:"body-text"},Xe={class:"u-flex-1 flex item-center justify-between"},et=C((()=>I("div",{class:"small-title"},"所有人",-1))),tt={class:"body-text"},lt={class:"u-flex-1 flex item-center justify-between"},st=C((()=>I("div",{class:"small-title"},"电话",-1))),it={class:"body-text"},at={class:"u-flex-1 flex item-center justify-between"},ot=C((()=>I("div",{class:"small-title"},"电站ID",-1))),nt={class:"body-text"},rt={class:"u-flex-1 flex item-center justify-between"},ct=C((()=>I("div",{class:"small-title"},"电站朝向",-1))),dt={class:"body-text"},mt={class:"content-right h-full tw-flex-1 tw-bg-white tw-flex tw-flex-col"},ut={class:"chart-header tw-bg-blue-800 u-flex-center-no justify-left u-gap-10"},ft={class:"tw-flex-1 tw-m-0 tw-p-0",ref:"chartDom"},pt={class:"tw-h-[50px] tw-w-full tw-text-base tw-flex tw-items-center"},vt={class:"tw-w-1/3 tw-text-center u-flex-center"},wt=C((()=>I("span",{class:"body-text"}," A点 电网电压 ",-1))),xt={class:"tw-w-1/3 tw-text-center u-flex-center"},bt=C((()=>I("span",{class:"body-text"}," B点 失压开关",-1))),yt={class:"tw-w-1/3 tw-text-center u-flex-center"},jt=C((()=>I("span",{class:"body-text"}," C点 过流开关",-1))),ht=w({__name:"index",setup(s){const w=l(),N={chart:null},U={chartDom:d("chartDom"),dateDom:d("dateDom")},W=b({dateLoading:!1}),C=b({baseInfo:{},echartInfo:{},dateType:"日"});C.snlist=y((()=>{let e=[];return(null==C?void 0:C.echartInfo)&&m._.isArray(null==C?void 0:C.echartInfo)&&(null==C||C.echartInfo.map((t=>{var l;(null==t?void 0:t.device)&&"total"!=(null==(l=t.device)?void 0:l.deviceId)&&e.push(t.device),(null==t?void 0:t.deviceList)&&m._.isArray(t.deviceList)&&(e=null==t?void 0:t.deviceList)}))),e.length>0?e:[{}]}));const ht=y((()=>{var e,t;return(((null==(e=C.baseInfo)?void 0:e.dayElec)||0)*((null==(t=C.baseInfo)?void 0:t.salePrice)||0)).toFixed(2)})),gt=b({itemList:[{label:"电量",value:["todayElectricity","totalElectricity"]},{label:"电压",value:["vac","vpv"]},{label:"电流",value:["iac","ipv"]},{label:"功率",value:["power"]},{label:"频率",value:["fac"]},{label:"温度",value:["temp"]}],item:["功率"],memoryItem:["功率"],snList:[],sn:"",date:y((()=>{var e,t;return(null==(e=U.dateDom)?void 0:e.value.date)?null==(t=U.dateDom)?void 0:t.value.date:""})),plantUid:"",plantName:""});async function It(e){e&&["发电量","电压","电流","功率","频率","温度"].includes(e[0])&&(gt.memoryItem=e),gt.item="total"==e?["功率"]:gt.memoryItem,gt[m._.isArray(e)?"item":"sn"]=(null==e?void 0:e.length)?e:gt.item;let t=U.dateDom.value.date;const l=N.chart.getOption();let s=[];switch(t.length){case 10:gt.snList[gt.snList.length-1]={label:"电站功率",value:"total"};const e={iac:"交流电流",ipv:"直流电流",vac:"交流电压",vpv:"直流电压"},t={todayElectricity:"当日发电量",totalElectricity:"累计发电量",temp:"温度"},a={power:"功率"},o={fac:"频率"},n=gt.item.map((e=>"total"==gt.sn?[gt.itemList.find((t=>t.label==e)).value[0]]:gt.itemList.find((t=>t.label==e)).value)).flat(),r=u(await i(gt.sn)).data.pvNum,c=n.map((l=>{const s=gt.itemList.findIndex((e=>e.value.includes(l)));if(Object.keys(e).includes(l))return m._.range(1,r+1).map((t=>({value:l+t,label:e[l]+t,y:[],yIndex:s})));if(Object.keys(o).includes(l))return m._.range(1,4).map((e=>({value:l+e,label:o[l]+e,y:[],yIndex:s})));if(Object.keys(t).includes(l))return{value:l,label:t[l],y:[],yIndex:s};if("power".includes(l)){return[{value:l,label:a[l],y:[],yIndex:s}].concat(m._.range(1,r+1).map((e=>({value:l+e,label:a[l]+e,y:[],yIndex:s}))))}})).flat(),d=C.echartInfo.find((e=>e.device.deviceId==gt.sn));for(const l of d.powerInfoDTOList){for(const e of c)e.y.push(l[e.value]||0);s.push(l.initTime.slice(10))}const f=m._.cloneDeep(l.series[0]);l.series.length=0;const p=m._.cloneDeep(l.yAxis[0]);l.yAxis.length=0,l.yAxis=gt.itemList.map(((e,t)=>(p.show=gt.item[0]==e.label,p.axisLabel={show:gt.item[0]===e.label},m._.cloneDeep(p)))),l.legend[0].data=c.map((e=>e.label));for(const s of c)f.data=s.y.reverse(),f.name=s.label,f.type="line",f.smooth=!0,f.yAxisIndex=s.yIndex,f.name.includes("电压")&&(f.lineStyle.color="#ff3d00"),f.name.includes("电流")&&(f.lineStyle.color="#22c55e"),f.name.includes("电量")&&(f.lineStyle.color="#eab308"),f.name.includes("功率")&&(f.lineStyle.color="#3b82f6"),f.name.includes("频率")&&(f.lineStyle.color="#757575"),f.name.includes("温度")&&(f.lineStyle.color="#ee6666"),l.series.push(m._.cloneDeep(f));l.xAxis[0].data=s.reverse(),l.grid.containLabel=!1;break;case 0:case 4:case 7:s=C.echartInfo[0].electricityList.map((e=>e.collectDate));const v=C.echartInfo[0].electricityList.map((e=>e.electricity));l.xAxis[0].data=s,l.series[0].data=v,l.series[0].name="发电量",l.series[0].type="bar",l.series=l.series.slice(0,1),l.legend[0].data=["发电量"],l.grid.containLabel=!0}N.chart.setOption(l,!0)}async function Lt(){const e=await o(gt.plantUid),t=u(e);C.baseInfo={...C.baseInfo,...t.data};const l=await n(gt.plantUid),s=u(l);w.title=s.data.plantName,C.baseInfo={...C.baseInfo,...s.data}}async function St(e){W.dateLoading=!0;const t=await a(gt.plantUid,gt.date),l=u(t);gt.snList=l.data.map((e=>{var t,l;return{label:null==(t=null==e?void 0:e.device)?void 0:t.deviceId,value:null==(l=null==e?void 0:e.device)?void 0:l.deviceId}})),gt.sn=gt.snList[gt.snList.length-1].value,C.echartInfo=l.data,C.baseInfo.dayElec=m._.isArray(null==l?void 0:l.data)&&l.data[l.data.length-1].totalElectricity||0,It(gt.sn),W.dateLoading=!1}return j((async()=>{var e,t;(null==(e=c().query)?void 0:e.plantUid)&&(gt.plantUid=null==(t=c().query)?void 0:t.plantUid),async function(){var e;const t=r(),l=location.hash.split("?plantUid=")[1];if(l)m._.set(t,"cur.plantUid",l),await Lt();else{const l=null==(e=(await f(1,1,"",[],"")).response.value.data.records[0])?void 0:e.plantUid;gt.plantUid=l,m._.set(t,"cur.plantUid",l),await Lt()}N.chart=await p(U.chartDom,F),await St(v().format("YYYY-MM-DD"))}()})),(l,s)=>{var i,a,o,n,r,c,d,m,u,f,p,v,w,b,y,j,N,U,F,Lt;const kt=t,Dt=e,_t=E;return h(),g("div",O,[I("div",M,[I("div",q,[I("div",V,[I("article",P,L((null==(a=100*(null==(i=S(C).baseInfo)?void 0:i.workEfficiency))?void 0:a.toFixed(2))||"0")+"% ",1),Y]),I("div",$,[I("article",B,L((null==(o=S(C).baseInfo)?void 0:o.plantCapacity)||"0.00")+"kWp ",1),Z]),I("div",G,[I("article",H,L((null==(n=S(C).baseInfo)?void 0:n.treeNum)||"0")+"棵 ",1),K]),I("div",Q,[I("article",R,L((null==(r=S(C).baseInfo)?void 0:r.reduceCo2)||"0")+"吨 ",1),J])]),I("div",X,[I("div",ee,[te,I("div",le,[I("div",se,L(1*(null==(c=S(C).baseInfo)?void 0:c.power)||"0"),1),ie]),I("div",ae,[oe,I("div",ne,L(1*(null==(d=S(C).baseInfo)?void 0:d.maxPower)||"0"),1)])]),I("div",re,[ce,I("div",de,[I("div",me,L((null==(m=S(C).baseInfo)?void 0:m.dayElec)||"0"),1),I("div",ue,L("总"==S(C).dateType?"总":`当${S(C).dateType}`),1)]),I("div",fe,[pe,I("div",ve,L((null==(u=S(C).baseInfo)?void 0:u.totalElectricity)||"0"),1)])]),I("div",we,[xe,I("div",be,[I("div",ye,L(S(ht)),1),I("div",je,L("总"==S(C).dateType?"总":`当${S(C).dateType}`),1)]),I("div",he,[ge,I("div",Ie,L((((null==(f=S(C).baseInfo)?void 0:f.totalElectricity)||0)*((null==(p=S(C).baseInfo)?void 0:p.salePrice)||0)).toFixed(2)),1)])]),I("div",Le,[Se,I("div",ke,[I("div",De,L((null==(v=S(C).baseInfo)?void 0:v.todayAlarmNum)||"0"),1),I("div",_e,L("实时告警"),1)]),I("div",Ae,[Te,I("div",ze,L((null==(w=S(C).baseInfo)?void 0:w.onlineInverterStats)||"0"),1)])])])]),I("div",Ne,[I("div",Ue,[k(kt,{class:"tw-h-[30%] tw-w-full",src:"https://www.btosolarman.com/hyWeb/img/bird.7bd94ba8.jpg"}),I("div",Ee,[I("div",Fe,[We,I("div",Ce,L((null==(b=S(C).baseInfo)?void 0:b.createTime)||"-"),1)]),I("div",Oe,[Me,I("div",qe,L((null==(y=S(C).baseInfo)?void 0:y.warrantyTime)||"-"),1)]),I("div",Ve,[Pe,I("div",Ye,L((null==(j=S(C).baseInfo)?void 0:j.address)||"-"),1)]),(h(!0),g(D,null,_(S(C).snlist,((e,t)=>(h(),g(D,{key:e},[I("div",$e,[I("div",Be,"逆变器SN"+L(t+1),1),I("div",Ze,L(e.deviceId||"-"),1)]),I("div",Ge,[He,I("div",Ke,L(e.manufacturer||"-"),1)]),I("div",Qe,[Re,I("div",Je,L(e.module||"-"),1)])],64)))),128)),I("div",Xe,[et,I("div",tt,L((null==(N=S(C).baseInfo)?void 0:N.userName)||"-"),1)]),I("div",lt,[st,I("div",it,L((null==(U=S(C).baseInfo)?void 0:U.userPhone)||"-"),1)]),I("div",at,[ot,I("div",nt,L((null==(F=S(C).baseInfo)?void 0:F.plantUid)||"-"),1)]),I("div",rt,[ct,I("div",dt,L((null==(Lt=S(C).baseInfo)?void 0:Lt.orientation)||"-"),1)])])]),I("div",mt,[I("div",ut,[k(Dt,{tabs:"日月年总",ref:"dateDom",type:"single",onUpdateDate:St,onTabChange:s[0]||(s[0]=e=>S(C).dateType=e),disabled:S(W).dateLoading,size:"small"},null,8,["disabled"]),"日"==S(C).dateType?(h(),A(_t,{key:0,class:"n-select1",options:S(gt).snList,size:"medium","key-field":"value","label-field":"label",value:S(gt).sn,"on-update:value":It,placeholder:"选择逆变器"},null,8,["options","value"])):T("",!0),"日"==S(C).dateType&&"total"!=S(gt).sn?(h(),A(_t,{key:1,class:"n-select2",options:S(gt).itemList,size:"medium","key-field":"label","label-field":"label","on-update:value":It,value:S(gt).item,placeholder:"选择单位","max-tag-count":"responsive",multiple:""},null,8,["options","value"])):T("",!0)]),I("figure",ft,null,512),I("div",pt,[I("div",vt,[wt,k(S(x),{class:z(["tw-w-[17px] tw-ml-2 tw-scale-125","正常"==S(C).baseInfo.apv?"tw-text-green-600":"tw-text-red-600"])},null,8,["class"])]),I("div",xt,[bt,k(S(x),{class:z(["tw-w-[17px] tw-ml-2 tw-scale-125","正常"==S(C).baseInfo.bpv?"tw-text-green-600":"tw-text-red-600"])},null,8,["class"])]),I("div",yt,[jt,k(S(x),{class:z(["tw-w-[17px] tw-ml-2 tw-scale-125","正常"==S(C).baseInfo.cpv?"tw-text-green-600":"tw-text-red-600"])},null,8,["class"])])])])])])}}},[["__scopeId","data-v-23827e32"]]);export{ht as default};
