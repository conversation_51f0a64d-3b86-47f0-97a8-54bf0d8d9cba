import"./vue-5bfa3a54.js";import{n as t}from"./@vue-5e5cdef9.js";function e(t){return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function r(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function n(t){return function(t){if(Array.isArray(t))return o(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return o(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return o(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function i(t,r){if(t===r)return!0;if("object"===e(t)){for(var n in t)if(!i(t[n],r[n]))return!1;return!0}return!1}var a=function(){function e(t,r,n){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),this.el=t,this.observer=null,this.frozen=!1,this.createObserver(r,n)}var o,i,a;return o=e,i=[{key:"createObserver",value:function(e,r){var o=this;if(this.observer&&this.destroyObserver(),!this.frozen){var i;if(this.options="function"==typeof(i=e)?{callback:i}:i,this.callback=function(t,e){o.options.callback(t,e),t&&o.options.once&&(o.frozen=!0,o.destroyObserver())},this.callback&&this.options.throttle){var a=(this.options.throttleOptions||{}).leading;this.callback=function(t,e){var r,o,i,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},s=function(s){for(var l=arguments.length,c=new Array(l>1?l-1:0),u=1;u<l;u++)c[u-1]=arguments[u];if(i=c,!r||s!==o){var f=a.leading;"function"==typeof f&&(f=f(s,o)),r&&s===o||!f||t.apply(void 0,[s].concat(n(i))),o=s,clearTimeout(r),r=setTimeout((function(){t.apply(void 0,[s].concat(n(i))),r=0}),e)}};return s._clear=function(){clearTimeout(r),r=null},s}(this.callback,this.options.throttle,{leading:function(t){return"both"===a||"visible"===a&&t||"hidden"===a&&!t}})}this.oldResult=void 0,this.observer=new IntersectionObserver((function(t){var e=t[0];if(t.length>1){var r=t.find((function(t){return t.isIntersecting}));r&&(e=r)}if(o.callback){var n=e.isIntersecting&&e.intersectionRatio>=o.threshold;if(n===o.oldResult)return;o.oldResult=n,o.callback(n,e)}}),this.options.intersection),t((function(){o.observer&&o.observer.observe(o.el)}))}}},{key:"destroyObserver",value:function(){this.observer&&(this.observer.disconnect(),this.observer=null),this.callback&&this.callback._clear&&(this.callback._clear(),this.callback=null)}},{key:"threshold",get:function(){return this.options.intersection&&"number"==typeof this.options.intersection.threshold?this.options.intersection.threshold:0}}],i&&r(o.prototype,i),a&&r(o,a),e}();function s(t,e,r){var n=e.value;if(n)if("undefined"==typeof IntersectionObserver);else{var o=new a(t,n,r);t._vue_visibilityState=o}}function l(t){var e=t._vue_visibilityState;e&&(e.destroyObserver(),delete t._vue_visibilityState)}var c={beforeMount:s,updated:function(t,e,r){var n=e.value;if(!i(n,e.oldValue)){var o=t._vue_visibilityState;n?o?o.createObserver(n,r):s(t,{value:n},r):l(t)}},unmounted:l};export{c as O};
