import{C as e,f as t,d as a,D as l,F as i,x as o,w as d,u as s,e as r,c as n}from"./element-plus-d975be09.js";import"./vue-5bfa3a54.js";import{Q as m}from"./@element-plus-4c34063a.js";import{a as p}from"./vue-router-6159329f.js";import{a as u}from"./vxe-table-3a25f2d2.js";import{X as c}from"./xe-utils-fe99d42a.js";import{i as y,_ as b}from"./index-8cc8d4b8.js";import{h,j as v,m as f,p as x,as as j,o as g,c as k,x as D,a8 as w,a as _,aa as V,f as C,r as q,b as T,t as z,a6 as S,F as U,k as A,l as L,C as M,D as F}from"./@vue-5e5cdef9.js";import"./lodash-es-ea7deab5.js";import"./@vueuse-af86c621.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./dayjs-d60cc07f.js";import"./@babel-f3c0a00c.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./dom-zindex-5f662ad1.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./nprogress-e136a1b4.js";import"./quasar-b3f06d8a.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-7bbbb435.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./lodash-6d99edc3.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";const E={type:[{type:"success",label:"菜单"},{type:"",label:"按钮"},{type:"warning",label:"接口"}]},I={version:[{value:"2",label:"bto2.0"},{value:"3",label:"bto3.0"}],type:[{value:"0",label:"菜单"},{value:"1",label:"按钮"},{value:"2",label:"接口"}],keepAlive:[{value:0,label:"否"},{value:1,label:"是"}],hidden:[{value:0,label:"否"},{value:1,label:"是"}],breadcrumb:[{value:0,label:"否"},{value:1,label:"是"}],affix:[{value:0,label:"否"},{value:1,label:"是"}],openStyle:[{value:0,label:"当前页"},{value:1,label:"新窗口"}]},O={version:{htmltype:"select",dataType:"text",text:"版本",require:!0},label:{htmltype:"input",dataType:"text",text:"菜单名称",require:!0,place:"请输入菜单名称"},path:{htmltype:"input",dataType:"text",text:"菜单路径",require:!0,place:"请输入对应文件所在前端项目路由路径"},component:{htmltype:"input",dataType:"text",text:"文件路径",require:!1,place:"请输入对应文件所在前端项目文件路径"},redirect:{htmltype:"input",dataType:"text",text:"重定向",place:"重新定位到哪个子级菜单",require:!1},title:{htmltype:"input",dataType:"text",text:"路由名称",require:!0,place:"前端路由名称，唯一，请输入英文"},icon:{htmltype:"btn",dataType:"text",text:"图标",require:!1},pid:{htmltype:"tree",dataType:"text",text:"父级菜单ID",require:!0,place:""},sort:{htmltype:"numEdit",dataType:"text",text:"排序",place:"",require:!1},type:{htmltype:"select",dataType:"text",text:"类别",require:!0},hidden:{htmltype:"select",dataType:"text",text:"不在侧边菜单显示",require:!0},keepAlive:{htmltype:"select",dataType:"text",text:"缓存数据",require:!0},breadcrumb:{htmltype:"select",dataType:"text",text:"面包屑展示",require:!0},affix:{htmltype:"select",dataType:"text",text:"tags-view固定",require:!0},openStyle:{htmltype:"select",dataType:"text",text:"打开方式",require:!1}},P={class:"table-btn"},R=(e=>(M("data-v-6019e220"),e=e(),F(),e))((()=>_("div",{class:"table-btn"},null,-1))),H={class:"close-btn text-right"},N={class:"iconList u-flex-center"},G=b(Object.assign({name:"menu"},{__name:"index",setup(b){p();const M=h(),F=h(),G=v({condition:{},modelData:{type:"menu",visible:!1,loading:!1,add:{activeMenu:"",component:"",affix:0,alwaysShow:0,auth:"",breadcrumb:1,hidden:0,icon:"",id:"",keepAlive:0,label:"",path:"",pid:null,sort:0,title:"",type:"0",version:"3"},data:{activeMenu:"",affix:0,alwaysShow:0,component:"",auth:"",breadcrumb:0,hidden:0,icon:"",id:"",keepAlive:0,label:"",path:"",pid:null,sort:0,title:"",type:"0",version:""},menuList:[],iconVisible:!1},tablePage:{totalResult:0,currentPage:1,pageSize:15}}),$=v({id:"menuinfo",border:"inner",showFooter:!1,minHeight:600,height:"auto",loading:!1,autoResize:!0,editConfig:{trigger:"click",mode:"cell"},sortConfig:{remote:!0,multiple:!1,defaultSort:[]},data:[],toolbarConfig:{custom:!1,slots:{buttons:"toolbar_buttons",tools:"toolbar_tools"}},customConfig:{storage:{visible:!0,fixed:!0}},treeConfig:{rowField:"id",parentField:"pid",childrenField:"children"},columns:[{type:"seq",width:50,fixed:"left"},{field:"label",title:"菜单名称",treeNode:!0},{field:"redirect",title:"重定向"},{field:"icon",title:"图标",slots:{default:"icon-name"}},{field:"type",title:"类型",slots:{default:"type-name"}},{field:"operations",title:"操作",fixed:"right",showOverflow:!1,slots:{default:"row-operate"}}]}),B=({field:e,order:t})=>{null===t?(G.condition.order="",G.isAsc=""):(G.condition.order=e,G.condition.isAsc="asc"==t),Q()},Q=async()=>{$.loading=!0;try{const e=await y({url:"/system/menu/getMenuList",method:"GET"});if("00000"==e.status){$.data=[],$.data=c.clone(e.data,!0);let t=c.clone(e.data,!0);G.modelData.menuList=Y(t),G.modelData.menuList.unshift({label:"根目录",value:"0"})}else $.data=[];$.loading=!1}catch(e){$.loading=!1}},W=async()=>{try{"00000"==(await(e=G.modelData.data,y({url:"/system/menu/addMenu",method:"POST",data:{...e}}))).status&&("condition"=="add"?G.condition={plantUid:"",plantName:"",deviceId:"",multiInverterStatus:[]}:G.modelData.add={activeMenu:"",affix:0,component:"",alwaysShow:0,auth:"",breadcrumb:1,hidden:0,icon:"",id:"",keepAlive:0,label:"",path:"",pid:null,sort:0,title:"",type:"0",version:"3"},K(),Q())}catch(t){}var e},X=async()=>{try{"00000"==(await(e=G.modelData.data,y({url:"/system/menu/editMenu",method:"POST",data:{...e}}))).status&&(K(),Q())}catch(t){}var e},Z=async e=>{if("confirm"===await u.modal.confirm("您确定要删除此条记录吗?"))try{"00000"==(await(t=e,y({url:`/system/menu/deleteMenu/${t}`,method:"DELETE"}))).status&&(K(),Q())}catch(a){}var t},J=(e,t)=>{G.modelData.type=e,G.modelData.data="add"==e?G.modelData.add:c.clone(t,!0),G.modelData.visible=!0},K=()=>{G.modelData.visible=!1},Y=e=>{for(let t in e){let a={label:e[t].label,value:e[t].id};e[t].children?(e[t]={children:e[t].children,...a},Y(e[t].children)):e[t]=a}return e},ee=()=>{G.modelData.iconVisible=!1},te=(e,t,a)=>{};return f((async()=>{await Q()})),x((()=>{})),(p,u)=>{const c=j("vxe-button"),y=e,b=t,h=j("vxe-grid"),v=a,f=l,x=i,Q=o,Y=d,ae=s,le=r,ie=n,oe=j("vxe-modal");return g(),k("div",{class:"app-container",ref_key:"appContainerRef",ref:M},[D(h,S({ref_key:"xGrid",ref:F,class:"my-grid66"},T($),{onSortChange:B}),{toolbar_buttons:w((()=>[_("div",P,[D(c,{status:"primary",onClick:u[0]||(u[0]=e=>J("add"))},{default:w((()=>[V("创建")])),_:1})])])),toolbar_tools:w((()=>[R])),"icon-name":w((({row:e})=>[(g(),C(q(e.icon),{class:"el-icon"}))])),"type-name":w((({row:e})=>[D(y,{type:T(E).type[e.type].type},{default:w((()=>[V(z(T(E).type[e.type].label),1)])),_:2},1032,["type"])])),"row-operate":w((({row:e})=>[D(b,{type:"primary",size:"small",onClick:t=>J("edit",e)},{default:w((()=>[V("编辑")])),_:2},1032,["onClick"]),D(b,{type:"danger",size:"small",onClick:t=>Z(e.id)},{default:w((()=>[V("删除")])),_:2},1032,["onClick"])])),_:1},16),D(oe,{modelValue:T(G).modelData.visible,"onUpdate:modelValue":u[5]||(u[5]=e=>T(G).modelData.visible=e),title:"edit"==T(G).modelData.type?"编辑菜单":"新建菜单",width:"600","min-width":"400","min-height":"300",loading:T(G).modelData.loading,resize:"","destroy-on-close":"",onHide:K,zIndex:2010},{default:w((()=>["edit"==T(G).modelData.type||"add"==T(G).modelData.type?(g(),C(ie,{key:0,ref:"ruleFormRef",model:T(G).modelData.data},{default:w((()=>[(g(!0),k(U,null,A(T(O),((e,t)=>(g(),C(le,{key:t,label:T(O)[t].text,prop:T(O)[t]?t:"",required:e.require},{default:w((()=>["input"==T(O)[t].htmltype?(g(),C(v,{key:0,type:T(O)[t].dataType,modelValue:T(G).modelData.data[t],"onUpdate:modelValue":e=>T(G).modelData.data[t]=e,clearable:"",placeholder:T(O)[t].place},null,8,["type","modelValue","onUpdate:modelValue","placeholder"])):"select"==T(O)[t].htmltype?(g(),C(x,{key:1,modelValue:T(G).modelData.data[t],"onUpdate:modelValue":e=>T(G).modelData.data[t]=e,placeholder:"请选择"},{default:w((()=>[(g(!0),k(U,null,A(T(I)[t],(e=>(g(),C(f,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue"])):"btn"==T(O)[t].htmltype?(g(),C(Q,{key:2,placement:"right",width:400,trigger:"click",visible:T(G).modelData.iconVisible,onHide:ee},{reference:w((()=>[D(b,{style:{"margin-right":"16px"},icon:T(G).modelData.data.icon,onClick:u[1]||(u[1]=e=>T(G).modelData.iconVisible=!0)},{default:w((()=>[V(z(""==T(G).modelData.data.icon?"选择图标":"已选择"),1)])),_:1},8,["icon"])])),default:w((()=>[_("div",H,[D(b,{class:"close",type:"danger",size:"small",onClick:u[2]||(u[2]=e=>T(G).modelData.iconVisible=!1)},{default:w((()=>[V("关闭")])),_:1})]),_("div",N,[(g(),k(U,null,A(m,((e,t)=>D(b,{key:t,icon:t,onClick:e=>(e=>{G.modelData.data.icon=e,G.modelData.iconVisible=!1})(t)},null,8,["icon","onClick"]))),64))])])),_:2},1032,["visible"])):"numEdit"==T(O)[t].htmltype?(g(),C(Y,{key:3,modelValue:T(G).modelData.data[t],"onUpdate:modelValue":e=>T(G).modelData.data[t]=e,min:0,max:10,"controls-position":"right"},null,8,["modelValue","onUpdate:modelValue"])):"tree"==T(O)[t].htmltype?(g(),C(ae,{key:4,class:"w-full",modelValue:T(G).modelData.data[t],"onUpdate:modelValue":e=>T(G).modelData.data[t]=e,data:T(G).modelData.menuList,"render-after-expand":!1,"show-checkbox":"","check-strictly":"","check-on-click-node":"",onCheckChange:te,placeholder:"请选择父项",disabled:"edit"==T(G).modelData.type},null,8,["modelValue","onUpdate:modelValue","data","disabled"])):L("",!0)])),_:2},1032,["label","prop","required"])))),128)),D(le,{class:"text-right"},{default:w((()=>[D(b,{type:"info",onClick:K},{default:w((()=>[V("关闭")])),_:1}),"add"==T(G).modelData.type?(g(),C(b,{key:0,type:"primary",onClick:u[3]||(u[3]=e=>W())},{default:w((()=>[V("提交")])),_:1})):(g(),C(b,{key:1,type:"primary",onClick:u[4]||(u[4]=e=>X())},{default:w((()=>[V("提交")])),_:1}))])),_:1})])),_:1},8,["model"])):(g(),k(U,{key:1},[],64))])),_:1},8,["modelValue","title","loading"])],512)}}}),[["__scopeId","data-v-6019e220"]]);export{G as default};
