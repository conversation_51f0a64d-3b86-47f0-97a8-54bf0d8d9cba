import{i as t}from"./vue-demi-01e7384c.js";import{ax as e,h as n,O as s,aG as o,i as a,w as c,j as r,H as i,aE as u,ap as f,z as p,E as l,G as h,n as d,I as y,e as v}from"./@vue-5e5cdef9.js";
/*!
 * pinia v2.1.7
 * (c) 2023 <PERSON>
 * @license MIT
 */let _;const b=t=>_=t,j=Symbol();function O(t){return t&&"object"==typeof t&&"[object Object]"===Object.prototype.toString.call(t)&&"function"!=typeof t.toJSON}var m,$;function g(){const o=e(!0),a=o.run((()=>n({})));let c=[],r=[];const i=s({install(t){b(i),i._a=t,t.provide(j,i),t.config.globalProperties.$pinia=i,r.forEach((t=>c.push(t))),r=[]},use(e){return this._a||t?c.push(e):r.push(e),this},_p:c,_a:null,_e:o,_s:new Map,state:a});return i}($=m||(m={})).direct="direct",$.patchObject="patch object",$.patchFunction="patch function";const E=()=>{};function P(t,e,n,s=E){t.push(e);const o=()=>{const n=t.indexOf(e);n>-1&&(t.splice(n,1),s())};return!n&&l()&&h(o),o}function S(t,...e){t.slice().forEach((t=>{t(...e)}))}const w=t=>t();function x(t,e){t instanceof Map&&e instanceof Map&&e.forEach(((e,n)=>t.set(n,e))),t instanceof Set&&e instanceof Set&&e.forEach(t.add,t);for(const n in e){if(!e.hasOwnProperty(n))continue;const s=e[n],o=t[n];O(o)&&O(s)&&t.hasOwnProperty(n)&&!i(s)&&!u(s)?t[n]=x(o,s):t[n]=s}return t}const I=Symbol();const{assign:M}=Object;function A(t,s,o={},a,p,l){let h;const y=M({actions:{}},o),v={deep:!0};let _,j,$,g=[],A=[];const F=a.state.value[t];let G;function k(e){let n;_=j=!1,"function"==typeof e?(e(a.state.value[t]),n={type:m.patchFunction,storeId:t,events:$}):(x(a.state.value[t],e),n={type:m.patchObject,payload:e,storeId:t,events:$});const s=G=Symbol();d().then((()=>{G===s&&(_=!0)})),j=!0,S(g,n,a.state.value[t])}l||F||(a.state.value[t]={}),n({});const z=l?function(){const{state:t}=o,e=t?t():{};this.$patch((t=>{M(t,e)}))}:E;function C(e,n){return function(){b(a);const s=Array.from(arguments),o=[],c=[];let r;S(A,{args:s,name:e,store:J,after:function(t){o.push(t)},onError:function(t){c.push(t)}});try{r=n.apply(this&&this.$id===t?this:J,s)}catch(i){throw S(c,i),i}return r instanceof Promise?r.then((t=>(S(o,t),t))).catch((t=>(S(c,t),Promise.reject(t)))):(S(o,r),r)}}const H={_p:a,$id:t,$onAction:P.bind(null,A),$patch:k,$reset:z,$subscribe(e,n={}){const s=P(g,e,n.detached,(()=>o())),o=h.run((()=>c((()=>a.state.value[t]),(s=>{("sync"===n.flush?j:_)&&e({storeId:t,type:m.direct,events:$},s)}),M({},v,n))));return s},$dispose:function(){h.stop(),g=[],A=[],a._s.delete(t)}},J=r(H);a._s.set(t,J);const N=(a._a&&a._a.runWithContext||w)((()=>a._e.run((()=>(h=e()).run(s)))));for(const e in N){const n=N[e];if(i(n)&&(!i(q=n)||!q.effect)||u(n))l||(!F||O(W=n)&&W.hasOwnProperty(I)||(i(n)?n.value=F[e]:x(n,F[e])),a.state.value[t][e]=n);else if("function"==typeof n){const t=C(e,n);N[e]=t,y.actions[e]=n}}var W,q;return M(J,N),M(f(J),N),Object.defineProperty(J,"$state",{get:()=>a.state.value[t],set:t=>{k((e=>{M(e,t)}))}}),a._p.forEach((t=>{M(J,h.run((()=>t({store:J,app:a._a,pinia:a,options:y}))))})),F&&l&&o.hydrate&&o.hydrate(J.$state,F),_=!0,j=!0,J}function F(t,e,n){let c,r;const i="function"==typeof e;function u(t,n){const u=o();(t=t||(u?a(j,null):null))&&b(t),(t=_)._s.has(c)||(i?A(c,e,r,t):function(t,e,n,o){const{state:a,actions:c,getters:r}=e,i=n.state.value[t];let u;u=A(t,(function(){i||(n.state.value[t]=a?a():{});const e=y(n.state.value[t]);return M(e,c,Object.keys(r||{}).reduce(((e,o)=>(e[o]=s(v((()=>{b(n);const e=n._s.get(t);return r[o].call(e,e)}))),e)),{}))}),e,n,0,!0)}(c,r,t));return t._s.get(c)}return"string"==typeof t?(c=t,r=i?n:e):(r=t,c=t.id),u.$id=c,u}function G(t){{t=f(t);const e={};for(const n in t){const s=t[n];(i(s)||u(s))&&(e[n]=p(t,n))}return e}}export{g as c,F as d,G as s};
