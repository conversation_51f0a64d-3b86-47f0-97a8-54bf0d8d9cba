function e(e,t){var r;return e="object"==typeof(r=e)&&null!==r?e:Object.create(null),new Proxy(e,{get:(e,r,o)=>"key"===r?Reflect.get(e,r,o):Reflect.get(e,r,o)||Reflect.get(t,r,o)})}function t(e,{storage:t,serializer:r,key:o,debug:s}){try{const s=null==t?void 0:t.getItem(o);s&&e.$patch(null==r?void 0:r.deserialize(s))}catch(n){}}function r(e,{storage:t,serializer:r,key:o,paths:s,debug:n}){try{const n=Array.isArray(s)?function(e,t){return t.reduce(((t,r)=>{const o=r.split(".");return function(e,t,r){return t.slice(0,-1).reduce(((e,t)=>/^(__proto__)$/.test(t)?{}:e[t]=e[t]||{}),e)[t[t.length-1]]=r,e}(t,o,function(e,t){return t.reduce(((e,t)=>null==e?void 0:e[t]),e)}(e,o))}),{})}(e,s):e;t.setItem(o,r.serialize(n))}catch(i){}}var o=function(o={}){return s=>{const{auto:n=!1}=o,{options:{persist:i=n},store:a,pinia:l}=s;if(!i)return;if(!(a.$id in l.state.value)){const e=l._s.get(a.$id.replace("__hot:",""));return void(e&&Promise.resolve().then((()=>e.$persist())))}const u=(Array.isArray(i)?i.map((t=>e(t,o))):[e(i,o)]).map(function(e,t){return r=>{var o;try{const{storage:s=localStorage,beforeRestore:n,afterRestore:i,serializer:a={serialize:JSON.stringify,deserialize:JSON.parse},key:l=t.$id,paths:u=null,debug:c=!1}=r;return{storage:s,beforeRestore:n,afterRestore:i,serializer:a,key:(null!=(o=e.key)?o:e=>e)("string"==typeof l?l:l(t.$id)),paths:u,debug:c}}catch(s){return r.debug,null}}}(o,a)).filter(Boolean);a.$persist=()=>{u.forEach((e=>{r(a.$state,e)}))},a.$hydrate=({runHooks:e=!0}={})=>{u.forEach((r=>{const{beforeRestore:o,afterRestore:n}=r;e&&(null==o||o(s)),t(a,r),e&&(null==n||n(s))}))},u.forEach((e=>{const{beforeRestore:o,afterRestore:n}=e;null==o||o(s),t(a,e),null==n||n(s),a.$subscribe(((t,o)=>{r(o,e)}),{detached:!0})}))}}();export{o as s};
