import{f as a,B as e}from"./element-plus-d975be09.js";import"./vue-5bfa3a54.js";import{_ as t,g as l,l as s,b as i}from"./index-8cc8d4b8.js";import{s as r}from"./pinia-c7531a5f.js";import{m as n,J as o}from"./vue3-drr-grid-layout-3f9cba0a.js";import{c,d as u,i as m}from"./chartResize-3e3d11d7.js";import{X as d}from"./xe-utils-fe99d42a.js";import{B as p}from"./bignumber.js-a537a5ca.js";import{a as v}from"./vxe-table-3a25f2d2.js";import{i as h,p as f,a as g,b as y,c as x,d as w,e as b,l as j,g as C,f as k,h as N,j as E,k as P,m as S}from"./index-6788ffa0.js";import{g as _}from"./imgImport-3dead1a5.js";import{h as I,j as z,w as D,e as M,m as q,v as W,as as $,o as O,c as L,b as R,a9 as T,l as F,a as J,F as U,k as A,y as B,t as V,aa as G,x as Z,a8 as H,f as K,a6 as X,C as Y,D as Q}from"./@vue-5e5cdef9.js";import"./lodash-es-ea7deab5.js";import"./@vueuse-af86c621.js";import"./vue-router-6159329f.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./dayjs-d60cc07f.js";import"./@babel-f3c0a00c.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./nprogress-e136a1b4.js";import"./quasar-b3f06d8a.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-7bbbb435.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./lodash-6d99edc3.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./vue-demi-01e7384c.js";import"./dom-zindex-5f662ad1.js";import"./element-resize-detector-0d37a2ab.js";import"./batch-processor-06abf2b4.js";import"./index-15186f59.js";import"./sass-ac65759c.js";import"./immutable-901ee85f.js";import"./exceljs-b3a0e81d.js";const aa=a=>(Y("data-v-543b11da"),a=a(),Q(),a),ea={key:0,class:"screen-loading u-wh-full","element-loading-text":"正在获取数据"},ta={class:"u-wh-full info-statics u-flex-column u-border"},la=aa((()=>J("div",{class:"title regular-title u-flex-y-center"},[J("p",{class:"square"}),J("p",null,"整体概况")],-1))),sa={class:"info-statics-content u-flex-1 w-full"},ia=["onClick"],ra={class:"card-icon u-flex-center-no"},na=["src","alt"],oa={class:"u-flex-1 u-flex-column justify-center"},ca={class:"text-left small-title"},ua={class:"text-left digtal"},ma={class:"u-wh-full station-statics u-flex-column u-border"},da=aa((()=>J("div",{class:"title regular-title u-flex-y-center"},[J("p",{class:"square"}),J("p",null,"电站实时状态")],-1))),pa={class:"station-statics-content u-flex-1 w-full u-flex-center-no"},va=aa((()=>J("figure",{id:"plantProgressChart",class:"progress u-flex-1 h-full"},null,-1))),ha={class:"index u-flex-1 station-statics-content-item"},fa=["onClick"],ga={class:"w-full u-flex-center-no u-gap-6"},ya=["xlink:href"],xa=["src"],wa={class:"small-title text-center"},ba={class:"digtal text-center u-flex-center-no"},ja={class:"u-wh-full elec-chart u-flex-column u-border"},Ca=aa((()=>J("div",{class:"title regular-title u-flex-y-center"},[J("p",{class:"square"}),J("p",null,"近期发电")],-1))),ka={class:"elec-chart-content u-flex-1 u-flex-center-no u-gap-18"},Na={class:"elec-chart-content-chart u-flex-1 h-full u-flex-column"},Ea={class:"u-flex-center title1"},Pa=aa((()=>J("span",null,"今日发电",-1))),Sa=aa((()=>J("figure",{class:"u-flex-1 w-full",id:"dayElecChart"},null,-1))),_a=aa((()=>J("div",{class:"elec-chart-content-chart u-flex-1 h-full u-flex-column"},[J("p",{class:"text-center title0"},"近6月发电趋势"),J("figure",{class:"u-flex-1 w-full",id:"monthElecChart"})],-1))),Ia={class:"u-wh-full inverter-statics u-flex-column u-border"},za=aa((()=>J("div",{class:"title regular-title u-flex-y-center"},[J("p",{class:"square"}),J("p",null,"逆变器实时状态")],-1))),Da={class:"inverter-statics-content u-flex-1 w-full u-flex-center-no"},Ma=aa((()=>J("figure",{id:"inverterProgressChart",class:"progress u-flex-1 h-full"},null,-1))),qa={class:"index u-flex-1 inverter-statics-content-item"},Wa=["onClick"],$a={class:"w-full u-flex-center-no u-gap-6"},Oa=["xlink:href"],La=["src"],Ra={class:"small-title text-center"},Ta={class:"digtal text-center u-flex-center-no"},Fa={class:"grid-layout u-wh-full"},Ja={class:"grid-layout-box u-flex-center"},Ua={class:"u-wh-full u-flex-center"},Aa={class:"grid-save-btns u-flex-y-center justify-end"},Ba=t({__name:"indexCopy",setup(t){const Y=l(),{projectId:Q}=r(Y),aa=I();I();const Ba=I(),Va=I(!0);let Ga=null;const Za=z({loading:!1,rowHeight:400,layout:[{x:0,y:0,w:1,h:1,i:1,name:"infoStatics",template:h},{x:1,y:0,w:1,h:1,i:2,name:"plantStatus",template:f},{x:0,y:1,w:1,h:1,i:3,name:"plantElec",template:g},{x:1,y:1,w:1,h:1,i:4,name:"inverterStatus",template:y}]}),Ha=z({visible:!1,submitLoading:!1,layout:[{x:0,y:0,w:1,h:1,i:1,name:"infoStatics",text:"电站概况"},{x:1,y:0,w:1,h:1,i:2,name:"plantStatus",text:"电站状态"},{x:0,y:1,w:1,h:1,i:3,name:"plantElec",text:"发电趋势"},{x:1,y:1,w:1,h:1,i:4,name:"inverterStatus",text:"逆变器状态"}]}),Ka=I(""),Xa=z({totalNum:{label:"电站数量",value:0,unit:null,icon:"icon-Union",bgColor:"bg-fff4e7",color:"font-ffa828",picName:"plant_num.png",event:()=>ie()},InverterNum:{label:"逆变器数",value:0,unit:null,icon:"icon-nibianqi2",bgColor:"bg-eaf4ff",color:"font-3a8fea",picName:"inverter_num.png",event:()=>re()},plantCapacity:{label:"装机容量",value:0,icon:"icon-zhuangjirongliang-mianicon",unit:"(KWp)",bgColor:"bg-eefbff",picName:"capacity.png",color:"font-0facd5"},totalElectricity:{label:"总发电量",value:0,icon:"icon-leijifadianliang",unit:"(kWh)",bgColor:"bg-ebfefd",picName:"total_elec.png",color:"font-36cfc9"}}),Ya=z({visible:!1,filterText:"",sel:"",selLabel:"",data:[]}),Qa=z({offlineNum:{label:"光精灵离线",value:0,color:"color-outline",icon:"#icon-RemoveFilled",event:()=>ie(0)},alarmNum:{label:"告警",value:0,color:"color-alarm",icon:"#icon-jinggaozhuangtai",event:()=>se("")},inverterShutdownNum:{label:"夜间模式",value:0,color:"color-night",icon:"#icon-a-zu2837",event:()=>ie(4)},selfCheckNum:{label:"自检提示",value:0,color:"color-self",icon:"#icon-jinggaozhuangtai",event:()=>ie(5)}}),ae=z({offlineNum:{label:"离线",value:0,color:"color-outline",icon:"#icon-RemoveFilled",event:()=>re("0")},alarmNum:{label:"告警",value:0,color:"color-alarm",icon:"icon-alarmNew",event:()=>re("1")},inverterShutdownNum:{label:"夜间模式",value:0,color:"color-night",icon:"#icon-a-zu2837",event:()=>re("4")},selfCheckNum:{label:"自检提示",value:0,color:"color-self",icon:"#icon-jinggaozhuangtai",event:()=>re()}}),ee={plantProgressChart:null,inverterProgressChart:null,dayElecChart:null,monthElecChart:null},te=z({plantProgressChart:null,inverterProgressChart:null,dayElecChart:null,monthElecChart:null}),le=z({plantProgressChart:0,inverterProgressChart:0,dayElecChart:{xData:[],data:[]},monthElecChart:{xData:[],data:[]}}),se=a=>{s.push({path:"/plantManage/alarmManage",query:{deviceType:a}})},ie=a=>{s.push({path:"statementManage/plant",query:{status:a}})},re=a=>{s.push({path:"plantManage/alarmManage",query:{multiInverterStatus:a}})},ne=(a,e)=>{let t;switch(te[a]=!0,a){case"dayElecChart":t=d.clone(w,!0);let a=d.clone(j,!0);a.data=le.dayElecChart.data,a.name="发电量(MWh)",t.series[0]=a,t.xAxis.data=le.dayElecChart.xData;break;case"monthElecChart":t=d.clone(w,!0);let e=d.clone(b,!0);e.data=le.monthElecChart.data,e.name="发电量(MWh)",t.series[0]=e,t.yAxis[0].splitLine.lineStyle.color=["rgba(248, 182, 72, .6)"],t.xAxis.data=le.monthElecChart.xData;break;case"plantProgressChart":t=d.clone(x,!0),t.graphic[0].style.text=`${new p(le.plantProgressChart).times(100).decimalPlaces(2).toNumber()}%`;let l=new p(le.plantProgressChart).times(100).decimalPlaces(2).toNumber();t.series[0].data[0].value=l,t.series[0].data[1].value=new p(100).minus(l).decimalPlaces(2).toNumber();break;case"inverterProgressChart":t=d.clone(x,!0),t.graphic[0].style.text=`${new p(le.inverterProgressChart).times(100).decimalPlaces(2).toNumber()}%`,t.graphic[1].style.text="逆变器正常运行率";let s=new p(le.inverterProgressChart).times(100).decimalPlaces(2).toNumber();t.series[0].data[0].value=s,t.series[0].data[1].value=new p(100).minus(s).decimalPlaces(2).toNumber()}e[a]&&e[a].dispose(),e[a]=m(a,t),te[a]=!1,c(aa.value,e)},oe=async a=>{try{const e=await C(a);if("00000"==e.status){for(let a in Qa)e.data[a]&&(Qa[a].value=e.data[a]);Xa.totalNum.value=e.data.totalNum,le.plantProgressChart=e.data.normalRate,Za.loading&&ne("plantProgressChart",ee)}}catch(e){}},ce=async a=>{try{const e=await k(a);if("00000"==e.status){let a=e.data.plantCapacity;a.length>9?(Xa.plantCapacity.value=new p(a).div(1e3).decimalPlaces(2).toString(),Xa.plantCapacity.unit="(MWp)"):(Xa.plantCapacity.value=new p(a).decimalPlaces(2).toString(),Xa.plantCapacity.unit="(kWp)");let t=e.data.totalElectricity;t.length>9?(Xa.totalElectricity.value=new p(t).div(1e3).decimalPlaces(2).toString(),Xa.totalElectricity.unit="(MWh)"):(Xa.totalElectricity.value=new p(t).decimalPlaces(2).toString(),Xa.totalElectricity.unit="(kWh)"),Ka.value=`${e.data.todayElectricity}kWh`}}catch(e){}},ue=async a=>{try{const e=await N(a);if("00000"==e.status){for(let a in ae)e.data[a]?ae[a].value=e.data[a]:ae[a].value="NaN";Xa.InverterNum.value=e.data.totalNum,le.inverterProgressChart=e.data.normalRate,Za.loading&&ne("inverterProgressChart",ee)}}catch(e){}},me=async a=>{try{const e=await E(a);"00000"==e.status&&(le.monthElecChart={xData:[],data:[]},e.data.forEach((a=>{le.monthElecChart.xData.push(`${a.collectDate.substr(5,2)}月`),le.monthElecChart.data.push(new p(a.electricity).div(1e3).decimalPlaces(2).toNumber())})),Za.loading&&ne("monthElecChart",ee))}catch(e){}},de=async a=>{try{const e=await P(a);"00000"==e.status&&(le.dayElecChart={xData:[],data:[]},e.data.forEach((a=>{le.dayElecChart.xData.push(`${a.collectDate.substr(11,5)}时`),le.dayElecChart.data.push(new p(a.electricity).div(1e3).decimalPlaces(2).toNumber())})),Za.loading&&ne("dayElecChart",ee))}catch(e){}},pe=()=>{Ha.visible=!1},ve=async()=>{Ha.submitLoading=!0;try{const a=await S(JSON.stringify(Ha.layout));Ha.submitLoading=!1,"00000"==a.status?(localStorage.setItem("plantOverviewGrid",encodeURIComponent(JSON.stringify(Ha.layout))),Ha.visible=!1,Za.loading=!1,Va.value=!0,he()):v.modal.message({content:"保存布局异常，请稍后再试",status:"info"})}catch(a){Ha.submitLoading=!1}},he=()=>{let a=JSON.parse(decodeURIComponent(localStorage.getItem("plantOverviewGrid")));a.layout&&(a=a.layout),Ha.layout=a;let e={};a.forEach((a=>{e[a.name]={x:a.x,y:a.y,w:a.w,h:a.h}}));for(let t in Za.layout)Za.layout[t]={...Za.layout[t],...e[Za.layout[t].name]};setTimeout((()=>{Za.loading=!0,Va.value=!1}),1e3)};D((()=>Za.loading),(a=>{a&&setTimeout((async()=>{await ne("dayElecChart",ee),await ne("monthElecChart",ee),await ne("plantProgressChart",ee),await ne("inverterProgressChart",ee)}),300)})),D((()=>Ya.filterText),((a,e)=>{Ba.value.filter(a)})),D(Q,((a,e)=>{Ya.sel=a,(async()=>{Va.value=!0,Ga&&(clearInterval(Ga),Ga=null),await oe(Ya.sel),await ce(Ya.sel),await ue(Ya.sel),await me(Ya.sel),await de(Ya.sel),Ga||(Ga=setInterval((async()=>{await oe(Ya.sel),await ce(Ya.sel),await ue(Ya.sel),await me(Ya.sel),await de(Ya.sel)}),3e5)),Va.value=!1})()}));const fe=i(),{isFullScreen:ge}=r(fe),ye=M((()=>!fe.sidebar.opened));D(ye,(a=>{c(aa.value,ee)}));let xe=null;return D(ge,(a=>{Va.value=!0,Za.loading=!1,xe&&(clearTimeout(xe),xe=null),xe=setTimeout((()=>{Za.loading=!0,Va.value=!1}),300)})),q((async()=>{Y.getProjectId(),Ya.sel=Y.projectId,Za.loading=!0,Va.value=!0,await oe(Ya.sel),await ce(Ya.sel),await ue(Ya.sel),await me(Ya.sel),await de(Ya.sel),Za.loading=!1,Va.value=!1,Ga=setInterval((async()=>{await oe(Ya.sel),await ce(Ya.sel),await ue(Ya.sel),await me(Ya.sel),await de(Ya.sel)}),3e5)})),W((()=>{u(aa.value),Ga&&(clearInterval(Ga),Ga=null)})),(t,l)=>{const s=a,i=$("vxe-modal"),r=e;return O(),L("div",{class:"app-container plantOverview-margin u-flex-column",ref_key:"overviewRef",ref:aa},[R(Va)?T((O(),L("div",ea,null,512)),[[r,R(Va)]]):F("",!0),J("div",ta,[la,J("div",sa,[(O(!0),L(U,null,A(R(Xa),((a,e)=>(O(),L("p",{key:e,class:B(["card-style",a.event?`cursor-pointer ${a.bgColor}`:`${a.bgColor}`]),onClick:a.event},[J("span",ra,[J("img",{src:R(_)("plantOverview",a.picName),class:"card-icon-img",alt:a.picName},null,8,na)]),J("span",oa,[J("i",ca,V(a.label)+" "+V(a.unit),1),J("i",ua,V(a.value),1)])],10,ia)))),128))])]),J("div",ma,[da,J("div",pa,[va,J("div",ha,[(O(!0),L(U,null,A(R(Qa),((a,e)=>(O(),L("p",{key:e,class:B(["card-style2",`card-${a.color}`]),onClick:a.event},[J("span",ga,["告警"!==a.label?(O(),L("svg",{key:0,class:B(["icon status-icon",a.color]),"aria-hidden":"true"},[J("use",{"xlink:href":a.icon},null,8,ya)],2)):(O(),L("img",{key:1,src:R(_)("plantOverview","icon_alarm.png"),class:"icon-alarmNew",alt:"icon_alarm"},null,8,xa)),J("i",wa,V(a.label),1)]),J("span",ba,V(a.value),1)],10,fa)))),128))])])]),J("div",ja,[Ca,J("div",ka,[J("div",Na,[J("p",Ea,[Pa,G("  "),J("span",null,V(R(Ka)),1)]),Sa]),_a])]),J("div",Ia,[za,J("div",Da,[Ma,J("div",qa,[(O(!0),L(U,null,A(R(ae),((a,e)=>(O(),L("p",{key:e,class:B(["card-style2",`card-${a.color}`]),onClick:a.event},[J("span",$a,["告警"!==a.label?(O(),L("svg",{key:0,class:B(["icon status-icon",a.color]),"aria-hidden":"true"},[J("use",{"xlink:href":a.icon},null,8,Oa)],2)):(O(),L("img",{key:1,src:R(_)("plantOverview","icon_alarm.png"),class:"icon-alarmNew",alt:"icon_alarm"},null,8,La)),J("i",Ra,V(a.label),1)]),J("span",Ta,V(a.value),1)],10,Wa)))),128))])])]),Z(i,{modelValue:R(Ha).visible,"onUpdate:modelValue":l[1]||(l[1]=a=>R(Ha).visible=a),title:"自定义布局",width:"1200","min-width":"400","min-height":"400",height:"600",loading:R(Ha).submitLoading,resize:"","destroy-on-close":"",zIndex:2008},{default:H((()=>[J("div",Fa,[J("div",Ja,[Z(R(n),{layout:R(Ha).layout,"onUpdate:layout":l[0]||(l[0]=a=>R(Ha).layout=a),"col-num":2,"row-height":200,"max-rows":10,"is-resizable":!1,style:{width:"100%",height:"100%"}},{default:H((({gridItemProps:a})=>[(O(!0),L(U,null,A(R(Ha).layout,(e=>(O(),K(R(o),X({key:e.i},a,{x:e.x,y:e.y,w:e.w,h:e.h,i:e.i}),{default:H((()=>[J("div",Ua,[J("p",null,V(e.text),1)])])),_:2},1040,["x","y","w","h","i"])))),128))])),_:1},8,["layout"])]),J("div",Aa,[Z(s,{onClick:pe},{default:H((()=>[G("取消")])),_:1}),Z(s,{type:"primary",onClick:ve},{default:H((()=>[G("保存")])),_:1})])])])),_:1},8,["modelValue","loading"])],512)}}},[["__scopeId","data-v-543b11da"]]);export{Ba as default};
