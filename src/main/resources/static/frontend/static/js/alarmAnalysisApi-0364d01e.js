import{a}from"./api-360ec627.js";const e=(e="alarm",r=1,t=10,s="",m="",l="",n="",o="",p="",i="")=>a("/alarm/inverterAlarm/getInverterAlarmInfo",{},{currentPage:r,pageSize:t,startTime:s,endTime:m,alarmType:e,alarmMean:n,plantName:l,alarmLevel:o,alarmStatus:p,plantUid:i,order:"status",isAsc:!0},"post"),r=(e=1,r=10,t,s,m,l,n="")=>a("/alarm/operatorAlarm/getOperatorAlarmInfo",{},{currentPage:e,pageSize:r,startTime:t,endTime:s,plantName:m,address:l,plantUid:n,order:"status",isAsc:!0},"post"),t=(e,r,t,s,m,l="")=>a("/alarm/export/getOperatorAlarmInfo",{},{startTime:r,endTime:t,plantName:s,address:m,plantUid:l,columnsList:e},"post","blob");export{e as X,r as a,t as b};
