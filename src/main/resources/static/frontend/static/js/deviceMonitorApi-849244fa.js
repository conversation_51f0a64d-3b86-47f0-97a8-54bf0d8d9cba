import{a as e}from"./api-360ec627.js";const a=a=>e("/system/plantManage/plantDetail/"+a),t=a=>e("/system/deviceManage/inverterDetails/"+a),s=(a,t)=>e("/system/deviceManage/inverterInfoChart/"+t,{date:a},{},"get"),r=(a,t,s,r)=>e("/device/deviceId/realTime",{},{currentPage:a,pageSize:t,inverterSN:s,date:r},"post"),i=(a,t)=>e("/system/deviceManage/inverterChartInfo/"+a,{date:t}),n=a=>e("/system/deviceManage/EdgeServerSIMCards",{plantUid:a}),d=a=>e("/system/deviceManage/getPvNums/"+a),g=a=>e("/system/deviceManage/operator/getPointStatus/"+a);export{t as X,s as a,a as b,d as c,i as d,g as e,n as f,r as g};
