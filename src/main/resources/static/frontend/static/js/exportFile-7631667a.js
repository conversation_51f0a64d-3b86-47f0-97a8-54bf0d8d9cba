import{d as e}from"./dayjs-d60cc07f.js";import{m as o}from"./notification-950a5f80.js";function n(n,t){let a=new Blob([n]),i=t+e().format("YYYY-MM-DD HHmmss")+".xlsx";if(window.navigator&&window.navigator.msSaveOrOpenBlob)window.navigator.msSaveOrOpenBlob(a,i);else{let e=window.URL.createObjectURL(a),o=document.createElement("a");o.style.display="none",o.href=e,o.download=i,document.body.appendChild(o),o.click(),document.body.removeChild(o),window.URL.revokeObjectURL(e)}o.success("导出成功！")}export{n as e};
