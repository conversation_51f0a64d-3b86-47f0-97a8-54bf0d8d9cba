import{b as e,a as t,O as a,f as l,G as o,P as i,d as s,e as r,K as n,x as p,D as m,F as c,c as u,R as d}from"./element-plus-d975be09.js";import{F as f}from"./quasar-b3f06d8a.js";import"./vue-5bfa3a54.js";import{a as j,X as b,b as w,c as v,d as g}from"./systemSettingApi-af083d35.js";import{r as h}from"./icons-95011f8c.js";import{l as y}from"./lodash-6d99edc3.js";import{g as k}from"./api-b858041e.js";import{m as x}from"./notification-950a5f80.js";import{L as _}from"./ui-d5488bf7.js";import{_ as V}from"./index-8cc8d4b8.js";import{h as z,j as C,m as T,az as D,o as S,c as P,a as U,a9 as q,x as B,a8 as F,b as A,aa as R,t as I,f as L,l as M,F as N,k as W,r as E,C as G,D as K}from"./@vue-5e5cdef9.js";import{n as O}from"./@vicons-f32a0bdb.js";import{N as Q}from"./naive-ui-0ee0b8c3.js";import"./lodash-es-ea7deab5.js";import"./@vueuse-af86c621.js";import"./vue-router-6159329f.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./dayjs-d60cc07f.js";import"./@babel-f3c0a00c.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./menuStore-26f8ddd8.js";import"./@x-ui-vue3-df3ba55b.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./xe-utils-fe99d42a.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";/* empty css                *//* empty css                    */import"./vxe-table-plugin-element-7bbbb435.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./date-fns-tz-0cc073c8.js";const X=e=>(G("data-v-0f73a6ff"),e=e(),K(),e),Z={class:"q-pa-md mainBox flex flex-col overflow-hidden"},$={class:"tw-h-[47px] header tw-pt-2 tw-px-4 fontWhite main tw-w-full tw-min-w-[1280]"},H={colspan:"1"},J=X((()=>U("h6",{class:"tw-text-2xl tw-my-0 text-white text-left"},"菜单管理",-1))),Y=X((()=>U("div",null,null,-1))),ee={class:"tw-w-full tw-inline-flex tw-justify-center"},te={class:"icon-popover"},ae=["onClick"],le={class:"dialog-footer"},oe=V({__name:"manuManage",setup(V){const G=j;let K=z();const X=O,oe=y._.transform(O,((e,t,a)=>{const l={icon:h(t),title:t};e.push(l)}),[]).slice();let ie=C({dialogVisible:!1,dialogTitle:"",form:{},tableData:[],menuProps:{value:"id",emitPath:!1,checkStrictly:!0}});const se=["菜单","按钮","接口"],re=[{label:"菜单",value:"0"},{label:"按钮",value:"1"},{label:"接口",value:"2"}];function ne(){K&&K.value.validate((async e=>{if(!e)return!1;ie.dialogTitle.includes("新增")?await async function(){try{const e=await v(ie.form.pid||"0",ie.form.label,ie.form.icon,ie.form.path,null,"","0",ie.form.type),t=k(e);"00000"===t.status?(x.success("新增操作成功"),ie.dialogVisible=!1,pe()):x.error(t.message)}catch(e){x.error("新增操作接口请求出错")}}():await async function(){try{const e=await g(ie.form.id,ie.form.pid,ie.form.label,ie.form.icon,ie.form.path,ie.form.type),t=k(e);"00000"===t.status?(x.success("编辑操作成功"),ie.dialogVisible=!1,pe()):x.error(t.message)}catch(e){x.error("编辑操作接口请求出错")}}()}))}async function pe(){me.set(!0);try{const e=await w(),t=k(e);ie.tableData=t.data}catch(e){}me.set(!1)}const me=new _;return T((async()=>{me.set(!1),await pe()})),(j,w)=>{const v=f,g=a,h=Q,y=l,x=o,_=i,V=s,z=r,C=n,T=p,O=m,me=c,ce=u,ue=d,de=D("skeleton-item"),fe=D("x"),je=D("g");return S(),P("div",Z,[U("div",$,[U("div",H,[q((S(),P("section",null,[J,Y,q(B(v,{class:"tw-bg-blue-500 tw-text-white",label:"新增","no-caps":"",dense:"",onClick:w[0]||(w[0]=e=>(ie.dialogVisible=!0,ie.dialogTitle="新增菜单",void(ie.form={icon:"Accessibility"})))},null,512),[[de]])])),[[fe,[3,17,1]],[je,10]])])]),B(_,{data:A(ie).tableData,"row-key":"id",border:"",class:"tw-w-full tw-h-[93%]"},{default:F((()=>[B(g,{prop:"label",label:"菜单名称",align:"center"}),B(g,{prop:"icon",width:"100",label:"菜单图标",align:"center"},{default:F((e=>[B(h,{size:"18",component:A(X)[e.row.icon]},null,8,["component"])])),_:1}),B(g,{prop:"type",label:"菜单类型",align:"center",width:"100"},{default:F((e=>[R(I(se[e.row.type]||""),1)])),_:1}),B(g,{prop:"sort",width:"100",label:"排序",align:"center"}),B(g,{prop:"path",width:"400",label:"菜单路径",align:"center"}),B(g,{prop:"createTime",label:"创建时间",align:"center"}),B(g,{label:"操作",width:"100",align:"center"},{default:F((a=>[U("div",ee,[B(x,{effect:"dark",content:"编辑",placement:"top"},{default:F((()=>[B(y,{icon:A(X).PencilSharp,class:"operation-button",plain:"",onClick:e=>{return t=a.row,ie.form=t,ie.dialogVisible=!0,void(ie.dialogTitle="编辑菜单");var t}},null,8,["icon","onClick"])])),_:2},1024),B(x,{effect:"dark",content:"删除",placement:"top"},{default:F((()=>[B(y,{icon:A(X).TrashSharp,class:"operation-button",plain:"",onClick:l=>{return o=a.row.id,void e.confirm("是否要删除该菜单",{confirmButtonText:"确定",cancelButtonText:"取消"}).then((async()=>{try{const e=await b(o);"00000"===k(e).status&&(t.success("菜单删除成功"),await pe())}catch(e){}})).catch((()=>{}));var o}},null,8,["icon","onClick"])])),_:2},1024)])])),_:1})])),_:1},8,["data"]),B(ue,{modelValue:A(ie).dialogVisible,"onUpdate:modelValue":w[7]||(w[7]=e=>A(ie).dialogVisible=e),title:A(ie).dialogTitle,width:"35%","z-index":1e3},{footer:F((()=>[U("span",le,[B(y,{type:"primary",plain:"",onClick:w[5]||(w[5]=e=>A(ie).dialogVisible=!1)},{default:F((()=>[R("取消")])),_:1}),B(y,{type:"primary",onClick:w[6]||(w[6]=e=>ne(A(K)))},{default:F((()=>[R("确定")])),_:1})])])),default:F((()=>[B(ce,{ref_key:"ruleFormRef",ref:K,model:A(ie).form,"label-width":"150px",size:"large",rules:A(G)},{default:F((()=>[B(z,{label:"菜单名称",prop:"label"},{default:F((()=>[B(V,{modelValue:A(ie).form.label,"onUpdate:modelValue":w[1]||(w[1]=e=>A(ie).form.label=e)},null,8,["modelValue"])])),_:1}),A(ie).dialogTitle.includes("新增")?(S(),L(z,{key:0,label:"新增下级菜单",prop:"projectPID"},{default:F((()=>[B(C,{clearable:"",class:"tw-w-full",placeholder:"不选择即为新增菜单级别",modelValue:A(ie).form.pid,"onUpdate:modelValue":w[2]||(w[2]=e=>A(ie).form.pid=e),props:A(ie).menuProps,options:A(ie).tableData},null,8,["modelValue","props","options"])])),_:1})):M("",!0),B(z,{label:"菜单图标",prop:"icon"},{default:F((()=>[B(h,{size:"30",component:A(X)[A(ie).form.icon]},null,8,["component"]),B(T,{placement:"bottom",width:500,trigger:"click"},{reference:F((()=>[q(B(v,{class:"tw-bg-blue-500 tw-text-white tw-ml-5",label:"选择图标","no-caps":"",dense:""},null,512),[[de]])])),default:F((()=>[U("div",te,[(S(!0),P(N,null,W(A(oe),(e=>(S(),P("div",{class:"icon tw-inline-block",key:e.title,onClick:t=>{return a=e.title,void(ie.form.icon=a.name);var a}},[(S(),L(E(e.icon),{class:"tw-inline-block"}))],8,ae)))),128))])])),_:1})])),_:1}),B(z,{label:"菜单类型",prop:"type"},{default:F((()=>[B(me,{class:"tw-w-full",modelValue:A(ie).form.type,"onUpdate:modelValue":w[3]||(w[3]=e=>A(ie).form.type=e),placeholder:"菜单类型",clearable:""},{default:F((()=>[(S(),P(N,null,W(re,(e=>B(O,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),B(z,{label:"菜单路径",prop:"path"},{default:F((()=>[B(V,{modelValue:A(ie).form.path,"onUpdate:modelValue":w[4]||(w[4]=e=>A(ie).form.path=e)},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue","title"])])}}},[["__scopeId","data-v-0f73a6ff"]]);export{oe as default};
