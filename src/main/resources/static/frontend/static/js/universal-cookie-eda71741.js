import{p as t,s as e}from"./cookie-b9a095b7.js";function o(t,e){void 0===e&&(e={});var o=function(t){if(t&&"j"===t[0]&&":"===t[1])return t.substr(2);return t}(t);if(function(t,e){return void 0===e&&(e=!t||"{"!==t[0]&&"["!==t[0]&&'"'!==t[0]),!e}(o,e.doNotParse))try{return JSON.parse(o)}catch(i){}return t}var i=globalThis&&globalThis.__assign||function(){return i=Object.assign||function(t){for(var e,o=1,i=arguments.length;o<i;o++)for(var n in e=arguments[o])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t},i.apply(this,arguments)};const n=function(){function n(e,o){var i=this;this.changeListeners=[],this.HAS_DOCUMENT_COOKIE=!1,this.cookies=function(e,o){return"string"==typeof e?t(e,o):"object"==typeof e&&null!==e?e:{}}(e,o),new Promise((function(){i.HAS_DOCUMENT_COOKIE="object"==typeof document&&"string"==typeof document.cookie})).catch((function(){}))}return n.prototype._updateBrowserValues=function(e){this.HAS_DOCUMENT_COOKIE&&(this.cookies=t(document.cookie,e))},n.prototype._emitChange=function(t){for(var e=0;e<this.changeListeners.length;++e)this.changeListeners[e](t)},n.prototype.get=function(t,e,i){return void 0===e&&(e={}),this._updateBrowserValues(i),o(this.cookies[t],e)},n.prototype.getAll=function(t,e){void 0===t&&(t={}),this._updateBrowserValues(e);var i={};for(var n in this.cookies)i[n]=o(this.cookies[n],t);return i},n.prototype.set=function(t,o,n){var s;"object"==typeof o&&(o=JSON.stringify(o)),this.cookies=i(i({},this.cookies),((s={})[t]=o,s)),this.HAS_DOCUMENT_COOKIE&&(document.cookie=e(t,o,n)),this._emitChange({name:t,value:o,options:n})},n.prototype.remove=function(t,o){var n=o=i(i({},o),{expires:new Date(1970,1,1,0,0,1),maxAge:0});this.cookies=i({},this.cookies),delete this.cookies[t],this.HAS_DOCUMENT_COOKIE&&(document.cookie=e(t,"",n)),this._emitChange({name:t,value:void 0,options:o})},n.prototype.addChangeListener=function(t){this.changeListeners.push(t)},n.prototype.removeChangeListener=function(t){var e=this.changeListeners.indexOf(t);e>=0&&this.changeListeners.splice(e,1)},n}();export{n as C};
