import{v as e}from"./@vueuse-af86c621.js";import{r as t,f as a,v as i,k as l,u as s,d as o,B as n}from"./element-plus-d975be09.js";import"./vue-5bfa3a54.js";import{M as r}from"./@element-plus-4c34063a.js";import{u as d}from"./vue-router-6159329f.js";import{a as c}from"./vxe-table-3a25f2d2.js";import{X as p}from"./xe-utils-fe99d42a.js";import{a as m,b as u,g as f}from"./index-18c14ee1.js";import{f as h,d as v,c as g}from"./chartResize-3e3d11d7.js";import{d as b}from"./dayjs-d60cc07f.js";import{i as y}from"./echarts-f30da64f.js";import{h as x,j as w,m as L,p as j,as as V,o as P,c as k,a as S,t as $,b as C,x as N,a8 as z,aa as A,F as E,k as D,y as _,f as T,a6 as O,a9 as U,ak as W,l as H,B as M,C as R,D as Y}from"./@vue-5e5cdef9.js";import{_ as I}from"./index-8cc8d4b8.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./lodash-es-ea7deab5.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./@babel-f3c0a00c.js";import"./dom-zindex-5f662ad1.js";import"./element-resize-detector-0d37a2ab.js";import"./batch-processor-06abf2b4.js";import"./lodash-6d99edc3.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./nprogress-e136a1b4.js";import"./quasar-b3f06d8a.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-7bbbb435.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";const q=e=>(R("data-v-edcf7bfd"),e=e(),Y(),e),B={class:"station-sel flex items-center justify-start u-gap-10"},F=q((()=>S("span",{class:"u-flex-y-center"},[S("i",{class:"square"}),S("i",{class:"small-title"},"当前电站")],-1))),G={class:"body-text"},J={class:"small-title u-flex-1 text-right"},X={class:"inverter-list"},K={class:"scrollbar-flex-content"},Z=["onClick"],Q={class:"table-btn"},ee={key:1,class:"chart-box","element-loading-text":"正在生成图表"},te={class:"table-btn"},ae=M('<div class="u-flex-column chart-power-item" data-v-edcf7bfd><p class="chart-title small-title u-flex-y-center" data-v-edcf7bfd>日功率</p><figure id="powerChart" class="u-flex-1" data-v-edcf7bfd></figure></div><div class="u-flex-column chart-today-item" data-v-edcf7bfd><p class="chart-title small-title u-flex-y-center" data-v-edcf7bfd>日发电量</p><figure id="todayChart" class="u-flex-1" data-v-edcf7bfd></figure></div>',2),ie={class:"u-flex-column chart-PV-item"},le={class:"chart-title small-title u-flex-y-center justify-between"},se=q((()=>S("span",null,"直流输入",-1))),oe=q((()=>S("figure",{id:"pvChart",class:"u-flex-1"},null,-1))),ne={class:"u-flex-column chart-L-item"},re={class:"chart-title small-title u-flex-y-center justify-between"},de=q((()=>S("span",null,"交流输出",-1))),ce=q((()=>S("figure",{id:"LChart",class:"u-flex-1"},null,-1))),pe={class:"station-list"},me={class:"infinite-list",style:{overflow:"auto"}},ue={key:0,class:"infinite-list-empty supplementary-text text-center"},fe=["onClick"],he=I(Object.assign({name:"realtime"},{__name:"index",setup(M){const R=d(),Y=x(),I=x(),q=x();x(!0);const he=x("选择电站"),ve=x(!1),ge=w({show:!1,loading:!0,opt:{title:{show:!1,text:"",textStyle:{fontSize:h(16)}},legend:{show:!0,bottom:"2%",selected:{}},tooltip:{show:!0,trigger:"axis",confine:!0,formatter:null},grid:{left:"10%",right:"10%",top:"20%",bottom:"25%"},xAxis:{data:[]},yAxis:{type:"value",axisLine:{show:!0},axisTick:{show:!0},splitLine:{show:!1}},series:[]},originData:{},PVSel:[],LSel:[],chartTem:{time:[],PV:{pv1:{name:"直流输入",data:[]}},L:{L1:{name:"交流输出",data:[]}},power:{name:"功率(W)",data:[]},todayElec:{name:"日发电量(kWh)",data:[]},totalElec:{name:"总发电量(kWh)",data:[]}},pvSelected:[],Lselected:[],pvOpt:{},LOpt:{}}),be={powerChart:null,todayChart:null,pvChart:null,LChart:null},ye=w({first:!0,plantUid:"",data:[],page:{currentPage:1,pageSize:50}}),xe=w({type:"station",visible:!1,stationData:[],submitLoading:!1,list:{plantUid:"",plantName:"",pageSize:15,currentPage:1}}),we=w({condition:{inverterSN:"",date:b().format("YYYY-MM-DD")},modelData:{},tablePage:{totalResult:0,currentPage:1,pageSize:15}}),Le=w({id:"realTime",border:!0,showFooter:!1,minHeight:500,height:"96%",maxHeight:860,loading:!1,autoResize:!0,editConfig:{trigger:"click",mode:"cell"},scrollX:{enabled:!1},scrollY:{enabled:!1},sortConfig:{sortMethod:({sortList:e})=>{let t={};e.forEach((e=>{t[e.field]=e.order}))}},data:[],customConfig:{storage:{visible:!0,fixed:!0}},toolbarConfig:{custom:!0,slots:{buttons:"toolbar_buttons",tools:"toolbar_tools"}},columns:[{title:" 时间",field:"initTime",width:120,fixed:"left",slots:{default:"time"}},{title:" 直流输入",children:[{title:"模式",field:"pvType",width:100},{title:"通道",field:"pvNum",width:100},{title:"电压",field:"vpv",width:100},{title:"电流",field:"ipv",width:100},{title:"组串",field:"pv",width:200}]},{title:"交流输出",children:[{title:"相位",field:"acNum",width:100},{title:"电压",field:"vac",width:100},{title:"电流",field:"iac",width:100},{title:"频率",field:"fac",width:100}]},{title:"功率(W)",field:"power"},{title:"当日发电量(kWh)",field:"todayElectricity"},{title:"总发电量(kWh)",field:"totalElectricity"}],spanMethod:({row:e,_rowIndex:t,column:a,visibleData:i})=>{const l=e[a.field];if(l&&["initTime","power","totalElectricity","todayElectricity"].includes(a.field)){const e=i[t-1];let s=i[t+1];if(e&&e[a.field]===l)return{rowspan:0,colspan:0};{let e=1;for(;s&&s[a.field]===l;)s=i[++e+t];if(e>1)return{rowspan:e,colspan:1}}}}}),je=async e=>{if(we.condition.inverterSN=e,ve.value=!0,Le.loading)c.modal.message({content:"正在请求数据中，请勿频繁点击",status:"info"});else{Le.loading=!0;try{const e=await m({...we.tablePage,...we.condition});if("00000"==e.status){if(ge.show)ge.originData=((e,t)=>{let a={time:[],PV:{},L:{},power:{name:"功率(W)",type:"line",smooth:!0,data:[],lineStyle:{color:"#f8b62d"},itemStyle:{color:"#f8b62d",normal:{color:"#f8b62d"}},areaStyle:{color:"#f8b62d"}},todayElec:{name:"日发电量(kWh)",type:"bar",data:[],lineStyle:{color:"#f8b62d"},itemStyle:{color:"#f8b62d",normal:{color:"#f8b62d"}},areaStyle:{color:"#f8b62d"}},totalElec:{name:"总发电量(kWh)",type:"bar",data:[],lineStyle:{color:"#f8b62d"},itemStyle:{color:"#f8b62d",normal:{color:"#f8b62d"}},areaStyle:{color:"#f8b62d"}}};return e.forEach((e=>{a.time.push(e.initTime.substr(11,5)),a.power.data.push(e.power),a.todayElec.data.push(e.todayElectricity),a.totalElec.data.push(e.totalElectricity);for(let i=1;i<t+1;i++)a.PV[`PV${i}电压(V)`]||(ge.PVSel.length<=t&&ge.PVSel.push({name:[`PV${i}`],value:`PV${i}`,children:[{name:`PV${i}电压(V)`,value:`PV${i}电压(V)`},{name:`PV${i}电流(A)`,value:`PV${i}电流(A)`}]}),a.PV[`PV${i}电压(V)`]={name:`PV${i}电压(V)`,type:"line",smooth:!0,data:[]},a.PV[`PV${i}电流(A)`]={name:`PV${i}电流(A)`,type:"line",data:[],smooth:!0,yAxisIndex:1}),a.L[`L${i}电压(V)`]||(ge.LSel.length<=t&&ge.LSel.push({name:[`L${i}`],value:`L${i}`,children:[{name:`L${i}电压(V)`,value:`L${i}电压(V)`},{name:`L${i}电流(A)`,value:`L${i}电流(A)`},{name:`L${i}频率(Hz)`,value:`L${i}频率(Hz)`}]}),a.L[`L${i}电压(V)`]={name:`L${i}电压(V)`,type:"line",smooth:!0,data:[]},a.L[`L${i}电流(A)`]={name:`L${i}电流(A)`,type:"line",smooth:!0,data:[],yAxisIndex:1},a.L[`L${i}频率(Hz)`]={name:`L${i}频率(Hz)`,type:"line",smooth:!0,data:[],yAxisIndex:2}),a.PV[`PV${i}电压(V)`].data.push(e[`vpv${i}`]),a.PV[`PV${i}电流(A)`].data.push(e[`ipv${i}`]),a.L[`L${i}电压(V)`].data.push(e[`vac${i}`]),a.L[`L${i}电流(A)`].data.push(e[`iac${i}`]),a.L[`L${i}频率(Hz)`].data.push(e[`fac${i}`])})),a})(e.data.records.reverse(),e.data.pvNum),Ae(ge.originData,be);else{we.tablePage.totalResult=e.data.total;let t=p.clone(e.data.records,!0);Le.data=((e,t)=>{let a=[];return e.forEach((e=>{for(let i=1;i<t+1;i++){let t={};t.initTime=e.initTime,t.pvType="独立模式",t.pvNum=`PV${i}`,t.vpv=e[`vpv${i}`],t.ipv=e[`ipv${i}`],t.pv=e[`pv${i}`],t.acNum=`L${i}`,t.vac=e[`vac${i}`],t.iac=e[`iac${i}`],t.fac=e[`fac${i}`],t.power=e.power,t.todayElectricity=e.todayElectricity,t.totalElectricity=e.totalElectricity,a.push(t)}})),a})(t,e.data.pvNum)}Le.loading=!1}else Le.data=[],we.tablePage.totalResult=0,Le.loading=!1,ge.show&&(ge.loading=!1,Ae(ge.chartTem,be))}catch(t){Le.data=[],we.tablePage.totalResult=0,Le.loading=!1}finally{ve.value=!1}}},Ve=async(e,t)=>{xe.submitLoading=!0;try{const a=await u({plantUid:e,...ye.page});xe.submitLoading=!1,he.value=t,xe.visible=!1,"00000"==a.status&&(we.tablePage={totalResult:0,currentPage:1,pageSize:20},ye.data=a.data.records,we.condition.inverterSN=a.data.records[0].inverterSN,je(we.condition.inverterSN))}catch(a){xe.submitLoading=!1}},Pe=()=>{xe.list.currentPage+=1,ke("next")},ke=async(e,t)=>{xe.list.plantUid="","reset"==e?xe.list.currentPage=1:"page"==e&&(xe.list.plantUid=t),xe.submitLoading=!0;try{const t=await f(xe.list);xe.submitLoading=!1,"00000"==t.status?(1==xe.list.currentPage?xe.stationData=t.data.records:xe.stationData=[...xe.stationData,...t.data.records],ye.first&&(he.value=xe.stationData[0].plantName,Ve(xe.stationData[0].plantUid,xe.stationData[0].plantName)),"page"==e&&(xe.list.plantName=xe.stationData[0].plantName)):xe.stationData=[]}catch(a){xe.submitLoading=!1}},Se=()=>{je(we.condition.inverterSN)},$e=async()=>{Se()},Ce=e=>e.getTime()>Date.now(),Ne=()=>{},ze=(e,t)=>{let a=document.getElementById(e),i=y(a);return i.setOption(t),i},Ae=(e,t)=>{let a;ge.loading=!0;for(let i in t){switch(a=JSON.parse(JSON.stringify(ge.opt)),a.xAxis.data=e.time,i){case"todayChart":a.title.text="日发电量(kWh)",a.yAxis.name="kWh",a.series.push(e.todayElec);break;case"totalChart":a.title.text="总发电量(kWh)",a.yAxis.name="kWh",a.series.push(e.totalElec);break;case"powerChart":a.title.text="日功率(W)",a.yAxis.name="W",a.series.push(e.power);break;case"pvChart":a.title.text="直流输入",ge.pvSelected=[],Object.keys(e.PV).forEach((e=>{e.indexOf("V1电")>-1?(a.legend.selected[`${e}`]=!0,ge.pvSelected.push(e)):a.legend.selected[`${e}`]=!1})),a.yAxis=[{type:"value",name:"电压(V)",axisLine:{show:!0},axisTick:{show:!0},splitLine:{show:!1}},{type:"value",name:"电流(A)",axisLine:{show:!0},axisTick:{show:!0},splitLine:{show:!1}}],a.series=Object.values(e.PV),a.legend.show=!1,ge.pvOpt=p.clone(a,!0);break;case"LChart":a.title.text="交流输出",ge.Lselected=[],Object.keys(e.L).forEach((e=>{e.indexOf("L1电")>-1||e.indexOf("L1频")>-1?(a.legend.selected[`${e}`]=!0,ge.Lselected.push(e)):a.legend.selected[`${e}`]=!1})),a.yAxis=[{type:"value",name:"电压(V)",axisLine:{show:!0},axisTick:{show:!0},splitLine:{show:!1}},{type:"value",name:"电流(A)",axisLine:{show:!0},axisTick:{show:!0},splitLine:{show:!1}},{type:"value",name:"频率(Hz)",offset:h(40),axisLine:{show:!0},axisTick:{show:!0},splitLine:{show:!1}}],a.series=Object.values(e.L),a.legend.show=!1,ge.LOpt=p.clone(a,!0)}t[i]&&(t[i].dispose(),v(q.value)),t[i]=ze(i,a)}setTimeout((()=>{ge.loading=!1}),1e3),g(q.value,t)},Ee=e=>{for(let t in e)e[t]&&e[t].dispose()},De=async e=>{"chart"==e?(ge.show=!0,we.tablePage={currentPage:1,pageSize:100},await je(we.condition.inverterSN)):(ge.PVSel=[],ge.LSel=[],ge.pvSelected=[],ge.Lselected=[],Ee(be),we.tablePage={currentPage:1,pageSize:15},v(q.value),ge.loading=!0,ge.show=!1,await je(we.condition.inverterSN))},_e=(e,t)=>{if(!t)if("PV"==e){let e=p.clone(ge.pvOpt,!0),t=ge.pvSelected.join("-");for(let a in e.legend.selected)t.indexOf(a)>-1?e.legend.selected[a]=!0:e.legend.selected[a]=!1;be.pvChart.dispose(),be.pvChart=ze("pvChart",e)}else{let e=p.clone(ge.LOpt,!0),t=ge.Lselected.join("-");for(let a in e.legend.selected)t.indexOf(a)>-1?e.legend.selected[a]=!0:e.legend.selected[a]=!1;be.LChart.dispose(),be.LChart=ze("LChart",e)}},Te=async e=>{await je(we.condition.inverterSN)};return L((async()=>{R.query.plantUid&&""!=R.query.plantUid?await ke("page",R.query.plantUid):await ke("reset"),ye.first=!1})),j((()=>{v(q.value),Ee(be)})),(d,c)=>{const p=V("CaretBottom"),m=t,u=a,f=i,h=l,v=V("vxe-button"),g=V("vxe-pager"),b=V("vxe-grid"),y=s,x=o,w=V("vxe-modal"),L=n,j=e;return P(),k("div",{class:"app-container",ref_key:"appContainerRef",ref:Y},[S("div",B,[S("p",{class:"plant-sel h-full u-flex-center u-gap-10 shadow-md body-text justify-between",onClick:c[0]||(c[0]=e=>{return t="station",xe.type=t,void(xe.visible=!0);var t})},[F,S("span",G,$(C(he)),1),N(m,{class:"sel-btn",size:20},{default:z((()=>[N(p)])),_:1})]),S("span",J,[N(u,{type:"primary",onClick:$e,loading:C(ve)},{default:z((()=>[A("刷新")])),_:1},8,["loading"]),A("  "),N(f,{modelValue:C(we).condition.date,"onUpdate:modelValue":c[1]||(c[1]=e=>C(we).condition.date=e),disabled:C(Le).loading,type:"date","value-format":"YYYY-MM-DD",onChange:Se,"disabled-date":Ce},null,8,["modelValue","disabled"])])]),S("div",X,[N(h,null,{default:z((()=>[S("div",K,[(P(!0),k(E,null,D(C(ye).data,((e,t)=>(P(),k("p",{class:_(["inverter-list-item h-full body-text u-flex-center",C(we).condition.inverterSN==e.inverterSN?"is-active":""]),onClick:t=>je(e.inverterSN)},$(e.inverterSN),11,Z)))),256))])])),_:1})]),C(ge).show?U((P(),k("div",ee,[S("div",te,[N(v,{status:"primary",onClick:c[5]||(c[5]=e=>De("table"))},{default:z((()=>[A("表格")])),_:1})]),S("div",{ref_key:"gatherChart",ref:q,id:"chartGather"},[ae,S("div",ie,[S("p",le,[se,N(y,{modelValue:C(ge).pvSelected,"onUpdate:modelValue":c[6]||(c[6]=e=>C(ge).pvSelected=e),data:C(ge).PVSel,"render-after-expand":!1,"show-checkbox":"",multiple:"","collapse-tags":"",onVisibleChange:c[7]||(c[7]=e=>_e("PV",e))},null,8,["modelValue","data"])]),oe]),S("div",ne,[S("p",re,[de,N(y,{modelValue:C(ge).Lselected,"onUpdate:modelValue":c[8]||(c[8]=e=>C(ge).Lselected=e),data:C(ge).LSel,"render-after-expand":!1,"show-checkbox":"",multiple:"","collapse-tags":"",onVisibleChange:c[9]||(c[9]=e=>_e("L",e))},null,8,["modelValue","data"])]),ce])],512)])),[[L,C(ge).loading]]):(P(),T(b,O({key:0,ref_key:"xGrid",ref:I,class:"my-grid66"},C(Le)),{toolbar_buttons:z((()=>[])),toolbar_tools:z((()=>[S("div",Q,[N(v,{status:"warning",icon:"vxe-icon-chart-line",onClick:c[2]||(c[2]=e=>De("chart"))})])])),time:z((({row:e})=>[S("span",null,$(e.initTime.substr(11,5)),1)])),"row-operate":z((({row:e})=>[N(u,{link:"",type:"primary",onClick:t=>d.seeTableItem(e)},{default:z((()=>[A("查看详情")])),_:2},1032,["onClick"])])),bottom:z((()=>[])),pager:z((()=>[N(g,{perfect:"","current-page":C(we).tablePage.currentPage,"onUpdate:currentPage":c[3]||(c[3]=e=>C(we).tablePage.currentPage=e),"page-size":C(we).tablePage.pageSize,"onUpdate:pageSize":c[4]||(c[4]=e=>C(we).tablePage.pageSize=e),total:C(we).tablePage.totalResult,onPageChange:Te},null,8,["current-page","page-size","total"])])),_:1},16)),N(w,{modelValue:C(xe).visible,"onUpdate:modelValue":c[13]||(c[13]=e=>C(xe).visible=e),title:"电站选择",width:"500","min-width":"400","min-height":"100",loading:C(xe).submitLoading,resize:"","destroy-on-close":"",onHide:Ne},{default:z((()=>[S("div",pe,[N(x,{modelValue:C(xe).list.plantName,"onUpdate:modelValue":c[11]||(c[11]=e=>C(xe).list.plantName=e),clearable:"",onKeyup:c[12]||(c[12]=W((e=>ke("reset")),["enter"]))},{append:z((()=>[N(u,{icon:C(r),onClick:c[10]||(c[10]=e=>ke("reset"))},null,8,["icon"])])),_:1},8,["modelValue"]),U((P(),k("ul",me,[0==C(xe).stationData.length?(P(),k("li",ue,"暂无数据")):H("",!0),(P(!0),k(E,null,D(C(xe).stationData,(e=>(P(),k("li",{class:"infinite-list-item body-text",key:e.plantUid,onClick:t=>Ve(e.plantUid,e.plantName)},$(e.plantName),9,fe)))),128))])),[[j,Pe]])])])),_:1},8,["modelValue","loading"])],512)}}}),[["__scopeId","data-v-edcf7bfd"]]);export{he as default};
