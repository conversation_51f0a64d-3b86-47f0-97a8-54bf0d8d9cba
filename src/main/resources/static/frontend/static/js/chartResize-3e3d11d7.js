import{e}from"./element-resize-detector-0d37a2ab.js";import{l as t}from"./lodash-6d99edc3.js";import{i as n}from"./echarts-f30da64f.js";const o=e(),i=(e,n)=>{o.listenTo(e,t.debounce((e=>{(e=>{for(let t in e)e[t]&&e[t].resize()})(n)}),300))},r=e=>{o.uninstall(e)},s=(e,t=1920)=>{let n=window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth;return n?Number((e*(n/t)).toFixed(3)):e},d=(e,t)=>{let o=document.getElementById(e),i=n(o);return i.setOption(t),i};export{i as c,r as d,s as f,d as i};
