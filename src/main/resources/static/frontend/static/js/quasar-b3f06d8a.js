import"./vue-5bfa3a54.js";import{h as e,A as t,Q as l,s as a,e as o,w as n,p as i,m as r,at as s,N as u,i as d,j as c,O as v,ap as p,ac as f,n as m,a9 as g,aj as h,aD as b,a4 as y,aH as w,aB as x,d as _,v as S,ai as k,a5 as q,H as C,ad as $,ab as M,b as T}from"./@vue-5e5cdef9.js";
/*!
 * Quasar Framework v2.15.1
 * (c) 2015-present <PERSON><PERSON><PERSON>
 * Released under the MIT License.
 */var B=Object.create,L=Object.defineProperty,z=Object.getOwnPropertyDescriptor,V=Object.getOwnPropertyNames,O=Object.getPrototypeOf,A=Object.prototype.hasOwnProperty,E=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),P=(e,t)=>{for(var l in t)L(e,l,{get:t[l],enumerable:!0})},F=(e,t,l)=>(l=null!=e?B(O(e)):{},((e,t,l,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let o of V(t))!A.call(e,o)&&o!==l&&L(e,o,{get:()=>t[o],enumerable:!(a=z(t,o))||a.enumerable});return e})(!t&&e&&e.__esModule?l:L(l,"default",{value:e,enumerable:!0}),e)),R=E(((e,t)=>{t.exports={isoName:"en-US",nativeName:"English (US)",label:{clear:"Clear",ok:"OK",cancel:"Cancel",close:"Close",set:"Set",select:"Select",reset:"Reset",remove:"Remove",update:"Update",create:"Create",search:"Search",filter:"Filter",refresh:"Refresh",expand:e=>e?`Expand "${e}"`:"Expand",collapse:e=>e?`Collapse "${e}"`:"Collapse"},date:{days:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),daysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),firstDayOfWeek:0,format24h:!1,pluralDay:"days"},table:{noData:"No data available",noResults:"No matching records found",loading:"Loading...",selectedRecords:e=>1===e?"1 record selected.":(0===e?"No":e)+" records selected.",recordsPerPage:"Records per page:",allRows:"All",pagination:(e,t,l)=>e+"-"+t+" of "+l,columns:"Columns"},editor:{url:"URL",bold:"Bold",italic:"Italic",strikethrough:"Strikethrough",underline:"Underline",unorderedList:"Unordered List",orderedList:"Ordered List",subscript:"Subscript",superscript:"Superscript",hyperlink:"Hyperlink",toggleFullscreen:"Toggle Fullscreen",quote:"Quote",left:"Left align",center:"Center align",right:"Right align",justify:"Justify align",print:"Print",outdent:"Decrease indentation",indent:"Increase indentation",removeFormat:"Remove formatting",formatting:"Formatting",fontSize:"Font Size",align:"Align",hr:"Insert Horizontal Rule",undo:"Undo",redo:"Redo",heading1:"Heading 1",heading2:"Heading 2",heading3:"Heading 3",heading4:"Heading 4",heading5:"Heading 5",heading6:"Heading 6",paragraph:"Paragraph",code:"Code",size1:"Very small",size2:"A bit small",size3:"Normal",size4:"Medium-large",size5:"Big",size6:"Very big",size7:"Maximum",defaultFont:"Default Font",viewSource:"View Source"},tree:{noNodes:"No nodes available",noResults:"No matching nodes found"}}})),N=E(((e,t)=>{t.exports={name:"material-icons",type:{positive:"check_circle",negative:"warning",info:"info",warning:"priority_high"},arrow:{up:"arrow_upward",right:"arrow_forward",down:"arrow_downward",left:"arrow_back",dropdown:"arrow_drop_down"},chevron:{left:"chevron_left",right:"chevron_right"},colorPicker:{spectrum:"gradient",tune:"tune",palette:"style"},pullToRefresh:{icon:"refresh"},carousel:{left:"chevron_left",right:"chevron_right",up:"keyboard_arrow_up",down:"keyboard_arrow_down",navigationIcon:"lens"},chip:{remove:"cancel",selected:"check"},datetime:{arrowLeft:"chevron_left",arrowRight:"chevron_right",now:"access_time",today:"today"},editor:{bold:"format_bold",italic:"format_italic",strikethrough:"strikethrough_s",underline:"format_underlined",unorderedList:"format_list_bulleted",orderedList:"format_list_numbered",subscript:"vertical_align_bottom",superscript:"vertical_align_top",hyperlink:"link",toggleFullscreen:"fullscreen",quote:"format_quote",left:"format_align_left",center:"format_align_center",right:"format_align_right",justify:"format_align_justify",print:"print",outdent:"format_indent_decrease",indent:"format_indent_increase",removeFormat:"format_clear",formatting:"text_format",fontSize:"format_size",align:"format_align_left",hr:"remove",undo:"undo",redo:"redo",heading:"format_size",code:"code",size:"format_size",font:"font_download",viewSource:"code"},expansionItem:{icon:"keyboard_arrow_down",denseIcon:"arrow_drop_down"},fab:{icon:"add",activeIcon:"close"},field:{clear:"cancel",error:"error"},pagination:{first:"first_page",prev:"keyboard_arrow_left",next:"keyboard_arrow_right",last:"last_page"},rating:{icon:"grade"},stepper:{done:"check",active:"edit",error:"warning"},tabs:{left:"chevron_left",right:"chevron_right",up:"keyboard_arrow_up",down:"keyboard_arrow_down"},table:{arrowUp:"arrow_upward",warning:"warning",firstPage:"first_page",prevPage:"chevron_left",nextPage:"chevron_right",lastPage:"last_page"},tree:{icon:"play_arrow"},uploader:{done:"done",clear:"clear",add:"add_box",upload:"cloud_upload",removeQueue:"clear_all",removeUploaded:"done_all"}}}));function H(e,t,l,a){return Object.defineProperty(e,t,{get:l,set:a,enumerable:!0}),e}function I(e,t){for(let l in t)H(e,l,t[l]);return e}var j,D=e(!1);var Q="ontouchstart"in window||window.navigator.maxTouchPoints>0;var U=navigator.userAgent||navigator.vendor||window.opera,W={userAgent:U,is:function(e){let t=e.toLowerCase(),l=function(e){return/(ipad)/.exec(e)||/(ipod)/.exec(e)||/(windows phone)/.exec(e)||/(iphone)/.exec(e)||/(kindle)/.exec(e)||/(silk)/.exec(e)||/(android)/.exec(e)||/(win)/.exec(e)||/(mac)/.exec(e)||/(linux)/.exec(e)||/(cros)/.exec(e)||/(playbook)/.exec(e)||/(bb)/.exec(e)||/(blackberry)/.exec(e)||[]}(t),a=function(e,t){let l=/(edg|edge|edga|edgios)\/([\w.]+)/.exec(e)||/(opr)[\/]([\w.]+)/.exec(e)||/(vivaldi)[\/]([\w.]+)/.exec(e)||/(chrome|crios)[\/]([\w.]+)/.exec(e)||/(version)(applewebkit)[\/]([\w.]+).*(safari)[\/]([\w.]+)/.exec(e)||/(webkit)[\/]([\w.]+).*(version)[\/]([\w.]+).*(safari)[\/]([\w.]+)/.exec(e)||/(firefox|fxios)[\/]([\w.]+)/.exec(e)||/(webkit)[\/]([\w.]+)/.exec(e)||/(opera)(?:.*version|)[\/]([\w.]+)/.exec(e)||[];return{browser:l[5]||l[3]||l[1]||"",version:l[2]||l[4]||"0",versionNumber:l[4]||l[2]||"0",platform:t[0]||""}}(t,l),o={};a.browser&&(o[a.browser]=!0,o.version=a.version,o.versionNumber=parseInt(a.versionNumber,10)),a.platform&&(o[a.platform]=!0);let n=o.android||o.ios||o.bb||o.blackberry||o.ipad||o.iphone||o.ipod||o.kindle||o.playbook||o.silk||o["windows phone"];if(!0===n||-1!==t.indexOf("mobile")?o.mobile=!0:o.desktop=!0,o["windows phone"]&&(o.winphone=!0,delete o["windows phone"]),o.edga||o.edgios?(o.edge=!0,a.browser="edge"):o.crios?(o.chrome=!0,a.browser="chrome"):o.fxios?(o.firefox=!0,a.browser="firefox"):(o.ipod||o.ipad||o.iphone)&&(o.ios=!0),o.vivaldi&&(a.browser="vivaldi",o.vivaldi=!0),(o.chrome||o.opr||o.safari||o.vivaldi||!0===o.mobile&&!0!==o.ios&&!0!==n)&&(o.webkit=!0),o.edg?(a.browser="edgechromium",o.edgeChromium=!0):o.opr&&(a.browser="opera",o.opera=!0),o.safari&&(o.blackberry||o.bb?(a.browser="blackberry",o.blackberry=!0):o.playbook?(a.browser="playbook",o.playbook=!0):o.android?(a.browser="android",o.android=!0):o.kindle?(a.browser="kindle",o.kindle=!0):o.silk&&(a.browser="silk",o.silk=!0)),o.name=a.browser,o.platform=a.platform,-1!==t.indexOf("electron"))o.electron=!0;else if(-1!==document.location.href.indexOf("-extension://"))o.bex=!0;else{if(void 0!==window.Capacitor?(o.capacitor=!0,o.nativeMobile=!0,o.nativeMobileWrapper="capacitor"):(void 0!==window._cordovaNative||void 0!==window.cordova)&&(o.cordova=!0,o.nativeMobile=!0,o.nativeMobileWrapper="cordova"),!0===D.value&&(j={is:{...o}}),!0===Q&&!0===o.mac&&(!0===o.desktop&&!0===o.safari||!0===o.nativeMobile&&!0!==o.android&&!0!==o.ios&&!0!==o.ipad)){delete o.mac,delete o.desktop;let e=Math.min(window.innerHeight,window.innerWidth)>414?"ipad":"iphone";Object.assign(o,{mobile:!0,ios:!0,platform:e,[e]:!0})}!0!==o.mobile&&window.navigator.userAgentData&&window.navigator.userAgentData.mobile&&(delete o.desktop,o.mobile=!0)}return o}(U),has:{touch:Q},within:{iframe:window.self!==window.top}},K={install(e){let{$q:t}=e;!0===D.value?(e.onSSRHydrated.push((()=>{Object.assign(t.platform,W),D.value=!1})),t.platform=c(this)):t.platform=this}};{let e;H(W.has,"webStorage",(()=>{if(void 0!==e)return e;try{if(window.localStorage)return e=!0,!0}catch{}return e=!1,!1})),Object.assign(K,W),!0===D.value&&(Object.assign(K,j,{has:{touch:!1,webStorage:!1},within:{iframe:!1}}),j=null)}var Y=K,X=(e,t)=>{let l=c(e);for(let a in e)H(t,a,(()=>l[a]),(e=>{l[a]=e}));return t},Z={hasPassive:!1,passiveCapture:!0,notPassiveCapture:!0};try{let e=Object.defineProperty({},"passive",{get(){Object.assign(Z,{hasPassive:!0,passive:{passive:!0},notPassive:{passive:!1},passiveCapture:{passive:!0,capture:!0},notPassiveCapture:{passive:!1,capture:!0}})}});window.addEventListener("qtest",null,e),window.removeEventListener("qtest",null,e)}catch{}function G(){}function J(e){return 0===e.button}function ee(e){return e.touches&&e.touches[0]?e=e.touches[0]:e.changedTouches&&e.changedTouches[0]?e=e.changedTouches[0]:e.targetTouches&&e.targetTouches[0]&&(e=e.targetTouches[0]),{top:e.clientY,left:e.clientX}}function te(e){e.stopPropagation()}function le(e){!1!==e.cancelable&&e.preventDefault()}function ae(e){!1!==e.cancelable&&e.preventDefault(),e.stopPropagation()}function oe(e,t){if(void 0===e||!0===t&&!0===e.__dragPrevented)return;let l=!0===t?e=>{e.__dragPrevented=!0,e.addEventListener("dragstart",le,Z.notPassiveCapture)}:e=>{delete e.__dragPrevented,e.removeEventListener("dragstart",le,Z.notPassiveCapture)};e.querySelectorAll("a, img").forEach(l)}function ne(e,t,l){let a=`__q_${t}_evt`;e[a]=void 0!==e[a]?e[a].concat(l):l,l.forEach((t=>{t[0].addEventListener(t[1],e[t[2]],Z[t[3]])}))}function ie(e,t){let l=`__q_${t}_evt`;void 0!==e[l]&&(e[l].forEach((t=>{t[0].removeEventListener(t[1],e[t[2]],Z[t[3]])})),e[l]=void 0)}function re(e,t=250,l){let a=null;function o(){let o=arguments;null!==a?clearTimeout(a):!0===l&&e.apply(this,o),a=setTimeout((()=>{a=null,!0!==l&&e.apply(this,o)}),t)}return o.cancel=()=>{null!==a&&clearTimeout(a)},o}var se=["sm","md","lg","xl"],{passive:ue}=Z,de=X({width:0,height:0,name:"xs",sizes:{sm:600,md:1024,lg:1440,xl:1920},lt:{sm:!0,md:!0,lg:!0,xl:!0},gt:{xs:!1,sm:!1,md:!1,lg:!1},xs:!0,sm:!1,md:!1,lg:!1,xl:!1},{setSizes:G,setDebounce:G,install({$q:e,onSSRHydrated:t}){if(e.screen=this,!0===this.__installed)return void(void 0!==e.config.screen&&(!1===e.config.screen.bodyClasses?document.body.classList.remove(`screen--${this.name}`):this.__update(!0)));let{visualViewport:l}=window,a=l||window,o=document.scrollingElement||document.documentElement,n=void 0===l||!0===W.is.mobile?()=>[Math.max(window.innerWidth,o.clientWidth),Math.max(window.innerHeight,o.clientHeight)]:()=>[l.width*l.scale+window.innerWidth-o.clientWidth,l.height*l.scale+window.innerHeight-o.clientHeight],i=void 0!==e.config.screen&&!0===e.config.screen.bodyClasses;this.__update=e=>{let[t,l]=n();if(l!==this.height&&(this.height=l),t!==this.width)this.width=t;else if(!0!==e)return;let a=this.sizes;this.gt.xs=t>=a.sm,this.gt.sm=t>=a.md,this.gt.md=t>=a.lg,this.gt.lg=t>=a.xl,this.lt.sm=t<a.sm,this.lt.md=t<a.md,this.lt.lg=t<a.lg,this.lt.xl=t<a.xl,this.xs=this.lt.sm,this.sm=!0===this.gt.xs&&!0===this.lt.md,this.md=!0===this.gt.sm&&!0===this.lt.lg,this.lg=!0===this.gt.md&&!0===this.lt.xl,this.xl=this.gt.lg,a=(!0===this.xs?"xs":!0===this.sm&&"sm")||!0===this.md&&"md"||!0===this.lg&&"lg"||"xl",a!==this.name&&(!0===i&&(document.body.classList.remove(`screen--${this.name}`),document.body.classList.add(`screen--${a}`)),this.name=a)};let r,s={},u=16;this.setSizes=e=>{se.forEach((t=>{void 0!==e[t]&&(s[t]=e[t])}))},this.setDebounce=e=>{u=e};let d=()=>{let e=getComputedStyle(document.body);e.getPropertyValue("--q-size-sm")&&se.forEach((t=>{this.sizes[t]=parseInt(e.getPropertyValue(`--q-size-${t}`),10)})),this.setSizes=e=>{se.forEach((t=>{e[t]&&(this.sizes[t]=e[t])})),this.__update(!0)},this.setDebounce=e=>{void 0!==r&&a.removeEventListener("resize",r,ue),r=e>0?re(this.__update,e):this.__update,a.addEventListener("resize",r,ue)},this.setDebounce(u),0!==Object.keys(s).length?(this.setSizes(s),s=void 0):this.__update(),!0===i&&"xs"===this.name&&document.body.classList.add("screen--xs")};!0===D.value?t.push(d):d()}}),ce=X({isActive:!1,mode:!1},{__media:void 0,set(e){ce.mode=e,"auto"===e?(void 0===ce.__media&&(ce.__media=window.matchMedia("(prefers-color-scheme: dark)"),ce.__updateMedia=()=>{ce.set("auto")},ce.__media.addListener(ce.__updateMedia)),e=ce.__media.matches):void 0!==ce.__media&&(ce.__media.removeListener(ce.__updateMedia),ce.__media=void 0),ce.isActive=!0===e,document.body.classList.remove("body--"+(!0===e?"light":"dark")),document.body.classList.add("body--"+(!0===e?"dark":"light"))},toggle(){ce.set(!1===ce.isActive)},install({$q:e,ssrContext:t}){let{dark:l}=e.config;e.dark=this,!0!==this.__installed&&this.set(void 0!==l&&l)}}),ve=ce,pe=()=>!0;function fe(e){return"string"==typeof e&&""!==e&&"/"!==e&&"#/"!==e}function me(e){return!0===e.startsWith("#")&&(e=e.substring(1)),!1===e.startsWith("/")&&(e="/"+e),!0===e.endsWith("/")&&(e=e.substring(0,e.length-1)),"#"+e}var ge={__history:[],add:G,remove:G,install({$q:e}){if(!0===this.__installed)return;let{cordova:t,capacitor:l}=W.is;if(!0!==t&&!0!==l)return;let a=e.config[!0===t?"cordova":"capacitor"];if(void 0!==a&&!1===a.backButton||!0===l&&(void 0===window.Capacitor||void 0===window.Capacitor.Plugins.App))return;this.add=e=>{void 0===e.condition&&(e.condition=pe),this.__history.push(e)},this.remove=e=>{let t=this.__history.indexOf(e);t>=0&&this.__history.splice(t,1)};let o=function(e){if(!1===e.backButtonExit)return()=>!1;if("*"===e.backButtonExit)return pe;let t=["#/"];return!0===Array.isArray(e.backButtonExit)&&t.push(...e.backButtonExit.filter(fe).map(me)),()=>t.includes(window.location.hash)}(Object.assign({backButtonExit:!0},a)),n=()=>{if(this.__history.length){let e=this.__history[this.__history.length-1];!0===e.condition()&&(this.__history.pop(),e.handler())}else!0===o()?navigator.app.exitApp():window.history.back()};!0===t?document.addEventListener("deviceready",(()=>{document.addEventListener("backbutton",n,!1)})):window.Capacitor.Plugins.App.addListener("backButton",n)}},he=F(R());function be(){let e=!0===Array.isArray(navigator.languages)&&0!==navigator.languages.length?navigator.languages[0]:navigator.language;if("string"==typeof e)return e.split(/[-_]/).map(((e,t)=>0===t?e.toLowerCase():t>1||e.length<4?e.toUpperCase():e[0].toUpperCase()+e.slice(1).toLowerCase())).join("-")}var ye=X({__langPack:{}},{getLocale:be,set(e=he.default,t){let l={...e,rtl:!0===e.rtl,getLocale:be};if(l.set=ye.set,void 0===ye.__langConfig||!0!==ye.__langConfig.noHtmlAttrs){let e=document.documentElement;e.setAttribute("dir",!0===l.rtl?"rtl":"ltr"),e.setAttribute("lang",l.isoName)}Object.assign(ye.__langPack,l),ye.props=l,ye.isoName=l.isoName,ye.nativeName=l.nativeName},install({$q:e,lang:t,ssrContext:l}){e.lang=ye.__langPack,ye.__langConfig=e.config.lang,!0===this.__installed?void 0!==t&&this.set(t):this.set(t||he.default)}}),we=ye;function xe(e,t,l=document.body){if("string"!=typeof e)throw new TypeError("Expected a string as propName");if("string"!=typeof t)throw new TypeError("Expected a string as value");if(!(l instanceof Element))throw new TypeError("Expected a DOM element");l.style.setProperty(`--q-${e}`,t)}var _e=!1;function Se(e){_e=!0===e.isComposing}function ke(e){return!0===_e||e!==Object(e)||!0===e.isComposing||!0===e.qKeyEvent}function qe(e,t){return!0!==ke(e)&&[].concat(t).includes(e.keyCode)}function Ce(e){return!0===e.ios?"ios":!0===e.android?"android":void 0}var $e={install(e){if(!0!==this.__installed){if(!0===D.value)!function(){let{is:e}=W,t=document.body.className,l=new Set(t.replace(/ {2}/g," ").split(" "));if(!0!==e.nativeMobile&&!0!==e.electron&&!0!==e.bex)if(!0===e.desktop)l.delete("mobile"),l.delete("platform-ios"),l.delete("platform-android"),l.add("desktop");else if(!0===e.mobile){l.delete("desktop"),l.add("mobile"),l.delete("platform-ios"),l.delete("platform-android");let t=Ce(e);void 0!==t&&l.add(`platform-${t}`)}!0===W.has.touch&&(l.delete("no-touch"),l.add("touch")),!0===W.within.iframe&&l.add("within-iframe");let a=Array.from(l).join(" ");t!==a&&(document.body.className=a)}();else{let{$q:t}=e;void 0!==t.config.brand&&function(e){for(let t in e)xe(t,e[t])}(t.config.brand);let l=function({is:e,has:t,within:l},a){let o=[!0===e.desktop?"desktop":"mobile",(!1===t.touch?"no-":"")+"touch"];if(!0===e.mobile){let t=Ce(e);void 0!==t&&o.push("platform-"+t)}if(!0===e.nativeMobile){let t=e.nativeMobileWrapper;o.push(t),o.push("native-mobile"),!0===e.ios&&(void 0===a[t]||!1!==a[t].iosStatusBarPadding)&&o.push("q-ios-padding")}else!0===e.electron?o.push("electron"):!0===e.bex&&o.push("bex");return!0===l.iframe&&o.push("within-iframe"),o}(W,t.config);document.body.classList.add.apply(document.body.classList,l)}!0===W.is.ios&&document.body.addEventListener("touchstart",G),window.addEventListener("keydown",Se,!0)}}},Me=F(N()),Te=X({iconMapFn:null,__icons:{}},{set(e,t){let l={...e,rtl:!0===e.rtl};l.set=Te.set,Object.assign(Te.__icons,l)},install({$q:e,iconSet:t,ssrContext:l}){void 0!==e.config.iconMapFn&&(this.iconMapFn=e.config.iconMapFn),e.iconSet=this.__icons,H(e,"iconMapFn",(()=>this.iconMapFn),(e=>{this.iconMapFn=e})),!0===this.__installed?void 0!==t&&this.set(t):this.set(t||Me.default)}}),Be=Te,Le="_q_t_",ze="_q_s_",Ve="_q_l_",Oe="_q_pc_",Ae="_q_f_",Ee="_q_fo_",Pe="_q_tabs_",Fe="_q_u_",Re=()=>{},Ne={},He=!1;function Ie(e,t){if(e===t)return!0;if(null!==e&&null!==t&&"object"==typeof e&&"object"==typeof t){if(e.constructor!==t.constructor)return!1;let l,a;if(e.constructor===Array){if(l=e.length,l!==t.length)return!1;for(a=l;0!=a--;)if(!0!==Ie(e[a],t[a]))return!1;return!0}if(e.constructor===Map){if(e.size!==t.size)return!1;let l=e.entries();for(a=l.next();!0!==a.done;){if(!0!==t.has(a.value[0]))return!1;a=l.next()}for(l=e.entries(),a=l.next();!0!==a.done;){if(!0!==Ie(a.value[1],t.get(a.value[0])))return!1;a=l.next()}return!0}if(e.constructor===Set){if(e.size!==t.size)return!1;let l=e.entries();for(a=l.next();!0!==a.done;){if(!0!==t.has(a.value[0]))return!1;a=l.next()}return!0}if(null!=e.buffer&&e.buffer.constructor===ArrayBuffer){if(l=e.length,l!==t.length)return!1;for(a=l;0!=a--;)if(e[a]!==t[a])return!1;return!0}if(e.constructor===RegExp)return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===t.toString();let o=Object.keys(e).filter((t=>void 0!==e[t]));if(l=o.length,l!==Object.keys(t).filter((e=>void 0!==t[e])).length)return!1;for(a=l;0!=a--;){let l=o[a];if(!0!==Ie(e[l],t[l]))return!1}return!0}return e!=e&&t!=t}function je(e){return null!==e&&"object"==typeof e&&!0!==Array.isArray(e)}function De(e){return"[object Date]"===Object.prototype.toString.call(e)}function Qe(e){return"number"==typeof e&&isFinite(e)}var Ue=[Y,$e,ve,de,ge,we,Be];function We(e,t){let l=x(e);l.config.globalProperties=t.config.globalProperties;let{reload:a,...o}=t._context;return Object.assign(l._context,o),l}function Ke(e,t){t.forEach((t=>{t.install(e),t.__installed=!0}))}var Ye=function(e,t={}){let l={version:"2.15.1"};!1===He?(void 0!==t.config&&Object.assign(Ne,t.config),l.config={...Ne},He=!0):l.config=t.config||{},function(e,t,l){e.config.globalProperties.$q=l.$q,e.provide("_q_",l.$q),Ke(l,Ue),void 0!==t.components&&Object.values(t.components).forEach((t=>{!0===je(t)&&void 0!==t.name&&e.component(t.name,t)})),void 0!==t.directives&&Object.values(t.directives).forEach((t=>{!0===je(t)&&void 0!==t.name&&e.directive(t.name,t)})),void 0!==t.plugins&&Ke(l,Object.values(t.plugins).filter((e=>"function"==typeof e.install&&!1===Ue.includes(e)))),!0===D.value&&(l.$q.onSSRHydrated=()=>{l.onSSRHydrated.forEach((e=>{e()})),l.$q.onSSRHydrated=()=>{}})}(e,t,{parentApp:e,$q:l,lang:t.lang,iconSet:t.iconSet,onSSRHydrated:[]})},Xe={};P(Xe,{QAjaxBar:()=>ut,QAvatar:()=>Vt,QBadge:()=>At,QBanner:()=>Ft,QBar:()=>Rt,QBreadcrumbs:()=>Xt,QBreadcrumbsEl:()=>al,QBtn:()=>$l,QBtnDropdown:()=>Xa,QBtnGroup:()=>Ml,QBtnToggle:()=>to,QCard:()=>lo,QCardActions:()=>oo,QCardSection:()=>ao,QCarousel:()=>qo,QCarouselControl:()=>$o,QCarouselSlide:()=>Co,QChatMessage:()=>Mo,QCheckbox:()=>Ao,QChip:()=>Po,QCircularProgress:()=>Ho,QColor:()=>Ln,QDate:()=>hi,QDialog:()=>Pi,QDrawer:()=>Fi,QEditor:()=>or,QExpansionItem:()=>vr,QFab:()=>br,QFabAction:()=>xr,QField:()=>Br,QFile:()=>Pr,QFooter:()=>Fr,QForm:()=>Rr,QFormChildMixin:()=>Nr,QHeader:()=>Hr,QIcon:()=>zt,QImg:()=>Dr,QInfiniteScroll:()=>Ur,QInnerLoading:()=>Wr,QInput:()=>rs,QIntersection:()=>vs,QItem:()=>Qi,QItemLabel:()=>nr,QItemSection:()=>Ui,QKnob:()=>gs,QLayout:()=>ws,QLinearProgress:()=>Us,QList:()=>ps,QMarkupTable:()=>_s,QMenu:()=>Ha,QNoSsr:()=>Ss,QOptionGroup:()=>Ts,QPage:()=>Bs,QPageContainer:()=>Ls,QPageScroller:()=>Os,QPageSticky:()=>As,QPagination:()=>Ps,QParallax:()=>Ns,QPopupEdit:()=>Is,QPopupProxy:()=>js,QPullToRefresh:()=>Ks,QRadio:()=>qs,QRange:()=>Gs,QRating:()=>Js,QResizeObserver:()=>an,QResponsive:()=>eu,QRouteTab:()=>Qd,QScrollArea:()=>nu,QScrollObserver:()=>ys,QSelect:()=>wu,QSeparator:()=>ur,QSkeleton:()=>Su,QSlideItem:()=>qu,QSlideTransition:()=>ir,QSlider:()=>Jo,QSpace:()=>$u,QSpinner:()=>il,QSpinnerAudio:()=>Tu,QSpinnerBall:()=>Lu,QSpinnerBars:()=>Vu,QSpinnerBox:()=>Au,QSpinnerClock:()=>Pu,QSpinnerComment:()=>Ru,QSpinnerCube:()=>Hu,QSpinnerDots:()=>ju,QSpinnerFacebook:()=>Qu,QSpinnerGears:()=>Wu,QSpinnerGrid:()=>Yu,QSpinnerHearts:()=>Zu,QSpinnerHourglass:()=>Ju,QSpinnerInfinity:()=>td,QSpinnerIos:()=>ad,QSpinnerOrbit:()=>nd,QSpinnerOval:()=>rd,QSpinnerPie:()=>ud,QSpinnerPuff:()=>cd,QSpinnerRadio:()=>pd,QSpinnerRings:()=>md,QSpinnerTail:()=>hd,QSplitter:()=>bd,QStep:()=>_d,QStepper:()=>kd,QStepperNavigation:()=>qd,QTab:()=>pn,QTabPanel:()=>mn,QTabPanels:()=>fn,QTable:()=>Id,QTabs:()=>sn,QTd:()=>Dd,QTh:()=>Cd,QTime:()=>Wd,QTimeline:()=>Kd,QTimelineEntry:()=>Yd,QToggle:()=>Cs,QToolbar:()=>Xd,QToolbarTitle:()=>Zd,QTooltip:()=>Di,QTr:()=>jd,QTree:()=>Jd,QUploader:()=>uc,QUploaderAddTrigger:()=>dc,QVideo:()=>cc,QVirtualScroll:()=>Bd});var Ze=e=>v(_(e)),Ge=e=>v(e),Je=["B","KB","MB","GB","TB","PB"];function et(e){let t=0;for(;parseInt(e,10)>=1024&&t<Je.length-1;)e/=1024,++t;return`${e.toFixed(1)}${Je[t]}`}function tt(e,t,l){return l<=t?t:Math.min(l,Math.max(t,e))}function lt(e,t,l){if(l<=t)return t;let a=l-t+1,o=t+(e-t)%a;return o<t&&(o=a+o),0===o?0:o}function at(e,t=2,l="0"){if(null==e)return e;let a=""+e;return a.length>=t?a:new Array(t-a.length+1).join(l)+a}var ot=XMLHttpRequest,nt=ot.prototype.open,it=["top","right","bottom","left"],rt=[],st=0;var ut=Ze({name:"QAjaxBar",props:{position:{type:String,default:"top",validator:e=>it.includes(e)},size:{type:String,default:"2px"},color:String,skipHijack:Boolean,reverse:Boolean,hijackFilter:Function},emits:["start","stop"],setup(l,{emit:n}){let s,u,{proxy:d}=a(),c=e(0),v=e(!1),p=e(!0),f=0,m=null,g=o((()=>`q-loading-bar q-loading-bar--${l.position}`+(void 0!==l.color?` bg-${l.color}`:"")+(!0===p.value?"":" no-transition"))),h=o((()=>"top"===l.position||"bottom"===l.position)),b=o((()=>!0===h.value?"height":"width")),y=o((()=>{let e=v.value,t=function({p:e,pos:t,active:l,horiz:a,reverse:o,dir:n}){let i=1,r=1;return!0===a?(!0===o&&(i=-1),"bottom"===t&&(r=-1),{transform:`translate3d(${i*(e-100)}%,${l?0:-200*r}%,0)`}):(!0===o&&(r=-1),"right"===t&&(i=-1),{transform:`translate3d(${l?0:n*i*-200}%,${r*(e-100)}%,0)`})}({p:c.value,pos:l.position,active:e,horiz:h.value,reverse:!0===d.$q.lang.rtl&&["top","bottom"].includes(l.position)?!1===l.reverse:l.reverse,dir:!0===d.$q.lang.rtl?-1:1});return t[b.value]=l.size,t.opacity=e?1:0,t})),w=o((()=>!0===v.value?{role:"progressbar","aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":c.value}:{"aria-hidden":"true"}));function x(e=300){let t=s;return s=Math.max(0,e)||0,f++,f>1?(0===t&&e>0?k():null!==m&&t>0&&e<=0&&(clearTimeout(m),m=null),f):(null!==m&&clearTimeout(m),n("start"),c.value=0,m=setTimeout((()=>{m=null,p.value=!0,e>0&&k()}),!0===v._value?500:1),!0!==v._value&&(v.value=!0,p.value=!1),f)}function _(e){return f>0&&(c.value=function(e,t){return"number"!=typeof t&&(t=e<25?3*Math.random()+3:e<65?3*Math.random():e<85?2*Math.random():e<99?.6:0),tt(e+t,0,100)}(c.value,e)),f}function S(){if(f=Math.max(0,f-1),f>0)return f;null!==m&&(clearTimeout(m),m=null),n("stop");let e=()=>{p.value=!0,c.value=100,m=setTimeout((()=>{m=null,v.value=!1}),1e3)};return 0===c.value?m=setTimeout(e,1):e(),f}function k(){c.value<100&&(m=setTimeout((()=>{m=null,_(),k()}),s))}return r((()=>{!0!==l.skipHijack&&(u=!0,function(e){st++,rt.push(e),!(st>1)&&(ot.prototype.open=function(e,t){let l=[];this.addEventListener("loadstart",(()=>{rt.forEach((e=>{(null===e.hijackFilter.value||!0===e.hijackFilter.value(t))&&(e.start(),l.push(e.stop))}))}),{once:!0}),this.addEventListener("loadend",(()=>{l.forEach((e=>{e()}))}),{once:!0}),nt.apply(this,arguments)})}({start:x,stop:S,hijackFilter:o((()=>l.hijackFilter||null))}))})),i((()=>{null!==m&&clearTimeout(m),!0===u&&function(e){rt=rt.filter((t=>t.start!==e)),0===(st=Math.max(0,st-1))&&(ot.prototype.open=nt)}(x)})),Object.assign(d,{start:x,stop:S,increment:_}),()=>t("div",{class:g.value,style:y.value,...w.value})}}),dt={xs:18,sm:24,md:32,lg:38,xl:46},ct={size:String};function vt(e,t=dt){return o((()=>void 0!==e.size?{fontSize:e.size in t?`${t[e.size]}px`:e.size}:null))}function pt(e,t){return void 0!==e&&e()||t}function ft(e,t){if(void 0!==e){let t=e();if(null!=t)return t.slice()}return t}function mt(e,t){return void 0!==e?t.concat(e()):t}function gt(e,t){return void 0===e?t:void 0!==t?t.concat(e()):e()}function ht(e,l,a,o,n,i){l.key=o+n;let r=t(e,l,a);return!0===n?g(r,i()):r}var bt="0 0 24 24",yt=e=>e,wt=e=>`ionicons ${e}`,xt={"mdi-":e=>`mdi ${e}`,"icon-":yt,"bt-":e=>`bt ${e}`,"eva-":e=>`eva ${e}`,"ion-md":wt,"ion-ios":wt,"ion-logo":wt,"iconfont ":yt,"ti-":e=>`themify-icon ${e}`,"bi-":e=>`bootstrap-icons ${e}`},_t={o_:"-outlined",r_:"-round",s_:"-sharp"},St={sym_o_:"-outlined",sym_r_:"-rounded",sym_s_:"-sharp"},kt=new RegExp("^("+Object.keys(xt).join("|")+")"),qt=new RegExp("^("+Object.keys(_t).join("|")+")"),Ct=new RegExp("^("+Object.keys(St).join("|")+")"),$t=/^[Mm]\s?[-+]?\.?\d/,Mt=/^img:/,Tt=/^svguse:/,Bt=/^ion-/,Lt=/^(fa-(sharp|solid|regular|light|brands|duotone|thin)|[lf]a[srlbdk]?) /,zt=Ze({name:"QIcon",props:{...ct,tag:{type:String,default:"i"},name:String,color:String,left:Boolean,right:Boolean},setup(e,{slots:l}){let{proxy:{$q:n}}=a(),i=vt(e),r=o((()=>"q-icon"+(!0===e.left?" on-left":"")+(!0===e.right?" on-right":"")+(void 0!==e.color?` text-${e.color}`:""))),s=o((()=>{let l,a=e.name;if("none"===a||!a)return{none:!0};if(null!==n.iconMapFn){let e=n.iconMapFn(a);if(void 0!==e){if(void 0===e.icon)return{cls:e.cls,content:void 0!==e.content?e.content:" "};if(a=e.icon,"none"===a||!a)return{none:!0}}}if(!0===$t.test(a)){let[e,l=bt]=a.split("|");return{svg:!0,viewBox:l,nodes:e.split("&&").map((e=>{let[l,a,o]=e.split("@@");return t("path",{style:a,d:l,transform:o})}))}}if(!0===Mt.test(a))return{img:!0,src:a.substring(4)};if(!0===Tt.test(a)){let[e,t=bt]=a.split("|");return{svguse:!0,src:e.substring(7),viewBox:t}}let o=" ",i=a.match(kt);if(null!==i)l=xt[i[1]](a);else if(!0===Lt.test(a))l=a;else if(!0===Bt.test(a))l=`ionicons ion-${!0===n.platform.is.ios?"ios":"md"}${a.substring(3)}`;else if(!0===Ct.test(a)){l="notranslate material-symbols";let e=a.match(Ct);null!==e&&(a=a.substring(6),l+=St[e[1]]),o=a}else{l="notranslate material-icons";let e=a.match(qt);null!==e&&(a=a.substring(2),l+=_t[e[1]]),o=a}return{cls:l,content:o}}));return()=>{let a={class:r.value,style:i.value,"aria-hidden":"true",role:"presentation"};return!0===s.value.none?t(e.tag,a,pt(l.default)):!0===s.value.img?t(e.tag,a,mt(l.default,[t("img",{src:s.value.src})])):!0===s.value.svg?t(e.tag,a,mt(l.default,[t("svg",{viewBox:s.value.viewBox||"0 0 24 24"},s.value.nodes)])):!0===s.value.svguse?t(e.tag,a,mt(l.default,[t("svg",{viewBox:s.value.viewBox},[t("use",{"xlink:href":s.value.src})])])):(void 0!==s.value.cls&&(a.class+=" "+s.value.cls),t(e.tag,a,mt(l.default,[s.value.content])))}}}),Vt=Ze({name:"QAvatar",props:{...ct,fontSize:String,color:String,textColor:String,icon:String,square:Boolean,rounded:Boolean},setup(e,{slots:l}){let a=vt(e),n=o((()=>"q-avatar"+(e.color?` bg-${e.color}`:"")+(e.textColor?` text-${e.textColor} q-chip--colored`:"")+(!0===e.square?" q-avatar--square":!0===e.rounded?" rounded-borders":""))),i=o((()=>e.fontSize?{fontSize:e.fontSize}:null));return()=>{let o=void 0!==e.icon?[t(zt,{name:e.icon})]:void 0;return t("div",{class:n.value,style:a.value},[t("div",{class:"q-avatar__content row flex-center overflow-hidden",style:i.value},gt(l.default,o))])}}}),Ot=["top","middle","bottom"],At=Ze({name:"QBadge",props:{color:String,textColor:String,floating:Boolean,transparent:Boolean,multiLine:Boolean,outline:Boolean,rounded:Boolean,label:[Number,String],align:{type:String,validator:e=>Ot.includes(e)}},setup(e,{slots:l}){let a=o((()=>void 0!==e.align?{verticalAlign:e.align}:null)),n=o((()=>{let t=!0===e.outline&&e.color||e.textColor;return`q-badge flex inline items-center no-wrap q-badge--${!0===e.multiLine?"multi":"single"}-line`+(!0===e.outline?" q-badge--outline":void 0!==e.color?` bg-${e.color}`:"")+(void 0!==t?` text-${t}`:"")+(!0===e.floating?" q-badge--floating":"")+(!0===e.rounded?" q-badge--rounded":"")+(!0===e.transparent?" q-badge--transparent":"")}));return()=>t("div",{class:n.value,style:a.value,role:"status","aria-label":e.label},mt(l.default,void 0!==e.label?[e.label]:[]))}}),Et={dark:{type:Boolean,default:null}};function Pt(e,t){return o((()=>null===e.dark?t.dark.isActive:e.dark))}var Ft=Ze({name:"QBanner",props:{...Et,inlineActions:Boolean,dense:Boolean,rounded:Boolean},setup(e,{slots:l}){let{proxy:{$q:n}}=a(),i=Pt(e,n),r=o((()=>"q-banner row items-center"+(!0===e.dense?" q-banner--dense":"")+(!0===i.value?" q-banner--dark q-dark":"")+(!0===e.rounded?" rounded-borders":""))),s=o((()=>"q-banner__actions row items-center justify-end col-"+(!0===e.inlineActions?"auto":"all")));return()=>{let a=[t("div",{class:"q-banner__avatar col-auto row items-center self-start"},pt(l.avatar)),t("div",{class:"q-banner__content col text-body2"},pt(l.default))],o=pt(l.action);return void 0!==o&&a.push(t("div",{class:s.value},o)),t("div",{class:r.value+(!1===e.inlineActions&&void 0!==o?" q-banner--top-padding":""),role:"alert"},a)}}}),Rt=Ze({name:"QBar",props:{...Et,dense:Boolean},setup(e,{slots:l}){let{proxy:{$q:n}}=a(),i=Pt(e,n),r=o((()=>`q-bar row no-wrap items-center q-bar--${!0===e.dense?"dense":"standard"}  q-bar--${!0===i.value?"dark":"light"}`));return()=>t("div",{class:r.value,role:"toolbar"},pt(l.default))}}),Nt={left:"start",center:"center",right:"end",between:"between",around:"around",evenly:"evenly",stretch:"stretch"},Ht=Object.keys(Nt),It={align:{type:String,validator:e=>Ht.includes(e)}};function jt(e){return o((()=>{let t=void 0===e.align?!0===e.vertical?"stretch":"left":e.align;return`${!0===e.vertical?"items":"justify"}-${Nt[t]}`}))}function Dt(e){if(Object(e.$parent)===e.$parent)return e.$parent;let{parent:t}=e.$;for(;Object(t)===t;){if(Object(t.proxy)===t.proxy)return t.proxy;t=t.parent}}function Qt(e,t){"symbol"==typeof t.type?!0===Array.isArray(t.children)&&t.children.forEach((t=>{Qt(e,t)})):e.add(t)}function Ut(e){let t=new Set;return e.forEach((e=>{Qt(t,e)})),Array.from(t)}function Wt(e){return void 0!==e.appContext.config.globalProperties.$router}function Kt(e){return!0===e.isUnmounted||!0===e.isDeactivated}var Yt=["",!0],Xt=Ze({name:"QBreadcrumbs",props:{...It,separator:{type:String,default:"/"},separatorColor:String,activeColor:{type:String,default:"primary"},gutter:{type:String,validator:e=>["none","xs","sm","md","lg","xl"].includes(e),default:"sm"}},setup(e,{slots:l}){let a=jt(e),n=o((()=>`flex items-center ${a.value}${"none"===e.gutter?"":` q-gutter-${e.gutter}`}`)),i=o((()=>e.separatorColor?` text-${e.separatorColor}`:"")),r=o((()=>` text-${e.activeColor}`));return()=>{let a=Ut(pt(l.default));if(0===a.length)return;let o=1,s=[],u=a.filter((e=>void 0!==e.type&&"QBreadcrumbsEl"===e.type.name)).length,d=void 0!==l.separator?l.separator:()=>e.separator;return a.forEach((e=>{if(void 0!==e.type&&"QBreadcrumbsEl"===e.type.name){let l=o<u,a=(!0===l?"":" q-breadcrumbs--last")+(!0!==(null!==e.props&&Yt.includes(e.props.disable))&&!0===l?r.value:"");o++,s.push(t("div",{class:`flex items-center${a}`},[e])),!0===l&&s.push(t("div",{class:"q-breadcrumbs__separator"+i.value},d()))}else s.push(e)})),t("div",{class:"q-breadcrumbs"},[t("div",{class:n.value},s)])}}});function Zt(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}function Gt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Jt(e,t){return!0===Array.isArray(t)?e.length===t.length&&e.every(((e,l)=>e===t[l])):1===e.length&&e[0]===t}function el(e,t){return!0===Array.isArray(e)?Jt(e,t):!0===Array.isArray(t)?Jt(t,e):e===t}var tl={to:[String,Object],replace:Boolean,exact:Boolean,activeClass:{type:String,default:"q-router-link--active"},exactActiveClass:{type:String,default:"q-router-link--exact-active"},href:String,target:String,disable:Boolean};function ll({fallbackTag:e,useDisableForRouterLinkProps:t=!0}={}){let l=a(),{props:n,proxy:i,emit:r}=l,s=Wt(l),u=o((()=>!0!==n.disable&&void 0!==n.href)),d=o(!0===t?()=>!0===s&&!0!==n.disable&&!0!==u.value&&void 0!==n.to&&null!==n.to&&""!==n.to:()=>!0===s&&!0!==u.value&&void 0!==n.to&&null!==n.to&&""!==n.to),c=o((()=>!0===d.value?w(n.to):null)),v=o((()=>null!==c.value)),p=o((()=>!0===u.value||!0===v.value)),f=o((()=>"a"===n.type||!0===p.value?"a":n.tag||e||"div")),m=o((()=>!0===u.value?{href:n.href,target:n.target}:!0===v.value?{href:c.value.href,target:n.target}:{})),g=o((()=>{if(!1===v.value)return-1;let{matched:e}=c.value,{length:t}=e,l=e[t-1];if(void 0===l)return-1;let a=i.$route.matched;if(0===a.length)return-1;let o=a.findIndex(Gt.bind(null,l));if(-1!==o)return o;let n=Zt(e[t-2]);return t>1&&Zt(l)===n&&a[a.length-1].path!==n?a.findIndex(Gt.bind(null,e[t-2])):o})),h=o((()=>!0===v.value&&-1!==g.value&&function(e,t){for(let l in t){let a=t[l],o=e[l];if("string"==typeof a){if(a!==o)return!1}else if(!1===Array.isArray(o)||o.length!==a.length||a.some(((e,t)=>e!==o[t])))return!1}return!0}(i.$route.params,c.value.params))),b=o((()=>!0===h.value&&g.value===i.$route.matched.length-1&&function(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(let l in e)if(!1===el(e[l],t[l]))return!1;return!0}(i.$route.params,c.value.params))),y=o((()=>!0===v.value?!0===b.value?` ${n.exactActiveClass} ${n.activeClass}`:!0===n.exact?"":!0===h.value?` ${n.activeClass}`:"":""));function w(e){try{return i.$router.resolve(e)}catch{}return null}function x(e,{returnRouterError:t,to:l=n.to,replace:a=n.replace}={}){if(!0===n.disable)return e.preventDefault(),Promise.resolve(!1);if(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey||void 0!==e.button&&0!==e.button||"_blank"===n.target)return Promise.resolve(!1);e.preventDefault();let o=i.$router[!0===a?"replace":"push"](l);return!0===t?o:o.then((()=>{})).catch((()=>{}))}return{hasRouterLink:v,hasHrefLink:u,hasLink:p,linkTag:f,resolvedLink:c,linkIsActive:h,linkIsExactActive:b,linkClass:y,linkAttrs:m,getLink:w,navigateToRouterLink:x,navigateOnClick:function(e){if(!0===v.value){let t=t=>x(e,t);r("click",e,t),!0!==e.defaultPrevented&&t()}else r("click",e)}}}var al=Ze({name:"QBreadcrumbsEl",props:{...tl,label:String,icon:String,tag:{type:String,default:"span"}},emits:["click"],setup(e,{slots:l}){let{linkTag:a,linkAttrs:n,linkClass:i,navigateOnClick:r}=ll(),s=o((()=>({class:"q-breadcrumbs__el q-link flex inline items-center relative-position "+(!0!==e.disable?"q-link--focusable"+i.value:"q-breadcrumbs__el--disable"),...n.value,onClick:r}))),u=o((()=>"q-breadcrumbs__el-icon"+(void 0!==e.label?" q-breadcrumbs__el-icon--with-label":"")));return()=>{let o=[];return void 0!==e.icon&&o.push(t(zt,{class:u.value,name:e.icon})),void 0!==e.label&&o.push(e.label),t(a.value,{...s.value},mt(l.default,o))}}}),ol={size:{type:[Number,String],default:"1em"},color:String};function nl(e){return{cSize:o((()=>e.size in dt?`${dt[e.size]}px`:e.size)),classes:o((()=>"q-spinner"+(e.color?` text-${e.color}`:"")))}}var il=Ze({name:"QSpinner",props:{...ol,thickness:{type:Number,default:5}},setup(e){let{cSize:l,classes:a}=nl(e);return()=>t("svg",{class:a.value+" q-spinner-mat",width:l.value,height:l.value,viewBox:"25 25 50 50"},[t("circle",{class:"path",cx:"50",cy:"50",r:"20",fill:"none",stroke:"currentColor","stroke-width":e.thickness,"stroke-miterlimit":"10"})])}});function rl(e){if(e===window)return{top:0,left:0};let{top:t,left:l}=e.getBoundingClientRect();return{top:t,left:l}}function sl(e){return e===window?window.innerHeight:e.getBoundingClientRect().height}function ul(e,t){let l=e.style;for(let a in t)l[a]=t[a]}function dl(e,t){if(null==e||!0===e.contains(t))return!0;for(let l=e.nextElementSibling;null!==l;l=l.nextElementSibling)if(l.contains(t))return!0;return!1}function cl(e,t=250){let l,a=!1;return function(){return!1===a&&(a=!0,setTimeout((()=>{a=!1}),t),l=e.apply(this,arguments)),l}}function vl(e,t,l,a){!0===l.modifiers.stop&&te(e);let o=l.modifiers.color,n=l.modifiers.center;n=!0===n||!0===a;let i=document.createElement("span"),r=document.createElement("span"),s=ee(e),{left:u,top:d,width:c,height:v}=t.getBoundingClientRect(),p=Math.sqrt(c*c+v*v),f=p/2,m=(c-p)/2+"px",g=n?m:s.left-u-f+"px",h=(v-p)/2+"px",b=n?h:s.top-d-f+"px";r.className="q-ripple__inner",ul(r,{height:`${p}px`,width:`${p}px`,transform:`translate3d(${g},${b},0) scale3d(.2,.2,1)`,opacity:0}),i.className="q-ripple"+(o?" text-"+o:""),i.setAttribute("dir","ltr"),i.appendChild(r),t.appendChild(i);let y=()=>{i.remove(),clearTimeout(w)};l.abort.push(y);let w=setTimeout((()=>{r.classList.add("q-ripple__inner--enter"),r.style.transform=`translate3d(${m},${h},0) scale3d(1,1,1)`,r.style.opacity=.2,w=setTimeout((()=>{r.classList.remove("q-ripple__inner--enter"),r.classList.add("q-ripple__inner--leave"),r.style.opacity=0,w=setTimeout((()=>{i.remove(),l.abort.splice(l.abort.indexOf(y),1)}),275)}),250)}),50)}function pl(e,{modifiers:t,value:l,arg:a}){let o=Object.assign({},e.cfg.ripple,t,l);e.modifiers={early:!0===o.early,stop:!0===o.stop,center:!0===o.center,color:o.color||a,keyCodes:[].concat(o.keyCodes||13)}}var fl=Ge({name:"ripple",beforeMount(e,t){let l=t.instance.$.appContext.config.globalProperties.$q.config||{};if(!1===l.ripple)return;let a={cfg:l,enabled:!1!==t.value,modifiers:{},abort:[],start(t){!0===a.enabled&&!0!==t.qSkipRipple&&t.type===(!0===a.modifiers.early?"pointerdown":"click")&&vl(t,e,a,!0===t.qKeyEvent)},keystart:cl((t=>{!0===a.enabled&&!0!==t.qSkipRipple&&!0===qe(t,a.modifiers.keyCodes)&&t.type==="key"+(!0===a.modifiers.early?"down":"up")&&vl(t,e,a,!0)}),300)};pl(a,t),e.__qripple=a,ne(a,"main",[[e,"pointerdown","start","passive"],[e,"click","start","passive"],[e,"keydown","keystart","passive"],[e,"keyup","keystart","passive"]])},updated(e,t){if(t.oldValue!==t.value){let l=e.__qripple;void 0!==l&&(l.enabled=!1!==t.value,!0===l.enabled&&Object(t.value)===t.value&&pl(l,t))}},beforeUnmount(e){let t=e.__qripple;void 0!==t&&(t.abort.forEach((e=>{e()})),ie(t,"main"),delete e._qripple)}}),ml={none:0,xs:4,sm:8,md:16,lg:24,xl:32},gl={xs:8,sm:10,md:14,lg:20,xl:24},hl=["button","submit","reset"],bl=/[^\s]\/[^\s]/,yl=["flat","outline","push","unelevated"],wl=(e,t)=>!0===e.flat?"flat":!0===e.outline?"outline":!0===e.push?"push":!0===e.unelevated?"unelevated":t,xl=e=>{let t=wl(e);return void 0!==t?{[t]:!0}:{}},_l={...ct,...tl,type:{type:String,default:"button"},label:[Number,String],icon:String,iconRight:String,...yl.reduce(((e,t)=>(e[t]=Boolean)&&e),{}),square:Boolean,round:Boolean,rounded:Boolean,glossy:Boolean,size:String,fab:Boolean,fabMini:Boolean,padding:String,color:String,textColor:String,noCaps:Boolean,noWrap:Boolean,dense:Boolean,tabindex:[Number,String],ripple:{type:[Boolean,Object],default:!0},align:{...It.align,default:"center"},stack:Boolean,stretch:Boolean,loading:{type:Boolean,default:null},disable:Boolean};var{passiveCapture:Sl}=Z,kl=null,ql=null,Cl=null,$l=Ze({name:"QBtn",props:{..._l,percentage:Number,darkPercentage:Boolean,onTouchstart:[Function,Array]},emits:["click","keydown","mousedown","keyup"],setup(l,{slots:n,emit:r}){let s,{proxy:u}=a(),{classes:d,style:c,innerClasses:v,attributes:p,hasLink:m,linkTag:h,navigateOnClick:b,isActionable:y}=function(e){let t=vt(e,gl),l=jt(e),{hasRouterLink:a,hasLink:n,linkTag:i,linkAttrs:r,navigateOnClick:s}=ll({fallbackTag:"button"}),u=o((()=>{let l=!1===e.fab&&!1===e.fabMini?t.value:{};return void 0!==e.padding?Object.assign({},l,{padding:e.padding.split(/\s+/).map((e=>e in ml?ml[e]+"px":e)).join(" "),minWidth:"0",minHeight:"0"}):l})),d=o((()=>!0===e.rounded||!0===e.fab||!0===e.fabMini)),c=o((()=>!0!==e.disable&&!0!==e.loading)),v=o((()=>!0===c.value?e.tabindex||0:-1)),p=o((()=>wl(e,"standard"))),f=o((()=>{let t={tabindex:v.value};return!0===n.value?Object.assign(t,r.value):!0===hl.includes(e.type)&&(t.type=e.type),"a"===i.value?(!0===e.disable?t["aria-disabled"]="true":void 0===t.href&&(t.role="button"),!0!==a.value&&!0===bl.test(e.type)&&(t.type=e.type)):!0===e.disable&&(t.disabled="",t["aria-disabled"]="true"),!0===e.loading&&void 0!==e.percentage&&Object.assign(t,{role:"progressbar","aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":e.percentage}),t}));return{classes:o((()=>{let t;void 0!==e.color?t=!0===e.flat||!0===e.outline?`text-${e.textColor||e.color}`:`bg-${e.color} text-${e.textColor||"white"}`:e.textColor&&(t=`text-${e.textColor}`);let l=!0===e.round?"round":"rectangle"+(!0===d.value?" q-btn--rounded":!0===e.square?" q-btn--square":"");return`q-btn--${p.value} q-btn--${l}`+(void 0!==t?" "+t:"")+(!0===c.value?" q-btn--actionable q-focusable q-hoverable":!0===e.disable?" disabled":"")+(!0===e.fab?" q-btn--fab":!0===e.fabMini?" q-btn--fab-mini":"")+(!0===e.noCaps?" q-btn--no-uppercase":"")+(!0===e.dense?" q-btn--dense":"")+(!0===e.stretch?" no-border-radius self-stretch":"")+(!0===e.glossy?" glossy":"")+(e.square?" q-btn--square":"")})),style:u,innerClasses:o((()=>l.value+(!0===e.stack?" column":" row")+(!0===e.noWrap?" no-wrap text-no-wrap":"")+(!0===e.loading?" q-btn__content--hidden":""))),attributes:f,hasLink:n,linkTag:i,navigateOnClick:s,isActionable:c}}(l),w=e(null),x=e(null),_=null,S=null,k=o((()=>void 0!==l.label&&null!==l.label&&""!==l.label)),q=o((()=>!0!==l.disable&&!1!==l.ripple&&{keyCodes:!0===m.value?[13,32]:[13],...!0===l.ripple?{}:l.ripple})),C=o((()=>({center:l.round}))),$=o((()=>{let e=Math.max(0,Math.min(100,l.percentage));return e>0?{transition:"transform 0.6s",transform:`translateX(${e-100}%)`}:{}})),M=o((()=>{if(!0===l.loading)return{onMousedown:E,onTouchstart:E,onClick:E,onKeydown:E,onKeyup:E};if(!0===y.value){let e={onClick:B,onKeydown:L,onMousedown:V};if(!0===u.$q.platform.has.touch){e[`onTouchstart${void 0!==l.onTouchstart?"":"Passive"}`]=z}return e}return{onClick:ae}})),T=o((()=>({ref:w,class:"q-btn q-btn-item non-selectable no-outline "+d.value,style:c.value,...p.value,...M.value})));function B(e){if(null!==w.value){if(void 0!==e){if(!0===e.defaultPrevented)return;let t=document.activeElement;if("submit"===l.type&&t!==document.body&&!1===w.value.contains(t)&&!1===t.contains(w.value)){w.value.focus();let e=()=>{document.removeEventListener("keydown",ae,!0),document.removeEventListener("keyup",e,Sl),null!==w.value&&w.value.removeEventListener("blur",e,Sl)};document.addEventListener("keydown",ae,!0),document.addEventListener("keyup",e,Sl),w.value.addEventListener("blur",e,Sl)}}b(e)}}function L(e){null!==w.value&&(r("keydown",e),!0===qe(e,[13,32])&&ql!==w.value&&(null!==ql&&A(),!0!==e.defaultPrevented&&(w.value.focus(),ql=w.value,w.value.classList.add("q-btn--active"),document.addEventListener("keyup",O,!0),w.value.addEventListener("blur",O,Sl)),ae(e)))}function z(e){null!==w.value&&(r("touchstart",e),!0!==e.defaultPrevented&&(kl!==w.value&&(null!==kl&&A(),kl=w.value,_=e.target,_.addEventListener("touchcancel",O,Sl),_.addEventListener("touchend",O,Sl)),s=!0,null!==S&&clearTimeout(S),S=setTimeout((()=>{S=null,s=!1}),200)))}function V(e){null!==w.value&&(e.qSkipRipple=!0===s,r("mousedown",e),!0!==e.defaultPrevented&&Cl!==w.value&&(null!==Cl&&A(),Cl=w.value,w.value.classList.add("q-btn--active"),document.addEventListener("mouseup",O,Sl)))}function O(e){if(null!==w.value&&(void 0===e||"blur"!==e.type||document.activeElement!==w.value)){if(void 0!==e&&"keyup"===e.type){if(ql===w.value&&!0===qe(e,[13,32])){let t=new MouseEvent("click",e);t.qKeyEvent=!0,!0===e.defaultPrevented&&le(t),!0===e.cancelBubble&&te(t),w.value.dispatchEvent(t),ae(e),e.qKeyEvent=!0}r("keyup",e)}A()}}function A(e){let t=x.value;!0!==e&&(kl===w.value||Cl===w.value)&&null!==t&&t!==document.activeElement&&(t.setAttribute("tabindex",-1),t.focus()),kl===w.value&&(null!==_&&(_.removeEventListener("touchcancel",O,Sl),_.removeEventListener("touchend",O,Sl)),kl=_=null),Cl===w.value&&(document.removeEventListener("mouseup",O,Sl),Cl=null),ql===w.value&&(document.removeEventListener("keyup",O,!0),null!==w.value&&w.value.removeEventListener("blur",O,Sl),ql=null),null!==w.value&&w.value.classList.remove("q-btn--active")}function E(e){ae(e),e.qSkipRipple=!0}return i((()=>{A(!0)})),Object.assign(u,{click:B}),()=>{let e=[];void 0!==l.icon&&e.push(t(zt,{name:l.icon,left:!0!==l.stack&&!0===k.value,role:"img","aria-hidden":"true"})),!0===k.value&&e.push(t("span",{class:"block"},[l.label])),e=mt(n.default,e),void 0!==l.iconRight&&!1===l.round&&e.push(t(zt,{name:l.iconRight,right:!0!==l.stack&&!0===k.value,role:"img","aria-hidden":"true"}));let a=[t("span",{class:"q-focus-helper",ref:x})];return!0===l.loading&&void 0!==l.percentage&&a.push(t("span",{class:"q-btn__progress absolute-full overflow-hidden"+(!0===l.darkPercentage?" q-btn__progress--dark":"")},[t("span",{class:"q-btn__progress-indicator fit block",style:$.value})])),a.push(t("span",{class:"q-btn__content text-center col items-center q-anchor--skip "+v.value},e)),null!==l.loading&&a.push(t(f,{name:"q-transition--fade"},(()=>!0===l.loading?[t("span",{key:"loading",class:"absolute-full flex flex-center"},void 0!==n.loading?n.loading():[t(il)])]:null))),g(t(h.value,T.value,a),[[fl,q.value,void 0,C.value]])}}}),Ml=Ze({name:"QBtnGroup",props:{unelevated:Boolean,outline:Boolean,flat:Boolean,rounded:Boolean,square:Boolean,push:Boolean,stretch:Boolean,glossy:Boolean,spread:Boolean},setup(e,{slots:l}){let a=o((()=>{let t=["unelevated","outline","flat","rounded","square","push","stretch","glossy"].filter((t=>!0===e[t])).map((e=>`q-btn-group--${e}`)).join(" ");return"q-btn-group row no-wrap"+(0!==t.length?" "+t:"")+(!0===e.spread?" q-btn-group--spread":" inline")}));return()=>t("div",{class:a.value},pt(l.default))}});function Tl(){if(void 0!==window.getSelection){let e=window.getSelection();void 0!==e.empty?e.empty():void 0!==e.removeAllRanges&&(e.removeAllRanges(),!0!==Y.is.mobile&&e.addRange(document.createRange()))}else void 0!==document.selection&&document.selection.empty()}var Bl={target:{default:!0},noParentEvent:Boolean,contextMenu:Boolean};function Ll({showing:t,avoidEmit:l,configureAnchorEl:o}){let{props:s,proxy:u,emit:d}=a(),c=e(null),v=null;function p(e){return null!==c.value&&(void 0===e||void 0===e.touches||e.touches.length<=1)}let f={};function g(){ie(f,"anchor")}function h(){if(!1===s.target||""===s.target||null===u.$el.parentNode)c.value=null;else if(!0===s.target)!function(e){for(c.value=e;c.value.classList.contains("q-anchor--skip");)c.value=c.value.parentNode;o()}(u.$el.parentNode);else{let e=s.target;if("string"==typeof s.target)try{e=document.querySelector(s.target)}catch{e=void 0}null!=e?(c.value=e.$el||e,o()):c.value=null}}return void 0===o&&(Object.assign(f,{hide(e){u.hide(e)},toggle(e){u.toggle(e),e.qAnchorHandled=!0},toggleKey(e){!0===qe(e,13)&&f.toggle(e)},contextClick(e){u.hide(e),le(e),m((()=>{u.show(e),e.qAnchorHandled=!0}))},prevent:le,mobileTouch(e){if(f.mobileCleanup(e),!0!==p(e))return;u.hide(e),c.value.classList.add("non-selectable");let t=e.target;ne(f,"anchor",[[t,"touchmove","mobileCleanup","passive"],[t,"touchend","mobileCleanup","passive"],[t,"touchcancel","mobileCleanup","passive"],[c.value,"contextmenu","prevent","notPassive"]]),v=setTimeout((()=>{v=null,u.show(e),e.qAnchorHandled=!0}),300)},mobileCleanup(e){c.value.classList.remove("non-selectable"),null!==v&&(clearTimeout(v),v=null),!0===t.value&&void 0!==e&&Tl()}}),o=function(e=s.contextMenu){if(!0===s.noParentEvent||null===c.value)return;let t;t=!0===e?!0===u.$q.platform.is.mobile?[[c.value,"touchstart","mobileTouch","passive"]]:[[c.value,"mousedown","hide","passive"],[c.value,"contextmenu","contextClick","notPassive"]]:[[c.value,"click","toggle","passive"],[c.value,"keyup","toggleKey","passive"]],ne(f,"anchor",t)}),n((()=>s.contextMenu),(e=>{null!==c.value&&(g(),o(e))})),n((()=>s.target),(()=>{null!==c.value&&g(),h()})),n((()=>s.noParentEvent),(e=>{null!==c.value&&(!0===e?g():o())})),r((()=>{h(),!0!==l&&!0===s.modelValue&&null===c.value&&d("update:modelValue",!1)})),i((()=>{null!==v&&clearTimeout(v),g()})),{anchorEl:c,canShow:p,anchorEvents:f}}function zl(t,l){let a,o=e(null);function r(e,t){let l=(void 0!==t?"add":"remove")+"EventListener",o=void 0!==t?t:a;e!==window&&e[l]("scroll",o,Z.passive),window[l]("scroll",o,Z.passive),a=t}function s(){null!==o.value&&(r(o.value),o.value=null)}let u=n((()=>t.noParentEvent),(()=>{null!==o.value&&(s(),l())}));return i(u),{localScrollTarget:o,unconfigureScrollTarget:s,changeScrollEvent:r}}var Vl={modelValue:{type:Boolean,default:null},"onUpdate:modelValue":[Function,Array]},Ol=["beforeShow","show","beforeHide","hide"];function Al({showing:e,canShow:t,hideOnRouteChange:l,handleShow:o,handleHide:i,processOnMount:s}){let u,d=a(),{props:c,emit:v,proxy:p}=d;function f(e){if(!0===c.disable||void 0!==e&&!0===e.qAnchorHandled||void 0!==t&&!0!==t(e))return;let l=void 0!==c["onUpdate:modelValue"];!0===l&&(v("update:modelValue",!0),u=e,m((()=>{u===e&&(u=void 0)}))),(null===c.modelValue||!1===l)&&g(e)}function g(t){!0!==e.value&&(e.value=!0,v("beforeShow",t),void 0!==o?o(t):v("show",t))}function h(e){if(!0===c.disable)return;let t=void 0!==c["onUpdate:modelValue"];!0===t&&(v("update:modelValue",!1),u=e,m((()=>{u===e&&(u=void 0)}))),(null===c.modelValue||!1===t)&&b(e)}function b(t){!1!==e.value&&(e.value=!1,v("beforeHide",t),void 0!==i?i(t):v("hide",t))}function y(t){!0===c.disable&&!0===t?void 0!==c["onUpdate:modelValue"]&&v("update:modelValue",!1):!0===t!==e.value&&(!0===t?g:b)(u)}n((()=>c.modelValue),y),void 0!==l&&!0===Wt(d)&&n((()=>p.$route.fullPath),(()=>{!0===l.value&&!0===e.value&&h()})),!0===s&&r((()=>{y(c.modelValue)}));let w={show:f,hide:h,toggle:function(t){!0===e.value?h(t):f(t)}};return Object.assign(p,w),w}var El=[],Pl=[];function Fl(e){Pl=Pl.filter((t=>t!==e))}function Rl(e){Fl(e),0===Pl.length&&0!==El.length&&(El[El.length-1](),El=[])}function Nl(e){0===Pl.length?e():El.push(e)}var Hl=[],Il=[],jl=1,Dl=document.body;function Ql(e,t){let l=document.createElement("div");if(l.id=void 0!==t?`q-portal--${t}--${jl++}`:e,void 0!==Ne.globalNodes){let e=Ne.globalNodes.class;void 0!==e&&(l.className=e)}return Dl.appendChild(l),Hl.push(l),Il.push(t),l}function Ul(e){let t=Hl.indexOf(e);Hl.splice(t,1),Il.splice(t,1),e.remove()}var Wl=[];function Kl(e,t){do{if("QMenu"===e.$options.name){if(e.hide(t),!0===e.$props.separateClosePopup)return Dt(e)}else if(!0===e.__qPortal){let l=Dt(e);return void 0!==l&&"QPopupProxy"===l.$options.name?(e.hide(t),l):e}e=Dt(e)}while(null!=e)}function Yl(l,a,o,n){let i=e(!1),r=e(!1),s=null,u={},d="dialog"===n&&function(e){for(e=e.parent;null!=e;){if("QGlobalDialog"===e.type.name)return!0;if("QDialog"===e.type.name||"QMenu"===e.type.name)return!1;e=e.parent}return!1}(l);function c(e){if(r.value=!1,!0!==e)return;Rl(u),i.value=!1;let t=Wl.indexOf(l.proxy);-1!==t&&Wl.splice(t,1),null!==s&&(Ul(s),s=null)}return S((()=>{c(!0)})),l.proxy.__qPortal=!0,H(l.proxy,"contentEl",(()=>a.value)),{showPortal:function(e){if(!0===e)return Rl(u),void(r.value=!0);r.value=!1,!1===i.value&&(!1===d&&null===s&&(s=Ql(!1,n)),i.value=!0,Wl.push(l.proxy),function(e){Fl(e),Pl.push(e)}(u))},hidePortal:c,portalIsActive:i,portalIsAccessible:r,renderPortal:()=>!0===d?o():!0===i.value?[t(k,{to:s},o())]:void 0}}var Xl={transitionShow:{type:String,default:"fade"},transitionHide:{type:String,default:"fade"},transitionDuration:{type:[String,Number],default:300}};function Zl(e,t=(()=>{}),l=(()=>{})){return{transitionProps:o((()=>{let a=`q-transition--${e.transitionShow||t()}`,o=`q-transition--${e.transitionHide||l()}`;return{appear:!0,enterFromClass:`${a}-enter-from`,enterActiveClass:`${a}-enter-active`,enterToClass:`${a}-enter-to`,leaveFromClass:`${o}-leave-from`,leaveActiveClass:`${o}-leave-active`,leaveToClass:`${o}-leave-to`}})),transitionStyle:o((()=>`--q-transition-duration: ${e.transitionDuration}ms`))}}function Gl(){let e,t=a();function l(){e=void 0}return h(l),i(l),{removeTick:l,registerTick(l){e=l,m((()=>{e===l&&(!1===Kt(t)&&e(),e=void 0)}))}}}function Jl(){let e=null,t=a();function l(){null!==e&&(clearTimeout(e),e=null)}return h(l),i(l),{removeTimeout:l,registerTimeout(a,o){l(),!1===Kt(t)&&(e=setTimeout((()=>{e=null,a()}),o))}}}var ea,ta=[null,document,document.body,document.scrollingElement,document.documentElement];function la(e,t){let l=function(e){if(null==e)return;if("string"==typeof e)try{return document.querySelector(e)||void 0}catch{return}let t=T(e);return t?t.$el||t:void 0}(t);if(void 0===l){if(null==e)return window;l=e.closest(".scroll,.scroll-y,.overflow-auto")}return ta.includes(l)?window:l}function aa(e){return(e===window?document.body:e).scrollHeight}function oa(e){return e===window?window.pageYOffset||window.scrollY||document.body.scrollTop||0:e.scrollTop}function na(e){return e===window?window.pageXOffset||window.scrollX||document.body.scrollLeft||0:e.scrollLeft}function ia(e,t,l=0){let a=void 0===arguments[3]?performance.now():arguments[3],o=oa(e);l<=0?o!==t&&sa(e,t):requestAnimationFrame((n=>{let i=n-a,r=o+(t-o)/Math.max(i,l)*i;sa(e,r),r!==t&&ia(e,t,l-i,n)}))}function ra(e,t,l=0){let a=void 0===arguments[3]?performance.now():arguments[3],o=na(e);l<=0?o!==t&&ua(e,t):requestAnimationFrame((n=>{let i=n-a,r=o+(t-o)/Math.max(i,l)*i;ua(e,r),r!==t&&ra(e,t,l-i,n)}))}function sa(e,t){e!==window?e.scrollTop=t:window.scrollTo(window.pageXOffset||window.scrollX||document.body.scrollLeft||0,t)}function ua(e,t){e!==window?e.scrollLeft=t:window.scrollTo(t,window.pageYOffset||window.scrollY||document.body.scrollTop||0)}function da(e,t,l){l?ia(e,t,l):sa(e,t)}function ca(e,t,l){l?ra(e,t,l):ua(e,t)}function va(){if(void 0!==ea)return ea;let e=document.createElement("p"),t=document.createElement("div");ul(e,{width:"100%",height:"200px"}),ul(t,{position:"absolute",top:"0px",left:"0px",visibility:"hidden",width:"200px",height:"150px",overflow:"hidden"}),t.appendChild(e),document.body.appendChild(t);let l=e.offsetWidth;t.style.overflow="scroll";let a=e.offsetWidth;return l===a&&(a=t.clientWidth),t.remove(),ea=l-a}function pa(e,t=!0){return!(!e||e.nodeType!==Node.ELEMENT_NODE)&&(t?e.scrollHeight>e.clientHeight&&(e.classList.contains("scroll")||e.classList.contains("overflow-auto")||["auto","scroll"].includes(window.getComputedStyle(e)["overflow-y"])):e.scrollWidth>e.clientWidth&&(e.classList.contains("scroll")||e.classList.contains("overflow-auto")||["auto","scroll"].includes(window.getComputedStyle(e)["overflow-x"])))}var fa,ma=[];function ga(e){fa=27===e.keyCode}function ha(){!0===fa&&(fa=!1)}function ba(e){!0===fa&&(fa=!1,!0===qe(e,27)&&ma[ma.length-1](e))}function ya(e){window[e]("keydown",ga),window[e]("blur",ha),window[e]("keyup",ba),fa=!1}function wa(e){!0===W.is.desktop&&(ma.push(e),1===ma.length&&ya("addEventListener"))}function xa(e){let t=ma.indexOf(e);-1!==t&&(ma.splice(t,1),0===ma.length&&ya("removeEventListener"))}var _a=[];function Sa(e){_a[_a.length-1](e)}function ka(e){!0===W.is.desktop&&(_a.push(e),1===_a.length&&document.body.addEventListener("focusin",Sa))}function qa(e){let t=_a.indexOf(e);-1!==t&&(_a.splice(t,1),0===_a.length&&document.body.removeEventListener("focusin",Sa))}var Ca,$a,{notPassiveCapture:Ma}=Z,Ta=[];function Ba(e){let t=e.target;if(void 0===t||8===t.nodeType||!0===t.classList.contains("no-pointer-events"))return;let l=Wl.length-1;for(;l>=0;){let e=Wl[l].$;if("QTooltip"!==e.type.name){if("QDialog"!==e.type.name)break;if(!0!==e.props.seamless)return;l--}else l--}for(let a=Ta.length-1;a>=0;a--){let l=Ta[a];if(null!==l.anchorEl.value&&!1!==l.anchorEl.value.contains(t)||t!==document.body&&(null===l.innerRef.value||!1!==l.innerRef.value.contains(t)))return;e.qClickOutside=!0,l.onClickOutside(e)}}function La(e){Ta.push(e),1===Ta.length&&(document.addEventListener("mousedown",Ba,Ma),document.addEventListener("touchstart",Ba,Ma))}function za(e){let t=Ta.findIndex((t=>t===e));-1!==t&&(Ta.splice(t,1),0===Ta.length&&(document.removeEventListener("mousedown",Ba,Ma),document.removeEventListener("touchstart",Ba,Ma)))}function Va(e){let t=e.split(" ");return 2===t.length&&(!0===["top","center","bottom"].includes(t[0])&&!0===["left","middle","right","start","end"].includes(t[1]))}function Oa(e){return!e||!(2!==e.length||"number"!=typeof e[0]||"number"!=typeof e[1])}var Aa={"start#ltr":"left","start#rtl":"right","end#ltr":"right","end#rtl":"left"};function Ea(e,t){let l=e.split(" ");return{vertical:l[0],horizontal:Aa[`${l[1]}#${!0===t?"rtl":"ltr"}`]}}function Pa(e,t,l,a){return{top:e[l.vertical]-t[a.vertical],left:e[l.horizontal]-t[a.horizontal]}}function Fa(e,t=0){if(null===e.targetEl||null===e.anchorEl||t>5)return;if(0===e.targetEl.offsetHeight||0===e.targetEl.offsetWidth)return void setTimeout((()=>{Fa(e,t+1)}),10);let{targetEl:l,offset:a,anchorEl:o,anchorOrigin:n,selfOrigin:i,absoluteOffset:r,fit:s,cover:u,maxHeight:d,maxWidth:c}=e;if(!0===W.is.ios&&void 0!==window.visualViewport){let e=document.body.style,{offsetLeft:t,offsetTop:l}=window.visualViewport;t!==Ca&&(e.setProperty("--q-pe-left",t+"px"),Ca=t),l!==$a&&(e.setProperty("--q-pe-top",l+"px"),$a=l)}let{scrollLeft:v,scrollTop:p}=l,f=void 0===r?function(e,t){let{top:l,left:a,right:o,bottom:n,width:i,height:r}=e.getBoundingClientRect();return void 0!==t&&(l-=t[1],a-=t[0],n+=t[1],o+=t[0],i+=t[0],r+=t[1]),{top:l,bottom:n,height:r,left:a,right:o,width:i,middle:a+(o-a)/2,center:l+(n-l)/2}}(o,!0===u?[0,0]:a):function(e,t,l){let{top:a,left:o}=e.getBoundingClientRect();return a+=t.top,o+=t.left,void 0!==l&&(a+=l[1],o+=l[0]),{top:a,bottom:a+1,height:1,left:o,right:o+1,width:1,middle:o,center:a}}(o,r,a);Object.assign(l.style,{top:0,left:0,minWidth:null,minHeight:null,maxWidth:c||"100vw",maxHeight:d||"100vh",visibility:"visible"});let{offsetWidth:m,offsetHeight:g}=l,{elWidth:h,elHeight:b}=!0===s||!0===u?{elWidth:Math.max(f.width,m),elHeight:!0===u?Math.max(f.height,g):g}:{elWidth:m,elHeight:g},y={maxWidth:c,maxHeight:d};(!0===s||!0===u)&&(y.minWidth=f.width+"px",!0===u&&(y.minHeight=f.height+"px")),Object.assign(l.style,y);let w=function(e,t){return{top:0,center:t/2,bottom:t,left:0,middle:e/2,right:e}}(h,b),x=Pa(f,w,n,i);if(void 0===r||void 0===a)Ra(x,f,w,n,i);else{let{top:e,left:t}=x;Ra(x,f,w,n,i);let l=!1;if(x.top!==e){l=!0;let e=2*a[1];f.center=f.top-=e,f.bottom-=e+2}if(x.left!==t){l=!0;let e=2*a[0];f.middle=f.left-=e,f.right-=e+2}!0===l&&(x=Pa(f,w,n,i),Ra(x,f,w,n,i))}y={top:x.top+"px",left:x.left+"px"},void 0!==x.maxHeight&&(y.maxHeight=x.maxHeight+"px",f.height>x.maxHeight&&(y.minHeight=y.maxHeight)),void 0!==x.maxWidth&&(y.maxWidth=x.maxWidth+"px",f.width>x.maxWidth&&(y.minWidth=y.maxWidth)),Object.assign(l.style,y),l.scrollTop!==p&&(l.scrollTop=p),l.scrollLeft!==v&&(l.scrollLeft=v)}function Ra(e,t,l,a,o){let n=l.bottom,i=l.right,r=va(),s=window.innerHeight-r,u=document.body.clientWidth;if(e.top<0||e.top+n>s)if("center"===o.vertical)e.top=t[a.vertical]>s/2?Math.max(0,s-n):0,e.maxHeight=Math.min(n,s);else if(t[a.vertical]>s/2){let l=Math.min(s,"center"===a.vertical?t.center:a.vertical===o.vertical?t.bottom:t.top);e.maxHeight=Math.min(n,l),e.top=Math.max(0,l-n)}else e.top=Math.max(0,"center"===a.vertical?t.center:a.vertical===o.vertical?t.top:t.bottom),e.maxHeight=Math.min(n,s-e.top);if(e.left<0||e.left+i>u)if(e.maxWidth=Math.min(i,u),"middle"===o.horizontal)e.left=t[a.horizontal]>u/2?Math.max(0,u-i):0;else if(t[a.horizontal]>u/2){let l=Math.min(u,"middle"===a.horizontal?t.middle:a.horizontal===o.horizontal?t.right:t.left);e.maxWidth=Math.min(i,l),e.left=Math.max(0,l-e.maxWidth)}else e.left=Math.max(0,"middle"===a.horizontal?t.middle:a.horizontal===o.horizontal?t.left:t.right),e.maxWidth=Math.min(i,u-e.left)}["left","middle","right"].forEach((e=>{Aa[`${e}#ltr`]=e,Aa[`${e}#rtl`]=e}));var Na,Ha=Ze({name:"QMenu",inheritAttrs:!1,props:{...Bl,...Vl,...Et,...Xl,persistent:Boolean,autoClose:Boolean,separateClosePopup:Boolean,noRouteDismiss:Boolean,noRefocus:Boolean,noFocus:Boolean,fit:Boolean,cover:Boolean,square:Boolean,anchor:{type:String,validator:Va},self:{type:String,validator:Va},offset:{type:Array,validator:Oa},scrollTarget:{default:void 0},touchPosition:Boolean,maxHeight:{type:String,default:null},maxWidth:{type:String,default:null}},emits:[...Ol,"click","escapeKey"],setup(l,{slots:r,emit:s,attrs:u}){let d,c,v,p=null,m=a(),{proxy:g}=m,{$q:h}=g,b=e(null),y=e(!1),w=o((()=>!0!==l.persistent&&!0!==l.noRouteDismiss)),x=Pt(l,h),{registerTick:_,removeTick:S}=Gl(),{registerTimeout:k}=Jl(),{transitionProps:q,transitionStyle:C}=Zl(l),{localScrollTarget:$,changeScrollEvent:M,unconfigureScrollTarget:T}=zl(l,D),{anchorEl:B,canShow:L}=Ll({showing:y}),{hide:z}=Al({showing:y,canShow:L,handleShow:function(e){if(p=!1===l.noRefocus?document.activeElement:null,ka(U),V(),D(),d=void 0,void 0!==e&&(l.touchPosition||l.contextMenu)){let t=ee(e);if(void 0!==t.left){let{top:e,left:l}=B.value.getBoundingClientRect();d={left:t.left-l,top:t.top-e}}}void 0===c&&(c=n((()=>h.screen.width+"|"+h.screen.height+"|"+l.self+"|"+l.anchor+"|"+h.lang.rtl),K)),!0!==l.noFocus&&document.activeElement.blur(),_((()=>{K(),!0!==l.noFocus&&I()})),k((()=>{!0===h.platform.is.ios&&(v=l.autoClose,b.value.click()),K(),V(!0),s("show",e)}),l.transitionDuration)},handleHide:function(e){S(),O(),j(!0),null!==p&&(void 0===e||!0!==e.qClickOutside)&&(((e&&0===e.type.indexOf("key")?p.closest('[tabindex]:not([tabindex^="-"])'):void 0)||p).focus(),p=null),k((()=>{O(!0),s("hide",e)}),l.transitionDuration)},hideOnRouteChange:w,processOnMount:!0}),{showPortal:V,hidePortal:O,renderPortal:A}=Yl(m,b,(function(){return t(f,q.value,(()=>!0===y.value?t("div",{role:"menu",...u,ref:b,tabindex:-1,class:["q-menu q-position-engine scroll"+R.value,u.class],style:[u.style,C.value],...N.value},pt(r.default)):null))}),"menu"),E={anchorEl:B,innerRef:b,onClickOutside(e){if(!0!==l.persistent&&!0===y.value)return z(e),("touchstart"===e.type||e.target.classList.contains("q-dialog__backdrop"))&&ae(e),!0}},P=o((()=>Ea(l.anchor||(!0===l.cover?"center middle":"bottom start"),h.lang.rtl))),F=o((()=>!0===l.cover?P.value:Ea(l.self||"top start",h.lang.rtl))),R=o((()=>(!0===l.square?" q-menu--square":"")+(!0===x.value?" q-menu--dark q-dark":""))),N=o((()=>!0===l.autoClose?{onClick:Q}:{})),H=o((()=>!0===y.value&&!0!==l.persistent));function I(){Nl((()=>{let e=b.value;e&&!0!==e.contains(document.activeElement)&&(e=e.querySelector("[autofocus][tabindex], [data-autofocus][tabindex]")||e.querySelector("[autofocus] [tabindex], [data-autofocus] [tabindex]")||e.querySelector("[autofocus], [data-autofocus]")||e,e.focus({preventScroll:!0}))}))}function j(e){d=void 0,void 0!==c&&(c(),c=void 0),(!0===e||!0===y.value)&&(qa(U),T(),za(E),xa(W)),!0!==e&&(p=null)}function D(){(null!==B.value||void 0!==l.scrollTarget)&&($.value=la(B.value,l.scrollTarget),M($.value,K))}function Q(e){!0!==v?(Kl(g,e),s("click",e)):v=!1}function U(e){!0===H.value&&!0!==l.noFocus&&!0!==dl(b.value,e.target)&&I()}function W(e){s("escapeKey"),z(e)}function K(){Fa({targetEl:b.value,offset:l.offset,anchorEl:B.value,anchorOrigin:P.value,selfOrigin:F.value,absoluteOffset:d,fit:l.fit,cover:l.cover,maxHeight:l.maxHeight,maxWidth:l.maxWidth})}return n(H,(e=>{!0===e?(wa(W),La(E)):(xa(W),za(E))})),i(j),Object.assign(g,{focus:I,updatePosition:K}),A}}),Ia=0,ja=new Array(256);for(let Wv=0;Wv<256;Wv++)ja[Wv]=(Wv+256).toString(16).substring(1);var Da=(()=>{let e=typeof crypto<"u"?crypto:typeof window<"u"?window.crypto||window.msCrypto:void 0;if(void 0!==e){if(void 0!==e.randomBytes)return e.randomBytes;if(void 0!==e.getRandomValues)return t=>{let l=new Uint8Array(t);return e.getRandomValues(l),l}}return e=>{let t=[];for(let l=e;l>0;l--)t.push(Math.floor(256*Math.random()));return t}})(),Qa=4096;function Ua(){(void 0===Na||Ia+16>Qa)&&(Ia=0,Na=Da(Qa));let e=Array.prototype.slice.call(Na,Ia,Ia+=16);return e[6]=15&e[6]|64,e[8]=63&e[8]|128,ja[e[0]]+ja[e[1]]+ja[e[2]]+ja[e[3]]+"-"+ja[e[4]]+ja[e[5]]+"-"+ja[e[6]]+ja[e[7]]+"-"+ja[e[8]]+ja[e[9]]+"-"+ja[e[10]]+ja[e[11]]+ja[e[12]]+ja[e[13]]+ja[e[14]]+ja[e[15]]}function Wa(e,t){return e??(!0===t?`f_${Ua()}`:null)}function Ka({getValue:t,required:l=!0}={}){if(!0===D.value){let a=e(void 0!==t?function(e){return e??null}(t()):null);return!0===l&&null===a.value&&r((()=>{a.value=`f_${Ua()}`})),void 0!==t&&n(t,(e=>{a.value=Wa(e,l)})),a}return void 0!==t?o((()=>Wa(t(),l))):e(`f_${Ua()}`)}var Ya=Object.keys(_l),Xa=Ze({name:"QBtnDropdown",props:{..._l,...Xl,modelValue:Boolean,split:Boolean,dropdownIcon:String,contentClass:[Array,String,Object],contentStyle:[Array,String,Object],cover:Boolean,persistent:Boolean,noRouteDismiss:Boolean,autoClose:Boolean,menuAnchor:{type:String,default:"bottom end"},menuSelf:{type:String,default:"top end"},menuOffset:Array,disableMainBtn:Boolean,disableDropdown:Boolean,noIconAnimation:Boolean,toggleAriaLabel:String},emits:["update:modelValue","click","beforeShow","show","beforeHide","hide"],setup(l,{slots:i,emit:s}){let{proxy:u}=a(),d=e(l.modelValue),c=e(null),v=Ka(),p=o((()=>{let e={"aria-expanded":!0===d.value?"true":"false","aria-haspopup":"true","aria-controls":v.value,"aria-label":l.toggleAriaLabel||u.$q.lang.label[!0===d.value?"collapse":"expand"](l.label)};return(!0===l.disable||!1===l.split&&!0===l.disableMainBtn||!0===l.disableDropdown)&&(e["aria-disabled"]="true"),e})),f=o((()=>"q-btn-dropdown__arrow"+(!0===d.value&&!1===l.noIconAnimation?" rotate-180":"")+(!1===l.split?" q-btn-dropdown__arrow-container":""))),m=o((()=>xl(l))),g=o((()=>(e=>Ya.reduce(((t,l)=>{let a=e[l];return void 0!==a&&(t[l]=a),t}),{}))(l)));function h(e){d.value=!0,s("beforeShow",e)}function b(e){s("show",e),s("update:modelValue",!0)}function y(e){d.value=!1,s("beforeHide",e)}function w(e){s("hide",e),s("update:modelValue",!1)}function x(e){s("click",e)}function _(e){te(e),k(),s("click",e)}function S(e){null!==c.value&&c.value.show(e)}function k(e){null!==c.value&&c.value.hide(e)}return n((()=>l.modelValue),(e=>{null!==c.value&&c.value[e?"show":"hide"]()})),n((()=>l.split),k),Object.assign(u,{show:S,hide:k,toggle:function(e){null!==c.value&&c.value.toggle(e)}}),r((()=>{!0===l.modelValue&&S()})),()=>{let e=[t(zt,{class:f.value,name:l.dropdownIcon||u.$q.iconSet.arrow.dropdown})];return!0!==l.disableDropdown&&e.push(t(Ha,{ref:c,id:v.value,class:l.contentClass,style:l.contentStyle,cover:l.cover,fit:!0,persistent:l.persistent,noRouteDismiss:l.noRouteDismiss,autoClose:l.autoClose,anchor:l.menuAnchor,self:l.menuSelf,offset:l.menuOffset,separateClosePopup:!0,transitionShow:l.transitionShow,transitionHide:l.transitionHide,transitionDuration:l.transitionDuration,onBeforeShow:h,onShow:b,onBeforeHide:y,onHide:w},i.default)),!1===l.split?t($l,{class:"q-btn-dropdown q-btn-dropdown--simple",...g.value,...p.value,disable:!0===l.disable||!0===l.disableMainBtn,noWrap:!0,round:!1,onClick:x},{default:()=>pt(i.label,[]).concat(e),loading:i.loading}):t(Ml,{class:"q-btn-dropdown q-btn-dropdown--split no-wrap q-btn-item",rounded:l.rounded,square:l.square,...m.value,glossy:l.glossy,stretch:l.stretch},(()=>[t($l,{class:"q-btn-dropdown--current",...g.value,disable:!0===l.disable||!0===l.disableMainBtn,noWrap:!0,round:!1,onClick:_},{default:i.label,loading:i.loading}),t($l,{class:"q-btn-dropdown__arrow-container q-anchor--skip",...p.value,...m.value,disable:!0===l.disable||!0===l.disableDropdown,rounded:l.rounded,color:l.color,textColor:l.textColor,dense:l.dense,size:l.size,padding:l.padding,ripple:l.ripple},(()=>e))]))}}}),Za={name:String};function Ga(e){return o((()=>({type:"hidden",name:e.name,value:e.modelValue})))}function Ja(e={}){return(l,a,o)=>{l[a](t("input",{class:"hidden"+(o||""),...e.value}))}}function eo(e){return o((()=>e.name||e.for))}var to=Ze({name:"QBtnToggle",props:{...Za,modelValue:{required:!0},options:{type:Array,required:!0,validator:e=>e.every((e=>("label"in e||"icon"in e||"slot"in e)&&"value"in e))},color:String,textColor:String,toggleColor:{type:String,default:"primary"},toggleTextColor:String,outline:Boolean,flat:Boolean,unelevated:Boolean,rounded:Boolean,push:Boolean,glossy:Boolean,size:String,padding:String,noCaps:Boolean,noWrap:Boolean,dense:Boolean,readonly:Boolean,disable:Boolean,stack:Boolean,stretch:Boolean,spread:Boolean,clearable:Boolean,ripple:{type:[Boolean,Object],default:!0}},emits:["update:modelValue","clear","click"],setup(e,{slots:l,emit:a}){let n=o((()=>void 0!==e.options.find((t=>t.value===e.modelValue)))),i=Ja(o((()=>({type:"hidden",name:e.name,value:e.modelValue})))),r=o((()=>xl(e))),s=o((()=>({rounded:e.rounded,dense:e.dense,...r.value}))),u=o((()=>e.options.map(((t,l)=>{let{attrs:o,value:n,slot:i,...r}=t;return{slot:i,props:{key:l,"aria-pressed":n===e.modelValue?"true":"false",...o,...r,...s.value,disable:!0===e.disable||!0===r.disable,color:n===e.modelValue?d(r,"toggleColor"):d(r,"color"),textColor:n===e.modelValue?d(r,"toggleTextColor"):d(r,"textColor"),noCaps:!0===d(r,"noCaps"),noWrap:!0===d(r,"noWrap"),size:d(r,"size"),padding:d(r,"padding"),ripple:d(r,"ripple"),stack:!0===d(r,"stack"),stretch:!0===d(r,"stretch"),onClick(l){!function(t,l,o){!0!==e.readonly&&(e.modelValue===t?!0===e.clearable&&(a("update:modelValue",null,null),a("clear")):a("update:modelValue",t,l),a("click",o))}(n,t,l)}}}}))));function d(t,l){return void 0===t[l]?e[l]:t[l]}function c(){let a=u.value.map((e=>t($l,e.props,void 0!==e.slot?l[e.slot]:void 0)));return void 0!==e.name&&!0!==e.disable&&!0===n.value&&i(a,"push"),mt(l.default,a)}return()=>t(Ml,{class:"q-btn-toggle",...r.value,rounded:e.rounded,stretch:e.stretch,glossy:e.glossy,spread:e.spread},c)}}),lo=Ze({name:"QCard",props:{...Et,tag:{type:String,default:"div"},square:Boolean,flat:Boolean,bordered:Boolean},setup(e,{slots:l}){let{proxy:{$q:n}}=a(),i=Pt(e,n),r=o((()=>"q-card"+(!0===i.value?" q-card--dark q-dark":"")+(!0===e.bordered?" q-card--bordered":"")+(!0===e.square?" q-card--square no-border-radius":"")+(!0===e.flat?" q-card--flat no-shadow":"")));return()=>t(e.tag,{class:r.value},pt(l.default))}}),ao=Ze({name:"QCardSection",props:{tag:{type:String,default:"div"},horizontal:Boolean},setup(e,{slots:l}){let a=o((()=>"q-card__section q-card__section--"+(!0===e.horizontal?"horiz row no-wrap":"vert")));return()=>t(e.tag,{class:a.value},pt(l.default))}}),oo=Ze({name:"QCardActions",props:{...It,vertical:Boolean},setup(e,{slots:l}){let a=jt(e),n=o((()=>`q-card__actions ${a.value} q-card__actions--${!0===e.vertical?"vert column":"horiz row"}`));return()=>t("div",{class:n.value},pt(l.default))}}),no={left:!0,right:!0,up:!0,down:!0,horizontal:!0,vertical:!0},io=Object.keys(no);function ro(e){let t={};for(let l of io)!0===e[l]&&(t[l]=!0);return 0===Object.keys(t).length?no:(!0===t.horizontal?t.left=t.right=!0:!0===t.left&&!0===t.right&&(t.horizontal=!0),!0===t.vertical?t.up=t.down=!0:!0===t.up&&!0===t.down&&(t.vertical=!0),!0===t.horizontal&&!0===t.vertical&&(t.all=!0),t)}no.all=!0;var so=["INPUT","TEXTAREA"];function uo(e,t){return void 0===t.event&&void 0!==e.target&&!0!==e.target.draggable&&"function"==typeof t.handler&&!1===so.includes(e.target.nodeName.toUpperCase())&&(void 0===e.qClonedBy||-1===e.qClonedBy.indexOf(t.uid))}function co(e){let t=[.06,6,50];return"string"==typeof e&&e.length&&e.split(":").forEach(((e,l)=>{let a=parseFloat(e);a&&(t[l]=a)})),t}var vo=Ge({name:"touch-swipe",beforeMount(e,{value:t,arg:l,modifiers:a}){if(!0!==a.mouse&&!0!==W.has.touch)return;let o=!0===a.mouseCapture?"Capture":"",n={handler:t,sensitivity:co(l),direction:ro(a),noop:G,mouseStart(e){uo(e,n)&&J(e)&&(ne(n,"temp",[[document,"mousemove","move",`notPassive${o}`],[document,"mouseup","end","notPassiveCapture"]]),n.start(e,!0))},touchStart(e){if(uo(e,n)){let t=e.target;ne(n,"temp",[[t,"touchmove","move","notPassiveCapture"],[t,"touchcancel","end","notPassiveCapture"],[t,"touchend","end","notPassiveCapture"]]),n.start(e)}},start(t,l){!0===W.is.firefox&&oe(e,!0);let a=ee(t);n.event={x:a.left,y:a.top,time:Date.now(),mouse:!0===l,dir:!1}},move(e){if(void 0===n.event)return;if(!1!==n.event.dir)return void ae(e);let t=Date.now()-n.event.time;if(0===t)return;let l=ee(e),a=l.left-n.event.x,o=Math.abs(a),i=l.top-n.event.y,r=Math.abs(i);if(!0!==n.event.mouse){if(o<n.sensitivity[1]&&r<n.sensitivity[1])return void n.end(e)}else{if(""!==window.getSelection().toString())return void n.end(e);if(o<n.sensitivity[2]&&r<n.sensitivity[2])return}let s=o/t,u=r/t;!0===n.direction.vertical&&o<r&&o<100&&u>n.sensitivity[0]&&(n.event.dir=i<0?"up":"down"),!0===n.direction.horizontal&&o>r&&r<100&&s>n.sensitivity[0]&&(n.event.dir=a<0?"left":"right"),!0===n.direction.up&&o<r&&i<0&&o<100&&u>n.sensitivity[0]&&(n.event.dir="up"),!0===n.direction.down&&o<r&&i>0&&o<100&&u>n.sensitivity[0]&&(n.event.dir="down"),!0===n.direction.left&&o>r&&a<0&&r<100&&s>n.sensitivity[0]&&(n.event.dir="left"),!0===n.direction.right&&o>r&&a>0&&r<100&&s>n.sensitivity[0]&&(n.event.dir="right"),!1!==n.event.dir?(ae(e),!0===n.event.mouse&&(document.body.classList.add("no-pointer-events--children"),document.body.classList.add("non-selectable"),Tl(),n.styleCleanup=e=>{n.styleCleanup=void 0,document.body.classList.remove("non-selectable");let t=()=>{document.body.classList.remove("no-pointer-events--children")};!0===e?setTimeout(t,50):t()}),n.handler({evt:e,touch:!0!==n.event.mouse,mouse:n.event.mouse,direction:n.event.dir,duration:t,distance:{x:o,y:r}})):n.end(e)},end(t){void 0!==n.event&&(ie(n,"temp"),!0===W.is.firefox&&oe(e,!1),void 0!==n.styleCleanup&&n.styleCleanup(!0),void 0!==t&&!1!==n.event.dir&&ae(t),n.event=void 0)}};if(e.__qtouchswipe=n,!0===a.mouse){let t=!0===a.mouseCapture||!0===a.mousecapture?"Capture":"";ne(n,"main",[[e,"mousedown","mouseStart",`passive${t}`]])}!0===W.has.touch&&ne(n,"main",[[e,"touchstart","touchStart","passive"+(!0===a.capture?"Capture":"")],[e,"touchmove","noop","notPassiveCapture"]])},updated(e,t){let l=e.__qtouchswipe;void 0!==l&&(t.oldValue!==t.value&&("function"!=typeof t.value&&l.end(),l.handler=t.value),l.direction=ro(t.modifiers))},beforeUnmount(e){let t=e.__qtouchswipe;void 0!==t&&(ie(t,"main"),ie(t,"temp"),!0===W.is.firefox&&oe(e,!1),void 0!==t.styleCleanup&&t.styleCleanup(),delete e.__qtouchswipe)}});function po(){let e=Object.create(null);return{getCache:(t,l)=>void 0===e[t]?e[t]="function"==typeof l?l():l:e[t],setCache(t,l){e[t]=l},hasCache:t=>e.hasOwnProperty(t),clearCache(t){void 0!==t?delete e[t]:e={}}}}var fo={name:{required:!0},disable:Boolean},mo={setup:(e,{slots:l})=>()=>t("div",{class:"q-panel scroll",role:"tabpanel"},pt(l.default))},go={modelValue:{required:!0},animated:Boolean,infinite:Boolean,swipeable:Boolean,vertical:Boolean,transitionPrev:String,transitionNext:String,transitionDuration:{type:[String,Number],default:300},keepAlive:Boolean,keepAliveInclude:[String,Array,RegExp],keepAliveExclude:[String,Array,RegExp],keepAliveMax:Number},ho=["update:modelValue","beforeTransition","transition"];function bo(){let l,i,{props:r,emit:s,proxy:u}=a(),{getCache:d}=po(),c=e(null),v=e(null);function p(e){let t=!0===r.vertical?"up":"left";B((!0===u.$q.lang.rtl?-1:1)*(e.direction===t?1:-1))}let g=o((()=>[[vo,p,void 0,{horizontal:!0!==r.vertical,vertical:r.vertical,mouse:!0}]])),h=o((()=>r.transitionPrev||"slide-"+(!0===r.vertical?"down":"right"))),b=o((()=>r.transitionNext||"slide-"+(!0===r.vertical?"up":"left"))),y=o((()=>`--q-transition-duration: ${r.transitionDuration}ms`)),x=o((()=>"string"==typeof r.modelValue||"number"==typeof r.modelValue?r.modelValue:String(r.modelValue))),_=o((()=>({include:r.keepAliveInclude,exclude:r.keepAliveExclude,max:r.keepAliveMax}))),S=o((()=>void 0!==r.keepAliveInclude||void 0!==r.keepAliveExclude));function k(){B(1)}function q(){B(-1)}function C(e){s("update:modelValue",e)}function $(e){return null!=e&&""!==e}function M(e){return l.findIndex((t=>t.props.name===e&&""!==t.props.disable&&!0!==t.props.disable))}function T(e){let t=0!==e&&!0===r.animated&&-1!==c.value?"q-transition--"+(-1===e?h.value:b.value):null;v.value!==t&&(v.value=t)}function B(e,t=c.value){let a=t+e;for(;-1!==a&&a<l.length;){let t=l[a];if(void 0!==t&&""!==t.props.disable&&!0!==t.props.disable)return T(e),i=!0,s("update:modelValue",t.props.name),void setTimeout((()=>{i=!1}));a+=e}!0===r.infinite&&0!==l.length&&-1!==t&&t!==l.length&&B(e,-1===e?l.length:-1)}function L(){let e=M(r.modelValue);return c.value!==e&&(c.value=e),!0}function z(){let e=!0===$(r.modelValue)&&L()&&l[c.value];return!0===r.keepAlive?[t(w,_.value,[t(!0===S.value?d(x.value,(()=>({...mo,name:x.value}))):mo,{key:x.value,style:y.value},(()=>e))])]:[t("div",{class:"q-panel scroll",style:y.value,key:x.value,role:"tabpanel"},[e])]}return n((()=>r.modelValue),((e,t)=>{let l=!0===$(e)?M(e):-1;!0!==i&&T(-1===l?0:l<M(t)?-1:1),c.value!==l&&(c.value=l,s("beforeTransition",e,t),m((()=>{s("transition",e,t)})))})),Object.assign(u,{next:k,previous:q,goTo:C}),{panelIndex:c,panelDirectives:g,updatePanelsList:function(e){return l=Ut(pt(e.default,[])).filter((e=>null!==e.props&&void 0===e.props.slot&&!0===$(e.props.name))),l.length},updatePanelIndex:L,getPanelContent:function(){if(0!==l.length)return!0===r.animated?[t(f,{name:v.value},z)]:z()},getEnabledPanels:function(){return l.filter((e=>""!==e.props.disable&&!0!==e.props.disable))},getPanels:function(){return l},isValidPanelName:$,keepAliveProps:_,needsUniqueKeepAliveWrapper:S,goToPanelByOffset:B,goToPanel:C,nextPanel:k,previousPanel:q}}var yo=0,wo={fullscreen:Boolean,noRouteFullscreenExit:Boolean},xo=["update:fullscreen","fullscreen"];function _o(){let t,l,o,s=a(),{props:u,emit:d,proxy:c}=s,v=e(!1);function p(){!0===v.value?m():f()}function f(){!0!==v.value&&(v.value=!0,o=c.$el.parentNode,o.replaceChild(l,c.$el),document.body.appendChild(c.$el),1===++yo&&document.body.classList.add("q-body--fullscreen-mixin"),t={handler:m},ge.add(t))}function m(){!0===v.value&&(void 0!==t&&(ge.remove(t),t=void 0),o.replaceChild(c.$el,l),v.value=!1,0===(yo=Math.max(0,yo-1))&&(document.body.classList.remove("q-body--fullscreen-mixin"),void 0!==c.$el.scrollIntoView&&setTimeout((()=>{c.$el.scrollIntoView()}))))}return!0===Wt(s)&&n((()=>c.$route.fullPath),(()=>{!0!==u.noRouteFullscreenExit&&m()})),n((()=>u.fullscreen),(e=>{v.value!==e&&p()})),n(v,(e=>{d("update:fullscreen",e),d("fullscreen",e)})),y((()=>{l=document.createElement("span")})),r((()=>{!0===u.fullscreen&&f()})),i(m),Object.assign(c,{toggleFullscreen:p,setFullscreen:f,exitFullscreen:m}),{inFullscreen:v,toggleFullscreen:p}}var So=["top","right","bottom","left"],ko=["regular","flat","outline","push","unelevated"],qo=Ze({name:"QCarousel",props:{...Et,...go,...wo,transitionPrev:{type:String,default:"fade"},transitionNext:{type:String,default:"fade"},height:String,padding:Boolean,controlColor:String,controlTextColor:String,controlType:{type:String,validator:e=>ko.includes(e),default:"flat"},autoplay:[Number,Boolean],arrows:Boolean,prevIcon:String,nextIcon:String,navigation:Boolean,navigationPosition:{type:String,validator:e=>So.includes(e)},navigationIcon:String,navigationActiveIcon:String,thumbnails:Boolean},emits:[...xo,...ho],setup(e,{slots:l}){let s,{proxy:{$q:u}}=a(),d=Pt(e,u),c=null,{updatePanelsList:v,getPanelContent:p,panelDirectives:f,goToPanel:m,previousPanel:g,nextPanel:h,getEnabledPanels:b,panelIndex:y}=bo(),{inFullscreen:w}=_o(),x=o((()=>!0!==w.value&&void 0!==e.height?{height:e.height}:{})),_=o((()=>!0===e.vertical?"vertical":"horizontal")),S=o((()=>`q-carousel q-panel-parent q-carousel--with${!0===e.padding?"":"out"}-padding`+(!0===w.value?" fullscreen":"")+(!0===d.value?" q-carousel--dark q-dark":"")+(!0===e.arrows?` q-carousel--arrows-${_.value}`:"")+(!0===e.navigation?` q-carousel--navigation-${$.value}`:""))),k=o((()=>{let t=[e.prevIcon||u.iconSet.carousel[!0===e.vertical?"up":"left"],e.nextIcon||u.iconSet.carousel[!0===e.vertical?"down":"right"]];return!1===e.vertical&&!0===u.lang.rtl?t.reverse():t})),q=o((()=>e.navigationIcon||u.iconSet.carousel.navigationIcon)),C=o((()=>e.navigationActiveIcon||q.value)),$=o((()=>e.navigationPosition||(!0===e.vertical?"right":"bottom"))),M=o((()=>({color:e.controlColor,textColor:e.controlTextColor,round:!0,[e.controlType]:!0,dense:!0})));function T(){let t=!0===Qe(e.autoplay)?Math.abs(e.autoplay):5e3;null!==c&&clearTimeout(c),c=setTimeout((()=>{c=null,t>=0?h():g()}),t)}function B(l,a){return t("div",{class:`q-carousel__control q-carousel__navigation no-wrap absolute flex q-carousel__navigation--${l} q-carousel__navigation--${$.value}`+(void 0!==e.controlColor?` text-${e.controlColor}`:"")},[t("div",{class:"q-carousel__navigation-inner flex flex-center no-wrap"},b().map(a))])}return n((()=>e.modelValue),(()=>{e.autoplay&&T()})),n((()=>e.autoplay),(e=>{e?T():null!==c&&(clearTimeout(c),c=null)})),r((()=>{e.autoplay&&T()})),i((()=>{null!==c&&clearTimeout(c)})),()=>(s=v(l),t("div",{class:S.value,style:x.value},[ht("div",{class:"q-carousel__slides-container"},p(),"sl-cont",e.swipeable,(()=>f.value))].concat(function(){let a=[];if(!0===e.navigation){let e=void 0!==l["navigation-icon"]?l["navigation-icon"]:e=>t($l,{key:"nav"+e.name,class:`q-carousel__navigation-icon q-carousel__navigation-icon--${!0===e.active?"":"in"}active`,...e.btnProps,onClick:e.onClick}),o=s-1;a.push(B("buttons",((t,l)=>{let a=t.props.name,n=y.value===l;return e({index:l,maxIndex:o,name:a,active:n,btnProps:{icon:!0===n?C.value:q.value,size:"sm",...M.value},onClick:()=>{m(a)}})})))}else if(!0===e.thumbnails){let l=void 0!==e.controlColor?` text-${e.controlColor}`:"";a.push(B("thumbnails",(a=>{let o=a.props;return t("img",{key:"tmb#"+o.name,class:`q-carousel__thumbnail q-carousel__thumbnail--${o.name===e.modelValue?"":"in"}active`+l,src:o.imgSrc||o["img-src"],onClick:()=>{m(o.name)}})})))}return!0===e.arrows&&y.value>=0&&((!0===e.infinite||y.value>0)&&a.push(t("div",{key:"prev",class:`q-carousel__control q-carousel__arrow q-carousel__prev-arrow q-carousel__prev-arrow--${_.value} absolute flex flex-center`},[t($l,{icon:k.value[0],...M.value,onClick:g})])),(!0===e.infinite||y.value<s-1)&&a.push(t("div",{key:"next",class:`q-carousel__control q-carousel__arrow q-carousel__next-arrow q-carousel__next-arrow--${_.value} absolute flex flex-center`},[t($l,{icon:k.value[1],...M.value,onClick:h})]))),mt(l.control,a)}())))}}),Co=Ze({name:"QCarouselSlide",props:{...fo,imgSrc:String},setup(e,{slots:l}){let a=o((()=>e.imgSrc?{backgroundImage:`url("${e.imgSrc}")`}:{}));return()=>t("div",{class:"q-carousel__slide",style:a.value},pt(l.default))}}),$o=Ze({name:"QCarouselControl",props:{position:{type:String,default:"bottom-right",validator:e=>["top-right","top-left","bottom-right","bottom-left","top","right","bottom","left"].includes(e)},offset:{type:Array,default:()=>[18,18],validator:e=>2===e.length}},setup(e,{slots:l}){let a=o((()=>`q-carousel__control absolute absolute-${e.position}`)),n=o((()=>({margin:`${e.offset[1]}px ${e.offset[0]}px`})));return()=>t("div",{class:a.value,style:n.value},pt(l.default))}}),Mo=Ze({name:"QChatMessage",props:{sent:Boolean,label:String,bgColor:String,textColor:String,name:String,avatar:String,text:Array,stamp:String,size:String,labelHtml:Boolean,nameHtml:Boolean,textHtml:Boolean,stampHtml:Boolean},setup(e,{slots:l}){let a=o((()=>!0===e.sent?"sent":"received")),n=o((()=>`q-message-text-content q-message-text-content--${a.value}`+(void 0!==e.textColor?` text-${e.textColor}`:""))),i=o((()=>`q-message-text q-message-text--${a.value}`+(void 0!==e.bgColor?` text-${e.bgColor}`:""))),r=o((()=>"q-message-container row items-end no-wrap"+(!0===e.sent?" reverse":""))),s=o((()=>void 0!==e.size?`col-${e.size}`:"")),u=o((()=>({msg:!0===e.textHtml?"innerHTML":"textContent",stamp:!0===e.stampHtml?"innerHTML":"textContent",name:!0===e.nameHtml?"innerHTML":"textContent",label:!0===e.labelHtml?"innerHTML":"textContent"})));function d(a){return void 0!==l.stamp?[a,t("div",{class:"q-message-stamp"},l.stamp())]:e.stamp?[a,t("div",{class:"q-message-stamp",[u.value.stamp]:e.stamp})]:[a]}function c(e,l){let a=!0===l?e.length>1?e=>e:e=>t("div",[e]):e=>t("div",{[u.value.msg]:e});return e.map(((e,l)=>t("div",{key:l,class:i.value},[t("div",{class:n.value},d(a(e)))])))}return()=>{let o=[];void 0!==l.avatar?o.push(l.avatar()):void 0!==e.avatar&&o.push(t("img",{class:`q-message-avatar q-message-avatar--${a.value}`,src:e.avatar,"aria-hidden":"true"}));let n=[];void 0!==l.name?n.push(t("div",{class:`q-message-name q-message-name--${a.value}`},l.name())):void 0!==e.name&&n.push(t("div",{class:`q-message-name q-message-name--${a.value}`,[u.value.name]:e.name})),void 0!==l.default?n.push(c(Ut(l.default()),!0)):void 0!==e.text&&n.push(c(e.text)),o.push(t("div",{class:s.value},n));let i=[];return void 0!==l.label?i.push(t("div",{class:"q-message-label"},l.label())):void 0!==e.label&&i.push(t("div",{class:"q-message-label",[u.value.label]:e.label})),i.push(t("div",{class:r.value},o)),t("div",{class:`q-message q-message-${a.value}`},i)}}});function To(l,a){let n=e(null);return{refocusTargetEl:o((()=>!0===l.disable?null:t("span",{ref:n,class:"no-outline",tabindex:-1}))),refocusTarget:function(e){let t=a.value;void 0!==e&&0===e.type.indexOf("key")?null!==t&&document.activeElement!==t&&!0===t.contains(document.activeElement)&&t.focus():null!==n.value&&(void 0===e||null!==t&&!0===t.contains(e.target))&&n.value.focus()}}}var Bo={xs:30,sm:35,md:40,lg:50,xl:60},Lo={...Et,...ct,...Za,modelValue:{required:!0,default:null},val:{},trueValue:{default:!0},falseValue:{default:!1},indeterminateValue:{default:null},checkedIcon:String,uncheckedIcon:String,indeterminateIcon:String,toggleOrder:{type:String,validator:e=>"tf"===e||"ft"===e},toggleIndeterminate:Boolean,label:String,leftLabel:Boolean,color:String,keepColor:Boolean,dense:Boolean,disable:Boolean,tabindex:[String,Number]},zo=["update:modelValue"];function Vo(l,n){let{props:i,slots:r,emit:s,proxy:u}=a(),{$q:d}=u,c=Pt(i,d),v=e(null),{refocusTargetEl:f,refocusTarget:m}=To(i,v),g=vt(i,Bo),h=o((()=>void 0!==i.val&&Array.isArray(i.modelValue))),b=o((()=>{let e=p(i.val);return!0===h.value?i.modelValue.findIndex((t=>p(t)===e)):-1})),y=o((()=>!0===h.value?-1!==b.value:p(i.modelValue)===p(i.trueValue))),w=o((()=>!0===h.value?-1===b.value:p(i.modelValue)===p(i.falseValue))),x=o((()=>!1===y.value&&!1===w.value)),_=o((()=>!0===i.disable?-1:i.tabindex||0)),S=o((()=>`q-${l} cursor-pointer no-outline row inline no-wrap items-center`+(!0===i.disable?" disabled":"")+(!0===c.value?` q-${l}--dark`:"")+(!0===i.dense?` q-${l}--dense`:"")+(!0===i.leftLabel?" reverse":""))),k=o((()=>{let e=!0===y.value?"truthy":!0===w.value?"falsy":"indet",t=void 0===i.color||!0!==i.keepColor&&("toggle"===l?!0!==y.value:!0===w.value)?"":` text-${i.color}`;return`q-${l}__inner relative-position non-selectable q-${l}__inner--${e}${t}`})),q=Ja(o((()=>{let e={type:"checkbox"};return void 0!==i.name&&Object.assign(e,{".checked":y.value,"^checked":!0===y.value?"checked":void 0,name:i.name,value:!0===h.value?i.val:i.trueValue}),e}))),C=o((()=>{let e={tabindex:_.value,role:"toggle"===l?"switch":"checkbox","aria-label":i.label,"aria-checked":!0===x.value?"mixed":!0===y.value?"true":"false"};return!0===i.disable&&(e["aria-disabled"]="true"),e}));function $(e){void 0!==e&&(ae(e),m(e)),!0!==i.disable&&s("update:modelValue",function(){if(!0===h.value){if(!0===y.value){let e=i.modelValue.slice();return e.splice(b.value,1),e}return i.modelValue.concat([i.val])}if(!0===y.value){if("ft"!==i.toggleOrder||!1===i.toggleIndeterminate)return i.falseValue}else{if(!0!==w.value)return"ft"!==i.toggleOrder?i.trueValue:i.falseValue;if("ft"===i.toggleOrder||!1===i.toggleIndeterminate)return i.trueValue}return i.indeterminateValue}(),e)}function M(e){(13===e.keyCode||32===e.keyCode)&&ae(e)}function T(e){(13===e.keyCode||32===e.keyCode)&&$(e)}let B=n(y,x);return Object.assign(u,{toggle:$}),()=>{let e=B();!0!==i.disable&&q(e,"unshift",` q-${l}__native absolute q-ma-none q-pa-none`);let a=[t("div",{class:k.value,style:g.value,"aria-hidden":"true"},e)];null!==f.value&&a.push(f.value);let o=void 0!==i.label?mt(r.default,[i.label]):pt(r.default);return void 0!==o&&a.push(t("div",{class:`q-${l}__label q-anchor--skip`},o)),t("div",{ref:v,class:S.value,...C.value,onClick:$,onKeydown:M,onKeyup:T},a)}}var Oo=t("div",{key:"svg",class:"q-checkbox__bg absolute"},[t("svg",{class:"q-checkbox__svg fit absolute-full",viewBox:"0 0 24 24"},[t("path",{class:"q-checkbox__truthy",fill:"none",d:"M1.73,12.91 8.1,19.28 22.79,4.59"}),t("path",{class:"q-checkbox__indet",d:"M4,14H20V10H4"})])]),Ao=Ze({name:"QCheckbox",props:Lo,emits:zo,setup:e=>Vo("checkbox",(function(l,a){let n=o((()=>(!0===l.value?e.checkedIcon:!0===a.value?e.indeterminateIcon:e.uncheckedIcon)||null));return()=>null!==n.value?[t("div",{key:"icon",class:"q-checkbox__icon-container absolute-full flex flex-center no-wrap"},[t(zt,{class:"q-checkbox__icon",name:n.value})])]:[Oo]}))}),Eo={xs:8,sm:10,md:14,lg:20,xl:24},Po=Ze({name:"QChip",props:{...Et,...ct,dense:Boolean,icon:String,iconRight:String,iconRemove:String,iconSelected:String,label:[String,Number],color:String,textColor:String,modelValue:{type:Boolean,default:!0},selected:{type:Boolean,default:null},square:Boolean,outline:Boolean,clickable:Boolean,removable:Boolean,removeAriaLabel:String,tabindex:[String,Number],disable:Boolean,ripple:{type:[Boolean,Object],default:!0}},emits:["update:modelValue","update:selected","remove","click"],setup(e,{slots:l,emit:n}){let{proxy:{$q:i}}=a(),r=Pt(e,i),s=vt(e,Eo),u=o((()=>!0===e.selected||void 0!==e.icon)),d=o((()=>!0===e.selected?e.iconSelected||i.iconSet.chip.selected:e.icon)),c=o((()=>e.iconRemove||i.iconSet.chip.remove)),v=o((()=>!1===e.disable&&(!0===e.clickable||null!==e.selected))),p=o((()=>{let t=!0===e.outline&&e.color||e.textColor;return"q-chip row inline no-wrap items-center"+(!1===e.outline&&void 0!==e.color?` bg-${e.color}`:"")+(t?` text-${t} q-chip--colored`:"")+(!0===e.disable?" disabled":"")+(!0===e.dense?" q-chip--dense":"")+(!0===e.outline?" q-chip--outline":"")+(!0===e.selected?" q-chip--selected":"")+(!0===v.value?" q-chip--clickable cursor-pointer non-selectable q-hoverable":"")+(!0===e.square?" q-chip--square":"")+(!0===r.value?" q-chip--dark q-dark":"")})),f=o((()=>{let t=!0===e.disable?{tabindex:-1,"aria-disabled":"true"}:{tabindex:e.tabindex||0};return{chip:t,remove:{...t,role:"button","aria-hidden":"false","aria-label":e.removeAriaLabel||i.lang.label.remove}}}));function m(e){13===e.keyCode&&g(e)}function g(t){e.disable||(n("update:selected",!e.selected),n("click",t))}function h(t){(void 0===t.keyCode||13===t.keyCode)&&(ae(t),!1===e.disable&&(n("update:modelValue",!1),n("remove")))}return()=>{if(!1===e.modelValue)return;let a={class:p.value,style:s.value};return!0===v.value&&Object.assign(a,f.value.chip,{onClick:g,onKeyup:m}),ht("div",a,function(){let a=[];!0===v.value&&a.push(t("div",{class:"q-focus-helper"})),!0===u.value&&a.push(t(zt,{class:"q-chip__icon q-chip__icon--left",name:d.value}));let o=void 0!==e.label?[t("div",{class:"ellipsis"},[e.label])]:void 0;return a.push(t("div",{class:"q-chip__content col row no-wrap items-center q-anchor--skip"},gt(l.default,o))),e.iconRight&&a.push(t(zt,{class:"q-chip__icon q-chip__icon--right",name:e.iconRight})),!0===e.removable&&a.push(t(zt,{class:"q-chip__icon q-chip__icon--remove cursor-pointer",name:c.value,...f.value.remove,onClick:h,onKeyup:h})),a}(),"ripple",!1!==e.ripple&&!0!==e.disable,(()=>[[fl,e.ripple]]))}}}),Fo={...ct,min:{type:Number,default:0},max:{type:Number,default:100},color:String,centerColor:String,trackColor:String,fontSize:String,rounded:Boolean,thickness:{type:Number,default:.2,validator:e=>e>=0&&e<=1},angle:{type:Number,default:0},showValue:Boolean,reverse:Boolean,instantFeedback:Boolean},Ro=100*Math.PI,No=Math.round(1e3*Ro)/1e3,Ho=Ze({name:"QCircularProgress",props:{...Fo,value:{type:Number,default:0},animationSpeed:{type:[String,Number],default:600},indeterminate:Boolean},setup(e,{slots:l}){let{proxy:{$q:n}}=a(),i=vt(e),r=o((()=>{let t=(!0===n.lang.rtl?-1:1)*e.angle;return{transform:e.reverse!==(!0===n.lang.rtl)?`scale3d(-1, 1, 1) rotate3d(0, 0, 1, ${-90-t}deg)`:`rotate3d(0, 0, 1, ${t-90}deg)`}})),s=o((()=>!0!==e.instantFeedback&&!0!==e.indeterminate?{transition:`stroke-dashoffset ${e.animationSpeed}ms ease 0s, stroke ${e.animationSpeed}ms ease`}:"")),u=o((()=>100/(1-e.thickness/2))),d=o((()=>`${u.value/2} ${u.value/2} ${u.value} ${u.value}`)),c=o((()=>tt(e.value,e.min,e.max))),v=o((()=>e.max-e.min)),p=o((()=>e.thickness/2*u.value)),f=o((()=>{let t=(e.max-c.value)/v.value,l=!0===e.rounded&&c.value<e.max&&t<.25?p.value/2*(1-t/.25):0;return Ro*t+l}));function m({thickness:e,offset:l,color:a,cls:o,rounded:n}){return t("circle",{class:"q-circular-progress__"+o+(void 0!==a?` text-${a}`:""),style:s.value,fill:"transparent",stroke:"currentColor","stroke-width":e,"stroke-dasharray":No,"stroke-dashoffset":l,"stroke-linecap":n,cx:u.value,cy:u.value,r:50})}return()=>{let a=[];void 0!==e.centerColor&&"transparent"!==e.centerColor&&a.push(t("circle",{class:`q-circular-progress__center text-${e.centerColor}`,fill:"currentColor",r:50-p.value/2,cx:u.value,cy:u.value})),void 0!==e.trackColor&&"transparent"!==e.trackColor&&a.push(m({cls:"track",thickness:p.value,offset:0,color:e.trackColor})),a.push(m({cls:"circle",thickness:p.value,offset:f.value,color:e.color,rounded:!0===e.rounded?"round":void 0}));let o=[t("svg",{class:"q-circular-progress__svg",style:r.value,viewBox:d.value,"aria-hidden":"true"},a)];return!0===e.showValue&&o.push(t("div",{class:"q-circular-progress__text absolute-full row flex-center content-center",style:{fontSize:e.fontSize}},void 0!==l.default?l.default():[t("div",c.value)])),t("div",{class:`q-circular-progress q-circular-progress--${!0===e.indeterminate?"in":""}determinate`,style:i.value,role:"progressbar","aria-valuemin":e.min,"aria-valuemax":e.max,"aria-valuenow":!0===e.indeterminate?void 0:c.value},gt(l.internal,o))}}});function Io(e,t,l){let a,o=ee(e),n=o.left-t.event.x,i=o.top-t.event.y,r=Math.abs(n),s=Math.abs(i),u=t.direction;!0===u.horizontal&&!0!==u.vertical?a=n<0?"left":"right":!0!==u.horizontal&&!0===u.vertical?a=i<0?"up":"down":!0===u.up&&i<0?(a="up",r>s&&(!0===u.left&&n<0?a="left":!0===u.right&&n>0&&(a="right"))):!0===u.down&&i>0?(a="down",r>s&&(!0===u.left&&n<0?a="left":!0===u.right&&n>0&&(a="right"))):!0===u.left&&n<0?(a="left",r<s&&(!0===u.up&&i<0?a="up":!0===u.down&&i>0&&(a="down"))):!0===u.right&&n>0&&(a="right",r<s&&(!0===u.up&&i<0?a="up":!0===u.down&&i>0&&(a="down")));let d=!1;if(void 0===a&&!1===l){if(!0===t.event.isFirst||void 0===t.event.lastDir)return{};a=t.event.lastDir,d=!0,"left"===a||"right"===a?(o.left-=n,r=0,n=0):(o.top-=i,s=0,i=0)}return{synthetic:d,payload:{evt:e,touch:!0!==t.event.mouse,mouse:!0===t.event.mouse,position:o,direction:a,isFirst:t.event.isFirst,isFinal:!0===l,duration:Date.now()-t.event.time,distance:{x:r,y:s},offset:{x:n,y:i},delta:{x:o.left-t.event.lastX,y:o.top-t.event.lastY}}}}var jo=0,Do=Ge({name:"touch-pan",beforeMount(e,{value:t,modifiers:l}){if(!0!==l.mouse&&!0!==W.has.touch)return;function a(e,t){!0===l.mouse&&!0===t?ae(e):(!0===l.stop&&te(e),!0===l.prevent&&le(e))}let o={uid:"qvtp_"+jo++,handler:t,modifiers:l,direction:ro(l),noop:G,mouseStart(e){uo(e,o)&&J(e)&&(ne(o,"temp",[[document,"mousemove","move","notPassiveCapture"],[document,"mouseup","end","passiveCapture"]]),o.start(e,!0))},touchStart(e){if(uo(e,o)){let t=e.target;ne(o,"temp",[[t,"touchmove","move","notPassiveCapture"],[t,"touchcancel","end","passiveCapture"],[t,"touchend","end","passiveCapture"]]),o.start(e)}},start(t,a){if(!0===W.is.firefox&&oe(e,!0),o.lastEvt=t,!0===a||!0===l.stop){if(!0!==o.direction.all&&(!0!==a||!0!==o.modifiers.mouseAllDir&&!0!==o.modifiers.mousealldir)){let e=-1!==t.type.indexOf("mouse")?new MouseEvent(t.type,t):new TouchEvent(t.type,t);!0===t.defaultPrevented&&le(e),!0===t.cancelBubble&&te(e),Object.assign(e,{qKeyEvent:t.qKeyEvent,qClickOutside:t.qClickOutside,qAnchorHandled:t.qAnchorHandled,qClonedBy:void 0===t.qClonedBy?[o.uid]:t.qClonedBy.concat(o.uid)}),o.initialEvent={target:t.target,event:e}}te(t)}let{left:n,top:i}=ee(t);o.event={x:n,y:i,time:Date.now(),mouse:!0===a,detected:!1,isFirst:!0,isFinal:!1,lastX:n,lastY:i}},move(e){if(void 0===o.event)return;let t=ee(e),n=t.left-o.event.x,i=t.top-o.event.y;if(0===n&&0===i)return;o.lastEvt=e;let r=!0===o.event.mouse,s=()=>{let t;a(e,r),!0!==l.preserveCursor&&!0!==l.preservecursor&&(t=document.documentElement.style.cursor||"",document.documentElement.style.cursor="grabbing"),!0===r&&document.body.classList.add("no-pointer-events--children"),document.body.classList.add("non-selectable"),Tl(),o.styleCleanup=e=>{if(o.styleCleanup=void 0,void 0!==t&&(document.documentElement.style.cursor=t),document.body.classList.remove("non-selectable"),!0===r){let t=()=>{document.body.classList.remove("no-pointer-events--children")};void 0!==e?setTimeout((()=>{t(),e()}),50):t()}else void 0!==e&&e()}};if(!0===o.event.detected){!0!==o.event.isFirst&&a(e,o.event.mouse);let{payload:t,synthetic:l}=Io(e,o,!1);return void(void 0!==t&&(!1===o.handler(t)?o.end(e):(void 0===o.styleCleanup&&!0===o.event.isFirst&&s(),o.event.lastX=t.position.left,o.event.lastY=t.position.top,o.event.lastDir=!0===l?void 0:t.direction,o.event.isFirst=!1)))}if(!0===o.direction.all||!0===r&&(!0===o.modifiers.mouseAllDir||!0===o.modifiers.mousealldir))return s(),o.event.detected=!0,void o.move(e);let u=Math.abs(n),d=Math.abs(i);u!==d&&(!0===o.direction.horizontal&&u>d||!0===o.direction.vertical&&u<d||!0===o.direction.up&&u<d&&i<0||!0===o.direction.down&&u<d&&i>0||!0===o.direction.left&&u>d&&n<0||!0===o.direction.right&&u>d&&n>0?(o.event.detected=!0,o.move(e)):o.end(e,!0))},end(t,l){if(void 0!==o.event){if(ie(o,"temp"),!0===W.is.firefox&&oe(e,!1),!0===l)void 0!==o.styleCleanup&&o.styleCleanup(),!0!==o.event.detected&&void 0!==o.initialEvent&&o.initialEvent.target.dispatchEvent(o.initialEvent.event);else if(!0===o.event.detected){!0===o.event.isFirst&&o.handler(Io(void 0===t?o.lastEvt:t,o).payload);let{payload:e}=Io(void 0===t?o.lastEvt:t,o,!0),l=()=>{o.handler(e)};void 0!==o.styleCleanup?o.styleCleanup(l):l()}o.event=void 0,o.initialEvent=void 0,o.lastEvt=void 0}}};if(e.__qtouchpan=o,!0===l.mouse){let t=!0===l.mouseCapture||!0===l.mousecapture?"Capture":"";ne(o,"main",[[e,"mousedown","mouseStart",`passive${t}`]])}!0===W.has.touch&&ne(o,"main",[[e,"touchstart","touchStart","passive"+(!0===l.capture?"Capture":"")],[e,"touchmove","noop","notPassiveCapture"]])},updated(e,t){let l=e.__qtouchpan;void 0!==l&&(t.oldValue!==t.value&&("function"!=typeof value&&l.end(),l.handler=t.value),l.direction=ro(t.modifiers))},beforeUnmount(e){let t=e.__qtouchpan;void 0!==t&&(void 0!==t.event&&t.end(),ie(t,"main"),ie(t,"temp"),!0===W.is.firefox&&oe(e,!1),void 0!==t.styleCleanup&&t.styleCleanup(),delete e.__qtouchpan)}}),Qo="q-slider__marker-labels",Uo=e=>({value:e}),Wo=({marker:e})=>t("div",{key:e.value,style:e.style,class:e.classes},e.label),Ko=[34,37,40,33,39,38],Yo={...Et,...Za,min:{type:Number,default:0},max:{type:Number,default:100},innerMin:Number,innerMax:Number,step:{type:Number,default:1,validator:e=>e>=0},snap:Boolean,vertical:Boolean,reverse:Boolean,hideSelection:Boolean,color:String,markerLabelsClass:String,label:Boolean,labelColor:String,labelTextColor:String,labelAlways:Boolean,switchLabelSide:Boolean,markers:[Boolean,Number],markerLabels:[Boolean,Array,Object,Function],switchMarkerLabelsSide:Boolean,trackImg:String,trackColor:String,innerTrackImg:String,innerTrackColor:String,selectionColor:String,selectionImg:String,thumbSize:{type:String,default:"20px"},trackSize:{type:String,default:"4px"},disable:Boolean,readonly:Boolean,dense:Boolean,tabindex:[String,Number],thumbColor:String,thumbPath:{type:String,default:"M 4, 10 a 6,6 0 1,0 12,0 a 6,6 0 1,0 -12,0"}},Xo=["pan","update:modelValue","change"];function Zo({updateValue:l,updatePosition:n,getDragging:r,formAttrs:s}){let{props:u,emit:d,slots:c,proxy:{$q:v}}=a(),p=Pt(u,v),f=Ja(s),m=e(!1),g=e(!1),h=e(!1),b=e(!1),y=o((()=>!0===u.vertical?"--v":"--h")),w=o((()=>"-"+(!0===u.switchLabelSide?"switched":"standard"))),x=o((()=>!0===u.vertical?!0===u.reverse:u.reverse!==(!0===v.lang.rtl))),_=o((()=>!0===isNaN(u.innerMin)||u.innerMin<u.min?u.min:u.innerMin)),S=o((()=>!0===isNaN(u.innerMax)||u.innerMax>u.max?u.max:u.innerMax)),k=o((()=>!0!==u.disable&&!0!==u.readonly&&_.value<S.value)),q=o((()=>{if(0===u.step)return e=>e;let e=(String(u.step).trim().split(".")[1]||"").length;return t=>parseFloat(t.toFixed(e))})),C=o((()=>0===u.step?1:u.step)),$=o((()=>!0===k.value?u.tabindex||0:-1)),M=o((()=>u.max-u.min)),T=o((()=>S.value-_.value)),B=o((()=>Z(_.value))),L=o((()=>Z(S.value))),z=o((()=>!0===u.vertical?!0===x.value?"bottom":"top":!0===x.value?"right":"left")),V=o((()=>!0===u.vertical?"height":"width")),O=o((()=>!0===u.vertical?"width":"height")),A=o((()=>!0===u.vertical?"vertical":"horizontal")),E=o((()=>{let e={role:"slider","aria-valuemin":_.value,"aria-valuemax":S.value,"aria-orientation":A.value,"data-step":u.step};return!0===u.disable?e["aria-disabled"]="true":!0===u.readonly&&(e["aria-readonly"]="true"),e})),P=o((()=>`q-slider q-slider${y.value} q-slider--${!0===m.value?"":"in"}active inline no-wrap `+(!0===u.vertical?"row":"column")+(!0===u.disable?" disabled":" q-slider--enabled"+(!0===k.value?" q-slider--editable":""))+("both"===h.value?" q-slider--focus":"")+(u.label||!0===u.labelAlways?" q-slider--label":"")+(!0===u.labelAlways?" q-slider--label-always":"")+(!0===p.value?" q-slider--dark":"")+(!0===u.dense?" q-slider--dense q-slider--dense"+y.value:"")));function F(e){let t="q-slider__"+e;return`${t} ${t}${y.value} ${t}${y.value}${w.value}`}function R(e){let t="q-slider__"+e;return`${t} ${t}${y.value}`}let N=o((()=>{let e=u.selectionColor||u.color;return"q-slider__selection absolute"+(void 0!==e?` text-${e}`:"")})),H=o((()=>R("markers")+" absolute overflow-hidden")),I=o((()=>R("track-container"))),j=o((()=>F("pin"))),D=o((()=>F("label"))),Q=o((()=>F("text-container"))),U=o((()=>F("marker-labels-container")+(void 0!==u.markerLabelsClass?` ${u.markerLabelsClass}`:""))),W=o((()=>"q-slider__track relative-position no-outline"+(void 0!==u.trackColor?` bg-${u.trackColor}`:""))),K=o((()=>{let e={[O.value]:u.trackSize};return void 0!==u.trackImg&&(e.backgroundImage=`url(${u.trackImg}) !important`),e})),Y=o((()=>"q-slider__inner absolute"+(void 0!==u.innerTrackColor?` bg-${u.innerTrackColor}`:""))),X=o((()=>{let e=L.value-B.value,t={[z.value]:100*B.value+"%",[V.value]:0===e?"2px":100*e+"%"};return void 0!==u.innerTrackImg&&(t.backgroundImage=`url(${u.innerTrackImg}) !important`),t}));function Z(e){return 0===M.value?0:(e-u.min)/M.value}let G=o((()=>!0===Qe(u.markers)?u.markers:C.value)),J=o((()=>{let e=[],t=G.value,l=u.max,a=u.min;do{e.push(a),a+=t}while(a<l);return e.push(l),e})),te=o((()=>{let e=` ${Qo}${y.value}-`;return Qo+`${e}${!0===u.switchMarkerLabelsSide?"switched":"standard"}${e}${!0===x.value?"rtl":"ltr"}`})),le=o((()=>!1===u.markerLabels?null:function(e){if(!1===e)return null;if(!0===e)return J.value.map(Uo);if("function"==typeof e)return J.value.map((t=>{let l=e(t);return!0===je(l)?{...l,value:t}:{value:t,label:l}}));let t=({value:e})=>e>=u.min&&e<=u.max;return!0===Array.isArray(e)?e.map((e=>!0===je(e)?e:{value:e})).filter(t):Object.keys(e).map((t=>{let l=e[t],a=Number(t);return!0===je(l)?{...l,value:a}:{value:a,label:l}})).filter(t)}(u.markerLabels).map(((e,t)=>({index:t,value:e.value,label:e.label||e.value,classes:te.value+(void 0!==e.classes?" "+e.classes:""),style:{...ne(e.value),...e.style||{}}}))))),ae=o((()=>({markerList:le.value,markerMap:ie.value,classes:te.value,getStyle:ne}))),oe=o((()=>{let e=0===T.value?"2px":100*G.value/T.value;return{...X.value,backgroundSize:!0===u.vertical?`2px ${e}%`:`${e}% 2px`}}));function ne(e){return{[z.value]:100*(e-u.min)/M.value+"%"}}let ie=o((()=>{if(!1===u.markerLabels)return null;let e={};return le.value.forEach((t=>{e[t.value]=t})),e}));let re=o((()=>[[Do,se,void 0,{[A.value]:!0,prevent:!0,stop:!0,mouse:!0,mouseAllDir:!0}]]));function se(e){!0===e.isFinal?(void 0!==b.value&&(n(e.evt),!0===e.touch&&l(!0),b.value=void 0,d("pan","end")),m.value=!1,h.value=!1):!0===e.isFirst?(b.value=r(e.evt),n(e.evt),l(),m.value=!0,d("pan","start")):(n(e.evt),l())}function ue(){h.value=!1}function de(){g.value=!1,m.value=!1,l(!0),ue(),document.removeEventListener("mouseup",de,!0)}return i((()=>{document.removeEventListener("mouseup",de,!0)})),{state:{active:m,focus:h,preventFocus:g,dragging:b,editable:k,classes:P,tabindex:$,attributes:E,roundValueFn:q,keyStep:C,trackLen:M,innerMin:_,innerMinRatio:B,innerMax:S,innerMaxRatio:L,positionProp:z,sizeProp:V,isReversed:x},methods:{onActivate:function(e){n(e,r(e)),l(),g.value=!0,m.value=!0,document.addEventListener("mouseup",de,!0)},onMobileClick:function(e){n(e,r(e)),l(!0)},onBlur:ue,onKeyup:function(e){Ko.includes(e.keyCode)&&l(!0)},getContent:function(e,l,a,o){let n=[];"transparent"!==u.innerTrackColor&&n.push(t("div",{key:"inner",class:Y.value,style:X.value})),"transparent"!==u.selectionColor&&n.push(t("div",{key:"selection",class:N.value,style:e.value})),!1!==u.markers&&n.push(t("div",{key:"marker",class:H.value,style:oe.value})),o(n);let i=[ht("div",{key:"trackC",class:I.value,tabindex:l.value,...a.value},[t("div",{class:W.value,style:K.value},n)],"slide",k.value,(()=>re.value))];if(!1!==u.markerLabels){i[!0===u.switchMarkerLabelsSide?"unshift":"push"](t("div",{key:"markerL",class:U.value},function(){if(void 0!==c["marker-label-group"])return c["marker-label-group"](ae.value);let e=c["marker-label"]||Wo;return le.value.map((t=>e({marker:t,...ae.value})))}()))}return i},getThumbRenderFn:function(e){let l=o((()=>!1!==g.value||h.value!==e.focusValue&&"both"!==h.value?"":" q-slider--focus")),a=o((()=>`q-slider__thumb q-slider__thumb${y.value} q-slider__thumb${y.value}-${!0===x.value?"rtl":"ltr"} absolute non-selectable`+l.value+(void 0!==e.thumbColor.value?` text-${e.thumbColor.value}`:""))),n=o((()=>({width:u.thumbSize,height:u.thumbSize,[z.value]:100*e.ratio.value+"%",zIndex:h.value===e.focusValue?2:void 0}))),i=o((()=>void 0!==e.labelColor.value?` text-${e.labelColor.value}`:"")),r=o((()=>function(e){if(!0===u.vertical)return null;let t=v.lang.rtl!==u.reverse?1-e:e;return{transform:`translateX(calc(${2*t-1} * ${u.thumbSize} / 2 + ${50-100*t}%))`}}(e.ratio.value))),s=o((()=>"q-slider__text"+(void 0!==e.labelTextColor.value?` text-${e.labelTextColor.value}`:"")));return()=>{let l=[t("svg",{class:"q-slider__thumb-shape absolute-full",viewBox:"0 0 20 20","aria-hidden":"true"},[t("path",{d:u.thumbPath})]),t("div",{class:"q-slider__focus-ring fit"})];return(!0===u.label||!0===u.labelAlways)&&(l.push(t("div",{class:j.value+" absolute fit no-pointer-events"+i.value},[t("div",{class:D.value,style:{minWidth:u.thumbSize}},[t("div",{class:Q.value,style:r.value},[t("span",{class:s.value},e.label.value)])])])),void 0!==u.name&&!0!==u.disable&&f(l,"push")),t("div",{class:a.value,style:n.value,...e.getNodeData()},l)}},convertRatioToModel:function(e){let{min:t,max:l,step:a}=u,o=t+e*(l-t);if(a>0){let e=(o-_.value)%a;o+=(Math.abs(e)>=a/2?(e<0?-1:1)*a:0)-e}return o=q.value(o),tt(o,_.value,S.value)},convertModelToRatio:Z,getDraggingRatio:function(e,t){let l=ee(e),a=!0===u.vertical?tt((l.top-t.top)/t.height,0,1):tt((l.left-t.left)/t.width,0,1);return tt(!0===x.value?1-a:a,B.value,L.value)}}}}var Go=()=>({}),Jo=Ze({name:"QSlider",props:{...Yo,modelValue:{required:!0,default:null,validator:e=>"number"==typeof e||null===e},labelValue:[String,Number]},emits:Xo,setup(l,{emit:i}){let{proxy:{$q:r}}=a(),{state:s,methods:u}=Zo({updateValue:y,updatePosition:function(e,t=s.dragging.value){let a=u.getDraggingRatio(e,t);v.value=u.convertRatioToModel(a),c.value=!0!==l.snap||0===l.step?a:u.convertModelToRatio(v.value)},getDragging:function(){return d.value.getBoundingClientRect()},formAttrs:Ga(l)}),d=e(null),c=e(0),v=e(0);function p(){v.value=null===l.modelValue?s.innerMin.value:tt(l.modelValue,s.innerMin.value,s.innerMax.value)}n((()=>`${l.modelValue}|${s.innerMin.value}|${s.innerMax.value}`),p),p();let f=o((()=>u.convertModelToRatio(v.value))),m=o((()=>!0===s.active.value?c.value:f.value)),g=o((()=>{let e={[s.positionProp.value]:100*s.innerMinRatio.value+"%",[s.sizeProp.value]:100*(m.value-s.innerMinRatio.value)+"%"};return void 0!==l.selectionImg&&(e.backgroundImage=`url(${l.selectionImg}) !important`),e})),h=u.getThumbRenderFn({focusValue:!0,getNodeData:Go,ratio:m,label:o((()=>void 0!==l.labelValue?l.labelValue:v.value)),thumbColor:o((()=>l.thumbColor||l.color)),labelColor:o((()=>l.labelColor)),labelTextColor:o((()=>l.labelTextColor))}),b=o((()=>!0!==s.editable.value?{}:!0===r.platform.is.mobile?{onClick:u.onMobileClick}:{onMousedown:u.onActivate,onFocus:w,onBlur:u.onBlur,onKeydown:x,onKeyup:u.onKeyup}));function y(e){v.value!==l.modelValue&&i("update:modelValue",v.value),!0===e&&i("change",v.value)}function w(){s.focus.value=!0}function x(e){if(!Ko.includes(e.keyCode))return;ae(e);let t=([34,33].includes(e.keyCode)?10:1)*s.keyStep.value,a=([34,37,40].includes(e.keyCode)?-1:1)*(!0===s.isReversed.value?-1:1)*(!0===l.vertical?-1:1)*t;v.value=tt(s.roundValueFn.value(v.value+a),s.innerMin.value,s.innerMax.value),y()}return()=>{let e=u.getContent(g,s.tabindex,b,(e=>{e.push(h())}));return t("div",{ref:d,class:s.classes.value+(null===l.modelValue?" q-slider--no-value":""),...s.attributes.value,"aria-valuenow":l.modelValue},e)}}});function en(){let t=e(!D.value);return!1===t.value&&r((()=>{t.value=!0})),{isHydrated:t}}var tn=typeof ResizeObserver<"u",ln=!0===tn?{}:{style:"display:block;position:absolute;top:0;left:0;right:0;bottom:0;height:100%;width:100%;overflow:hidden;pointer-events:none;z-index:-1;",url:"about:blank"},an=Ze({name:"QResizeObserver",props:{debounce:{type:[String,Number],default:100}},emits:["resize"],setup(e,{emit:l}){let o,n=null,s={width:-1,height:-1};function u(t){!0===t||0===e.debounce||"0"===e.debounce?d():null===n&&(n=setTimeout(d,e.debounce))}function d(){if(null!==n&&(clearTimeout(n),n=null),o){let{offsetWidth:e,offsetHeight:t}=o;(e!==s.width||t!==s.height)&&(s={width:e,height:t},l("resize",s))}}let{proxy:c}=a();if(c.trigger=u,!0===tn){let e,t=l=>{o=c.$el.parentNode,o?(e=new ResizeObserver(u),e.observe(o),d()):!0!==l&&m((()=>{t(!0)}))};return r((()=>{t()})),i((()=>{null!==n&&clearTimeout(n),void 0!==e&&(void 0!==e.disconnect?e.disconnect():o&&e.unobserve(o))})),G}{let e,l=function(){null!==n&&(clearTimeout(n),n=null),void 0!==e&&(void 0!==e.removeEventListener&&e.removeEventListener("resize",u,Z.passive),e=void 0)},a=function(){l(),o&&o.contentDocument&&(e=o.contentDocument.defaultView,e.addEventListener("resize",u,Z.passive),d())},{isHydrated:s}=en();return r((()=>{m((()=>{o=c.$el,o&&a()}))})),i(l),()=>{if(!0===s.value)return t("object",{class:"q--avoid-card-border",style:ln.style,tabindex:-1,type:"text/html",data:ln.url,"aria-hidden":"true",onLoad:a})}}}}),on=!1;{let e=document.createElement("div");e.setAttribute("dir","rtl"),Object.assign(e.style,{width:"1px",height:"1px",overflow:"auto"});let t=document.createElement("div");Object.assign(t.style,{width:"1000px",height:"1px"}),document.body.appendChild(e),e.appendChild(t),e.scrollLeft=-1e3,on=e.scrollLeft>=0,e.remove()}function nn(e,t,l){let a=!0===l?["left","right"]:["top","bottom"];return`absolute-${!0===t?a[0]:a[1]}${e?` text-${e}`:""}`}var rn=["left","center","right","justify"],sn=Ze({name:"QTabs",props:{modelValue:[Number,String],align:{type:String,default:"center",validator:e=>rn.includes(e)},breakpoint:{type:[String,Number],default:600},vertical:Boolean,shrink:Boolean,stretch:Boolean,activeClass:String,activeColor:String,activeBgColor:String,indicatorColor:String,leftIcon:String,rightIcon:String,outsideArrows:Boolean,mobileArrows:Boolean,switchIndicator:Boolean,narrowIndicator:Boolean,inlineLabel:Boolean,noCaps:Boolean,dense:Boolean,contentClass:String,"onUpdate:modelValue":[Function,Array]},setup(l,{slots:r,emit:s}){let u,{proxy:d}=a(),{$q:c}=d,{registerTick:v}=Gl(),{registerTick:p}=Gl(),{registerTick:f}=Gl(),{registerTimeout:m,removeTimeout:g}=Jl(),{registerTimeout:y,removeTimeout:w}=Jl(),x=e(null),_=e(null),S=e(l.modelValue),k=e(!1),C=e(!0),$=e(!1),M=e(!1),T=[],B=e(0),L=e(!1),z=null,V=null,O=o((()=>({activeClass:l.activeClass,activeColor:l.activeColor,activeBgColor:l.activeBgColor,indicatorClass:nn(l.indicatorColor,l.switchIndicator,l.vertical),narrowIndicator:l.narrowIndicator,inlineLabel:l.inlineLabel,noCaps:l.noCaps}))),A=o((()=>{let e=B.value,t=S.value;for(let l=0;l<e;l++)if(T[l].name.value===t)return!0;return!1})),E=o((()=>`q-tabs__content--align-${!0===k.value?"left":!0===M.value?"justify":l.align}`)),P=o((()=>`q-tabs row no-wrap items-center q-tabs--${!0===k.value?"":"not-"}scrollable q-tabs--${!0===l.vertical?"vertical":"horizontal"} q-tabs__arrows--${!0===l.outsideArrows?"outside":"inside"} q-tabs--mobile-with${!0===l.mobileArrows?"":"out"}-arrows`+(!0===l.dense?" q-tabs--dense":"")+(!0===l.shrink?" col-shrink":"")+(!0===l.stretch?" self-stretch":""))),F=o((()=>"q-tabs__content scroll--mobile row no-wrap items-center self-stretch hide-scrollbar relative-position "+E.value+(void 0!==l.contentClass?` ${l.contentClass}`:""))),R=o((()=>!0===l.vertical?{container:"height",content:"offsetHeight",scroll:"scrollHeight"}:{container:"width",content:"offsetWidth",scroll:"scrollWidth"})),N=o((()=>!0!==l.vertical&&!0===c.lang.rtl)),H=o((()=>!1===on&&!0===N.value));function I({name:e,setCurrent:t,skipEmit:a}){S.value!==e&&(!0!==a&&void 0!==l["onUpdate:modelValue"]&&s("update:modelValue",e),(!0===t||void 0===l["onUpdate:modelValue"])&&(function(e,t){let a=null!=e&&""!==e?T.find((t=>t.name.value===e)):null,o=null!=t&&""!==t?T.find((e=>e.name.value===t)):null;if(a&&o){let e=a.tabIndicatorRef.value,t=o.tabIndicatorRef.value;null!==z&&(clearTimeout(z),z=null),e.style.transition="none",e.style.transform="none",t.style.transition="none",t.style.transform="none";let n=e.getBoundingClientRect(),i=t.getBoundingClientRect();t.style.transform=!0===l.vertical?`translate3d(0,${n.top-i.top}px,0) scale3d(1,${i.height?n.height/i.height:1},1)`:`translate3d(${n.left-i.left}px,0,0) scale3d(${i.width?n.width/i.width:1},1,1)`,f((()=>{z=setTimeout((()=>{z=null,t.style.transition="transform .25s cubic-bezier(.4, 0, .2, 1)",t.style.transform="none"}),70)}))}o&&!0===k.value&&Q(o.rootRef.value)}(S.value,e),S.value=e))}function j(){v((()=>{D({width:x.value.offsetWidth,height:x.value.offsetHeight})}))}function D(e){if(void 0===R.value||null===_.value)return;let t=e[R.value.container],a=Math.min(_.value[R.value.scroll],Array.prototype.reduce.call(_.value.children,((e,t)=>e+(t[R.value.content]||0)),0)),o=t>0&&a>t;k.value=o,!0===o&&p(U),M.value=t<parseInt(l.breakpoint,10)}function Q(e){let{left:t,width:a,top:o,height:n}=_.value.getBoundingClientRect(),i=e.getBoundingClientRect(),r=!0===l.vertical?i.top-o:i.left-t;if(r<0)return _.value[!0===l.vertical?"scrollTop":"scrollLeft"]+=Math.floor(r),void U();r+=!0===l.vertical?i.height-n:i.width-a,r>0&&(_.value[!0===l.vertical?"scrollTop":"scrollLeft"]+=Math.ceil(r),U())}function U(){let e=_.value;if(null===e)return;let t=e.getBoundingClientRect(),a=!0===l.vertical?e.scrollTop:Math.abs(e.scrollLeft);!0===N.value?(C.value=Math.ceil(a+t.width)<e.scrollWidth-1,$.value=a>0):(C.value=a>0,$.value=!0===l.vertical?Math.ceil(a+t.height)<e.scrollHeight:Math.ceil(a+t.width)<e.scrollWidth)}function W(e){null!==V&&clearInterval(V),V=setInterval((()=>{!0===function(e){let t=_.value,{get:l,set:a}=Z.value,o=!1,n=l(t),i=e<n?-1:1;return n+=5*i,n<0?(o=!0,n=0):(-1===i&&n<=e||1===i&&n>=e)&&(o=!0,n=e),a(t,n),U(),o}(e)&&X()}),5)}function K(){W(!0===H.value?Number.MAX_SAFE_INTEGER:0)}function Y(){W(!0===H.value?0:Number.MAX_SAFE_INTEGER)}function X(){null!==V&&(clearInterval(V),V=null)}n(N,U),n((()=>l.modelValue),(e=>{I({name:e,setCurrent:!0,skipEmit:!0})})),n((()=>l.outsideArrows),j);let Z=o((()=>!0===H.value?{get:e=>Math.abs(e.scrollLeft),set:(e,t)=>{e.scrollLeft=-t}}:!0===l.vertical?{get:e=>e.scrollTop,set:(e,t)=>{e.scrollTop=t}}:{get:e=>e.scrollLeft,set:(e,t)=>{e.scrollLeft=t}}));function G(e,t){for(let l in e)if(e[l]!==t[l])return!1;return!0}function J(){let e=null,t={matchedLen:0,queryDiff:9999,hrefLen:0},l=T.filter((e=>void 0!==e.routeData&&!0===e.routeData.hasRouterLink.value)),{hash:a,query:o}=d.$route,n=Object.keys(o).length;for(let i of l){let l=!0===i.routeData.exact.value;if(!0!==i.routeData[!0===l?"linkIsExactActive":"linkIsActive"].value)continue;let{hash:r,query:s,matched:u,href:d}=i.routeData.resolvedLink.value,c=Object.keys(s).length;if(!0===l){if(r!==a||c!==n||!1===G(o,s))continue;e=i.name.value;break}if(""!==r&&r!==a||0!==c&&!1===G(s,o))continue;let v={matchedLen:u.length,queryDiff:n-c,hrefLen:d.length-r.length};if(v.matchedLen>t.matchedLen)e=i.name.value,t=v;else if(v.matchedLen===t.matchedLen){if(v.queryDiff<t.queryDiff)e=i.name.value,t=v;else if(v.queryDiff!==t.queryDiff)continue;v.hrefLen>t.hrefLen&&(e=i.name.value,t=v)}}null===e&&!0===T.some((e=>void 0===e.routeData&&e.name.value===S.value))||I({name:e,setCurrent:!0})}function ee(e){if(g(),!0!==L.value&&null!==x.value&&e.target&&"function"==typeof e.target.closest){let t=e.target.closest(".q-tab");t&&!0===x.value.contains(t)&&(L.value=!0,!0===k.value&&Q(t))}}function te(){m((()=>{L.value=!1}),30)}function le(){!1===ne.avoidRouteWatcher?y(J):w()}function ae(){if(void 0===u){let e=n((()=>d.$route.fullPath),le);u=()=>{e(),u=void 0}}}let oe,ne={currentModel:S,tabProps:O,hasFocus:L,hasActiveTab:A,registerTab:function(e){T.push(e),B.value++,j(),void 0===e.routeData||void 0===d.$route?y((()=>{if(!0===k.value){let e=S.value,t=null!=e&&""!==e?T.find((t=>t.name.value===e)):null;t&&Q(t.rootRef.value)}})):(ae(),!0===e.routeData.hasRouterLink.value&&le())},unregisterTab:function(e){T.splice(T.indexOf(e),1),B.value--,j(),void 0!==u&&void 0!==e.routeData&&(!0===T.every((e=>void 0===e.routeData))&&u(),le())},verifyRouteModel:le,updateModel:I,onKbdNavigate:function(e,t){let a=Array.prototype.filter.call(_.value.children,(e=>e===t||e.matches&&!0===e.matches(".q-tab.q-focusable"))),o=a.length;if(0===o)return;if(36===e)return Q(a[0]),a[0].focus(),!0;if(35===e)return Q(a[o-1]),a[o-1].focus(),!0;let n=e===(!0===l.vertical?38:37),i=e===(!0===l.vertical?40:39),r=!0===n?-1:!0===i?1:void 0;if(void 0!==r){let e=!0===N.value?-1:1,l=a.indexOf(t)+r*e;return l>=0&&l<o&&(Q(a[l]),a[l].focus({preventScroll:!0})),!0}},avoidRouteWatcher:!1};function ie(){null!==z&&clearTimeout(z),X(),void 0!==u&&u()}return q(Pe,ne),i(ie),h((()=>{oe=void 0!==u,ie()})),b((()=>{!0===oe&&ae(),j()})),()=>t("div",{ref:x,class:P.value,role:"tablist",onFocusin:ee,onFocusout:te},[t(an,{onResize:D}),t("div",{ref:_,class:F.value,onScroll:U},pt(r.default)),t(zt,{class:"q-tabs__arrow q-tabs__arrow--left absolute q-tab__icon"+(!0===C.value?"":" q-tabs__arrow--faded"),name:l.leftIcon||c.iconSet.tabs[!0===l.vertical?"up":"left"],onMousedownPassive:K,onTouchstartPassive:K,onMouseupPassive:X,onMouseleavePassive:X,onTouchendPassive:X}),t(zt,{class:"q-tabs__arrow q-tabs__arrow--right absolute q-tab__icon"+(!0===$.value?"":" q-tabs__arrow--faded"),name:l.rightIcon||c.iconSet.tabs[!0===l.vertical?"down":"right"],onMousedownPassive:Y,onTouchstartPassive:Y,onMouseupPassive:X,onMouseleavePassive:X,onTouchendPassive:X})])}}),un=0,dn=["click","keydown"],cn={icon:String,label:[Number,String],alert:[Boolean,String],alertIcon:String,name:{type:[Number,String],default:()=>"t_"+un++},noCaps:Boolean,tabindex:[String,Number],disable:Boolean,contentClass:String,ripple:{type:[Boolean,Object],default:!0}};function vn(l,n,s,u){let c=d(Pe,Re);if(c===Re)return Re;let{proxy:v}=a(),p=e(null),f=e(null),m=e(null),h=o((()=>!0!==l.disable&&!1!==l.ripple&&Object.assign({keyCodes:[13,32],early:!0},!0===l.ripple?{}:l.ripple))),b=o((()=>c.currentModel.value===l.name)),y=o((()=>"q-tab relative-position self-stretch flex flex-center text-center"+(!0===b.value?" q-tab--active"+(c.tabProps.value.activeClass?" "+c.tabProps.value.activeClass:"")+(c.tabProps.value.activeColor?` text-${c.tabProps.value.activeColor}`:"")+(c.tabProps.value.activeBgColor?` bg-${c.tabProps.value.activeBgColor}`:""):" q-tab--inactive")+(l.icon&&l.label&&!1===c.tabProps.value.inlineLabel?" q-tab--full":"")+(!0===l.noCaps||!0===c.tabProps.value.noCaps?" q-tab--no-caps":"")+(!0===l.disable?" disabled":" q-focusable q-hoverable cursor-pointer")+(void 0!==u?u.linkClass.value:""))),w=o((()=>"q-tab__content self-stretch flex-center relative-position q-anchor--skip non-selectable "+(!0===c.tabProps.value.inlineLabel?"row no-wrap q-tab__content--inline":"column")+(void 0!==l.contentClass?` ${l.contentClass}`:""))),x=o((()=>!0===l.disable||!0===c.hasFocus.value||!1===b.value&&!0===c.hasActiveTab.value?-1:l.tabindex||0));function _(e,t){if(!0!==t&&null!==p.value&&p.value.focus(),!0!==l.disable){if(void 0===u)return c.updateModel({name:l.name}),void s("click",e);if(!0===u.hasRouterLink.value){let t=(t={})=>{let a,o=void 0===t.to||!0===Ie(t.to,l.to)?c.avoidRouteWatcher=Ua():null;return u.navigateToRouterLink(e,{...t,returnRouterError:!0}).catch((e=>{a=e})).then((e=>{if(o===c.avoidRouteWatcher&&(c.avoidRouteWatcher=!1,void 0===a&&(void 0===e||void 0!==e.message&&!0===e.message.startsWith("Avoided redundant navigation"))&&c.updateModel({name:l.name})),!0===t.returnRouterError)return void 0!==a?Promise.reject(a):e}))};return s("click",e,t),void(!0!==e.defaultPrevented&&t())}s("click",e)}else void 0!==u&&!0===u.hasRouterLink.value&&ae(e)}function S(e){qe(e,[13,32])?_(e,!0):!0!==ke(e)&&e.keyCode>=35&&e.keyCode<=40&&!0!==e.altKey&&!0!==e.metaKey&&!0===c.onKbdNavigate(e.keyCode,v.$el)&&ae(e),s("keydown",e)}let k={name:o((()=>l.name)),rootRef:f,tabIndicatorRef:m,routeData:u};return i((()=>{c.unregisterTab(k)})),r((()=>{c.registerTab(k)})),{renderTab:function(e,a){let o={ref:f,class:y.value,tabindex:x.value,role:"tab","aria-selected":!0===b.value?"true":"false","aria-disabled":!0===l.disable?"true":void 0,onClick:_,onKeydown:S,...a};return g(t(e,o,function(){let e=c.tabProps.value.narrowIndicator,a=[],o=t("div",{ref:m,class:["q-tab__indicator",c.tabProps.value.indicatorClass]});void 0!==l.icon&&a.push(t(zt,{class:"q-tab__icon",name:l.icon})),void 0!==l.label&&a.push(t("div",{class:"q-tab__label"},l.label)),!1!==l.alert&&a.push(void 0!==l.alertIcon?t(zt,{class:"q-tab__alert-icon",color:!0!==l.alert?l.alert:void 0,name:l.alertIcon}):t("div",{class:"q-tab__alert"+(!0!==l.alert?` text-${l.alert}`:"")})),!0===e&&a.push(o);let i=[t("div",{class:"q-focus-helper",tabindex:-1,ref:p}),t("div",{class:w.value},mt(n.default,a))];return!1===e&&i.push(o),i}()),[[fl,h.value]])},$tabs:c}}var pn=Ze({name:"QTab",props:cn,emits:dn,setup(e,{slots:t,emit:l}){let{renderTab:a}=vn(e,t,l);return()=>a("div")}}),fn=Ze({name:"QTabPanels",props:{...go,...Et},emits:ho,setup(e,{slots:t}){let l=a(),n=Pt(e,l.proxy.$q),{updatePanelsList:i,getPanelContent:r,panelDirectives:s}=bo(),u=o((()=>"q-tab-panels q-panel-parent"+(!0===n.value?" q-tab-panels--dark q-dark":"")));return()=>(i(t),ht("div",{class:u.value},r(),"pan",e.swipeable,(()=>s.value)))}}),mn=Ze({name:"QTabPanel",props:fo,setup:(e,{slots:l})=>()=>t("div",{class:"q-tab-panel",role:"tabpanel"},pt(l.default))}),gn=/^#[0-9a-fA-F]{3}([0-9a-fA-F]{3})?$/,hn=/^#[0-9a-fA-F]{4}([0-9a-fA-F]{4})?$/,bn=/^#([0-9a-fA-F]{3}|[0-9a-fA-F]{4}|[0-9a-fA-F]{6}|[0-9a-fA-F]{8})$/,yn=/^rgb\(((0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5]),){2}(0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5])\)$/,wn=/^rgba\(((0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5]),){2}(0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5]),(0|0\.[0-9]+[1-9]|0\.[1-9]+|1)\)$/,xn={date:e=>/^-?[\d]+\/[0-1]\d\/[0-3]\d$/.test(e),time:e=>/^([0-1]?\d|2[0-3]):[0-5]\d$/.test(e),fulltime:e=>/^([0-1]?\d|2[0-3]):[0-5]\d:[0-5]\d$/.test(e),timeOrFulltime:e=>/^([0-1]?\d|2[0-3]):[0-5]\d(:[0-5]\d)?$/.test(e),email:e=>/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(e),hexColor:e=>gn.test(e),hexaColor:e=>hn.test(e),hexOrHexaColor:e=>bn.test(e),rgbColor:e=>yn.test(e),rgbaColor:e=>wn.test(e),rgbOrRgbaColor:e=>yn.test(e)||wn.test(e),hexOrRgbColor:e=>gn.test(e)||yn.test(e),hexaOrRgbaColor:e=>hn.test(e)||wn.test(e),anyColor:e=>bn.test(e)||yn.test(e)||wn.test(e)},_n=/^rgb(a)?\((\d{1,3}),(\d{1,3}),(\d{1,3}),?([01]?\.?\d*?)?\)$/;function Sn({r:e,g:t,b:l,a:a}){let o=void 0!==a;if(e=Math.round(e),t=Math.round(t),l=Math.round(l),e>255||t>255||l>255||o&&a>100)throw new TypeError("Expected 3 numbers below 256 (and optionally one below 100)");return a=o?(256|Math.round(255*a/100)).toString(16).slice(1):"","#"+(l|t<<8|e<<16|1<<24).toString(16).slice(1)+a}function kn({r:e,g:t,b:l,a:a}){return`rgb${void 0!==a?"a":""}(${e},${t},${l}${void 0!==a?","+a/100:""})`}function qn(e){if("string"!=typeof e)throw new TypeError("Expected a string");3===(e=e.replace(/^#/,"")).length?e=e[0]+e[0]+e[1]+e[1]+e[2]+e[2]:4===e.length&&(e=e[0]+e[0]+e[1]+e[1]+e[2]+e[2]+e[3]+e[3]);let t=parseInt(e,16);return e.length>6?{r:t>>24&255,g:t>>16&255,b:t>>8&255,a:Math.round((255&t)/2.55)}:{r:t>>16,g:t>>8&255,b:255&t}}function Cn({h:e,s:t,v:l,a:a}){let o,n,i;t/=100,l/=100,e/=360;let r=Math.floor(6*e),s=6*e-r,u=l*(1-t),d=l*(1-s*t),c=l*(1-(1-s)*t);switch(r%6){case 0:o=l,n=c,i=u;break;case 1:o=d,n=l,i=u;break;case 2:o=u,n=l,i=c;break;case 3:o=u,n=d,i=l;break;case 4:o=c,n=u,i=l;break;case 5:o=l,n=u,i=d}return{r:Math.round(255*o),g:Math.round(255*n),b:Math.round(255*i),a:a}}function $n({r:e,g:t,b:l,a:a}){let o,n=Math.max(e,t,l),i=Math.min(e,t,l),r=n-i,s=0===n?0:r/n,u=n/255;switch(n){case i:o=0;break;case e:o=t-l+r*(t<l?6:0),o/=6*r;break;case t:o=l-e+2*r,o/=6*r;break;case l:o=e-t+4*r,o/=6*r}return{h:Math.round(360*o),s:Math.round(100*s),v:Math.round(100*u),a:a}}function Mn(e){if("string"!=typeof e)throw new TypeError("Expected a string");let t=e.replace(/ /g,""),l=_n.exec(t);if(null===l)return qn(t);let a={r:Math.min(255,parseInt(l[2],10)),g:Math.min(255,parseInt(l[3],10)),b:Math.min(255,parseInt(l[4],10))};if(l[1]){let e=parseFloat(l[5]);a.a=100*Math.min(1,!0===isNaN(e)?1:e)}return a}var Tn=["rgb(255,204,204)","rgb(255,230,204)","rgb(255,255,204)","rgb(204,255,204)","rgb(204,255,230)","rgb(204,255,255)","rgb(204,230,255)","rgb(204,204,255)","rgb(230,204,255)","rgb(255,204,255)","rgb(255,153,153)","rgb(255,204,153)","rgb(255,255,153)","rgb(153,255,153)","rgb(153,255,204)","rgb(153,255,255)","rgb(153,204,255)","rgb(153,153,255)","rgb(204,153,255)","rgb(255,153,255)","rgb(255,102,102)","rgb(255,179,102)","rgb(255,255,102)","rgb(102,255,102)","rgb(102,255,179)","rgb(102,255,255)","rgb(102,179,255)","rgb(102,102,255)","rgb(179,102,255)","rgb(255,102,255)","rgb(255,51,51)","rgb(255,153,51)","rgb(255,255,51)","rgb(51,255,51)","rgb(51,255,153)","rgb(51,255,255)","rgb(51,153,255)","rgb(51,51,255)","rgb(153,51,255)","rgb(255,51,255)","rgb(255,0,0)","rgb(255,128,0)","rgb(255,255,0)","rgb(0,255,0)","rgb(0,255,128)","rgb(0,255,255)","rgb(0,128,255)","rgb(0,0,255)","rgb(128,0,255)","rgb(255,0,255)","rgb(245,0,0)","rgb(245,123,0)","rgb(245,245,0)","rgb(0,245,0)","rgb(0,245,123)","rgb(0,245,245)","rgb(0,123,245)","rgb(0,0,245)","rgb(123,0,245)","rgb(245,0,245)","rgb(214,0,0)","rgb(214,108,0)","rgb(214,214,0)","rgb(0,214,0)","rgb(0,214,108)","rgb(0,214,214)","rgb(0,108,214)","rgb(0,0,214)","rgb(108,0,214)","rgb(214,0,214)","rgb(163,0,0)","rgb(163,82,0)","rgb(163,163,0)","rgb(0,163,0)","rgb(0,163,82)","rgb(0,163,163)","rgb(0,82,163)","rgb(0,0,163)","rgb(82,0,163)","rgb(163,0,163)","rgb(92,0,0)","rgb(92,46,0)","rgb(92,92,0)","rgb(0,92,0)","rgb(0,92,46)","rgb(0,92,92)","rgb(0,46,92)","rgb(0,0,92)","rgb(46,0,92)","rgb(92,0,92)","rgb(255,255,255)","rgb(205,205,205)","rgb(178,178,178)","rgb(153,153,153)","rgb(127,127,127)","rgb(102,102,102)","rgb(76,76,76)","rgb(51,51,51)","rgb(25,25,25)","rgb(0,0,0)"],Bn="M5 5 h10 v10 h-10 v-10 z",Ln=Ze({name:"QColor",props:{...Et,...Za,modelValue:String,defaultValue:String,defaultView:{type:String,default:"spectrum",validator:e=>["spectrum","tune","palette"].includes(e)},formatModel:{type:String,default:"auto",validator:e=>["auto","hex","rgb","hexa","rgba"].includes(e)},palette:Array,noHeader:Boolean,noHeaderTabs:Boolean,noFooter:Boolean,square:Boolean,flat:Boolean,bordered:Boolean,disable:Boolean,readonly:Boolean},emits:["update:modelValue","change"],setup(l,{emit:i}){let{proxy:r}=a(),{$q:s}=r,u=Pt(l,s),{getCache:d}=po(),c=e(null),v=e(null),p=o((()=>"auto"===l.formatModel?null:-1!==l.formatModel.indexOf("hex"))),f=o((()=>"auto"===l.formatModel?null:-1!==l.formatModel.indexOf("a"))),g=e("auto"===l.formatModel?void 0===l.modelValue||null===l.modelValue||""===l.modelValue||l.modelValue.startsWith("#")?"hex":"rgb":l.formatModel.startsWith("hex")?"hex":"rgb"),h=e(l.defaultView),b=e(V(l.modelValue||l.defaultValue)),y=o((()=>!0!==l.disable&&!0!==l.readonly)),w=o((()=>void 0===l.modelValue||null===l.modelValue||""===l.modelValue||l.modelValue.startsWith("#"))),x=o((()=>null!==p.value?p.value:w.value)),_=Ja(o((()=>({type:"hidden",name:l.name,value:b.value[!0===x.value?"hex":"rgb"]})))),S=o((()=>null!==f.value?f.value:void 0!==b.value.a)),k=o((()=>({backgroundColor:b.value.rgb||"#000"}))),q=o((()=>"q-color-picker__header-content q-color-picker__header-content--"+(void 0!==b.value.a&&b.value.a<65||function(e){if("string"!=typeof e&&(!e||void 0===e.r))throw new TypeError("Expected a string or a {r, g, b} object as color");let t="string"==typeof e?Mn(e):e,l=t.r/255,a=t.g/255,o=t.b/255;return.2126*(l<=.03928?l/12.92:Math.pow((l+.055)/1.055,2.4))+.7152*(a<=.03928?a/12.92:Math.pow((a+.055)/1.055,2.4))+.0722*(o<=.03928?o/12.92:Math.pow((o+.055)/1.055,2.4))}(b.value)>.4?"light":"dark"))),C=o((()=>({background:`hsl(${b.value.h},100%,50%)`}))),$=o((()=>({top:100-b.value.v+"%",[!0===s.lang.rtl?"right":"left"]:`${b.value.s}%`}))),M=o((()=>void 0!==l.palette&&0!==l.palette.length?l.palette:Tn)),T=o((()=>"q-color-picker"+(!0===l.bordered?" q-color-picker--bordered":"")+(!0===l.square?" q-color-picker--square no-border-radius":"")+(!0===l.flat?" q-color-picker--flat no-shadow":"")+(!0===l.disable?" disabled":"")+(!0===u.value?" q-color-picker--dark q-dark":""))),B=o((()=>!0===l.disable?{"aria-disabled":"true"}:{})),L=o((()=>[[Do,R,void 0,{prevent:!0,stop:!0,mouse:!0}]]));function z(e,t){b.value.hex=Sn(e),b.value.rgb=kn(e),b.value.r=e.r,b.value.g=e.g,b.value.b=e.b,b.value.a=e.a;let l=b.value[!0===x.value?"hex":"rgb"];i("update:modelValue",l),!0===t&&i("change",l)}function V(e){let t=void 0!==f.value?f.value:"auto"===l.formatModel?null:-1!==l.formatModel.indexOf("a");if("string"!=typeof e||0===e.length||!0!==xn.anyColor(e.replace(/ /g,"")))return{h:0,s:0,v:0,r:0,g:0,b:0,a:!0===t?100:void 0,hex:void 0,rgb:void 0};let a=Mn(e);return!0===t&&void 0===a.a&&(a.a=100),a.hex=Sn(a),a.rgb=kn(a),Object.assign(a,$n(a))}function O(e,t,l){let a=c.value;if(null===a)return;let o=a.clientWidth,n=a.clientHeight,i=a.getBoundingClientRect(),r=Math.min(o,Math.max(0,e-i.left));!0===s.lang.rtl&&(r=o-r);let u=Math.min(n,Math.max(0,t-i.top)),d=Math.round(100*r/o),v=Math.round(100*Math.max(0,Math.min(1,-u/n+1))),p=Cn({h:b.value.h,s:d,v:v,a:!0===S.value?b.value.a:void 0});b.value.s=d,b.value.v=v,z(p,l)}function A(e,t){let l=Math.round(e),a=Cn({h:l,s:b.value.s,v:b.value.v,a:!0===S.value?b.value.a:void 0});b.value.h=l,z(a,t)}function E(e){A(e,!0)}function P(e,t,l,a,o){if(void 0!==a&&te(a),!/^[0-9]+$/.test(e))return void(!0===o&&r.$forceUpdate());let n=Math.floor(Number(e));if(n<0||n>l)return void(!0===o&&r.$forceUpdate());let i={r:"r"===t?n:b.value.r,g:"g"===t?n:b.value.g,b:"b"===t?n:b.value.b,a:!0===S.value?"a"===t?n:b.value.a:void 0};if("a"!==t){let e=$n(i);b.value.h=e.h,b.value.s=e.s,b.value.v=e.v}if(z(i,o),void 0!==a&&!0!==o&&void 0!==a.target.selectionEnd){let e=a.target.selectionEnd;m((()=>{a.target.setSelectionRange(e,e)}))}}function F(e,t){let l,a=e.target.value;if(te(e),"hex"===g.value){if(a.length!==(!0===S.value?9:7)||!/^#[0-9A-Fa-f]+$/.test(a))return!0;l=qn(a)}else{let e;if(!a.endsWith(")"))return!0;if(!0!==S.value&&a.startsWith("rgb(")){if(e=a.substring(4,a.length-1).split(",").map((e=>parseInt(e,10))),3!==e.length||!/^rgb\([0-9]{1,3},[0-9]{1,3},[0-9]{1,3}\)$/.test(a))return!0}else{if(!0!==S.value||!a.startsWith("rgba("))return!0;{if(e=a.substring(5,a.length-1).split(","),4!==e.length||!/^rgba\([0-9]{1,3},[0-9]{1,3},[0-9]{1,3},(0|0\.[0-9]+[1-9]|0\.[1-9]+|1)\)$/.test(a))return!0;for(let l=0;l<3;l++){let t=parseInt(e[l],10);if(t<0||t>255)return!0;e[l]=t}let t=parseFloat(e[3]);if(t<0||t>1)return!0;e[3]=t}}if(e[0]<0||e[0]>255||e[1]<0||e[1]>255||e[2]<0||e[2]>255||!0===S.value&&(e[3]<0||e[3]>1))return!0;l={r:e[0],g:e[1],b:e[2],a:!0===S.value?100*e[3]:void 0}}let o=$n(l);if(b.value.h=o.h,b.value.s=o.s,b.value.v=o.v,z(l,t),!0!==t){let t=e.target.selectionEnd;m((()=>{e.target.setSelectionRange(t,t)}))}}function R(e){e.isFinal?O(e.position.left,e.position.top,!0):N(e)}n((()=>l.modelValue),(e=>{let t=V(e||l.defaultValue);t.hex!==b.value.hex&&(b.value=t)})),n((()=>l.defaultValue),(e=>{if(!l.modelValue&&e){let t=V(e);t.hex!==b.value.hex&&(b.value=t)}}));let N=cl((e=>{O(e.position.left,e.position.top)}),20);function H(e){O(e.pageX-window.pageXOffset,e.pageY-window.pageYOffset,!0)}function I(e){O(e.pageX-window.pageXOffset,e.pageY-window.pageYOffset)}function j(e){null!==v.value&&(v.value.$el.style.opacity=e?1:0)}function D(e){g.value=e}function Q(e){h.value=e}function U(){let e={ref:c,class:"q-color-picker__spectrum non-selectable relative-position cursor-pointer"+(!0!==y.value?" readonly":""),style:C.value,...!0===y.value?{onClick:H,onMousedown:I}:{}},l=[t("div",{style:{paddingBottom:"100%"}}),t("div",{class:"q-color-picker__spectrum-white absolute-full"}),t("div",{class:"q-color-picker__spectrum-black absolute-full"}),t("div",{class:"absolute",style:$.value},[void 0!==b.value.hex?t("div",{class:"q-color-picker__spectrum-circle"}):null])],a=[t(Jo,{class:"q-color-picker__hue non-selectable",modelValue:b.value.h,min:0,max:360,trackSize:"8px",innerTrackColor:"transparent",selectionColor:"transparent",readonly:!0!==y.value,thumbPath:Bn,"onUpdate:modelValue":A,onChange:E})];return!0===S.value&&a.push(t(Jo,{class:"q-color-picker__alpha non-selectable",modelValue:b.value.a,min:0,max:100,trackSize:"8px",trackColor:"white",innerTrackColor:"transparent",selectionColor:"transparent",trackImg:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAAH0lEQVQoU2NkYGAwZkAFZ5G5jPRRgOYEVDeB3EBjBQBOZwTVugIGyAAAAABJRU5ErkJggg==",readonly:!0!==y.value,hideSelection:!0,thumbPath:Bn,...d("alphaSlide",{"onUpdate:modelValue":e=>P(e,"a",100),onChange:e=>P(e,"a",100,void 0,!0)})})),[ht("div",e,l,"spec",y.value,(()=>L.value)),t("div",{class:"q-color-picker__sliders"},a)]}function W(){return[t("div",{class:"row items-center no-wrap"},[t("div","R"),t(Jo,{modelValue:b.value.r,min:0,max:255,color:"red",dark:u.value,readonly:!0!==y.value,...d("rSlide",{"onUpdate:modelValue":e=>P(e,"r",255),onChange:e=>P(e,"r",255,void 0,!0)})}),t("input",{value:b.value.r,maxlength:3,readonly:!0!==y.value,onChange:te,...d("rIn",{onInput:e=>P(e.target.value,"r",255,e),onBlur:e=>P(e.target.value,"r",255,e,!0)})})]),t("div",{class:"row items-center no-wrap"},[t("div","G"),t(Jo,{modelValue:b.value.g,min:0,max:255,color:"green",dark:u.value,readonly:!0!==y.value,...d("gSlide",{"onUpdate:modelValue":e=>P(e,"g",255),onChange:e=>P(e,"g",255,void 0,!0)})}),t("input",{value:b.value.g,maxlength:3,readonly:!0!==y.value,onChange:te,...d("gIn",{onInput:e=>P(e.target.value,"g",255,e),onBlur:e=>P(e.target.value,"g",255,e,!0)})})]),t("div",{class:"row items-center no-wrap"},[t("div","B"),t(Jo,{modelValue:b.value.b,min:0,max:255,color:"blue",readonly:!0!==y.value,dark:u.value,...d("bSlide",{"onUpdate:modelValue":e=>P(e,"b",255),onChange:e=>P(e,"b",255,void 0,!0)})}),t("input",{value:b.value.b,maxlength:3,readonly:!0!==y.value,onChange:te,...d("bIn",{onInput:e=>P(e.target.value,"b",255,e),onBlur:e=>P(e.target.value,"b",255,e,!0)})})]),!0===S.value?t("div",{class:"row items-center no-wrap"},[t("div","A"),t(Jo,{modelValue:b.value.a,color:"grey",readonly:!0!==y.value,dark:u.value,...d("aSlide",{"onUpdate:modelValue":e=>P(e,"a",100),onChange:e=>P(e,"a",100,void 0,!0)})}),t("input",{value:b.value.a,maxlength:3,readonly:!0!==y.value,onChange:te,...d("aIn",{onInput:e=>P(e.target.value,"a",100,e),onBlur:e=>P(e.target.value,"a",100,e,!0)})})]):null]}function K(){return[t("div",{class:"row items-center q-color-picker__palette-rows"+(!0===y.value?" q-color-picker__palette-rows--editable":"")},M.value.map((e=>t("div",{class:"q-color-picker__cube col-auto",style:{backgroundColor:e},...!0===y.value?d("palette#"+e,{onClick:()=>{!function(e){let t=V(e),l={r:t.r,g:t.g,b:t.b,a:t.a};void 0===l.a&&(l.a=b.value.a),b.value.h=t.h,b.value.s=t.s,b.value.v=t.v,z(l,!0)}(e)}}):{}}))))]}return()=>{let e=[t(fn,{modelValue:h.value,animated:!0},(()=>[t(mn,{class:"q-color-picker__spectrum-tab overflow-hidden",name:"spectrum"},U),t(mn,{class:"q-pa-md q-color-picker__tune-tab",name:"tune"},W),t(mn,{class:"q-color-picker__palette-tab",name:"palette"},K)]))];return void 0!==l.name&&!0!==l.disable&&_(e,"push"),!0!==l.noHeader&&e.unshift(function(){let e=[];return!0!==l.noHeaderTabs&&e.push(t(sn,{class:"q-color-picker__header-tabs",modelValue:g.value,dense:!0,align:"justify","onUpdate:modelValue":D},(()=>[t(pn,{label:"HEX"+(!0===S.value?"A":""),name:"hex",ripple:!1}),t(pn,{label:"RGB"+(!0===S.value?"A":""),name:"rgb",ripple:!1})]))),e.push(t("div",{class:"q-color-picker__header-banner row flex-center no-wrap"},[t("input",{class:"fit",value:b.value[g.value],...!0!==y.value?{readonly:!0}:{},...d("topIn",{onInput:e=>{j(!0===F(e))},onChange:te,onBlur:e=>{!0===F(e,!0)&&r.$forceUpdate(),j(!1)}})}),t(zt,{ref:v,class:"q-color-picker__error-icon absolute no-pointer-events",name:s.iconSet.type.negative})])),t("div",{class:"q-color-picker__header relative-position overflow-hidden"},[t("div",{class:"q-color-picker__header-bg absolute-full"}),t("div",{class:q.value,style:k.value},e)])}()),!0!==l.noFooter&&e.push(t("div",{class:"q-color-picker__footer relative-position overflow-hidden"},[t(sn,{class:"absolute-full",modelValue:h.value,dense:!0,align:"justify","onUpdate:modelValue":Q},(()=>[t(pn,{icon:s.iconSet.colorPicker.spectrum,name:"spectrum",ripple:!1}),t(pn,{icon:s.iconSet.colorPicker.tune,name:"tune",ripple:!1}),t(pn,{icon:s.iconSet.colorPicker.palette,name:"palette",ripple:!1})]))])),t("div",{class:T.value,...B.value},e)}}}),zn=[-61,9,38,199,426,686,756,818,1111,1181,1210,1635,2060,2097,2192,2262,2324,2394,2456,3178];function Vn(e,t,l){return"[object Date]"===Object.prototype.toString.call(e)&&(l=e.getDate(),t=e.getMonth()+1,e=e.getFullYear()),function(e){let t,l,a,o=Rn(e).gy,n=o-621,i=Pn(n,!1),r=Fn(o,3,i.march);if(a=e-r,a>=0){if(a<=185)return l=1+Nn(a,31),t=Hn(a,31)+1,{jy:n,jm:l,jd:t};a-=186}else n-=1,a+=179,1===i.leap&&(a+=1);return l=7+Nn(a,30),t=Hn(a,30)+1,{jy:n,jm:l,jd:t}}(Fn(e,t,l))}function On(e,t,l){return Rn(function(e,t,l){let a=Pn(e,!0);return Fn(a.gy,3,a.march)+31*(t-1)-Nn(t,7)*(t-7)+l-1}(e,t,l))}function An(e){return 0===function(e){let t,l,a,o,n,i=zn.length,r=zn[0];if(e<r||e>=zn[i-1])throw new Error("Invalid Jalaali year "+e);for(n=1;n<i&&(t=zn[n],l=t-r,!(e<t));n+=1)r=t;return o=e-r,l-o<6&&(o=o-l+33*Nn(l+4,33)),a=Hn(Hn(o+1,33)-1,4),-1===a&&(a=4),a}(e)}function En(e,t){return t<=6?31:t<=11||An(e)?30:29}function Pn(e,t){let l,a,o,n,i,r=zn.length,s=e+621,u=-14,d=zn[0];if(e<d||e>=zn[r-1])throw new Error("Invalid Jalaali year "+e);for(i=1;i<r&&(l=zn[i],a=l-d,!(e<l));i+=1)u=u+8*Nn(a,33)+Nn(Hn(a,33),4),d=l;n=e-d,u=u+8*Nn(n,33)+Nn(Hn(n,33)+3,4),4===Hn(a,33)&&a-n==4&&(u+=1);let c=20+u-(Nn(s,4)-Nn(3*(Nn(s,100)+1),4)-150);return t||(a-n<6&&(n=n-a+33*Nn(a+4,33)),o=Hn(Hn(n+1,33)-1,4),-1===o&&(o=4)),{leap:o,gy:s,march:c}}function Fn(e,t,l){let a=Nn(1461*(e+Nn(t-8,6)+100100),4)+Nn(153*Hn(t+9,12)+2,5)+l-34840408;return a=a-Nn(3*Nn(e+100100+Nn(t-8,6),100),4)+752,a}function Rn(e){let t=4*e+139361631;t=t+4*Nn(3*Nn(4*e+183187720,146097),4)-3908;let l=5*Nn(Hn(t,1461),4)+308,a=Nn(Hn(l,153),5)+1,o=Hn(Nn(l,153),12)+1;return{gy:Nn(t,1461)-100100+Nn(8-o,6),gm:o,gd:a}}function Nn(e,t){return~~(e/t)}function Hn(e,t){return e-~~(e/t)*t}var In=["gregorian","persian"],jn={modelValue:{required:!0},mask:{type:String},locale:Object,calendar:{type:String,validator:e=>In.includes(e),default:"gregorian"},landscape:Boolean,color:String,textColor:String,square:Boolean,flat:Boolean,bordered:Boolean,readonly:Boolean,disable:Boolean},Dn=["update:modelValue"];function Qn(e){return e.year+"/"+at(e.month)+"/"+at(e.day)}function Un(e,t){let l=o((()=>!0!==e.disable&&!0!==e.readonly)),a=o((()=>!0===l.value?0:-1)),n=o((()=>{let t=[];return void 0!==e.color&&t.push(`bg-${e.color}`),void 0!==e.textColor&&t.push(`text-${e.textColor}`),t.join(" ")}));return{editable:l,tabindex:a,headerClass:n,getLocale:function(){return void 0!==e.locale?{...t.lang.date,...e.locale}:t.lang.date},getCurrentDate:function(t){let l=new Date,a=!0===t?null:0;if("persian"===e.calendar){let e=Vn(l);return{year:e.jy,month:e.jm,day:e.jd}}return{year:l.getFullYear(),month:l.getMonth()+1,day:l.getDate(),hour:a,minute:a,second:a,millisecond:a}}}}var Wn=864e5,Kn=36e5,Yn=6e4,Xn="YYYY-MM-DDTHH:mm:ss.SSSZ",Zn=/\[((?:[^\]\\]|\\]|\\)*)\]|d{1,4}|M{1,4}|m{1,2}|w{1,2}|Qo|Do|D{1,4}|YY(?:YY)?|H{1,2}|h{1,2}|s{1,2}|S{1,3}|Z{1,2}|a{1,2}|[AQExX]/g,Gn=/(\[[^\]]*\])|d{1,4}|M{1,4}|m{1,2}|w{1,2}|Qo|Do|D{1,4}|YY(?:YY)?|H{1,2}|h{1,2}|s{1,2}|S{1,3}|Z{1,2}|a{1,2}|[AQExX]|([.*+:?^,\s${}()|\\]+)/g,Jn={};function ei(e,t){return void 0!==e?e:void 0!==t?t.date:he.default.date}function ti(e,t=""){let l=e>0?"-":"+",a=Math.abs(e),o=a%60;return l+at(Math.floor(a/60))+t+at(o)}function li(e,t,l,a,o){let n={year:null,month:null,day:null,hour:null,minute:null,second:null,millisecond:null,timezoneOffset:null,dateHash:null,timeHash:null};if(void 0!==o&&Object.assign(n,o),null==e||""===e||"string"!=typeof e)return n;void 0===t&&(t=Xn);let i=ei(l,we.props),r=i.months,s=i.monthsShort,{regex:u,map:d}=function(e,t){let l="("+t.days.join("|")+")",a=e+l;if(void 0!==Jn[a])return Jn[a];let o="("+t.daysShort.join("|")+")",n="("+t.months.join("|")+")",i="("+t.monthsShort.join("|")+")",r={},s=0,u=e.replace(Gn,(e=>{switch(s++,e){case"YY":return r.YY=s,"(-?\\d{1,2})";case"YYYY":return r.YYYY=s,"(-?\\d{1,4})";case"M":return r.M=s,"(\\d{1,2})";case"MM":return r.M=s,"(\\d{2})";case"MMM":return r.MMM=s,i;case"MMMM":return r.MMMM=s,n;case"D":return r.D=s,"(\\d{1,2})";case"Do":return r.D=s++,"(\\d{1,2}(st|nd|rd|th))";case"DD":return r.D=s,"(\\d{2})";case"H":return r.H=s,"(\\d{1,2})";case"HH":return r.H=s,"(\\d{2})";case"h":return r.h=s,"(\\d{1,2})";case"hh":return r.h=s,"(\\d{2})";case"m":return r.m=s,"(\\d{1,2})";case"mm":return r.m=s,"(\\d{2})";case"s":return r.s=s,"(\\d{1,2})";case"ss":return r.s=s,"(\\d{2})";case"S":return r.S=s,"(\\d{1})";case"SS":return r.S=s,"(\\d{2})";case"SSS":return r.S=s,"(\\d{3})";case"A":return r.A=s,"(AM|PM)";case"a":return r.a=s,"(am|pm)";case"aa":return r.aa=s,"(a\\.m\\.|p\\.m\\.)";case"ddd":return o;case"dddd":return l;case"Q":case"d":case"E":return"(\\d{1})";case"Qo":return"(1st|2nd|3rd|4th)";case"DDD":case"DDDD":return"(\\d{1,3})";case"w":return"(\\d{1,2})";case"ww":return"(\\d{2})";case"Z":return r.Z=s,"(Z|[+-]\\d{2}:\\d{2})";case"ZZ":return r.ZZ=s,"(Z|[+-]\\d{2}\\d{2})";case"X":return r.X=s,"(-?\\d+)";case"x":return r.x=s,"(-?\\d{4,})";default:return s--,"["===e[0]&&(e=e.substring(1,e.length-1)),e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}})),d={map:r,regex:new RegExp("^"+u)};return Jn[a]=d,d}(t,i),c=e.match(u);if(null===c)return n;let v="";if(void 0!==d.X||void 0!==d.x){let e=parseInt(c[void 0!==d.X?d.X:d.x],10);if(!0===isNaN(e)||e<0)return n;let t=new Date(e*(void 0!==d.X?1e3:1));n.year=t.getFullYear(),n.month=t.getMonth()+1,n.day=t.getDate(),n.hour=t.getHours(),n.minute=t.getMinutes(),n.second=t.getSeconds(),n.millisecond=t.getMilliseconds()}else{if(void 0!==d.YYYY)n.year=parseInt(c[d.YYYY],10);else if(void 0!==d.YY){let e=parseInt(c[d.YY],10);n.year=e<0?e:2e3+e}if(void 0!==d.M){if(n.month=parseInt(c[d.M],10),n.month<1||n.month>12)return n}else void 0!==d.MMM?n.month=s.indexOf(c[d.MMM])+1:void 0!==d.MMMM&&(n.month=r.indexOf(c[d.MMMM])+1);if(void 0!==d.D){if(n.day=parseInt(c[d.D],10),null===n.year||null===n.month||n.day<1)return n;let e="persian"!==a?new Date(n.year,n.month,0).getDate():En(n.year,n.month);if(n.day>e)return n}void 0!==d.H?n.hour=parseInt(c[d.H],10)%24:void 0!==d.h&&(n.hour=parseInt(c[d.h],10)%12,(d.A&&"PM"===c[d.A]||d.a&&"pm"===c[d.a]||d.aa&&"p.m."===c[d.aa])&&(n.hour+=12),n.hour=n.hour%24),void 0!==d.m&&(n.minute=parseInt(c[d.m],10)%60),void 0!==d.s&&(n.second=parseInt(c[d.s],10)%60),void 0!==d.S&&(n.millisecond=parseInt(c[d.S],10)*10**(3-c[d.S].length)),(void 0!==d.Z||void 0!==d.ZZ)&&(v=void 0!==d.Z?c[d.Z].replace(":",""):c[d.ZZ],n.timezoneOffset=("+"===v[0]?-1:1)*(60*v.slice(1,3)+1*v.slice(3,5)))}return n.dateHash=at(n.year,6)+"/"+at(n.month)+"/"+at(n.day),n.timeHash=at(n.hour)+":"+at(n.minute)+":"+at(n.second)+v,n}function ai(e){let t=new Date(e.getFullYear(),e.getMonth(),e.getDate());t.setDate(t.getDate()-(t.getDay()+6)%7+3);let l=new Date(t.getFullYear(),0,4);l.setDate(l.getDate()-(l.getDay()+6)%7+3);let a=t.getTimezoneOffset()-l.getTimezoneOffset();t.setHours(t.getHours()-a);let o=(t-l)/(7*Wn);return 1+Math.floor(o)}function oi(e,t,l){let a=new Date(e),o="set"+(!0===l?"UTC":"");switch(t){case"year":case"years":a[`${o}Month`](0);case"month":case"months":a[`${o}Date`](1);case"day":case"days":case"date":a[`${o}Hours`](0);case"hour":case"hours":a[`${o}Minutes`](0);case"minute":case"minutes":a[`${o}Seconds`](0);case"second":case"seconds":a[`${o}Milliseconds`](0)}return a}function ni(e,t,l){return(e.getTime()-e.getTimezoneOffset()*Yn-(t.getTime()-t.getTimezoneOffset()*Yn))/l}function ii(e,t,l="days"){let a=new Date(e),o=new Date(t);switch(l){case"years":case"year":return a.getFullYear()-o.getFullYear();case"months":case"month":return 12*(a.getFullYear()-o.getFullYear())+a.getMonth()-o.getMonth();case"days":case"day":case"date":return ni(oi(a,"day"),oi(o,"day"),Wn);case"hours":case"hour":return ni(oi(a,"hour"),oi(o,"hour"),Kn);case"minutes":case"minute":return ni(oi(a,"minute"),oi(o,"minute"),Yn);case"seconds":case"second":return ni(oi(a,"second"),oi(o,"second"),1e3)}}function ri(e){return ii(e,oi(e,"year"),"days")+1}function si(e){if(e>=11&&e<=13)return`${e}th`;switch(e%10){case 1:return`${e}st`;case 2:return`${e}nd`;case 3:return`${e}rd`}return`${e}th`}var ui={YY(e,t,l){let a=this.YYYY(e,t,l)%100;return a>=0?at(a):"-"+at(Math.abs(a))},YYYY:(e,t,l)=>l??e.getFullYear(),M:e=>e.getMonth()+1,MM:e=>at(e.getMonth()+1),MMM:(e,t)=>t.monthsShort[e.getMonth()],MMMM:(e,t)=>t.months[e.getMonth()],Q:e=>Math.ceil((e.getMonth()+1)/3),Qo(e){return si(this.Q(e))},D:e=>e.getDate(),Do:e=>si(e.getDate()),DD:e=>at(e.getDate()),DDD:e=>ri(e),DDDD:e=>at(ri(e),3),d:e=>e.getDay(),dd(e,t){return this.dddd(e,t).slice(0,2)},ddd:(e,t)=>t.daysShort[e.getDay()],dddd:(e,t)=>t.days[e.getDay()],E:e=>e.getDay()||7,w:e=>ai(e),ww:e=>at(ai(e)),H:e=>e.getHours(),HH:e=>at(e.getHours()),h(e){let t=e.getHours();return 0===t?12:t>12?t%12:t},hh(e){return at(this.h(e))},m:e=>e.getMinutes(),mm:e=>at(e.getMinutes()),s:e=>e.getSeconds(),ss:e=>at(e.getSeconds()),S:e=>Math.floor(e.getMilliseconds()/100),SS:e=>at(Math.floor(e.getMilliseconds()/10)),SSS:e=>at(e.getMilliseconds(),3),A(e){return this.H(e)<12?"AM":"PM"},a(e){return this.H(e)<12?"am":"pm"},aa(e){return this.H(e)<12?"a.m.":"p.m."},Z:(e,t,l,a)=>ti(a??e.getTimezoneOffset(),":"),ZZ:(e,t,l,a)=>ti(a??e.getTimezoneOffset()),X:e=>Math.floor(e.getTime()/1e3),x:e=>e.getTime()};function di(e,t,l,a,o){if(0!==e&&!e||e===1/0||e===-1/0)return;let n=new Date(e);if(isNaN(n))return;void 0===t&&(t=Xn);let i=ei(l,we.props);return t.replace(Zn,((e,t)=>e in ui?ui[e](n,i,a,o):void 0===t?e:t.split("\\]").join("]")))}var ci=20,vi=["Calendar","Years","Months"],pi=e=>vi.includes(e),fi=e=>/^-?[\d]+\/[0-1]\d$/.test(e),mi=" — ";function gi(e){return e.year+"/"+at(e.month)}var hi=Ze({name:"QDate",props:{...jn,...Za,...Et,multiple:Boolean,range:Boolean,title:String,subtitle:String,mask:{default:"YYYY/MM/DD"},defaultYearMonth:{type:String,validator:fi},yearsInMonthView:Boolean,events:[Array,Function],eventColor:[String,Function],emitImmediately:Boolean,options:[Array,Function],navigationMinYearMonth:{type:String,validator:fi},navigationMaxYearMonth:{type:String,validator:fi},noUnset:Boolean,firstDayOfWeek:[String,Number],todayBtn:Boolean,minimal:Boolean,defaultView:{type:String,default:"Calendar",validator:pi}},emits:[...Dn,"rangeStart","rangeEnd","navigation"],setup(l,{slots:i,emit:r}){let s,{proxy:u}=a(),{$q:d}=u,c=Pt(l,d),{getCache:v}=po(),{tabindex:p,headerClass:g,getLocale:h,getCurrentDate:b}=Un(l,d),y=Ja(Ga(l)),w=e(null),x=e(pe()),_=e(h()),S=o((()=>pe())),k=o((()=>h())),q=o((()=>b())),C=e(me(x.value,_.value)),$=e(l.defaultView),M=!0===d.lang.rtl?"right":"left",T=e(M.value),B=e(M.value),L=C.value.year,z=e(L-L%ci-(L<0?ci:0)),V=e(null),O=o((()=>{let e=!0===l.landscape?"landscape":"portrait";return`q-date q-date--${e} q-date--${e}-${!0===l.minimal?"minimal":"standard"}`+(!0===c.value?" q-date--dark q-dark":"")+(!0===l.bordered?" q-date--bordered":"")+(!0===l.square?" q-date--square no-border-radius":"")+(!0===l.flat?" q-date--flat no-shadow":"")+(!0===l.disable?" disabled":!0===l.readonly?" q-date--readonly":"")})),A=o((()=>l.color||"primary")),E=o((()=>l.textColor||"white")),P=o((()=>!0===l.emitImmediately&&!0!==l.multiple&&!0!==l.range)),F=o((()=>!0===Array.isArray(l.modelValue)?l.modelValue:null!==l.modelValue&&void 0!==l.modelValue?[l.modelValue]:[])),R=o((()=>F.value.filter((e=>"string"==typeof e)).map((e=>fe(e,x.value,_.value))).filter((e=>null!==e.dateHash&&null!==e.day&&null!==e.month&&null!==e.year)))),N=o((()=>{let e=e=>fe(e,x.value,_.value);return F.value.filter((e=>!0===je(e)&&void 0!==e.from&&void 0!==e.to)).map((t=>({from:e(t.from),to:e(t.to)}))).filter((e=>null!==e.from.dateHash&&null!==e.to.dateHash&&e.from.dateHash<e.to.dateHash))})),H=o((()=>"persian"!==l.calendar?e=>new Date(e.year,e.month-1,e.day):e=>{let t=On(e.year,e.month,e.day);return new Date(t.gy,t.gm-1,t.gd)})),I=o((()=>"persian"===l.calendar?Qn:(e,t,l)=>di(new Date(e.year,e.month-1,e.day,e.hour,e.minute,e.second,e.millisecond),void 0===t?x.value:t,void 0===l?_.value:l,e.year,e.timezoneOffset))),j=o((()=>R.value.length+N.value.reduce(((e,t)=>e+1+ii(H.value(t.to),H.value(t.from))),0))),D=o((()=>{if(void 0!==l.title&&null!==l.title&&0!==l.title.length)return l.title;if(null!==V.value){let e=V.value.init,t=H.value(e);return _.value.daysShort[t.getDay()]+", "+_.value.monthsShort[e.month-1]+" "+e.day+mi+"?"}if(0===j.value)return mi;if(j.value>1)return`${j.value} ${_.value.pluralDay}`;let e=R.value[0],t=H.value(e);return!0===isNaN(t.valueOf())?mi:void 0!==_.value.headerTitle?_.value.headerTitle(t,e):_.value.daysShort[t.getDay()]+", "+_.value.monthsShort[e.month-1]+" "+e.day})),Q=o((()=>R.value.concat(N.value.map((e=>e.from))).sort(((e,t)=>e.year-t.year||e.month-t.month))[0])),U=o((()=>R.value.concat(N.value.map((e=>e.to))).sort(((e,t)=>t.year-e.year||t.month-e.month))[0])),W=o((()=>{if(void 0!==l.subtitle&&null!==l.subtitle&&0!==l.subtitle.length)return l.subtitle;if(0===j.value)return mi;if(j.value>1){let e=Q.value,t=U.value,l=_.value.monthsShort;return l[e.month-1]+(e.year!==t.year?" "+e.year+mi+l[t.month-1]+" ":e.month!==t.month?mi+l[t.month-1]:"")+" "+t.year}return R.value[0].year})),K=o((()=>{let e=[d.iconSet.datetime.arrowLeft,d.iconSet.datetime.arrowRight];return!0===d.lang.rtl?e.reverse():e})),Y=o((()=>void 0!==l.firstDayOfWeek?Number(l.firstDayOfWeek):_.value.firstDayOfWeek)),X=o((()=>{let e=_.value.daysShort,t=Y.value;return t>0?e.slice(t,7).concat(e.slice(0,t)):e})),Z=o((()=>{let e=C.value;return"persian"!==l.calendar?new Date(e.year,e.month,0).getDate():En(e.year,e.month)})),G=o((()=>"function"==typeof l.eventColor?l.eventColor:()=>l.eventColor)),J=o((()=>{if(void 0===l.navigationMinYearMonth)return null;let e=l.navigationMinYearMonth.split("/");return{year:parseInt(e[0],10),month:parseInt(e[1],10)}})),ee=o((()=>{if(void 0===l.navigationMaxYearMonth)return null;let e=l.navigationMaxYearMonth.split("/");return{year:parseInt(e[0],10),month:parseInt(e[1],10)}})),te=o((()=>{let e={month:{prev:!0,next:!0},year:{prev:!0,next:!0}};return null!==J.value&&J.value.year>=C.value.year&&(e.year.prev=!1,J.value.year===C.value.year&&J.value.month>=C.value.month&&(e.month.prev=!1)),null!==ee.value&&ee.value.year<=C.value.year&&(e.year.next=!1,ee.value.year===C.value.year&&ee.value.month<=C.value.month&&(e.month.next=!1)),e})),le=o((()=>{let e={};return R.value.forEach((t=>{let l=gi(t);void 0===e[l]&&(e[l]=[]),e[l].push(t.day)})),e})),ae=o((()=>{let e={};return N.value.forEach((t=>{let l=gi(t.from),a=gi(t.to);if(void 0===e[l]&&(e[l]=[]),e[l].push({from:t.from.day,to:l===a?t.to.day:void 0,range:t}),l<a){let l,{year:o,month:n}=t.from,i=n<12?{year:o,month:n+1}:{year:o+1,month:1};for(;(l=gi(i))<=a;)void 0===e[l]&&(e[l]=[]),e[l].push({from:void 0,to:l===a?t.to.day:void 0,range:t}),i.month++,i.month>12&&(i.year++,i.month=1)}})),e})),oe=o((()=>{if(null===V.value)return;let{init:e,initHash:t,final:l,finalHash:a}=V.value,[o,n]=t<=a?[e,l]:[l,e],i=gi(o),r=gi(n);if(i!==ne.value&&r!==ne.value)return;let s={};return i===ne.value?(s.from=o.day,s.includeFrom=!0):s.from=1,r===ne.value?(s.to=n.day,s.includeTo=!0):s.to=Z.value,s})),ne=o((()=>gi(C.value))),ie=o((()=>{let e={};if(void 0===l.options){for(let t=1;t<=Z.value;t++)e[t]=!0;return e}let t="function"==typeof l.options?l.options:e=>l.options.includes(e);for(let l=1;l<=Z.value;l++){let a=ne.value+"/"+at(l);e[l]=t(a)}return e})),re=o((()=>{let e={};if(void 0===l.events)for(let t=1;t<=Z.value;t++)e[t]=!1;else{let t="function"==typeof l.events?l.events:e=>l.events.includes(e);for(let l=1;l<=Z.value;l++){let a=ne.value+"/"+at(l);e[l]=!0===t(a)&&G.value(a)}}return e})),se=o((()=>{let e,t,{year:a,month:o}=C.value;if("persian"!==l.calendar)e=new Date(a,o-1,1),t=new Date(a,o-1,0).getDate();else{let l=On(a,o,1);e=new Date(l.gy,l.gm-1,l.gd);let n=o-1,i=a;0===n&&(n=12,i--),t=En(i,n)}return{days:e.getDay()-Y.value-1,endDay:t}})),ue=o((()=>{let e=[],{days:t,endDay:l}=se.value,a=t<0?t+7:t;if(a<6)for(let i=l-a;i<=l;i++)e.push({i:i,fill:!0});let o=e.length;for(let i=1;i<=Z.value;i++){let t={i:i,event:re.value[i],classes:[]};!0===ie.value[i]&&(t.in=!0,t.flat=!0),e.push(t)}if(void 0!==le.value[ne.value]&&le.value[ne.value].forEach((t=>{let l=o+t-1;Object.assign(e[l],{selected:!0,unelevated:!0,flat:!1,color:A.value,textColor:E.value})})),void 0!==ae.value[ne.value]&&ae.value[ne.value].forEach((t=>{if(void 0!==t.from){let l=o+t.from-1,a=o+(t.to||Z.value)-1;for(let o=l;o<=a;o++)Object.assign(e[o],{range:t.range,unelevated:!0,color:A.value,textColor:E.value});Object.assign(e[l],{rangeFrom:!0,flat:!1}),void 0!==t.to&&Object.assign(e[a],{rangeTo:!0,flat:!1})}else if(void 0!==t.to){let l=o+t.to-1;for(let a=o;a<=l;a++)Object.assign(e[a],{range:t.range,unelevated:!0,color:A.value,textColor:E.value});Object.assign(e[l],{flat:!1,rangeTo:!0})}else{let l=o+Z.value-1;for(let a=o;a<=l;a++)Object.assign(e[a],{range:t.range,unelevated:!0,color:A.value,textColor:E.value})}})),void 0!==oe.value){let t=o+oe.value.from-1,l=o+oe.value.to-1;for(let a=t;a<=l;a++)e[a].color=A.value,e[a].editRange=!0;!0===oe.value.includeFrom&&(e[t].editRangeFrom=!0),!0===oe.value.includeTo&&(e[l].editRangeTo=!0)}C.value.year===q.value.year&&C.value.month===q.value.month&&(e[o+q.value.day-1].today=!0);let n=e.length%7;if(n>0){let t=7-n;for(let l=1;l<=t;l++)e.push({i:l,fill:!0})}return e.forEach((e=>{let t="q-date__calendar-item ";!0===e.fill?t+="q-date__calendar-item--fill":(t+="q-date__calendar-item--"+(!0===e.in?"in":"out"),void 0!==e.range&&(t+=" q-date__range"+(!0===e.rangeTo?"-to":!0===e.rangeFrom?"-from":"")),!0===e.editRange&&(t+=` q-date__edit-range${!0===e.editRangeFrom?"-from":""}${!0===e.editRangeTo?"-to":""}`),(void 0!==e.range||!0===e.editRange)&&(t+=` text-${e.color}`)),e.classes=t})),e})),de=o((()=>!0===l.disable?{"aria-disabled":"true"}:{}));function ce(){let{year:e,month:t,day:l}=q.value,a={...C.value,year:e,month:t,day:l},o=le.value[gi(a)];(void 0===o||!1===o.includes(a.day))&&Ce(a),ve(a.year,a.month)}function ve(e,t){$.value="Calendar",xe(e,t)}function pe(){return"persian"===l.calendar?"YYYY/MM/DD":l.mask}function fe(e,t,a){return li(e,t,a,l.calendar,{hour:0,minute:0,second:0,millisecond:0})}function me(e,t){let a=!0===Array.isArray(l.modelValue)?l.modelValue:l.modelValue?[l.modelValue]:[];if(0===a.length)return ge();let o=a[a.length-1],n=fe(void 0!==o.from?o.from:o,e,t);return null===n.dateHash?ge():n}function ge(){let e,t;if(void 0!==l.defaultYearMonth){let a=l.defaultYearMonth.split("/");e=parseInt(a[0],10),t=parseInt(a[1],10)}else{let l=void 0!==q.value?q.value:b();e=l.year,t=l.month}return{year:e,month:t,day:1,hour:0,minute:0,second:0,millisecond:0,dateHash:e+"/"+at(t)+"/01"}}function he(e){let t=C.value.year,l=Number(C.value.month)+e;13===l?(l=1,t++):0===l&&(l=12,t--),xe(t,l),!0===P.value&&Se("month")}function be(e){xe(Number(C.value.year)+e,C.value.month),!0===P.value&&Se("year")}function ye(e){xe(e,C.value.month),$.value="Years"===l.defaultView?"Months":"Calendar",!0===P.value&&Se("year")}function we(e){return{year:e.year,month:e.month,day:e.day}}function xe(e,t,l){if(null!==J.value&&e<=J.value.year&&((t<J.value.month||e<J.value.year)&&(t=J.value.month),e=J.value.year),null!==ee.value&&e>=ee.value.year&&((t>ee.value.month||e>ee.value.year)&&(t=ee.value.month),e=ee.value.year),void 0!==l){let{hour:e,minute:t,second:a,millisecond:o,timezoneOffset:n,timeHash:i}=l;Object.assign(C.value,{hour:e,minute:t,second:a,millisecond:o,timezoneOffset:n,timeHash:i})}let a=e+"/"+at(t)+"/01";a!==C.value.dateHash&&(T.value=C.value.dateHash<a==(!0!==d.lang.rtl)?"left":"right",e!==C.value.year&&(B.value=T.value),m((()=>{z.value=e-e%ci-(e<0?ci:0),Object.assign(C.value,{year:e,month:t,day:1,dateHash:a})})))}function _e(e,t,a){let o=null!==e&&1===e.length&&!1===l.multiple?e[0]:e;s=o;let{reason:n,details:i}=ke(t,a);r("update:modelValue",o,n,i)}function Se(e){let t=void 0!==R.value[0]&&null!==R.value[0].dateHash?{...R.value[0]}:{...C.value};m((()=>{t.year=C.value.year,t.month=C.value.month;let a="persian"!==l.calendar?new Date(t.year,t.month,0).getDate():En(t.year,t.month);t.day=Math.min(Math.max(1,t.day),a);let o=qe(t);s=o;let{details:n}=ke("",t);r("update:modelValue",o,e,n)}))}function ke(e,t){return void 0!==t.from?{reason:`${e}-range`,details:{...we(t.target),from:we(t.from),to:we(t.to)}}:{reason:`${e}-day`,details:we(t)}}function qe(e,t,l){return void 0!==e.from?{from:I.value(e.from,t,l),to:I.value(e.to,t,l)}:I.value(e,t,l)}function Ce(e){let t;if(!0===l.multiple)if(void 0!==e.from){let l=Qn(e.from),a=Qn(e.to),o=R.value.filter((e=>e.dateHash<l||e.dateHash>a)),n=N.value.filter((({from:e,to:t})=>t.dateHash<l||e.dateHash>a));t=o.concat(n).concat(e).map((e=>qe(e)))}else{let l=F.value.slice();l.push(qe(e)),t=l}else t=qe(e);_e(t,"add",e)}function $e(e){if(!0===l.noUnset)return;let t=null;if(!0===l.multiple&&!0===Array.isArray(l.modelValue)){let a=qe(e);t=void 0!==e.from?l.modelValue.filter((e=>void 0===e.from||e.from!==a.from&&e.to!==a.to)):l.modelValue.filter((e=>e!==a)),0===t.length&&(t=null)}_e(t,"remove",e)}function Me(e,t,a){let o=R.value.concat(N.value).map((l=>qe(l,e,t))).filter((e=>void 0!==e.from?null!==e.from.dateHash&&null!==e.to.dateHash:null!==e.dateHash));r("update:modelValue",(!0===l.multiple?o:o[0])||null,a)}function Te(){if(!0!==l.minimal)return t("div",{class:"q-date__header "+g.value},[t("div",{class:"relative-position"},[t(f,{name:"q-transition--fade"},(()=>t("div",{key:"h-yr-"+W.value,class:"q-date__header-subtitle q-date__header-link "+("Years"===$.value?"q-date__header-link--active":"cursor-pointer"),tabindex:p.value,...v("vY",{onClick(){$.value="Years"},onKeyup(e){13===e.keyCode&&($.value="Years")}})},[W.value])))]),t("div",{class:"q-date__header-title relative-position flex no-wrap"},[t("div",{class:"relative-position col"},[t(f,{name:"q-transition--fade"},(()=>t("div",{key:"h-sub"+D.value,class:"q-date__header-title-label q-date__header-link "+("Calendar"===$.value?"q-date__header-link--active":"cursor-pointer"),tabindex:p.value,...v("vC",{onClick(){$.value="Calendar"},onKeyup(e){13===e.keyCode&&($.value="Calendar")}})},[D.value])))]),!0===l.todayBtn?t($l,{class:"q-date__header-today self-start",icon:d.iconSet.datetime.today,flat:!0,size:"sm",round:!0,tabindex:p.value,onClick:ce}):null])])}function Be({label:e,type:l,key:a,dir:o,goTo:n,boundaries:i,cls:r}){return[t("div",{class:"row items-center q-date__arrow"},[t($l,{round:!0,dense:!0,size:"sm",flat:!0,icon:K.value[0],tabindex:p.value,disable:!1===i.prev,...v("go-#"+l,{onClick(){n(-1)}})})]),t("div",{class:"relative-position overflow-hidden flex flex-center"+r},[t(f,{name:"q-transition--jump-"+o},(()=>t("div",{key:a},[t($l,{flat:!0,dense:!0,noCaps:!0,label:e,tabindex:p.value,...v("view#"+l,{onClick:()=>{$.value=l}})})])))]),t("div",{class:"row items-center q-date__arrow"},[t($l,{round:!0,dense:!0,size:"sm",flat:!0,icon:K.value[1],tabindex:p.value,disable:!1===i.next,...v("go+#"+l,{onClick(){n(1)}})})])]}n((()=>l.modelValue),(e=>{if(s===e)s=0;else{let e=me(x.value,_.value);xe(e.year,e.month,e)}})),n($,(()=>{null!==w.value&&!0===u.$el.contains(document.activeElement)&&w.value.focus()})),n((()=>C.value.year+"|"+C.value.month),(()=>{r("navigation",{year:C.value.year,month:C.value.month})})),n(S,(e=>{Me(e,_.value,"mask"),x.value=e})),n(k,(e=>{Me(x.value,e,"locale"),_.value=e}));let Le={Calendar:()=>[t("div",{key:"calendar-view",class:"q-date__view q-date__calendar"},[t("div",{class:"q-date__navigation row items-center no-wrap"},Be({label:_.value.months[C.value.month-1],type:"Months",key:C.value.month,dir:T.value,goTo:he,boundaries:te.value.month,cls:" col"}).concat(Be({label:C.value.year,type:"Years",key:C.value.year,dir:B.value,goTo:be,boundaries:te.value.year,cls:""}))),t("div",{class:"q-date__calendar-weekdays row items-center no-wrap"},X.value.map((e=>t("div",{class:"q-date__calendar-item"},[t("div",e)])))),t("div",{class:"q-date__calendar-days-container relative-position overflow-hidden"},[t(f,{name:"q-transition--slide-"+T.value},(()=>t("div",{key:ne.value,class:"q-date__calendar-days fit"},ue.value.map((e=>t("div",{class:e.classes},[!0===e.in?t($l,{class:!0===e.today?"q-date__today":"",dense:!0,flat:e.flat,unelevated:e.unelevated,color:e.color,textColor:e.textColor,label:e.i,tabindex:p.value,...v("day#"+e.i,{onClick:()=>{!function(e){let t={...C.value,day:e};if(!1===l.range)return void function(e,t){let l=le.value[t];(void 0!==l&&!0===l.includes(e.day)?$e:Ce)(e)}(t,ne.value);if(null===V.value){let a=ue.value.find((t=>!0!==t.fill&&t.i===e));if(!0!==l.noUnset&&void 0!==a.range)return void $e({target:t,from:a.range.from,to:a.range.to});if(!0===a.selected)return void $e(t);let o=Qn(t);V.value={init:t,initHash:o,final:t,finalHash:o},r("rangeStart",we(t))}else{let e=V.value.initHash,l=Qn(t),a=e<=l?{from:V.value.init,to:t}:{from:t,to:V.value.init};V.value=null,Ce(e===l?t:{target:t,...a}),r("rangeEnd",{from:we(a.from),to:we(a.to)})}}(e.i)},onMouseover:()=>{!function(e){if(null!==V.value){let t={...C.value,day:e};Object.assign(V.value,{final:t,finalHash:Qn(t)})}}(e.i)}})},!1!==e.event?()=>t("div",{class:"q-date__event bg-"+e.event}):null):t("div",""+e.i)]))))))])])],Months(){let e=C.value.year===q.value.year,a=e=>null!==J.value&&C.value.year===J.value.year&&J.value.month>e||null!==ee.value&&C.value.year===ee.value.year&&ee.value.month<e,o=_.value.monthsShort.map(((l,o)=>{let n=C.value.month===o+1;return t("div",{class:"q-date__months-item flex flex-center"},[t($l,{class:!0===e&&q.value.month===o+1?"q-date__today":null,flat:!0!==n,label:l,unelevated:n,color:!0===n?A.value:null,textColor:!0===n?E.value:null,tabindex:p.value,disable:a(o+1),...v("month#"+o,{onClick:()=>{!function(e){xe(C.value.year,e),$.value="Calendar",!0===P.value&&Se("month")}(o+1)}})})])}));return!0===l.yearsInMonthView&&o.unshift(t("div",{class:"row no-wrap full-width"},[Be({label:C.value.year,type:"Years",key:C.value.year,dir:B.value,goTo:be,boundaries:te.value.year,cls:" col"})])),t("div",{key:"months-view",class:"q-date__view q-date__months flex flex-center"},o)},Years(){let e=z.value,l=e+ci,a=[],o=e=>null!==J.value&&J.value.year>e||null!==ee.value&&ee.value.year<e;for(let n=e;n<=l;n++){let e=C.value.year===n;a.push(t("div",{class:"q-date__years-item flex flex-center"},[t($l,{key:"yr"+n,class:q.value.year===n?"q-date__today":null,flat:!e,label:n,dense:!0,unelevated:e,color:!0===e?A.value:null,textColor:!0===e?E.value:null,tabindex:p.value,disable:o(n),...v("yr#"+n,{onClick:()=>{ye(n)}})})]))}return t("div",{class:"q-date__view q-date__years flex flex-center"},[t("div",{class:"col-auto"},[t($l,{round:!0,dense:!0,flat:!0,icon:K.value[0],tabindex:p.value,disable:o(e),...v("y-",{onClick:()=>{z.value-=ci}})})]),t("div",{class:"q-date__years-content col self-stretch row items-center"},a),t("div",{class:"col-auto"},[t($l,{round:!0,dense:!0,flat:!0,icon:K.value[1],tabindex:p.value,disable:o(l),...v("y+",{onClick:()=>{z.value+=ci}})})])])}};return Object.assign(u,{setToday:ce,setView:function(e){!0===pi(e)&&($.value=e)},offsetCalendar:function(e,t){["month","year"].includes(e)&&("month"===e?he:be)(!0===t?-1:1)},setCalendarTo:ve,setEditingRange:function(e,t){if(!1===l.range||!e)return void(V.value=null);let a=Object.assign({...C.value},e),o=void 0!==t?Object.assign({...C.value},t):a;V.value={init:a,initHash:Qn(a),final:o,finalHash:Qn(o)},ve(a.year,a.month)}}),()=>{let e=[t("div",{class:"q-date__content col relative-position"},[t(f,{name:"q-transition--fade"},Le[$.value])])],a=pt(i.default);return void 0!==a&&e.push(t("div",{class:"q-date__actions"},a)),void 0!==l.name&&!0!==l.disable&&y(e,"push"),t("div",{class:O.value,...de.value},[Te(),t("div",{ref:w,class:"q-date__main col column",tabindex:-1},e)])}}});function bi(e,t,l){let a;function o(){void 0!==a&&(ge.remove(a),a=void 0)}return i((()=>{!0===e.value&&o()})),{removeFromHistory:o,addToHistory(){a={condition:()=>!0===l.value,handler:t},ge.add(a)}}}var yi,wi,xi,_i,Si,ki,qi=0,Ci=!1,$i=null;function Mi(e){(function(e){if(e.target===document.body||e.target.classList.contains("q-layout__backdrop"))return!0;let t=function(e){if(e.path)return e.path;if(e.composedPath)return e.composedPath();let t=[],l=e.target;for(;l;){if(t.push(l),"HTML"===l.tagName)return t.push(document),t.push(window),t;l=l.parentElement}}(e),l=e.shiftKey&&!e.deltaX,a=!l&&Math.abs(e.deltaX)<=Math.abs(e.deltaY),o=l||a?e.deltaY:e.deltaX;for(let n=0;n<t.length;n++){let e=t[n];if(pa(e,a))return a?o<0&&0===e.scrollTop||o>0&&e.scrollTop+e.clientHeight===e.scrollHeight:o<0&&0===e.scrollLeft||o>0&&e.scrollLeft+e.clientWidth===e.scrollWidth}return!0})(e)&&ae(e)}function Ti(e){e.target===document&&(document.scrollingElement.scrollTop=document.scrollingElement.scrollTop)}function Bi(e){!0!==Ci&&(Ci=!0,requestAnimationFrame((()=>{Ci=!1;let{height:t}=e.target,{clientHeight:l,scrollTop:a}=document.scrollingElement;(void 0===xi||t!==window.innerHeight)&&(xi=l-t,document.scrollingElement.scrollTop=a),a>xi&&(document.scrollingElement.scrollTop-=Math.ceil((a-xi)/8))})))}function Li(e){let t=document.body,l=void 0!==window.visualViewport;if("add"===e){let{overflowY:e,overflowX:a}=window.getComputedStyle(t);yi=na(window),wi=oa(window),_i=t.style.left,Si=t.style.top,ki=window.location.href,t.style.left=`-${yi}px`,t.style.top=`-${wi}px`,"hidden"!==a&&("scroll"===a||t.scrollWidth>window.innerWidth)&&t.classList.add("q-body--force-scrollbar-x"),"hidden"!==e&&("scroll"===e||t.scrollHeight>window.innerHeight)&&t.classList.add("q-body--force-scrollbar-y"),t.classList.add("q-body--prevent-scroll"),document.qScrollPrevented=!0,!0===W.is.ios&&(!0===l?(window.scrollTo(0,0),window.visualViewport.addEventListener("resize",Bi,Z.passiveCapture),window.visualViewport.addEventListener("scroll",Bi,Z.passiveCapture),window.scrollTo(0,0)):window.addEventListener("scroll",Ti,Z.passiveCapture))}!0===W.is.desktop&&!0===W.is.mac&&window[`${e}EventListener`]("wheel",Mi,Z.notPassive),"remove"===e&&(!0===W.is.ios&&(!0===l?(window.visualViewport.removeEventListener("resize",Bi,Z.passiveCapture),window.visualViewport.removeEventListener("scroll",Bi,Z.passiveCapture)):window.removeEventListener("scroll",Ti,Z.passiveCapture)),t.classList.remove("q-body--prevent-scroll"),t.classList.remove("q-body--force-scrollbar-x"),t.classList.remove("q-body--force-scrollbar-y"),document.qScrollPrevented=!1,t.style.left=_i,t.style.top=Si,window.location.href===ki&&window.scrollTo(yi,wi),xi=void 0)}function zi(e){let t="add";if(!0===e){if(qi++,null!==$i)return clearTimeout($i),void($i=null);if(qi>1)return}else{if(0===qi||--qi>0)return;if(t="remove",!0===W.is.ios&&!0===W.is.nativeMobile)return null!==$i&&clearTimeout($i),void($i=setTimeout((()=>{Li(t),$i=null}),100))}Li(t)}function Vi(){let e;return{preventBodyScroll(t){t!==e&&(void 0!==e||!0===t)&&(e=t,zi(t))}}}var Oi=0,Ai={standard:"fixed-full flex-center",top:"fixed-top justify-center",bottom:"fixed-bottom justify-center",right:"fixed-right items-center",left:"fixed-left items-center"},Ei={standard:["scale","scale"],top:["slide-down","slide-up"],bottom:["slide-up","slide-down"],right:["slide-left","slide-right"],left:["slide-right","slide-left"]},Pi=Ze({name:"QDialog",inheritAttrs:!1,props:{...Vl,...Xl,transitionShow:String,transitionHide:String,persistent:Boolean,autoClose:Boolean,allowFocusOutside:Boolean,noEscDismiss:Boolean,noBackdropDismiss:Boolean,noRouteDismiss:Boolean,noRefocus:Boolean,noFocus:Boolean,noShake:Boolean,seamless:Boolean,maximized:Boolean,fullWidth:Boolean,fullHeight:Boolean,square:Boolean,backdropFilter:String,position:{type:String,default:"standard",validator:e=>"standard"===e||["top","bottom","left","right"].includes(e)}},emits:[...Ol,"shake","click","escapeKey"],setup(l,{slots:r,emit:s,attrs:u}){let d,c,v=a(),p=e(null),m=e(!1),g=e(!1),h=null,b=null,y=o((()=>!0!==l.persistent&&!0!==l.noRouteDismiss&&!0!==l.seamless)),{preventBodyScroll:w}=Vi(),{registerTimeout:x}=Jl(),{registerTick:_,removeTick:S}=Gl(),{transitionProps:k,transitionStyle:q}=Zl(l,(()=>Ei[l.position][0]),(()=>Ei[l.position][1])),C=o((()=>q.value+(void 0!==l.backdropFilter?`;backdrop-filter:${l.backdropFilter};-webkit-backdrop-filter:${l.backdropFilter}`:""))),{showPortal:$,hidePortal:M,portalIsAccessible:T,renderPortal:B}=Yl(v,p,(function(){return t("div",{role:"dialog","aria-modal":!0===A.value?"true":"false",...u,class:P.value},[t(f,{name:"q-transition--fade",appear:!0},(()=>!0===A.value?t("div",{class:"q-dialog__backdrop fixed-full",style:C.value,"aria-hidden":"true",tabindex:-1,onClick:D}):null)),t(f,k.value,(()=>!0===m.value?t("div",{ref:p,class:O.value,style:q.value,tabindex:-1,...E.value},pt(r.default)):null))])}),"dialog"),{hide:L}=Al({showing:m,hideOnRouteChange:y,handleShow:function(e){z(),b=!1===l.noRefocus&&null!==document.activeElement?document.activeElement:null,I(l.maximized),$(),g.value=!0,!0!==l.noFocus?(null!==document.activeElement&&document.activeElement.blur(),_(F)):S(),x((()=>{if(!0===v.proxy.$q.platform.is.ios){if(!0!==l.seamless&&document.activeElement){let{top:e,bottom:t}=document.activeElement.getBoundingClientRect(),{innerHeight:l}=window,a=void 0!==window.visualViewport?window.visualViewport.height:l;e>0&&t>a/2&&(document.scrollingElement.scrollTop=Math.min(document.scrollingElement.scrollHeight-a,t>=l?1/0:Math.ceil(document.scrollingElement.scrollTop+t-a/2))),document.activeElement.scrollIntoView()}c=!0,p.value.click(),c=!1}$(!0),g.value=!1,s("show",e)}),l.transitionDuration)},handleHide:function(e){S(),V(),H(!0),g.value=!0,M(),null!==b&&(((e&&0===e.type.indexOf("key")?b.closest('[tabindex]:not([tabindex^="-"])'):void 0)||b).focus(),b=null),x((()=>{M(!0),g.value=!1,s("hide",e)}),l.transitionDuration)},processOnMount:!0}),{addToHistory:z,removeFromHistory:V}=bi(m,L,y),O=o((()=>`q-dialog__inner flex no-pointer-events q-dialog__inner--${!0===l.maximized?"maximized":"minimized"} q-dialog__inner--${l.position} ${Ai[l.position]}`+(!0===g.value?" q-dialog__inner--animating":"")+(!0===l.fullWidth?" q-dialog__inner--fullwidth":"")+(!0===l.fullHeight?" q-dialog__inner--fullheight":"")+(!0===l.square?" q-dialog__inner--square":""))),A=o((()=>!0===m.value&&!0!==l.seamless)),E=o((()=>!0===l.autoClose?{onClick:j}:{})),P=o((()=>["q-dialog fullscreen no-pointer-events q-dialog--"+(!0===A.value?"modal":"seamless"),u.class]));function F(e){Nl((()=>{let t=p.value;null===t||!0===t.contains(document.activeElement)||(t=(""!==e?t.querySelector(e):null)||t.querySelector("[autofocus][tabindex], [data-autofocus][tabindex]")||t.querySelector("[autofocus] [tabindex], [data-autofocus] [tabindex]")||t.querySelector("[autofocus], [data-autofocus]")||t,t.focus({preventScroll:!0}))}))}function R(e){e&&"function"==typeof e.focus?e.focus({preventScroll:!0}):F(),s("shake");let t=p.value;null!==t&&(t.classList.remove("q-animate--scale"),t.classList.add("q-animate--scale"),null!==h&&clearTimeout(h),h=setTimeout((()=>{h=null,null!==p.value&&(t.classList.remove("q-animate--scale"),F())}),170))}function N(){!0!==l.seamless&&(!0===l.persistent||!0===l.noEscDismiss?!0!==l.maximized&&!0!==l.noShake&&R():(s("escapeKey"),L()))}function H(e){null!==h&&(clearTimeout(h),h=null),(!0===e||!0===m.value)&&(I(!1),!0!==l.seamless&&(w(!1),qa(Q),xa(N))),!0!==e&&(b=null)}function I(e){!0===e?!0!==d&&(Oi<1&&document.body.classList.add("q-body--dialog"),Oi++,d=!0):!0===d&&(Oi<2&&document.body.classList.remove("q-body--dialog"),Oi--,d=!1)}function j(e){!0!==c&&(L(e),s("click",e))}function D(e){!0!==l.persistent&&!0!==l.noBackdropDismiss?L(e):!0!==l.noShake&&R()}function Q(e){!0!==l.allowFocusOutside&&!0===T.value&&!0!==dl(p.value,e.target)&&F('[tabindex]:not([tabindex="-1"])')}return n((()=>l.maximized),(e=>{!0===m.value&&I(e)})),n(A,(e=>{w(e),!0===e?(ka(Q),wa(N)):(qa(Q),xa(N))})),Object.assign(v.proxy,{focus:F,shake:R,__updateRefocusTarget(e){b=e||null}}),i(H),B}}),Fi=Ze({name:"QDrawer",inheritAttrs:!1,props:{...Vl,...Et,side:{type:String,default:"left",validator:e=>["left","right"].includes(e)},width:{type:Number,default:300},mini:Boolean,miniToOverlay:Boolean,miniWidth:{type:Number,default:57},noMiniAnimation:Boolean,breakpoint:{type:Number,default:1023},showIfAbove:Boolean,behavior:{type:String,validator:e=>["default","desktop","mobile"].includes(e),default:"default"},bordered:Boolean,elevated:Boolean,overlay:Boolean,persistent:Boolean,noSwipeOpen:Boolean,noSwipeClose:Boolean,noSwipeBackdrop:Boolean},emits:[...Ol,"onLayout","miniState"],setup(l,{slots:s,emit:u,attrs:c}){let v=a(),{proxy:{$q:p}}=v,f=Pt(l,p),{preventBodyScroll:h}=Vi(),{registerTimeout:b,removeTimeout:y}=Jl(),w=d(Ve,Re);if(w===Re)return Re;let x,_,S=null,k=e("mobile"===l.behavior||"desktop"!==l.behavior&&w.totalWidth.value<=l.breakpoint),q=o((()=>!0===l.mini&&!0!==k.value)),C=o((()=>!0===q.value?l.miniWidth:l.width)),$=e(!0===l.showIfAbove&&!1===k.value||!0===l.modelValue),M=o((()=>!0!==l.persistent&&(!0===k.value||!0===U.value)));function T(e,t){if(V(),!1!==e&&w.animate(),ne(0),!0===k.value){let e=w.instances[I.value];void 0!==e&&!0===e.belowBreakpoint&&e.hide(!1),ie(1),!0!==w.isContainer.value&&h(!0)}else ie(0),!1!==e&&re(!1);b((()=>{!1!==e&&re(!0),!0!==t&&u("show",e)}),150)}function B(e,t){O(),!1!==e&&w.animate(),ie(0),ne(P.value*C.value),de(),!0!==t?b((()=>{u("hide",e)}),150):y()}let{show:L,hide:z}=Al({showing:$,hideOnRouteChange:M,handleShow:T,handleHide:B}),{addToHistory:V,removeFromHistory:O}=bi($,z,M),A={belowBreakpoint:k,hide:z},E=o((()=>"right"===l.side)),P=o((()=>(!0===p.lang.rtl?-1:1)*(!0===E.value?1:-1))),F=e(0),R=e(!1),N=e(!1),H=e(C.value*P.value),I=o((()=>!0===E.value?"left":"right")),j=o((()=>!0===$.value&&!1===k.value&&!1===l.overlay?!0===l.miniToOverlay?l.miniWidth:C.value:0)),D=o((()=>!0===l.overlay||!0===l.miniToOverlay||-1!==w.view.value.indexOf(E.value?"R":"L")||!0===p.platform.is.ios&&!0===w.isContainer.value)),Q=o((()=>!1===l.overlay&&!0===$.value&&!1===k.value)),U=o((()=>!0===l.overlay&&!0===$.value&&!1===k.value)),W=o((()=>"fullscreen q-drawer__backdrop"+(!1===$.value&&!1===R.value?" hidden":""))),K=o((()=>({backgroundColor:`rgba(0,0,0,${.4*F.value})`}))),Y=o((()=>!0===E.value?"r"===w.rows.value.top[2]:"l"===w.rows.value.top[0])),X=o((()=>!0===E.value?"r"===w.rows.value.bottom[2]:"l"===w.rows.value.bottom[0])),Z=o((()=>{let e={};return!0===w.header.space&&!1===Y.value&&(!0===D.value?e.top=`${w.header.offset}px`:!0===w.header.space&&(e.top=`${w.header.size}px`)),!0===w.footer.space&&!1===X.value&&(!0===D.value?e.bottom=`${w.footer.offset}px`:!0===w.footer.space&&(e.bottom=`${w.footer.size}px`)),e})),G=o((()=>{let e={width:`${C.value}px`,transform:`translateX(${H.value}px)`};return!0===k.value?e:Object.assign(e,Z.value)})),J=o((()=>"q-drawer__content fit "+(!0!==w.isContainer.value?"scroll":"overflow-auto"))),ee=o((()=>`q-drawer q-drawer--${l.side}`+(!0===N.value?" q-drawer--mini-animate":"")+(!0===l.bordered?" q-drawer--bordered":"")+(!0===f.value?" q-drawer--dark q-dark":"")+(!0===R.value?" no-transition":!0===$.value?"":" q-layout--prevent-focus")+(!0===k.value?" fixed q-drawer--on-top q-drawer--mobile q-drawer--top-padding":" q-drawer--"+(!0===q.value?"mini":"standard")+(!0===D.value||!0!==Q.value?" fixed":"")+(!0===l.overlay||!0===l.miniToOverlay?" q-drawer--on-top":"")+(!0===Y.value?" q-drawer--top-padding":"")))),te=o((()=>{let e=!0===p.lang.rtl?l.side:I.value;return[[Do,se,void 0,{[e]:!0,mouse:!0}]]})),le=o((()=>{let e=!0===p.lang.rtl?I.value:l.side;return[[Do,ue,void 0,{[e]:!0,mouse:!0}]]})),ae=o((()=>{let e=!0===p.lang.rtl?I.value:l.side;return[[Do,ue,void 0,{[e]:!0,mouse:!0,mouseAllDir:!0}]]}));function oe(){var e,t;e=k,t="mobile"===l.behavior||"desktop"!==l.behavior&&w.totalWidth.value<=l.breakpoint,e.value!==t&&(e.value=t)}function ne(e){void 0===e?m((()=>{e=!0===$.value?0:C.value,ne(P.value*e)})):(!0===w.isContainer.value&&!0===E.value&&(!0===k.value||Math.abs(e)===C.value)&&(e+=P.value*w.scrollbarWidth.value),H.value=e)}function ie(e){F.value=e}function re(e){let t=!0===e?"remove":!0!==w.isContainer.value?"add":"";""!==t&&document.body.classList[t]("q-body--drawer-toggle")}function se(e){if(!1!==$.value)return;let t=C.value,l=tt(e.distance.x,0,t);if(!0===e.isFinal)return l>=Math.min(75,t)==!0?L():(w.animate(),ie(0),ne(P.value*t)),void(R.value=!1);ne((!0===p.lang.rtl?!0!==E.value:E.value)?Math.max(t-l,0):Math.min(0,l-t)),ie(tt(l/t,0,1)),!0===e.isFirst&&(R.value=!0)}function ue(e){if(!0!==$.value)return;let t=C.value,a=e.direction===l.side,o=(!0===p.lang.rtl?!0!==a:a)?tt(e.distance.x,0,t):0;if(!0===e.isFinal)return Math.abs(o)<Math.min(75,t)==!0?(w.animate(),ie(1),ne(0)):z(),void(R.value=!1);ne(P.value*o),ie(tt(1-o/t,0,1)),!0===e.isFirst&&(R.value=!0)}function de(){h(!1),re(!0)}function ce(e,t){w.update(l.side,e,t)}function ve(e,t){ce("size",!0===e?l.miniWidth:t)}return n(k,(e=>{!0===e?(x=$.value,!0===$.value&&z(!1)):!1===l.overlay&&"mobile"!==l.behavior&&!1!==x&&(!0===$.value?(ne(0),ie(0),de()):L(!1))})),n((()=>l.side),((e,t)=>{w.instances[t]===A&&(w.instances[t]=void 0,w[t].space=!1,w[t].offset=0),w.instances[e]=A,w[e].size=C.value,w[e].space=Q.value,w[e].offset=j.value})),n(w.totalWidth,(()=>{(!0===w.isContainer.value||!0!==document.qScrollPrevented)&&oe()})),n((()=>l.behavior+l.breakpoint),oe),n(w.isContainer,(e=>{!0===$.value&&h(!0!==e),!0===e&&oe()})),n(w.scrollbarWidth,(()=>{ne(!0===$.value?0:void 0)})),n(j,(e=>{ce("offset",e)})),n(Q,(e=>{u("onLayout",e),ce("space",e)})),n(E,(()=>{ne()})),n(C,(e=>{ne(),ve(l.miniToOverlay,e)})),n((()=>l.miniToOverlay),(e=>{ve(e,C.value)})),n((()=>p.lang.rtl),(()=>{ne()})),n((()=>l.mini),(()=>{l.noMiniAnimation||!0===l.modelValue&&(null!==S&&clearTimeout(S),v.proxy&&v.proxy.$el&&v.proxy.$el.classList.add("q-drawer--mini-animate"),N.value=!0,S=setTimeout((()=>{S=null,N.value=!1,v&&v.proxy&&v.proxy.$el&&v.proxy.$el.classList.remove("q-drawer--mini-animate")}),150),w.animate())})),n(q,(e=>{u("miniState",e)})),w.instances[l.side]=A,ve(l.miniToOverlay,C.value),ce("space",Q.value),ce("offset",j.value),!0===l.showIfAbove&&!0!==l.modelValue&&!0===$.value&&void 0!==l["onUpdate:modelValue"]&&u("update:modelValue",!0),r((()=>{u("onLayout",Q.value),u("miniState",q.value),x=!0===l.showIfAbove;let e=()=>{(!0===$.value?T:B)(!1,!0)};0===w.totalWidth.value?_=n(w.totalWidth,(()=>{_(),_=void 0,!1===$.value&&!0===l.showIfAbove&&!1===k.value?L(!1):e()})):m(e)})),i((()=>{void 0!==_&&_(),null!==S&&(clearTimeout(S),S=null),!0===$.value&&de(),w.instances[l.side]===A&&(w.instances[l.side]=void 0,ce("size",0),ce("offset",0),ce("space",!1))})),()=>{let e=[];!0===k.value&&(!1===l.noSwipeOpen&&e.push(g(t("div",{key:"open",class:`q-drawer__opener fixed-${l.side}`,"aria-hidden":"true"}),te.value)),e.push(ht("div",{ref:"backdrop",class:W.value,style:K.value,"aria-hidden":"true",onClick:z},void 0,"backdrop",!0!==l.noSwipeBackdrop&&!0===$.value,(()=>ae.value))));let a=!0===q.value&&void 0!==s.mini,o=[t("div",{...c,key:""+a,class:[J.value,c.class]},!0===a?s.mini():pt(s.default))];return!0===l.elevated&&!0===$.value&&o.push(t("div",{class:"q-layout__shadow absolute-full overflow-hidden no-pointer-events"})),e.push(ht("aside",{ref:"content",class:ee.value,style:G.value},o,"contentclose",!0!==l.noSwipeClose&&!0===k.value,(()=>le.value))),t("div",{class:"q-drawer-container"},e)}}});function Ri(e,t){if(t&&e===t)return null;let l=e.nodeName.toLowerCase();if(!0===["div","li","ul","ol","blockquote"].includes(l))return e;let a=(window.getComputedStyle?window.getComputedStyle(e):e.currentStyle).display;return"block"===a||"table"===a?e:Ri(e.parentNode)}function Ni(e,t,l){return!(!e||e===document.body)&&(!0===l&&e===t||(t===document?document.body:t).contains(e.parentNode))}function Hi(e,t,l){if(l||((l=document.createRange()).selectNode(e),l.setStart(e,0)),0===t.count)l.setEnd(e,t.count);else if(t.count>0)if(e.nodeType===Node.TEXT_NODE)e.textContent.length<t.count?t.count-=e.textContent.length:(l.setEnd(e,t.count),t.count=0);else for(let a=0;0!==t.count&&a<e.childNodes.length;a++)l=Hi(e.childNodes[a],t,l);return l}var Ii=/^https?:\/\//,ji=class{constructor(e,t){this.el=e,this.eVm=t,this._range=null}get selection(){if(this.el){let e=document.getSelection();if(Ni(e.anchorNode,this.el,!0)&&Ni(e.focusNode,this.el,!0))return e}return null}get hasSelection(){return null!==this.selection&&0!==this.selection.toString().length}get range(){let e=this.selection;return null!==e&&e.rangeCount?e.getRangeAt(0):this._range}get parent(){let e=this.range;if(null!==e){let t=e.startContainer;return t.nodeType===document.ELEMENT_NODE?t:t.parentNode}return null}get blockParent(){let e=this.parent;return null!==e?Ri(e,this.el):null}save(e=this.range){null!==e&&(this._range=e)}restore(e=this._range){let t=document.createRange(),l=document.getSelection();null!==e?(t.setStart(e.startContainer,e.startOffset),t.setEnd(e.endContainer,e.endOffset),l.removeAllRanges(),l.addRange(t)):(l.selectAllChildren(this.el),l.collapseToEnd())}savePosition(){let e,t=-1,l=document.getSelection(),a=this.el.parentNode;if(l.focusNode&&Ni(l.focusNode,a))for(e=l.focusNode,t=l.focusOffset;e&&e!==a;)e!==this.el&&e.previousSibling?(e=e.previousSibling,t+=e.textContent.length):e=e.parentNode;this.savedPos=t}restorePosition(e=0){if(this.savedPos>0&&this.savedPos<e){let e=window.getSelection(),t=Hi(this.el,{count:this.savedPos});t&&(t.collapse(!1),e.removeAllRanges(),e.addRange(t))}}hasParent(e,t){let l=t?this.parent:this.blockParent;return null!==l&&l.nodeName.toLowerCase()===e.toLowerCase()}hasParents(e,t,l=this.parent){return null!==l&&(!0===e.includes(l.nodeName.toLowerCase())||!0===t&&this.hasParents(e,t,l.parentNode))}is(e,t){if(null===this.selection)return!1;switch(e){case"formatBlock":return"DIV"===t&&this.parent===this.el||this.hasParent(t,"PRE"===t);case"link":return this.hasParent("A",!0);case"fontSize":return document.queryCommandValue(e)===t;case"fontName":let l=document.queryCommandValue(e);return l===`"${t}"`||l===t;case"fullscreen":return this.eVm.inFullscreen.value;case"viewsource":return this.eVm.isViewingSource.value;case void 0:return!1;default:let a=document.queryCommandState(e);return void 0!==t?a===t:a}}getParentAttribute(e){return null!==this.parent?this.parent.getAttribute(e):null}can(e){return"outdent"===e?this.hasParents(["blockquote","li"],!0):"indent"===e?this.hasParents(["li"],!0):"link"===e?null!==this.selection||this.is("link"):void 0}apply(e,t,l=G){if("formatBlock"===e)["BLOCKQUOTE","H1","H2","H3","H4","H5","H6"].includes(t)&&this.is(e,t)&&(e="outdent",t=null),"PRE"===t&&this.is(e,"PRE")&&(t="P");else{if("print"===e){l();let e=window.open();return e.document.write(`\n        <!doctype html>\n        <html>\n          <head>\n            <title>Print - ${document.title}</title>\n          </head>\n          <body>\n            <div>${this.el.innerHTML}</div>\n          </body>\n        </html>\n      `),e.print(),void e.close()}if("link"===e){let e=this.getParentAttribute("href");if(null===e){let e=this.selectWord(this.selection),t=e?e.toString():"";if(!(t.length||this.range&&this.range.cloneContents().querySelector("img")))return;this.eVm.editLinkUrl.value=Ii.test(t)?t:"https://",document.execCommand("createLink",!1,this.eVm.editLinkUrl.value),this.save(e.getRangeAt(0))}else this.eVm.editLinkUrl.value=e,this.range.selectNodeContents(this.parent),this.save();return}if("fullscreen"===e)return this.eVm.toggleFullscreen(),void l();if("viewsource"===e)return this.eVm.isViewingSource.value=!1===this.eVm.isViewingSource.value,this.eVm.setContent(this.eVm.props.modelValue),void l()}document.execCommand(e,!1,t),l()}selectWord(e){if(null===e||!0!==e.isCollapsed||void 0===e.modify)return e;let t=document.createRange();t.setStart(e.anchorNode,e.anchorOffset),t.setEnd(e.focusNode,e.focusOffset);let l=t.collapsed?["backward","forward"]:["forward","backward"];t.detach();let a=e.focusNode,o=e.focusOffset;return e.collapse(e.anchorNode,e.anchorOffset),e.modify("move",l[0],"character"),e.modify("move",l[1],"word"),e.extend(a,o),e.modify("extend",l[1],"character"),e.modify("extend",l[0],"word"),e}},Di=Ze({name:"QTooltip",inheritAttrs:!1,props:{...Bl,...Vl,...Xl,maxHeight:{type:String,default:null},maxWidth:{type:String,default:null},transitionShow:{default:"jump-down"},transitionHide:{default:"jump-up"},anchor:{type:String,default:"bottom middle",validator:Va},self:{type:String,default:"top middle",validator:Va},offset:{type:Array,default:()=>[14,14],validator:Oa},scrollTarget:{default:void 0},delay:{type:Number,default:0},hideDelay:{type:Number,default:0},persistent:Boolean},emits:[...Ol],setup(l,{slots:r,emit:s,attrs:u}){let d,c,v=a(),{proxy:{$q:p}}=v,m=e(null),g=e(!1),h=o((()=>Ea(l.anchor,p.lang.rtl))),b=o((()=>Ea(l.self,p.lang.rtl))),y=o((()=>!0!==l.persistent)),{registerTick:w,removeTick:x}=Gl(),{registerTimeout:_}=Jl(),{transitionProps:S,transitionStyle:k}=Zl(l),{localScrollTarget:q,changeScrollEvent:C,unconfigureScrollTarget:$}=zl(l,F),{anchorEl:M,canShow:T,anchorEvents:B}=Ll({showing:g,configureAnchorEl:function(){if(!0===l.noParentEvent||null===M.value)return;let e=!0===p.platform.is.mobile?[[M.value,"touchstart","delayShow","passive"]]:[[M.value,"mouseenter","delayShow","passive"],[M.value,"mouseleave","delayHide","passive"]];ne(B,"anchor",e)}}),{show:L,hide:z}=Al({showing:g,canShow:T,handleShow:function(e){V(),w((()=>{c=new MutationObserver((()=>P())),c.observe(m.value,{attributes:!1,childList:!0,characterData:!0,subtree:!0}),P(),F()})),void 0===d&&(d=n((()=>p.screen.width+"|"+p.screen.height+"|"+l.self+"|"+l.anchor+"|"+p.lang.rtl),P)),_((()=>{V(!0),s("show",e)}),l.transitionDuration)},handleHide:function(e){x(),O(),E(),_((()=>{O(!0),s("hide",e)}),l.transitionDuration)},hideOnRouteChange:y,processOnMount:!0});Object.assign(B,{delayShow:function(e){if(!0===p.platform.is.mobile){Tl(),document.body.classList.add("non-selectable");let e=M.value,t=["touchmove","touchcancel","touchend","click"].map((t=>[e,t,"delayHide","passiveCapture"]));ne(B,"tooltipTemp",t)}_((()=>{L(e)}),l.delay)},delayHide:function(e){!0===p.platform.is.mobile&&(ie(B,"tooltipTemp"),Tl(),setTimeout((()=>{document.body.classList.remove("non-selectable")}),10)),_((()=>{z(e)}),l.hideDelay)}});let{showPortal:V,hidePortal:O,renderPortal:A}=Yl(v,m,(function(){return t(f,S.value,R)}),"tooltip");if(!0===p.platform.is.mobile){let e={anchorEl:M,innerRef:m,onClickOutside:e=>(z(e),e.target.classList.contains("q-dialog__backdrop")&&ae(e),!0)},t=o((()=>null===l.modelValue&&!0!==l.persistent&&!0===g.value));n(t,(t=>{(!0===t?La:za)(e)})),i((()=>{za(e)}))}function E(){void 0!==c&&(c.disconnect(),c=void 0),void 0!==d&&(d(),d=void 0),$(),ie(B,"tooltipTemp")}function P(){Fa({targetEl:m.value,offset:l.offset,anchorEl:M.value,anchorOrigin:h.value,selfOrigin:b.value,maxHeight:l.maxHeight,maxWidth:l.maxWidth})}function F(){if(null!==M.value||void 0!==l.scrollTarget){q.value=la(M.value,l.scrollTarget);let e=!0===l.noParentEvent?P:z;C(q.value,e)}}function R(){return!0===g.value?t("div",{...u,ref:m,class:["q-tooltip q-tooltip--style q-position-engine no-pointer-events",u.class],style:[u.style,k.value],role:"tooltip"},pt(r.default)):null}return i(E),Object.assign(v.proxy,{updatePosition:P}),A}}),Qi=Ze({name:"QItem",props:{...Et,...tl,tag:{type:String,default:"div"},active:{type:Boolean,default:null},clickable:Boolean,dense:Boolean,insetLevel:Number,tabindex:[String,Number],focused:Boolean,manualFocus:Boolean},emits:["click","keyup"],setup(l,{slots:n,emit:i}){let{proxy:{$q:r}}=a(),s=Pt(l,r),{hasLink:u,linkAttrs:d,linkClass:c,linkTag:v,navigateOnClick:p}=ll(),f=e(null),m=e(null),g=o((()=>!0===l.clickable||!0===u.value||"label"===l.tag)),h=o((()=>!0!==l.disable&&!0===g.value)),b=o((()=>"q-item q-item-type row no-wrap"+(!0===l.dense?" q-item--dense":"")+(!0===s.value?" q-item--dark":"")+(!0===u.value&&null===l.active?c.value:!0===l.active?" q-item--active"+(void 0!==l.activeClass?` ${l.activeClass}`:""):"")+(!0===l.disable?" disabled":"")+(!0===h.value?" q-item--clickable q-link cursor-pointer "+(!0===l.manualFocus?"q-manual-focusable":"q-focusable q-hoverable")+(!0===l.focused?" q-manual-focusable--focused":""):""))),y=o((()=>void 0===l.insetLevel?null:{["padding"+(!0===r.lang.rtl?"Right":"Left")]:16+56*l.insetLevel+"px"}));function w(e){!0===h.value&&(null!==m.value&&(!0!==e.qKeyEvent&&document.activeElement===f.value?m.value.focus():document.activeElement===m.value&&f.value.focus()),p(e))}function x(e){if(!0===h.value&&!0===qe(e,[13,32])){ae(e),e.qKeyEvent=!0;let t=new MouseEvent("click",e);t.qKeyEvent=!0,f.value.dispatchEvent(t)}i("keyup",e)}return()=>{let e={ref:f,class:b.value,style:y.value,role:"listitem",onClick:w,onKeyup:x};return!0===h.value?(e.tabindex=l.tabindex||"0",Object.assign(e,d.value)):!0===g.value&&(e["aria-disabled"]="true"),t(v.value,e,function(){let e=ft(n.default,[]);return!0===h.value&&e.unshift(t("div",{class:"q-focus-helper",tabindex:-1,ref:m})),e}())}}}),Ui=Ze({name:"QItemSection",props:{avatar:Boolean,thumbnail:Boolean,side:Boolean,top:Boolean,noWrap:Boolean},setup(e,{slots:l}){let a=o((()=>"q-item__section column q-item__section--"+(!0===e.avatar||!0===e.side||!0===e.thumbnail?"side":"main")+(!0===e.top?" q-item__section--top justify-start":" justify-center")+(!0===e.avatar?" q-item__section--avatar":"")+(!0===e.thumbnail?" q-item__section--thumbnail":"")+(!0===e.noWrap?" q-item__section--nowrap":"")));return()=>t("div",{class:a.value},pt(l.default))}});function Wi(e,t,l){t.handler?t.handler(e,l,l.caret):l.runCmd(t.cmd,t.param)}function Ki(e){return t("div",{class:"q-editor__toolbar-group"},e)}function Yi(e,l,a,o=!1){let n=o||"toggle"===l.type&&(l.toggled?l.toggled(e):l.cmd&&e.caret.is(l.cmd,l.param)),i=[];if(l.tip&&e.$q.platform.is.desktop){let e=l.key?t("div",[t("small",`(CTRL + ${String.fromCharCode(l.key)})`)]):null;i.push(t(Di,{delay:1e3},(()=>[t("div",{innerHTML:l.tip}),e])))}return t($l,{...e.buttonProps.value,icon:null!==l.icon?l.icon:void 0,color:n?l.toggleColor||e.props.toolbarToggleColor:l.color||e.props.toolbarColor,textColor:n&&!e.props.toolbarPush?null:l.textColor||e.props.toolbarTextColor,label:l.label,disable:!!l.disable&&("function"!=typeof l.disable||l.disable(e)),size:"sm",onClick(t){a&&a(),Wi(t,l,e)}},(()=>i))}function Xi(e){if(e.caret)return e.buttons.value.filter((t=>!e.isViewingSource.value||t.find((e=>"viewsource"===e.cmd)))).map((l=>Ki(l.map((l=>(!e.isViewingSource.value||"viewsource"===l.cmd)&&("slot"===l.type?pt(e.slots[l.slot]):"dropdown"===l.type?function(e,l){let a,o,n="only-icons"===l.list,i=l.label,r=null!==l.icon?l.icon:void 0;function s(){d.component.proxy.hide()}if(n)o=l.options.map((t=>{let l=void 0===t.type&&e.caret.is(t.cmd,t.param);return l&&(i=t.tip,r=null!==t.icon?t.icon:void 0),Yi(e,t,s,l)})),a=e.toolbarBackgroundClass.value,o=[Ki(o)];else{let n=void 0!==e.props.toolbarToggleColor?`text-${e.props.toolbarToggleColor}`:null,u=void 0!==e.props.toolbarTextColor?`text-${e.props.toolbarTextColor}`:null,d="no-icons"===l.list;o=l.options.map((l=>{let a=!!l.disable&&l.disable(e),o=void 0===l.type&&e.caret.is(l.cmd,l.param);o&&(i=l.tip,r=null!==l.icon?l.icon:void 0);let c=l.htmlTip;return t(Qi,{active:o,activeClass:n,clickable:!0,disable:a,dense:!0,onClick(t){s(),null!==e.contentRef.value&&e.contentRef.value.focus(),e.caret.restore(),Wi(t,l,e)}},(()=>[!0===d?null:t(Ui,{class:o?n:u,side:!0},(()=>t(zt,{name:null!==l.icon?l.icon:void 0}))),t(Ui,c?()=>t("div",{class:"text-no-wrap",innerHTML:l.htmlTip}):l.tip?()=>t("div",{class:"text-no-wrap"},l.tip):void 0)]))})),a=[e.toolbarBackgroundClass.value,u]}let u=l.highlight&&i!==l.label,d=t(Xa,{...e.buttonProps.value,noCaps:!0,noWrap:!0,color:u?e.props.toolbarToggleColor:e.props.toolbarColor,textColor:u&&!e.props.toolbarPush?null:e.props.toolbarTextColor,label:l.fixedLabel?l.label:i,icon:l.fixedIcon?null!==l.icon?l.icon:void 0:r,contentClass:a,onShow:t=>e.emit("dropdownShow",t),onHide:t=>e.emit("dropdownHide",t),onBeforeShow:t=>e.emit("dropdownBeforeShow",t),onBeforeHide:t=>e.emit("dropdownBeforeHide",t)},(()=>o));return d}(e,l):Yi(e,l)))))))}var Zi=/^on[A-Z]/;function Gi(){let{attrs:t,vnode:l}=a(),o={listeners:e({}),attributes:e({})};function n(){let e={},a={};for(let l in t)"class"!==l&&"style"!==l&&!1===Zi.test(l)&&(e[l]=t[l]);for(let t in l.props)!0===Zi.test(t)&&(a[t]=l.props[t]);o.attributes.value=e,o.listeners.value=a}return s(n),n(),o}var Ji=Object.prototype.toString,er=Object.prototype.hasOwnProperty,tr=new Set(["Boolean","Number","String","Function","Array","Date","RegExp"].map((e=>"[object "+e+"]")));function lr(e){if(e!==Object(e)||!0===tr.has(Ji.call(e))||e.constructor&&!1===er.call(e,"constructor")&&!1===er.call(e.constructor.prototype,"isPrototypeOf"))return!1;let t;for(t in e);return void 0===t||er.call(e,t)}function ar(){let e,t,l,a,o,n,i=arguments[0]||{},r=1,s=!1,u=arguments.length;for("boolean"==typeof i&&(s=i,i=arguments[1]||{},r=2),Object(i)!==i&&"function"!=typeof i&&(i={}),u===r&&(i=this,r--);r<u;r++)if(null!==(e=arguments[r]))for(t in e)l=i[t],a=e[t],i!==a&&(!0===s&&a&&((o=Array.isArray(a))||!0===lr(a))?(n=!0===o?!0===Array.isArray(l)?l:[]:!0===lr(l)?l:{},i[t]=ar(s,n,a)):void 0!==a&&(i[t]=a));return i}var or=Ze({name:"QEditor",props:{...Et,...wo,modelValue:{type:String,required:!0},readonly:Boolean,disable:Boolean,minHeight:{type:String,default:"10rem"},maxHeight:String,height:String,definitions:Object,fonts:Object,placeholder:String,toolbar:{type:Array,validator:e=>0===e.length||e.every((e=>e.length)),default:()=>[["left","center","right","justify"],["bold","italic","underline","strike"],["undo","redo"]]},toolbarColor:String,toolbarBg:String,toolbarTextColor:String,toolbarToggleColor:{type:String,default:"primary"},toolbarOutline:Boolean,toolbarPush:Boolean,toolbarRounded:Boolean,paragraphTag:{type:String,validator:e=>["div","p"].includes(e),default:"div"},contentStyle:Object,contentClass:[Object,Array,String],square:Boolean,flat:Boolean,dense:Boolean},emits:[...xo,"update:modelValue","keydown","click","mouseup","keyup","touchend","focus","blur","dropdownShow","dropdownHide","dropdownBeforeShow","dropdownBeforeHide","linkShow","linkHide"],setup(l,{slots:s,emit:u}){let d,c,{proxy:v}=a(),{$q:p}=v,f=Pt(l,p),{inFullscreen:g,toggleFullscreen:h}=_o(),b=Gi(),y=e(null),w=e(null),x=e(null),_=e(!1),S=o((()=>!l.readonly&&!l.disable)),k=l.modelValue;document.execCommand("defaultParagraphSeparator",!1,l.paragraphTag),d=window.getComputedStyle(document.body).fontFamily;let q=o((()=>l.toolbarBg?` bg-${l.toolbarBg}`:"")),C=o((()=>({type:"a",flat:!0!==l.toolbarOutline&&!0!==l.toolbarPush,noWrap:!0,outline:l.toolbarOutline,push:l.toolbarPush,rounded:l.toolbarRounded,dense:!0,color:l.toolbarColor,disable:!S.value,size:"sm"}))),$=o((()=>{let e=p.lang.editor,t=p.iconSet.editor;return{bold:{cmd:"bold",icon:t.bold,tip:e.bold,key:66},italic:{cmd:"italic",icon:t.italic,tip:e.italic,key:73},strike:{cmd:"strikeThrough",icon:t.strikethrough,tip:e.strikethrough,key:83},underline:{cmd:"underline",icon:t.underline,tip:e.underline,key:85},unordered:{cmd:"insertUnorderedList",icon:t.unorderedList,tip:e.unorderedList},ordered:{cmd:"insertOrderedList",icon:t.orderedList,tip:e.orderedList},subscript:{cmd:"subscript",icon:t.subscript,tip:e.subscript,htmlTip:"x<subscript>2</subscript>"},superscript:{cmd:"superscript",icon:t.superscript,tip:e.superscript,htmlTip:"x<superscript>2</superscript>"},link:{cmd:"link",disable:e=>e.caret&&!e.caret.can("link"),icon:t.hyperlink,tip:e.hyperlink,key:76},fullscreen:{cmd:"fullscreen",icon:t.toggleFullscreen,tip:e.toggleFullscreen,key:70},viewsource:{cmd:"viewsource",icon:t.viewSource,tip:e.viewSource},quote:{cmd:"formatBlock",param:"BLOCKQUOTE",icon:t.quote,tip:e.quote,key:81},left:{cmd:"justifyLeft",icon:t.left,tip:e.left},center:{cmd:"justifyCenter",icon:t.center,tip:e.center},right:{cmd:"justifyRight",icon:t.right,tip:e.right},justify:{cmd:"justifyFull",icon:t.justify,tip:e.justify},print:{type:"no-state",cmd:"print",icon:t.print,tip:e.print,key:80},outdent:{type:"no-state",disable:e=>e.caret&&!e.caret.can("outdent"),cmd:"outdent",icon:t.outdent,tip:e.outdent},indent:{type:"no-state",disable:e=>e.caret&&!e.caret.can("indent"),cmd:"indent",icon:t.indent,tip:e.indent},removeFormat:{type:"no-state",cmd:"removeFormat",icon:t.removeFormat,tip:e.removeFormat},hr:{type:"no-state",cmd:"insertHorizontalRule",icon:t.hr,tip:e.hr},undo:{type:"no-state",cmd:"undo",icon:t.undo,tip:e.undo,key:90},redo:{type:"no-state",cmd:"redo",icon:t.redo,tip:e.redo,key:89},h1:{cmd:"formatBlock",param:"H1",icon:t.heading1||t.heading,tip:e.heading1,htmlTip:`<h1 class="q-ma-none">${e.heading1}</h1>`},h2:{cmd:"formatBlock",param:"H2",icon:t.heading2||t.heading,tip:e.heading2,htmlTip:`<h2 class="q-ma-none">${e.heading2}</h2>`},h3:{cmd:"formatBlock",param:"H3",icon:t.heading3||t.heading,tip:e.heading3,htmlTip:`<h3 class="q-ma-none">${e.heading3}</h3>`},h4:{cmd:"formatBlock",param:"H4",icon:t.heading4||t.heading,tip:e.heading4,htmlTip:`<h4 class="q-ma-none">${e.heading4}</h4>`},h5:{cmd:"formatBlock",param:"H5",icon:t.heading5||t.heading,tip:e.heading5,htmlTip:`<h5 class="q-ma-none">${e.heading5}</h5>`},h6:{cmd:"formatBlock",param:"H6",icon:t.heading6||t.heading,tip:e.heading6,htmlTip:`<h6 class="q-ma-none">${e.heading6}</h6>`},p:{cmd:"formatBlock",param:l.paragraphTag,icon:t.heading,tip:e.paragraph},code:{cmd:"formatBlock",param:"PRE",icon:t.code,htmlTip:`<code>${e.code}</code>`},"size-1":{cmd:"fontSize",param:"1",icon:t.size1||t.size,tip:e.size1,htmlTip:`<font size="1">${e.size1}</font>`},"size-2":{cmd:"fontSize",param:"2",icon:t.size2||t.size,tip:e.size2,htmlTip:`<font size="2">${e.size2}</font>`},"size-3":{cmd:"fontSize",param:"3",icon:t.size3||t.size,tip:e.size3,htmlTip:`<font size="3">${e.size3}</font>`},"size-4":{cmd:"fontSize",param:"4",icon:t.size4||t.size,tip:e.size4,htmlTip:`<font size="4">${e.size4}</font>`},"size-5":{cmd:"fontSize",param:"5",icon:t.size5||t.size,tip:e.size5,htmlTip:`<font size="5">${e.size5}</font>`},"size-6":{cmd:"fontSize",param:"6",icon:t.size6||t.size,tip:e.size6,htmlTip:`<font size="6">${e.size6}</font>`},"size-7":{cmd:"fontSize",param:"7",icon:t.size7||t.size,tip:e.size7,htmlTip:`<font size="7">${e.size7}</font>`}}})),M=o((()=>{let e=l.definitions||{},t=l.definitions||l.fonts?ar(!0,{},$.value,e,function(e,t,l,a={}){let o=Object.keys(a);if(0===o.length)return{};let n={default_font:{cmd:"fontName",param:e,icon:l,tip:t}};return o.forEach((e=>{let t=a[e];n[e]={cmd:"fontName",param:t,icon:l,tip:t,htmlTip:`<font face="${t}">${t}</font>`}})),n}(d,p.lang.editor.defaultFont,p.iconSet.editor.font,l.fonts)):$.value;return l.toolbar.map((l=>l.map((l=>{if(l.options)return{type:"dropdown",icon:l.icon,label:l.label,size:"sm",dense:!0,fixedLabel:l.fixedLabel,fixedIcon:l.fixedIcon,highlight:l.highlight,list:l.list,options:l.options.map((e=>t[e]))};let a=t[l];return a?"no-state"===a.type||e[l]&&(void 0===a.cmd||$.value[a.cmd]&&"no-state"===$.value[a.cmd].type)?a:Object.assign({type:"toggle"},a):{type:"slot",slot:l}}))))})),T={$q:p,props:l,slots:s,emit:u,inFullscreen:g,toggleFullscreen:h,runCmd:U,isViewingSource:_,editLinkUrl:x,toolbarBackgroundClass:q,buttonProps:C,contentRef:w,buttons:M,setContent:Q};n((()=>l.modelValue),(e=>{k!==e&&(k=e,Q(e,!0))})),n(x,(e=>{u("link"+(e?"Show":"Hide"))}));let B=o((()=>l.toolbar&&0!==l.toolbar.length)),L=o((()=>{let e={},t=t=>{t.key&&(e[t.key]={cmd:t.cmd,param:t.param})};return M.value.forEach((e=>{e.forEach((e=>{e.options?e.options.forEach(t):t(e)}))})),e})),z=o((()=>g.value?l.contentStyle:[{minHeight:l.minHeight,height:l.height,maxHeight:l.maxHeight},l.contentStyle])),V=o((()=>"q-editor q-editor--"+(!0===_.value?"source":"default")+(!0===l.disable?" disabled":"")+(!0===g.value?" fullscreen column":"")+(!0===l.square?" q-editor--square no-border-radius":"")+(!0===l.flat?" q-editor--flat":"")+(!0===l.dense?" q-editor--dense":"")+(!0===f.value?" q-editor--dark q-dark":""))),O=o((()=>[l.contentClass,"q-editor__content",{col:g.value,"overflow-auto":g.value||l.maxHeight}])),A=o((()=>!0===l.disable?{"aria-disabled":"true"}:{}));function E(){if(null!==w.value){let e="inner"+(!0===_.value?"Text":"HTML"),t=w.value[e];t!==l.modelValue&&(k=t,u("update:modelValue",t))}}function P(e){if(u("keydown",e),!0!==e.ctrlKey||!0===ke(e))return void W();let t=e.keyCode,l=L.value[t];if(void 0!==l){let{cmd:t,param:a}=l;ae(e),U(t,a,!1)}}function F(e){W(),u("click",e)}function R(e){if(null!==w.value){let{scrollTop:e,scrollHeight:t}=w.value;c=t-e}T.caret.save(),u("blur",e)}function N(e){m((()=>{null!==w.value&&void 0!==c&&(w.value.scrollTop=w.value.scrollHeight-c)})),u("focus",e)}function H(e){let t=y.value;if(null!==t&&!0===t.contains(e.target)&&(null===e.relatedTarget||!0!==t.contains(e.relatedTarget))){let e="inner"+(!0===_.value?"Text":"HTML");T.caret.restorePosition(w.value[e].length),W()}}function I(e){let t=y.value;null!==t&&!0===t.contains(e.target)&&(null===e.relatedTarget||!0!==t.contains(e.relatedTarget))&&(T.caret.savePosition(),W())}function j(){c=void 0}function D(e){T.caret.save()}function Q(e,t){if(null!==w.value){!0===t&&T.caret.savePosition();let l="inner"+(!0===_.value?"Text":"HTML");w.value[l]=e,!0===t&&(T.caret.restorePosition(w.value[l].length),W())}}function U(e,t,l=!0){K(),T.caret.restore(),T.caret.apply(e,t,(()=>{K(),T.caret.save(),l&&W()}))}function W(){setTimeout((()=>{x.value=null,v.$forceUpdate()}),1)}function K(){Nl((()=>{null!==w.value&&w.value.focus({preventScroll:!0})}))}return r((()=>{T.caret=v.caret=new ji(w.value,T),Q(l.modelValue),W(),document.addEventListener("selectionchange",D)})),i((()=>{document.removeEventListener("selectionchange",D)})),Object.assign(v,{runCmd:U,refreshToolbar:W,focus:K,getContentEl:function(){return w.value}}),()=>{let e;if(B.value){let l=[t("div",{key:"qedt_top",class:"q-editor__toolbar row no-wrap scroll-x"+q.value},Xi(T))];null!==x.value&&l.push(t("div",{key:"qedt_btm",class:"q-editor__toolbar row no-wrap items-center scroll-x"+q.value},function(e){if(e.caret){let l=e.props.toolbarColor||e.props.toolbarTextColor,a=e.editLinkUrl.value,o=()=>{e.caret.restore(),a!==e.editLinkUrl.value&&document.execCommand("createLink",!1,""===a?" ":a),e.editLinkUrl.value=null};return[t("div",{class:`q-mx-xs text-${l}`},`${e.$q.lang.editor.url}: `),t("input",{key:"qedt_btm_input",class:"col q-editor__link-input",value:a,onInput:e=>{te(e),a=e.target.value},onKeydown:t=>{if(!0!==ke(t))switch(t.keyCode){case 13:return le(t),o();case 27:le(t),e.caret.restore(),(!e.editLinkUrl.value||"https://"===e.editLinkUrl.value)&&document.execCommand("unlink"),e.editLinkUrl.value=null}}}),Ki([t($l,{key:"qedt_btm_rem",tabindex:-1,...e.buttonProps.value,label:e.$q.lang.label.remove,noCaps:!0,onClick:()=>{e.caret.restore(),document.execCommand("unlink"),e.editLinkUrl.value=null}}),t($l,{key:"qedt_btm_upd",...e.buttonProps.value,label:e.$q.lang.label.update,noCaps:!0,onClick:o})])]}}(T))),e=t("div",{key:"toolbar_ctainer",class:"q-editor__toolbars-container"},l)}return t("div",{ref:y,class:V.value,style:{height:!0===g.value?"100%":null},...A.value,onFocusin:H,onFocusout:I},[e,t("div",{ref:w,style:z.value,class:O.value,contenteditable:S.value,placeholder:l.placeholder,...b.listeners.value,onInput:E,onKeydown:P,onClick:F,onBlur:R,onFocus:N,onMousedown:j,onTouchstartPassive:j})])}}}),nr=Ze({name:"QItemLabel",props:{overline:Boolean,caption:Boolean,header:Boolean,lines:[Number,String]},setup(e,{slots:l}){let a=o((()=>parseInt(e.lines,10))),n=o((()=>"q-item__label"+(!0===e.overline?" q-item__label--overline text-overline":"")+(!0===e.caption?" q-item__label--caption text-caption":"")+(!0===e.header?" q-item__label--header":"")+(1===a.value?" ellipsis":""))),i=o((()=>void 0!==e.lines&&a.value>1?{overflow:"hidden",display:"-webkit-box","-webkit-box-orient":"vertical","-webkit-line-clamp":a.value}:null));return()=>t("div",{style:i.value,class:n.value},pt(l.default))}}),ir=Ze({name:"QSlideTransition",props:{appear:Boolean,duration:{type:Number,default:300}},emits:["show","hide"],setup(e,{slots:l,emit:a}){let o,n,r,s,u=!1,d=null,c=null;function v(){o&&o(),o=null,u=!1,null!==d&&(clearTimeout(d),d=null),null!==c&&(clearTimeout(c),c=null),void 0!==n&&n.removeEventListener("transitionend",r),r=null}function p(t,l,a){void 0!==l&&(t.style.height=`${l}px`),t.style.transition=`height ${e.duration}ms cubic-bezier(.25, .8, .50, 1)`,u=!0,o=a}function m(e,t){e.style.overflowY=null,e.style.height=null,e.style.transition=null,v(),t!==s&&a(t)}function g(t,l){let a=0;n=t,!0===u?(v(),a=t.offsetHeight===t.scrollHeight?0:void 0):(s="hide",t.style.overflowY="hidden"),p(t,a,l),d=setTimeout((()=>{d=null,t.style.height=`${t.scrollHeight}px`,r=e=>{c=null,(Object(e)!==e||e.target===t)&&m(t,"show")},t.addEventListener("transitionend",r),c=setTimeout(r,1.1*e.duration)}),100)}function h(t,l){let a;n=t,!0===u?v():(s="show",t.style.overflowY="hidden",a=t.scrollHeight),p(t,a,l),d=setTimeout((()=>{d=null,t.style.height=0,r=e=>{c=null,(Object(e)!==e||e.target===t)&&m(t,"hide")},t.addEventListener("transitionend",r),c=setTimeout(r,1.1*e.duration)}),100)}return i((()=>{!0===u&&v()})),()=>t(f,{css:!1,appear:e.appear,onEnter:g,onLeave:h},l.default)}}),rr={true:"inset",item:"item-inset","item-thumbnail":"item-thumbnail-inset"},sr={xs:2,sm:4,md:8,lg:16,xl:24},ur=Ze({name:"QSeparator",props:{...Et,spaced:[Boolean,String],inset:[Boolean,String],vertical:Boolean,color:String,size:String},setup(e){let l=a(),n=Pt(e,l.proxy.$q),i=o((()=>!0===e.vertical?"vertical":"horizontal")),r=o((()=>` q-separator--${i.value}`)),s=o((()=>!1!==e.inset?`${r.value}-${rr[e.inset]}`:"")),u=o((()=>`q-separator${r.value}${s.value}`+(void 0!==e.color?` bg-${e.color}`:"")+(!0===n.value?" q-separator--dark":""))),d=o((()=>{let t={};if(void 0!==e.size&&(t[!0===e.vertical?"width":"height"]=e.size),!1!==e.spaced){let l=!0===e.spaced?`${sr.md}px`:e.spaced in sr?`${sr[e.spaced]}px`:e.spaced,a=!0===e.vertical?["Left","Right"]:["Top","Bottom"];t[`margin${a[0]}`]=t[`margin${a[1]}`]=l}return t}));return()=>t("hr",{class:u.value,style:d.value,"aria-orientation":i.value})}}),dr=l({}),cr=Object.keys(tl),vr=Ze({name:"QExpansionItem",props:{...tl,...Vl,...Et,icon:String,label:String,labelLines:[Number,String],caption:String,captionLines:[Number,String],dense:Boolean,toggleAriaLabel:String,expandIcon:String,expandedIcon:String,expandIconClass:[Array,String,Object],duration:Number,headerInsetLevel:Number,contentInsetLevel:Number,expandSeparator:Boolean,defaultOpened:Boolean,hideExpandIcon:Boolean,expandIconToggle:Boolean,switchToggleSide:Boolean,denseToggle:Boolean,group:String,popup:Boolean,headerStyle:[Array,String,Object],headerClass:[Array,String,Object]},emits:[...Ol,"click","afterShow","afterHide"],setup(l,{slots:r,emit:s}){let u,d,{proxy:{$q:c}}=a(),v=Pt(l,c),p=e(null!==l.modelValue?l.modelValue:l.defaultOpened),f=e(null),m=Ka(),{show:h,hide:b,toggle:y}=Al({showing:p}),w=o((()=>`q-expansion-item q-item-type q-expansion-item--${!0===p.value?"expanded":"collapsed"} q-expansion-item--${!0===l.popup?"popup":"standard"}`)),x=o((()=>void 0===l.contentInsetLevel?null:{["padding"+(!0===c.lang.rtl?"Right":"Left")]:56*l.contentInsetLevel+"px"})),_=o((()=>!0!==l.disable&&(void 0!==l.href||void 0!==l.to&&null!==l.to&&""!==l.to))),S=o((()=>{let e={};return cr.forEach((t=>{e[t]=l[t]})),e})),k=o((()=>!0===_.value||!0!==l.expandIconToggle)),q=o((()=>void 0!==l.expandedIcon&&!0===p.value?l.expandedIcon:l.expandIcon||c.iconSet.expansionItem[!0===l.denseToggle?"denseIcon":"icon"])),C=o((()=>!0!==l.disable&&(!0===_.value||!0===l.expandIconToggle))),$=o((()=>({expanded:!0===p.value,detailsId:l.targetUid,toggle:y,show:h,hide:b}))),T=o((()=>{let e=void 0!==l.toggleAriaLabel?l.toggleAriaLabel:c.lang.label[!0===p.value?"collapse":"expand"](l.label);return{role:"button","aria-expanded":!0===p.value?"true":"false","aria-controls":m.value,"aria-label":e}}));function B(e){!0!==_.value&&y(e),s("click",e)}function L(e){13===e.keyCode&&z(e,!0)}function z(e,t){!0!==t&&null!==f.value&&f.value.focus(),y(e),ae(e)}function V(){s("afterShow")}function O(){s("afterHide")}function A(){void 0===u&&(u=Ua()),!0===p.value&&(dr[l.group]=u);let e=n(p,(e=>{!0===e?dr[l.group]=u:dr[l.group]===u&&delete dr[l.group]})),t=n((()=>dr[l.group]),((e,t)=>{t===u&&void 0!==e&&e!==u&&b()}));d=()=>{e(),t(),dr[l.group]===u&&delete dr[l.group],d=void 0}}function E(){let e;return void 0!==r.header?e=[].concat(r.header($.value)):(e=[t(Ui,(()=>[t(nr,{lines:l.labelLines},(()=>l.label||"")),l.caption?t(nr,{lines:l.captionLines,caption:!0},(()=>l.caption)):null]))],l.icon&&e[!0===l.switchToggleSide?"push":"unshift"](t(Ui,{side:!0===l.switchToggleSide,avatar:!0!==l.switchToggleSide},(()=>t(zt,{name:l.icon}))))),!0!==l.disable&&!0!==l.hideExpandIcon&&e[!0===l.switchToggleSide?"unshift":"push"](function(){let e={class:["q-focusable relative-position cursor-pointer"+(!0===l.denseToggle&&!0===l.switchToggleSide?" items-end":""),l.expandIconClass],side:!0!==l.switchToggleSide,avatar:l.switchToggleSide},a=[t(zt,{class:"q-expansion-item__toggle-icon"+(void 0===l.expandedIcon&&!0===p.value?" q-expansion-item__toggle-icon--rotated":""),name:q.value})];return!0===C.value&&(Object.assign(e,{tabindex:0,...T.value,onClick:z,onKeyup:L}),a.unshift(t("div",{ref:f,class:"q-expansion-item__toggle-focus q-icon q-focus-helper q-focus-helper--rounded",tabindex:-1}))),t(Ui,e,(()=>a))}()),e}function P(){let e={ref:"item",style:l.headerStyle,class:l.headerClass,dark:v.value,disable:l.disable,dense:l.dense,insetLevel:l.headerInsetLevel};return!0===k.value&&(e.clickable=!0,e.onClick=B,Object.assign(e,!0===_.value?S.value:T.value)),t(Qi,e,E)}function F(){return g(t("div",{key:"e-content",class:"q-expansion-item__content relative-position",style:x.value,id:m.value},pt(r.default)),[[M,p.value]])}function R(){let e=[P(),t(ir,{duration:l.duration,onShow:V,onHide:O},F)];return!0===l.expandSeparator&&e.push(t(ur,{class:"q-expansion-item__border q-expansion-item__border--top absolute-top",dark:v.value}),t(ur,{class:"q-expansion-item__border q-expansion-item__border--bottom absolute-bottom",dark:v.value})),e}return n((()=>l.group),(e=>{void 0!==d&&d(),void 0!==e&&A()})),void 0!==l.group&&A(),i((()=>{void 0!==d&&d()})),()=>t("div",{class:w.value},[t("div",{class:"q-expansion-item__container relative-position"},R())])}}),pr=["top","right","bottom","left"],fr={type:{type:String,default:"a"},outline:Boolean,push:Boolean,flat:Boolean,unelevated:Boolean,color:String,textColor:String,glossy:Boolean,square:Boolean,padding:String,label:{type:[String,Number],default:""},labelPosition:{type:String,default:"right",validator:e=>pr.includes(e)},externalLabel:Boolean,hideLabel:{type:Boolean},labelClass:[Array,String,Object],labelStyle:[Array,String,Object],disable:Boolean,tabindex:[Number,String]};function mr(e,t){return{formClass:o((()=>"q-fab--form-"+(!0===e.square?"square":"rounded"))),stacked:o((()=>!1===e.externalLabel&&["top","bottom"].includes(e.labelPosition))),labelProps:o((()=>{if(!0===e.externalLabel){let l=null===e.hideLabel?!1===t.value:e.hideLabel;return{action:"push",data:{class:[e.labelClass,`q-fab__label q-tooltip--style q-fab__label--external q-fab__label--external-${e.labelPosition}`+(!0===l?" q-fab__label--external-hidden":"")],style:e.labelStyle}}}return{action:["left","top"].includes(e.labelPosition)?"unshift":"push",data:{class:[e.labelClass,`q-fab__label q-fab__label--internal q-fab__label--internal-${e.labelPosition}`+(!0===e.hideLabel?" q-fab__label--internal-hidden":"")],style:e.labelStyle}}}))}}var gr=["up","right","down","left"],hr=["left","center","right"],br=Ze({name:"QFab",props:{...fr,...Vl,icon:String,activeIcon:String,hideIcon:Boolean,hideLabel:{default:null},direction:{type:String,default:"right",validator:e=>gr.includes(e)},persistent:Boolean,verticalActionsAlign:{type:String,default:"center",validator:e=>hr.includes(e)}},emits:Ol,setup(l,{slots:n}){let i=e(null),r=e(!0===l.modelValue),s=Ka(),{proxy:{$q:u}}=a(),{formClass:d,labelProps:c}=mr(l,r),v=o((()=>!0!==l.persistent)),{hide:p,toggle:f}=Al({showing:r,hideOnRouteChange:v}),m=o((()=>({opened:r.value}))),g=o((()=>`q-fab z-fab row inline justify-center q-fab--align-${l.verticalActionsAlign} ${d.value}`+(!0===r.value?" q-fab--opened":" q-fab--closed"))),h=o((()=>`q-fab__actions flex no-wrap inline q-fab__actions--${l.direction} q-fab__actions--${!0===r.value?"opened":"closed"}`)),b=o((()=>{let e={id:s.value,role:"menu"};return!0!==r.value&&(e["aria-hidden"]="true"),e})),y=o((()=>"q-fab__icon-holder  q-fab__icon-holder--"+(!0===r.value?"opened":"closed")));function w(e,a){let o=n[e],i=`q-fab__${e} absolute-full`;return void 0===o?t(zt,{class:i,name:l[a]||u.iconSet.fab[a]}):t("div",{class:i},o(m.value))}function x(){let e=[];return!0!==l.hideIcon&&e.push(t("div",{class:y.value},[w("icon","icon"),w("active-icon","activeIcon")])),(""!==l.label||void 0!==n.label)&&e[c.value.action](t("div",c.value.data,void 0!==n.label?n.label(m.value):[l.label])),mt(n.tooltip,e)}return q(Ae,{showing:r,onChildClick(e){p(e),null!==i.value&&i.value.$el.focus()}}),()=>t("div",{class:g.value},[t($l,{ref:i,class:d.value,...l,noWrap:!0,stack:l.stacked,align:void 0,icon:void 0,label:void 0,noCaps:!0,fab:!0,"aria-expanded":!0===r.value?"true":"false","aria-haspopup":"true","aria-controls":s.value,onClick:f},x),t("div",{class:h.value,...b.value},pt(n.default))])}}),yr={start:"self-end",center:"self-center",end:"self-start"},wr=Object.keys(yr),xr=Ze({name:"QFabAction",props:{...fr,icon:{type:String,default:""},anchor:{type:String,validator:e=>wr.includes(e)},to:[String,Object],replace:Boolean},emits:["click"],setup(e,{slots:l,emit:n}){let i=d(Ae,(()=>({showing:{value:!0},onChildClick:G}))),{formClass:r,labelProps:s}=mr(e,i.showing),u=o((()=>{let t=yr[e.anchor];return r.value+(void 0!==t?` ${t}`:"")})),c=o((()=>!0===e.disable||!0!==i.showing.value));function v(e){i.onChildClick(e),n("click",e)}function p(){let a=[];return void 0!==l.icon?a.push(l.icon()):""!==e.icon&&a.push(t(zt,{name:e.icon})),(""!==e.label||void 0!==l.label)&&a[s.value.action](t("div",s.value.data,void 0!==l.label?l.label():[e.label])),mt(l.default,a)}let f=a();return Object.assign(f.proxy,{click:v}),()=>t($l,{class:u.value,...e,noWrap:!0,stack:e.stacked,icon:void 0,label:void 0,noCaps:!0,fabMini:!0,disable:c.value,onClick:v},p)}});var _r=[!0,!1,"ondemand"],Sr={modelValue:{},error:{type:Boolean,default:null},errorMessage:String,noErrorIcon:Boolean,rules:Array,reactiveRules:Boolean,lazyRules:{type:[Boolean,String],default:!1,validator:e=>_r.includes(e)}};function kr(t,l){let{props:s,proxy:u}=a(),c=e(!1),v=e(null),p=e(!1);!function({validate:e,resetValidation:t,requiresQForm:l}){let o=d(Ee,!1);if(!1!==o){let{props:l,proxy:s}=a();Object.assign(s,{validate:e,resetValidation:t}),n((()=>l.disable),(e=>{!0===e?("function"==typeof t&&t(),o.unbindComponent(s)):o.bindComponent(s)})),r((()=>{!0!==l.disable&&o.bindComponent(s)})),i((()=>{!0!==l.disable&&o.unbindComponent(s)}))}}({validate:_,resetValidation:x});let f,m=0,g=o((()=>void 0!==s.rules&&null!==s.rules&&0!==s.rules.length)),h=o((()=>!0!==s.disable&&!0===g.value&&!1===l.value)),b=o((()=>!0===s.error||!0===c.value)),y=o((()=>"string"==typeof s.errorMessage&&0!==s.errorMessage.length?s.errorMessage:v.value));function w(){"ondemand"!==s.lazyRules&&!0===h.value&&!0===p.value&&S()}function x(){m++,l.value=!1,p.value=!1,c.value=!1,v.value=null,S.cancel()}function _(e=s.modelValue){if(!0===s.disable||!1===g.value)return!0;let t=++m,a=!0!==l.value?()=>{p.value=!0}:()=>{},o=(e,t)=>{!0===e&&a(),c.value=e,v.value=t||null,l.value=!1},n=[];for(let l=0;l<s.rules.length;l++){let t,a=s.rules[l];if("function"==typeof a?t=a(e,xn):"string"==typeof a&&void 0!==xn[a]&&(t=xn[a](e)),!1===t||"string"==typeof t)return o(!0,t),!1;!0!==t&&void 0!==t&&n.push(t)}return 0===n.length?(o(!1),!0):(l.value=!0,Promise.all(n).then((e=>{if(void 0===e||!1===Array.isArray(e)||0===e.length)return t===m&&o(!1),!0;let l=e.find((e=>!1===e||"string"==typeof e));return t===m&&o(void 0!==l,l),void 0===l}),(e=>(t===m&&o(!0),!1))))}n((()=>s.modelValue),(()=>{p.value=!0,!0===h.value&&!1===s.lazyRules&&S()})),n((()=>s.reactiveRules),(e=>{!0===e?void 0===f&&(f=n((()=>s.rules),w,{immediate:!0,deep:!0})):void 0!==f&&(f(),f=void 0)}),{immediate:!0}),n((()=>s.lazyRules),w),n(t,(e=>{!0===e?p.value=!0:!0===h.value&&"ondemand"!==s.lazyRules&&S()}));let S=re(_,0);return i((()=>{void 0!==f&&f(),S.cancel()})),Object.assign(u,{resetValidation:x,validate:_}),H(u,"hasError",(()=>b.value)),{isDirtyModel:p,hasRules:g,hasError:b,errorMessage:y,validate:_,resetValidation:x}}function qr(e){return null!=e&&0!==(""+e).length}var Cr={...Et,...Sr,label:String,stackLabel:Boolean,hint:String,hideHint:Boolean,prefix:String,suffix:String,labelColor:String,color:String,bgColor:String,filled:Boolean,outlined:Boolean,borderless:Boolean,standout:[Boolean,String],square:Boolean,loading:Boolean,labelSlot:Boolean,bottomSlots:Boolean,hideBottomSpace:Boolean,rounded:Boolean,dense:Boolean,itemAligned:Boolean,counter:Boolean,clearable:Boolean,clearIcon:String,disable:Boolean,readonly:Boolean,autofocus:Boolean,for:String,maxlength:[Number,String]},$r=["update:modelValue","clear","focus","blur","popupShow","popupHide"];function Mr({requiredForAttr:t=!0,tagProp:l}={}){let{props:n,proxy:i}=a(),r=Pt(n,i.$q),s=Ka({required:t,getValue:()=>n.for});return{requiredForAttr:t,tag:!0===l?o((()=>n.tag)):{value:"label"},isDark:r,editable:o((()=>!0!==n.disable&&!0!==n.readonly)),innerLoading:e(!1),focused:e(!1),hasPopupOpen:!1,splitAttrs:Gi(),targetUid:s,rootRef:e(null),targetRef:e(null),controlRef:e(null)}}function Tr(e){let{props:l,emit:n,slots:s,attrs:u,proxy:d}=a(),{$q:c}=d,v=null;void 0===e.hasValue&&(e.hasValue=o((()=>qr(l.modelValue)))),void 0===e.emitValue&&(e.emitValue=e=>{n("update:modelValue",e)}),void 0===e.controlEvents&&(e.controlEvents={onFocusin:V,onFocusout:O}),Object.assign(e,{clearValue:A,onControlFocusin:V,onControlFocusout:O,focus:z}),void 0===e.computedCounter&&(e.computedCounter=o((()=>{if(!1!==l.counter){let e="string"==typeof l.modelValue||"number"==typeof l.modelValue?(""+l.modelValue).length:!0===Array.isArray(l.modelValue)?l.modelValue.length:0,t=void 0!==l.maxlength?l.maxlength:l.maxValues;return e+(void 0!==t?" / "+t:"")}})));let{isDirtyModel:p,hasRules:g,hasError:y,errorMessage:w,resetValidation:x}=kr(e.focused,e.innerLoading),_=void 0!==e.floatingLabel?o((()=>!0===l.stackLabel||!0===e.focused.value||!0===e.floatingLabel.value)):o((()=>!0===l.stackLabel||!0===e.focused.value||!0===e.hasValue.value)),S=o((()=>!0===l.bottomSlots||void 0!==l.hint||!0===g.value||!0===l.counter||null!==l.error)),k=o((()=>!0===l.filled?"filled":!0===l.outlined?"outlined":!0===l.borderless?"borderless":l.standout?"standout":"standard")),q=o((()=>`q-field row no-wrap items-start q-field--${k.value}`+(void 0!==e.fieldClass?` ${e.fieldClass.value}`:"")+(!0===l.rounded?" q-field--rounded":"")+(!0===l.square?" q-field--square":"")+(!0===_.value?" q-field--float":"")+(!0===$.value?" q-field--labeled":"")+(!0===l.dense?" q-field--dense":"")+(!0===l.itemAligned?" q-field--item-aligned q-item-type":"")+(!0===e.isDark.value?" q-field--dark":"")+(void 0===e.getControl?" q-field--auto-height":"")+(!0===e.focused.value?" q-field--focused":"")+(!0===y.value?" q-field--error":"")+(!0===y.value||!0===e.focused.value?" q-field--highlighted":"")+(!0!==l.hideBottomSpace&&!0===S.value?" q-field--with-bottom":"")+(!0===l.disable?" q-field--disabled":!0===l.readonly?" q-field--readonly":""))),C=o((()=>"q-field__control relative-position row no-wrap"+(void 0!==l.bgColor?` bg-${l.bgColor}`:"")+(!0===y.value?" text-negative":"string"==typeof l.standout&&0!==l.standout.length&&!0===e.focused.value?` ${l.standout}`:void 0!==l.color?` text-${l.color}`:""))),$=o((()=>!0===l.labelSlot||void 0!==l.label)),M=o((()=>"q-field__label no-pointer-events absolute ellipsis"+(void 0!==l.labelColor&&!0!==y.value?` text-${l.labelColor}`:""))),T=o((()=>({id:e.targetUid.value,editable:e.editable.value,focused:e.focused.value,floatingLabel:_.value,modelValue:l.modelValue,emitValue:e.emitValue}))),B=o((()=>{let t={};return e.targetUid.value&&(t.for=e.targetUid.value),!0===l.disable&&(t["aria-disabled"]="true"),t}));function L(){let t=document.activeElement,l=void 0!==e.targetRef&&e.targetRef.value;l&&(null===t||t.id!==e.targetUid.value)&&(!0===l.hasAttribute("tabindex")||(l=l.querySelector("[tabindex]")),l&&l!==t&&l.focus({preventScroll:!0}))}function z(){Nl(L)}function V(t){null!==v&&(clearTimeout(v),v=null),!0===e.editable.value&&!1===e.focused.value&&(e.focused.value=!0,n("focus",t))}function O(t,l){null!==v&&clearTimeout(v),v=setTimeout((()=>{v=null,(!0!==document.hasFocus()||!0!==e.hasPopupOpen&&void 0!==e.controlRef&&null!==e.controlRef.value&&!1===e.controlRef.value.contains(document.activeElement))&&(!0===e.focused.value&&(e.focused.value=!1,n("blur",t)),void 0!==l&&l())}))}function A(t){ae(t),!0!==c.platform.is.mobile?(void 0!==e.targetRef&&e.targetRef.value||e.rootRef.value).focus():!0===e.rootRef.value.contains(document.activeElement)&&document.activeElement.blur(),"file"===l.type&&(e.inputRef.value.value=null),n("update:modelValue",null),n("clear",l.modelValue),m((()=>{let e=p.value;x(),p.value=e}))}function E(){let a=[];return void 0!==s.prepend&&a.push(t("div",{class:"q-field__prepend q-field__marginal row no-wrap items-center",key:"prepend",onClick:le},s.prepend())),a.push(t("div",{class:"q-field__control-container col relative-position row no-wrap q-anchor--skip"},function(){let a=[];return void 0!==l.prefix&&null!==l.prefix&&a.push(t("div",{class:"q-field__prefix no-pointer-events row items-center"},l.prefix)),void 0!==e.getShadowControl&&!0===e.hasShadow.value&&a.push(e.getShadowControl()),void 0!==e.getControl?a.push(e.getControl()):void 0!==s.rawControl?a.push(s.rawControl()):void 0!==s.control&&a.push(t("div",{ref:e.targetRef,class:"q-field__native row",tabindex:-1,...e.splitAttrs.attributes.value,"data-autofocus":!0===l.autofocus||void 0},s.control(T.value))),!0===$.value&&a.push(t("div",{class:M.value},pt(s.label,l.label))),void 0!==l.suffix&&null!==l.suffix&&a.push(t("div",{class:"q-field__suffix no-pointer-events row items-center"},l.suffix)),a.concat(pt(s.default))}())),!0===y.value&&!1===l.noErrorIcon&&a.push(F("error",[t(zt,{name:c.iconSet.field.error,color:"negative"})])),!0===l.loading||!0===e.innerLoading.value?a.push(F("inner-loading-append",void 0!==s.loading?s.loading():[t(il,{color:l.color})])):!0===l.clearable&&!0===e.hasValue.value&&!0===e.editable.value&&a.push(F("inner-clearable-append",[t(zt,{class:"q-field__focusable-action",tag:"button",name:l.clearIcon||c.iconSet.field.clear,tabindex:0,type:"button","aria-hidden":null,role:null,onClick:A})])),void 0!==s.append&&a.push(t("div",{class:"q-field__append q-field__marginal row no-wrap items-center",key:"append",onClick:le},s.append())),void 0!==e.getInnerAppend&&a.push(F("inner-append",e.getInnerAppend())),void 0!==e.getControlChild&&a.push(e.getControlChild()),a}function P(){let a,o;!0===y.value?null!==w.value?(a=[t("div",{role:"alert"},w.value)],o=`q--slot-error-${w.value}`):(a=pt(s.error),o="q--slot-error"):(!0!==l.hideHint||!0===e.focused.value)&&(void 0!==l.hint?(a=[t("div",l.hint)],o=`q--slot-hint-${l.hint}`):(a=pt(s.hint),o="q--slot-hint"));let n=!0===l.counter||void 0!==s.counter;if(!0===l.hideBottomSpace&&!1===n&&void 0===a)return;let i=t("div",{key:o,class:"q-field__messages col"},a);return t("div",{class:"q-field__bottom row items-start q-field__bottom--"+(!0!==l.hideBottomSpace?"animated":"stale"),onClick:le},[!0===l.hideBottomSpace?i:t(f,{name:"q-transition--field-message"},(()=>i)),!0===n?t("div",{class:"q-field__counter"},void 0!==s.counter?s.counter():e.computedCounter.value):null])}function F(e,l){return null===l?null:t("div",{key:e,class:"q-field__append q-field__marginal row no-wrap items-center q-anchor--skip"},l)}let R=!1;return h((()=>{R=!0})),b((()=>{!0===R&&!0===l.autofocus&&d.focus()})),!0===l.autofocus&&r((()=>{d.focus()})),i((()=>{null!==v&&clearTimeout(v)})),Object.assign(d,{focus:z,blur:function(){!function(e){El=El.filter((t=>t!==e))}(L);let t=document.activeElement;null!==t&&e.rootRef.value.contains(t)&&t.blur()}}),function(){let a=void 0===e.getControl&&void 0===s.control?{...e.splitAttrs.attributes.value,"data-autofocus":!0===l.autofocus||void 0,...B.value}:B.value;return t(e.tag.value,{ref:e.rootRef,class:[q.value,u.class],style:u.style,...a},[void 0!==s.before?t("div",{class:"q-field__before q-field__marginal row no-wrap items-center",onClick:le},s.before()):null,t("div",{class:"q-field__inner relative-position col self-stretch"},[t("div",{ref:e.controlRef,class:C.value,tabindex:-1,...e.controlEvents},E()),!0===S.value?P():null]),void 0!==s.after?t("div",{class:"q-field__after q-field__marginal row no-wrap items-center",onClick:le},s.after()):null])}}var Br=Ze({name:"QField",inheritAttrs:!1,props:{...Cr,tag:{type:String,default:"label"}},emits:$r,setup:()=>Tr(Mr({requiredForAttr:!1,tagProp:!0}))});function Lr(e,t,l,a){let o=[];return e.forEach((e=>{!0===a(e)?o.push(e):t.push({failedPropValidation:l,file:e})})),o}function zr(e){e&&e.dataTransfer&&(e.dataTransfer.dropEffect="copy"),ae(e)}var Vr={multiple:Boolean,accept:String,capture:String,maxFileSize:[Number,String],maxTotalSize:[Number,String],maxFiles:[Number,String],filter:Function},Or=["rejected"];function Ar({editable:l,dnd:n,getFileInput:i,addFilesToQueue:r}){let{props:s,emit:u,proxy:d}=a(),c=e(null),v=o((()=>void 0!==s.accept?s.accept.split(",").map((e=>"*"===(e=e.trim())?"*/":(e.endsWith("/*")&&(e=e.slice(0,e.length-1)),e.toUpperCase()))):null)),p=o((()=>parseInt(s.maxFiles,10))),f=o((()=>parseInt(s.maxTotalSize,10)));function m(e){if(l.value)if(e!==Object(e)&&(e={target:null}),null!==e.target&&!0===e.target.matches('input[type="file"]'))0===e.clientX&&0===e.clientY&&te(e);else{let t=i();t&&t!==e.target&&t.click(e)}}function g(e){l.value&&e&&r(null,e)}function h(e){ae(e),!0==(null!==e.relatedTarget||!0!==W.is.safari?e.relatedTarget!==c.value:!1===document.elementsFromPoint(e.clientX,e.clientY).includes(c.value))&&(n.value=!1)}function b(e){zr(e);let t=e.dataTransfer.files;0!==t.length&&r(null,t),n.value=!1}return Object.assign(d,{pickFiles:m,addFiles:g}),{pickFiles:m,addFiles:g,onDragover:function(e){zr(e),!0!==n.value&&(n.value=!0)},onDragleave:h,processFiles:function(e,t,l,a){let o=Array.from(t||e.target.files),n=[],i=()=>{0!==n.length&&u("rejected",n)};if(void 0!==s.accept&&-1===v.value.indexOf("*/")&&(o=Lr(o,n,"accept",(e=>v.value.some((t=>e.type.toUpperCase().startsWith(t)||e.name.toUpperCase().endsWith(t))))),0===o.length))return i();if(void 0!==s.maxFileSize){let e=parseInt(s.maxFileSize,10);if(o=Lr(o,n,"max-file-size",(t=>t.size<=e)),0===o.length)return i()}if(!0!==s.multiple&&0!==o.length&&(o=[o[0]]),o.forEach((e=>{e.__key=e.webkitRelativePath+e.lastModified+e.name+e.size})),!0===a){let e=l.map((e=>e.__key));o=Lr(o,n,"duplicate",(t=>!1===e.includes(t.__key)))}if(0===o.length)return i();if(void 0!==s.maxTotalSize){let e=!0===a?l.reduce(((e,t)=>e+t.size),0):0;if(o=Lr(o,n,"max-total-size",(t=>(e+=t.size,e<=f.value))),0===o.length)return i()}if("function"==typeof s.filter){let e=s.filter(o);o=Lr(o,n,"filter",(t=>e.includes(t)))}if(void 0!==s.maxFiles){let e=!0===a?l.length:0;if(o=Lr(o,n,"max-files",(()=>(e++,e<=p.value))),0===o.length)return i()}return i(),0!==o.length?o:void 0},getDndNode:function(e){if(!0===n.value)return t("div",{ref:c,class:`q-${e}__dnd absolute-full`,onDragenter:zr,onDragover:zr,onDragleave:h,onDrop:b})},maxFilesNumber:p,maxTotalSizeNumber:f}}function Er(e,t){function l(){let t=e.modelValue;try{let e="DataTransfer"in window?new DataTransfer:"ClipboardEvent"in window?new ClipboardEvent("").clipboardData:void 0;return Object(t)===t&&("length"in t?Array.from(t):[t]).forEach((t=>{e.items.add(t)})),{files:e.files}}catch{return{files:void 0}}}return o(!0===t?()=>{if("file"===e.type)return l()}:l)}var Pr=Ze({name:"QFile",inheritAttrs:!1,props:{...Cr,...Za,...Vr,modelValue:[File,FileList,Array],append:Boolean,useChips:Boolean,displayValue:[String,Number],tabindex:{type:[String,Number],default:0},counterLabel:Function,inputClass:[Array,String,Object],inputStyle:[Array,String,Object]},emits:[...$r,...Or],setup(l,{slots:n,emit:i,attrs:r}){let{proxy:s}=a(),u=Mr(),d=e(null),c=e(!1),v=eo(l),{pickFiles:p,onDragover:f,onDragleave:m,processFiles:g,getDndNode:h}=Ar({editable:u.editable,dnd:c,getFileInput:L,addFilesToQueue:z}),b=Er(l),y=o((()=>Object(l.modelValue)===l.modelValue?"length"in l.modelValue?Array.from(l.modelValue):[l.modelValue]:[])),w=o((()=>qr(y.value))),x=o((()=>y.value.map((e=>e.name)).join(", "))),_=o((()=>et(y.value.reduce(((e,t)=>e+t.size),0)))),S=o((()=>({totalSize:_.value,filesNumber:y.value.length,maxFiles:l.maxFiles}))),k=o((()=>({tabindex:-1,type:"file",title:"",accept:l.accept,capture:l.capture,name:v.value,...r,id:u.targetUid.value,disabled:!0!==u.editable.value}))),q=o((()=>"q-file q-field--auto-height"+(!0===c.value?" q-file--dnd":""))),C=o((()=>!0===l.multiple&&!0===l.append));function $(e){let t=y.value.slice();t.splice(e,1),M(t)}function M(e){i("update:modelValue",!0===l.multiple?e:e[0])}function T(e){13===e.keyCode&&le(e)}function B(e){(13===e.keyCode||32===e.keyCode)&&p(e)}function L(){return d.value}function z(e,t){let a=g(e,t,y.value,C.value),o=L();null!=o&&(o.value=""),void 0!==a&&((!0===l.multiple?l.modelValue&&a.every((e=>y.value.includes(e))):l.modelValue===a[0])||M(!0===C.value?y.value.concat(a):a))}function V(){return[t("input",{class:[l.inputClass,"q-file__filler"],style:l.inputStyle})]}function O(){let e={ref:d,...k.value,...b.value,class:"q-field__input fit absolute-full cursor-pointer",onChange:z};return!0===l.multiple&&(e.multiple=!0),t("input",e)}return Object.assign(u,{fieldClass:q,emitValue:M,hasValue:w,inputRef:d,innerValue:y,floatingLabel:o((()=>!0===w.value||qr(l.displayValue))),computedCounter:o((()=>{if(void 0!==l.counterLabel)return l.counterLabel(S.value);let e=l.maxFiles;return`${y.value.length}${void 0!==e?" / "+e:""} (${_.value})`})),getControlChild:()=>h("file"),getControl:()=>{let e={ref:u.targetRef,class:"q-field__native row items-center cursor-pointer",tabindex:l.tabindex};return!0===u.editable.value&&Object.assign(e,{onDragover:f,onDragleave:m,onKeydown:T,onKeyup:B}),t("div",e,[O()].concat(function(){if(void 0!==n.file)return 0===y.value.length?V():y.value.map(((e,t)=>n.file({index:t,file:e,ref:this})));if(void 0!==n.selected)return 0===y.value.length?V():n.selected({files:y.value,ref:this});if(!0===l.useChips)return 0===y.value.length?V():y.value.map(((e,a)=>t(Po,{key:"file-"+a,removable:u.editable.value,dense:!0,textColor:l.color,tabindex:l.tabindex,onRemove:()=>{$(a)}},(()=>t("span",{class:"ellipsis",textContent:e.name})))));let e=void 0!==l.displayValue?l.displayValue:x.value;return 0!==e.length?[t("div",{class:l.inputClass,style:l.inputStyle,textContent:e})]:V()}()))}}),Object.assign(s,{removeAtIndex:$,removeFile:function(e){let t=y.value.indexOf(e);-1!==t&&$(t)},getNativeElement:()=>d.value}),H(s,"nativeEl",(()=>d.value)),Tr(u)}}),Fr=Ze({name:"QFooter",props:{modelValue:{type:Boolean,default:!0},reveal:Boolean,bordered:Boolean,elevated:Boolean,heightHint:{type:[String,Number],default:50}},emits:["reveal","focusin"],setup(l,{slots:r,emit:s}){let{proxy:{$q:u}}=a(),c=d(Ve,Re);if(c===Re)return Re;let v=e(parseInt(l.heightHint,10)),p=e(!0),f=e(!0===D.value||!0===c.isContainer.value?0:window.innerHeight),m=o((()=>!0===l.reveal||-1!==c.view.value.indexOf("F")||u.platform.is.ios&&!0===c.isContainer.value)),g=o((()=>!0===c.isContainer.value?c.containerHeight.value:f.value)),h=o((()=>{if(!0!==l.modelValue)return 0;if(!0===m.value)return!0===p.value?v.value:0;let e=c.scroll.value.position+g.value+v.value-c.height.value;return e>0?e:0})),b=o((()=>!0!==l.modelValue||!0===m.value&&!0!==p.value)),y=o((()=>!0===l.modelValue&&!0===b.value&&!0===l.reveal)),w=o((()=>"q-footer q-layout__section--marginal "+(!0===m.value?"fixed":"absolute")+"-bottom"+(!0===l.bordered?" q-footer--bordered":"")+(!0===b.value?" q-footer--hidden":"")+(!0!==l.modelValue?" q-layout--prevent-focus"+(!0!==m.value?" hidden":""):""))),x=o((()=>{let e=c.rows.value.bottom,t={};return"l"===e[0]&&!0===c.left.space&&(t[!0===u.lang.rtl?"right":"left"]=`${c.left.size}px`),"r"===e[2]&&!0===c.right.space&&(t[!0===u.lang.rtl?"left":"right"]=`${c.right.size}px`),t}));function _(e,t){c.update("footer",e,t)}function S(e,t){e.value!==t&&(e.value=t)}function k({height:e}){S(v,e),_("size",e)}function q(e){!0===y.value&&S(p,!0),s("focusin",e)}n((()=>l.modelValue),(e=>{_("space",e),S(p,!0),c.animate()})),n(h,(e=>{_("offset",e)})),n((()=>l.reveal),(e=>{!1===e&&S(p,l.modelValue)})),n(p,(e=>{c.animate(),s("reveal",e)})),n([v,c.scroll,c.height],(function(){if(!0!==l.reveal)return;let{direction:e,position:t,inflectionPoint:a}=c.scroll.value;S(p,"up"===e||t-a<100||c.height.value-g.value-t-v.value<300)})),n((()=>u.screen.height),(e=>{!0!==c.isContainer.value&&S(f,e)}));let C={};return c.instances.footer=C,!0===l.modelValue&&_("size",v.value),_("space",l.modelValue),_("offset",h.value),i((()=>{c.instances.footer===C&&(c.instances.footer=void 0,_("size",0),_("offset",0),_("space",!1))})),()=>{let e=mt(r.default,[t(an,{debounce:0,onResize:k})]);return!0===l.elevated&&e.push(t("div",{class:"q-layout__shadow absolute-full overflow-hidden no-pointer-events"})),t("footer",{class:w.value,style:x.value,onFocusin:q},e)}}}),Rr=Ze({name:"QForm",props:{autofocus:Boolean,noErrorFocus:Boolean,noResetFocus:Boolean,greedy:Boolean,onSubmit:Function},emits:["reset","validationSuccess","validationError"],setup(l,{slots:o,emit:n}){let i=a(),s=e(null),u=0,d=[];function c(e){let t="boolean"==typeof e?e:!0!==l.noErrorFocus,a=++u,o=(e,t)=>{n("validation"+(!0===e?"Success":"Error"),t)},i=e=>{let t=e.validate();return"function"==typeof t.then?t.then((t=>({valid:t,comp:e})),(t=>({valid:!1,comp:e,err:t}))):Promise.resolve({valid:t,comp:e})};return(!0===l.greedy?Promise.all(d.map(i)).then((e=>e.filter((e=>!0!==e.valid)))):d.reduce(((e,t)=>e.then((()=>i(t).then((e=>{if(!1===e.valid)return Promise.reject(e)}))))),Promise.resolve()).catch((e=>[e]))).then((e=>{if(void 0===e||0===e.length)return a===u&&o(!0),!0;if(a===u){let{comp:l,err:a}=e[0];if(o(!1,l),!0===t){let t=e.find((({comp:e})=>"function"==typeof e.focus&&!1===Kt(e.$)));void 0!==t&&t.comp.focus()}}return!1}))}function v(){u++,d.forEach((e=>{"function"==typeof e.resetValidation&&e.resetValidation()}))}function p(e){void 0!==e&&ae(e);let t=u+1;c().then((a=>{t===u&&!0===a&&(void 0!==l.onSubmit?n("submit",e):void 0!==e&&void 0!==e.target&&"function"==typeof e.target.submit&&e.target.submit())}))}function f(e){void 0!==e&&ae(e),n("reset"),m((()=>{v(),!0===l.autofocus&&!0!==l.noResetFocus&&g()}))}function g(){Nl((()=>{if(null===s.value)return;let e=s.value.querySelector("[autofocus][tabindex], [data-autofocus][tabindex]")||s.value.querySelector("[autofocus] [tabindex], [data-autofocus] [tabindex]")||s.value.querySelector("[autofocus], [data-autofocus]")||Array.prototype.find.call(s.value.querySelectorAll("[tabindex]"),(e=>-1!==e.tabIndex));null==e||e.focus({preventScroll:!0})}))}q(Ee,{bindComponent(e){d.push(e)},unbindComponent(e){let t=d.indexOf(e);-1!==t&&d.splice(t,1)}});let y=!1;return h((()=>{y=!0})),b((()=>{!0===y&&!0===l.autofocus&&g()})),r((()=>{!0===l.autofocus&&g()})),Object.assign(i.proxy,{validate:c,resetValidation:v,submit:p,reset:f,focus:g,getValidationComponents:()=>d}),()=>t("form",{class:"q-form",ref:s,onSubmit:p,onReset:f},pt(o.default))}}),Nr={inject:{[Ee]:{default:G}},watch:{disable(e){let t=this.$.provides[Ee];void 0!==t&&(!0===e?(this.resetValidation(),t.unbindComponent(this)):t.bindComponent(this))}},methods:{validate(){},resetValidation(){}},mounted(){let e=this.$.provides[Ee];void 0!==e&&!0!==this.disable&&e.bindComponent(this)},beforeUnmount(){let e=this.$.provides[Ee];void 0!==e&&!0!==this.disable&&e.unbindComponent(this)}},Hr=Ze({name:"QHeader",props:{modelValue:{type:Boolean,default:!0},reveal:Boolean,revealOffset:{type:Number,default:250},bordered:Boolean,elevated:Boolean,heightHint:{type:[String,Number],default:50}},emits:["reveal","focusin"],setup(l,{slots:r,emit:s}){let{proxy:{$q:u}}=a(),c=d(Ve,Re);if(c===Re)return Re;let v=e(parseInt(l.heightHint,10)),p=e(!0),f=o((()=>!0===l.reveal||-1!==c.view.value.indexOf("H")||u.platform.is.ios&&!0===c.isContainer.value)),m=o((()=>{if(!0!==l.modelValue)return 0;if(!0===f.value)return!0===p.value?v.value:0;let e=v.value-c.scroll.value.position;return e>0?e:0})),g=o((()=>!0!==l.modelValue||!0===f.value&&!0!==p.value)),h=o((()=>!0===l.modelValue&&!0===g.value&&!0===l.reveal)),b=o((()=>"q-header q-layout__section--marginal "+(!0===f.value?"fixed":"absolute")+"-top"+(!0===l.bordered?" q-header--bordered":"")+(!0===g.value?" q-header--hidden":"")+(!0!==l.modelValue?" q-layout--prevent-focus":""))),y=o((()=>{let e=c.rows.value.top,t={};return"l"===e[0]&&!0===c.left.space&&(t[!0===u.lang.rtl?"right":"left"]=`${c.left.size}px`),"r"===e[2]&&!0===c.right.space&&(t[!0===u.lang.rtl?"left":"right"]=`${c.right.size}px`),t}));function w(e,t){c.update("header",e,t)}function x(e,t){e.value!==t&&(e.value=t)}function _({height:e}){x(v,e),w("size",e)}function S(e){!0===h.value&&x(p,!0),s("focusin",e)}n((()=>l.modelValue),(e=>{w("space",e),x(p,!0),c.animate()})),n(m,(e=>{w("offset",e)})),n((()=>l.reveal),(e=>{!1===e&&x(p,l.modelValue)})),n(p,(e=>{c.animate(),s("reveal",e)})),n(c.scroll,(e=>{!0===l.reveal&&x(p,"up"===e.direction||e.position<=l.revealOffset||e.position-e.inflectionPoint<100)}));let k={};return c.instances.header=k,!0===l.modelValue&&w("size",v.value),w("space",l.modelValue),w("offset",m.value),i((()=>{c.instances.header===k&&(c.instances.header=void 0,w("size",0),w("offset",0),w("space",!1))})),()=>{let e=ft(r.default,[]);return!0===l.elevated&&e.push(t("div",{class:"q-layout__shadow absolute-full overflow-hidden no-pointer-events"})),e.push(t(an,{debounce:0,onResize:_})),t("header",{class:b.value,style:y.value,onFocusin:S},e)}}}),Ir={ratio:[String,Number]};function jr(e,t){return o((()=>{let l=Number(e.ratio||(void 0!==t?t.value:void 0));return!0!==isNaN(l)&&l>0?{paddingBottom:100/l+"%"}:null}))}var Dr=Ze({name:"QImg",props:{...Ir,src:String,srcset:String,sizes:String,alt:String,crossorigin:String,decoding:String,referrerpolicy:String,draggable:Boolean,loading:{type:String,default:"lazy"},loadingShowDelay:{type:[Number,String],default:0},fetchpriority:{type:String,default:"auto"},width:String,height:String,initialRatio:{type:[Number,String],default:1.7778},placeholderSrc:String,errorSrc:String,fit:{type:String,default:"cover"},position:{type:String,default:"50% 50%"},imgClass:String,imgStyle:Object,noSpinner:Boolean,noNativeMenu:Boolean,noTransition:Boolean,spinnerColor:String,spinnerSize:String},emits:["load","error"],setup(l,{slots:i,emit:s}){let u=e(l.initialRatio),d=jr(l,u),c=a(),{registerTimeout:v,removeTimeout:p}=Jl(),{registerTimeout:m,removeTimeout:g}=Jl(),h=o((()=>void 0!==l.placeholderSrc?{src:l.placeholderSrc}:null)),b=o((()=>void 0!==l.errorSrc?{src:l.errorSrc,__qerror:!0}:null)),y=[e(null),e(h.value)],w=e(0),x=e(!1),_=e(!1),S=o((()=>`q-img q-img--${!0===l.noNativeMenu?"no-":""}menu`)),k=o((()=>({width:l.width,height:l.height}))),q=o((()=>`q-img__image ${void 0!==l.imgClass?l.imgClass+" ":""}q-img__image--with${!0===l.noTransition?"out":""}-transition q-img__image--`)),C=o((()=>({...l.imgStyle,objectFit:l.fit,objectPosition:l.position})));function $(){g(),x.value=!1}function M({target:e}){!1===Kt(c)&&(p(),u.value=0===e.naturalHeight?.5:e.naturalWidth/e.naturalHeight,T(e,1))}function T(e,t){1e3===t||!0===Kt(c)||(!0===e.complete?function(e){!0!==Kt(c)&&(w.value=1^w.value,y[w.value].value=null,$(),"true"!==e.getAttribute("__qerror")&&(_.value=!1),s("load",e.currentSrc||e.src))}(e):v((()=>{T(e,t+1)}),50))}function B(e){p(),$(),_.value=!0,y[w.value].value=b.value,y[1^w.value].value=h.value,s("error",e)}function L(e){let a=y[e].value,o={key:"img_"+e,class:q.value,style:C.value,alt:l.alt,crossorigin:l.crossorigin,decoding:l.decoding,referrerpolicy:l.referrerpolicy,height:l.height,width:l.width,loading:l.loading,fetchpriority:l.fetchpriority,"aria-hidden":"true",draggable:l.draggable,...a};return w.value===e?Object.assign(o,{class:o.class+"current",onLoad:M,onError:B}):o.class+="loaded",t("div",{class:"q-img__container absolute-full",key:"img"+e},t("img",o))}function z(){return!1===x.value?t("div",{key:"content",class:"q-img__content absolute-full q-anchor--skip"},pt(i[!0===_.value?"error":"default"])):t("div",{key:"loading",class:"q-img__loading absolute-full flex flex-center"},void 0!==i.loading?i.loading():!0===l.noSpinner?void 0:[t(il,{color:l.spinnerColor,size:l.spinnerSize})])}{let e=function(){n((()=>l.src||l.srcset||l.sizes?{src:l.src,srcset:l.srcset,sizes:l.sizes}:null),(e=>{p(),_.value=!1,null===e?($(),y[1^w.value].value=h.value):(g(),0!==l.loadingShowDelay?m((()=>{x.value=!0}),l.loadingShowDelay):x.value=!0),y[w.value].value=e}),{immediate:!0})};!0===D.value?r(e):e()}return()=>{let e=[];return null!==d.value&&e.push(t("div",{key:"filler",style:d.value})),null!==y[0].value&&e.push(L(0)),null!==y[1].value&&e.push(L(1)),e.push(t(f,{name:"q-transition--fade"},z)),t("div",{key:"main",class:S.value,style:k.value,role:"img","aria-label":l.alt},e)}}}),{passive:Qr}=Z,Ur=Ze({name:"QInfiniteScroll",props:{offset:{type:Number,default:500},debounce:{type:[String,Number],default:100},scrollTarget:{default:void 0},initialIndex:Number,disable:Boolean,reverse:Boolean},emits:["load"],setup(l,{slots:s,emit:u}){let d,c,v=e(!1),p=e(!0),f=e(null),g=e(null),y=l.initialIndex||0,w=o((()=>"q-infinite-scroll__loading"+(!0===v.value?"":" invisible")));function x(){if(!0===l.disable||!0===v.value||!1===p.value)return;let e=aa(d),t=oa(d),a=sl(d);!1===l.reverse?Math.round(t+a+l.offset)>=Math.round(e)&&_():Math.round(t)<=l.offset&&_()}function _(){if(!0===l.disable||!0===v.value||!1===p.value)return;y++,v.value=!0;let e=aa(d);u("load",y,(t=>{!0===p.value&&(v.value=!1,m((()=>{if(!0===l.reverse){let t=aa(d),l=oa(d);da(d,l+(t-e))}!0===t?k():f.value&&f.value.closest("body")&&c()})))}))}function S(){!1===p.value&&(p.value=!0,d.addEventListener("scroll",c,Qr)),x()}function k(){!0===p.value&&(p.value=!1,v.value=!1,d.removeEventListener("scroll",c,Qr),void 0!==c&&void 0!==c.cancel&&c.cancel())}function q(){if(d&&!0===p.value&&d.removeEventListener("scroll",c,Qr),d=la(f.value,l.scrollTarget),!0===p.value){if(d.addEventListener("scroll",c,Qr),!0===l.reverse){let e=aa(d),t=sl(d);da(d,e-t)}x()}}function C(e){e=parseInt(e,10);let t=c;c=e<=0?x:re(x,!0===isNaN(e)?100:e),d&&!0===p.value&&(void 0!==t&&d.removeEventListener("scroll",t,Qr),d.addEventListener("scroll",c,Qr))}function $(e){if(!0===M.value){if(null===g.value)return void(!0!==e&&m((()=>{$(!0)})));let t=(!0===v.value?"un":"")+"pauseAnimations";Array.from(g.value.getElementsByTagName("svg")).forEach((e=>{e[t]()}))}}let M=o((()=>!0!==l.disable&&!0===p.value));n([v,M],(()=>{$()})),n((()=>l.disable),(e=>{!0===e?k():S()})),n((()=>l.reverse),(()=>{!1===v.value&&!0===p.value&&x()})),n((()=>l.scrollTarget),q),n((()=>l.debounce),C);let T=!1;b((()=>{!1!==T&&d&&da(d,T)})),h((()=>{T=!!d&&oa(d)})),i((()=>{!0===p.value&&d.removeEventListener("scroll",c,Qr)})),r((()=>{C(l.debounce),q(),!1===v.value&&$()}));let B=a();return Object.assign(B.proxy,{poll:()=>{void 0!==c&&c()},trigger:_,stop:k,reset:function(){y=0},resume:S,setIndex:function(e){y=e},updateScrollTarget:q}),()=>{let e=ft(s.default,[]);return!0===M.value&&e[!1===l.reverse?"push":"unshift"](t("div",{ref:g,class:w.value},pt(s.loading))),t("div",{class:"q-infinite-scroll",ref:f},e)}}}),Wr=Ze({name:"QInnerLoading",props:{...Et,...Xl,showing:Boolean,color:String,size:{type:[String,Number],default:42},label:String,labelClass:String,labelStyle:[String,Array,Object]},setup(e,{slots:l}){let n=a(),i=Pt(e,n.proxy.$q),{transitionProps:r,transitionStyle:s}=Zl(e),u=o((()=>"q-inner-loading q--avoid-card-border absolute-full column flex-center"+(!0===i.value?" q-inner-loading--dark":""))),d=o((()=>"q-inner-loading__label"+(void 0!==e.labelClass?` ${e.labelClass}`:"")));function c(){return!0===e.showing?t("div",{class:u.value,style:s.value},void 0!==l.default?l.default():function(){let l=[t(il,{size:e.size,color:e.color})];return void 0!==e.label&&l.push(t("div",{class:d.value,style:e.labelStyle},[e.label])),l}()):null}return()=>t(f,r.value,c)}}),Kr={date:"####/##/##",datetime:"####/##/## ##:##",time:"##:##",fulltime:"##:##:##",phone:"(###) ### - ####",card:"#### #### #### ####"},Yr={"#":{pattern:"[\\d]",negate:"[^\\d]"},S:{pattern:"[a-zA-Z]",negate:"[^a-zA-Z]"},N:{pattern:"[0-9a-zA-Z]",negate:"[^0-9a-zA-Z]"},A:{pattern:"[a-zA-Z]",negate:"[^a-zA-Z]",transform:e=>e.toLocaleUpperCase()},a:{pattern:"[a-zA-Z]",negate:"[^a-zA-Z]",transform:e=>e.toLocaleLowerCase()},X:{pattern:"[0-9a-zA-Z]",negate:"[^0-9a-zA-Z]",transform:e=>e.toLocaleUpperCase()},x:{pattern:"[0-9a-zA-Z]",negate:"[^0-9a-zA-Z]",transform:e=>e.toLocaleLowerCase()}},Xr=Object.keys(Yr);Xr.forEach((e=>{Yr[e].regex=new RegExp(Yr[e].pattern)}));var Zr=new RegExp("\\\\([^.*+?^${}()|([\\]])|([.*+?^${}()|[\\]])|(["+Xr.join("")+"])|(.)","g"),Gr=/[.*+?^${}()|[\]\\]/g,Jr="",es={mask:String,reverseFillMask:Boolean,fillMask:[Boolean,String],unmaskedValue:Boolean};function ts(t,l,a,o){let i,r,s,u,d,c,v=e(null),p=e(function(){if(g(),!0===v.value){let e=y(w(t.modelValue));return!1!==t.fillMask?x(e):e}return t.modelValue}());function f(e){if(e<i.length)return i.slice(-e);let t="",l=i,a=l.indexOf(Jr);if(-1!==a){for(let a=e-l.length;a>0;a--)t+=Jr;l=l.slice(0,a)+t+l.slice(a)}return l}function g(){if(v.value=void 0!==t.mask&&0!==t.mask.length&&(!0===t.autogrow||["textarea","text","search","url","tel","password"].includes(t.type)),!1===v.value)return u=void 0,i="",void(r="");let e=void 0===Kr[t.mask]?t.mask:Kr[t.mask],l="string"==typeof t.fillMask&&0!==t.fillMask.length?t.fillMask.slice(0,1):"_",a=l.replace(Gr,"\\$&"),o=[],n=[],d=[],c=!0===t.reverseFillMask,p="",f="";e.replace(Zr,((e,t,l,a,i)=>{if(void 0!==a){let e=Yr[a];d.push(e),f=e.negate,!0===c&&(n.push("(?:"+f+"+)?("+e.pattern+"+)?(?:"+f+"+)?("+e.pattern+"+)?"),c=!1),n.push("(?:"+f+"+)?("+e.pattern+")?")}else if(void 0!==l)p="\\"+("\\"===l?"":l),d.push(l),o.push("([^"+p+"]+)?"+p+"?");else{let e=void 0!==t?t:i;p="\\"===e?"\\\\\\\\":e.replace(Gr,"\\\\$&"),d.push(e),o.push("([^"+p+"]+)?"+p+"?")}}));let m=new RegExp("^"+o.join("")+"("+(""===p?".":"[^"+p+"]")+"+)?"+(""===p?"":"["+p+"]*")+"$"),g=n.length-1,h=n.map(((e,l)=>0===l&&!0===t.reverseFillMask?new RegExp("^"+a+"*"+e):l===g?new RegExp("^"+e+"("+(""===f?".":f)+"+)?"+(!0===t.reverseFillMask?"$":a+"*")):new RegExp("^"+e)));s=d,u=e=>{let l=m.exec(!0===t.reverseFillMask?e:e.slice(0,d.length+1));null!==l&&(e=l.slice(1).join(""));let a=[],o=h.length;for(let t=0,n=e;t<o;t++){let e=h[t].exec(n);if(null===e)break;n=n.slice(e.shift().length),a.push(...e)}return 0!==a.length?a.join(""):e},i=d.map((e=>"string"==typeof e?e:Jr)).join(""),r=i.split(Jr).join(l)}function h(e,l,n){let s=o.value,u=s.selectionEnd,c=s.value.length-u,v=w(e);!0===l&&g();let f=y(v),h=!1!==t.fillMask?x(f):f,_=p.value!==h;s.value!==h&&(s.value=h),!0===_&&(p.value=h),document.activeElement===s&&m((()=>{if(h!==r)if("insertFromPaste"!==n||!0===t.reverseFillMask)if(-1===["deleteContentBackward","deleteContentForward"].indexOf(n))if(!0===t.reverseFillMask)if(!0===_){let e=Math.max(0,h.length-(h===r?0:Math.min(f.length,c+1)));1===e&&1===u?s.setSelectionRange(e,e,"forward"):b.rightReverse(s,e)}else{let e=h.length-c;s.setSelectionRange(e,e,"backward")}else if(!0===_){let e=Math.max(0,i.indexOf(Jr),Math.min(f.length,u)-1);b.right(s,e)}else{let e=u-1;b.right(s,e)}else{let e=!0===t.reverseFillMask?0===u?h.length>f.length?1:0:Math.max(0,h.length-(h===r?0:Math.min(f.length,c)+1))+1:u;s.setSelectionRange(e,e,"forward")}else{let e=s.selectionEnd,t=u-1;for(let l=d;l<=t&&l<e;l++)i[l]!==Jr&&t++;b.right(s,t)}else{let e=!0===t.reverseFillMask?r.length:0;s.setSelectionRange(e,e,"forward")}}));let S=!0===t.unmaskedValue?w(h):h;String(t.modelValue)!==S&&(null!==t.modelValue||""!==S)&&a(S,!0)}n((()=>t.type+t.autogrow),g),n((()=>t.mask),(e=>{if(void 0!==e)h(p.value,!0);else{let e=w(p.value);g(),t.modelValue!==e&&l("update:modelValue",e)}})),n((()=>t.fillMask+t.reverseFillMask),(()=>{!0===v.value&&h(p.value,!0)})),n((()=>t.unmaskedValue),(()=>{!0===v.value&&h(p.value)}));let b={left(e,t){let l=-1===i.slice(t-1).indexOf(Jr),a=Math.max(0,t-1);for(;a>=0;a--)if(i[a]===Jr){t=a,!0===l&&t++;break}if(a<0&&void 0!==i[t]&&i[t]!==Jr)return b.right(e,0);t>=0&&e.setSelectionRange(t,t,"backward")},right(e,t){let l=e.value.length,a=Math.min(l,t+1);for(;a<=l;a++){if(i[a]===Jr){t=a;break}i[a-1]===Jr&&(t=a)}if(a>l&&void 0!==i[t-1]&&i[t-1]!==Jr)return b.left(e,l);e.setSelectionRange(t,t,"forward")},leftReverse(e,t){let l=f(e.value.length),a=Math.max(0,t-1);for(;a>=0;a--){if(l[a-1]===Jr){t=a;break}if(l[a]===Jr&&(t=a,0===a))break}if(a<0&&void 0!==l[t]&&l[t]!==Jr)return b.rightReverse(e,0);t>=0&&e.setSelectionRange(t,t,"backward")},rightReverse(e,t){let l=e.value.length,a=f(l),o=-1===a.slice(0,t+1).indexOf(Jr),n=Math.min(l,t+1);for(;n<=l;n++)if(a[n-1]===Jr){(t=n)>0&&!0===o&&t--;break}if(n>l&&void 0!==a[t-1]&&a[t-1]!==Jr)return b.leftReverse(e,l);e.setSelectionRange(t,t,"forward")}};function y(e){if(null==e||""===e)return"";if(!0===t.reverseFillMask)return function(e){let t=s,l=i.indexOf(Jr),a=e.length-1,o="";for(let n=t.length-1;n>=0&&-1!==a;n--){let i=t[n],r=e[a];if("string"==typeof i)o=i+o,r===i&&a--;else{if(void 0===r||!i.regex.test(r))return o;do{o=(void 0!==i.transform?i.transform(r):r)+o,a--,r=e[a]}while(l===n&&void 0!==r&&i.regex.test(r))}}return o}(e);let l=s,a=0,o="";for(let t=0;t<l.length;t++){let n=e[a],i=l[t];if("string"==typeof i)o+=i,n===i&&a++;else{if(void 0===n||!i.regex.test(n))return o;o+=void 0!==i.transform?i.transform(n):n,a++}}return o}function w(e){return"string"!=typeof e||void 0===u?"number"==typeof e?u(""+e):e:u(e)}function x(e){return r.length-e.length<=0?e:!0===t.reverseFillMask&&0!==e.length?r.slice(0,-e.length)+e:e+r.slice(e.length)}return{innerValue:p,hasMask:v,moveCursorForPaste:function(e,t,l){let a=y(w(e.value));t=Math.max(0,i.indexOf(Jr),Math.min(a.length,t)),d=t,e.setSelectionRange(t,l,"forward")},updateMaskValue:h,onMaskedKeydown:function(e){if(l("keydown",e),!0===ke(e)||!0===e.altKey)return;let a=o.value,n=a.selectionStart,i=a.selectionEnd;if(e.shiftKey||(c=void 0),37===e.keyCode||39===e.keyCode){e.shiftKey&&void 0===c&&(c="forward"===a.selectionDirection?n:i);let l=b[(39===e.keyCode?"right":"left")+(!0===t.reverseFillMask?"Reverse":"")];if(e.preventDefault(),l(a,c===n?i:n),e.shiftKey){let e=a.selectionStart;a.setSelectionRange(Math.min(c,e),Math.max(c,e),"forward")}}else 8===e.keyCode&&!0!==t.reverseFillMask&&n===i?(b.left(a,n),a.setSelectionRange(a.selectionStart,i,"backward")):46===e.keyCode&&!0===t.reverseFillMask&&n===i&&(b.rightReverse(a,i),a.setSelectionRange(n,a.selectionEnd,"forward"))},onMaskedClick:function(e){l("click",e),c=void 0}}}var ls=/[\u3000-\u303f\u3040-\u309f\u30a0-\u30ff\uff00-\uff9f\u4e00-\u9faf\u3400-\u4dbf]/,as=/[\u4e00-\u9fff\u3400-\u4dbf\u{20000}-\u{2a6df}\u{2a700}-\u{2b73f}\u{2b740}-\u{2b81f}\u{2b820}-\u{2ceaf}\uf900-\ufaff\u3300-\u33ff\ufe30-\ufe4f\uf900-\ufaff\u{2f800}-\u{2fa1f}]/u,os=/[\u3131-\u314e\u314f-\u3163\uac00-\ud7a3]/,ns=/[a-z0-9_ -]$/i;function is(e){return function(t){if("compositionend"===t.type||"change"===t.type){if(!0!==t.target.qComposing)return;t.target.qComposing=!1,e(t)}else"compositionupdate"===t.type&&!0!==t.target.qComposing&&"string"==typeof t.data&&!0==(!0===W.is.firefox?!1===ns.test(t.data):!0===ls.test(t.data)||!0===as.test(t.data)||!0===os.test(t.data))&&(t.target.qComposing=!0)}}var rs=Ze({name:"QInput",inheritAttrs:!1,props:{...Cr,...es,...Za,modelValue:{required:!1},shadowText:String,type:{type:String,default:"text"},debounce:[String,Number],autogrow:Boolean,inputClass:[Array,String,Object],inputStyle:[Array,String,Object]},emits:[...$r,"paste","change","keydown","click","animationend"],setup(l,{emit:s,attrs:u}){let d,c,v,{proxy:p}=a(),{$q:f}=p,g={},h=NaN,b=null,y=e(null),w=eo(l),{innerValue:x,hasMask:_,moveCursorForPaste:S,updateMaskValue:k,onMaskedKeydown:q,onMaskedClick:C}=ts(l,s,F,y),$=Er(l,!0),M=o((()=>qr(x.value))),T=is(E),B=Mr(),L=o((()=>"textarea"===l.type||!0===l.autogrow)),z=o((()=>!0===L.value||["text","search","url","tel","password"].includes(l.type))),V=o((()=>{let e={...B.splitAttrs.listeners.value,onInput:E,onPaste:A,onChange:N,onBlur:I,onFocus:te};return e.onCompositionstart=e.onCompositionupdate=e.onCompositionend=T,!0===_.value&&(e.onKeydown=q,e.onClick=C),!0===l.autogrow&&(e.onAnimationend=P),e})),O=o((()=>{let e={tabindex:0,"data-autofocus":!0===l.autofocus||void 0,rows:"textarea"===l.type?6:void 0,"aria-label":l.label,name:w.value,...B.splitAttrs.attributes.value,id:B.targetUid.value,maxlength:l.maxlength,disabled:!0===l.disable,readonly:!0===l.readonly};return!1===L.value&&(e.type=l.type),!0===l.autogrow&&(e.rows=1),e}));function A(e){if(!0===_.value&&!0!==l.reverseFillMask){let t=e.target;S(t,t.selectionStart,t.selectionEnd)}s("paste",e)}function E(e){if(!e||!e.target)return;if("file"===l.type)return void s("update:modelValue",e.target.files);let t=e.target.value;if(!0!==e.target.qComposing){if(!0===_.value)k(t,!1,e.inputType);else if(F(t),!0===z.value&&e.target===document.activeElement){let{selectionStart:l,selectionEnd:a}=e.target;void 0!==l&&void 0!==a&&m((()=>{e.target===document.activeElement&&0===t.indexOf(e.target.value)&&e.target.setSelectionRange(l,a)}))}!0===l.autogrow&&R()}else g.value=t}function P(e){s("animationend",e),R()}function F(e,t){v=()=>{b=null,"number"!==l.type&&!0===g.hasOwnProperty("value")&&delete g.value,l.modelValue!==e&&h!==e&&(h=e,!0===t&&(c=!0),s("update:modelValue",e),m((()=>{h===e&&(h=NaN)}))),v=void 0},"number"===l.type&&(d=!0,g.value=e),void 0!==l.debounce?(null!==b&&clearTimeout(b),g.value=e,b=setTimeout(v,l.debounce)):v()}function R(){requestAnimationFrame((()=>{let e=y.value;if(null!==e){let t=e.parentNode.style,{scrollTop:l}=e,{overflowY:a,maxHeight:o}=!0===f.platform.is.firefox?{}:window.getComputedStyle(e),n=void 0!==a&&"scroll"!==a;!0===n&&(e.style.overflowY="hidden"),t.marginBottom=e.scrollHeight-1+"px",e.style.height="1px",e.style.height=e.scrollHeight+"px",!0===n&&(e.style.overflowY=parseInt(o,10)<e.scrollHeight?"auto":"hidden"),t.marginBottom="",e.scrollTop=l}}))}function N(e){T(e),null!==b&&(clearTimeout(b),b=null),void 0!==v&&v(),s("change",e.target.value)}function I(e){void 0!==e&&te(e),null!==b&&(clearTimeout(b),b=null),void 0!==v&&v(),d=!1,c=!1,delete g.value,"file"!==l.type&&setTimeout((()=>{null!==y.value&&(y.value.value=void 0!==x.value?x.value:"")}))}function j(){return!0===g.hasOwnProperty("value")?g.value:void 0!==x.value?x.value:""}n((()=>l.type),(()=>{y.value&&(y.value.value=l.modelValue)})),n((()=>l.modelValue),(e=>{if(!0===_.value){if(!0===c&&(c=!1,String(e)===h))return;k(e)}else x.value!==e&&(x.value=e,"number"===l.type&&!0===g.hasOwnProperty("value")&&(!0===d?d=!1:delete g.value));!0===l.autogrow&&m(R)})),n((()=>l.autogrow),(e=>{!0===e?m(R):null!==y.value&&u.rows>0&&(y.value.style.height="auto")})),n((()=>l.dense),(()=>{!0===l.autogrow&&m(R)})),i((()=>{I()})),r((()=>{!0===l.autogrow&&R()})),Object.assign(B,{innerValue:x,fieldClass:o((()=>"q-"+(!0===L.value?"textarea":"input")+(!0===l.autogrow?" q-textarea--autogrow":""))),hasShadow:o((()=>"file"!==l.type&&"string"==typeof l.shadowText&&0!==l.shadowText.length)),inputRef:y,emitValue:F,hasValue:M,floatingLabel:o((()=>!0===M.value&&("number"!==l.type||!1===isNaN(x.value))||qr(l.displayValue))),getControl:()=>t(!0===L.value?"textarea":"input",{ref:y,class:["q-field__native q-placeholder",l.inputClass],style:l.inputStyle,...O.value,...V.value,..."file"!==l.type?{value:j()}:$.value}),getShadowControl:()=>t("div",{class:"q-field__native q-field__shadow absolute-bottom no-pointer-events"+(!0===L.value?"":" text-no-wrap")},[t("span",{class:"invisible"},j()),t("span",l.shadowText)])});let D=Tr(B);return Object.assign(p,{focus:function(){Nl((()=>{let e=document.activeElement;null!==y.value&&y.value!==e&&(null===e||e.id!==B.targetUid.value)&&y.value.focus({preventScroll:!0})}))},select:function(){null!==y.value&&y.value.select()},getNativeElement:()=>y.value}),H(p,"nativeEl",(()=>y.value)),D}}),ss={threshold:0,root:null,rootMargin:"0px"};function us(e,t,l){let a,o,n;"function"==typeof l?(a=l,o=ss,n=void 0===t.cfg):(a=l.handler,o=Object.assign({},ss,l.cfg),n=void 0===t.cfg||!1===Ie(t.cfg,o)),t.handler!==a&&(t.handler=a),!0===n&&(t.cfg=o,void 0!==t.observer&&t.observer.unobserve(e),t.observer=new IntersectionObserver((([l])=>{if("function"==typeof t.handler){if(null===l.rootBounds&&!0===document.body.contains(e))return t.observer.unobserve(e),void t.observer.observe(e);(!1===t.handler(l,t.observer)||!0===t.once&&!0===l.isIntersecting)&&ds(e)}}),o),t.observer.observe(e))}function ds(e){let t=e.__qvisible;void 0!==t&&(void 0!==t.observer&&t.observer.unobserve(e),delete e.__qvisible)}var cs=Ge({name:"intersection",mounted(e,{modifiers:t,value:l}){let a={once:!0===t.once};us(e,a,l),e.__qvisible=a},updated(e,t){let l=e.__qvisible;void 0!==l&&us(e,l,t.value)},beforeUnmount:ds}),vs=Ze({name:"QIntersection",props:{tag:{type:String,default:"div"},once:Boolean,transition:String,transitionDuration:{type:[String,Number],default:300},ssrPrerender:Boolean,margin:String,threshold:[Number,Array],root:{default:null},disable:Boolean,onVisibility:Function},setup(l,{slots:a,emit:n}){let i=e(!0===D.value&&l.ssrPrerender),r=o((()=>void 0!==l.root||void 0!==l.margin||void 0!==l.threshold?{handler:c,cfg:{root:l.root,rootMargin:l.margin,threshold:l.threshold}}:c)),s=o((()=>!0!==l.disable&&(!0!==D.value||!0!==l.once||!0!==l.ssrPrerender))),u=o((()=>[[cs,r.value,void 0,{once:l.once}]])),d=o((()=>`--q-transition-duration: ${l.transitionDuration}ms`));function c(e){i.value!==e.isIntersecting&&(i.value=e.isIntersecting,void 0!==l.onVisibility&&n("visibility",i.value))}function v(){return!0===i.value?[t("div",{key:"content",style:d.value},pt(a.default))]:void 0!==a.hidden?[t("div",{key:"hidden",style:d.value},a.hidden())]:void 0}return()=>{let e=l.transition?[t(f,{name:"q-transition--"+l.transition},v)]:v();return ht(l.tag,{class:"q-intersection"},e,"main",s.value,(()=>u.value))}}}),ps=Ze({name:"QList",props:{...Et,bordered:Boolean,dense:Boolean,separator:Boolean,padding:Boolean,tag:{type:String,default:"div"}},setup(e,{slots:l}){let n=a(),i=Pt(e,n.proxy.$q),r=o((()=>"q-list"+(!0===e.bordered?" q-list--bordered":"")+(!0===e.dense?" q-list--dense":"")+(!0===e.separator?" q-list--separator":"")+(!0===i.value?" q-list--dark":"")+(!0===e.padding?" q-list--padding":"")));return()=>t(e.tag,{class:r.value},pt(l.default))}}),fs=[34,37,40,33,39,38],ms=Object.keys(Fo),gs=Ze({name:"QKnob",props:{...Za,...Fo,modelValue:{type:Number,required:!0},innerMin:Number,innerMax:Number,step:{type:Number,default:1,validator:e=>e>=0},tabindex:{type:[Number,String],default:0},disable:Boolean,readonly:Boolean},emits:["update:modelValue","change","dragValue"],setup(l,{slots:i,emit:r}){let s,{proxy:u}=a(),{$q:d}=u,c=e(l.modelValue),v=e(!1),p=o((()=>!0===isNaN(l.innerMin)||l.innerMin<l.min?l.min:l.innerMin)),f=o((()=>!0===isNaN(l.innerMax)||l.innerMax>l.max?l.max:l.innerMax));function m(){c.value=null===l.modelValue?p.value:tt(l.modelValue,p.value,f.value),z(!0)}n((()=>`${l.modelValue}|${p.value}|${f.value}`),m),m();let g=o((()=>!1===l.disable&&!1===l.readonly)),h=o((()=>"q-knob non-selectable"+(!0===g.value?" q-knob--editable":!0===l.disable?" disabled":""))),b=o((()=>(String(l.step).trim().split(".")[1]||"").length)),y=o((()=>0===l.step?1:l.step)),w=o((()=>!0===l.instantFeedback||!0===v.value)),x=!0===d.platform.is.mobile?o((()=>!0===g.value?{onClick:M}:{})):o((()=>!0===g.value?{onMousedown:$,onClick:M,onKeydown:T,onKeyup:L}:{})),_=o((()=>!0===g.value?{tabindex:l.tabindex}:{["aria-"+(!0===l.disable?"disabled":"readonly")]:"true"})),S=o((()=>{let e={};return ms.forEach((t=>{e[t]=l[t]})),e}));function k(e){e.isFinal?(B(e.evt,!0),v.value=!1):(e.isFirst&&(C(),v.value=!0),B(e.evt))}let q=o((()=>[[Do,k,void 0,{prevent:!0,stop:!0,mouse:!0}]]));function C(){let{top:e,left:t,width:l,height:a}=u.$el.getBoundingClientRect();s={top:e+a/2,left:t+l/2}}function $(e){C(),B(e)}function M(e){C(),B(e,!0)}function T(e){if(!fs.includes(e.keyCode))return;ae(e);let t=([34,33].includes(e.keyCode)?10:1)*y.value,l=[34,37,40].includes(e.keyCode)?-t:t;c.value=tt(parseFloat((c.value+l).toFixed(b.value)),p.value,f.value),z()}function B(e,t){let a=ee(e),o=Math.abs(a.top-s.top),n=Math.sqrt(o**2+Math.abs(a.left-s.left)**2),i=Math.asin(o/n)*(180/Math.PI);i=a.top<s.top?s.left<a.left?90-i:270+i:s.left<a.left?i+90:270-i,!0===d.lang.rtl?i=lt(-i-l.angle,0,360):l.angle&&(i=lt(i-l.angle,0,360)),!0===l.reverse&&(i=360-i);let u=l.min+i/360*(l.max-l.min);if(0!==y.value){let e=u%y.value;u=u-e+(Math.abs(e)>=y.value/2?(e<0?-1:1)*y.value:0),u=parseFloat(u.toFixed(b.value))}u=tt(u,p.value,f.value),r("dragValue",u),c.value!==u&&(c.value=u),z(t)}function L(e){fs.includes(e.keyCode)&&z(!0)}function z(e){l.modelValue!==c.value&&r("update:modelValue",c.value),!0===e&&r("change",c.value)}let V=Ga(l);function O(){return t("input",V.value)}return()=>{let e={class:h.value,role:"slider","aria-valuemin":p.value,"aria-valuemax":f.value,"aria-valuenow":l.modelValue,..._.value,...S.value,value:c.value,instantFeedback:w.value,...x.value},t={default:i.default};return!0===g.value&&void 0!==l.name&&(t.internal=O),ht(Ho,e,t,"knob",g.value,(()=>q.value))}}}),{passive:hs}=Z,bs=["both","horizontal","vertical"],ys=Ze({name:"QScrollObserver",props:{axis:{type:String,validator:e=>bs.includes(e),default:"vertical"},debounce:[String,Number],scrollTarget:{default:void 0}},emits:["scroll"],setup(e,{emit:t}){let l,o,s={position:{top:0,left:0},direction:"down",directionChanged:!1,delta:{top:0,left:0},inflectionPoint:{top:0,left:0}},u=null;function d(){null!==u&&u();let a=Math.max(0,oa(l)),o=na(l),n={top:a-s.position.top,left:o-s.position.left};if("vertical"===e.axis&&0===n.top||"horizontal"===e.axis&&0===n.left)return;let i=Math.abs(n.top)>=Math.abs(n.left)?n.top<0?"up":"down":n.left<0?"left":"right";s.position={top:a,left:o},s.directionChanged=s.direction!==i,s.delta=n,!0===s.directionChanged&&(s.direction=i,s.inflectionPoint=s.position),t("scroll",{...s})}function c(){l=la(o,e.scrollTarget),l.addEventListener("scroll",p,hs),p(!0)}function v(){void 0!==l&&(l.removeEventListener("scroll",p,hs),l=void 0)}function p(t){if(!0===t||0===e.debounce||"0"===e.debounce)d();else if(null===u){let[t,l]=e.debounce?[setTimeout(d,e.debounce),clearTimeout]:[requestAnimationFrame(d),cancelAnimationFrame];u=()=>{l(t),u=null}}}n((()=>e.scrollTarget),(()=>{v(),c()}));let{proxy:f}=a();return n((()=>f.$q.lang.rtl),d),r((()=>{o=f.$el.parentNode,c()})),i((()=>{null!==u&&u(),v()})),Object.assign(f,{trigger:p,getPosition:()=>s}),G}}),ws=Ze({name:"QLayout",props:{container:Boolean,view:{type:String,default:"hhh lpr fff",validator:e=>/^(h|l)h(h|r) lpr (f|l)f(f|r)$/.test(e.toLowerCase())},onScroll:Function,onScrollHeight:Function,onResize:Function},setup(l,{slots:i,emit:r}){let{proxy:{$q:s}}=a(),u=e(null),d=e(s.screen.height),v=e(!0===l.container?0:s.screen.width),p=e({position:0,direction:"down",inflectionPoint:0}),f=e(0),m=e(!0===D.value?0:va()),g=o((()=>"q-layout q-layout--"+(!0===l.container?"containerized":"standard"))),h=o((()=>!1===l.container?{minHeight:s.screen.height+"px"}:null)),b=o((()=>0!==m.value?{[!0===s.lang.rtl?"left":"right"]:`${m.value}px`}:null)),y=o((()=>0!==m.value?{[!0===s.lang.rtl?"right":"left"]:0,[!0===s.lang.rtl?"left":"right"]:`-${m.value}px`,width:`calc(100% + ${m.value}px)`}:null));function w(e){if(!0===l.container||!0!==document.qScrollPrevented){let t={position:e.position.top,direction:e.direction,directionChanged:e.directionChanged,inflectionPoint:e.inflectionPoint.top,delta:e.delta.top};p.value=t,void 0!==l.onScroll&&r("scroll",t)}}function x(e){let{height:t,width:a}=e,o=!1;d.value!==t&&(o=!0,d.value=t,void 0!==l.onScrollHeight&&r("scrollHeight",t),k()),v.value!==a&&(o=!0,v.value=a),!0===o&&void 0!==l.onResize&&r("resize",e)}function _({height:e}){f.value!==e&&(f.value=e,k())}function k(){if(!0===l.container){let e=d.value>f.value?va():0;m.value!==e&&(m.value=e)}}let C=null,$={instances:{},view:o((()=>l.view)),isContainer:o((()=>l.container)),rootRef:u,height:d,containerHeight:f,scrollbarWidth:m,totalWidth:o((()=>v.value+m.value)),rows:o((()=>{let e=l.view.toLowerCase().split(" ");return{top:e[0].split(""),middle:e[1].split(""),bottom:e[2].split("")}})),header:c({size:0,offset:0,space:!1}),right:c({size:300,offset:0,space:!1}),footer:c({size:0,offset:0,space:!1}),left:c({size:300,offset:0,space:!1}),scroll:p,animate(){null!==C?clearTimeout(C):document.body.classList.add("q-body--layout-animate"),C=setTimeout((()=>{C=null,document.body.classList.remove("q-body--layout-animate")}),155)},update(e,t,l){$[e][t]=l}};if(q(Ve,$),va()>0){let e=function(){o=null,i.classList.remove("hide-scrollbar")},t=function(){if(null===o){if(i.scrollHeight>s.screen.height)return;i.classList.add("hide-scrollbar")}else clearTimeout(o);o=setTimeout(e,300)},a=function(l){null!==o&&"remove"===l&&(clearTimeout(o),e()),window[`${l}EventListener`]("resize",t)},o=null,i=document.body;n((()=>!0!==l.container?"add":"remove"),a),!0!==l.container&&a("add"),S((()=>{a("remove")}))}return()=>{let e=mt(i.default,[t(ys,{onScroll:w}),t(an,{onResize:x})]),a=t("div",{class:g.value,style:h.value,ref:!0===l.container?void 0:u,tabindex:-1},e);return!0===l.container?t("div",{class:"q-layout-container overflow-hidden",ref:u},[t(an,{onResize:_}),t("div",{class:"absolute-full",style:b.value},[t("div",{class:"scroll",style:y.value},[a])])]):a}}}),xs=["horizontal","vertical","cell","none"],_s=Ze({name:"QMarkupTable",props:{...Et,dense:Boolean,flat:Boolean,bordered:Boolean,square:Boolean,wrapCells:Boolean,separator:{type:String,default:"horizontal",validator:e=>xs.includes(e)}},setup(e,{slots:l}){let n=a(),i=Pt(e,n.proxy.$q),r=o((()=>`q-markup-table q-table__container q-table__card q-table--${e.separator}-separator`+(!0===i.value?" q-table--dark q-table__card--dark q-dark":"")+(!0===e.dense?" q-table--dense":"")+(!0===e.flat?" q-table--flat":"")+(!0===e.bordered?" q-table--bordered":"")+(!0===e.square?" q-table--square":"")+(!1===e.wrapCells?" q-table--no-wrap":"")));return()=>t("div",{class:r.value},[t("table",{class:"q-table"},pt(l.default))])}}),Ss=Ze({name:"QNoSsr",props:{tag:{type:String,default:"div"},placeholder:String},setup(e,{slots:l}){let{isHydrated:a}=en();return()=>{if(!0===a.value){let a=pt(l.default);return void 0===a?a:a.length>1?t(e.tag,{},a):a[0]}let o={class:"q-no-ssr-placeholder"},n=pt(l.placeholder);return void 0!==n?n.length>1?t(e.tag,o,n):n[0]:void 0!==e.placeholder?t(e.tag,o,e.placeholder):void 0}}}),ks=t("svg",{key:"svg",class:"q-radio__bg absolute non-selectable",viewBox:"0 0 24 24"},[t("path",{d:"M12,22a10,10 0 0 1 -10,-10a10,10 0 0 1 10,-10a10,10 0 0 1 10,10a10,10 0 0 1 -10,10m0,-22a12,12 0 0 0 -12,12a12,12 0 0 0 12,12a12,12 0 0 0 12,-12a12,12 0 0 0 -12,-12"}),t("path",{class:"q-radio__check",d:"M12,6a6,6 0 0 0 -6,6a6,6 0 0 0 6,6a6,6 0 0 0 6,-6a6,6 0 0 0 -6,-6"})]),qs=Ze({name:"QRadio",props:{...Et,...ct,...Za,modelValue:{required:!0},val:{required:!0},label:String,leftLabel:Boolean,checkedIcon:String,uncheckedIcon:String,color:String,keepColor:Boolean,dense:Boolean,disable:Boolean,tabindex:[String,Number]},emits:["update:modelValue"],setup(l,{slots:n,emit:i}){let{proxy:r}=a(),s=Pt(l,r.$q),u=vt(l,Bo),d=e(null),{refocusTargetEl:c,refocusTarget:v}=To(l,d),f=o((()=>p(l.modelValue)===p(l.val))),m=o((()=>"q-radio cursor-pointer no-outline row inline no-wrap items-center"+(!0===l.disable?" disabled":"")+(!0===s.value?" q-radio--dark":"")+(!0===l.dense?" q-radio--dense":"")+(!0===l.leftLabel?" reverse":""))),g=o((()=>{let e=void 0===l.color||!0!==l.keepColor&&!0!==f.value?"":` text-${l.color}`;return`q-radio__inner relative-position q-radio__inner--${!0===f.value?"truthy":"falsy"}${e}`})),h=o((()=>(!0===f.value?l.checkedIcon:l.uncheckedIcon)||null)),b=o((()=>!0===l.disable?-1:l.tabindex||0)),y=Ja(o((()=>{let e={type:"radio"};return void 0!==l.name&&Object.assign(e,{".checked":!0===f.value,"^checked":!0===f.value?"checked":void 0,name:l.name,value:l.val}),e})));function w(e){void 0!==e&&(ae(e),v(e)),!0!==l.disable&&!0!==f.value&&i("update:modelValue",l.val,e)}function x(e){(13===e.keyCode||32===e.keyCode)&&ae(e)}function _(e){(13===e.keyCode||32===e.keyCode)&&w(e)}return Object.assign(r,{set:w}),()=>{let e=null!==h.value?[t("div",{key:"icon",class:"q-radio__icon-container absolute-full flex flex-center no-wrap"},[t(zt,{class:"q-radio__icon",name:h.value})])]:[ks];!0!==l.disable&&y(e,"unshift"," q-radio__native q-ma-none q-pa-none");let a=[t("div",{class:g.value,style:u.value,"aria-hidden":"true"},e)];null!==c.value&&a.push(c.value);let o=void 0!==l.label?mt(n.default,[l.label]):pt(n.default);return void 0!==o&&a.push(t("div",{class:"q-radio__label q-anchor--skip"},o)),t("div",{ref:d,class:m.value,tabindex:b.value,role:"radio","aria-label":l.label,"aria-checked":!0===f.value?"true":"false","aria-disabled":!0===l.disable?"true":void 0,onClick:w,onKeydown:x,onKeyup:_},a)}}}),Cs=Ze({name:"QToggle",props:{...Lo,icon:String,iconColor:String},emits:zo,setup:e=>Vo("toggle",(function(l,a){let n=o((()=>(!0===l.value?e.checkedIcon:!0===a.value?e.indeterminateIcon:e.uncheckedIcon)||e.icon)),i=o((()=>!0===l.value?e.iconColor:null));return()=>[t("div",{class:"q-toggle__track"}),t("div",{class:"q-toggle__thumb absolute flex flex-center no-wrap"},void 0!==n.value?[t(zt,{name:n.value,color:i.value})]:void 0)]}))}),$s={radio:qs,checkbox:Ao,toggle:Cs},Ms=Object.keys($s),Ts=Ze({name:"QOptionGroup",props:{...Et,modelValue:{required:!0},options:{type:Array,validator:e=>e.every((e=>"value"in e&&"label"in e))},name:String,type:{default:"radio",validator:e=>Ms.includes(e)},color:String,keepColor:Boolean,dense:Boolean,size:String,leftLabel:Boolean,inline:Boolean,disable:Boolean},emits:["update:modelValue"],setup(e,{emit:l,slots:n}){let{proxy:{$q:i}}=a();Array.isArray(e.modelValue);e.type;let r=Pt(e,i),s=o((()=>$s[e.type])),u=o((()=>"q-option-group q-gutter-x-sm"+(!0===e.inline?" q-option-group--inline":""))),d=o((()=>{let t={role:"group"};return"radio"===e.type&&(t.role="radiogroup",!0===e.disable&&(t["aria-disabled"]="true")),t}));function c(e){l("update:modelValue",e)}return()=>t("div",{class:u.value,...d.value},e.options.map(((l,a)=>{let o=void 0!==n["label-"+a]?()=>n["label-"+a](l):void 0!==n.label?()=>n.label(l):void 0;return t("div",[t(s.value,{modelValue:e.modelValue,val:l.value,name:void 0===l.name?e.name:l.name,disable:e.disable||l.disable,label:void 0===o?l.label:null,leftLabel:void 0===l.leftLabel?e.leftLabel:l.leftLabel,color:void 0===l.color?e.color:l.color,checkedIcon:l.checkedIcon,uncheckedIcon:l.uncheckedIcon,dark:l.dark||r.value,size:void 0===l.size?e.size:l.size,dense:e.dense,keepColor:void 0===l.keepColor?e.keepColor:l.keepColor,"onUpdate:modelValue":c},o)])})))}}),Bs=Ze({name:"QPage",props:{padding:Boolean,styleFn:Function},setup(e,{slots:l}){let{proxy:{$q:n}}=a(),i=d(Ve,Re);if(i===Re)return Re;if(d(Oe,Re)===Re)return Re;let r=o((()=>{let t=(!0===i.header.space?i.header.size:0)+(!0===i.footer.space?i.footer.size:0);if("function"==typeof e.styleFn){let l=!0===i.isContainer.value?i.containerHeight.value:n.screen.height;return e.styleFn(t,l)}return{minHeight:!0===i.isContainer.value?i.containerHeight.value-t+"px":0===n.screen.height?0!==t?`calc(100vh - ${t}px)`:"100vh":n.screen.height-t+"px"}})),s=o((()=>"q-page"+(!0===e.padding?" q-layout-padding":"")));return()=>t("main",{class:s.value,style:r.value},pt(l.default))}}),Ls=Ze({name:"QPageContainer",setup(e,{slots:l}){let{proxy:{$q:n}}=a(),i=d(Ve,Re);if(i===Re)return Re;q(Oe,!0);let r=o((()=>{let e={};return!0===i.header.space&&(e.paddingTop=`${i.header.size}px`),!0===i.right.space&&(e["padding"+(!0===n.lang.rtl?"Left":"Right")]=`${i.right.size}px`),!0===i.footer.space&&(e.paddingBottom=`${i.footer.size}px`),!0===i.left.space&&(e["padding"+(!0===n.lang.rtl?"Right":"Left")]=`${i.left.size}px`),e}));return()=>t("div",{class:"q-page-container",style:r.value},pt(l.default))}}),zs={position:{type:String,default:"bottom-right",validator:e=>["top-right","top-left","bottom-right","bottom-left","top","right","bottom","left"].includes(e)},offset:{type:Array,validator:e=>2===e.length},expand:Boolean};function Vs(){let{props:e,proxy:{$q:l}}=a(),n=d(Ve,Re);if(n===Re)return Re;let i=o((()=>{let t=e.position;return{top:-1!==t.indexOf("top"),right:-1!==t.indexOf("right"),bottom:-1!==t.indexOf("bottom"),left:-1!==t.indexOf("left"),vertical:"top"===t||"bottom"===t,horizontal:"left"===t||"right"===t}})),r=o((()=>n.header.offset)),s=o((()=>n.right.offset)),u=o((()=>n.footer.offset)),c=o((()=>n.left.offset)),v=o((()=>{let t=0,a=0,o=i.value,n=!0===l.lang.rtl?-1:1;!0===o.top&&0!==r.value?a=`${r.value}px`:!0===o.bottom&&0!==u.value&&(a=-u.value+"px"),!0===o.left&&0!==c.value?t=n*c.value+"px":!0===o.right&&0!==s.value&&(t=-n*s.value+"px");let d={transform:`translate(${t}, ${a})`};return e.offset&&(d.margin=`${e.offset[1]}px ${e.offset[0]}px`),!0===o.vertical?(0!==c.value&&(d[!0===l.lang.rtl?"right":"left"]=`${c.value}px`),0!==s.value&&(d[!0===l.lang.rtl?"left":"right"]=`${s.value}px`)):!0===o.horizontal&&(0!==r.value&&(d.top=`${r.value}px`),0!==u.value&&(d.bottom=`${u.value}px`)),d})),p=o((()=>`q-page-sticky row flex-center fixed-${e.position} q-page-sticky--${!0===e.expand?"expand":"shrink"}`));return{$layout:n,getStickyContent:function(l){let a=pt(l.default);return t("div",{class:p.value,style:v.value},!0===e.expand?a:[t("div",a)])}}}var Os=Ze({name:"QPageScroller",props:{...zs,scrollOffset:{type:Number,default:1e3},reverse:Boolean,duration:{type:Number,default:300},offset:{default:()=>[18,18]}},emits:["click"],setup(l,{slots:r,emit:s}){let u,{proxy:{$q:d}}=a(),{$layout:c,getStickyContent:v}=Vs(),p=e(null),m=o((()=>c.height.value-(!0===c.isContainer.value?c.containerHeight.value:d.screen.height)));function g(){return!0===l.reverse?m.value-c.scroll.value.position>l.scrollOffset:c.scroll.value.position>l.scrollOffset}let h=e(g());function b(){let e=g();h.value!==e&&(h.value=e)}function y(){!0===l.reverse?void 0===u&&(u=n(m,b)):w()}function w(){void 0!==u&&(u(),u=void 0)}function x(e){da(la(!0===c.isContainer.value?p.value:c.rootRef.value),!0===l.reverse?c.height.value:0,l.duration),s("click",e)}function _(){return!0===h.value?t("div",{ref:p,class:"q-page-scroller",onClick:x},v(r)):null}return n(c.scroll,b),n((()=>l.reverse),y),y(),i(w),()=>t(f,{name:"q-transition--fade"},_)}}),As=Ze({name:"QPageSticky",props:zs,setup(e,{slots:t}){let{getStickyContent:l}=Vs();return()=>l(t)}});function Es(e,t){return[!0,!1].includes(e)?e:t}var Ps=Ze({name:"QPagination",props:{...Et,modelValue:{type:Number,required:!0},min:{type:[Number,String],default:1},max:{type:[Number,String],required:!0},maxPages:{type:[Number,String],default:0,validator:e=>("string"==typeof e?parseInt(e,10):e)>=0},inputStyle:[Array,String,Object],inputClass:[Array,String,Object],size:String,disable:Boolean,input:Boolean,iconPrev:String,iconNext:String,iconFirst:String,iconLast:String,toFn:Function,boundaryLinks:{type:Boolean,default:null},boundaryNumbers:{type:Boolean,default:null},directionLinks:{type:Boolean,default:null},ellipses:{type:Boolean,default:null},ripple:{type:[Boolean,Object],default:null},round:Boolean,rounded:Boolean,flat:Boolean,outline:Boolean,unelevated:Boolean,push:Boolean,glossy:Boolean,color:{type:String,default:"primary"},textColor:String,activeDesign:{type:String,default:"",values:e=>""===e||yl.includes(e)},activeColor:String,activeTextColor:String,gutter:String,padding:{type:String,default:"3px 2px"}},emits:["update:modelValue"],setup(l,{emit:i}){let{proxy:r}=a(),{$q:s}=r,u=Pt(l,s),d=o((()=>parseInt(l.min,10))),c=o((()=>parseInt(l.max,10))),v=o((()=>parseInt(l.maxPages,10))),p=o((()=>y.value+" / "+c.value)),f=o((()=>Es(l.boundaryLinks,l.input))),m=o((()=>Es(l.boundaryNumbers,!l.input))),g=o((()=>Es(l.directionLinks,l.input))),h=o((()=>Es(l.ellipses,!l.input))),b=e(null),y=o({get:()=>l.modelValue,set:e=>{if(e=parseInt(e,10),l.disable||isNaN(e))return;let t=tt(e,d.value,c.value);l.modelValue!==t&&i("update:modelValue",t)}});n((()=>`${d.value}|${c.value}`),(()=>{y.value=l.modelValue}));let w=o((()=>"q-pagination row no-wrap items-center"+(!0===l.disable?" disabled":""))),x=o((()=>l.gutter in ml?`${ml[l.gutter]}px`:l.gutter||null)),_=o((()=>null!==x.value?`--q-pagination-gutter-parent:-${x.value};--q-pagination-gutter-child:${x.value}`:null)),S=o((()=>{let e=[l.iconFirst||s.iconSet.pagination.first,l.iconPrev||s.iconSet.pagination.prev,l.iconNext||s.iconSet.pagination.next,l.iconLast||s.iconSet.pagination.last];return!0===s.lang.rtl?e.reverse():e})),k=o((()=>({"aria-disabled":!0===l.disable?"true":"false",role:"navigation"}))),q=o((()=>wl(l,"flat"))),C=o((()=>({[q.value]:!0,round:l.round,rounded:l.rounded,padding:l.padding,color:l.color,textColor:l.textColor,size:l.size,ripple:null===l.ripple||l.ripple}))),$=o((()=>{let e={[q.value]:!1};return""!==l.activeDesign&&(e[l.activeDesign]=!0),e})),M=o((()=>({...$.value,color:l.activeColor||l.color,textColor:l.activeTextColor||l.textColor}))),T=o((()=>{let e=Math.max(v.value,1+(h.value?2:0)+(m.value?2:0)),t={pgFrom:d.value,pgTo:c.value,ellipsesStart:!1,ellipsesEnd:!1,boundaryStart:!1,boundaryEnd:!1,marginalStyle:{minWidth:`${Math.max(2,String(c.value).length)}em`}};return v.value&&e<c.value-d.value+1&&(e=1+2*Math.floor(e/2),t.pgFrom=Math.max(d.value,Math.min(c.value-e+1,l.modelValue-Math.floor(e/2))),t.pgTo=Math.min(c.value,t.pgFrom+e-1),m.value&&(t.boundaryStart=!0,t.pgFrom++),h.value&&t.pgFrom>d.value+(m.value?1:0)&&(t.ellipsesStart=!0,t.pgFrom++),m.value&&(t.boundaryEnd=!0,t.pgTo--),h.value&&t.pgTo<c.value-(m.value?1:0)&&(t.ellipsesEnd=!0,t.pgTo--)),t}));function B(e){y.value=e}let L=o((()=>{function e(){y.value=b.value,b.value=null}return{"onUpdate:modelValue":e=>{b.value=e},onKeyup:t=>{!0===qe(t,13)&&e()},onBlur:e}}));function z(e,a,o){let n={"aria-label":a,"aria-current":"false",...C.value,...e};return!0===o&&Object.assign(n,{"aria-current":"true",...M.value}),void 0!==a&&(void 0!==l.toFn?n.to=l.toFn(a):n.onClick=()=>{B(a)}),t($l,n)}return Object.assign(r,{set:B,setByOffset:function(e){y.value=y.value+e}}),()=>{let e,a=[],o=[];if(!0===f.value&&(a.push(z({key:"bls",disable:l.disable||l.modelValue<=d.value,icon:S.value[0]},d.value)),o.unshift(z({key:"ble",disable:l.disable||l.modelValue>=c.value,icon:S.value[3]},c.value))),!0===g.value&&(a.push(z({key:"bdp",disable:l.disable||l.modelValue<=d.value,icon:S.value[1]},l.modelValue-1)),o.unshift(z({key:"bdn",disable:l.disable||l.modelValue>=c.value,icon:S.value[2]},l.modelValue+1))),!0!==l.input){e=[];let{pgFrom:t,pgTo:n,marginalStyle:i}=T.value;if(!0===T.value.boundaryStart){let e=d.value===l.modelValue;a.push(z({key:"bns",style:i,disable:l.disable,label:d.value},d.value,e))}if(!0===T.value.boundaryEnd){let e=c.value===l.modelValue;o.unshift(z({key:"bne",style:i,disable:l.disable,label:c.value},c.value,e))}!0===T.value.ellipsesStart&&a.push(z({key:"bes",style:i,disable:l.disable,label:"…",ripple:!1},t-1)),!0===T.value.ellipsesEnd&&o.unshift(z({key:"bee",style:i,disable:l.disable,label:"…",ripple:!1},n+1));for(let a=t;a<=n;a++)e.push(z({key:`bpg${a}`,style:i,disable:l.disable,label:a},a,a===l.modelValue))}return t("div",{class:w.value,...k.value},[t("div",{class:"q-pagination__content row no-wrap items-center",style:_.value},[...a,!0===l.input?t(rs,{class:"inline",style:{width:p.value.length/1.5+"em"},type:"number",dense:!0,value:b.value,disable:l.disable,dark:u.value,borderless:!0,inputClass:l.inputClass,inputStyle:l.inputStyle,placeholder:p.value,min:d.value,max:c.value,...L.value}):t("div",{class:"q-pagination__middle row justify-center"},e),...o])])}}});function Fs(e){let t,l,a=!1;function o(){l=arguments,!0!==a&&(a=!0,t=requestAnimationFrame((()=>{e.apply(this,l),l=void 0,a=!1})))}return o.cancel=()=>{window.cancelAnimationFrame(t),a=!1},o}var{passive:Rs}=Z,Ns=Ze({name:"QParallax",props:{src:String,height:{type:Number,default:500},speed:{type:Number,default:1,validator:e=>e>=0&&e<=1},scrollTarget:{default:void 0},onScroll:Function},setup(l,{slots:a,emit:o}){let s,u,d,c,v,p,f=e(0),m=e(null),g=e(null),h=e(null);n((()=>l.height),(()=>{!0===s&&y()})),n((()=>l.scrollTarget),(()=>{!0===s&&(S(),_())}));let b=e=>{f.value=e,void 0!==l.onScroll&&o("scroll",e)};function y(){let e,t,a;p===window?(e=0,a=t=window.innerHeight):(e=rl(p).top,t=sl(p),a=e+t);let o=rl(m.value).top,n=o+l.height;if(void 0!==v||n>e&&o<a){let e=(a-o)/(l.height+t);w((d-l.height)*e*l.speed),b(e)}}let w=e=>{u.style.transform=`translate3d(-50%,${Math.round(e)}px,0)`};function x(){d=u.naturalHeight||u.videoHeight||sl(u),!0===s&&y()}function _(){s=!0,p=la(m.value,l.scrollTarget),p.addEventListener("scroll",y,Rs),window.addEventListener("resize",c,Rs),y()}function S(){!0===s&&(s=!1,p.removeEventListener("scroll",y,Rs),window.removeEventListener("resize",c,Rs),p=void 0,w.cancel(),b.cancel(),c.cancel())}return r((()=>{w=Fs(w),b=Fs(b),c=Fs(x),u=void 0!==a.media?g.value.children[0]:h.value,u.onload=u.onloadstart=u.loadedmetadata=x,x(),u.style.display="initial",void 0!==window.IntersectionObserver?(v=new IntersectionObserver((e=>{(!0===e[0].isIntersecting?_:S)()})),v.observe(m.value)):_()})),i((()=>{S(),void 0!==v&&v.disconnect(),u.onload=u.onloadstart=u.loadedmetadata=null})),()=>t("div",{ref:m,class:"q-parallax",style:{height:`${l.height}px`}},[t("div",{ref:g,class:"q-parallax__media absolute-full"},void 0!==a.media?a.media():[t("img",{ref:h,src:l.src})]),t("div",{class:"q-parallax__content absolute-full column flex-center"},void 0!==a.content?a.content({percentScrolled:f.value}):pt(a.default))])}});function Hs(e,t=new WeakMap){if(Object(e)!==e)return e;if(t.has(e))return t.get(e);let l=e instanceof Date?new Date(e):e instanceof RegExp?new RegExp(e.source,e.flags):e instanceof Set?new Set:e instanceof Map?new Map:"function"!=typeof e.constructor?Object.create(null):void 0!==e.prototype&&"function"==typeof e.prototype.constructor?e:new e.constructor;if("function"==typeof e.constructor&&"function"==typeof e.valueOf){let l=e.valueOf();if(Object(l)!==l){let a=new e.constructor(l);return t.set(e,a),a}}return t.set(e,l),e instanceof Set?e.forEach((e=>{l.add(Hs(e,t))})):e instanceof Map&&e.forEach(((e,a)=>{l.set(a,Hs(e,t))})),Object.assign(l,...Object.keys(e).map((l=>({[l]:Hs(e[l],t)}))))}var Is=Ze({name:"QPopupEdit",props:{modelValue:{required:!0},title:String,buttons:Boolean,labelSet:String,labelCancel:String,color:{type:String,default:"primary"},validate:{type:Function,default:()=>!0},autoSave:Boolean,cover:{type:Boolean,default:!0},disable:Boolean},emits:["update:modelValue","save","cancel","beforeShow","show","beforeHide","hide"],setup(l,{slots:n,emit:i}){let{proxy:r}=a(),{$q:s}=r,u=e(null),d=e(""),c=e(""),v=!1,p=o((()=>H({initialValue:d.value,validate:l.validate,set:f,cancel:g,updatePosition:h},"value",(()=>c.value),(e=>{c.value=e}))));function f(){!1!==l.validate(c.value)&&(!0===b()&&(i("save",c.value,d.value),i("update:modelValue",c.value)),y())}function g(){!0===b()&&i("cancel",c.value,d.value),y()}function h(){m((()=>{u.value.updatePosition()}))}function b(){return!1===Ie(c.value,d.value)}function y(){v=!0,u.value.hide()}function w(){v=!1,d.value=Hs(l.modelValue),c.value=Hs(l.modelValue),i("beforeShow")}function x(){i("show")}function _(){!1===v&&!0===b()&&(!0===l.autoSave&&!0===l.validate(c.value)?(i("save",c.value,d.value),i("update:modelValue",c.value)):i("cancel",c.value,d.value)),i("beforeHide")}function S(){i("hide")}function k(){let e=void 0!==n.default?[].concat(n.default(p.value)):[];return l.title&&e.unshift(t("div",{class:"q-dialog__title q-mt-sm q-mb-sm"},l.title)),!0===l.buttons&&e.push(t("div",{class:"q-popup-edit__buttons row justify-center no-wrap"},[t($l,{flat:!0,color:l.color,label:l.labelCancel||s.lang.label.cancel,onClick:g}),t($l,{flat:!0,color:l.color,label:l.labelSet||s.lang.label.set,onClick:f})])),e}return Object.assign(r,{set:f,cancel:g,show(e){null!==u.value&&u.value.show(e)},hide(e){null!==u.value&&u.value.hide(e)},updatePosition:h}),()=>{if(!0!==l.disable)return t(Ha,{ref:u,class:"q-popup-edit",cover:l.cover,onBeforeShow:w,onShow:x,onBeforeHide:_,onHide:S,onEscapeKey:g},k)}}}),js=Ze({name:"QPopupProxy",props:{...Bl,breakpoint:{type:[String,Number],default:450}},emits:["show","hide"],setup(l,{slots:i,emit:r,attrs:s}){let{proxy:u}=a(),{$q:d}=u,c=e(!1),v=e(null),p=o((()=>parseInt(l.breakpoint,10))),{canShow:f}=Ll({showing:c});function m(){return d.screen.width<p.value||d.screen.height<p.value?"dialog":"menu"}let g=e(m()),h=o((()=>"menu"===g.value?{maxHeight:"99vh"}:{}));function b(e){c.value=!0,r("show",e)}function y(e){c.value=!1,g.value=m(),r("hide",e)}return n((()=>m()),(e=>{!0!==c.value&&(g.value=e)})),Object.assign(u,{show(e){!0===f(e)&&v.value.show(e)},hide(e){v.value.hide(e)},toggle(e){v.value.toggle(e)}}),H(u,"currentComponent",(()=>({type:g.value,ref:v.value}))),()=>{let e,a={ref:v,...h.value,...s,onShow:b,onHide:y};return"dialog"===g.value?e=Pi:(e=Ha,Object.assign(a,{target:l.target,contextMenu:l.contextMenu,noParentEvent:!0,separateClosePopup:!0})),t(e,a,i.default)}}}),Ds={xs:2,sm:4,md:6,lg:10,xl:14};function Qs(e,t,l){return{transform:!0===t?`translateX(${!0===l.lang.rtl?"-":""}100%) scale3d(${-e},1,1)`:`scale3d(${e},1,1)`}}var Us=Ze({name:"QLinearProgress",props:{...Et,...ct,value:{type:Number,default:0},buffer:Number,color:String,trackColor:String,reverse:Boolean,stripe:Boolean,indeterminate:Boolean,query:Boolean,rounded:Boolean,animationSpeed:{type:[String,Number],default:2100},instantFeedback:Boolean},setup(e,{slots:l}){let{proxy:n}=a(),i=Pt(e,n.$q),r=vt(e,Ds),s=o((()=>!0===e.indeterminate||!0===e.query)),u=o((()=>e.reverse!==e.query)),d=o((()=>({...null!==r.value?r.value:{},"--q-linear-progress-speed":`${e.animationSpeed}ms`}))),c=o((()=>"q-linear-progress"+(void 0!==e.color?` text-${e.color}`:"")+(!0===e.reverse||!0===e.query?" q-linear-progress--reverse":"")+(!0===e.rounded?" rounded-borders":""))),v=o((()=>Qs(void 0!==e.buffer?e.buffer:1,u.value,n.$q))),p=o((()=>`with${!0===e.instantFeedback?"out":""}-transition`)),f=o((()=>`q-linear-progress__track absolute-full q-linear-progress__track--${p.value} q-linear-progress__track--${!0===i.value?"dark":"light"}`+(void 0!==e.trackColor?` bg-${e.trackColor}`:""))),m=o((()=>Qs(!0===s.value?1:e.value,u.value,n.$q))),g=o((()=>`q-linear-progress__model absolute-full q-linear-progress__model--${p.value} q-linear-progress__model--${!0===s.value?"in":""}determinate`)),h=o((()=>({width:100*e.value+"%"}))),b=o((()=>`q-linear-progress__stripe absolute-${!0===e.reverse?"right":"left"} q-linear-progress__stripe--${p.value}`));return()=>{let a=[t("div",{class:f.value,style:v.value}),t("div",{class:g.value,style:m.value})];return!0===e.stripe&&!1===s.value&&a.push(t("div",{class:b.value,style:h.value})),t("div",{class:c.value,style:d.value,role:"progressbar","aria-valuemin":0,"aria-valuemax":1,"aria-valuenow":!0===e.indeterminate?void 0:e.value},mt(l.default,a))}}}),Ws=40,Ks=Ze({name:"QPullToRefresh",props:{color:String,bgColor:String,icon:String,noMouse:Boolean,disable:Boolean,scrollTarget:{default:void 0}},emits:["refresh"],setup(l,{slots:s,emit:u}){let{proxy:d}=a(),{$q:c}=d,v=e("pull"),p=e(0),f=e(!1),m=e(-40),g=e(!1),h=e({}),b=o((()=>({opacity:p.value,transform:`translateY(${m.value}px) rotate(${360*p.value}deg)`}))),y=o((()=>"q-pull-to-refresh__puller row flex-center"+(!0===g.value?" q-pull-to-refresh__puller--animating":"")+(void 0!==l.bgColor?` bg-${l.bgColor}`:"")));function w(e){if(!0===e.isFinal)return void(!0===f.value&&(f.value=!1,"pulled"===v.value?(v.value="refreshing",$({pos:20}),S()):"pull"===v.value&&$({pos:-40,ratio:0})));if(!0===g.value||"refreshing"===v.value)return!1;if(!0===e.isFirst){if(0!==oa(q)||"down"!==e.direction)return!0===f.value&&(f.value=!1,v.value="pull",$({pos:-40,ratio:0})),!1;f.value=!0;let{top:t,left:l}=k.getBoundingClientRect();h.value={top:t+"px",left:l+"px",width:window.getComputedStyle(k).getPropertyValue("width")}}le(e.evt);let t=Math.min(140,Math.max(0,e.distance.y));m.value=t-Ws,p.value=tt(t/60,0,1);let l=m.value>20?"pulled":"pull";v.value!==l&&(v.value=l)}let x=o((()=>{let e={down:!0};return!0!==l.noMouse&&(e.mouse=!0),[[Do,w,void 0,e]]})),_=o((()=>"q-pull-to-refresh__content"+(!0===f.value?" no-pointer-events":"")));function S(){u("refresh",(()=>{$({pos:-40,ratio:0},(()=>{v.value="pull"}))}))}let k,q,C=null;function $({pos:e,ratio:t},l){g.value=!0,m.value=e,void 0!==t&&(p.value=t),null!==C&&clearTimeout(C),C=setTimeout((()=>{C=null,g.value=!1,l&&l()}),300)}function M(){q=la(k,l.scrollTarget)}return n((()=>l.scrollTarget),M),r((()=>{k=d.$el,M()})),i((()=>{null!==C&&clearTimeout(C)})),Object.assign(d,{trigger:S,updateScrollTarget:M}),()=>ht("div",{class:"q-pull-to-refresh"},[t("div",{class:_.value},pt(s.default)),t("div",{class:"q-pull-to-refresh__puller-container fixed row flex-center no-pointer-events z-top",style:h.value},[t("div",{class:y.value,style:b.value},["refreshing"!==v.value?t(zt,{name:l.icon||c.iconSet.pullToRefresh.icon,color:l.color,size:"32px"}):t(il,{size:"24px",color:l.color})])])],"main",!1===l.disable,(()=>x.value))}}),Ys=0,Xs=1,Zs=2,Gs=Ze({name:"QRange",props:{...Yo,modelValue:{type:Object,default:()=>({min:null,max:null}),validator:e=>"min"in e&&"max"in e},dragRange:Boolean,dragOnlyRange:Boolean,leftLabelColor:String,leftLabelTextColor:String,rightLabelColor:String,rightLabelTextColor:String,leftLabelValue:[String,Number],rightLabelValue:[String,Number],leftThumbColor:String,rightThumbColor:String},emits:Xo,setup(l,{emit:i}){let{proxy:{$q:r}}=a(),{state:s,methods:u}=Zo({updateValue:T,updatePosition:function(e,t=s.dragging.value){let a,o=u.getDraggingRatio(e,t),n=u.convertRatioToModel(o);switch(t.type){case Ys:o<=t.ratioMax?(a={minR:o,maxR:t.ratioMax,min:n,max:t.valueMax},s.focus.value="min"):(a={minR:t.ratioMax,maxR:o,min:t.valueMax,max:n},s.focus.value="max");break;case Zs:o>=t.ratioMin?(a={minR:t.ratioMin,maxR:o,min:t.valueMin,max:n},s.focus.value="max"):(a={minR:o,maxR:t.ratioMin,min:n,max:t.valueMin},s.focus.value="min");break;case Xs:let e=o-t.offsetRatio,l=tt(t.ratioMin+e,s.innerMinRatio.value,s.innerMaxRatio.value-t.rangeRatio),i=n-t.offsetModel,r=tt(t.valueMin+i,s.innerMin.value,s.innerMax.value-t.rangeValue);a={minR:l,maxR:l+t.rangeRatio,min:s.roundValueFn.value(r),max:s.roundValueFn.value(r+t.rangeValue)},s.focus.value="both"}p.value=null===p.value.min||null===p.value.max?{min:a.min||l.min,max:a.max||l.max}:{min:a.min,max:a.max},!0!==l.snap||0===l.step?(c.value=a.minR,v.value=a.maxR):(c.value=u.convertModelToRatio(p.value.min),v.value=u.convertModelToRatio(p.value.max))},getDragging:function(e){let{left:t,top:a,width:o,height:n}=d.value.getBoundingClientRect(),i=!0===l.dragOnlyRange?0:!0===l.vertical?k.value.offsetHeight/(2*n):k.value.offsetWidth/(2*o),r={left:t,top:a,width:o,height:n,valueMin:p.value.min,valueMax:p.value.max,ratioMin:m.value,ratioMax:g.value},s=u.getDraggingRatio(e,r);return!0!==l.dragOnlyRange&&s<r.ratioMin+i?r.type=Ys:!0===l.dragOnlyRange||s<r.ratioMax-i?!0===l.dragRange||!0===l.dragOnlyRange?(r.type=Xs,Object.assign(r,{offsetRatio:s,offsetModel:u.convertRatioToModel(s),rangeValue:r.valueMax-r.valueMin,rangeRatio:r.ratioMax-r.ratioMin})):r.type=r.ratioMax-s<s-r.ratioMin?Zs:Ys:r.type=Zs,r},formAttrs:o((()=>({type:"hidden",name:l.name,value:`${l.modelValue.min}|${l.modelValue.max}`})))}),d=e(null),c=e(0),v=e(0),p=e({min:0,max:0});function f(){p.value.min=null===l.modelValue.min?s.innerMin.value:tt(l.modelValue.min,s.innerMin.value,s.innerMax.value),p.value.max=null===l.modelValue.max?s.innerMax.value:tt(l.modelValue.max,s.innerMin.value,s.innerMax.value)}n((()=>`${l.modelValue.min}|${l.modelValue.max}|${s.innerMin.value}|${s.innerMax.value}`),f),f();let m=o((()=>u.convertModelToRatio(p.value.min))),g=o((()=>u.convertModelToRatio(p.value.max))),h=o((()=>!0===s.active.value?c.value:m.value)),b=o((()=>!0===s.active.value?v.value:g.value)),y=o((()=>{let e={[s.positionProp.value]:100*h.value+"%",[s.sizeProp.value]:100*(b.value-h.value)+"%"};return void 0!==l.selectionImg&&(e.backgroundImage=`url(${l.selectionImg}) !important`),e})),w=o((()=>{if(!0!==s.editable.value)return{};if(!0===r.platform.is.mobile)return{onClick:u.onMobileClick};let e={onMousedown:u.onActivate};return(!0===l.dragRange||!0===l.dragOnlyRange)&&Object.assign(e,{onFocus:()=>{s.focus.value="both"},onBlur:u.onBlur,onKeydown:B,onKeyup:u.onKeyup}),e}));function x(e){return!0!==r.platform.is.mobile&&!0===s.editable.value&&!0!==l.dragOnlyRange?{onFocus:()=>{s.focus.value=e},onBlur:u.onBlur,onKeydown:B,onKeyup:u.onKeyup}:{}}let _=o((()=>!0!==l.dragOnlyRange?s.tabindex.value:null)),S=o((()=>!0===r.platform.is.mobile||!l.dragRange&&!0!==l.dragOnlyRange?null:s.tabindex.value)),k=e(null),q=o((()=>x("min"))),C=u.getThumbRenderFn({focusValue:"min",getNodeData:()=>({ref:k,key:"tmin",...q.value,tabindex:_.value}),ratio:h,label:o((()=>void 0!==l.leftLabelValue?l.leftLabelValue:p.value.min)),thumbColor:o((()=>l.leftThumbColor||l.thumbColor||l.color)),labelColor:o((()=>l.leftLabelColor||l.labelColor)),labelTextColor:o((()=>l.leftLabelTextColor||l.labelTextColor))}),$=o((()=>x("max"))),M=u.getThumbRenderFn({focusValue:"max",getNodeData:()=>({...$.value,key:"tmax",tabindex:_.value}),ratio:b,label:o((()=>void 0!==l.rightLabelValue?l.rightLabelValue:p.value.max)),thumbColor:o((()=>l.rightThumbColor||l.thumbColor||l.color)),labelColor:o((()=>l.rightLabelColor||l.labelColor)),labelTextColor:o((()=>l.rightLabelTextColor||l.labelTextColor))});function T(e){(p.value.min!==l.modelValue.min||p.value.max!==l.modelValue.max)&&i("update:modelValue",{...p.value}),!0===e&&i("change",{...p.value})}function B(e){if(!Ko.includes(e.keyCode))return;ae(e);let t=([34,33].includes(e.keyCode)?10:1)*s.keyStep.value,a=([34,37,40].includes(e.keyCode)?-1:1)*(!0===s.isReversed.value?-1:1)*(!0===l.vertical?-1:1)*t;if("both"===s.focus.value){let e=p.value.max-p.value.min,t=tt(s.roundValueFn.value(p.value.min+a),s.innerMin.value,s.innerMax.value-e);p.value={min:t,max:s.roundValueFn.value(t+e)}}else{if(!1===s.focus.value)return;{let e=s.focus.value;p.value={...p.value,[e]:tt(s.roundValueFn.value(p.value[e]+a),"min"===e?s.innerMin.value:p.value.min,"max"===e?s.innerMax.value:p.value.max)}}}T()}return()=>{let e=u.getContent(y,S,w,(e=>{e.push(C(),M())}));return t("div",{ref:d,class:"q-range "+s.classes.value+(null===l.modelValue.min||null===l.modelValue.max?" q-slider--no-value":""),...s.attributes.value,"aria-valuenow":l.modelValue.min+"|"+l.modelValue.max},e)}}}),Js=Ze({name:"QRating",props:{...ct,...Za,modelValue:{type:Number,required:!0},max:{type:[String,Number],default:5},icon:[String,Array],iconHalf:[String,Array],iconSelected:[String,Array],iconAriaLabel:[String,Array],color:[String,Array],colorHalf:[String,Array],colorSelected:[String,Array],noReset:Boolean,noDimming:Boolean,readonly:Boolean,disable:Boolean},emits:["update:modelValue"],setup(l,{slots:n,emit:i}){let{proxy:{$q:r}}=a(),u=vt(l),d=Ja(Ga(l)),c=e(0),v={},p=o((()=>!0!==l.readonly&&!0!==l.disable)),f=o((()=>`q-rating row inline items-center q-rating--${!0===p.value?"":"non-"}editable`+(!0===l.noDimming?" q-rating--no-dimming":"")+(!0===l.disable?" disabled":"")+(void 0!==l.color&&!1===Array.isArray(l.color)?` text-${l.color}`:""))),m=o((()=>{let e=!0===Array.isArray(l.icon)?l.icon.length:0,t=!0===Array.isArray(l.iconSelected)?l.iconSelected.length:0,a=!0===Array.isArray(l.iconHalf)?l.iconHalf.length:0,o=!0===Array.isArray(l.color)?l.color.length:0,n=!0===Array.isArray(l.colorSelected)?l.colorSelected.length:0,i=!0===Array.isArray(l.colorHalf)?l.colorHalf.length:0;return{iconLen:e,icon:e>0?l.icon[e-1]:l.icon,selIconLen:t,selIcon:t>0?l.iconSelected[t-1]:l.iconSelected,halfIconLen:a,halfIcon:a>0?l.iconHalf[t-1]:l.iconHalf,colorLen:o,color:o>0?l.color[o-1]:l.color,selColorLen:n,selColor:n>0?l.colorSelected[n-1]:l.colorSelected,halfColorLen:i,halfColor:i>0?l.colorHalf[i-1]:l.colorHalf}})),g=o((()=>{if("string"==typeof l.iconAriaLabel){let e=0!==l.iconAriaLabel.length?`${l.iconAriaLabel} `:"";return t=>`${e}${t}`}if(!0===Array.isArray(l.iconAriaLabel)){let e=l.iconAriaLabel.length;if(e>0)return t=>l.iconAriaLabel[Math.min(t,e)-1]}return(e,t)=>`${t} ${e}`})),h=o((()=>{let e=[],t=m.value,a=Math.ceil(l.modelValue),o=!0===p.value?0:null,n=void 0===l.iconHalf||a===l.modelValue?-1:a;for(let i=1;i<=l.max;i++){let s=0===c.value&&l.modelValue>=i||c.value>0&&c.value>=i,u=n===i&&c.value<i,d=c.value>0&&(!0===u?a:l.modelValue)>=i&&c.value<i,v=!0===u?i<=t.halfColorLen?l.colorHalf[i-1]:t.halfColor:void 0!==t.selColor&&!0===s?i<=t.selColorLen?l.colorSelected[i-1]:t.selColor:i<=t.colorLen?l.color[i-1]:t.color,p=(!0===u?i<=t.halfIconLen?l.iconHalf[i-1]:t.halfIcon:void 0===t.selIcon||!0!==s&&!0!==d?i<=t.iconLen?l.icon[i-1]:t.icon:i<=t.selIconLen?l.iconSelected[i-1]:t.selIcon)||r.iconSet.rating.icon;e.push({name:(!0===u?i<=t.halfIconLen?l.iconHalf[i-1]:t.halfIcon:void 0===t.selIcon||!0!==s&&!0!==d?i<=t.iconLen?l.icon[i-1]:t.icon:i<=t.selIconLen?l.iconSelected[i-1]:t.selIcon)||r.iconSet.rating.icon,attrs:{tabindex:o,role:"radio","aria-checked":l.modelValue===i?"true":"false","aria-label":g.value(i,p)},iconClass:"q-rating__icon"+(!0===s||!0===u?" q-rating__icon--active":"")+(!0===d?" q-rating__icon--exselected":"")+(c.value===i?" q-rating__icon--hovered":"")+(void 0!==v?` text-${v}`:"")})}return e})),b=o((()=>{let e={role:"radiogroup"};return!0===l.disable&&(e["aria-disabled"]="true"),!0===l.readonly&&(e["aria-readonly"]="true"),e}));function y(e){if(!0===p.value){let t=tt(parseInt(e,10),1,parseInt(l.max,10)),a=!0!==l.noReset&&l.modelValue===t?0:t;a!==l.modelValue&&i("update:modelValue",a),c.value=0}}function w(e){!0===p.value&&(c.value=e)}function x(){c.value=0}return s((()=>{v={}})),()=>{let e=[];return h.value.forEach((({iconClass:l,name:a,attrs:o},i)=>{let r=i+1;e.push(t("div",{key:r,ref:e=>{v[`rt${r}`]=e},class:"q-rating__icon-container flex flex-center",...o,onClick(){y(r)},onMouseover(){w(r)},onMouseout:x,onFocus(){w(r)},onBlur:x,onKeyup(e){!function(e,t){switch(e.keyCode){case 13:case 32:return y(t),ae(e);case 37:case 40:return v["rt"+(t-1)]&&v["rt"+(t-1)].focus(),ae(e);case 39:case 38:v[`rt${t+1}`]&&v[`rt${t+1}`].focus(),ae(e)}}(e,r)}},mt(n[`tip-${r}`],[t(zt,{class:l,name:a})])))})),void 0!==l.name&&!0!==l.disable&&d(e,"push"),t("div",{class:f.value,style:u.value,...b.value},e)}}}),eu=Ze({name:"QResponsive",props:Ir,setup(e,{slots:l}){let a=jr(e);return()=>t("div",{class:"q-responsive"},[t("div",{class:"q-responsive__filler overflow-hidden"},[t("div",{style:a.value})]),t("div",{class:"q-responsive__content absolute-full fit"},pt(l.default))])}}),tu=["vertical","horizontal"],lu={vertical:{offset:"offsetY",scroll:"scrollTop",dir:"down",dist:"y"},horizontal:{offset:"offsetX",scroll:"scrollLeft",dir:"right",dist:"x"}},au={prevent:!0,mouse:!0,mouseAllDir:!0},ou=e=>e>=250?50:Math.ceil(e/5),nu=Ze({name:"QScrollArea",props:{...Et,thumbStyle:Object,verticalThumbStyle:Object,horizontalThumbStyle:Object,barStyle:[Array,String,Object],verticalBarStyle:[Array,String,Object],horizontalBarStyle:[Array,String,Object],contentStyle:[Array,String,Object],contentActiveStyle:[Array,String,Object],delay:{type:[String,Number],default:1e3},visible:{type:Boolean,default:null},tabindex:[String,Number],onScroll:Function},setup(l,{slots:r,emit:s}){let u,d=e(!1),c=e(!1),v=e(!1),p={vertical:e(0),horizontal:e(0)},f={vertical:{ref:e(null),position:e(0),size:e(0)},horizontal:{ref:e(null),position:e(0),size:e(0)}},{proxy:m}=a(),y=Pt(l,m.$q),w=null,x=e(null),_=o((()=>"q-scrollarea"+(!0===y.value?" q-scrollarea--dark":"")));f.vertical.percentage=o((()=>{let e=f.vertical.size.value-p.vertical.value;if(e<=0)return 0;let t=tt(f.vertical.position.value/e,0,1);return Math.round(1e4*t)/1e4})),f.vertical.thumbHidden=o((()=>!0!==(null===l.visible?v.value:l.visible)&&!1===d.value&&!1===c.value||f.vertical.size.value<=p.vertical.value+1)),f.vertical.thumbStart=o((()=>f.vertical.percentage.value*(p.vertical.value-f.vertical.thumbSize.value))),f.vertical.thumbSize=o((()=>Math.round(tt(p.vertical.value*p.vertical.value/f.vertical.size.value,ou(p.vertical.value),p.vertical.value)))),f.vertical.style=o((()=>({...l.thumbStyle,...l.verticalThumbStyle,top:`${f.vertical.thumbStart.value}px`,height:`${f.vertical.thumbSize.value}px`}))),f.vertical.thumbClass=o((()=>"q-scrollarea__thumb q-scrollarea__thumb--v absolute-right"+(!0===f.vertical.thumbHidden.value?" q-scrollarea__thumb--invisible":""))),f.vertical.barClass=o((()=>"q-scrollarea__bar q-scrollarea__bar--v absolute-right"+(!0===f.vertical.thumbHidden.value?" q-scrollarea__bar--invisible":""))),f.horizontal.percentage=o((()=>{let e=f.horizontal.size.value-p.horizontal.value;if(e<=0)return 0;let t=tt(Math.abs(f.horizontal.position.value)/e,0,1);return Math.round(1e4*t)/1e4})),f.horizontal.thumbHidden=o((()=>!0!==(null===l.visible?v.value:l.visible)&&!1===d.value&&!1===c.value||f.horizontal.size.value<=p.horizontal.value+1)),f.horizontal.thumbStart=o((()=>f.horizontal.percentage.value*(p.horizontal.value-f.horizontal.thumbSize.value))),f.horizontal.thumbSize=o((()=>Math.round(tt(p.horizontal.value*p.horizontal.value/f.horizontal.size.value,ou(p.horizontal.value),p.horizontal.value)))),f.horizontal.style=o((()=>({...l.thumbStyle,...l.horizontalThumbStyle,[!0===m.$q.lang.rtl?"right":"left"]:`${f.horizontal.thumbStart.value}px`,width:`${f.horizontal.thumbSize.value}px`}))),f.horizontal.thumbClass=o((()=>"q-scrollarea__thumb q-scrollarea__thumb--h absolute-bottom"+(!0===f.horizontal.thumbHidden.value?" q-scrollarea__thumb--invisible":""))),f.horizontal.barClass=o((()=>"q-scrollarea__bar q-scrollarea__bar--h absolute-bottom"+(!0===f.horizontal.thumbHidden.value?" q-scrollarea__bar--invisible":"")));let S=o((()=>!0===f.vertical.thumbHidden.value&&!0===f.horizontal.thumbHidden.value?l.contentStyle:l.contentActiveStyle)),k=[[Do,e=>{z(e,"vertical")},void 0,{vertical:!0,...au}]],q=[[Do,e=>{z(e,"horizontal")},void 0,{horizontal:!0,...au}]];function C(){let e={};return tu.forEach((t=>{let l=f[t];e[t+"Position"]=l.position.value,e[t+"Percentage"]=l.percentage.value,e[t+"Size"]=l.size.value,e[t+"ContainerSize"]=p[t].value})),e}let $=re((()=>{let e=C();e.ref=m,s("scroll",e)}),0);function M(e,t,l){!1!==tu.includes(e)&&("vertical"===e?da:ca)(x.value,t,l)}function T({height:e,width:t}){let l=!1;p.vertical.value!==e&&(p.vertical.value=e,l=!0),p.horizontal.value!==t&&(p.horizontal.value=t,l=!0),!0===l&&E()}function B({position:e}){let t=!1;f.vertical.position.value!==e.top&&(f.vertical.position.value=e.top,t=!0),f.horizontal.position.value!==e.left&&(f.horizontal.position.value=e.left,t=!0),!0===t&&E()}function L({height:e,width:t}){f.horizontal.size.value!==t&&(f.horizontal.size.value=t,E()),f.vertical.size.value!==e&&(f.vertical.size.value=e,E())}function z(e,t){let l=f[t];if(!0===e.isFirst){if(!0===l.thumbHidden.value)return;u=l.position.value,c.value=!0}else if(!0!==c.value)return;!0===e.isFinal&&(c.value=!1);let a=lu[t],o=p[t].value,n=(l.size.value-o)/(o-l.thumbSize.value),i=e.distance[a.dist];P(u+(e.direction===a.dir?1:-1)*i*n,t)}function V(e,t){let l=f[t];if(!0!==l.thumbHidden.value){let a=e[lu[t].offset];if(a<l.thumbStart.value||a>l.thumbStart.value+l.thumbSize.value){P((a-l.thumbSize.value/2)/p[t].value*l.size.value,t)}null!==l.ref.value&&l.ref.value.dispatchEvent(new MouseEvent(e.type,e))}}function O(e){V(e,"vertical")}function A(e){V(e,"horizontal")}function E(){d.value=!0,null!==w&&clearTimeout(w),w=setTimeout((()=>{w=null,d.value=!1}),l.delay),void 0!==l.onScroll&&$()}function P(e,t){x.value[lu[t].scroll]=e}let F=null;function R(){null!==F&&clearTimeout(F),F=setTimeout((()=>{F=null,v.value=!0}),m.$q.platform.is.ios?50:0)}function N(){null!==F&&(clearTimeout(F),F=null),v.value=!1}let H=null;return n((()=>m.$q.lang.rtl),(e=>{null!==x.value&&ca(x.value,Math.abs(f.horizontal.position.value)*(!0===e?-1:1))})),h((()=>{H={top:f.vertical.position.value,left:f.horizontal.position.value}})),b((()=>{if(null===H)return;let e=x.value;null!==e&&(ca(e,H.left),da(e,H.top))})),i($.cancel),Object.assign(m,{getScrollTarget:()=>x.value,getScroll:C,getScrollPosition:()=>({top:f.vertical.position.value,left:f.horizontal.position.value}),getScrollPercentage:()=>({top:f.vertical.percentage.value,left:f.horizontal.percentage.value}),setScrollPosition:M,setScrollPercentage(e,t,l){M(e,t*(f[e].size.value-p[e].value)*("horizontal"===e&&!0===m.$q.lang.rtl?-1:1),l)}}),()=>t("div",{class:_.value,onMouseenter:R,onMouseleave:N},[t("div",{ref:x,class:"q-scrollarea__container scroll relative-position fit hide-scrollbar",tabindex:void 0!==l.tabindex?l.tabindex:void 0},[t("div",{class:"q-scrollarea__content absolute",style:S.value},mt(r.default,[t(an,{debounce:0,onResize:L})])),t(ys,{axis:"both",onScroll:B})]),t(an,{debounce:0,onResize:T}),t("div",{class:f.vertical.barClass.value,style:[l.barStyle,l.verticalBarStyle],"aria-hidden":"true",onMousedown:O}),t("div",{class:f.horizontal.barClass.value,style:[l.barStyle,l.horizontalBarStyle],"aria-hidden":"true",onMousedown:A}),g(t("div",{ref:f.vertical.ref,class:f.vertical.thumbClass.value,style:f.vertical.style.value,"aria-hidden":"true"}),k),g(t("div",{ref:f.horizontal.ref,class:f.horizontal.thumbClass.value,style:f.horizontal.style.value,"aria-hidden":"true"}),q)])}}),iu=1e3,ru=["start","center","end","start-force","center-force","end-force"],su=Array.prototype.filter,uu=void 0===window.getComputedStyle(document.body).overflowAnchor?G:function(e,t){null!==e&&(void 0!==e._qOverflowAnimationFrame&&cancelAnimationFrame(e._qOverflowAnimationFrame),e._qOverflowAnimationFrame=requestAnimationFrame((()=>{if(null===e)return;e._qOverflowAnimationFrame=void 0;let l=e.children||[];su.call(l,(e=>e.dataset&&void 0!==e.dataset.qVsAnchor)).forEach((e=>{delete e.dataset.qVsAnchor}));let a=l[t];a&&a.dataset&&(a.dataset.qVsAnchor="")})))};function du(e,t){return e+t}function cu(e,t,l,a,o,n,i,r){let s=e===window?document.scrollingElement||document.documentElement:e,u=!0===o?"offsetWidth":"offsetHeight",d={scrollStart:0,scrollViewSize:-i-r,scrollMaxSize:0,offsetStart:-i,offsetEnd:-r};if(!0===o?(e===window?(d.scrollStart=window.pageXOffset||window.scrollX||document.body.scrollLeft||0,d.scrollViewSize+=document.documentElement.clientWidth):(d.scrollStart=s.scrollLeft,d.scrollViewSize+=s.clientWidth),d.scrollMaxSize=s.scrollWidth,!0===n&&(d.scrollStart=(!0===on?d.scrollMaxSize-d.scrollViewSize:0)-d.scrollStart)):(e===window?(d.scrollStart=window.pageYOffset||window.scrollY||document.body.scrollTop||0,d.scrollViewSize+=document.documentElement.clientHeight):(d.scrollStart=s.scrollTop,d.scrollViewSize+=s.clientHeight),d.scrollMaxSize=s.scrollHeight),null!==l)for(let c=l.previousElementSibling;null!==c;c=c.previousElementSibling)!1===c.classList.contains("q-virtual-scroll--skip")&&(d.offsetStart+=c[u]);if(null!==a)for(let c=a.nextElementSibling;null!==c;c=c.nextElementSibling)!1===c.classList.contains("q-virtual-scroll--skip")&&(d.offsetEnd+=c[u]);if(t!==e){let l=s.getBoundingClientRect(),a=t.getBoundingClientRect();!0===o?(d.offsetStart+=a.left-l.left,d.offsetEnd-=a.width):(d.offsetStart+=a.top-l.top,d.offsetEnd-=a.height),e!==window&&(d.offsetStart+=d.scrollStart),d.offsetEnd+=d.scrollMaxSize-d.offsetStart}return d}function vu(e,t,l,a){"end"===t&&(t=(e===window?document.body:e)[!0===l?"scrollWidth":"scrollHeight"]),e===window?!0===l?(!0===a&&(t=(!0===on?document.body.scrollWidth-document.documentElement.clientWidth:0)-t),window.scrollTo(t,window.pageYOffset||window.scrollY||document.body.scrollTop||0)):window.scrollTo(window.pageXOffset||window.scrollX||document.body.scrollLeft||0,t):!0===l?(!0===a&&(t=(!0===on?e.scrollWidth-e.offsetWidth:0)-t),e.scrollLeft=t):e.scrollTop=t}function pu(e,t,l,a){if(l>=a)return 0;let o=t.length,n=Math.floor(l/iu),i=Math.floor((a-1)/iu)+1,r=e.slice(n,i).reduce(du,0);return l%iu!=0&&(r-=t.slice(n*iu,l).reduce(du,0)),a%iu!=0&&a!==o&&(r-=t.slice(a,i*iu).reduce(du,0)),r}var fu={virtualScrollSliceSize:{type:[Number,String],default:null},virtualScrollSliceRatioBefore:{type:[Number,String],default:1},virtualScrollSliceRatioAfter:{type:[Number,String],default:1},virtualScrollItemSize:{type:[Number,String],default:24},virtualScrollStickySizeStart:{type:[Number,String],default:0},virtualScrollStickySizeEnd:{type:[Number,String],default:0},tableColspan:[Number,String]},mu=Object.keys(fu),gu={virtualScrollHorizontal:Boolean,onVirtualScroll:Function,...fu};function hu({virtualScrollLength:l,getVirtualScrollTarget:r,getVirtualScrollEl:s,virtualScrollItemSizeComputed:u}){let d,c,v,p,f=a(),{props:g,emit:w,proxy:x}=f,{$q:_}=x,S=[],k=e(0),q=e(0),C=e({}),$=e(null),M=e(null),T=e(null),B=e({from:0,to:0}),L=o((()=>void 0!==g.tableColspan?g.tableColspan:100));void 0===u&&(u=o((()=>g.virtualScrollItemSize)));let z=o((()=>u.value+";"+g.virtualScrollHorizontal)),V=o((()=>z.value+";"+g.virtualScrollSliceRatioBefore+";"+g.virtualScrollSliceRatioAfter));function O(){N(c,!0)}function A(e){N(void 0===e?c:e)}function E(e,t){let a=r();if(null==a||8===a.nodeType)return;let o=cu(a,s(),$.value,M.value,g.virtualScrollHorizontal,_.lang.rtl,g.virtualScrollStickySizeStart,g.virtualScrollStickySizeEnd);v!==o.scrollViewSize&&H(o.scrollViewSize),P(a,o,Math.min(l.value-1,Math.max(0,parseInt(e,10)||0)),0,-1!==ru.indexOf(t)?t:-1!==c&&e>c?"end":"start")}function P(e,t,a,o,n){let i="string"==typeof n&&-1!==n.indexOf("-force"),r=!0===i?n.replace("-force",""):n,s=void 0!==r?r:"start",u=Math.max(0,a-C.value[s]),c=u+C.value.total;c>l.value&&(c=l.value,u=Math.max(0,c-C.value.total)),d=t.scrollStart;let v=u!==B.value.from||c!==B.value.to;if(!1===v&&void 0===r)return void I(a);let{activeElement:f}=document,m=T.value;!0===v&&null!==m&&m!==f&&!0===m.contains(f)&&(m.addEventListener("focusout",R),setTimeout((()=>{null!==m&&m.removeEventListener("focusout",R)}))),uu(m,a-u);let h=void 0!==r?p.slice(u,a).reduce(du,0):0;if(!0===v){let e=c>=B.value.from&&u<=B.value.to?B.value.to:c;B.value={from:u,to:e},k.value=pu(S,p,0,u),q.value=pu(S,p,c,l.value),requestAnimationFrame((()=>{B.value.to!==c&&d===t.scrollStart&&(B.value={from:B.value.from,to:c},q.value=pu(S,p,c,l.value))}))}requestAnimationFrame((()=>{if(d!==t.scrollStart)return;!0===v&&F(u);let l=p.slice(u,a).reduce(du,0),n=l+t.offsetStart+k.value,s=n+p[a],c=n+o;if(void 0!==r){let e=l-h,o=t.scrollStart+e;c=!0!==i&&o<n&&s<o+t.scrollViewSize?o:"end"===r?s-t.scrollViewSize:n-("start"===r?0:Math.round((t.scrollViewSize-p[a])/2))}d=c,vu(e,c,g.virtualScrollHorizontal,_.lang.rtl),I(a)}))}function F(e){let t=T.value;if(t){let l,a,o=su.call(t.children,(e=>e.classList&&!1===e.classList.contains("q-virtual-scroll--skip"))),n=o.length,i=!0===g.virtualScrollHorizontal?e=>e.getBoundingClientRect().width:e=>e.offsetHeight,r=e;for(let e=0;e<n;){for(l=i(o[e]),e++;e<n&&!0===o[e].classList.contains("q-virtual-scroll--with-prev");)l+=i(o[e]),e++;a=l-p[r],0!==a&&(p[r]+=a,S[Math.floor(r/iu)]+=a),r++}}}function R(){null!==T.value&&void 0!==T.value&&T.value.focus()}function N(e,t){let a=1*u.value;(!0===t||!1===Array.isArray(p))&&(p=[]);let o=p.length;p.length=l.value;for(let i=l.value-1;i>=o;i--)p[i]=a;let n=Math.floor((l.value-1)/iu);S=[];for(let i=0;i<=n;i++){let e=0,t=Math.min((i+1)*iu,l.value);for(let l=i*iu;l<t;l++)e+=p[l];S.push(e)}c=-1,d=void 0,k.value=pu(S,p,0,B.value.from),q.value=pu(S,p,B.value.to,l.value),e>=0?(F(B.value.from),m((()=>{E(e)}))):j()}function H(e){if(void 0===e&&typeof window<"u"){let t=r();null!=t&&8!==t.nodeType&&(e=cu(t,s(),$.value,M.value,g.virtualScrollHorizontal,_.lang.rtl,g.virtualScrollStickySizeStart,g.virtualScrollStickySizeEnd).scrollViewSize)}v=e;let t=parseFloat(g.virtualScrollSliceRatioBefore)||0,l=1+t+(parseFloat(g.virtualScrollSliceRatioAfter)||0),a=void 0===e||e<=0?1:Math.ceil(e/u.value),o=Math.max(1,a,Math.ceil((g.virtualScrollSliceSize>0?g.virtualScrollSliceSize:10)/l));C.value={total:Math.ceil(o*l),start:Math.ceil(o*t),center:Math.ceil(o*(.5+t)),end:Math.ceil(o*(1+t)),view:a}}function I(e){c!==e&&(void 0!==g.onVirtualScroll&&w("virtualScroll",{index:e,from:B.value.from,to:B.value.to-1,direction:e<c?"decrease":"increase",ref:x}),c=e)}n(V,(()=>{H()})),n(z,O),H();let j=re((function(){let e=r();if(null==e||8===e.nodeType)return;let t=cu(e,s(),$.value,M.value,g.virtualScrollHorizontal,_.lang.rtl,g.virtualScrollStickySizeStart,g.virtualScrollStickySizeEnd),a=l.value-1,o=t.scrollMaxSize-t.offsetStart-t.offsetEnd-q.value;if(d===t.scrollStart)return;if(t.scrollMaxSize<=0)return void P(e,t,0,0);v!==t.scrollViewSize&&H(t.scrollViewSize),F(B.value.from);let n=Math.floor(t.scrollMaxSize-Math.max(t.scrollViewSize,t.offsetEnd)-Math.min(p[a],t.scrollViewSize/2));if(n>0&&Math.ceil(t.scrollStart)>=n)return void P(e,t,a,t.scrollMaxSize-t.offsetEnd-S.reduce(du,0));let i=0,u=t.scrollStart-t.offsetStart,c=u;if(u<=o&&u+t.scrollViewSize>=k.value)u-=k.value,i=B.value.from,c=u;else for(let l=0;u>=S[l]&&i<a;l++)u-=S[l],i+=iu;for(;u>0&&i<a;)u-=p[i],u>-t.scrollViewSize?(i++,c=u):c=p[i]+u;P(e,t,i,c)}),!0===_.platform.is.ios?120:35);y((()=>{H()}));let D=!1;return h((()=>{D=!0})),b((()=>{if(!0!==D)return;let e=r();void 0!==d&&null!=e&&8!==e.nodeType?vu(e,d,g.virtualScrollHorizontal,_.lang.rtl):E(c)})),i((()=>{j.cancel()})),Object.assign(x,{scrollTo:E,reset:O,refresh:A}),{virtualScrollSliceRange:B,virtualScrollSliceSizeComputed:C,setVirtualScrollSize:H,onVirtualScrollEvt:j,localResetVirtualScroll:N,padVirtualScroll:function(e,l){let a=!0===g.virtualScrollHorizontal?"width":"height",o={["--q-virtual-scroll-item-"+a]:u.value+"px"};return["tbody"===e?t(e,{class:"q-virtual-scroll__padding",key:"before",ref:$},[t("tr",[t("td",{style:{[a]:`${k.value}px`,...o},colspan:L.value})])]):t(e,{class:"q-virtual-scroll__padding",key:"before",ref:$,style:{[a]:`${k.value}px`,...o}}),t(e,{class:"q-virtual-scroll__content",key:"content",ref:T,tabindex:-1},l.flat()),"tbody"===e?t(e,{class:"q-virtual-scroll__padding",key:"after",ref:M},[t("tr",[t("td",{style:{[a]:`${q.value}px`,...o},colspan:L.value})])]):t(e,{class:"q-virtual-scroll__padding",key:"after",ref:M,style:{[a]:`${q.value}px`,...o}})]},scrollTo:E,reset:O,refresh:A}}var bu=e=>["add","add-unique","toggle"].includes(e),yu=Object.keys(Cr),wu=Ze({name:"QSelect",inheritAttrs:!1,props:{...gu,...Za,...Cr,modelValue:{required:!0},multiple:Boolean,displayValue:[String,Number],displayValueHtml:Boolean,dropdownIcon:String,options:{type:Array,default:()=>[]},optionValue:[Function,String],optionLabel:[Function,String],optionDisable:[Function,String],hideSelected:Boolean,hideDropdownIcon:Boolean,fillInput:Boolean,maxValues:[Number,String],optionsDense:Boolean,optionsDark:{type:Boolean,default:null},optionsSelectedClass:String,optionsHtml:Boolean,optionsCover:Boolean,menuShrink:Boolean,menuAnchor:String,menuSelf:String,menuOffset:Array,popupContentClass:String,popupContentStyle:[String,Array,Object],popupNoRouteDismiss:Boolean,useInput:Boolean,useChips:Boolean,newValueMode:{type:String,validator:bu},mapOptions:Boolean,emitValue:Boolean,inputDebounce:{type:[Number,String],default:500},inputClass:[Array,String,Object],inputStyle:[Array,String,Object],tabindex:{type:[String,Number],default:0},autocomplete:String,transitionShow:String,transitionHide:String,transitionDuration:[String,Number],behavior:{type:String,validator:e=>["default","menu","dialog"].includes(e),default:"default"},virtualScrollItemSize:{type:[Number,String],default:void 0},onNewValue:Function,onFilter:Function},emits:[...$r,"add","remove","inputValue","newValue","keyup","keypress","keydown","filterAbort"],setup(l,{slots:r,emit:d}){let c,v,p,f,g,h,b,{proxy:y}=a(),{$q:w}=y,x=e(!1),_=e(!1),S=e(-1),k=e(""),q=e(!1),C=e(!1),$=null,M=null,T=null,B=e(null),L=e(null),z=e(null),V=e(null),O=e(null),A=eo(l),E=is(Fe),P=o((()=>Array.isArray(l.options)?l.options.length:0)),F=o((()=>void 0===l.virtualScrollItemSize?!0===l.optionsDense?24:48:l.virtualScrollItemSize)),{virtualScrollSliceRange:R,virtualScrollSliceSizeComputed:N,localResetVirtualScroll:H,padVirtualScroll:I,onVirtualScrollEvt:j,scrollTo:D,setVirtualScrollSize:Q}=hu({virtualScrollLength:P,getVirtualScrollTarget:function(){return Ee()},getVirtualScrollEl:Ee,virtualScrollItemSizeComputed:F}),U=Mr(),W=o((()=>{let e=!0===l.mapOptions&&!0!==l.multiple,t=void 0===l.modelValue||null===l.modelValue&&!0!==e?[]:!0===l.multiple&&Array.isArray(l.modelValue)?l.modelValue:[l.modelValue];if(!0===l.mapOptions&&!0===Array.isArray(l.options)){let a=!0===l.mapOptions&&void 0!==c?c:[],o=t.map((e=>function(e,t){let a=t=>Ie(me.value(t),e);return l.options.find(a)||t.find(a)||e}(e,a)));return null===l.modelValue&&!0===e?o.filter((e=>null!==e)):o}return t})),K=o((()=>{let e={};return yu.forEach((t=>{let a=l[t];void 0!==a&&(e[t]=a)})),e})),Y=o((()=>null===l.optionsDark?U.isDark.value:l.optionsDark)),X=o((()=>qr(W.value))),Z=o((()=>{let e="q-field__input q-placeholder col";return!0===l.hideSelected||0===W.value.length?[e,l.inputClass]:(e+=" q-field__input--padding",void 0===l.inputClass?e:[e,l.inputClass])})),G=o((()=>(!0===l.virtualScrollHorizontal?"q-virtual-scroll--horizontal":"")+(l.popupContentClass?" "+l.popupContentClass:""))),J=o((()=>0===P.value)),ee=o((()=>W.value.map((e=>ge.value(e))).join(", "))),oe=o((()=>void 0!==l.displayValue?l.displayValue:ee.value)),ne=o((()=>!0===l.optionsHtml?()=>!0:e=>null!=e&&!0===e.html)),ie=o((()=>!0===l.displayValueHtml||void 0===l.displayValue&&(!0===l.optionsHtml||W.value.some(ne.value)))),re=o((()=>!0===U.focused.value?l.tabindex:-1)),se=o((()=>{let e={tabindex:l.tabindex,role:"combobox","aria-label":l.label,"aria-readonly":!0===l.readonly?"true":"false","aria-autocomplete":!0===l.useInput?"list":"none","aria-expanded":!0===x.value?"true":"false","aria-controls":`${U.targetUid.value}_lb`};return S.value>=0&&(e["aria-activedescendant"]=`${U.targetUid.value}_${S.value}`),e})),ue=o((()=>({id:`${U.targetUid.value}_lb`,role:"listbox","aria-multiselectable":!0===l.multiple?"true":"false"}))),de=o((()=>W.value.map(((e,t)=>({index:t,opt:e,html:ne.value(e),selected:!0,removeAtIndex:_e,toggleOption:Ce,tabindex:re.value}))))),ce=o((()=>{if(0===P.value)return[];let{from:e,to:t}=R.value;return l.options.slice(e,t).map(((t,a)=>{let o=!0===he.value(t),n=!0===Be(t),i=e+a,r={clickable:!0,active:n,activeClass:fe.value,manualFocus:!0,focused:!1,disable:o,tabindex:-1,dense:l.optionsDense,dark:Y.value,role:"option","aria-selected":!0===n?"true":"false",id:`${U.targetUid.value}_${i}`,onClick:()=>{Ce(t)}};return!0!==o&&(S.value===i&&(r.focused=!0),!0===w.platform.is.desktop&&(r.onMousemove=()=>{!0===x.value&&$e(i)})),{index:i,opt:t,html:ne.value(t),label:ge.value(t),selected:r.active,focused:r.focused,toggleOption:Ce,setOptionIndex:$e,itemProps:r}}))})),ve=o((()=>void 0!==l.dropdownIcon?l.dropdownIcon:w.iconSet.arrow.dropdown)),pe=o((()=>!1===l.optionsCover&&!0!==l.outlined&&!0!==l.standout&&!0!==l.borderless&&!0!==l.rounded)),fe=o((()=>void 0!==l.optionsSelectedClass?l.optionsSelectedClass:void 0!==l.color?`text-${l.color}`:"")),me=o((()=>Te(l.optionValue,"value"))),ge=o((()=>Te(l.optionLabel,"label"))),he=o((()=>Te(l.optionDisable,"disable"))),be=o((()=>W.value.map((e=>me.value(e))))),ye=o((()=>{let e={onInput:Fe,onChange:E,onKeydown:Ae,onKeyup:Ve,onKeypress:Oe,onFocus:Le,onClick(e){!0===v&&te(e)}};return e.onCompositionstart=e.onCompositionupdate=e.onCompositionend=E,e}));function we(e){return!0===l.emitValue?me.value(e):e}function xe(e){if(-1!==e&&e<W.value.length)if(!0===l.multiple){let t=l.modelValue.slice();d("remove",{index:e,value:t.splice(e,1)[0]}),d("update:modelValue",t)}else d("update:modelValue",null)}function _e(e){xe(e),U.focus()}function Se(e,t){let a=we(e);if(!0!==l.multiple)return!0===l.fillInput&&Ne(ge.value(e),!0,!0),void d("update:modelValue",a);if(0===W.value.length)return d("add",{index:0,value:a}),void d("update:modelValue",!0===l.multiple?[a]:a);if(!0===t&&!0===Be(e)||void 0!==l.maxValues&&l.modelValue.length>=l.maxValues)return;let o=l.modelValue.slice();d("add",{index:o.length,value:a}),o.push(a),d("update:modelValue",o)}function Ce(e,t){if(!0!==U.editable.value||void 0===e||!0===he.value(e))return;let a=me.value(e);if(!0!==l.multiple)return!0!==t&&(Ne(!0===l.fillInput?ge.value(e):"",!0,!0),Ge()),null!==L.value&&L.value.focus(),void((0===W.value.length||!0!==Ie(me.value(W.value[0]),a))&&d("update:modelValue",!0===l.emitValue?a:e));if((!0!==v||!0===q.value)&&U.focus(),Le(),0===W.value.length){let t=!0===l.emitValue?a:e;return d("add",{index:0,value:t}),void d("update:modelValue",!0===l.multiple?[t]:t)}let o=l.modelValue.slice(),n=be.value.findIndex((e=>Ie(e,a)));if(-1!==n)d("remove",{index:n,value:o.splice(n,1)[0]});else{if(void 0!==l.maxValues&&o.length>=l.maxValues)return;let t=!0===l.emitValue?a:e;d("add",{index:o.length,value:t}),o.push(t)}d("update:modelValue",o)}function $e(e){if(!0!==w.platform.is.desktop)return;let t=-1!==e&&e<P.value?e:-1;S.value!==t&&(S.value=t)}function Me(e=1,t){if(!0===x.value){let a=S.value;do{a=lt(a+e,-1,P.value-1)}while(-1!==a&&a!==S.value&&!0===he.value(l.options[a]));S.value!==a&&($e(a),D(a),!0!==t&&!0===l.useInput&&!0===l.fillInput&&Re(a>=0?ge.value(l.options[a]):f,!0))}}function Te(e,t){let l=void 0!==e?e:t;return"function"==typeof l?l:e=>null!==e&&"object"==typeof e&&l in e?e[l]:e}function Be(e){let t=me.value(e);return void 0!==be.value.find((e=>Ie(e,t)))}function Le(e){!0===l.useInput&&null!==L.value&&(void 0===e||L.value===e.target&&e.target.value===ee.value)&&L.value.select()}function ze(e){!0===qe(e,27)&&!0===x.value&&(te(e),Ge(),Je()),d("keyup",e)}function Ve(e){let{value:t}=e.target;if(void 0===e.keyCode)if(e.target.value="",null!==$&&(clearTimeout($),$=null),null!==M&&(clearTimeout(M),M=null),Je(),"string"==typeof t&&0!==t.length){let e=t.toLocaleLowerCase(),a=t=>{let a=l.options.find((l=>t.value(l).toLocaleLowerCase()===e));return void 0!==a&&(-1===W.value.indexOf(a)?Ce(a):Ge(),!0)},o=e=>{!0!==a(me)&&(!0===a(ge)||!0===e||He(t,!0,(()=>o(!0))))};o()}else U.clearValue(e);else ze(e)}function Oe(e){d("keypress",e)}function Ae(e){if(d("keydown",e),!0===ke(e))return;let t=0!==k.value.length&&(void 0!==l.newValueMode||void 0!==l.onNewValue),a=!0!==e.shiftKey&&!0!==l.multiple&&(-1!==S.value||!0===t);if(27===e.keyCode)return void le(e);if(9===e.keyCode&&!1===a)return void Xe();if(void 0===e.target||e.target.id!==U.targetUid.value||!0!==U.editable.value)return;if(40===e.keyCode&&!0!==U.innerLoading.value&&!1===x.value)return ae(e),void Ze();if(8===e.keyCode&&(!0===l.useChips||!0===l.clearable)&&!0!==l.hideSelected&&0===k.value.length)return void(!0===l.multiple&&!0===Array.isArray(l.modelValue)?xe(l.modelValue.length-1):!0!==l.multiple&&null!==l.modelValue&&d("update:modelValue",null));(35===e.keyCode||36===e.keyCode)&&("string"!=typeof k.value||0===k.value.length)&&(ae(e),S.value=-1,Me(36===e.keyCode?1:-1,l.multiple)),(33===e.keyCode||34===e.keyCode)&&void 0!==N.value&&(ae(e),S.value=Math.max(-1,Math.min(P.value,S.value+(33===e.keyCode?-1:1)*N.value.view)),Me(33===e.keyCode?1:-1,l.multiple)),(38===e.keyCode||40===e.keyCode)&&(ae(e),Me(38===e.keyCode?-1:1,l.multiple));let o=P.value;if((void 0===h||b<Date.now())&&(h=""),o>0&&!0!==l.useInput&&void 0!==e.key&&1===e.key.length&&!1===e.altKey&&!1===e.ctrlKey&&!1===e.metaKey&&(32!==e.keyCode||0!==h.length)){!0!==x.value&&Ze(e);let t=e.key.toLocaleLowerCase(),a=1===h.length&&h[0]===t;b=Date.now()+1500,!1===a&&(ae(e),h+=t);let n=new RegExp("^"+h.split("").map((e=>-1!==".*+?^${}()|[]\\".indexOf(e)?"\\"+e:e)).join(".*"),"i"),i=S.value;if(!0===a||i<0||!0!==n.test(ge.value(l.options[i])))do{i=lt(i+1,-1,o-1)}while(i!==S.value&&(!0===he.value(l.options[i])||!0!==n.test(ge.value(l.options[i]))));S.value!==i&&m((()=>{$e(i),D(i),i>=0&&!0===l.useInput&&!0===l.fillInput&&Re(ge.value(l.options[i]),!0)}))}else if(13===e.keyCode||32===e.keyCode&&!0!==l.useInput&&""===h||9===e.keyCode&&!1!==a){if(9!==e.keyCode&&ae(e),-1!==S.value&&S.value<o)return void Ce(l.options[S.value]);if(!0===t){let e=(e,t)=>{if(t){if(!0!==bu(t))return}else t=l.newValueMode;Ne("",!0!==l.multiple,!0),null!=e&&(("toggle"===t?Ce:Se)(e,"add-unique"===t),!0!==l.multiple&&(null!==L.value&&L.value.focus(),Ge()))};if(void 0!==l.onNewValue?d("newValue",k.value,e):e(k.value),!0!==l.multiple)return}!0===x.value?Xe():!0!==U.innerLoading.value&&Ze()}}function Ee(){return!0===v?O.value:null!==z.value&&null!==z.value.contentEl?z.value.contentEl:void 0}function Pe(){if(!0===J.value)return void 0!==r["no-option"]?r["no-option"]({inputValue:k.value}):void 0;let e=void 0!==r.option?r.option:e=>t(Qi,{key:e.index,...e.itemProps},(()=>t(Ui,(()=>t(nr,(()=>t("span",{[!0===e.html?"innerHTML":"textContent"]:e.label}))))))),l=I("div",ce.value.map(e));return void 0!==r["before-options"]&&(l=r["before-options"]().concat(l)),mt(r["after-options"],l)}function Fe(e){null!==$&&(clearTimeout($),$=null),null!==M&&(clearTimeout(M),M=null),(!e||!e.target||!0!==e.target.qComposing)&&(Re(e.target.value||""),p=!0,f=k.value,!0!==U.focused.value&&(!0!==v||!0===q.value)&&U.focus(),void 0!==l.onFilter&&($=setTimeout((()=>{$=null,He(k.value)}),l.inputDebounce)))}function Re(e,t){k.value!==e&&(k.value=e,!0===t||0===l.inputDebounce||"0"===l.inputDebounce?d("inputValue",e):M=setTimeout((()=>{M=null,d("inputValue",e)}),l.inputDebounce))}function Ne(e,t,a){p=!0!==a,!0===l.useInput&&(Re(e,!0),(!0===t||!0!==a)&&(f=e),!0!==t&&He(e))}function He(e,t,a){if(void 0===l.onFilter||!0!==t&&!0!==U.focused.value)return;!0===U.innerLoading.value?d("filterAbort"):(U.innerLoading.value=!0,C.value=!0),""!==e&&!0!==l.multiple&&0!==W.value.length&&!0!==p&&e===ge.value(W.value[0])&&(e="");let o=setTimeout((()=>{!0===x.value&&(x.value=!1)}),10);null!==T&&clearTimeout(T),T=o,d("filter",e,((e,l)=>{(!0===t||!0===U.focused.value)&&T===o&&(clearTimeout(T),"function"==typeof e&&e(),C.value=!1,m((()=>{U.innerLoading.value=!1,!0===U.editable.value&&(!0===t?!0===x.value&&Ge():!0===x.value?et(!0):x.value=!0),"function"==typeof l&&m((()=>{l(y)})),"function"==typeof a&&m((()=>{a(y)}))})))}),(()=>{!0===U.focused.value&&T===o&&(clearTimeout(T),U.innerLoading.value=!1,C.value=!1),!0===x.value&&(x.value=!1)}))}function je(e){ot(e),Xe()}function De(){Q()}function Qe(e){te(e),null!==L.value&&L.value.focus(),q.value=!0,window.scrollTo(window.pageXOffset||window.scrollX||document.body.scrollLeft||0,0)}function Ue(e){te(e),m((()=>{q.value=!1}))}function We(e){ot(e),null!==V.value&&V.value.__updateRefocusTarget(U.rootRef.value.querySelector(".q-field__native > [tabindex]:last-child")),U.focused.value=!1}function Ke(e){Ge(),!1===U.focused.value&&d("blur",e),Je()}function Ye(){let e=document.activeElement;(null===e||e.id!==U.targetUid.value)&&null!==L.value&&L.value!==e&&L.value.focus(),Q()}function Xe(){!0!==_.value&&(S.value=-1,!0===x.value&&(x.value=!1),!1===U.focused.value&&(null!==T&&(clearTimeout(T),T=null),!0===U.innerLoading.value&&(d("filterAbort"),U.innerLoading.value=!1,C.value=!1)))}function Ze(e){!0===U.editable.value&&(!0===v?(U.onControlFocusin(e),_.value=!0,m((()=>{U.focus()}))):U.focus(),void 0!==l.onFilter?He(k.value):(!0!==J.value||void 0!==r["no-option"])&&(x.value=!0))}function Ge(){_.value=!1,Xe()}function Je(){!0===l.useInput&&Ne(!0!==l.multiple&&!0===l.fillInput&&0!==W.value.length&&ge.value(W.value[0])||"",!0,!0)}function et(e){let t=-1;if(!0===e){if(0!==W.value.length){let e=me.value(W.value[0]);t=l.options.findIndex((t=>Ie(me.value(t),e)))}H(t)}$e(t)}function tt(){!1===_.value&&null!==z.value&&z.value.updatePosition()}function at(e){void 0!==e&&te(e),d("popupShow",e),U.hasPopupOpen=!0,U.onControlFocusin(e)}function ot(e){void 0!==e&&te(e),d("popupHide",e),U.hasPopupOpen=!1,U.onControlFocusout(e)}function nt(){v=(!0===w.platform.is.mobile||"dialog"===l.behavior)&&("menu"!==l.behavior&&(!0!==l.useInput||(void 0!==r["no-option"]||void 0!==l.onFilter||!1===J.value))),g=!0===w.platform.is.ios&&!0===v&&!0===l.useInput?"fade":l.transitionShow}return n(W,(e=>{c=e,!0===l.useInput&&!0===l.fillInput&&!0!==l.multiple&&!0!==U.innerLoading.value&&(!0!==_.value&&!0!==x.value||!0!==X.value)&&(!0!==p&&Je(),(!0===_.value||!0===x.value)&&He(""))}),{immediate:!0}),n((()=>l.fillInput),Je),n(x,et),n(P,(function(e,t){!0===x.value&&!1===U.innerLoading.value&&(H(-1,!0),m((()=>{!0===x.value&&!1===U.innerLoading.value&&(e>t?H():et(!0))})))})),s(nt),u(tt),nt(),i((()=>{null!==$&&clearTimeout($),null!==M&&clearTimeout(M)})),Object.assign(y,{showPopup:Ze,hidePopup:Ge,removeAtIndex:xe,add:Se,toggleOption:Ce,getOptionIndex:()=>S.value,setOptionIndex:$e,moveOptionSelection:Me,filter:He,updateMenuPosition:tt,updateInputValue:Ne,isOptionSelected:Be,getEmittingOptionValue:we,isOptionDisabled:(...e)=>!0===he.value.apply(null,e),getOptionValue:(...e)=>me.value.apply(null,e),getOptionLabel:(...e)=>ge.value.apply(null,e)}),Object.assign(U,{innerValue:W,fieldClass:o((()=>`q-select q-field--auto-height q-select--with${!0!==l.useInput?"out":""}-input q-select--with${!0!==l.useChips?"out":""}-chips q-select--${!0===l.multiple?"multiple":"single"}`)),inputRef:B,targetRef:L,hasValue:X,showPopup:Ze,floatingLabel:o((()=>!0!==l.hideSelected&&!0===X.value||"number"==typeof k.value||0!==k.value.length||qr(l.displayValue))),getControlChild:()=>{if(!1!==U.editable.value&&(!0===_.value||!0!==J.value||void 0!==r["no-option"]))return!0===v?function(){let e=[t(Br,{class:`col-auto ${U.fieldClass.value}`,...K.value,for:U.targetUid.value,dark:Y.value,square:!0,loading:C.value,itemAligned:!1,filled:!0,stackLabel:0!==k.value.length,...U.splitAttrs.listeners.value,onFocus:Qe,onBlur:Ue},{...r,rawControl:()=>U.getControl(!0),before:void 0,after:void 0})];return!0===x.value&&e.push(t("div",{ref:O,class:G.value+" scroll",style:l.popupContentStyle,...ue.value,onClick:le,onScrollPassive:j},Pe())),t(Pi,{ref:V,modelValue:_.value,position:!0===l.useInput?"top":void 0,transitionShow:g,transitionHide:l.transitionHide,transitionDuration:l.transitionDuration,noRouteDismiss:l.popupNoRouteDismiss,onBeforeShow:at,onBeforeHide:We,onHide:Ke,onShow:Ye},(()=>t("div",{class:"q-select__dialog"+(!0===Y.value?" q-select__dialog--dark q-dark":"")+(!0===q.value?" q-select__dialog--focused":"")},e)))}():t(Ha,{ref:z,class:G.value,style:l.popupContentStyle,modelValue:x.value,fit:!0!==l.menuShrink,cover:!0===l.optionsCover&&!0!==J.value&&!0!==l.useInput,anchor:l.menuAnchor,self:l.menuSelf,offset:l.menuOffset,dark:Y.value,noParentEvent:!0,noRefocus:!0,noFocus:!0,noRouteDismiss:l.popupNoRouteDismiss,square:pe.value,transitionShow:l.transitionShow,transitionHide:l.transitionHide,transitionDuration:l.transitionDuration,separateClosePopup:!0,...ue.value,onScrollPassive:j,onBeforeShow:at,onBeforeHide:je,onShow:De},Pe);!0===U.hasPopupOpen&&(U.hasPopupOpen=!1)},controlEvents:{onFocusin(e){U.onControlFocusin(e)},onFocusout(e){U.onControlFocusout(e,(()=>{Je(),Xe()}))},onClick(e){if(le(e),!0!==v&&!0===x.value)return Xe(),void(null!==L.value&&L.value.focus());Ze(e)}},getControl:e=>{let a=!0===l.hideSelected?[]:void 0!==r["selected-item"]?de.value.map((e=>r["selected-item"](e))).slice():void 0!==r.selected?[].concat(r.selected()):!0===l.useChips?de.value.map(((e,a)=>t(Po,{key:"option-"+a,removable:!0===U.editable.value&&!0!==he.value(e.opt),dense:!0,textColor:l.color,tabindex:re.value,onRemove(){e.removeAtIndex(a)}},(()=>t("span",{class:"ellipsis",[!0===e.html?"innerHTML":"textContent"]:ge.value(e.opt)}))))):[t("span",{[!0===ie.value?"innerHTML":"textContent"]:oe.value})],o=!0===e||!0!==_.value||!0!==v;if(!0===l.useInput)a.push(function(e,a){let o=!0===a?{...se.value,...U.splitAttrs.attributes.value}:void 0,n={ref:!0===a?L:void 0,key:"i_t",class:Z.value,style:l.inputStyle,value:void 0!==k.value?k.value:"",type:"search",...o,id:!0===a?U.targetUid.value:void 0,maxlength:l.maxlength,autocomplete:l.autocomplete,"data-autofocus":!0===e||!0===l.autofocus||void 0,disabled:!0===l.disable,readonly:!0===l.readonly,...ye.value};return!0!==e&&!0===v&&(!0===Array.isArray(n.class)?n.class=[...n.class,"no-pointer-events"]:n.class+=" no-pointer-events"),t("input",n)}(e,o));else if(!0===U.editable.value){let n=!0===o?se.value:void 0;a.push(t("input",{ref:!0===o?L:void 0,key:"d_t",class:"q-select__focus-target",id:!0===o?U.targetUid.value:void 0,value:oe.value,readonly:!0,"data-autofocus":!0===e||!0===l.autofocus||void 0,...n,onKeydown:Ae,onKeyup:ze,onKeypress:Oe})),!0===o&&"string"==typeof l.autocomplete&&0!==l.autocomplete.length&&a.push(t("input",{class:"q-select__autocomplete-input",autocomplete:l.autocomplete,tabindex:-1,onKeyup:Ve}))}if(void 0!==A.value&&!0!==l.disable&&0!==be.value.length){let e=be.value.map((e=>t("option",{value:e,selected:!0})));a.push(t("select",{class:"hidden",name:A.value,multiple:l.multiple},e))}let n=!0===l.useInput||!0!==o?void 0:U.splitAttrs.attributes.value;return t("div",{class:"q-field__native row items-center",...n,...U.splitAttrs.listeners.value},a)},getInnerAppend:()=>!0!==l.loading&&!0!==C.value&&!0!==l.hideDropdownIcon?[t(zt,{class:"q-select__dropdown-icon"+(!0===x.value?" rotate-180":""),name:ve.value})]:null}),Tr(U)}}),xu=["text","rect","circle","QBtn","QBadge","QChip","QToolbar","QCheckbox","QRadio","QToggle","QSlider","QRange","QInput","QAvatar"],_u=["wave","pulse","pulse-x","pulse-y","fade","blink","none"],Su=Ze({name:"QSkeleton",props:{...Et,tag:{type:String,default:"div"},type:{type:String,validator:e=>xu.includes(e),default:"rect"},animation:{type:String,validator:e=>_u.includes(e),default:"wave"},animationSpeed:{type:[String,Number],default:1500},square:Boolean,bordered:Boolean,size:String,width:String,height:String},setup(e,{slots:l}){let n=a(),i=Pt(e,n.proxy.$q),r=o((()=>{let t=void 0!==e.size?[e.size,e.size]:[e.width,e.height];return{"--q-skeleton-speed":`${e.animationSpeed}ms`,width:t[0],height:t[1]}})),s=o((()=>`q-skeleton q-skeleton--${!0===i.value?"dark":"light"} q-skeleton--type-${e.type}`+("none"!==e.animation?` q-skeleton--anim q-skeleton--anim-${e.animation}`:"")+(!0===e.square?" q-skeleton--square":"")+(!0===e.bordered?" q-skeleton--bordered":"")));return()=>t(e.tag,{class:s.value,style:r.value},pt(l.default))}}),ku=[["left","center","start","width"],["right","center","end","width"],["top","start","center","height"],["bottom","end","center","height"]],qu=Ze({name:"QSlideItem",props:{...Et,leftColor:String,rightColor:String,topColor:String,bottomColor:String,onSlide:Function},emits:["action","top","right","bottom","left"],setup(l,{slots:n,emit:r}){let{proxy:u}=a(),{$q:d}=u,c=Pt(l,d),{getCache:v}=po(),p=e(null),f=null,m={},h={},b={},y=o((()=>!0===d.lang.rtl?{left:"right",right:"left"}:{left:"left",right:"right"})),w=o((()=>"q-slide-item q-item-type overflow-hidden"+(!0===c.value?" q-slide-item--dark q-dark":"")));function x(){p.value.style.transform="translate(0,0)"}function _(e,t,a){void 0!==l.onSlide&&r("slide",{side:e,ratio:t,isReset:a})}function S(e){let t,l,a,o=p.value;if(e.isFirst)m={dir:null,size:{left:0,right:0,top:0,bottom:0},scale:0},o.classList.add("no-transition"),ku.forEach((e=>{if(void 0!==n[e[0]]){let t=b[e[0]];t.style.transform="scale(1)",m.size[e[0]]=t.getBoundingClientRect()[e[3]]}})),m.axis="up"===e.direction||"down"===e.direction?"Y":"X";else{if(e.isFinal)return o.classList.remove("no-transition"),void(1===m.scale?(o.style.transform=`translate${m.axis}(${100*m.dir}%)`,null!==f&&clearTimeout(f),f=setTimeout((()=>{f=null,r(m.showing,{reset:x}),r("action",{side:m.showing,reset:x})}),230)):(o.style.transform="translate(0,0)",_(m.showing,0,!0)));e.direction="X"===m.axis?e.offset.x<0?"left":"right":e.offset.y<0?"up":"down"}void 0===n.left&&e.direction===y.value.right||void 0===n.right&&e.direction===y.value.left||void 0===n.top&&"down"===e.direction||void 0===n.bottom&&"up"===e.direction?o.style.transform="translate(0,0)":("X"===m.axis?(l="left"===e.direction?-1:1,t=1===l?y.value.left:y.value.right,a=e.distance.x):(l="up"===e.direction?-2:2,t=2===l?"top":"bottom",a=e.distance.y),(null===m.dir||Math.abs(l)===Math.abs(m.dir))&&(m.dir!==l&&(["left","right","top","bottom"].forEach((e=>{h[e]&&(h[e].style.visibility=t===e?"visible":"hidden")})),m.showing=t,m.dir=l),m.scale=Math.max(0,Math.min(1,(a-40)/m.size[t])),o.style.transform=`translate${m.axis}(${a*l/Math.abs(l)}px)`,b[t].style.transform=`scale(${m.scale})`,_(t,m.scale,!1)))}return s((()=>{h={},b={}})),i((()=>{null!==f&&clearTimeout(f)})),Object.assign(u,{reset:x}),()=>{let e=[],a={left:void 0!==n[y.value.right],right:void 0!==n[y.value.left],up:void 0!==n.bottom,down:void 0!==n.top},o=Object.keys(a).filter((e=>!0===a[e]));ku.forEach((a=>{let o=a[0];void 0!==n[o]&&e.push(t("div",{key:o,ref:e=>{h[o]=e},class:`q-slide-item__${o} absolute-full row no-wrap items-${a[1]} justify-${a[2]}`+(void 0!==l[o+"Color"]?` bg-${l[o+"Color"]}`:"")},[t("div",{ref:e=>{b[o]=e}},n[o]())]))}));let i=t("div",{key:(0===o.length?"only-":"")+" content",ref:p,class:"q-slide-item__content"},pt(n.default));return 0===o.length?e.push(i):e.push(g(i,v("dir#"+o.join(""),(()=>{let e={prevent:!0,stop:!0,mouse:!0};return o.forEach((t=>{e[t]=!0})),[[Do,S,void 0,e]]})))),t("div",{class:w.value},e)}}}),Cu=t("div",{class:"q-space"}),$u=Ze({name:"QSpace",setup:()=>()=>Cu}),Mu=[t("g",{transform:"matrix(1 0 0 -1 0 80)"},[t("rect",{width:"10",height:"20",rx:"3"},[t("animate",{attributeName:"height",begin:"0s",dur:"4.3s",values:"20;45;57;80;64;32;66;45;64;23;66;13;64;56;34;34;2;23;76;79;20",calcMode:"linear",repeatCount:"indefinite"})]),t("rect",{x:"15",width:"10",height:"80",rx:"3"},[t("animate",{attributeName:"height",begin:"0s",dur:"2s",values:"80;55;33;5;75;23;73;33;12;14;60;80",calcMode:"linear",repeatCount:"indefinite"})]),t("rect",{x:"30",width:"10",height:"50",rx:"3"},[t("animate",{attributeName:"height",begin:"0s",dur:"1.4s",values:"50;34;78;23;56;23;34;76;80;54;21;50",calcMode:"linear",repeatCount:"indefinite"})]),t("rect",{x:"45",width:"10",height:"30",rx:"3"},[t("animate",{attributeName:"height",begin:"0s",dur:"2s",values:"30;45;13;80;56;72;45;76;34;23;67;30",calcMode:"linear",repeatCount:"indefinite"})])])],Tu=Ze({name:"QSpinnerAudio",props:ol,setup(e){let{cSize:l,classes:a}=nl(e);return()=>t("svg",{class:a.value,fill:"currentColor",width:l.value,height:l.value,viewBox:"0 0 55 80",xmlns:"http://www.w3.org/2000/svg"},Mu)}}),Bu=[t("g",{transform:"translate(1 1)","stroke-width":"2",fill:"none","fill-rule":"evenodd"},[t("circle",{cx:"5",cy:"50",r:"5"},[t("animate",{attributeName:"cy",begin:"0s",dur:"2.2s",values:"50;5;50;50",calcMode:"linear",repeatCount:"indefinite"}),t("animate",{attributeName:"cx",begin:"0s",dur:"2.2s",values:"5;27;49;5",calcMode:"linear",repeatCount:"indefinite"})]),t("circle",{cx:"27",cy:"5",r:"5"},[t("animate",{attributeName:"cy",begin:"0s",dur:"2.2s",from:"5",to:"5",values:"5;50;50;5",calcMode:"linear",repeatCount:"indefinite"}),t("animate",{attributeName:"cx",begin:"0s",dur:"2.2s",from:"27",to:"27",values:"27;49;5;27",calcMode:"linear",repeatCount:"indefinite"})]),t("circle",{cx:"49",cy:"50",r:"5"},[t("animate",{attributeName:"cy",begin:"0s",dur:"2.2s",values:"50;50;5;50",calcMode:"linear",repeatCount:"indefinite"}),t("animate",{attributeName:"cx",from:"49",to:"49",begin:"0s",dur:"2.2s",values:"49;5;27;49",calcMode:"linear",repeatCount:"indefinite"})])])],Lu=Ze({name:"QSpinnerBall",props:ol,setup(e){let{cSize:l,classes:a}=nl(e);return()=>t("svg",{class:a.value,stroke:"currentColor",width:l.value,height:l.value,viewBox:"0 0 57 57",xmlns:"http://www.w3.org/2000/svg"},Bu)}}),zu=[t("rect",{y:"10",width:"15",height:"120",rx:"6"},[t("animate",{attributeName:"height",begin:"0.5s",dur:"1s",values:"120;110;100;90;80;70;60;50;40;140;120",calcMode:"linear",repeatCount:"indefinite"}),t("animate",{attributeName:"y",begin:"0.5s",dur:"1s",values:"10;15;20;25;30;35;40;45;50;0;10",calcMode:"linear",repeatCount:"indefinite"})]),t("rect",{x:"30",y:"10",width:"15",height:"120",rx:"6"},[t("animate",{attributeName:"height",begin:"0.25s",dur:"1s",values:"120;110;100;90;80;70;60;50;40;140;120",calcMode:"linear",repeatCount:"indefinite"}),t("animate",{attributeName:"y",begin:"0.25s",dur:"1s",values:"10;15;20;25;30;35;40;45;50;0;10",calcMode:"linear",repeatCount:"indefinite"})]),t("rect",{x:"60",width:"15",height:"140",rx:"6"},[t("animate",{attributeName:"height",begin:"0s",dur:"1s",values:"120;110;100;90;80;70;60;50;40;140;120",calcMode:"linear",repeatCount:"indefinite"}),t("animate",{attributeName:"y",begin:"0s",dur:"1s",values:"10;15;20;25;30;35;40;45;50;0;10",calcMode:"linear",repeatCount:"indefinite"})]),t("rect",{x:"90",y:"10",width:"15",height:"120",rx:"6"},[t("animate",{attributeName:"height",begin:"0.25s",dur:"1s",values:"120;110;100;90;80;70;60;50;40;140;120",calcMode:"linear",repeatCount:"indefinite"}),t("animate",{attributeName:"y",begin:"0.25s",dur:"1s",values:"10;15;20;25;30;35;40;45;50;0;10",calcMode:"linear",repeatCount:"indefinite"})]),t("rect",{x:"120",y:"10",width:"15",height:"120",rx:"6"},[t("animate",{attributeName:"height",begin:"0.5s",dur:"1s",values:"120;110;100;90;80;70;60;50;40;140;120",calcMode:"linear",repeatCount:"indefinite"}),t("animate",{attributeName:"y",begin:"0.5s",dur:"1s",values:"10;15;20;25;30;35;40;45;50;0;10",calcMode:"linear",repeatCount:"indefinite"})])],Vu=Ze({name:"QSpinnerBars",props:ol,setup(e){let{cSize:l,classes:a}=nl(e);return()=>t("svg",{class:a.value,fill:"currentColor",width:l.value,height:l.value,viewBox:"0 0 135 140",xmlns:"http://www.w3.org/2000/svg"},zu)}}),Ou=[t("rect",{x:"25",y:"25",width:"50",height:"50",fill:"none","stroke-width":"4",stroke:"currentColor"},[t("animateTransform",{id:"spinnerBox",attributeName:"transform",type:"rotate",from:"0 50 50",to:"180 50 50",dur:"0.5s",begin:"rectBox.end"})]),t("rect",{x:"27",y:"27",width:"46",height:"50",fill:"currentColor"},[t("animate",{id:"rectBox",attributeName:"height",begin:"0s;spinnerBox.end",dur:"1.3s",from:"50",to:"0",fill:"freeze"})])],Au=Ze({name:"QSpinnerBox",props:ol,setup(e){let{cSize:l,classes:a}=nl(e);return()=>t("svg",{class:a.value,width:l.value,height:l.value,viewBox:"0 0 100 100",preserveAspectRatio:"xMidYMid",xmlns:"http://www.w3.org/2000/svg"},Ou)}}),Eu=[t("circle",{cx:"50",cy:"50",r:"48",fill:"none","stroke-width":"4","stroke-miterlimit":"10",stroke:"currentColor"}),t("line",{"stroke-linecap":"round","stroke-width":"4","stroke-miterlimit":"10",stroke:"currentColor",x1:"50",y1:"50",x2:"85",y2:"50.5"},[t("animateTransform",{attributeName:"transform",type:"rotate",from:"0 50 50",to:"360 50 50",dur:"2s",repeatCount:"indefinite"})]),t("line",{"stroke-linecap":"round","stroke-width":"4","stroke-miterlimit":"10",stroke:"currentColor",x1:"50",y1:"50",x2:"49.5",y2:"74"},[t("animateTransform",{attributeName:"transform",type:"rotate",from:"0 50 50",to:"360 50 50",dur:"15s",repeatCount:"indefinite"})])],Pu=Ze({name:"QSpinnerClock",props:ol,setup(e){let{cSize:l,classes:a}=nl(e);return()=>t("svg",{class:a.value,width:l.value,height:l.value,viewBox:"0 0 100 100",preserveAspectRatio:"xMidYMid",xmlns:"http://www.w3.org/2000/svg"},Eu)}}),Fu=[t("rect",{x:"0",y:"0",width:"100",height:"100",fill:"none"}),t("path",{d:"M78,19H22c-6.6,0-12,5.4-12,12v31c0,6.6,5.4,12,12,12h37.2c0.4,3,1.8,5.6,3.7,7.6c2.4,2.5,5.1,4.1,9.1,4 c-1.4-2.1-2-7.2-2-10.3c0-0.4,0-0.8,0-1.3h8c6.6,0,12-5.4,12-12V31C90,24.4,84.6,19,78,19z",fill:"currentColor"}),t("circle",{cx:"30",cy:"47",r:"5",fill:"#fff"},[t("animate",{attributeName:"opacity",from:"0",to:"1",values:"0;1;1",keyTimes:"0;0.2;1",dur:"1s",repeatCount:"indefinite"})]),t("circle",{cx:"50",cy:"47",r:"5",fill:"#fff"},[t("animate",{attributeName:"opacity",from:"0",to:"1",values:"0;0;1;1",keyTimes:"0;0.2;0.4;1",dur:"1s",repeatCount:"indefinite"})]),t("circle",{cx:"70",cy:"47",r:"5",fill:"#fff"},[t("animate",{attributeName:"opacity",from:"0",to:"1",values:"0;0;1;1",keyTimes:"0;0.4;0.6;1",dur:"1s",repeatCount:"indefinite"})])],Ru=Ze({name:"QSpinnerComment",props:ol,setup(e){let{cSize:l,classes:a}=nl(e);return()=>t("svg",{class:a.value,width:l.value,height:l.value,xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 100 100",preserveAspectRatio:"xMidYMid"},Fu)}}),Nu=[t("rect",{x:"0",y:"0",width:"100",height:"100",fill:"none"}),t("g",{transform:"translate(25 25)"},[t("rect",{x:"-20",y:"-20",width:"40",height:"40",fill:"currentColor",opacity:"0.9"},[t("animateTransform",{attributeName:"transform",type:"scale",from:"1.5",to:"1",repeatCount:"indefinite",begin:"0s",dur:"1s",calcMode:"spline",keySplines:"0.2 0.8 0.2 0.8",keyTimes:"0;1"})])]),t("g",{transform:"translate(75 25)"},[t("rect",{x:"-20",y:"-20",width:"40",height:"40",fill:"currentColor",opacity:"0.8"},[t("animateTransform",{attributeName:"transform",type:"scale",from:"1.5",to:"1",repeatCount:"indefinite",begin:"0.1s",dur:"1s",calcMode:"spline",keySplines:"0.2 0.8 0.2 0.8",keyTimes:"0;1"})])]),t("g",{transform:"translate(25 75)"},[t("rect",{x:"-20",y:"-20",width:"40",height:"40",fill:"currentColor",opacity:"0.7"},[t("animateTransform",{attributeName:"transform",type:"scale",from:"1.5",to:"1",repeatCount:"indefinite",begin:"0.3s",dur:"1s",calcMode:"spline",keySplines:"0.2 0.8 0.2 0.8",keyTimes:"0;1"})])]),t("g",{transform:"translate(75 75)"},[t("rect",{x:"-20",y:"-20",width:"40",height:"40",fill:"currentColor",opacity:"0.6"},[t("animateTransform",{attributeName:"transform",type:"scale",from:"1.5",to:"1",repeatCount:"indefinite",begin:"0.2s",dur:"1s",calcMode:"spline",keySplines:"0.2 0.8 0.2 0.8",keyTimes:"0;1"})])])],Hu=Ze({name:"QSpinnerCube",props:ol,setup(e){let{cSize:l,classes:a}=nl(e);return()=>t("svg",{class:a.value,width:l.value,height:l.value,xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 100 100",preserveAspectRatio:"xMidYMid"},Nu)}}),Iu=[t("circle",{cx:"15",cy:"15",r:"15"},[t("animate",{attributeName:"r",from:"15",to:"15",begin:"0s",dur:"0.8s",values:"15;9;15",calcMode:"linear",repeatCount:"indefinite"}),t("animate",{attributeName:"fill-opacity",from:"1",to:"1",begin:"0s",dur:"0.8s",values:"1;.5;1",calcMode:"linear",repeatCount:"indefinite"})]),t("circle",{cx:"60",cy:"15",r:"9","fill-opacity":".3"},[t("animate",{attributeName:"r",from:"9",to:"9",begin:"0s",dur:"0.8s",values:"9;15;9",calcMode:"linear",repeatCount:"indefinite"}),t("animate",{attributeName:"fill-opacity",from:".5",to:".5",begin:"0s",dur:"0.8s",values:".5;1;.5",calcMode:"linear",repeatCount:"indefinite"})]),t("circle",{cx:"105",cy:"15",r:"15"},[t("animate",{attributeName:"r",from:"15",to:"15",begin:"0s",dur:"0.8s",values:"15;9;15",calcMode:"linear",repeatCount:"indefinite"}),t("animate",{attributeName:"fill-opacity",from:"1",to:"1",begin:"0s",dur:"0.8s",values:"1;.5;1",calcMode:"linear",repeatCount:"indefinite"})])],ju=Ze({name:"QSpinnerDots",props:ol,setup(e){let{cSize:l,classes:a}=nl(e);return()=>t("svg",{class:a.value,fill:"currentColor",width:l.value,height:l.value,viewBox:"0 0 120 30",xmlns:"http://www.w3.org/2000/svg"},Iu)}}),Du=[t("g",{transform:"translate(20 50)"},[t("rect",{x:"-10",y:"-30",width:"20",height:"60",fill:"currentColor",opacity:"0.6"},[t("animateTransform",{attributeName:"transform",type:"scale",from:"2",to:"1",begin:"0s",repeatCount:"indefinite",dur:"1s",calcMode:"spline",keySplines:"0.1 0.9 0.4 1",keyTimes:"0;1",values:"2;1"})])]),t("g",{transform:"translate(50 50)"},[t("rect",{x:"-10",y:"-30",width:"20",height:"60",fill:"currentColor",opacity:"0.8"},[t("animateTransform",{attributeName:"transform",type:"scale",from:"2",to:"1",begin:"0.1s",repeatCount:"indefinite",dur:"1s",calcMode:"spline",keySplines:"0.1 0.9 0.4 1",keyTimes:"0;1",values:"2;1"})])]),t("g",{transform:"translate(80 50)"},[t("rect",{x:"-10",y:"-30",width:"20",height:"60",fill:"currentColor",opacity:"0.9"},[t("animateTransform",{attributeName:"transform",type:"scale",from:"2",to:"1",begin:"0.2s",repeatCount:"indefinite",dur:"1s",calcMode:"spline",keySplines:"0.1 0.9 0.4 1",keyTimes:"0;1",values:"2;1"})])])],Qu=Ze({name:"QSpinnerFacebook",props:ol,setup(e){let{cSize:l,classes:a}=nl(e);return()=>t("svg",{class:a.value,width:l.value,height:l.value,viewBox:"0 0 100 100",xmlns:"http://www.w3.org/2000/svg",preserveAspectRatio:"xMidYMid"},Du)}}),Uu=[t("g",{transform:"translate(-20,-20)"},[t("path",{d:"M79.9,52.6C80,51.8,80,50.9,80,50s0-1.8-0.1-2.6l-5.1-0.4c-0.3-2.4-0.9-4.6-1.8-6.7l4.2-2.9c-0.7-1.6-1.6-3.1-2.6-4.5 L70,35c-1.4-1.9-3.1-3.5-4.9-4.9l2.2-4.6c-1.4-1-2.9-1.9-4.5-2.6L59.8,27c-2.1-0.9-4.4-1.5-6.7-1.8l-0.4-5.1C51.8,20,50.9,20,50,20 s-1.8,0-2.6,0.1l-0.4,5.1c-2.4,0.3-4.6,0.9-6.7,1.8l-2.9-4.1c-1.6,0.7-3.1,1.6-4.5,2.6l2.1,4.6c-1.9,1.4-3.5,3.1-5,4.9l-4.5-2.1 c-1,1.4-1.9,2.9-2.6,4.5l4.1,2.9c-0.9,2.1-1.5,4.4-1.8,6.8l-5,0.4C20,48.2,20,49.1,20,50s0,1.8,0.1,2.6l5,0.4 c0.3,2.4,0.9,4.7,1.8,6.8l-4.1,2.9c0.7,1.6,1.6,3.1,2.6,4.5l4.5-2.1c1.4,1.9,3.1,3.5,5,4.9l-2.1,4.6c1.4,1,2.9,1.9,4.5,2.6l2.9-4.1 c2.1,0.9,4.4,1.5,6.7,1.8l0.4,5.1C48.2,80,49.1,80,50,80s1.8,0,2.6-0.1l0.4-5.1c2.3-0.3,4.6-0.9,6.7-1.8l2.9,4.2 c1.6-0.7,3.1-1.6,4.5-2.6L65,69.9c1.9-1.4,3.5-3,4.9-4.9l4.6,2.2c1-1.4,1.9-2.9,2.6-4.5L73,59.8c0.9-2.1,1.5-4.4,1.8-6.7L79.9,52.6 z M50,65c-8.3,0-15-6.7-15-15c0-8.3,6.7-15,15-15s15,6.7,15,15C65,58.3,58.3,65,50,65z",fill:"currentColor"},[t("animateTransform",{attributeName:"transform",type:"rotate",from:"90 50 50",to:"0 50 50",dur:"1s",repeatCount:"indefinite"})])]),t("g",{transform:"translate(20,20) rotate(15 50 50)"},[t("path",{d:"M79.9,52.6C80,51.8,80,50.9,80,50s0-1.8-0.1-2.6l-5.1-0.4c-0.3-2.4-0.9-4.6-1.8-6.7l4.2-2.9c-0.7-1.6-1.6-3.1-2.6-4.5 L70,35c-1.4-1.9-3.1-3.5-4.9-4.9l2.2-4.6c-1.4-1-2.9-1.9-4.5-2.6L59.8,27c-2.1-0.9-4.4-1.5-6.7-1.8l-0.4-5.1C51.8,20,50.9,20,50,20 s-1.8,0-2.6,0.1l-0.4,5.1c-2.4,0.3-4.6,0.9-6.7,1.8l-2.9-4.1c-1.6,0.7-3.1,1.6-4.5,2.6l2.1,4.6c-1.9,1.4-3.5,3.1-5,4.9l-4.5-2.1 c-1,1.4-1.9,2.9-2.6,4.5l4.1,2.9c-0.9,2.1-1.5,4.4-1.8,6.8l-5,0.4C20,48.2,20,49.1,20,50s0,1.8,0.1,2.6l5,0.4 c0.3,2.4,0.9,4.7,1.8,6.8l-4.1,2.9c0.7,1.6,1.6,3.1,2.6,4.5l4.5-2.1c1.4,1.9,3.1,3.5,5,4.9l-2.1,4.6c1.4,1,2.9,1.9,4.5,2.6l2.9-4.1 c2.1,0.9,4.4,1.5,6.7,1.8l0.4,5.1C48.2,80,49.1,80,50,80s1.8,0,2.6-0.1l0.4-5.1c2.3-0.3,4.6-0.9,6.7-1.8l2.9,4.2 c1.6-0.7,3.1-1.6,4.5-2.6L65,69.9c1.9-1.4,3.5-3,4.9-4.9l4.6,2.2c1-1.4,1.9-2.9,2.6-4.5L73,59.8c0.9-2.1,1.5-4.4,1.8-6.7L79.9,52.6 z M50,65c-8.3,0-15-6.7-15-15c0-8.3,6.7-15,15-15s15,6.7,15,15C65,58.3,58.3,65,50,65z",fill:"currentColor"},[t("animateTransform",{attributeName:"transform",type:"rotate",from:"0 50 50",to:"90 50 50",dur:"1s",repeatCount:"indefinite"})])])],Wu=Ze({name:"QSpinnerGears",props:ol,setup(e){let{cSize:l,classes:a}=nl(e);return()=>t("svg",{class:a.value,width:l.value,height:l.value,viewBox:"0 0 100 100",preserveAspectRatio:"xMidYMid",xmlns:"http://www.w3.org/2000/svg"},Uu)}}),Ku=[t("circle",{cx:"12.5",cy:"12.5",r:"12.5"},[t("animate",{attributeName:"fill-opacity",begin:"0s",dur:"1s",values:"1;.2;1",calcMode:"linear",repeatCount:"indefinite"})]),t("circle",{cx:"12.5",cy:"52.5",r:"12.5","fill-opacity":".5"},[t("animate",{attributeName:"fill-opacity",begin:"100ms",dur:"1s",values:"1;.2;1",calcMode:"linear",repeatCount:"indefinite"})]),t("circle",{cx:"52.5",cy:"12.5",r:"12.5"},[t("animate",{attributeName:"fill-opacity",begin:"300ms",dur:"1s",values:"1;.2;1",calcMode:"linear",repeatCount:"indefinite"})]),t("circle",{cx:"52.5",cy:"52.5",r:"12.5"},[t("animate",{attributeName:"fill-opacity",begin:"600ms",dur:"1s",values:"1;.2;1",calcMode:"linear",repeatCount:"indefinite"})]),t("circle",{cx:"92.5",cy:"12.5",r:"12.5"},[t("animate",{attributeName:"fill-opacity",begin:"800ms",dur:"1s",values:"1;.2;1",calcMode:"linear",repeatCount:"indefinite"})]),t("circle",{cx:"92.5",cy:"52.5",r:"12.5"},[t("animate",{attributeName:"fill-opacity",begin:"400ms",dur:"1s",values:"1;.2;1",calcMode:"linear",repeatCount:"indefinite"})]),t("circle",{cx:"12.5",cy:"92.5",r:"12.5"},[t("animate",{attributeName:"fill-opacity",begin:"700ms",dur:"1s",values:"1;.2;1",calcMode:"linear",repeatCount:"indefinite"})]),t("circle",{cx:"52.5",cy:"92.5",r:"12.5"},[t("animate",{attributeName:"fill-opacity",begin:"500ms",dur:"1s",values:"1;.2;1",calcMode:"linear",repeatCount:"indefinite"})]),t("circle",{cx:"92.5",cy:"92.5",r:"12.5"},[t("animate",{attributeName:"fill-opacity",begin:"200ms",dur:"1s",values:"1;.2;1",calcMode:"linear",repeatCount:"indefinite"})])],Yu=Ze({name:"QSpinnerGrid",props:ol,setup(e){let{cSize:l,classes:a}=nl(e);return()=>t("svg",{class:a.value,fill:"currentColor",width:l.value,height:l.value,viewBox:"0 0 105 105",xmlns:"http://www.w3.org/2000/svg"},Ku)}}),Xu=[t("path",{d:"M30.262 57.02L7.195 40.723c-5.84-3.976-7.56-12.06-3.842-18.063 3.715-6 11.467-7.65 17.306-3.68l4.52 3.76 2.6-5.274c3.716-6.002 11.47-7.65 17.304-3.68 5.84 3.97 7.56 12.054 3.842 18.062L34.49 56.118c-.897 1.512-2.793 1.915-4.228.9z","fill-opacity":".5"},[t("animate",{attributeName:"fill-opacity",begin:"0s",dur:"1.4s",values:"0.5;1;0.5",calcMode:"linear",repeatCount:"indefinite"})]),t("path",{d:"M105.512 56.12l-14.44-24.272c-3.716-6.008-1.996-14.093 3.843-18.062 5.835-3.97 13.588-2.322 17.306 3.68l2.6 5.274 4.52-3.76c5.84-3.97 13.593-2.32 17.308 3.68 3.718 6.003 1.998 14.088-3.842 18.064L109.74 57.02c-1.434 1.014-3.33.61-4.228-.9z","fill-opacity":".5"},[t("animate",{attributeName:"fill-opacity",begin:"0.7s",dur:"1.4s",values:"0.5;1;0.5",calcMode:"linear",repeatCount:"indefinite"})]),t("path",{d:"M67.408 57.834l-23.01-24.98c-5.864-6.15-5.864-16.108 0-22.248 5.86-6.14 15.37-6.14 21.234 0L70 16.168l4.368-5.562c5.863-6.14 15.375-6.14 21.235 0 5.863 6.14 5.863 16.098 0 22.247l-23.007 24.98c-1.43 1.556-3.757 1.556-5.188 0z"})],Zu=Ze({name:"QSpinnerHearts",props:ol,setup(e){let{cSize:l,classes:a}=nl(e);return()=>t("svg",{class:a.value,fill:"currentColor",width:l.value,height:l.value,viewBox:"0 0 140 64",xmlns:"http://www.w3.org/2000/svg"},Xu)}}),Gu=[t("g",[t("path",{fill:"none",stroke:"currentColor","stroke-width":"5","stroke-miterlimit":"10",d:"M58.4,51.7c-0.9-0.9-1.4-2-1.4-2.3s0.5-0.4,1.4-1.4 C70.8,43.8,79.8,30.5,80,15.5H70H30H20c0.2,15,9.2,28.1,21.6,32.3c0.9,0.9,1.4,1.2,1.4,1.5s-0.5,1.6-1.4,2.5 C29.2,56.1,20.2,69.5,20,85.5h10h40h10C79.8,69.5,70.8,55.9,58.4,51.7z"}),t("clipPath",{id:"uil-hourglass-clip1"},[t("rect",{x:"15",y:"20",width:"70",height:"25"},[t("animate",{attributeName:"height",from:"25",to:"0",dur:"1s",repeatCount:"indefinite",values:"25;0;0",keyTimes:"0;0.5;1"}),t("animate",{attributeName:"y",from:"20",to:"45",dur:"1s",repeatCount:"indefinite",values:"20;45;45",keyTimes:"0;0.5;1"})])]),t("clipPath",{id:"uil-hourglass-clip2"},[t("rect",{x:"15",y:"55",width:"70",height:"25"},[t("animate",{attributeName:"height",from:"0",to:"25",dur:"1s",repeatCount:"indefinite",values:"0;25;25",keyTimes:"0;0.5;1"}),t("animate",{attributeName:"y",from:"80",to:"55",dur:"1s",repeatCount:"indefinite",values:"80;55;55",keyTimes:"0;0.5;1"})])]),t("path",{d:"M29,23c3.1,11.4,11.3,19.5,21,19.5S67.9,34.4,71,23H29z","clip-path":"url(#uil-hourglass-clip1)",fill:"currentColor"}),t("path",{d:"M71.6,78c-3-11.6-11.5-20-21.5-20s-18.5,8.4-21.5,20H71.6z","clip-path":"url(#uil-hourglass-clip2)",fill:"currentColor"}),t("animateTransform",{attributeName:"transform",type:"rotate",from:"0 50 50",to:"180 50 50",repeatCount:"indefinite",dur:"1s",values:"0 50 50;0 50 50;180 50 50",keyTimes:"0;0.7;1"})])],Ju=Ze({name:"QSpinnerHourglass",props:ol,setup(e){let{cSize:l,classes:a}=nl(e);return()=>t("svg",{class:a.value,width:l.value,height:l.value,viewBox:"0 0 100 100",preserveAspectRatio:"xMidYMid",xmlns:"http://www.w3.org/2000/svg"},Gu)}}),ed=[t("path",{d:"M24.3,30C11.4,30,5,43.3,5,50s6.4,20,19.3,20c19.3,0,32.1-40,51.4-40C88.6,30,95,43.3,95,50s-6.4,20-19.3,20C56.4,70,43.6,30,24.3,30z",fill:"none",stroke:"currentColor","stroke-width":"8","stroke-dasharray":"10.691205342610678 10.691205342610678","stroke-dashoffset":"0"},[t("animate",{attributeName:"stroke-dashoffset",from:"0",to:"21.382410685221355",begin:"0",dur:"2s",repeatCount:"indefinite",fill:"freeze"})])],td=Ze({name:"QSpinnerInfinity",props:ol,setup(e){let{cSize:l,classes:a}=nl(e);return()=>t("svg",{class:a.value,width:l.value,height:l.value,viewBox:"0 0 100 100",preserveAspectRatio:"xMidYMid"},ed)}}),ld=[t("g",{"stroke-width":"4","stroke-linecap":"round"},[t("line",{y1:"17",y2:"29",transform:"translate(32,32) rotate(180)"},[t("animate",{attributeName:"stroke-opacity",dur:"750ms",values:"1;.85;.7;.65;.55;.45;.35;.25;.15;.1;0;1",repeatCount:"indefinite"})]),t("line",{y1:"17",y2:"29",transform:"translate(32,32) rotate(210)"},[t("animate",{attributeName:"stroke-opacity",dur:"750ms",values:"0;1;.85;.7;.65;.55;.45;.35;.25;.15;.1;0",repeatCount:"indefinite"})]),t("line",{y1:"17",y2:"29",transform:"translate(32,32) rotate(240)"},[t("animate",{attributeName:"stroke-opacity",dur:"750ms",values:".1;0;1;.85;.7;.65;.55;.45;.35;.25;.15;.1",repeatCount:"indefinite"})]),t("line",{y1:"17",y2:"29",transform:"translate(32,32) rotate(270)"},[t("animate",{attributeName:"stroke-opacity",dur:"750ms",values:".15;.1;0;1;.85;.7;.65;.55;.45;.35;.25;.15",repeatCount:"indefinite"})]),t("line",{y1:"17",y2:"29",transform:"translate(32,32) rotate(300)"},[t("animate",{attributeName:"stroke-opacity",dur:"750ms",values:".25;.15;.1;0;1;.85;.7;.65;.55;.45;.35;.25",repeatCount:"indefinite"})]),t("line",{y1:"17",y2:"29",transform:"translate(32,32) rotate(330)"},[t("animate",{attributeName:"stroke-opacity",dur:"750ms",values:".35;.25;.15;.1;0;1;.85;.7;.65;.55;.45;.35",repeatCount:"indefinite"})]),t("line",{y1:"17",y2:"29",transform:"translate(32,32) rotate(0)"},[t("animate",{attributeName:"stroke-opacity",dur:"750ms",values:".45;.35;.25;.15;.1;0;1;.85;.7;.65;.55;.45",repeatCount:"indefinite"})]),t("line",{y1:"17",y2:"29",transform:"translate(32,32) rotate(30)"},[t("animate",{attributeName:"stroke-opacity",dur:"750ms",values:".55;.45;.35;.25;.15;.1;0;1;.85;.7;.65;.55",repeatCount:"indefinite"})]),t("line",{y1:"17",y2:"29",transform:"translate(32,32) rotate(60)"},[t("animate",{attributeName:"stroke-opacity",dur:"750ms",values:".65;.55;.45;.35;.25;.15;.1;0;1;.85;.7;.65",repeatCount:"indefinite"})]),t("line",{y1:"17",y2:"29",transform:"translate(32,32) rotate(90)"},[t("animate",{attributeName:"stroke-opacity",dur:"750ms",values:".7;.65;.55;.45;.35;.25;.15;.1;0;1;.85;.7",repeatCount:"indefinite"})]),t("line",{y1:"17",y2:"29",transform:"translate(32,32) rotate(120)"},[t("animate",{attributeName:"stroke-opacity",dur:"750ms",values:".85;.7;.65;.55;.45;.35;.25;.15;.1;0;1;.85",repeatCount:"indefinite"})]),t("line",{y1:"17",y2:"29",transform:"translate(32,32) rotate(150)"},[t("animate",{attributeName:"stroke-opacity",dur:"750ms",values:"1;.85;.7;.65;.55;.45;.35;.25;.15;.1;0;1",repeatCount:"indefinite"})])])],ad=Ze({name:"QSpinnerIos",props:ol,setup(e){let{cSize:l,classes:a}=nl(e);return()=>t("svg",{class:a.value,width:l.value,height:l.value,stroke:"currentColor",fill:"currentColor",viewBox:"0 0 64 64"},ld)}}),od=[t("circle",{cx:"50",cy:"50",r:"44",fill:"none","stroke-width":"4","stroke-opacity":".5",stroke:"currentColor"}),t("circle",{cx:"8",cy:"54",r:"6",fill:"currentColor","stroke-width":"3",stroke:"currentColor"},[t("animateTransform",{attributeName:"transform",type:"rotate",from:"0 50 48",to:"360 50 52",dur:"2s",repeatCount:"indefinite"})])],nd=Ze({name:"QSpinnerOrbit",props:ol,setup(e){let{cSize:l,classes:a}=nl(e);return()=>t("svg",{class:a.value,width:l.value,height:l.value,viewBox:"0 0 100 100",preserveAspectRatio:"xMidYMid",xmlns:"http://www.w3.org/2000/svg"},od)}}),id=[t("g",{transform:"translate(1 1)","stroke-width":"2",fill:"none","fill-rule":"evenodd"},[t("circle",{"stroke-opacity":".5",cx:"18",cy:"18",r:"18"}),t("path",{d:"M36 18c0-9.94-8.06-18-18-18"},[t("animateTransform",{attributeName:"transform",type:"rotate",from:"0 18 18",to:"360 18 18",dur:"1s",repeatCount:"indefinite"})])])],rd=Ze({name:"QSpinnerOval",props:ol,setup(e){let{cSize:l,classes:a}=nl(e);return()=>t("svg",{class:a.value,stroke:"currentColor",width:l.value,height:l.value,viewBox:"0 0 38 38",xmlns:"http://www.w3.org/2000/svg"},id)}}),sd=[t("path",{d:"M0 50A50 50 0 0 1 50 0L50 50L0 50",fill:"currentColor",opacity:"0.5"},[t("animateTransform",{attributeName:"transform",type:"rotate",from:"0 50 50",to:"360 50 50",dur:"0.8s",repeatCount:"indefinite"})]),t("path",{d:"M50 0A50 50 0 0 1 100 50L50 50L50 0",fill:"currentColor",opacity:"0.5"},[t("animateTransform",{attributeName:"transform",type:"rotate",from:"0 50 50",to:"360 50 50",dur:"1.6s",repeatCount:"indefinite"})]),t("path",{d:"M100 50A50 50 0 0 1 50 100L50 50L100 50",fill:"currentColor",opacity:"0.5"},[t("animateTransform",{attributeName:"transform",type:"rotate",from:"0 50 50",to:"360 50 50",dur:"2.4s",repeatCount:"indefinite"})]),t("path",{d:"M50 100A50 50 0 0 1 0 50L50 50L50 100",fill:"currentColor",opacity:"0.5"},[t("animateTransform",{attributeName:"transform",type:"rotate",from:"0 50 50",to:"360 50 50",dur:"3.2s",repeatCount:"indefinite"})])],ud=Ze({name:"QSpinnerPie",props:ol,setup(e){let{cSize:l,classes:a}=nl(e);return()=>t("svg",{class:a.value,width:l.value,height:l.value,viewBox:"0 0 100 100",preserveAspectRatio:"xMidYMid",xmlns:"http://www.w3.org/2000/svg"},sd)}}),dd=[t("g",{fill:"none","fill-rule":"evenodd","stroke-width":"2"},[t("circle",{cx:"22",cy:"22",r:"1"},[t("animate",{attributeName:"r",begin:"0s",dur:"1.8s",values:"1; 20",calcMode:"spline",keyTimes:"0; 1",keySplines:"0.165, 0.84, 0.44, 1",repeatCount:"indefinite"}),t("animate",{attributeName:"stroke-opacity",begin:"0s",dur:"1.8s",values:"1; 0",calcMode:"spline",keyTimes:"0; 1",keySplines:"0.3, 0.61, 0.355, 1",repeatCount:"indefinite"})]),t("circle",{cx:"22",cy:"22",r:"1"},[t("animate",{attributeName:"r",begin:"-0.9s",dur:"1.8s",values:"1; 20",calcMode:"spline",keyTimes:"0; 1",keySplines:"0.165, 0.84, 0.44, 1",repeatCount:"indefinite"}),t("animate",{attributeName:"stroke-opacity",begin:"-0.9s",dur:"1.8s",values:"1; 0",calcMode:"spline",keyTimes:"0; 1",keySplines:"0.3, 0.61, 0.355, 1",repeatCount:"indefinite"})])])],cd=Ze({name:"QSpinnerPuff",props:ol,setup(e){let{cSize:l,classes:a}=nl(e);return()=>t("svg",{class:a.value,stroke:"currentColor",width:l.value,height:l.value,viewBox:"0 0 44 44",xmlns:"http://www.w3.org/2000/svg"},dd)}}),vd=[t("g",{transform:"scale(0.55)"},[t("circle",{cx:"30",cy:"150",r:"30",fill:"currentColor"},[t("animate",{attributeName:"opacity",from:"0",to:"1",dur:"1s",begin:"0",repeatCount:"indefinite",keyTimes:"0;0.5;1",values:"0;1;1"})]),t("path",{d:"M90,150h30c0-49.7-40.3-90-90-90v30C63.1,90,90,116.9,90,150z",fill:"currentColor"},[t("animate",{attributeName:"opacity",from:"0",to:"1",dur:"1s",begin:"0.1",repeatCount:"indefinite",keyTimes:"0;0.5;1",values:"0;1;1"})]),t("path",{d:"M150,150h30C180,67.2,112.8,0,30,0v30C96.3,30,150,83.7,150,150z",fill:"currentColor"},[t("animate",{attributeName:"opacity",from:"0",to:"1",dur:"1s",begin:"0.2",repeatCount:"indefinite",keyTimes:"0;0.5;1",values:"0;1;1"})])])],pd=Ze({name:"QSpinnerRadio",props:ol,setup(e){let{cSize:l,classes:a}=nl(e);return()=>t("svg",{class:a.value,width:l.value,height:l.value,viewBox:"0 0 100 100",preserveAspectRatio:"xMidYMid",xmlns:"http://www.w3.org/2000/svg"},vd)}}),fd=[t("g",{fill:"none","fill-rule":"evenodd",transform:"translate(1 1)","stroke-width":"2"},[t("circle",{cx:"22",cy:"22",r:"6"},[t("animate",{attributeName:"r",begin:"1.5s",dur:"3s",values:"6;22",calcMode:"linear",repeatCount:"indefinite"}),t("animate",{attributeName:"stroke-opacity",begin:"1.5s",dur:"3s",values:"1;0",calcMode:"linear",repeatCount:"indefinite"}),t("animate",{attributeName:"stroke-width",begin:"1.5s",dur:"3s",values:"2;0",calcMode:"linear",repeatCount:"indefinite"})]),t("circle",{cx:"22",cy:"22",r:"6"},[t("animate",{attributeName:"r",begin:"3s",dur:"3s",values:"6;22",calcMode:"linear",repeatCount:"indefinite"}),t("animate",{attributeName:"stroke-opacity",begin:"3s",dur:"3s",values:"1;0",calcMode:"linear",repeatCount:"indefinite"}),t("animate",{attributeName:"stroke-width",begin:"3s",dur:"3s",values:"2;0",calcMode:"linear",repeatCount:"indefinite"})]),t("circle",{cx:"22",cy:"22",r:"8"},[t("animate",{attributeName:"r",begin:"0s",dur:"1.5s",values:"6;1;2;3;4;5;6",calcMode:"linear",repeatCount:"indefinite"})])])],md=Ze({name:"QSpinnerRings",props:ol,setup(e){let{cSize:l,classes:a}=nl(e);return()=>t("svg",{class:a.value,stroke:"currentColor",width:l.value,height:l.value,viewBox:"0 0 45 45",xmlns:"http://www.w3.org/2000/svg"},fd)}}),gd=[t("defs",[t("linearGradient",{x1:"8.042%",y1:"0%",x2:"65.682%",y2:"23.865%",id:"a"},[t("stop",{"stop-color":"currentColor","stop-opacity":"0",offset:"0%"}),t("stop",{"stop-color":"currentColor","stop-opacity":".631",offset:"63.146%"}),t("stop",{"stop-color":"currentColor",offset:"100%"})])]),t("g",{transform:"translate(1 1)",fill:"none","fill-rule":"evenodd"},[t("path",{d:"M36 18c0-9.94-8.06-18-18-18",stroke:"url(#a)","stroke-width":"2"},[t("animateTransform",{attributeName:"transform",type:"rotate",from:"0 18 18",to:"360 18 18",dur:"0.9s",repeatCount:"indefinite"})]),t("circle",{fill:"currentColor",cx:"36",cy:"18",r:"1"},[t("animateTransform",{attributeName:"transform",type:"rotate",from:"0 18 18",to:"360 18 18",dur:"0.9s",repeatCount:"indefinite"})])])],hd=Ze({name:"QSpinnerTail",props:ol,setup(e){let{cSize:l,classes:a}=nl(e);return()=>t("svg",{class:a.value,width:l.value,height:l.value,viewBox:"0 0 38 38",xmlns:"http://www.w3.org/2000/svg"},gd)}}),bd=Ze({name:"QSplitter",props:{...Et,modelValue:{type:Number,required:!0},reverse:Boolean,unit:{type:String,default:"%",validator:e=>["%","px"].includes(e)},limits:{type:Array,validator:e=>2===e.length&&"number"==typeof e[0]&&"number"==typeof e[1]&&(e[0]>=0&&e[0]<=e[1])},emitImmediately:Boolean,horizontal:Boolean,disable:Boolean,beforeClass:[Array,String,Object],afterClass:[Array,String,Object],separatorClass:[Array,String,Object],separatorStyle:[Array,String,Object]},emits:["update:modelValue"],setup(l,{slots:i,emit:r}){let{proxy:{$q:s}}=a(),u=Pt(l,s),d=e(null),c={before:e(null),after:e(null)},v=o((()=>`q-splitter no-wrap ${!0===l.horizontal?"q-splitter--horizontal column":"q-splitter--vertical row"} q-splitter--${!0===l.disable?"disabled":"workable"}`+(!0===u.value?" q-splitter--dark":""))),p=o((()=>!0===l.horizontal?"height":"width")),f=o((()=>!0!==l.reverse?"before":"after")),g=o((()=>void 0!==l.limits?l.limits:"%"===l.unit?[10,90]:[50,1/0]));function h(e){return("%"===l.unit?e:Math.round(e))+l.unit}let b,y,w,x,_,S=o((()=>({[f.value]:{[p.value]:h(l.modelValue)}})));function k(e){if(!0===e.isFirst){let e=d.value.getBoundingClientRect()[p.value];return b=!0===l.horizontal?"up":"left",y="%"===l.unit?100:e,w=Math.min(y,g.value[1],Math.max(g.value[0],l.modelValue)),x=(!0!==l.reverse?1:-1)*(!0===l.horizontal?1:!0===s.lang.rtl?-1:1)*("%"===l.unit?0===e?0:100/e:1),void d.value.classList.add("q-splitter--active")}if(!0===e.isFinal)return _!==l.modelValue&&r("update:modelValue",_),void d.value.classList.remove("q-splitter--active");let t=w+x*(e.direction===b?-1:1)*e.distance[!0===l.horizontal?"y":"x"];_=Math.min(y,g.value[1],Math.max(g.value[0],t)),c[f.value].value.style[p.value]=h(_),!0===l.emitImmediately&&l.modelValue!==_&&r("update:modelValue",_)}let q=o((()=>[[Do,k,void 0,{[!0===l.horizontal?"vertical":"horizontal"]:!0,prevent:!0,stop:!0,mouse:!0,mouseAllDir:!0}]]));function C(e,t){e<t[0]?r("update:modelValue",t[0]):e>t[1]&&r("update:modelValue",t[1])}return n((()=>l.modelValue),(e=>{C(e,g.value)})),n((()=>l.limits),(()=>{m((()=>{C(l.modelValue,g.value)}))})),()=>{let e=[t("div",{ref:c.before,class:["q-splitter__panel q-splitter__before"+(!0===l.reverse?" col":""),l.beforeClass],style:S.value.before},pt(i.before)),t("div",{class:["q-splitter__separator",l.separatorClass],style:l.separatorStyle,"aria-disabled":!0===l.disable?"true":void 0},[ht("div",{class:"q-splitter__separator-area absolute-full"},pt(i.separator),"sep",!0!==l.disable,(()=>q.value))]),t("div",{ref:c.after,class:["q-splitter__panel q-splitter__after"+(!0===l.reverse?"":" col"),l.afterClass],style:S.value.after},pt(i.after))];return t("div",{class:v.value,ref:d},mt(i.default,e))}}}),yd=Ze({name:"StepHeader",props:{stepper:{},step:{},goToPanel:Function},setup(l,{attrs:n}){let{proxy:{$q:i}}=a(),r=e(null),s=o((()=>l.stepper.modelValue===l.step.name)),u=o((()=>{let e=l.step.disable;return!0===e||""===e})),d=o((()=>{let e=l.step.error;return!0===e||""===e})),c=o((()=>{let e=l.step.done;return!1===u.value&&(!0===e||""===e)})),v=o((()=>{let e=l.step.headerNav,t=!0===e||""===e||void 0===e;return!1===u.value&&l.stepper.headerNav&&t})),p=o((()=>l.step.prefix&&(!1===s.value||"none"===l.stepper.activeIcon)&&(!1===d.value||"none"===l.stepper.errorIcon)&&(!1===c.value||"none"===l.stepper.doneIcon))),f=o((()=>{let e=l.step.icon||l.stepper.inactiveIcon;if(!0===s.value){let t=l.step.activeIcon||l.stepper.activeIcon;return"none"===t?e:t||i.iconSet.stepper.active}if(!0===d.value){let t=l.step.errorIcon||l.stepper.errorIcon;return"none"===t?e:t||i.iconSet.stepper.error}if(!1===u.value&&!0===c.value){let t=l.step.doneIcon||l.stepper.doneIcon;return"none"===t?e:t||i.iconSet.stepper.done}return e})),m=o((()=>{let e=!0===d.value?l.step.errorColor||l.stepper.errorColor:void 0;if(!0===s.value){let t=l.step.activeColor||l.stepper.activeColor||l.step.color;return void 0!==t?t:e}return void 0!==e?e:!1===u.value&&!0===c.value?l.step.doneColor||l.stepper.doneColor||l.step.color||l.stepper.inactiveColor:l.step.color||l.stepper.inactiveColor})),h=o((()=>"q-stepper__tab col-grow flex items-center no-wrap relative-position"+(void 0!==m.value?` text-${m.value}`:"")+(!0===d.value?" q-stepper__tab--error q-stepper__tab--error-with-"+(!0===p.value?"prefix":"icon"):"")+(!0===s.value?" q-stepper__tab--active":"")+(!0===c.value?" q-stepper__tab--done":"")+(!0===v.value?" q-stepper__tab--navigation q-focusable q-hoverable":"")+(!0===u.value?" q-stepper__tab--disabled":""))),b=o((()=>!0===l.stepper.headerNav&&v.value));function y(){null!==r.value&&r.value.focus(),!1===s.value&&l.goToPanel(l.step.name)}function w(e){13===e.keyCode&&!1===s.value&&l.goToPanel(l.step.name)}return()=>{let e={class:h.value};!0===v.value&&(e.onClick=y,e.onKeyup=w,Object.assign(e,!0===u.value?{tabindex:-1,"aria-disabled":"true"}:{tabindex:n.tabindex||0}));let a=[t("div",{class:"q-focus-helper",tabindex:-1,ref:r}),t("div",{class:"q-stepper__dot row flex-center q-stepper__line relative-position"},[t("span",{class:"row flex-center"},[!0===p.value?l.step.prefix:t(zt,{name:f.value})])])];if(void 0!==l.step.title&&null!==l.step.title){let e=[t("div",{class:"q-stepper__title"},l.step.title)];void 0!==l.step.caption&&null!==l.step.caption&&e.push(t("div",{class:"q-stepper__caption"},l.step.caption)),a.push(t("div",{class:"q-stepper__label q-stepper__line relative-position"},e))}return g(t("div",e,a),[[fl,b.value]])}}});function wd(e){return t("div",{class:"q-stepper__step-content"},[t("div",{class:"q-stepper__step-inner"},pt(e.default))])}var xd={setup:(e,{slots:t})=>()=>wd(t)},_d=Ze({name:"QStep",props:{...fo,icon:String,color:String,title:{type:String,required:!0},caption:String,prefix:[String,Number],doneIcon:String,doneColor:String,activeIcon:String,activeColor:String,errorIcon:String,errorColor:String,headerNav:{type:Boolean,default:!0},done:Boolean,error:Boolean,onScroll:[Function,Array]},setup(l,{slots:n,emit:i}){let{proxy:{$q:r}}=a(),s=d(ze,Re);if(s===Re)return Re;let{getCache:u}=po(),c=e(null),v=o((()=>s.value.modelValue===l.name)),p=o((()=>!0!==r.platform.is.ios&&!0===r.platform.is.chrome||!0!==v.value||!0!==s.value.vertical?{}:{onScroll(e){let{target:t}=e;t.scrollTop>0&&(t.scrollTop=0),void 0!==l.onScroll&&i("scroll",e)}})),f=o((()=>"string"==typeof l.name||"number"==typeof l.name?l.name:String(l.name)));function m(){let e=s.value.vertical;return!0===e&&!0===s.value.keepAlive?t(w,s.value.keepAliveProps.value,!0===v.value?[t(!0===s.value.needsUniqueKeepAliveWrapper.value?u(f.value,(()=>({...xd,name:f.value}))):xd,{key:f.value},n.default)]:void 0):!0!==e||!0===v.value?wd(n):void 0}return()=>t("div",{ref:c,class:"q-stepper__step",role:"tabpanel",...p.value},!0===s.value.vertical?[t(yd,{stepper:s.value,step:l,goToPanel:s.value.goToPanel}),!0===s.value.animated?t(ir,m):m()]:[m()])}}),Sd=/(-\w)/g;var kd=Ze({name:"QStepper",props:{...Et,...go,flat:Boolean,bordered:Boolean,alternativeLabels:Boolean,headerNav:Boolean,contracted:Boolean,headerClass:String,inactiveColor:String,inactiveIcon:String,doneIcon:String,doneColor:String,activeIcon:String,activeColor:String,errorIcon:String,errorColor:String},emits:ho,setup(e,{slots:l}){let n=a(),i=Pt(e,n.proxy.$q),{updatePanelsList:r,isValidPanelName:s,updatePanelIndex:u,getPanelContent:d,getPanels:c,panelDirectives:v,goToPanel:p,keepAliveProps:f,needsUniqueKeepAliveWrapper:m}=bo();q(ze,o((()=>({goToPanel:p,keepAliveProps:f,needsUniqueKeepAliveWrapper:m,...e}))));let g=o((()=>"q-stepper q-stepper--"+(!0===e.vertical?"vertical":"horizontal")+(!0===e.flat?" q-stepper--flat":"")+(!0===e.bordered?" q-stepper--bordered":"")+(!0===i.value?" q-stepper--dark q-dark":""))),h=o((()=>`q-stepper__header row items-stretch justify-between q-stepper__header--${!0===e.alternativeLabels?"alternative":"standard"}-labels`+(!1===e.flat||!0===e.bordered?" q-stepper__header--border":"")+(!0===e.contracted?" q-stepper__header--contracted":"")+(void 0!==e.headerClass?` ${e.headerClass}`:"")));function b(){let a=pt(l.message,[]);if(!0===e.vertical){s(e.modelValue)&&u();let o=t("div",{class:"q-stepper__content"},pt(l.default));return void 0===a?[o]:a.concat(o)}return[t("div",{class:h.value},c().map((l=>{let a=function(e){let t={};for(let l in e)t[l.replace(Sd,(e=>e[1].toUpperCase()))]=e[l];return t}(l.props);return t(yd,{key:a.name,stepper:e,step:a,goToPanel:p})}))),a,ht("div",{class:"q-stepper__content q-panel-parent"},d(),"cont",e.swipeable,(()=>v.value))]}return()=>(r(l),t("div",{class:g.value},mt(l.navigation,b())))}}),qd=Ze({name:"QStepperNavigation",setup:(e,{slots:l})=>()=>t("div",{class:"q-stepper__nav"},pt(l.default))}),Cd=Ze({name:"QTh",props:{props:Object,autoWidth:Boolean},emits:["click"],setup(e,{slots:l,emit:o}){let n=a(),{proxy:{$q:i}}=n,r=e=>{o("click",e)};return()=>{if(void 0===e.props)return t("th",{class:!0===e.autoWidth?"q-table--col-auto-width":"",onClick:r},pt(l.default));let a,o,s=n.vnode.key;if(s){if(a=e.props.colsMap[s],void 0===a)return}else a=e.props.col;if(!0===a.sortable){let e="right"===a.align?"unshift":"push";o=ft(l.default,[]),o[e](t(zt,{class:a.__iconClass,name:i.iconSet.table.arrowUp}))}else o=pt(l.default);let u={class:a.__thClass+(!0===e.autoWidth?" q-table--col-auto-width":""),style:a.headerStyle,onClick:t=>{!0===a.sortable&&e.props.sort(a),r(t)}};return t("th",u,o)}}});function $d(e,l){return t("div",e,[t("table",{class:"q-table"},l)])}var Md={list:ps,table:_s},Td=["list","table","__qtable"],Bd=Ze({name:"QVirtualScroll",props:{...gu,type:{type:String,default:"list",validator:e=>Td.includes(e)},items:{type:Array,default:()=>[]},itemsFn:Function,itemsSize:Number,scrollTarget:{default:void 0}},setup(l,{slots:a,attrs:s}){let u,d=e(null),c=o((()=>l.itemsSize>=0&&void 0!==l.itemsFn?parseInt(l.itemsSize,10):Array.isArray(l.items)?l.items.length:0)),{virtualScrollSliceRange:v,localResetVirtualScroll:p,padVirtualScroll:f,onVirtualScrollEvt:m}=hu({virtualScrollLength:c,getVirtualScrollTarget:function(){return u},getVirtualScrollEl:_}),g=o((()=>{if(0===c.value)return[];let e=(e,t)=>({index:v.value.from+t,item:e});return void 0===l.itemsFn?l.items.slice(v.value.from,v.value.to).map(e):l.itemsFn(v.value.from,v.value.to-v.value.from).map(e)})),w=o((()=>"q-virtual-scroll q-virtual-scroll"+(!0===l.virtualScrollHorizontal?"--horizontal":"--vertical")+(void 0!==l.scrollTarget?"":" scroll"))),x=o((()=>void 0!==l.scrollTarget?{}:{tabindex:0}));function _(){return d.value.$el||d.value}function S(){u=la(_(),l.scrollTarget),u.addEventListener("scroll",m,Z.passive)}function k(){void 0!==u&&(u.removeEventListener("scroll",m,Z.passive),u=void 0)}function q(){let e=f("list"===l.type?"div":"tbody",g.value.map(a.default));return void 0!==a.before&&(e=a.before().concat(e)),mt(a.after,e)}return n(c,(()=>{p()})),n((()=>l.scrollTarget),(()=>{k(),S()})),y((()=>{p()})),r((()=>{S()})),b((()=>{S()})),h((()=>{k()})),i((()=>{k()})),()=>{if(void 0!==a.default)return"__qtable"===l.type?$d({ref:d,class:"q-table__middle "+w.value},q()):t(Md[l.type],{...s,ref:d,class:[s.class,w.value],...x.value},q)}}});var Ld={sortMethod:Function,binaryStateSort:Boolean,columnSortOrder:{type:String,validator:e=>"ad"===e||"da"===e,default:"ad"}};function zd(e,t,l,a){let n=o((()=>{let{sortBy:e}=t.value;return e&&l.value.find((t=>t.name===e))||null})),i=o((()=>void 0!==e.sortMethod?e.sortMethod:(e,t,a)=>{let o=l.value.find((e=>e.name===t));if(void 0===o||void 0===o.field)return e;let n=!0===a?-1:1,i="function"==typeof o.field?e=>o.field(e):e=>e[o.field];return e.sort(((e,t)=>{let l=i(e),a=i(t);return void 0!==o.rawSort?o.rawSort(l,a,e,t)*n:null==l?-1*n:null==a?1*n:void 0!==o.sort?o.sort(l,a,e,t)*n:!0===Qe(l)&&!0===Qe(a)?(l-a)*n:!0===De(l)&&!0===De(a)?function(e,t){return new Date(e)-new Date(t)}(l,a)*n:"boolean"==typeof l&&"boolean"==typeof a?(l-a)*n:([l,a]=[l,a].map((e=>(e+"").toLocaleString().toLowerCase())),l<a?-1*n:l===a?0:n)}))}));return{columnToSort:n,computedSortMethod:i,sort:function(o){let n=e.columnSortOrder;if(!0===je(o))o.sortOrder&&(n=o.sortOrder),o=o.name;else{let e=l.value.find((e=>e.name===o));void 0!==e&&e.sortOrder&&(n=e.sortOrder)}let{sortBy:i,descending:r}=t.value;i!==o?(i=o,r="da"===n):!0===e.binaryStateSort?r=!r:!0===r?"ad"===n?i=null:r=!1:"ad"===n?r=!0:i=null,a({sortBy:i,descending:r,page:1})}}}var Vd={filter:[String,Object],filterMethod:Function};function Od(e){return e.page<1&&(e.page=1),void 0!==e.rowsPerPage&&e.rowsPerPage<1&&(e.rowsPerPage=0),e}var Ad={pagination:Object,rowsPerPageOptions:{type:Array,default:()=>[5,7,10,15,20,25,50,0]},"onUpdate:pagination":[Function,Array]};var Ed={selection:{type:String,default:"none",validator:e=>["single","multiple","none"].includes(e)},selected:{type:Array,default:()=>[]}};function Pd(e){return Array.isArray(e)?e.slice():[]}var Fd={expanded:Array};var Rd={visibleColumns:Array};var Nd="q-table__bottom row items-center",Hd={};mu.forEach((e=>{Hd[e]={}}));var Id=Ze({name:"QTable",props:{rows:{type:Array,default:()=>[]},rowKey:{type:[String,Function],default:"id"},columns:Array,loading:Boolean,iconFirstPage:String,iconPrevPage:String,iconNextPage:String,iconLastPage:String,title:String,hideHeader:Boolean,grid:Boolean,gridHeader:Boolean,dense:Boolean,flat:Boolean,bordered:Boolean,square:Boolean,separator:{type:String,default:"horizontal",validator:e=>["horizontal","vertical","cell","none"].includes(e)},wrapCells:Boolean,virtualScroll:Boolean,virtualScrollTarget:{default:void 0},...Hd,noDataLabel:String,noResultsLabel:String,loadingLabel:String,selectedRowsLabel:Function,rowsPerPageLabel:String,paginationLabel:Function,color:{type:String,default:"grey-8"},titleClass:[String,Array,Object],tableStyle:[String,Array,Object],tableClass:[String,Array,Object],tableHeaderStyle:[String,Array,Object],tableHeaderClass:[String,Array,Object],cardContainerClass:[String,Array,Object],cardContainerStyle:[String,Array,Object],cardStyle:[String,Array,Object],cardClass:[String,Array,Object],hideBottom:Boolean,hideSelectedBanner:Boolean,hideNoData:Boolean,hidePagination:Boolean,onRowClick:Function,onRowDblclick:Function,onRowContextmenu:Function,...Et,...wo,...Rd,...Vd,...Ad,...Fd,...Ed,...Ld},emits:["request","virtualScroll",...xo,"update:expanded","update:selected","selection"],setup(l,{slots:i,emit:r}){let s=a(),{proxy:{$q:u}}=s,d=Pt(l,u),{inFullscreen:c,toggleFullscreen:v}=_o(),p=o((()=>"function"==typeof l.rowKey?l.rowKey:e=>e[l.rowKey])),f=e(null),g=e(null),h=o((()=>!0!==l.grid&&!0===l.virtualScroll)),b=o((()=>" q-table__card"+(!0===d.value?" q-table__card--dark q-dark":"")+(!0===l.square?" q-table--square":"")+(!0===l.flat?" q-table--flat":"")+(!0===l.bordered?" q-table--bordered":""))),y=o((()=>`q-table__container q-table--${l.separator}-separator column no-wrap`+(!0===l.grid?" q-table--grid":b.value)+(!0===d.value?" q-table--dark":"")+(!0===l.dense?" q-table--dense":"")+(!1===l.wrapCells?" q-table--no-wrap":"")+(!0===c.value?" fullscreen scroll":""))),w=o((()=>y.value+(!0===l.loading?" q-table--loading":"")));n((()=>l.tableStyle+l.tableClass+l.tableHeaderStyle+l.tableHeaderClass+y.value),(()=>{!0===h.value&&null!==g.value&&g.value.reset()}));let{innerPagination:x,computedPagination:_,isServerSide:S,requestServerInteraction:k,setPagination:q}=function(t,l){let{props:a,emit:n}=t,i=e(Object.assign({sortBy:null,descending:!1,page:1,rowsPerPage:0!==a.rowsPerPageOptions.length?a.rowsPerPageOptions[0]:5},a.pagination)),r=o((()=>Od(void 0!==a["onUpdate:pagination"]?{...i.value,...a.pagination}:i.value))),s=o((()=>void 0!==r.value.rowsNumber));function u(e){d({pagination:e,filter:a.filter})}function d(e={}){m((()=>{n("request",{pagination:e.pagination||r.value,filter:e.filter||a.filter,getCellValue:l})}))}return{innerPagination:i,computedPagination:r,isServerSide:s,requestServerInteraction:d,setPagination:function(e,t){let l=Od({...r.value,...e});!0!==function(e,t){for(let l in t)if(t[l]!==e[l])return!1;return!0}(r.value,l)?!0!==s.value?void 0!==a.pagination&&void 0!==a["onUpdate:pagination"]?n("update:pagination",l):i.value=l:u(l):!0===s.value&&!0===t&&u(l)}}}(s,he),{computedFilterMethod:C}=function(e,t){let l=o((()=>void 0!==e.filterMethod?e.filterMethod:(e,t,l,a)=>{let o=t?t.toLowerCase():"";return e.filter((e=>l.some((t=>{let l=a(t,e)+"";return-1!==("undefined"===l||"null"===l?"":l.toLowerCase()).indexOf(o)}))))}));return n((()=>e.filter),(()=>{m((()=>{t({page:1},!0)}))}),{deep:!0}),{computedFilterMethod:l}}(l,q),{isRowExpanded:$,setExpanded:M,updateExpanded:T}=function(t,l){let a=e(Pd(t.expanded));function o(e){void 0!==t.expanded?l("update:expanded",e):a.value=e}return n((()=>t.expanded),(e=>{a.value=Pd(e)})),{isRowExpanded:function(e){return a.value.includes(e)},setExpanded:o,updateExpanded:function(e,t){let l=a.value.slice(),n=l.indexOf(e);!0===t?-1===n&&(l.push(e),o(l)):-1!==n&&(l.splice(n,1),o(l))}}}(l,r),B=o((()=>{let e=l.rows;if(!0===S.value||0===e.length)return e;let{sortBy:t,descending:a}=_.value;return l.filter&&(e=C.value(e,l.filter,Q.value,he)),null!==K.value&&(e=Y.value(l.rows===e?e.slice():e,t,a)),e})),L=o((()=>B.value.length)),z=o((()=>{let e=B.value;if(!0===S.value)return e;let{rowsPerPage:t}=_.value;return 0!==t&&(0===Z.value&&l.rows!==e?e.length>G.value&&(e=e.slice(0,G.value)):e=e.slice(Z.value,G.value)),e})),{hasSelectionMode:V,singleSelection:O,multipleSelection:A,allRowsSelected:E,someRowsSelected:P,rowsSelectedNumber:F,isRowSelected:R,clearSelection:N,updateSelection:j}=function(e,t,l,a){let n=o((()=>{let t={};return e.selected.map(a.value).forEach((e=>{t[e]=!0})),t})),i=o((()=>"none"!==e.selection)),r=o((()=>"single"===e.selection)),s=o((()=>"multiple"===e.selection)),u=o((()=>0!==l.value.length&&l.value.every((e=>!0===n.value[a.value(e)])))),d=o((()=>!0!==u.value&&l.value.some((e=>!0===n.value[a.value(e)])))),c=o((()=>e.selected.length));return{hasSelectionMode:i,singleSelection:r,multipleSelection:s,allRowsSelected:u,someRowsSelected:d,rowsSelectedNumber:c,isRowSelected:function(e){return!0===n.value[e]},clearSelection:function(){t("update:selected",[])},updateSelection:function(l,o,n,i){t("selection",{rows:o,added:n,keys:l,evt:i});let s=!0===r.value?!0===n?o:[]:!0===n?e.selected.concat(o):e.selected.filter((e=>!1===l.includes(a.value(e))));t("update:selected",s)}}}(l,r,z,p),{colList:D,computedCols:Q,computedColsMap:U,computedColspan:W}=function(e,t,l){let a=o((()=>{if(void 0!==e.columns)return e.columns;let t=e.rows[0];return void 0!==t?Object.keys(t).map((e=>({name:e,label:e.toUpperCase(),field:e,align:Qe(t[e])?"right":"left",sortable:!0}))):[]})),n=o((()=>{let{sortBy:l,descending:o}=t.value;return(void 0!==e.visibleColumns?a.value.filter((t=>!0===t.required||!0===e.visibleColumns.includes(t.name))):a.value).map((e=>{let t=e.align||"right",a=`text-${t}`;return{...e,align:t,__iconClass:`q-table__sort-icon q-table__sort-icon--${t}`,__thClass:a+(void 0!==e.headerClasses?" "+e.headerClasses:"")+(!0===e.sortable?" sortable":"")+(e.name===l?" sorted "+(!0===o?"sort-desc":""):""),__tdStyle:void 0!==e.style?"function"!=typeof e.style?()=>e.style:e.style:()=>null,__tdClass:void 0!==e.classes?"function"!=typeof e.classes?()=>a+" "+e.classes:t=>a+" "+e.classes(t):()=>a}}))})),i=o((()=>{let e={};return n.value.forEach((t=>{e[t.name]=t})),e})),r=o((()=>void 0!==e.tableColspan?e.tableColspan:n.value.length+(!0===l.value?1:0)));return{colList:a,computedCols:n,computedColsMap:i,computedColspan:r}}(l,_,V),{columnToSort:K,computedSortMethod:Y,sort:X}=zd(l,_,D,q),{firstRowIndex:Z,lastRowIndex:G,isFirstPage:J,isLastPage:ee,pagesNumber:te,computedRowsPerPageOptions:le,computedRowsNumber:ae,firstPage:oe,prevPage:ne,nextPage:ie,lastPage:re}=function(e,t,l,a,i,r){let{props:s,emit:u,proxy:{$q:d}}=e,c=o((()=>!0===a.value?l.value.rowsNumber||0:r.value)),v=o((()=>{let{page:e,rowsPerPage:t}=l.value;return(e-1)*t})),p=o((()=>{let{page:e,rowsPerPage:t}=l.value;return e*t})),f=o((()=>1===l.value.page)),m=o((()=>0===l.value.rowsPerPage?1:Math.max(1,Math.ceil(c.value/l.value.rowsPerPage)))),g=o((()=>0===p.value||l.value.page>=m.value)),h=o((()=>(s.rowsPerPageOptions.includes(t.value.rowsPerPage)?s.rowsPerPageOptions:[t.value.rowsPerPage].concat(s.rowsPerPageOptions)).map((e=>({label:0===e?d.lang.table.allRows:""+e,value:e})))));return n(m,((e,t)=>{if(e===t)return;let a=l.value.page;e&&!a?i({page:1}):e<a&&i({page:e})})),void 0!==s["onUpdate:pagination"]&&u("update:pagination",{...l.value}),{firstRowIndex:v,lastRowIndex:p,isFirstPage:f,isLastPage:g,pagesNumber:m,computedRowsPerPageOptions:h,computedRowsNumber:c,firstPage:function(){i({page:1})},prevPage:function(){let{page:e}=l.value;e>1&&i({page:e-1})},nextPage:function(){let{page:e,rowsPerPage:t}=l.value;p.value>0&&e*t<c.value&&i({page:e+1})},lastPage:function(){i({page:m.value})}}}(s,x,_,S,q,L),se=o((()=>0===z.value.length)),ue=o((()=>{let e={};return mu.forEach((t=>{e[t]=l[t]})),void 0===e.virtualScrollItemSize&&(e.virtualScrollItemSize=!0===l.dense?28:48),e}));function de(){if(!0===l.grid)return function(){let e=void 0!==i.item?i.item:e=>{let a=e.cols.map((e=>t("div",{class:"q-table__grid-item-row"},[t("div",{class:"q-table__grid-item-title"},[e.label]),t("div",{class:"q-table__grid-item-value"},[e.value])])));if(!0===V.value){let o=i["body-selection"],n=void 0!==o?o(e):[t(Ao,{modelValue:e.selected,color:l.color,dark:d.value,dense:l.dense,"onUpdate:modelValue":(t,l)=>{j([e.key],[e.row],t,l)}})];a.unshift(t("div",{class:"q-table__grid-item-row"},n),t(ur,{dark:d.value}))}let o={class:["q-table__grid-item-card"+b.value,l.cardClass],style:l.cardStyle};return(void 0!==l.onRowClick||void 0!==l.onRowDblclick)&&(o.class[0]+=" cursor-pointer",void 0!==l.onRowClick&&(o.onClick=t=>{r("RowClick",t,e.row,e.pageIndex)}),void 0!==l.onRowDblclick&&(o.onDblclick=t=>{r("RowDblclick",t,e.row,e.pageIndex)})),t("div",{class:"q-table__grid-item col-xs-12 col-sm-6 col-md-4 col-lg-3"+(!0===e.selected?" q-table__grid-item--selected":"")},[t("div",o,a)])};return t("div",{class:["q-table__grid-content row",l.cardContainerClass],style:l.cardContainerStyle},z.value.map(((t,l)=>e(me({key:p.value(t),row:t,pageIndex:l})))))}();let e=!0!==l.hideHeader?xe:null;if(!0===h.value){let a=i["top-row"],o=i["bottom-row"],n={default:e=>pe(e.item,i.body,e.index)};if(void 0!==a){let l=t("tbody",a({cols:Q.value}));n.before=null===e?()=>l:()=>[e()].concat(l)}else null!==e&&(n.before=e);return void 0!==o&&(n.after=()=>t("tbody",o({cols:Q.value}))),t(Bd,{ref:g,class:l.tableClass,style:l.tableStyle,...ue.value,scrollTarget:l.virtualScrollTarget,items:z.value,type:"__qtable",tableColspan:W.value,onVirtualScroll:ce},n)}let a=[fe()];return null!==e&&a.unshift(e()),$d({class:["q-table__middle scroll",l.tableClass],style:l.tableStyle},a)}function ce(e){r("virtualScroll",e)}function ve(){return[t(Us,{class:"q-table__linear-progress",color:l.color,dark:d.value,indeterminate:!0,trackColor:"transparent"})]}function pe(e,a,o){let n=p.value(e),s=R(n);if(void 0!==a)return a(me({key:n,row:e,pageIndex:o,__trClass:s?"selected":""}));let u=i["body-cell"],c=Q.value.map((l=>{let a=i[`body-cell-${l.name}`],r=void 0!==a?a:u;return void 0!==r?r(function(e){return ge(e),H(e,"value",(()=>he(e.col,e.row))),e}({key:n,row:e,pageIndex:o,col:l})):t("td",{class:l.__tdClass(e),style:l.__tdStyle(e)},he(l,e))}));if(!0===V.value){let a=i["body-selection"],r=void 0!==a?a(function(e){return ge(e),e}({key:n,row:e,pageIndex:o})):[t(Ao,{modelValue:s,color:l.color,dark:d.value,dense:l.dense,"onUpdate:modelValue":(t,l)=>{j([n],[e],t,l)}})];c.unshift(t("td",{class:"q-table--col-auto-width"},r))}let v={key:n,class:{selected:s}};return void 0!==l.onRowClick&&(v.class["cursor-pointer"]=!0,v.onClick=t=>{r("RowClick",t,e,o)}),void 0!==l.onRowDblclick&&(v.class["cursor-pointer"]=!0,v.onDblclick=t=>{r("RowDblclick",t,e,o)}),void 0!==l.onRowContextmenu&&(v.class["cursor-pointer"]=!0,v.onContextmenu=t=>{r("RowContextmenu",t,e,o)}),t("tr",v,c)}function fe(){let e=i.body,l=i["top-row"],a=i["bottom-row"],o=z.value.map(((t,l)=>pe(t,e,l)));return void 0!==l&&(o=l({cols:Q.value}).concat(o)),void 0!==a&&(o=o.concat(a({cols:Q.value}))),t("tbody",o)}function me(e){return ge(e),e.cols=e.cols.map((t=>H({...t},"value",(()=>he(t,e.row))))),e}function ge(e){Object.assign(e,{cols:Q.value,colsMap:U.value,sort:X,rowIndex:Z.value+e.pageIndex,color:l.color,dark:d.value,dense:l.dense}),!0===V.value&&H(e,"selected",(()=>R(e.key)),((t,l)=>{j([e.key],[e.row],t,l)})),H(e,"expand",(()=>$(e.key)),(t=>{T(e.key,t)}))}function he(e,t){let l="function"==typeof e.field?e.field(t):t[e.field];return void 0!==e.format?e.format(l,t):l}let be=o((()=>({pagination:_.value,pagesNumber:te.value,isFirstPage:J.value,isLastPage:ee.value,firstPage:oe,prevPage:ne,nextPage:ie,lastPage:re,inFullscreen:c.value,toggleFullscreen:v})));function ye(){let e,a=i.top,o=i["top-left"],n=i["top-right"],r=i["top-selection"],s=!0===V.value&&void 0!==r&&F.value>0,u="q-table__top relative-position row items-center";return void 0!==a?t("div",{class:u},[a(be.value)]):(!0===s?e=r(be.value).slice():(e=[],void 0!==o?e.push(t("div",{class:"q-table__control"},[o(be.value)])):l.title&&e.push(t("div",{class:"q-table__control"},[t("div",{class:["q-table__title",l.titleClass]},l.title)]))),void 0!==n&&(e.push(t("div",{class:"q-table__separator col"})),e.push(t("div",{class:"q-table__control"},[n(be.value)]))),0!==e.length?t("div",{class:u},e):void 0)}let we=o((()=>!0===P.value?null:E.value));function xe(){let e=function(){let e=i.header,a=i["header-cell"];if(void 0!==e)return e(_e({header:!0})).slice();let o=Q.value.map((e=>{let l=i[`header-cell-${e.name}`],o=void 0!==l?l:a,n=_e({col:e});return void 0!==o?o(n):t(Cd,{key:e.name,props:n},(()=>e.label))}));if(!0===O.value&&!0!==l.grid)o.unshift(t("th",{class:"q-table--col-auto-width"}," "));else if(!0===A.value){let e=i["header-selection"],a=void 0!==e?e(_e({})):[t(Ao,{color:l.color,modelValue:we.value,dark:d.value,dense:l.dense,"onUpdate:modelValue":Se})];o.unshift(t("th",{class:"q-table--col-auto-width"},a))}return[t("tr",{class:l.tableHeaderClass,style:l.tableHeaderStyle},o)]}();return!0===l.loading&&void 0===i.loading&&e.push(t("tr",{class:"q-table__progress"},[t("th",{class:"relative-position",colspan:W.value},ve())])),t("thead",e)}function _e(e){return Object.assign(e,{cols:Q.value,sort:X,colsMap:U.value,color:l.color,dark:d.value,dense:l.dense}),!0===A.value&&H(e,"selected",(()=>we.value),Se),e}function Se(e){!0===P.value&&(e=!1),j(z.value.map(p.value),z.value,e)}let ke=o((()=>{let e=[l.iconFirstPage||u.iconSet.table.firstPage,l.iconPrevPage||u.iconSet.table.prevPage,l.iconNextPage||u.iconSet.table.nextPage,l.iconLastPage||u.iconSet.table.lastPage];return!0===u.lang.rtl?e.reverse():e}));function qe(){if(!0===l.hideBottom)return;if(!0===se.value){if(!0===l.hideNoData)return;let e=!0===l.loading?l.loadingLabel||u.lang.table.loading:l.filter?l.noResultsLabel||u.lang.table.noResults:l.noDataLabel||u.lang.table.noData,a=i["no-data"],o=void 0!==a?[a({message:e,icon:u.iconSet.table.warning,filter:l.filter})]:[t(zt,{class:"q-table__bottom-nodata-icon",name:u.iconSet.table.warning}),e];return t("div",{class:Nd+" q-table__bottom--nodata"},o)}let e=i.bottom;if(void 0!==e)return t("div",{class:Nd},[e(be.value)]);let a=!0!==l.hideSelectedBanner&&!0===V.value&&F.value>0?[t("div",{class:"q-table__control"},[t("div",[(l.selectedRowsLabel||u.lang.table.selectedRecords)(F.value)])])]:[];return!0!==l.hidePagination?t("div",{class:Nd+" justify-end"},function(e){let a,{rowsPerPage:o}=_.value,n=l.paginationLabel||u.lang.table.pagination,r=i.pagination,s=l.rowsPerPageOptions.length>1;if(e.push(t("div",{class:"q-table__separator col"})),!0===s&&e.push(t("div",{class:"q-table__control"},[t("span",{class:"q-table__bottom-item"},[l.rowsPerPageLabel||u.lang.table.recordsPerPage]),t(wu,{class:"q-table__select inline q-table__bottom-item",color:l.color,modelValue:o,options:le.value,displayValue:0===o?u.lang.table.allRows:o,dark:d.value,borderless:!0,dense:!0,optionsDense:!0,optionsCover:!0,"onUpdate:modelValue":Ce})])),void 0!==r)a=r(be.value);else if(a=[t("span",0!==o?{class:"q-table__bottom-item"}:{},[o?n(Z.value+1,Math.min(G.value,ae.value),ae.value):n(1,L.value,ae.value)])],0!==o&&te.value>1){let e={color:l.color,round:!0,dense:!0,flat:!0};!0===l.dense&&(e.size="sm"),te.value>2&&a.push(t($l,{key:"pgFirst",...e,icon:ke.value[0],disable:J.value,onClick:oe})),a.push(t($l,{key:"pgPrev",...e,icon:ke.value[1],disable:J.value,onClick:ne}),t($l,{key:"pgNext",...e,icon:ke.value[2],disable:ee.value,onClick:ie})),te.value>2&&a.push(t($l,{key:"pgLast",...e,icon:ke.value[3],disable:ee.value,onClick:re}))}return e.push(t("div",{class:"q-table__control"},a)),e}(a)):0!==a.length?t("div",{class:Nd},a):void 0}function Ce(e){q({page:1,rowsPerPage:e.value})}return Object.assign(s.proxy,{requestServerInteraction:k,setPagination:q,firstPage:oe,prevPage:ne,nextPage:ie,lastPage:re,isRowSelected:R,clearSelection:N,isRowExpanded:$,setExpanded:M,sort:X,resetVirtualScroll:function(){!0===h.value&&g.value.reset()},scrollTo:function(e,t){if(null!==g.value)return void g.value.scrollTo(e,t);e=parseInt(e,10);let a=f.value.querySelector(`tbody tr:nth-of-type(${e+1})`);if(null!==a){let t=f.value.querySelector(".q-table__middle.scroll"),o=a.offsetTop-l.virtualScrollStickySizeStart,n=o<t.scrollTop?"decrease":"increase";t.scrollTop=o,r("virtualScroll",{index:e,from:0,to:x.value.rowsPerPage-1,direction:n})}},getCellValue:he}),I(s.proxy,{filteredSortedRows:()=>B.value,computedRows:()=>z.value,computedRowsNumber:()=>ae.value}),()=>{let e=[ye()],a={ref:f,class:w.value};return!0===l.grid?e.push(function(){let e=!0===l.gridHeader?[t("table",{class:"q-table"},[xe()])]:!0===l.loading&&void 0===i.loading?ve():void 0;return t("div",{class:"q-table__middle"},e)}()):Object.assign(a,{class:[a.class,l.cardClass],style:l.cardStyle}),e.push(de(),qe()),!0===l.loading&&void 0!==i.loading&&e.push(i.loading()),t("div",a,e)}}}),jd=Ze({name:"QTr",props:{props:Object,noHover:Boolean},setup(e,{slots:l}){let a=o((()=>"q-tr"+(void 0===e.props||!0===e.props.header?"":" "+e.props.__trClass)+(!0===e.noHover?" q-tr--no-hover":"")));return()=>t("tr",{class:a.value},pt(l.default))}}),Dd=Ze({name:"QTd",props:{props:Object,autoWidth:Boolean,noHover:Boolean},setup(e,{slots:l}){let n=a(),i=o((()=>"q-td"+(!0===e.autoWidth?" q-table--col-auto-width":"")+(!0===e.noHover?" q-td--no-hover":"")+" "));return()=>{if(void 0===e.props)return t("td",{class:i.value},pt(l.default));let a=n.vnode.key,o=(void 0!==e.props.colsMap?e.props.colsMap[a]:null)||e.props.col;if(void 0===o)return;let{row:r}=e.props;return t("td",{class:i.value+o.__tdClass(r),style:o.__tdStyle(r)},pt(l.default))}}}),Qd=Ze({name:"QRouteTab",props:{...tl,...cn},emits:dn,setup(e,{slots:t,emit:l}){let a=ll({useDisableForRouterLinkProps:!1}),{renderTab:i,$tabs:r}=vn(e,t,l,{exact:o((()=>e.exact)),...a});return n((()=>`${e.name} | ${e.exact} | ${(a.resolvedLink.value||{}).href}`),(()=>{r.verifyRouteModel()})),()=>i(a.linkTag.value,a.linkAttrs.value)}});function Ud(){let e=new Date;return{hour:e.getHours(),minute:e.getMinutes(),second:e.getSeconds(),millisecond:e.getMilliseconds()}}var Wd=Ze({name:"QTime",props:{...Et,...Za,...jn,mask:{default:null},format24h:{type:Boolean,default:null},defaultDate:{type:String,validator:e=>/^-?[\d]+\/[0-1]\d\/[0-3]\d$/.test(e)},options:Function,hourOptions:Array,minuteOptions:Array,secondOptions:Array,withSeconds:Boolean,nowBtn:Boolean},emits:Dn,setup(l,{slots:i,emit:r}){let s,u,d=a(),{$q:c}=d.proxy,v=Pt(l,c),{tabindex:p,headerClass:h,getLocale:b,getCurrentDate:y}=Un(l,c),w=Ja(Ga(l)),x=e(null),_=o((()=>"persian"!==l.calendar&&null!==l.mask?l.mask:"HH:mm"+(!0===l.withSeconds?":ss":""))),S=o((()=>b())),k=o((()=>function(){if("string"!=typeof l.defaultDate){let e=y(!0);return e.dateHash=Qn(e),e}return li(l.defaultDate,"YYYY/MM/DD",void 0,l.calendar)}())),q=li(l.modelValue,_.value,S.value,l.calendar,k.value),C=e(function(e,t){if(null!==e.hour){if(null===e.minute)return"minute";if(!0===t&&null===e.second)return"second"}return"hour"}(q)),$=e(q),M=e(null===q.hour||q.hour<12),T=o((()=>"q-time q-time--"+(!0===l.landscape?"landscape":"portrait")+(!0===v.value?" q-time--dark q-dark":"")+(!0===l.disable?" disabled":!0===l.readonly?" q-time--readonly":"")+(!0===l.bordered?" q-time--bordered":"")+(!0===l.square?" q-time--square no-border-radius":"")+(!0===l.flat?" q-time--flat no-shadow":""))),B=o((()=>{let e=$.value;return{hour:null===e.hour?"--":!0===L.value?at(e.hour):String(!0===M.value?0===e.hour?12:e.hour:e.hour>12?e.hour-12:e.hour),minute:null===e.minute?"--":at(e.minute),second:null===e.second?"--":at(e.second)}})),L=o((()=>null!==l.format24h?l.format24h:c.lang.date.format24h)),z=o((()=>{let e="hour"===C.value,t=!0===e?12:60,l=$.value[C.value],a=`rotate(${Math.round(l*(360/t))-180}deg) translateX(-50%)`;return!0===e&&!0===L.value&&$.value.hour>=12&&(a+=" scale(.7)"),{transform:a}})),V=o((()=>null!==$.value.hour)),O=o((()=>!0===V.value&&null!==$.value.minute)),A=o((()=>void 0!==l.hourOptions?e=>l.hourOptions.includes(e):void 0!==l.options?e=>l.options(e,null,null):null)),E=o((()=>void 0!==l.minuteOptions?e=>l.minuteOptions.includes(e):void 0!==l.options?e=>l.options($.value.hour,e,null):null)),P=o((()=>void 0!==l.secondOptions?e=>l.secondOptions.includes(e):void 0!==l.options?e=>l.options($.value.hour,$.value.minute,e):null)),F=o((()=>{if(null===A.value)return null;let e=Q(0,11,A.value),t=Q(12,11,A.value);return{am:e,pm:t,values:e.values.concat(t.values)}})),R=o((()=>null!==E.value?Q(0,59,E.value):null)),N=o((()=>null!==P.value?Q(0,59,P.value):null)),H=o((()=>{switch(C.value){case"hour":return F.value;case"minute":return R.value;case"second":return N.value}})),I=o((()=>{let e,t,l=0,a=1,o=null!==H.value?H.value.values:void 0;"hour"===C.value?!0===L.value?(e=0,t=23):(e=0,t=11,!1===M.value&&(l=12)):(e=0,t=55,a=5);let n=[];for(let i=e,r=e;i<=t;i+=a,r++){let e=i+l,t=void 0!==o&&!1===o.includes(e),a="hour"===C.value&&0===i?!0===L.value?"00":"12":i;n.push({val:e,index:r,disable:t,label:a})}return n})),j=o((()=>[[Do,X,void 0,{stop:!0,prevent:!0,mouse:!0}]]));function D(){let e={...y(),...Ud()};ge(e),Object.assign($.value,e),C.value="hour"}function Q(e,t,l){let a=Array.apply(null,{length:t+1}).map(((t,a)=>{let o=a+e;return{index:o,val:!0===l(o)}})).filter((e=>!0===e.val)).map((e=>e.index));return{min:a[0],max:a[a.length-1],values:a,threshold:t+1}}function U(e,t,l){let a=Math.abs(e-t);return Math.min(a,l-a)}function W(e,{min:t,max:l,values:a,threshold:o}){if(e===t)return t;if(e<t||e>l)return U(e,t,o)<=U(e,l,o)?t:l;let n=a.findIndex((t=>e<=t)),i=a[n-1],r=a[n];return e-i<=r-e?i:r}function K(){return!0===Kt(d)||null!==H.value&&(0===H.value.values.length||"hour"===C.value&&!0!==L.value&&0===F.value[!0===M.value?"am":"pm"].values.length)}function Y(){let e=x.value,{top:t,left:l,width:a}=e.getBoundingClientRect(),o=a/2;return{top:t+o,left:l+o,dist:.7*o}}function X(e){if(!0!==K()){if(!0===e.isFirst)return s=Y(),void(u=G(e.evt,s));u=G(e.evt,s,u),!0===e.isFinal&&(s=!1,u=null,Z())}}function Z(){"hour"===C.value?C.value="minute":l.withSeconds&&"minute"===C.value&&(C.value="second")}function G(e,t,l){let a,o=ee(e),n=Math.abs(o.top-t.top),i=Math.sqrt(Math.pow(Math.abs(o.top-t.top),2)+Math.pow(Math.abs(o.left-t.left),2)),r=Math.asin(n/i)*(180/Math.PI);if(r=o.top<t.top?t.left<o.left?90-r:270+r:t.left<o.left?r+90:270-r,"hour"===C.value){if(a=r/30,null!==F.value){let e=!0!==L.value?!0===M.value:0!==F.value.am.values.length&&0!==F.value.pm.values.length?i>=t.dist:0!==F.value.am.values.length;a=W(a+(!0===e?0:12),F.value[!0===e?"am":"pm"])}else a=Math.round(a),!0===L.value?i<t.dist?a<12&&(a+=12):12===a&&(a=0):!0===M.value&&12===a?a=0:!1===M.value&&12!==a&&(a+=12);!0===L.value&&(M.value=a<12)}else a=Math.round(r/6)%60,"minute"===C.value&&null!==R.value?a=W(a,R.value):"second"===C.value&&null!==N.value&&(a=W(a,N.value));return l!==a&&ce[C.value](a),a}n((()=>l.modelValue),(e=>{let t=li(e,_.value,S.value,l.calendar,k.value);(t.dateHash!==$.value.dateHash||t.timeHash!==$.value.timeHash)&&($.value=t,null===t.hour?C.value="hour":M.value=t.hour<12)})),n([_,S],(()=>{m((()=>{ge()}))}));let J={hour(){C.value="hour"},minute(){C.value="minute"},second(){C.value="second"}};function te(e){13===e.keyCode&&ve()}function le(e){13===e.keyCode&&pe()}function ae(e){!0!==K()&&(!0!==c.platform.is.desktop&&G(e,Y()),Z())}function oe(e){!0!==K()&&G(e,Y())}function ne(e){if(13===e.keyCode)C.value="hour";else if([37,39].includes(e.keyCode)){let t=37===e.keyCode?-1:1;if(null!==F.value){let e=!0===L.value?F.value.values:F.value[!0===M.value?"am":"pm"].values;if(0===e.length)return;if(null===$.value.hour)se(e[0]);else{let l=(e.length+e.indexOf($.value.hour)+t)%e.length;se(e[l])}}else{let e=!0===L.value?24:12;se((!0!==L.value&&!1===M.value?12:0)+(24+(null===$.value.hour?-t:$.value.hour)+t)%e)}}}function ie(e){if(13===e.keyCode)C.value="minute";else if([37,39].includes(e.keyCode)){let t=37===e.keyCode?-1:1;if(null!==R.value){let e=R.value.values;if(0===e.length)return;if(null===$.value.minute)ue(e[0]);else{let l=(e.length+e.indexOf($.value.minute)+t)%e.length;ue(e[l])}}else{ue((60+(null===$.value.minute?-t:$.value.minute)+t)%60)}}}function re(e){if(13===e.keyCode)C.value="second";else if([37,39].includes(e.keyCode)){let t=37===e.keyCode?-1:1;if(null!==N.value){let e=N.value.values;if(0===e.length)return;if(null===$.value.seconds)de(e[0]);else{let l=(e.length+e.indexOf($.value.second)+t)%e.length;de(e[l])}}else{de((60+(null===$.value.second?-t:$.value.second)+t)%60)}}}function se(e){$.value.hour!==e&&($.value.hour=e,me())}function ue(e){$.value.minute!==e&&($.value.minute=e,me())}function de(e){$.value.second!==e&&($.value.second=e,me())}let ce={hour:se,minute:ue,second:de};function ve(){!1===M.value&&(M.value=!0,null!==$.value.hour&&($.value.hour-=12,me()))}function pe(){!0===M.value&&(M.value=!1,null!==$.value.hour&&($.value.hour+=12,me()))}function fe(e){let t=l.modelValue;C.value!==e&&null!=t&&""!==t&&"string"!=typeof t&&(C.value=e)}function me(){return null!==A.value&&!0!==A.value($.value.hour)?($.value=li(),void fe("hour")):null!==E.value&&!0!==E.value($.value.minute)?($.value.minute=null,$.value.second=null,void fe("minute")):!0===l.withSeconds&&null!==P.value&&!0!==P.value($.value.second)?($.value.second=null,void fe("second")):void(null===$.value.hour||null===$.value.minute||!0===l.withSeconds&&null===$.value.second||ge())}function ge(e){let t=Object.assign({...$.value},e),a="persian"===l.calendar?at(t.hour)+":"+at(t.minute)+(!0===l.withSeconds?":"+at(t.second):""):di(new Date(t.year,null===t.month?null:t.month-1,t.day,t.hour,t.minute,t.second,t.millisecond),_.value,S.value,t.year,t.timezoneOffset);t.changed=a!==l.modelValue,r("update:modelValue",a,t)}function he(){let e=[t("div",{class:"q-time__link "+("hour"===C.value?"q-time__link--active":"cursor-pointer"),tabindex:p.value,onClick:J.hour,onKeyup:ne},B.value.hour),t("div",":"),t("div",!0===V.value?{class:"q-time__link "+("minute"===C.value?"q-time__link--active":"cursor-pointer"),tabindex:p.value,onKeyup:ie,onClick:J.minute}:{class:"q-time__link"},B.value.minute)];!0===l.withSeconds&&e.push(t("div",":"),t("div",!0===O.value?{class:"q-time__link "+("second"===C.value?"q-time__link--active":"cursor-pointer"),tabindex:p.value,onKeyup:re,onClick:J.second}:{class:"q-time__link"},B.value.second));let a=[t("div",{class:"q-time__header-label row items-center no-wrap",dir:"ltr"},e)];return!1===L.value&&a.push(t("div",{class:"q-time__header-ampm column items-between no-wrap"},[t("div",{class:"q-time__link "+(!0===M.value?"q-time__link--active":"cursor-pointer"),tabindex:p.value,onClick:ve,onKeyup:te},"AM"),t("div",{class:"q-time__link "+(!0!==M.value?"q-time__link--active":"cursor-pointer"),tabindex:p.value,onClick:pe,onKeyup:le},"PM")])),t("div",{class:"q-time__header flex flex-center no-wrap "+h.value},a)}function be(){let e=$.value[C.value];return t("div",{class:"q-time__content col relative-position"},[t(f,{name:"q-transition--scale"},(()=>t("div",{key:"clock"+C.value,class:"q-time__container-parent absolute-full"},[t("div",{ref:x,class:"q-time__container-child fit overflow-hidden"},[g(t("div",{class:"q-time__clock cursor-pointer non-selectable",onClick:ae,onMousedown:oe},[t("div",{class:"q-time__clock-circle fit"},[t("div",{class:"q-time__clock-pointer"+(null===$.value[C.value]?" hidden":void 0!==l.color?` text-${l.color}`:""),style:z.value}),I.value.map((l=>t("div",{class:`q-time__clock-position row flex-center q-time__clock-pos-${l.index}`+(l.val===e?" q-time__clock-position--active "+h.value:!0===l.disable?" q-time__clock-position--disable":"")},[t("span",l.label)])))])]),j.value)])]))),!0===l.nowBtn?t($l,{class:"q-time__now-button absolute",icon:c.iconSet.datetime.now,unelevated:!0,size:"sm",round:!0,color:l.color,textColor:l.textColor,tabindex:p.value,onClick:D}):null])}return d.proxy.setNow=D,()=>{let e=[be()],a=pt(i.default);return void 0!==a&&e.push(t("div",{class:"q-time__actions"},a)),void 0!==l.name&&!0!==l.disable&&w(e,"push"),t("div",{class:T.value,tabindex:-1},[he(),t("div",{class:"q-time__main col overflow-auto"},e)])}}}),Kd=Ze({name:"QTimeline",props:{...Et,color:{type:String,default:"primary"},side:{type:String,default:"right",validator:e=>["left","right"].includes(e)},layout:{type:String,default:"dense",validator:e=>["dense","comfortable","loose"].includes(e)}},setup(e,{slots:l}){let n=a(),i=Pt(e,n.proxy.$q);q(Le,e);let r=o((()=>`q-timeline q-timeline--${e.layout} q-timeline--${e.layout}--${e.side}`+(!0===i.value?" q-timeline--dark":"")));return()=>t("ul",{class:r.value},pt(l.default))}}),Yd=Ze({name:"QTimelineEntry",props:{heading:Boolean,tag:{type:String,default:"h3"},side:{type:String,default:"right",validator:e=>["left","right"].includes(e)},icon:String,avatar:String,color:String,title:String,subtitle:String,body:String},setup(e,{slots:l}){let a=d(Le,Re);if(a===Re)return Re;let n=o((()=>`q-timeline__entry q-timeline__entry--${e.side}`+(void 0!==e.icon||void 0!==e.avatar?" q-timeline__entry--icon":""))),i=o((()=>`q-timeline__dot text-${e.color||a.color}`)),r=o((()=>"comfortable"===a.layout&&"left"===a.side));return()=>{let a,o=ft(l.default,[]);if(void 0!==e.body&&o.unshift(e.body),!0===e.heading){let l=[t("div"),t("div"),t(e.tag,{class:"q-timeline__heading-title"},o)];return t("div",{class:"q-timeline__heading"},!0===r.value?l.reverse():l)}void 0!==e.icon?a=[t(zt,{class:"row items-center justify-center",name:e.icon})]:void 0!==e.avatar&&(a=[t("img",{class:"q-timeline__dot-img",src:e.avatar})]);let s=[t("div",{class:"q-timeline__subtitle"},[t("span",{},pt(l.subtitle,[e.subtitle]))]),t("div",{class:i.value},a),t("div",{class:"q-timeline__content"},[t("h6",{class:"q-timeline__title"},pt(l.title,[e.title]))].concat(o))];return t("li",{class:n.value},!0===r.value?s.reverse():s)}}}),Xd=Ze({name:"QToolbar",props:{inset:Boolean},setup(e,{slots:l}){let a=o((()=>"q-toolbar row no-wrap items-center"+(!0===e.inset?" q-toolbar--inset":"")));return()=>t("div",{class:a.value,role:"toolbar"},pt(l.default))}}),Zd=Ze({name:"QToolbarTitle",props:{shrink:Boolean},setup(e,{slots:l}){let a=o((()=>"q-toolbar__title ellipsis"+(!0===e.shrink?" col-shrink":"")));return()=>t("div",{class:a.value},pt(l.default))}}),Gd=["none","strict","leaf","leaf-filtered"],Jd=Ze({name:"QTree",props:{...Et,nodes:{type:Array,required:!0},nodeKey:{type:String,required:!0},labelKey:{type:String,default:"label"},childrenKey:{type:String,default:"children"},dense:Boolean,color:String,controlColor:String,textColor:String,selectedColor:String,icon:String,tickStrategy:{type:String,default:"none",validator:e=>Gd.includes(e)},ticked:Array,expanded:Array,selected:{},noSelectionUnset:Boolean,defaultExpandAll:Boolean,accordion:Boolean,filter:String,filterMethod:Function,duration:Number,noConnectors:Boolean,noTransition:Boolean,noNodesLabel:String,noResultsLabel:String},emits:["update:expanded","update:ticked","update:selected","lazyLoad","afterShow","afterHide"],setup(l,{slots:i,emit:r}){let{proxy:u}=a(),{$q:d}=u,c=Pt(l,d),v=e({}),p=e(l.ticked||[]),f=e(l.expanded||[]),h={};s((()=>{h={}}));let b=o((()=>"q-tree q-tree--"+(!0===l.dense?"dense":"standard")+(!0===l.noConnectors?" q-tree--no-connectors":"")+(!0===c.value?" q-tree--dark":"")+(void 0!==l.color?` text-${l.color}`:""))),y=o((()=>void 0!==l.selected)),w=o((()=>l.icon||d.iconSet.tree.icon)),x=o((()=>l.controlColor||l.color)),_=o((()=>void 0!==l.textColor?` text-${l.textColor}`:"")),S=o((()=>{let e=l.selectedColor||l.color;return e?` text-${e}`:""})),k=o((()=>void 0!==l.filterMethod?l.filterMethod:(e,t)=>{let a=t.toLowerCase();return e[l.labelKey]&&-1!==e[l.labelKey].toLowerCase().indexOf(a)})),q=o((()=>{let e={},t=(a,o)=>{let n=a.tickStrategy||(o?o.tickStrategy:l.tickStrategy),i=a[l.nodeKey],r=a[l.childrenKey]&&Array.isArray(a[l.childrenKey])&&0!==a[l.childrenKey].length,s=!0!==a.disabled&&!0===y.value&&!1!==a.selectable,u=!0!==a.disabled&&!1!==a.expandable,d="none"!==n,c="strict"===n,m="leaf-filtered"===n,g="leaf"===n||"leaf-filtered"===n,h=!0!==a.disabled&&!1!==a.tickable;!0===g&&!0===h&&o&&!0!==o.tickable&&(h=!1);let b=a.lazy;!0===b&&void 0!==v.value[i]&&!0===Array.isArray(a[l.childrenKey])&&(b=v.value[i]);let w={key:i,parent:o,isParent:r,lazy:b,disabled:a.disabled,link:!0!==a.disabled&&(!0===s||!0===u&&(!0===r||!0===b)),children:[],matchesFilter:!l.filter||k.value(a,l.filter),selected:i===l.selected&&!0===s,selectable:s,expanded:!0===r&&f.value.includes(i),expandable:u,noTick:!0===a.noTick||!0!==c&&b&&"loaded"!==b,tickable:h,tickStrategy:n,hasTicking:d,strictTicking:c,leafFilteredTicking:m,leafTicking:g,ticked:(!0===c||!0!==r)&&p.value.includes(i)};if(e[i]=w,!0===r&&(w.children=a[l.childrenKey].map((e=>t(e,w))),l.filter&&(!0!==w.matchesFilter?w.matchesFilter=w.children.some((e=>e.matchesFilter)):!0!==w.noTick&&!0!==w.disabled&&!0===w.tickable&&!0===m&&!0===w.children.every((e=>!0!==e.matchesFilter||!0===e.noTick||!0!==e.tickable))&&(w.tickable=!1)),!0===w.matchesFilter&&(!0!==w.noTick&&!0!==c&&!0===w.children.every((e=>e.noTick))&&(w.noTick=!0),g))){if(w.ticked=!1,w.indeterminate=w.children.some((e=>!0===e.indeterminate)),w.tickable=!0===w.tickable&&w.children.some((e=>e.tickable)),!0!==w.indeterminate){let e=w.children.reduce(((e,t)=>!0===t.ticked?e+1:e),0);e===w.children.length?w.ticked=!0:e>0&&(w.indeterminate=!0)}!0===w.indeterminate&&(w.indeterminateNextState=w.children.every((e=>!0!==e.tickable||!0!==e.ticked)))}return w};return l.nodes.forEach((e=>t(e,null))),e}));function C(e){let t=[].reduce,a=(o,n)=>o||!n?o:!0===Array.isArray(n)?t.call(Object(n),a,o):n[l.nodeKey]===e?n:n[l.childrenKey]?a(null,n[l.childrenKey]):void 0;return a(null,l.nodes)}function $(){let e=[],t=a=>{a[l.childrenKey]&&0!==a[l.childrenKey].length&&!1!==a.expandable&&!0!==a.disabled&&(e.push(a[l.nodeKey]),a[l.childrenKey].forEach(t))};l.nodes.forEach(t),void 0!==l.expanded?r("update:expanded",e):f.value=e}function T(e,t,a=C(e),o=q.value[e]){if(o.lazy&&"loaded"!==o.lazy){if("loading"===o.lazy)return;v.value[e]="loading",!0!==Array.isArray(a[l.childrenKey])&&(a[l.childrenKey]=[]),r("lazyLoad",{node:a,key:e,done:t=>{v.value[e]="loaded",a[l.childrenKey]=!0===Array.isArray(t)?t:[],m((()=>{let t=q.value[e];t&&!0===t.isParent&&B(e,!0)}))},fail:()=>{delete v.value[e],0===a[l.childrenKey].length&&delete a[l.childrenKey]}})}else!0===o.isParent&&!0===o.expandable&&B(e,t)}function B(e,t){let a=f.value,o=void 0!==l.expanded;if(!0===o&&(a=a.slice()),t){if(l.accordion&&q.value[e]){let t=[];q.value[e].parent?q.value[e].parent.children.forEach((l=>{l.key!==e&&!0===l.expandable&&t.push(l.key)})):l.nodes.forEach((a=>{let o=a[l.nodeKey];o!==e&&t.push(o)})),0!==t.length&&(a=a.filter((e=>!1===t.includes(e))))}a=a.concat([e]).filter(((e,t,l)=>l.indexOf(e)===t))}else a=a.filter((t=>t!==e));!0===o?r("update:expanded",a):f.value=a}function L(e,t){let a=p.value,o=void 0!==l.ticked;!0===o&&(a=a.slice()),a=t?a.concat(e).filter(((e,t,l)=>l.indexOf(e)===t)):a.filter((t=>!1===e.includes(t))),!0===o&&r("update:ticked",a)}function z(e){return(l.filter?e.filter((e=>q.value[e[l.nodeKey]].matchesFilter)):e).map((e=>function(e){let a=e[l.nodeKey],o=q.value[a],n=e.header&&i[`header-${e.header}`]||i["default-header"],r=!0===o.isParent?z(e[l.childrenKey]):[],s=0!==r.length||o.lazy&&"loaded"!==o.lazy,d=e.body&&i[`body-${e.body}`]||i["default-body"],v=void 0!==n||void 0!==d?function(e,t,a){let o={tree:u,node:e,key:a,color:l.color,dark:c.value};return H(o,"expanded",(()=>t.expanded),(e=>{e!==t.expanded&&T(a,e)})),H(o,"ticked",(()=>t.ticked),(e=>{e!==t.ticked&&L([a],e)})),o}(e,o,a):null;return void 0!==d&&(d=t("div",{class:"q-tree__node-body relative-position"},[t("div",{class:_.value},[d(v)])])),t("div",{key:a,class:"q-tree__node relative-position q-tree__node--"+(!0===s?"parent":"child")},[t("div",{class:"q-tree__node-header relative-position row no-wrap items-center"+(!0===o.link?" q-tree__node--link q-hoverable q-focusable":"")+(!0===o.selected?" q-tree__node--selected":"")+(!0===o.disabled?" q-tree__node--disabled":""),tabindex:!0===o.link?0:-1,ariaExpanded:r.length>0?o.expanded:null,role:"treeitem",onClick:t=>{P(e,o,t)},onKeypress(t){!0!==ke(t)&&(13===t.keyCode?P(e,o,t,!0):32===t.keyCode&&F(e,o,t,!0))}},[t("div",{class:"q-focus-helper",tabindex:-1,ref:e=>{h[o.key]=e}}),"loading"===o.lazy?t(il,{class:"q-tree__spinner",color:x.value}):!0===s?t(zt,{class:"q-tree__arrow"+(!0===o.expanded?" q-tree__arrow--rotate":""),name:w.value,onClick(t){F(e,o,t)}}):null,!0===o.hasTicking&&!0!==o.noTick?t(Ao,{class:"q-tree__tickbox",modelValue:!0===o.indeterminate?null:o.ticked,color:x.value,dark:c.value,dense:!0,keepColor:!0,disable:!0!==o.tickable,onKeydown:ae,"onUpdate:modelValue":e=>{!function(e,t){if(!0===e.indeterminate&&(t=e.indeterminateNextState),e.strictTicking)L([e.key],t);else if(e.leafTicking){let l=[],a=e=>{e.isParent?(!0!==t&&!0!==e.noTick&&!0===e.tickable&&l.push(e.key),!0===e.leafTicking&&e.children.forEach(a)):!0!==e.noTick&&!0===e.tickable&&(!0!==e.leafFilteredTicking||!0===e.matchesFilter)&&l.push(e.key)};a(e),L(l,t)}}(o,e)}}):null,t("div",{class:"q-tree__node-header-content col row no-wrap items-center"+(!0===o.selected?S.value:_.value)},[n?n(v):[V(e),t("div",e[l.labelKey])]])]),!0===s?!0===l.noTransition?!0===o.expanded?t("div",{class:"q-tree__node-collapsible"+_.value,key:`${a}__q`},[d,t("div",{class:"q-tree__children"+(!0===o.disabled?" q-tree__node--disabled":""),role:"group"},r)]):null:t(ir,{duration:l.duration,onShow:O,onHide:A},(()=>g(t("div",{class:"q-tree__node-collapsible"+_.value,key:`${a}__q`},[d,t("div",{class:"q-tree__children"+(!0===o.disabled?" q-tree__node--disabled":""),role:"group"},r)]),[[M,o.expanded]]))):d])}(e)))}function V(e){if(void 0!==e.icon)return t(zt,{class:"q-tree__icon q-mr-sm",name:e.icon,color:e.iconColor});let l=e.img||e.avatar;return l?t("img",{class:`q-tree__${e.img?"img":"avatar"} q-mr-sm`,src:l}):void 0}function O(){r("afterShow")}function A(){r("afterHide")}function E(e){let t=h[e];t&&t.focus()}function P(e,t,a,o){!0!==o&&!1!==t.selectable&&E(t.key),y.value&&t.selectable?!1===l.noSelectionUnset?r("update:selected",t.key!==l.selected?t.key:null):t.key!==l.selected&&r("update:selected",void 0===t.key?null:t.key):F(e,t,a,o),"function"==typeof e.handler&&e.handler(e)}function F(e,t,l,a){void 0!==l&&ae(l),!0!==a&&!1!==t.selectable&&E(t.key),T(t.key,!t.expanded,e,t)}return n((()=>l.ticked),(e=>{p.value=e})),n((()=>l.expanded),(e=>{f.value=e})),!0===l.defaultExpandAll&&$(),Object.assign(u,{getNodeByKey:C,getTickedNodes:function(){return p.value.map((e=>C(e)))},getExpandedNodes:function(){return f.value.map((e=>C(e)))},isExpanded:function(e){return!(!e||!q.value[e])&&q.value[e].expanded},collapseAll:function(){void 0!==l.expanded?r("update:expanded",[]):f.value=[]},expandAll:$,setExpanded:T,isTicked:function(e){return!(!e||!q.value[e])&&q.value[e].ticked},setTicked:L}),()=>{let e=z(l.nodes);return t("div",{class:b.value,role:"tree"},0===e.length?l.filter?l.noResultsLabel||d.lang.tree.noResults:l.noNodesLabel||d.lang.tree.noNodes:e)}}});function ec(e){return(100*e).toFixed(2)+"%"}var tc={...Et,...Vr,label:String,color:String,textColor:String,square:Boolean,flat:Boolean,bordered:Boolean,noThumbnails:Boolean,autoUpload:Boolean,hideUploadBtn:Boolean,disable:Boolean,readonly:Boolean},lc=[...Or,"start","finish","added","removed"];var ac=()=>!0;function oc(e){let t={};return e.forEach((e=>{t[e]=ac})),t}var nc=oc(lc);function ic(e){return"function"==typeof e?e:()=>e}var rc={url:[Function,String],method:{type:[Function,String],default:"POST"},fieldName:{type:[Function,String],default:()=>e=>e.name},headers:[Function,Array],formFields:[Function,Array],withCredentials:[Function,Boolean],sendRaw:[Function,Boolean],batch:[Function,Boolean],factory:Function};var sc={name:"QUploader",props:rc,emits:["factoryFailed","uploaded","failed","uploading"],injectPlugin:function({props:t,emit:l,helpers:a}){let n,i=e([]),r=e([]),s=e(0),u=o((()=>({url:ic(t.url),method:ic(t.method),headers:ic(t.headers),formFields:ic(t.formFields),fieldName:ic(t.fieldName),withCredentials:ic(t.withCredentials),sendRaw:ic(t.sendRaw),batch:ic(t.batch)})));function d(e){if(s.value++,"function"!=typeof t.factory)return void c(e,{});let o=t.factory(e);if(o)if("function"==typeof o.catch&&"function"==typeof o.then){r.value.push(o);let t=t=>{!0===a.isAlive()&&(r.value=r.value.filter((e=>e!==o)),0===r.value.length&&(n=!1),a.queuedFiles.value=a.queuedFiles.value.concat(e),e.forEach((e=>{a.updateFileStatus(e,"failed")})),l("factoryFailed",t,e),s.value--)};o.then((l=>{!0===n?t(new Error("Aborted")):!0===a.isAlive()&&(r.value=r.value.filter((e=>e!==o)),c(e,l))})).catch(t)}else c(e,o||{});else l("factoryFailed",new Error("QUploader: factory() does not return properly"),e),s.value--}function c(e,t){let o=new FormData,n=new XMLHttpRequest,r=(e,l)=>void 0!==t[e]?ic(t[e])(l):u.value[e](l),d=r("url",e);if(!d)return void s.value--;let c=r("formFields",e);void 0!==c&&c.forEach((e=>{o.append(e.name,e.value)}));let v,p=0,f=0,m=0,g=0;n.upload.addEventListener("progress",(t=>{if(!0===v)return;let l=Math.min(g,t.loaded);a.uploadedSize.value+=l-m,m=l;let o=m-f;for(let n=p;o>0&&n<e.length;n++){let t=e[n];if(!(o>t.size))return void a.updateFileStatus(t,"uploading",o);o-=t.size,p++,f+=t.size,a.updateFileStatus(t,"uploading",t.size)}}),!1),n.onreadystatechange=()=>{n.readyState<4||(n.status&&n.status<400?(a.uploadedFiles.value=a.uploadedFiles.value.concat(e),e.forEach((e=>{a.updateFileStatus(e,"uploaded")})),l("uploaded",{files:e,xhr:n})):(v=!0,a.uploadedSize.value-=m,a.queuedFiles.value=a.queuedFiles.value.concat(e),e.forEach((e=>{a.updateFileStatus(e,"failed")})),l("failed",{files:e,xhr:n})),s.value--,i.value=i.value.filter((e=>e!==n)))},n.open(r("method",e),d),!0===r("withCredentials",e)&&(n.withCredentials=!0);let h=r("headers",e);void 0!==h&&h.forEach((e=>{n.setRequestHeader(e.name,e.value)}));let b=r("sendRaw",e);e.forEach((e=>{a.updateFileStatus(e,"uploading",0),!0!==b&&o.append(r("fieldName",e),e,e.name),e.xhr=n,e.__abort=()=>{n.abort()},g+=e.size})),l("uploading",{files:e,xhr:n}),i.value.push(n),!0===b?n.send(new Blob(e)):n.send(o)}return{isUploading:o((()=>s.value>0)),isBusy:o((()=>0!==r.value.length)),abort:function(){i.value.forEach((e=>{e.abort()})),0!==r.value.length&&(n=!0)},upload:function(){let e=a.queuedFiles.value.slice(0);a.queuedFiles.value=[],u.value.batch(e)?d(e):e.forEach((e=>{d([e])}))}}}},uc=(({name:l,props:r,emits:s,injectPlugin:u})=>Ze({name:l,props:{...tc,...r},emits:!0===je(s)?{...nc,...s}:[...lc,...s],setup:(l,{expose:r})=>function(l,r){let s=a(),{props:u,slots:d,emit:c,proxy:v}=s,{$q:p}=v,f=Pt(u,p),m=o((()=>!0!==u.disable&&!0!==u.readonly)),g=e(!1),h=e(null),b=e(null),y={files:e([]),queuedFiles:e([]),uploadedFiles:e([]),uploadedSize:e(0),updateFileStatus:function(e,t,l){if(e.__status=t,"idle"===t)return e.__uploaded=0,e.__progress=0,e.__sizeLabel=et(e.size),void(e.__progressLabel="0.00%");"failed"!==t?(e.__uploaded="uploaded"===t?e.size:l,e.__progress="uploaded"===t?1:Math.min(.9999,e.__uploaded/e.size),e.__progressLabel=ec(e.__progress),v.$forceUpdate()):v.$forceUpdate()},isAlive:()=>!1===Kt(s)},{pickFiles:w,addFiles:x,onDragover:_,onDragleave:S,processFiles:k,getDndNode:$,maxFilesNumber:M,maxTotalSizeNumber:T}=Ar({editable:m,dnd:g,getFileInput:U,addFilesToQueue:W});Object.assign(y,l({props:u,slots:d,emit:c,helpers:y,exposeApi:e=>{Object.assign(y,e)}})),void 0===y.isBusy&&(y.isBusy=e(!1));let B=e(0),L=o((()=>0===B.value?0:y.uploadedSize.value/B.value)),z=o((()=>ec(L.value))),V=o((()=>et(B.value))),O=o((()=>!0===m.value&&!0!==y.isUploading.value&&(!0===u.multiple||0===y.queuedFiles.value.length)&&(void 0===u.maxFiles||y.files.value.length<M.value)&&(void 0===u.maxTotalSize||B.value<T.value))),A=o((()=>!0===m.value&&!0!==y.isBusy.value&&!0!==y.isUploading.value&&0!==y.queuedFiles.value.length));q(Fe,X);let E=o((()=>"q-uploader column no-wrap"+(!0===f.value?" q-uploader--dark q-dark":"")+(!0===u.bordered?" q-uploader--bordered":"")+(!0===u.square?" q-uploader--square no-border-radius":"")+(!0===u.flat?" q-uploader--flat no-shadow":"")+(!0===u.disable?" disabled q-uploader--disable":"")+(!0===g.value?" q-uploader--dnd":""))),P=o((()=>"q-uploader__header"+(void 0!==u.color?` bg-${u.color}`:"")+(void 0!==u.textColor?` text-${u.textColor}`:"")));function F(){!1===u.disable&&(y.abort(),y.uploadedSize.value=0,B.value=0,Q(),y.files.value=[],y.queuedFiles.value=[],y.uploadedFiles.value=[])}function R(){!1===u.disable&&j(["uploaded"],(()=>{y.uploadedFiles.value=[]}))}function N(){j(["idle","failed"],(({size:e})=>{B.value-=e,y.queuedFiles.value=[]}))}function j(e,t){if(!0===u.disable)return;let l={files:[],size:0},a=y.files.value.filter((t=>-1===e.indexOf(t.__status)||(l.size+=t.size,l.files.push(t),void 0!==t.__img&&window.URL.revokeObjectURL(t.__img.src),!1)));0!==l.files.length&&(y.files.value=a,t(l),c("removed",l.files))}function D(e){u.disable||("uploaded"===e.__status?y.uploadedFiles.value=y.uploadedFiles.value.filter((t=>t.__key!==e.__key)):"uploading"===e.__status?e.__abort():B.value-=e.size,y.files.value=y.files.value.filter((t=>t.__key!==e.__key||(void 0!==t.__img&&window.URL.revokeObjectURL(t.__img.src),!1))),y.queuedFiles.value=y.queuedFiles.value.filter((t=>t.__key!==e.__key)),c("removed",[e]))}function Q(){y.files.value.forEach((e=>{void 0!==e.__img&&window.URL.revokeObjectURL(e.__img.src)}))}function U(){return b.value||h.value.getElementsByClassName("q-uploader__input")[0]}function W(e,t){let l=k(e,t,y.files.value,!0),a=U();null!=a&&(a.value=""),void 0!==l&&(l.forEach((e=>{if(y.updateFileStatus(e,"idle"),B.value+=e.size,!0!==u.noThumbnails&&e.type.toUpperCase().startsWith("IMAGE")){let t=new Image;t.src=window.URL.createObjectURL(e),e.__img=t}})),y.files.value=y.files.value.concat(l),y.queuedFiles.value=y.queuedFiles.value.concat(l),c("added",l),!0===u.autoUpload&&y.upload())}function K(){!0===A.value&&y.upload()}function Y(e,l,a){if(!0===e){let e,o={type:"a",key:l,icon:p.iconSet.uploader[l],flat:!0,dense:!0};return"add"===l?(o.onClick=w,e=X):o.onClick=a,t($l,o,e)}}function X(){return t("input",{ref:b,class:"q-uploader__input overflow-hidden absolute-full",tabindex:-1,type:"file",title:"",accept:u.accept,multiple:!0===u.multiple?"multiple":void 0,capture:u.capture,onMousedown:te,onClick:w,onChange:W})}n(y.isUploading,((e,t)=>{!1===t&&!0===e?c("start"):!0===t&&!1===e&&c("finish")})),i((()=>{!0===y.isUploading.value&&y.abort(),0!==y.files.value.length&&Q()}));let Z={};for(let e in y)!0===C(y[e])?H(Z,e,(()=>y[e].value)):Z[e]=y[e];return Object.assign(Z,{upload:K,reset:F,removeUploadedFiles:R,removeQueuedFiles:N,removeFile:D,pickFiles:w,addFiles:x}),I(Z,{canAddFiles:()=>O.value,canUpload:()=>A.value,uploadSizeLabel:()=>V.value,uploadProgressLabel:()=>z.value}),r({...y,upload:K,reset:F,removeUploadedFiles:R,removeQueuedFiles:N,removeFile:D,pickFiles:w,addFiles:x,canAddFiles:O,canUpload:A,uploadSizeLabel:V,uploadProgressLabel:z}),()=>{let e=[t("div",{class:P.value},void 0!==d.header?d.header(Z):[t("div",{class:"q-uploader__header-content column"},[t("div",{class:"flex flex-center no-wrap q-gutter-xs"},[Y(0!==y.queuedFiles.value.length,"removeQueue",N),Y(0!==y.uploadedFiles.value.length,"removeUploaded",R),!0===y.isUploading.value?t(il,{class:"q-uploader__spinner"}):null,t("div",{class:"col column justify-center"},[void 0!==u.label?t("div",{class:"q-uploader__title"},[u.label]):null,t("div",{class:"q-uploader__subtitle"},[V.value+" / "+z.value])]),Y(O.value,"add"),Y(!1===u.hideUploadBtn&&!0===A.value,"upload",y.upload),Y(y.isUploading.value,"clear",y.abort)])])]),t("div",{class:"q-uploader__list scroll"},void 0!==d.list?d.list(Z):y.files.value.map((e=>t("div",{key:e.__key,class:"q-uploader__file relative-position"+(!0!==u.noThumbnails&&void 0!==e.__img?" q-uploader__file--img":"")+("failed"===e.__status?" q-uploader__file--failed":"uploaded"===e.__status?" q-uploader__file--uploaded":""),style:!0!==u.noThumbnails&&void 0!==e.__img?{backgroundImage:'url("'+e.__img.src+'")'}:null},[t("div",{class:"q-uploader__file-header row flex-center no-wrap"},["failed"===e.__status?t(zt,{class:"q-uploader__file-status",name:p.iconSet.type.negative,color:"negative"}):null,t("div",{class:"q-uploader__file-header-content col"},[t("div",{class:"q-uploader__title"},[e.name]),t("div",{class:"q-uploader__subtitle row items-center no-wrap"},[e.__sizeLabel+" / "+e.__progressLabel])]),"uploading"===e.__status?t(Ho,{value:e.__progress,min:0,max:1,indeterminate:0===e.__progress}):t($l,{round:!0,dense:!0,flat:!0,icon:p.iconSet.uploader["uploaded"===e.__status?"done":"clear"],onClick:()=>{D(e)}})])])))),$("uploader")];!0===y.isBusy.value&&e.push(t("div",{class:"q-uploader__overlay absolute-full flex flex-center"},[t(il)]));let l={ref:h,class:E.value};return!0===O.value&&Object.assign(l,{onDragover:_,onDragleave:S}),t("div",l,e)}}(u,r)}))(sc),dc=Ze({name:"QUploaderAddTrigger",setup(){let e=d(Fe,Re);return e}}),cc=Ze({name:"QVideo",props:{...Ir,src:{type:String,required:!0},title:String,fetchpriority:{type:String,default:"auto"},loading:{type:String,default:"eager"},referrerpolicy:{type:String,default:"strict-origin-when-cross-origin"}},setup(e){let l=jr(e),a=o((()=>"q-video"+(void 0!==e.ratio?" q-video--responsive":"")));return()=>t("div",{class:a.value,style:l.value},[t("iframe",{src:e.src,title:e.title,fetchpriority:e.fetchpriority,loading:e.loading,referrerpolicy:e.referrerpolicy,frameborder:"0",allowfullscreen:!0})])}}),vc={};function pc(e){if(!1===e)return 0;if(!0===e||void 0===e)return 1;let t=parseInt(e,10);return isNaN(t)?0:t}P(vc,{ClosePopup:()=>mc,Intersection:()=>cs,Morph:()=>Pc,Mutation:()=>Hc,Ripple:()=>fl,Scroll:()=>Uc,ScrollFire:()=>Dc,TouchHold:()=>Wc,TouchPan:()=>Do,TouchRepeat:()=>Xc,TouchSwipe:()=>vo});var fc,mc=Ge({name:"close-popup",beforeMount(e,{value:t}){let l={depth:pc(t),handler(t){0!==l.depth&&setTimeout((()=>{let a=function(e){return Wl.find((t=>null!==t.contentEl&&t.contentEl.contains(e)))}(e);void 0!==a&&function(e,t,l){for(;0!==l&&null!=e;){if(!0===e.__qPortal){if(l--,"QMenu"===e.$options.name){e=Kl(e,t);continue}e.hide(t)}e=Dt(e)}}(a,t,l.depth)}))},handlerKey(e){!0===qe(e,13)&&l.handler(e)}};e.__qclosepopup=l,e.addEventListener("click",l.handler),e.addEventListener("keyup",l.handlerKey)},updated(e,{value:t,oldValue:l}){t!==l&&(e.__qclosepopup.depth=pc(t))},beforeUnmount(e){let t=e.__qclosepopup;e.removeEventListener("click",t.handler),e.removeEventListener("keyup",t.handlerKey),delete e.__qclosepopup}}),gc=0;function hc(e,t){void 0===fc&&((fc=document.createElement("div")).style.cssText="position: absolute; left: 0; top: 0",document.body.appendChild(fc));let l=e.getBoundingClientRect(),a=fc.getBoundingClientRect(),{marginLeft:o,marginRight:n,marginTop:i,marginBottom:r}=window.getComputedStyle(e),s=parseInt(o,10)+parseInt(n,10),u=parseInt(i,10)+parseInt(r,10);return{left:l.left-a.left,top:l.top-a.top,width:l.right-l.left,height:l.bottom-l.top,widthM:l.right-l.left+(!0===t?0:s),heightM:l.bottom-l.top+(!0===t?0:u),marginH:!0===t?s:0,marginV:!0===t?u:0}}function bc(e){return{width:e.scrollWidth,height:e.scrollHeight}}var yc=["Top","Right","Bottom","Left"],wc=["borderTopLeftRadius","borderTopRightRadius","borderBottomRightRadius","borderBottomLeftRadius"],xc=/-block|-inline|block-|inline-/,_c=/(-block|-inline|block-|inline-).*:/;function Sc(e,t){let l=window.getComputedStyle(e),a={};for(let o=0;o<t.length;o++){let e=t[o];if(""===l[e])if("cssText"===e){let t=l.length,o="";for(let e=0;e<t;e++)!0!==xc.test(l[e])&&(o+=l[e]+": "+l[l[e]]+"; ");a[e]=o}else if(-1!==["borderWidth","borderStyle","borderColor"].indexOf(e)){let t=e.replace("border",""),o="";for(let e=0;e<yc.length;e++){o+=l["border"+yc[e]+t]+" "}a[e]=o}else if("borderRadius"===e){let t="",o="";for(let e=0;e<wc.length;e++){let a=l[wc[e]].split(" ");t+=a[0]+" ",o+=(void 0===a[1]?a[0]:a[1])+" "}a[e]=t+"/ "+o}else a[e]=l[e];else a[e]="cssText"===e?l[e].split(";").filter((e=>!0!==_c.test(e))).join(";"):l[e]}return a}var kc=["absolute","fixed","relative","sticky"];function qc(e){let t=e,l=0;for(;null!==t&&t!==document;){let{position:a,zIndex:o}=window.getComputedStyle(t),n=Number(o);n>l&&(t===e||!0===kc.includes(a))&&(l=n),t=t.parentNode}return l}function Cc(e){let t=typeof e;return"function"===t?e():"string"===t?document.querySelector(e):e}function $c(e){return e&&e.ownerDocument===document&&null!==e.parentNode}function Mc(e){let t=()=>!1,l=!1,a=!0,o=function(e){return{from:e.from,to:void 0!==e.to?e.to:e.from}}(e),n=function(e){return"number"==typeof e?e={duration:e}:"function"==typeof e&&(e={onEnd:e}),{...e,waitFor:void 0===e.waitFor?0:e.waitFor,duration:!0===isNaN(e.duration)?300:parseInt(e.duration,10),easing:"string"==typeof e.easing&&0!==e.easing.length?e.easing:"ease-in-out",delay:!0===isNaN(e.delay)?0:parseInt(e.delay,10),fill:"string"==typeof e.fill&&0!==e.fill.length?e.fill:"none",resize:!0===e.resize,useCSS:!0===e.useCSS||!0===e.usecss,hideFromClone:!0===e.hideFromClone||!0===e.hidefromclone,keepToClone:!0===e.keepToClone||!0===e.keeptoclone,tween:!0===e.tween,tweenFromOpacity:!0===isNaN(e.tweenFromOpacity)?.6:parseFloat(e.tweenFromOpacity),tweenToOpacity:!0===isNaN(e.tweenToOpacity)?.5:parseFloat(e.tweenToOpacity)}}(e),i=Cc(o.from);if(!0!==$c(i))return t;"function"==typeof i.qMorphCancel&&i.qMorphCancel();let r,s,u,d,c=i.parentNode,v=i.nextElementSibling,p=hc(i,n.resize),{width:f,height:m}=bc(c),{borderWidth:g,borderStyle:h,borderColor:b,borderRadius:y,backgroundColor:w,transform:x,position:_,cssText:S}=Sc(i,["borderWidth","borderStyle","borderColor","borderRadius","backgroundColor","transform","position","cssText"]),k=i.classList.toString(),q=i.style.cssText,C=i.cloneNode(!0),$=!0===n.tween?i.cloneNode(!0):void 0;void 0!==$&&($.className=$.classList.toString().split(" ").filter((e=>!1===/^bg-/.test(e))).join(" ")),!0===n.hideFromClone&&C.classList.add("q-morph--internal"),C.setAttribute("aria-hidden","true"),C.style.transition="none",C.style.animation="none",C.style.pointerEvents="none",c.insertBefore(C,v),i.qMorphCancel=()=>{l=!0,C.remove(),void 0!==$&&$.remove(),!0===n.hideFromClone&&C.classList.remove("q-morph--internal"),i.qMorphCancel=void 0};return"function"==typeof e.onToggle&&e.onToggle(),requestAnimationFrame((()=>{let e=Cc(o.to);if(!0===l||!0!==$c(e))return void("function"==typeof i.qMorphCancel&&i.qMorphCancel());i!==e&&"function"==typeof e.qMorphCancel&&e.qMorphCancel(),!0!==n.keepToClone&&e.classList.add("q-morph--internal"),C.classList.add("q-morph--internal");let{width:v,height:M}=bc(c),{width:T,height:B}=bc(e.parentNode);!0!==n.hideFromClone&&C.classList.remove("q-morph--internal"),e.qMorphCancel=()=>{l=!0,C.remove(),void 0!==$&&$.remove(),!0===n.hideFromClone&&C.classList.remove("q-morph--internal"),!0!==n.keepToClone&&e.classList.remove("q-morph--internal"),i.qMorphCancel=void 0,e.qMorphCancel=void 0};let L=()=>{if(!0===l)return void("function"==typeof e.qMorphCancel&&e.qMorphCancel());!0!==n.hideFromClone&&(C.classList.add("q-morph--internal"),C.innerHTML="",C.style.left=0,C.style.right="unset",C.style.top=0,C.style.bottom="unset",C.style.transform="none"),!0!==n.keepToClone&&e.classList.remove("q-morph--internal");let o=e.parentNode,{width:L,height:z}=bc(o),V=e.cloneNode(n.keepToClone);V.setAttribute("aria-hidden","true"),!0!==n.keepToClone&&(V.style.left=0,V.style.right="unset",V.style.top=0,V.style.bottom="unset",V.style.transform="none",V.style.pointerEvents="none"),V.classList.add("q-morph--internal");let O=e===i&&c===o?C:e.nextElementSibling;o.insertBefore(V,O);let{borderWidth:A,borderStyle:E,borderColor:P,borderRadius:F,backgroundColor:R,transform:N,position:H,cssText:I}=Sc(e,["borderWidth","borderStyle","borderColor","borderRadius","backgroundColor","transform","position","cssText"]),j=e.classList.toString(),D=e.style.cssText;e.style.cssText=I,e.style.transform="none",e.style.animation="none",e.style.transition="none",e.className=j.split(" ").filter((e=>!1===/^bg-/.test(e))).join(" ");let Q=hc(e,n.resize),U=p.left-Q.left,W=p.top-Q.top,K=p.width/(Q.width>0?Q.width:10),Y=p.height/(Q.height>0?Q.height:100),X=f-v,Z=m-M,G=L-T,J=z-B,ee=Math.max(p.widthM,X),te=Math.max(p.heightM,Z),le=Math.max(Q.widthM,G),ae=Math.max(Q.heightM,J),oe=i===e&&!1===["absolute","fixed"].includes(H)&&!1===["absolute","fixed"].includes(_),ne="fixed"===H,ie=o;for(;!0!==ne&&ie!==document;)ne="fixed"===window.getComputedStyle(ie).position,ie=ie.parentNode;if(!0!==n.hideFromClone&&(C.style.display="block",C.style.flex="0 0 auto",C.style.opacity=0,C.style.minWidth="unset",C.style.maxWidth="unset",C.style.minHeight="unset",C.style.maxHeight="unset",C.classList.remove("q-morph--internal")),!0!==n.keepToClone&&(V.style.display="block",V.style.flex="0 0 auto",V.style.opacity=0,V.style.minWidth="unset",V.style.maxWidth="unset",V.style.minHeight="unset",V.style.maxHeight="unset"),V.classList.remove("q-morph--internal"),"string"==typeof n.classes&&(e.className+=" "+n.classes),"string"==typeof n.style)e.style.cssText+=" "+n.style;else if(!0===je(n.style))for(let t in n.style)e.style[t]=n.style[t];let re=qc(C),se=qc(e),ue=!0===ne?document.documentElement:{scrollLeft:0,scrollTop:0};e.style.position=!0===ne?"fixed":"absolute",e.style.left=Q.left-ue.scrollLeft+"px",e.style.right="unset",e.style.top=Q.top-ue.scrollTop+"px",e.style.margin=0,!0===n.resize&&(e.style.minWidth="unset",e.style.maxWidth="unset",e.style.minHeight="unset",e.style.maxHeight="unset",e.style.overflow="hidden",e.style.overflowX="hidden",e.style.overflowY="hidden"),document.body.appendChild(e),void 0!==$&&($.style.cssText=S,$.style.transform="none",$.style.animation="none",$.style.transition="none",$.style.position=e.style.position,$.style.left=p.left-ue.scrollLeft+"px",$.style.right="unset",$.style.top=p.top-ue.scrollTop+"px",$.style.margin=0,$.style.pointerEvents="none",!0===n.resize&&($.style.minWidth="unset",$.style.maxWidth="unset",$.style.minHeight="unset",$.style.maxHeight="unset",$.style.overflow="hidden",$.style.overflowX="hidden",$.style.overflowY="hidden"),document.body.appendChild($));let de=l=>{i===e&&!0!==a?(e.style.cssText=q,e.className=k):(e.style.cssText=D,e.className=j),V.parentNode===o&&o.insertBefore(e,V),C.remove(),V.remove(),void 0!==$&&$.remove(),t=()=>!1,i.qMorphCancel=void 0,e.qMorphCancel=void 0,"function"==typeof n.onEnd&&n.onEnd(!0===a?"to":"from",!0===l)};if(!0!==n.useCSS&&"function"==typeof e.animate){let o=!0===n.resize?{transform:`translate(${U}px, ${W}px)`,width:`${ee}px`,height:`${te}px`}:{transform:`translate(${U}px, ${W}px) scale(${K}, ${Y})`},c=!0===n.resize?{width:`${le}px`,height:`${ae}px`}:{},v=!0===n.resize?{width:`${ee}px`,height:`${te}px`}:{},f=!0===n.resize?{transform:`translate(${-1*U}px, ${-1*W}px)`,width:`${le}px`,height:`${ae}px`}:{transform:`translate(${-1*U}px, ${-1*W}px) scale(${1/K}, ${1/Y})`},m=void 0!==$?{opacity:n.tweenToOpacity}:{backgroundColor:w},_=void 0!==$?{opacity:1}:{backgroundColor:R};d=e.animate([{margin:0,borderWidth:g,borderStyle:h,borderColor:b,borderRadius:y,zIndex:re,transformOrigin:"0 0",...o,...m},{margin:0,borderWidth:A,borderStyle:E,borderColor:P,borderRadius:F,zIndex:se,transformOrigin:"0 0",transform:N,...c,..._}],{duration:n.duration,easing:n.easing,fill:n.fill,delay:n.delay}),s=void 0===$?void 0:$.animate([{opacity:n.tweenFromOpacity,margin:0,borderWidth:g,borderStyle:h,borderColor:b,borderRadius:y,zIndex:re,transformOrigin:"0 0",transform:x,...v},{opacity:0,margin:0,borderWidth:A,borderStyle:E,borderColor:P,borderRadius:F,zIndex:se,transformOrigin:"0 0",...f}],{duration:n.duration,easing:n.easing,fill:n.fill,delay:n.delay}),r=!0===n.hideFromClone||!0===oe?void 0:C.animate([{margin:`${Z<0?Z/2:0}px ${X<0?X/2:0}px`,width:`${ee+p.marginH}px`,height:`${te+p.marginV}px`},{margin:0,width:0,height:0}],{duration:n.duration,easing:n.easing,fill:n.fill,delay:n.delay}),u=!0===n.keepToClone?void 0:V.animate([!0===oe?{margin:`${Z<0?Z/2:0}px ${X<0?X/2:0}px`,width:`${ee+p.marginH}px`,height:`${te+p.marginV}px`}:{margin:0,width:0,height:0},{margin:`${J<0?J/2:0}px ${G<0?G/2:0}px`,width:`${le+Q.marginH}px`,height:`${ae+Q.marginV}px`}],{duration:n.duration,easing:n.easing,fill:n.fill,delay:n.delay});let S=e=>{void 0!==r&&r.cancel(),void 0!==s&&s.cancel(),void 0!==u&&u.cancel(),d.cancel(),d.removeEventListener("finish",S),d.removeEventListener("cancel",S),de(e),r=void 0,s=void 0,u=void 0,d=void 0};i.qMorphCancel=()=>{i.qMorphCancel=void 0,l=!0,S()},e.qMorphCancel=()=>{e.qMorphCancel=void 0,l=!0,S()},d.addEventListener("finish",S),d.addEventListener("cancel",S),t=e=>!0!==l&&void 0!==d&&(!0===e?(S(!0),!0):(a=!0!==a,void 0!==r&&r.reverse(),void 0!==s&&s.reverse(),void 0!==u&&u.reverse(),d.reverse(),!0))}else{let o="q-morph-anim-"+ ++gc,r=document.createElement("style"),s=!0===n.resize?`\n            transform: translate(${U}px, ${W}px);\n            width: ${ee}px;\n            height: ${te}px;\n          `:`transform: translate(${U}px, ${W}px) scale(${K}, ${Y});`,u=!0===n.resize?`\n            width: ${le}px;\n            height: ${ae}px;\n          `:"",d=!0===n.resize?`\n            width: ${ee}px;\n            height: ${te}px;\n          `:"",c=!0===n.resize?`\n            transform: translate(${-1*U}px, ${-1*W}px);\n            width: ${le}px;\n            height: ${ae}px;\n          `:`transform: translate(${-1*U}px, ${-1*W}px) scale(${1/K}, ${1/Y});`,v=void 0!==$?`opacity: ${n.tweenToOpacity};`:`background-color: ${w};`,f=void 0!==$?"opacity: 1;":`background-color: ${R};`,m=void 0===$?"":`\n            @keyframes ${o}-from-tween {\n              0% {\n                opacity: ${n.tweenFromOpacity};\n                margin: 0;\n                border-width: ${g};\n                border-style: ${h};\n                border-color: ${b};\n                border-radius: ${y};\n                z-index: ${re};\n                transform-origin: 0 0;\n                transform: ${x};\n                ${d}\n              }\n\n              100% {\n                opacity: 0;\n                margin: 0;\n                border-width: ${A};\n                border-style: ${E};\n                border-color: ${P};\n                border-radius: ${F};\n                z-index: ${se};\n                transform-origin: 0 0;\n                ${c}\n              }\n            }\n          `,_=!0===n.hideFromClone||!0===oe?"":`\n            @keyframes ${o}-from {\n              0% {\n                margin: ${Z<0?Z/2:0}px ${X<0?X/2:0}px;\n                width: ${ee+p.marginH}px;\n                height: ${te+p.marginV}px;\n              }\n\n              100% {\n                margin: 0;\n                width: 0;\n                height: 0;\n              }\n            }\n          `,S=!0===oe?`\n            margin: ${Z<0?Z/2:0}px ${X<0?X/2:0}px;\n            width: ${ee+p.marginH}px;\n            height: ${te+p.marginV}px;\n          `:"\n            margin: 0;\n            width: 0;\n            height: 0;\n          ",k=!0===n.keepToClone?"":`\n            @keyframes ${o}-to {\n              0% {\n                ${S}\n              }\n\n              100% {\n                margin: ${J<0?J/2:0}px ${G<0?G/2:0}px;\n                width: ${le+Q.marginH}px;\n                height: ${ae+Q.marginV}px;\n              }\n            }\n          `;r.innerHTML=`\n          @keyframes ${o} {\n            0% {\n              margin: 0;\n              border-width: ${g};\n              border-style: ${h};\n              border-color: ${b};\n              border-radius: ${y};\n              background-color: ${w};\n              z-index: ${re};\n              transform-origin: 0 0;\n              ${s}\n              ${v}\n            }\n\n            100% {\n              margin: 0;\n              border-width: ${A};\n              border-style: ${E};\n              border-color: ${P};\n              border-radius: ${F};\n              background-color: ${R};\n              z-index: ${se};\n              transform-origin: 0 0;\n              transform: ${N};\n              ${u}\n              ${f}\n            }\n          }\n\n          ${_}\n\n          ${m}\n\n          ${k}\n        `,document.head.appendChild(r);let q="normal";C.style.animation=`${n.duration}ms ${n.easing} ${n.delay}ms ${q} ${n.fill} ${o}-from`,void 0!==$&&($.style.animation=`${n.duration}ms ${n.easing} ${n.delay}ms ${q} ${n.fill} ${o}-from-tween`),V.style.animation=`${n.duration}ms ${n.easing} ${n.delay}ms ${q} ${n.fill} ${o}-to`,e.style.animation=`${n.duration}ms ${n.easing} ${n.delay}ms ${q} ${n.fill} ${o}`;let M=t=>{t===Object(t)&&t.animationName!==o||(e.removeEventListener("animationend",M),e.removeEventListener("animationcancel",M),de(),r.remove())};i.qMorphCancel=()=>{i.qMorphCancel=void 0,l=!0,M()},e.qMorphCancel=()=>{e.qMorphCancel=void 0,l=!0,M()},e.addEventListener("animationend",M),e.addEventListener("animationcancel",M),t=t=>!!(!0!==l&&e&&C&&V)&&(!0===t?(M(),!0):(a=!0!==a,q="normal"===q?"reverse":"normal",C.style.animationDirection=q,$.style.animationDirection=q,V.style.animationDirection=q,e.style.animationDirection=q,!0))}};n.waitFor>0||"transitionend"===n.waitFor||n.waitFor===Object(n.waitFor)&&"function"==typeof n.waitFor.then?(n.waitFor>0?new Promise((e=>setTimeout(e,n.waitFor))):"transitionend"===n.waitFor?new Promise((t=>{let l=()=>{null!==a&&(clearTimeout(a),a=null),e&&(e.removeEventListener("transitionend",l),e.removeEventListener("transitioncancel",l)),t()},a=setTimeout(l,400);e.addEventListener("transitionend",l),e.addEventListener("transitioncancel",l)})):n.waitFor).then(L).catch((()=>{"function"==typeof e.qMorphCancel&&e.qMorphCancel()})):L()})),e=>t(e)}var Tc={},Bc=["duration","delay","easing","fill","classes","style","duration","resize","useCSS","hideFromClone","keepToClone","tween","tweenFromOpacity","tweenToOpacity","waitFor","onEnd"],Lc=["resize","useCSS","hideFromClone","keepToClone","tween"];function zc(e,t){e.clsAction!==t&&(e.clsAction=t,e.el.classList[t]("q-morph--invisible"))}function Vc(e){if(!0===e.animating||e.queue.length<2)return;let[t,l]=e.queue;e.animating=!0,t.animating=!0,l.animating=!0,zc(t,"remove"),zc(l,"remove");let a=Mc({from:t.el,to:l.el,onToggle(){zc(t,"add"),zc(l,"remove")},...l.opts,onEnd(a,o){void 0!==l.opts.onEnd&&l.opts.onEnd(a,o),!0!==o&&(t.animating=!1,l.animating=!1,e.animating=!1,e.cancel=void 0,e.queue.shift(),Vc(e))}});e.cancel=()=>{a(!0),e.cancel=void 0}}function Oc(e,t){let l=t.opts;Lc.forEach((t=>{l[t]=!0===e[t]}))}function Ac(e,t){if(t.name!==e)!1===t.animating&&zc(t,"add");else{let l=Tc[t.group];void 0===l?(Tc[t.group]={name:t.group,model:e,queue:[t],animating:!1},zc(t,"remove")):l.model!==e&&(l.model=e,l.queue.push(t),!1===l.animating&&2===l.queue.length&&Vc(l))}}function Ec(e,t){let l;Object(t)===t?(l=""+t.model,function(e,t){void 0!==e.group&&(t.group=e.group),void 0!==e.name&&(t.name=e.name);let l=t.opts;Bc.forEach((t=>{void 0!==e[t]&&(l[t]=e[t])}))}(t,e),Oc(t,e)):l=""+t,l!==e.model?(e.model=l,Ac(l,e)):!1===e.animating&&void 0!==e.clsAction&&e.el.classList[e.clsAction]("q-morph--invisible")}var Pc=Ge({name:"morph",mounted(e,t){let l={el:e,animating:!1,opts:{}};Oc(t.modifiers,l),function(e,t){let l="string"==typeof e&&0!==e.length?e.split(":"):[];t.name=l[0],t.group=l[1],Object.assign(t.opts,{duration:!0===isNaN(l[2])?300:parseFloat(l[2]),waitFor:l[3]})}(t.arg,l),Ec(l,t.value),e.__qmorph=l},updated(e,t){Ec(e.__qmorph,t.value)},beforeUnmount(e){let t=e.__qmorph,l=Tc[t.group];void 0!==l&&-1!==l.queue.indexOf(t)&&(l.queue=l.queue.filter((e=>e!==t)),0===l.queue.length&&(void 0!==l.cancel&&l.cancel(),delete Tc[t.group])),"add"===t.clsAction&&e.classList.remove("q-morph--invisible"),delete e.__qmorph}}),Fc={childList:!0,subtree:!0,attributes:!0,characterData:!0,attributeOldValue:!0,characterDataOldValue:!0};function Rc(e,t,l){t.handler=l,void 0!==t.observer&&t.observer.disconnect(),t.observer=new MutationObserver((l=>{"function"==typeof t.handler&&(!1===t.handler(l)||!0===t.once)&&Nc(e)})),t.observer.observe(e,t.opts)}function Nc(e){let t=e.__qmutation;void 0!==t&&(void 0!==t.observer&&t.observer.disconnect(),delete e.__qmutation)}var Hc=Ge({name:"mutation",mounted(e,{modifiers:{once:t,...l},value:a}){let o={once:t,opts:0===Object.keys(l).length?Fc:l};Rc(e,o,a),e.__qmutation=o},updated(e,{oldValue:t,value:l}){let a=e.__qmutation;void 0!==a&&t!==l&&Rc(e,a,l)},beforeUnmount:Nc}),{passive:Ic}=Z;function jc(e,{value:t,oldValue:l}){"function"==typeof t?(e.handler=t,"function"!=typeof l&&(e.scrollTarget.addEventListener("scroll",e.scroll,Ic),e.scroll())):e.scrollTarget.removeEventListener("scroll",e.scroll,Ic)}var Dc=Ge({name:"scroll-fire",mounted(e,t){let l={scrollTarget:la(e),scroll:re((()=>{let t,a;l.scrollTarget===window?(a=e.getBoundingClientRect().bottom,t=window.innerHeight):(a=rl(e).top+sl(e),t=rl(l.scrollTarget).top+sl(l.scrollTarget)),a>0&&a<t&&(l.scrollTarget.removeEventListener("scroll",l.scroll,Ic),l.handler(e))}),25)};jc(l,t),e.__qscrollfire=l},updated(e,t){t.value!==t.oldValue&&jc(e.__qscrollfire,t)},beforeUnmount(e){let t=e.__qscrollfire;t.scrollTarget.removeEventListener("scroll",t.scroll,Ic),t.scroll.cancel(),delete e.__qscrollfire}});function Qc(e,{value:t,oldValue:l}){"function"==typeof t?(e.handler=t,"function"!=typeof l&&e.scrollTarget.addEventListener("scroll",e.scroll,Z.passive)):e.scrollTarget.removeEventListener("scroll",e.scroll,Z.passive)}var Uc=Ge({name:"scroll",mounted(e,t){let l={scrollTarget:la(e),scroll(){l.handler(oa(l.scrollTarget),na(l.scrollTarget))}};Qc(l,t),e.__qscroll=l},updated(e,t){void 0!==e.__qscroll&&t.oldValue!==t.value&&Qc(e.__qscroll,t)},beforeUnmount(e){let t=e.__qscroll;t.scrollTarget.removeEventListener("scroll",t.scroll,Z.passive),delete e.__qscroll}}),Wc=Ge({name:"touch-hold",beforeMount(e,t){let{modifiers:l}=t;if(!0!==l.mouse&&!0!==W.has.touch)return;let a={handler:t.value,noop:G,mouseStart(e){"function"==typeof a.handler&&!0===J(e)&&(ne(a,"temp",[[document,"mousemove","move","passiveCapture"],[document,"click","end","notPassiveCapture"]]),a.start(e,!0))},touchStart(e){if(void 0!==e.target&&"function"==typeof a.handler){let t=e.target;ne(a,"temp",[[t,"touchmove","move","passiveCapture"],[t,"touchcancel","end","notPassiveCapture"],[t,"touchend","end","notPassiveCapture"]]),a.start(e)}},start(e,t){a.origin=ee(e);let l=Date.now();!0===W.is.mobile&&(document.body.classList.add("non-selectable"),Tl(),a.styleCleanup=e=>{a.styleCleanup=void 0;let t=()=>{document.body.classList.remove("non-selectable")};!0===e?(Tl(),setTimeout(t,10)):t()}),a.triggered=!1,a.sensitivity=!0===t?a.mouseSensitivity:a.touchSensitivity,a.timer=setTimeout((()=>{a.timer=void 0,Tl(),a.triggered=!0,a.handler({evt:e,touch:!0!==t,mouse:!0===t,position:a.origin,duration:Date.now()-l})}),a.duration)},move(e){let{top:t,left:l}=ee(e);void 0!==a.timer&&(Math.abs(l-a.origin.left)>=a.sensitivity||Math.abs(t-a.origin.top)>=a.sensitivity)&&(clearTimeout(a.timer),a.timer=void 0)},end(e){ie(a,"temp"),void 0!==a.styleCleanup&&a.styleCleanup(a.triggered),!0===a.triggered?void 0!==e&&ae(e):void 0!==a.timer&&(clearTimeout(a.timer),a.timer=void 0)}},o=[600,5,7];if("string"==typeof t.arg&&0!==t.arg.length&&t.arg.split(":").forEach(((e,t)=>{let l=parseInt(e,10);l&&(o[t]=l)})),[a.duration,a.touchSensitivity,a.mouseSensitivity]=o,e.__qtouchhold=a,!0===l.mouse){let t=!0===l.mouseCapture||!0===l.mousecapture?"Capture":"";ne(a,"main",[[e,"mousedown","mouseStart",`passive${t}`]])}!0===W.has.touch&&ne(a,"main",[[e,"touchstart","touchStart","passive"+(!0===l.capture?"Capture":"")],[e,"touchend","noop","notPassiveCapture"]])},updated(e,t){let l=e.__qtouchhold;void 0!==l&&t.oldValue!==t.value&&("function"!=typeof t.value&&l.end(),l.handler=t.value)},beforeUnmount(e){let t=e.__qtouchhold;void 0!==t&&(ie(t,"main"),ie(t,"temp"),void 0!==t.timer&&clearTimeout(t.timer),void 0!==t.styleCleanup&&t.styleCleanup(),delete e.__qtouchhold)}}),Kc={esc:27,tab:9,enter:13,space:32,up:38,left:37,right:39,down:40,delete:[8,46]},Yc=new RegExp(`^([\\d+]+|${Object.keys(Kc).join("|")})$`,"i");var Xc=Ge({name:"touch-repeat",beforeMount(e,{modifiers:t,value:l,arg:a}){let o=Object.keys(t).reduce(((e,t)=>{if(!0===Yc.test(t)){let l=isNaN(parseInt(t,10))?Kc[t.toLowerCase()]:parseInt(t,10);l>=0&&e.push(l)}return e}),[]);if(!0!==t.mouse&&!0!==W.has.touch&&0===o.length)return;let n="string"==typeof a&&0!==a.length?a.split(":").map((e=>parseInt(e,10))):[0,600,300],i=n.length-1,r={keyboard:o,handler:l,noop:G,mouseStart(e){void 0===r.event&&"function"==typeof r.handler&&!0===J(e)&&(ne(r,"temp",[[document,"mousemove","move","passiveCapture"],[document,"click","end","notPassiveCapture"]]),r.start(e,!0))},keyboardStart(t){if("function"==typeof r.handler&&!0===qe(t,o)){if((0===n[0]||void 0!==r.event)&&(ae(t),e.focus(),void 0!==r.event))return;ne(r,"temp",[[document,"keyup","end","notPassiveCapture"],[document,"click","end","notPassiveCapture"]]),r.start(t,!1,!0)}},touchStart(e){if(void 0!==e.target&&"function"==typeof r.handler){let t=e.target;ne(r,"temp",[[t,"touchmove","move","passiveCapture"],[t,"touchcancel","end","notPassiveCapture"],[t,"touchend","end","notPassiveCapture"]]),r.start(e)}},start(e,t,l){function a(e){r.styleCleanup=void 0,document.documentElement.style.cursor="";let t=()=>{document.body.classList.remove("non-selectable")};!0===e?(Tl(),setTimeout(t,10)):t()}!0!==l&&(r.origin=ee(e)),!0===W.is.mobile&&(document.body.classList.add("non-selectable"),Tl(),r.styleCleanup=a),r.event={touch:!0!==t&&!0!==l,mouse:!0===t,keyboard:!0===l,startTime:Date.now(),repeatCount:0};let o=()=>{if(r.timer=void 0,void 0===r.event)return;0===r.event.repeatCount&&(r.event.evt=e,!0===l?r.event.keyCode=e.keyCode:r.event.position=ee(e),!0!==W.is.mobile&&(document.documentElement.style.cursor="pointer",document.body.classList.add("non-selectable"),Tl(),r.styleCleanup=a)),r.event.duration=Date.now()-r.event.startTime,r.event.repeatCount+=1,r.handler(r.event);let t=i<r.event.repeatCount?i:r.event.repeatCount;r.timer=setTimeout(o,n[t])};0===n[0]?o():r.timer=setTimeout(o,n[0])},move(e){void 0!==r.event&&void 0!==r.timer&&!0===function(e,t){let{top:l,left:a}=ee(e);return Math.abs(a-t.left)>=7||Math.abs(l-t.top)>=7}(e,r.origin)&&(clearTimeout(r.timer),r.timer=void 0)},end(e){void 0!==r.event&&(void 0!==r.styleCleanup&&r.styleCleanup(!0),void 0!==e&&r.event.repeatCount>0&&ae(e),ie(r,"temp"),void 0!==r.timer&&(clearTimeout(r.timer),r.timer=void 0),r.event=void 0)}};if(e.__qtouchrepeat=r,!0===t.mouse){let l=!0===t.mouseCapture||!0===t.mousecapture?"Capture":"";ne(r,"main",[[e,"mousedown","mouseStart",`passive${l}`]])}if(!0===W.has.touch&&ne(r,"main",[[e,"touchstart","touchStart","passive"+(!0===t.capture?"Capture":"")],[e,"touchend","noop","passiveCapture"]]),0!==o.length){let l=!0===t.keyCapture||!0===t.keycapture?"Capture":"";ne(r,"main",[[e,"keydown","keyboardStart",`notPassive${l}`]])}},updated(e,{oldValue:t,value:l}){let a=e.__qtouchrepeat;void 0!==a&&t!==l&&("function"!=typeof l&&a.end(),a.handler=l)},beforeUnmount(e){let t=e.__qtouchrepeat;void 0!==t&&(void 0!==t.timer&&clearTimeout(t.timer),ie(t,"main"),ie(t,"temp"),void 0!==t.styleCleanup&&t.styleCleanup(),delete e.__qtouchrepeat)}});!0!==W.is.mobile||!0!==W.is.nativeMobile&&!0!==W.is.winphone&&!0!==W.is.safari&&!0!==W.is.webkit&&W.is.vivaldi;var Zc={};function Gc(){return document.fullscreenElement||document.mozFullScreenElement||document.webkitFullscreenElement||document.msFullscreenElement||null}function Jc(){let e=av.activeEl=!1===av.isActive?null:Gc();!function(e){if(e===Dl)return;if((Dl=e)===document.body||Il.reduce(((e,t)=>"dialog"===t?e+1:e),0)<2)return void Hl.forEach((e=>{!1===e.contains(Dl)&&Dl.appendChild(e)}));let t=Il.lastIndexOf("dialog");for(let l=0;l<Hl.length;l++){let e=Hl[l];(l===t||"dialog"!==Il[l])&&!1===e.contains(Dl)&&Dl.appendChild(e)}}(null===e||e===document.documentElement?document.body:e)}function ev(){av.isActive=!1===av.isActive,Jc()}function tv(e,t){try{let l=e[t]();return void 0===l?Promise.resolve():l}catch(l){return Promise.reject(l)}}var lv,av=X({isActive:!1,activeEl:null},{isCapable:!1,install({$q:e}){e.fullscreen=this}});Zc.request=["requestFullscreen","msRequestFullscreen","mozRequestFullScreen","webkitRequestFullscreen"].find((e=>void 0!==document.documentElement[e])),av.isCapable=void 0!==Zc.request,!1===av.isCapable?(lv=()=>Promise.reject("Not capable"),Object.assign(av,{request:lv,exit:lv,toggle:lv})):(Object.assign(av,{request(e){let t=e||document.documentElement,{activeEl:l}=av;return t===l?Promise.resolve():(null!==l&&!0===t.contains(l)?av.exit():Promise.resolve()).finally((()=>tv(t,Zc.request)))},exit:()=>!0===av.isActive?tv(document,Zc.exit):Promise.resolve(),toggle:e=>!0===av.isActive?av.exit():av.request(e)}),Zc.exit=["exitFullscreen","msExitFullscreen","mozCancelFullScreen","webkitExitFullscreen"].find((e=>document[e])),av.isActive=!!Gc(),!0===av.isActive&&Jc(),["onfullscreenchange","onmsfullscreenchange","onwebkitfullscreenchange"].forEach((e=>{document[e]=ev})));var ov=X({appVisible:!0},{install({$q:e}){H(e,"appVisible",(()=>this.appVisible))}});{let e,t;if(typeof document.hidden<"u"?(e="hidden",t="visibilitychange"):typeof document.msHidden<"u"?(e="msHidden",t="msvisibilitychange"):typeof document.webkitHidden<"u"&&(e="webkitHidden",t="webkitvisibilitychange"),t&&typeof document[e]<"u"){let l=()=>{ov.appVisible=!document[e]};document.addEventListener(t,l,!1)}}function nv(e){return encodeURIComponent(e)}function iv(e){return decodeURIComponent(e)}function rv(e){if(""===e)return e;0===e.indexOf('"')&&(e=e.slice(1,-1).replace(/\\"/g,'"').replace(/\\\\/g,"\\")),e=iv(e.replace(/\+/g," "));try{let t=JSON.parse(e);(t===Object(t)||!0===Array.isArray(t))&&(e=t)}catch{}return e}function sv(e){let t=new Date;return t.setMilliseconds(t.getMilliseconds()+e),t.toUTCString()}function uv(e,t,l={},a){let o,n;void 0!==l.expires&&("[object Date]"===Object.prototype.toString.call(l.expires)?o=l.expires.toUTCString():"string"==typeof l.expires?o=function(e){let t=0,l=e.match(/(\d+)d/),a=e.match(/(\d+)h/),o=e.match(/(\d+)m/),n=e.match(/(\d+)s/);return l&&(t+=864e5*l[1]),a&&(t+=36e5*a[1]),o&&(t+=6e4*o[1]),n&&(t+=1e3*n[1]),0===t?e:sv(t)}(l.expires):(n=parseFloat(l.expires),o=!1===isNaN(n)?sv(864e5*n):l.expires));let i=`${nv(e)}=${function(e){return nv(e===Object(e)?JSON.stringify(e):""+e)}(t)}`,r=[i,void 0!==o?"; Expires="+o:"",l.path?"; Path="+l.path:"",l.domain?"; Domain="+l.domain:"",l.sameSite?"; SameSite="+l.sameSite:"",l.httpOnly?"; HttpOnly":"",l.secure?"; Secure":"",l.other?"; "+l.other:""].join("");if(a){a.req.qCookies?a.req.qCookies.push(r):a.req.qCookies=[r],a.res.setHeader("Set-Cookie",a.req.qCookies);let t=a.req.headers.cookie||"";if(void 0!==o&&n<0){let l=dv(e,a);void 0!==l&&(t=t.replace(`${e}=${l}; `,"").replace(`; ${e}=${l}`,"").replace(`${e}=${l}`,""))}else t=t?`${i}; ${t}`:r;a.req.headers.cookie=t}else document.cookie=r}function dv(e,t){let l,a,o,n=t?t.req.headers:document,i=n.cookie?n.cookie.split("; "):[],r=i.length,s=e?null:{},u=0;for(;u<r;u++)if(l=i[u].split("="),a=iv(l.shift()),o=l.join("="),e){if(e===a){s=rv(o);break}}else s[a]=o;return s}Ze({name:"BottomSheetPlugin",props:{...Et,title:String,message:String,actions:Array,grid:Boolean,cardClass:[String,Array,Object],cardStyle:[String,Array,Object]},emits:["ok","hide"],setup(l,{emit:o}){let{proxy:n}=a(),i=Pt(l,n.$q),r=e(null);function s(){r.value.hide()}function u(e){o("ok",e),s()}function d(){o("hide")}function c(){let e=[];return l.title&&e.push(t(ao,{class:"q-dialog__title"},(()=>l.title))),l.message&&e.push(t(ao,{class:"q-dialog__message"},(()=>l.message))),e.push(!0===l.grid?t("div",{class:"row items-stretch justify-start",role:"list"},l.actions.map((e=>{let l=e.avatar||e.img;return void 0===e.label?t(ur,{class:"col-all",dark:i.value}):t("div",{class:["q-bottom-sheet__item q-hoverable q-focusable cursor-pointer relative-position",e.class],style:e.style,tabindex:0,role:"listitem",onClick(){u(e)},onKeyup(t){13===t.keyCode&&u(e)}},[t("div",{class:"q-focus-helper"}),e.icon?t(zt,{name:e.icon,color:e.color}):l?t("img",{class:e.avatar?"q-bottom-sheet__avatar":"",src:l}):t("div",{class:"q-bottom-sheet__empty-icon"}),t("div",e.label)])}))):t("div",{role:"list"},l.actions.map((e=>{let l=e.avatar||e.img;return void 0===e.label?t(ur,{spaced:!0,dark:i.value}):t(Qi,{class:["q-bottom-sheet__item",e.classes],style:e.style,tabindex:0,clickable:!0,dark:i.value,onClick(){u(e)}},(()=>[t(Ui,{avatar:!0},(()=>e.icon?t(zt,{name:e.icon,color:e.color}):l?t("img",{class:e.avatar?"q-bottom-sheet__avatar":"",src:l}):null)),t(Ui,(()=>e.label))]))})))),e}function v(){return[t(lo,{class:["q-bottom-sheet q-bottom-sheet--"+(!0===l.grid?"grid":"list")+(!0===i.value?" q-bottom-sheet--dark q-dark":""),l.cardClass],style:l.cardStyle},c)]}return Object.assign(n,{show:function(){r.value.show()},hide:s}),()=>t(Pi,{ref:r,position:"bottom",onHide:d},v)}});var cv={install({$q:e,ssrContext:t}){e.cookies=this}};Object.assign(cv,function(e){return{get:t=>dv(t,e),set:(t,l,a)=>uv(t,l,a,e),has:t=>function(e,t){return null!==dv(e,t)}(t,e),remove:(t,l)=>function(e,t,l){uv(e,"",{expires:-1,...t},l)}(t,l,e),getAll:()=>dv(null,e)}}()),Ze({name:"DialogPlugin",props:{...Et,title:String,message:String,prompt:Object,options:Object,progress:[Boolean,Object],html:Boolean,ok:{type:[String,Object,Boolean],default:!0},cancel:[String,Object,Boolean],focus:{type:String,default:"ok",validator:e=>["ok","cancel","none"].includes(e)},stackButtons:Boolean,color:String,cardClass:[String,Array,Object],cardStyle:[String,Array,Object]},emits:["ok","hide"],setup(l,{emit:i}){let{proxy:r}=a(),{$q:s}=r,u=Pt(l,s),d=e(null),c=e(void 0!==l.prompt?l.prompt.model:void 0!==l.options?l.options.model:void 0),v=o((()=>"q-dialog-plugin"+(!0===u.value?" q-dialog-plugin--dark q-dark":"")+(!1!==l.progress?" q-dialog-plugin--progress":""))),f=o((()=>l.color||(!0===u.value?"amber":"primary"))),m=o((()=>!1===l.progress?null:!0===je(l.progress)?{component:l.progress.spinner||il,props:{color:l.progress.color||f.value}}:{component:il,props:{color:f.value}})),g=o((()=>void 0!==l.prompt||void 0!==l.options)),h=o((()=>{if(!0!==g.value)return{};let{model:e,isValid:t,items:a,...o}=void 0!==l.prompt?l.prompt:l.options;return o})),b=o((()=>!0===je(l.ok)||!0===l.ok?s.lang.label.ok:l.ok)),y=o((()=>!0===je(l.cancel)||!0===l.cancel?s.lang.label.cancel:l.cancel)),w=o((()=>void 0!==l.prompt?void 0!==l.prompt.isValid&&!0!==l.prompt.isValid(c.value):void 0!==l.options&&(void 0!==l.options.isValid&&!0!==l.options.isValid(c.value)))),x=o((()=>({color:f.value,label:b.value,ripple:!1,disable:w.value,...!0===je(l.ok)?l.ok:{flat:!0},"data-autofocus":"ok"===l.focus&&!0!==g.value||void 0,onClick:k}))),_=o((()=>({color:f.value,label:y.value,ripple:!1,...!0===je(l.cancel)?l.cancel:{flat:!0},"data-autofocus":"cancel"===l.focus&&!0!==g.value||void 0,onClick:q})));function S(){d.value.hide()}function k(){i("ok",p(c.value)),S()}function q(){S()}function C(){i("hide")}function $(e){c.value=e}function M(e){!0!==w.value&&"textarea"!==l.prompt.type&&!0===qe(e,13)&&k()}function T(e,a){return!0===l.html?t(ao,{class:e,innerHTML:a}):t(ao,{class:e},(()=>a))}function B(){return[t(rs,{color:f.value,dense:!0,autofocus:!0,dark:u.value,...h.value,modelValue:c.value,"onUpdate:modelValue":$,onKeyup:M})]}function L(){return[t(Ts,{color:f.value,options:l.options.items,dark:u.value,...h.value,modelValue:c.value,"onUpdate:modelValue":$})]}function z(){let e=[];return l.title&&e.push(T("q-dialog__title",l.title)),!1!==l.progress&&e.push(t(ao,{class:"q-dialog__progress"},(()=>t(m.value.component,m.value.props)))),l.message&&e.push(T("q-dialog__message",l.message)),void 0!==l.prompt?e.push(t(ao,{class:"scroll q-dialog-plugin__form"},B)):void 0!==l.options&&e.push(t(ur,{dark:u.value}),t(ao,{class:"scroll q-dialog-plugin__form"},L),t(ur,{dark:u.value})),(l.ok||l.cancel)&&e.push(function(){let e=[];return l.cancel&&e.push(t($l,_.value)),l.ok&&e.push(t($l,x.value)),t(oo,{class:!0===l.stackButtons?"items-end":"",vertical:l.stackButtons,align:"right"},(()=>e))}()),e}function V(){return[t(lo,{class:[v.value,l.cardClass],style:l.cardStyle,dark:u.value},z)]}return n((()=>l.prompt&&l.prompt.model),$),n((()=>l.options&&l.options.model),$),Object.assign(r,{show:function(){d.value.show()},hide:S}),()=>t(Pi,{ref:d,onHide:C},V)}});var vv,pv,fv=e(null),mv=X({isActive:!1},{start:G,stop:G,increment:G,setDefaults:G,install({$q:l,parentApp:a}){if(l.loadingBar=this,!0===this.__installed)return void(void 0!==l.config.loadingBar&&this.setDefaults(l.config.loadingBar));let o=e(void 0!==l.config.loadingBar?{...l.config.loadingBar}:{});function n(){mv.isActive=!0}function i(){mv.isActive=!1}let r=Ql("q-loading-bar");We({name:"LoadingBar",devtools:{hide:!0},setup:()=>()=>t(ut,{...o.value,onStart:n,onStop:i,ref:fv})},a).mount(r),Object.assign(this,{start(e){fv.value.start(e)},stop(){fv.value.stop()},increment(){fv.value.increment.apply(null,arguments)},setDefaults(e){!0===je(e)&&Object.assign(o.value,e)}})}}),gv=0,hv=null,bv={},yv={},wv={group:"__default_quasar_group__",delay:0,message:!1,html:!1,spinnerSize:80,spinnerColor:"",messageColor:"",backgroundColor:"",boxClass:"",spinner:il,customClass:""},xv={...wv};var _v=X({isActive:!1},{show(e){bv=function(e){if(e&&void 0!==e.group&&void 0!==yv[e.group])return Object.assign(yv[e.group],e);let t=!0===je(e)&&!0===e.ignoreDefaults?{...wv,...e}:{...xv,...e};return yv[t.group]=t,t}(e);let{group:l}=bv;return _v.isActive=!0,void 0!==vv?(bv.uid=gv,pv.$forceUpdate()):(bv.uid=++gv,null!==hv&&clearTimeout(hv),hv=setTimeout((()=>{hv=null;let e=Ql("q-loading");vv=We({name:"QLoading",setup(){function l(){!0!==_v.isActive&&void 0!==vv&&(zi(!1),vv.unmount(e),Ul(e),vv=void 0,pv=void 0)}function a(){if(!0!==_v.isActive)return null;let e=[t(bv.spinner,{class:"q-loading__spinner",color:bv.spinnerColor,size:bv.spinnerSize})];return bv.message&&e.push(t("div",{class:"q-loading__message"+(bv.messageColor?` text-${bv.messageColor}`:""),[!0===bv.html?"innerHTML":"textContent"]:bv.message})),t("div",{class:"q-loading fullscreen flex flex-center z-max "+bv.customClass.trim(),key:bv.uid},[t("div",{class:"q-loading__backdrop"+(bv.backgroundColor?` bg-${bv.backgroundColor}`:"")}),t("div",{class:"q-loading__box column items-center "+bv.boxClass},e)])}return r((()=>{zi(!0)})),()=>t(f,{name:"q-transition--fade",appear:!0,onAfterLeave:l},a)}},_v.__parentApp),pv=vv.mount(e)}),bv.delay)),e=>{void 0!==e&&Object(e)===e?_v.show({...e,group:l}):_v.hide(l)}},hide(e){if(!0===_v.isActive){if(void 0===e)yv={};else{if(void 0===yv[e])return;{delete yv[e];let t=Object.keys(yv);if(0!==t.length){let e=t[t.length-1];return void _v.show({group:e})}}}null!==hv&&(clearTimeout(hv),hv=null),_v.isActive=!1}},setDefaults(e){!0===je(e)&&Object.assign(xv,e)},install({$q:e,parentApp:t}){e.loading=this,_v.__parentApp=t,void 0!==e.config.loading&&this.setDefaults(e.config.loading)}}),Sv=0,kv={},qv={},Cv={},$v={},Mv=/^\s*$/,Tv=[],Bv=[void 0,null,!0,!1,""],Lv=["top-left","top-right","bottom-left","bottom-right","top","bottom","left","right","center"],zv=["top-left","top-right","bottom-left","bottom-right"],Vv={positive:{icon:e=>e.iconSet.type.positive,color:"positive"},negative:{icon:e=>e.iconSet.type.negative,color:"negative"},warning:{icon:e=>e.iconSet.type.warning,color:"warning",textColor:"dark"},info:{icon:e=>e.iconSet.type.info,color:"info"},ongoing:{group:!1,timeout:0,spinner:!0,color:"grey-8"}};function Ov(e,t,l){if(!e)return Ev("parameter required");let a,o={textColor:"white"};if(!0!==e.ignoreDefaults&&Object.assign(o,kv),!1===je(e)&&(o.type&&Object.assign(o,Vv[o.type]),e={message:e}),Object.assign(o,Vv[e.type||o.type],e),"function"==typeof o.icon&&(o.icon=o.icon(t)),o.spinner?(!0===o.spinner&&(o.spinner=il),o.spinner=v(o.spinner)):o.spinner=!1,o.meta={hasMedia:!(!1===o.spinner&&!o.icon&&!o.avatar),hasText:Av(o.message)||Av(o.caption)},o.position){if(!1===Lv.includes(o.position))return Ev("wrong position",e)}else o.position="bottom";if(!0===Bv.includes(o.timeout))o.timeout=5e3;else{let t=Number(o.timeout);if(isNaN(t)||t<0)return Ev("wrong timeout",e);o.timeout=Number.isFinite(t)?t:0}0===o.timeout?o.progress=!1:!0===o.progress&&(o.meta.progressClass="q-notification__progress"+(o.progressClass?` ${o.progressClass}`:""),o.meta.progressStyle={animationDuration:`${o.timeout+1e3}ms`});let n=(!0===Array.isArray(e.actions)?e.actions:[]).concat(!0!==e.ignoreDefaults&&!0===Array.isArray(kv.actions)?kv.actions:[]).concat(void 0!==Vv[e.type]&&!0===Array.isArray(Vv[e.type].actions)?Vv[e.type].actions:[]),{closeBtn:i}=o;if(i&&n.push({label:"string"==typeof i?i:t.lang.label.close}),o.actions=n.map((({handler:e,noDismiss:t,...l})=>({flat:!0,...l,onClick:"function"==typeof e?()=>{e(),!0!==t&&r()}:()=>{r()}}))),void 0===o.multiLine&&(o.multiLine=o.actions.length>1),Object.assign(o.meta,{class:"q-notification row items-stretch q-notification--"+(!0===o.multiLine?"multi-line":"standard")+(void 0!==o.color?` bg-${o.color}`:"")+(void 0!==o.textColor?` text-${o.textColor}`:"")+(void 0!==o.classes?` ${o.classes}`:""),wrapperClass:"q-notification__wrapper col relative-position border-radius-inherit "+(!0===o.multiLine?"column no-wrap justify-center":"row items-center"),contentClass:"q-notification__content row items-center"+(!0===o.multiLine?"":" col"),leftClass:!0===o.meta.hasText?"additional":"single",attrs:{role:"alert",...o.attrs}}),!1===o.group?(o.group=void 0,o.meta.group=void 0):((void 0===o.group||!0===o.group)&&(o.group=[o.message,o.caption,o.multiline].concat(o.actions.map((e=>`${e.label}*${e.icon}`))).join("|")),o.meta.group=o.group+"|"+o.position),0===o.actions.length?o.actions=void 0:o.meta.actionsClass="q-notification__actions row items-center "+(!0===o.multiLine?"justify-end":"col-auto")+(!0===o.meta.hasMedia?" q-notification__actions--with-media":""),void 0!==l){l.notif.meta.timer&&(clearTimeout(l.notif.meta.timer),l.notif.meta.timer=void 0),o.meta.uid=l.notif.meta.uid;let e=Cv[o.position].value.indexOf(l.notif);Cv[o.position].value[e]=o}else{let t=qv[o.meta.group];if(void 0===t){if(o.meta.uid=Sv++,o.meta.badge=1,-1!==["left","right","center"].indexOf(o.position))Cv[o.position].value.splice(Math.floor(Cv[o.position].value.length/2),0,o);else{let e=-1!==o.position.indexOf("top")?"unshift":"push";Cv[o.position].value[e](o)}void 0!==o.group&&(qv[o.meta.group]=o)}else{if(t.meta.timer&&(clearTimeout(t.meta.timer),t.meta.timer=void 0),void 0!==o.badgePosition){if(!1===zv.includes(o.badgePosition))return Ev("wrong badgePosition",e)}else o.badgePosition="top-"+(-1!==o.position.indexOf("left")?"right":"left");o.meta.uid=t.meta.uid,o.meta.badge=t.meta.badge+1,o.meta.badgeClass=`q-notification__badge q-notification__badge--${o.badgePosition}`+(void 0!==o.badgeColor?` bg-${o.badgeColor}`:"")+(void 0!==o.badgeTextColor?` text-${o.badgeTextColor}`:"")+(o.badgeClass?` ${o.badgeClass}`:"");let l=Cv[o.position].value.indexOf(t);Cv[o.position].value[l]=qv[o.meta.group]=o}}let r=()=>{(function(e){e.meta.timer&&(clearTimeout(e.meta.timer),e.meta.timer=void 0);let t=Cv[e.position].value.indexOf(e);if(-1!==t){void 0!==e.group&&delete qv[e.meta.group];let l=Tv[""+e.meta.uid];if(l){let{width:e,height:t}=getComputedStyle(l);l.style.left=`${l.offsetLeft}px`,l.style.width=e,l.style.height=t}Cv[e.position].value.splice(t,1),"function"==typeof e.onDismiss&&e.onDismiss()}})(o),a=void 0};return o.timeout>0&&(o.meta.timer=setTimeout((()=>{o.meta.timer=void 0,r()}),o.timeout+1e3)),void 0!==o.group?t=>{void 0!==t?Ev("trying to update a grouped one which is forbidden",e):r()}:(a={dismiss:r,config:e,notif:o},void 0===l?e=>{if(void 0!==a)if(void 0===e)a.dismiss();else{Ov(Object.assign({},a.config,e,{group:!1,position:o.position}),t,a)}}:void Object.assign(l,a))}function Av(e){return null!=e&&!0!==Mv.test(e)}function Ev(e,t){return!1}var Pv={setDefaults(e){!0===je(e)&&Object.assign(kv,e)},registerType(e,t){!0===je(t)&&(Vv[e]=t)},install({$q:l,parentApp:a}){if(l.notify=this.create=e=>Ov(e,l),l.notify.setDefaults=this.setDefaults,l.notify.registerType=this.registerType,void 0!==l.config.notify&&this.setDefaults(l.config.notify),!0!==this.__installed){Lv.forEach((t=>{Cv[t]=e([]);let l=!0===["left","center","right"].includes(t)?"center":-1!==t.indexOf("top")?"top":"bottom",a=-1!==t.indexOf("left")?"start":-1!==t.indexOf("right")?"end":"center",o=["left","right"].includes(t)?`items-${"left"===t?"start":"end"} justify-center`:"center"===t?"flex-center":`items-${a}`;$v[t]=`q-notifications__list q-notifications__list--${l} fixed column no-wrap ${o}`}));let l=Ql("q-notify");We(Ze({name:"QNotifications",devtools:{hide:!0},setup:()=>()=>t("div",{class:"q-notifications"},Lv.map((e=>t($,{key:e,class:$v[e],tag:"div",name:`q-notification--${e}`},(()=>Cv[e].value.map((e=>{let l=e.meta,a=[];if(!0===l.hasMedia&&(!1!==e.spinner?a.push(t(e.spinner,{class:"q-notification__spinner q-notification__spinner--"+l.leftClass,color:e.spinnerColor,size:e.spinnerSize})):e.icon?a.push(t(zt,{class:"q-notification__icon q-notification__icon--"+l.leftClass,name:e.icon,color:e.iconColor,size:e.iconSize,role:"img"})):e.avatar&&a.push(t(Vt,{class:"q-notification__avatar q-notification__avatar--"+l.leftClass},(()=>t("img",{src:e.avatar,"aria-hidden":"true"}))))),!0===l.hasText){let l,o={class:"q-notification__message col"};if(!0===e.html)o.innerHTML=e.caption?`<div>${e.message}</div><div class="q-notification__caption">${e.caption}</div>`:e.message;else{let a=[e.message];l=e.caption?[t("div",a),t("div",{class:"q-notification__caption"},[e.caption])]:a}a.push(t("div",o,l))}let o=[t("div",{class:l.contentClass},a)];return!0===e.progress&&o.push(t("div",{key:`${l.uid}|p|${l.badge}`,class:l.progressClass,style:l.progressStyle})),void 0!==e.actions&&o.push(t("div",{class:l.actionsClass},e.actions.map((e=>t($l,e))))),l.badge>1&&o.push(t("div",{key:`${l.uid}|${l.badge}`,class:e.meta.badgeClass,style:e.badgeStyle},[l.badge])),t("div",{ref:e=>{Tv[""+l.uid]=e},key:l.uid,class:l.class,...l.attrs},[t("div",{class:l.wrapperClass},o)])})))))))}),a).mount(l)}}};function Fv(e){return!0===De(e)?"__q_date|"+e.toUTCString():!0===function(e){return"[object RegExp]"===Object.prototype.toString.call(e)}(e)?"__q_expr|"+e.source:"number"==typeof e?"__q_numb|"+e:"boolean"==typeof e?"__q_bool|"+(e?"1":"0"):"string"==typeof e?"__q_strn|"+e:"function"==typeof e?"__q_strn|"+e.toString():e===Object(e)?"__q_objt|"+JSON.stringify(e):e}function Rv(){let e=()=>null;return{has:()=>!1,getLength:()=>0,getItem:e,getIndex:e,getKey:e,getAll:()=>{},getAllKeys:()=>[],set:G,remove:G,clear:G,isEmpty:()=>!0}}function Nv(e){let t=window[e+"Storage"],l=e=>{let l=t.getItem(e);return l?function(e){if(e.length<9)return e;let t=e.substring(0,8),l=e.substring(9);switch(t){case"__q_date":return new Date(l);case"__q_expr":return new RegExp(l);case"__q_numb":return Number(l);case"__q_bool":return"1"===l;case"__q_strn":return""+l;case"__q_objt":return JSON.parse(l);default:return e}}(l):null};return{has:e=>null!==t.getItem(e),getLength:()=>t.length,getItem:l,getIndex:e=>e<t.length?l(t.key(e)):null,getKey:e=>e<t.length?t.key(e):null,getAll:()=>{let e,a={},o=t.length;for(let n=0;n<o;n++)e=t.key(n),a[e]=l(e);return a},getAllKeys:()=>{let e=[],l=t.length;for(let a=0;a<l;a++)e.push(t.key(a));return e},set:(e,l)=>{t.setItem(e,Fv(l))},remove:e=>{t.removeItem(e)},clear:()=>{t.clear()},isEmpty:()=>0===t.length}}var Hv=!1===W.has.webStorage?Rv():Nv("local"),Iv={install({$q:e}){e.localStorage=Hv}};Object.assign(Iv,Hv);var jv=!1===W.has.webStorage?Rv():Nv("session"),Dv={install({$q:e}){e.sessionStorage=jv}};Object.assign(Dv,jv);oc(["ok","hide"]);var Qv={version:"2.15.1",install(e,t,l){Ye(e,{components:Xe,directives:vc,...t})},lang:we,iconSet:Be};const Uv={isoName:"zh-CN",nativeName:"中文(简体)",label:{clear:"清空",ok:"确定",cancel:"取消",close:"关闭",set:"设置",select:"选择",reset:"重置",remove:"移除",update:"更新",create:"创建",search:"搜索",filter:"过滤",refresh:"刷新",expand:e=>e?`展开"${e}"`:"扩张",collapse:e=>e?`折叠"${e}"`:"坍塌"},date:{days:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),daysShort:"周日_周一_周二_周三_周四_周五_周六".split("_"),months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),headerTitle:e=>new Intl.DateTimeFormat("zh-CN",{weekday:"short",month:"short",day:"numeric"}).format(e),firstDayOfWeek:0,format24h:!1,pluralDay:"天"},table:{noData:"没有可用数据",noResults:"找不到匹配的数据",loading:"正在加载...",selectedRecords:e=>"已选择"+e+"行",recordsPerPage:"每页的行数:",allRows:"全部",pagination:(e,t,l)=>e+"-"+t+" / "+l,columns:"列"},editor:{url:"URL",bold:"粗体",italic:"斜体",strikethrough:"删除线",underline:"下划线",unorderedList:"无序列表",orderedList:"有序列表",subscript:"下标",superscript:"上标",hyperlink:"超链接",toggleFullscreen:"全屏切换",quote:"引号",left:"左对齐",center:"居中对齐",right:"右对齐",justify:"两端对齐",print:"打印",outdent:"减少缩进",indent:"增加缩进",removeFormat:"清除样式",formatting:"格式化",fontSize:"字体大小",align:"对齐",hr:"插入水平线",undo:"撤消",redo:"重做",heading1:"标题一",heading2:"标题二",heading3:"标题三",heading4:"标题四",heading5:"标题五",heading6:"标题六",paragraph:"段落",code:"代码",size1:"非常小",size2:"比较小",size3:"正常",size4:"中等偏大",size5:"大",size6:"非常大",size7:"超级大",defaultFont:"默认字体",viewSource:"查看资料"},tree:{noNodes:"没有可用节点",noResults:"找不到匹配的节点"}};export{lo as $,ur as C,$l as F,nr as H,jd as K,sn as N,al as R,Rt as S,Id as U,Dr as V,_s as W,Dd as Y,$u as a,Ao as b,qs as c,rs as d,Jo as e,ps as f,ao as g,Ui as h,pn as i,Pi as j,Xt as k,to as l,Qv as m,Di as n,oo as o,Qi as p,Uv as q,Pv as t,zt as w};
