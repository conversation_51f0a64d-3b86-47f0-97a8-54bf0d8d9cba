import{i as e}from"./index-8cc8d4b8.js";function t(t){return e({url:"/system/deviceManage/deviceManage/inverterInfoList",method:"post",data:{...t}})}function r(t){return e({url:"/device/export/inverters",method:"post",responseType:"blob",data:{...t}})}function n(t){return e({url:`/system/deviceManage/inverterDetails/${t}`,method:"get"})}function a(t,r){return e({url:`/system/deviceManage/inverterInfoChart/${t}`,method:"get",params:{date:r}})}export{a,n as b,r as c,t as g};
