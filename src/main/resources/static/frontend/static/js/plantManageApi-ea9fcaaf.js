import{a as e}from"./api-b858041e.js";const t=(t,a,r,s,p,n,i=2)=>e("/system/plantManage/getPlantList",{},{currentPage:t,pageSize:a,plantName:r,multiPlantStatus:s,powerDistributor:p,projectId:n,sort:i},"post"),a=(t,a,r)=>e("/device/export/inverters",{},{plantName:t,deviceId:a,multiInverterStatus:r},"post","blob"),r=t=>e("/device/export/edgeServers",{},{plantName:t},"post","blob"),s=(t=1,a=10,r="",s="",p="",n=[])=>e("/system/deviceManage/deviceManage/inverterInfoList",{},{currentPage:t,pageSize:a,plantName:s,deviceId:p,multiInverterStatus:n,plantUid:r},"post"),p=(t,a,r)=>e("/device/deviceManage/edgeServers",{},{plantName:r,currentPage:t,pageSize:a,isAsc:"",order:""},"post"),n=(t,a,r,s)=>e("/plant/plantManage/updatePlant",{},{meterId:a,plantCapacity:r,plantName:s,plantUid:t},"post");export{s as X,t as a,p as b,r as c,a as d,n as e};
