let t=[];const r=new WeakMap;function n(){t.forEach((t=>t(...r.get(t)))),t=[]}function e(e,...o){r.set(e,o),t.includes(e)||1===t.push(e)&&requestAnimationFrame(n)}function o(t){if(null===t)return null;const r=function(t){return 9===t.nodeType?null:t.parentNode}(t);if(null===r)return null;if(9===r.nodeType)return document.documentElement;if(1===r.nodeType){const{overflow:t,overflowX:n,overflowY:e}=getComputedStyle(r);if(/(auto|scroll|overlay)/.test(t+e+n))return r}return o(r)}function u(t){return"string"==typeof t?document.querySelector(t):"function"==typeof t?t():t}function a(t,r){let{target:n}=t;for(;n;){if(n.dataset&&void 0!==n.dataset[r])return!0;n=n.parentElement}return!1}function i(t){return t.composedPath()[0]||null}function s(t,r){var n;if(null==t)return;const e=function(t){if("number"==typeof t)return{"":t.toString()};const r={};return t.split(/ +/).forEach((t=>{if(""===t)return;const[n,e]=t.split(":");void 0===e?r[""]=n:r[n]=e})),r}(t);if(void 0===r)return e[""];if("string"==typeof r)return null!==(n=e[r])&&void 0!==n?n:e[""];if(Array.isArray(r)){for(let t=r.length-1;t>=0;--t){const n=r[t];if(n in e)return e[n]}return e[""]}{let t,n=-1;return Object.keys(e).forEach((o=>{const u=Number(o);!Number.isNaN(u)&&r>=u&&u>=n&&(n=u,t=e[o])})),t}}function c(t){return"string"==typeof t?t.endsWith("px")?Number(t.slice(0,t.length-2)):Number(t):t}function $(t){if(null!=t)return"number"==typeof t?`${t}px`:t.endsWith("px")?t:`${t}px`}function f(t,r){const n=t.trim().split(/\s+/g),e={top:n[0]};switch(n.length){case 1:e.right=n[0],e.bottom=n[0],e.left=n[0];break;case 2:e.right=n[1],e.left=n[1],e.bottom=n[0];break;case 3:e.right=n[1],e.bottom=n[2],e.left=n[1];break;case 4:e.right=n[1],e.bottom=n[2],e.left=n[3];break;default:throw new Error("[seemly/getMargin]:"+t+" is not a valid value.")}return void 0===r?e:e[r]}function l(t,r){const[n,e]=t.split(" ");return r?"row"===r?n:e:{row:n,col:e||n}}const h={black:"#000",silver:"#C0C0C0",gray:"#808080",white:"#FFF",maroon:"#800000",red:"#F00",purple:"#800080",fuchsia:"#F0F",green:"#008000",lime:"#0F0",olive:"#808000",yellow:"#FF0",navy:"#000080",blue:"#00F",teal:"#008080",aqua:"#0FF",transparent:"#0000"};function p(t,r,n){n/=100;const e=(r/=100)*Math.min(n,1-n)+n;return[t,e?100*(2-2*n/e):0,100*e]}function g(t,r,n){const e=(n/=100)-n*(r/=100)/2,o=Math.min(e,1-e);return[t,o?(n-e)/o*100:0,100*e]}function m(t,r,n){r/=100,n/=100;let e=(e,o=(e+t/60)%6)=>n-n*r*Math.max(Math.min(o,4-o,1),0);return[255*e(5),255*e(3),255*e(1)]}function d(t,r,n){t/=255,r/=255,n/=255;let e=Math.max(t,r,n),o=e-Math.min(t,r,n),u=o&&(e==t?(r-n)/o:e==r?2+(n-t)/o:4+(t-r)/o);return[60*(u<0?u+6:u),e&&o/e*100,100*e]}function x(t,r,n){t/=255,r/=255,n/=255;let e=Math.max(t,r,n),o=e-Math.min(t,r,n),u=1-Math.abs(e+e-o-1),a=o&&(e==t?(r-n)/o:e==r?2+(n-t)/o:4+(t-r)/o);return[60*(a<0?a+6:a),u?o/u*100:0,50*(e+e-o)]}function y(t,r,n){n/=100;let e=(r/=100)*Math.min(n,1-n),o=(r,o=(r+t/30)%12)=>n-e*Math.max(Math.min(o-3,9-o,1),-1);return[255*o(0),255*o(8),255*o(4)]}const w="^\\s*",v="\\s*$",b="\\s*((\\.\\d+)|(\\d+(\\.\\d*)?))%\\s*",E="\\s*((\\.\\d+)|(\\d+(\\.\\d*)?))\\s*",F="([0-9A-Fa-f])",M="([0-9A-Fa-f]{2})",A=new RegExp(`${w}hsl\\s*\\(${E},${b},${b}\\)${v}`),S=new RegExp(`${w}hsv\\s*\\(${E},${b},${b}\\)${v}`),N=new RegExp(`${w}hsla\\s*\\(${E},${b},${b},${E}\\)${v}`),R=new RegExp(`${w}hsva\\s*\\(${E},${b},${b},${E}\\)${v}`),k=new RegExp(`${w}rgb\\s*\\(${E},${E},${E}\\)${v}`),C=new RegExp(`${w}rgba\\s*\\(${E},${E},${E},${E}\\)${v}`),I=new RegExp(`${w}#${F}${F}${F}${v}`),j=new RegExp(`${w}#${M}${M}${M}${v}`),q=new RegExp(`${w}#${F}${F}${F}${F}${v}`),T=new RegExp(`${w}#${M}${M}${M}${M}${v}`);function U(t){return parseInt(t,16)}function W(t){try{let r;if(r=N.exec(t))return[Y(r[1]),K(r[5]),K(r[9]),X(r[13])];if(r=A.exec(t))return[Y(r[1]),K(r[5]),K(r[9]),1];throw new Error(`[seemly/hsla]: Invalid color value ${t}.`)}catch(r){throw r}}function H(t){try{let r;if(r=R.exec(t))return[Y(r[1]),K(r[5]),K(r[9]),X(r[13])];if(r=S.exec(t))return[Y(r[1]),K(r[5]),K(r[9]),1];throw new Error(`[seemly/hsva]: Invalid color value ${t}.`)}catch(r){throw r}}function z(t){try{let r;if(r=j.exec(t))return[U(r[1]),U(r[2]),U(r[3]),1];if(r=k.exec(t))return[J(r[1]),J(r[5]),J(r[9]),1];if(r=C.exec(t))return[J(r[1]),J(r[5]),J(r[9]),X(r[13])];if(r=I.exec(t))return[U(r[1]+r[1]),U(r[2]+r[2]),U(r[3]+r[3]),1];if(r=T.exec(t))return[U(r[1]),U(r[2]),U(r[3]),X(U(r[4])/255)];if(r=q.exec(t))return[U(r[1]+r[1]),U(r[2]+r[2]),U(r[3]+r[3]),X(U(r[4]+r[4])/255)];if(t in h)return z(h[t]);throw new Error(`[seemly/rgba]: Invalid color value ${t}.`)}catch(r){throw r}}function B(t,r,n,e){return`rgba(${J(t)}, ${J(r)}, ${J(n)}, ${o=e,o>1?1:o<0?0:o})`;var o}function D(t,r,n,e,o){return J((t*r*(1-e)+n*e)/o)}function G(t,r){Array.isArray(t)||(t=z(t)),Array.isArray(r)||(r=z(r));const n=t[3],e=r[3],o=X(n+e-n*e);return B(D(t[0],n,r[0],e,o),D(t[1],n,r[1],e,o),D(t[2],n,r[2],e,o),o)}function O(t,r){const[n,e,o,u=1]=Array.isArray(t)?t:z(t);return r.alpha?B(n,e,o,r.alpha):B(n,e,o,u)}function P(t,r){const[n,e,o,u=1]=Array.isArray(t)?t:z(t),{lightness:a=1,alpha:i=1}=r;return Q([n*a,e*a,o*a,u*i])}function X(t){const r=Math.round(100*Number(t))/100;return r>1?1:r<0?0:r}function Y(t){const r=Math.round(Number(t));return r>=360||r<0?0:r}function J(t){const r=Math.round(Number(t));return r>255?255:r<0?0:r}function K(t){const r=Math.round(Number(t));return r>100?100:r<0?0:r}function L(t){const[r,n,e]=Array.isArray(t)?t:z(t);return function(t,r,n){return`rgb(${J(t)}, ${J(r)}, ${J(n)})`}(r,n,e)}function Q(t){const[r,n,e]=t;return 3 in t?`rgba(${J(r)}, ${J(n)}, ${J(e)}, ${X(t[3])})`:`rgba(${J(r)}, ${J(n)}, ${J(e)}, 1)`}function V(t){return`hsv(${Y(t[0])}, ${K(t[1])}%, ${K(t[2])}%)`}function Z(t){const[r,n,e]=t;return 3 in t?`hsva(${Y(r)}, ${K(n)}%, ${K(e)}%, ${X(t[3])})`:`hsva(${Y(r)}, ${K(n)}%, ${K(e)}%, 1)`}function _(t){return`hsl(${Y(t[0])}, ${K(t[1])}%, ${K(t[2])}%)`}function tt(t){const[r,n,e]=t;return 3 in t?`hsla(${Y(r)}, ${K(n)}%, ${K(e)}%, ${X(t[3])})`:`hsla(${Y(r)}, ${K(n)}%, ${K(e)}%, 1)`}function rt(t){if("string"==typeof t){let r;if(r=j.exec(t))return`${r[0]}FF`;if(r=T.exec(t))return r[0];if(r=I.exec(t))return`#${r[1]}${r[1]}${r[2]}${r[2]}${r[3]}${r[3]}FF`;if(r=q.exec(t))return`#${r[1]}${r[1]}${r[2]}${r[2]}${r[3]}${r[3]}${r[4]}${r[4]}`;throw new Error(`[seemly/toHexString]: Invalid hex value ${t}.`)}return`#${t.slice(0,3).map((t=>J(t).toString(16).toUpperCase().padStart(2,"0"))).join("")}`+(3===t.length?"FF":J(255*t[3]).toString(16).padStart(2,"0").toUpperCase())}function nt(t){if("string"==typeof t){let r;if(r=j.exec(t))return r[0];if(r=T.exec(t))return r[0].slice(0,7);if(r=I.exec(t)||q.exec(t))return`#${r[1]}${r[1]}${r[2]}${r[2]}${r[3]}${r[3]}`;throw new Error(`[seemly/toHexString]: Invalid hex value ${t}.`)}return`#${t.slice(0,3).map((t=>J(t).toString(16).toUpperCase().padStart(2,"0"))).join("")}`}function et(t=8){return Math.random().toString(16).slice(2,2+t)}function ot(t,r){const n=[];for(let e=0;e<t;++e)n.push(r);return n}function ut(t,r){const n=[];if(!r){for(let r=0;r<t;++r)n.push(r);return n}for(let e=0;e<t;++e)n.push(r(e));return n}export{V as A,ut as B,et as C,$ as D,ot as E,l as F,s as G,f as a,e as b,G as c,c as d,O as e,o as f,i as g,a as h,tt as i,x as j,Z as k,d as l,Q as m,W as n,y as o,p,H as q,z as r,P as s,rt as t,u,m as v,g as w,nt as x,_ as y,L as z};
