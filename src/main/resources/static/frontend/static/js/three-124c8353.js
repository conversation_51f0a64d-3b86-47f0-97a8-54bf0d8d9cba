import"./vue-5bfa3a54.js";import s from"./screen-f4683784.js";import o from"./carouselScreen-81f83c63.js";import{_ as t}from"./index-a5df0f75.js";import{j as e,o as i,c as r,a as p,x as m,y as a,b as j}from"./@vue-5e5cdef9.js";import"./@babel-f3c0a00c.js";import"./mapUtils-bebbcec2.js";import"./three-59a86278.js";import"./homeStore-7900023d.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./homeApi-030fb9ef.js";import"./index-092b8780.js";import"./api-360ec627.js";import"./@vueuse-5227c686.js";import"./vue-router-6159329f.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./menuStore-30bf76d3.js";import"./dayjs-67f8ddef.js";import"./lodash-6d99edc3.js";import"./icons-95011f8c.js";import"./naive-ui-0ee0b8c3.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./lodash-es-ea7deab5.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./date-fns-tz-0cc073c8.js";import"./async-validator-cf877c1f.js";import"./@vicons-f32a0bdb.js";import"./notification-950a5f80.js";import"./quasar-df1bac18.js";import"./taskUitls-36951a34.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./swiper-7f939876.js";import"./chartResize-3e3d11d7.js";import"./element-resize-detector-0d37a2ab.js";import"./batch-processor-06abf2b4.js";import"./echartsInit-2e16a3ff.js";import"./chartXY-a0399c4a.js";import"./countUtil-d7099b62.js";import"./alarmAnalysisApi-0364d01e.js";import"./element-plus-95e0b914.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./bignumber.js-a537a5ca.js";import"./startEndSlide-8c1800d1.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./xe-utils-fe99d42a.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-be30dbb6.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./sun-9a09fd29.js";import"./tableWrapper-2dedb322.js";const l={class:"three screenfull-content"},n={class:"overview"},c={class:"circulate"},u={class:"video1"},d=["src"],v={class:"video2"},h=["src"],f={class:"video3"},g=["src"],b=t({__name:"three",setup(t){const b=e({one:"",two:"",three:""}),y=s=>{var o=document.getElementById(s).files[0],t=URL.createObjectURL(o);b[s]=t};return(t,e)=>(i(),r("div",l,[p("section",n,[m(s,{iframe:!0})]),p("section",c,[m(o)]),p("section",u,[p("input",{type:"file",id:"one",onChange:e[0]||(e[0]=s=>y("one")),class:a(j(b).one.length>0?"hide":"show")},null,34),p("video",{class:"video-play",src:j(b).one,controls:"",autoplay:"",muted:"",loop:""},null,8,d)]),p("section",v,[p("input",{type:"file",id:"two",onChange:e[1]||(e[1]=s=>y("two")),class:a(j(b).two.length>0?"hide":"show")},null,34),p("video",{class:"video-play",src:j(b).two,controls:"",autoplay:"",muted:"",loop:""},null,8,h)]),p("section",f,[p("input",{type:"file",id:"three",onChange:e[2]||(e[2]=s=>y("three")),class:a(j(b).three.length>0?"hide":"show")},null,34),p("video",{class:"video-play2",src:j(b).three,controls:"",autoplay:"",muted:"",loop:""},null,8,g)])]))}},[["__scopeId","data-v-a27e2b4c"]]);export{b as default};
