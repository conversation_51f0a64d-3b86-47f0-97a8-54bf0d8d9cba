import{R as e}from"./vue-router-6159329f.js";import{d as a}from"./pinia-c7531a5f.js";import"./vue-5bfa3a54.js";import{d as t}from"./dayjs-d60cc07f.js";import{l}from"./lodash-6d99edc3.js";import{r as i}from"./icons-95011f8c.js";import{j as n,e as o,h as r,A as s}from"./@vue-5e5cdef9.js";import{n as c,B as p}from"./@vicons-f32a0bdb.js";const h={accessToken:"token令牌",refreshToken:"刷新token令牌",tokenType:"令牌类型",expiresIn:"令牌失效时间",useruid:"用户uid",router:[{label:"平台首页",icon:"Home",children:[{icon:"HomeOutline",label:"站点概览",path:"/home/<USER>"},{icon:"HomeSharp",label:"综合大屏",path:"/home/<USER>"},{icon:"HomeSharp",label:"站点轮播",path:"/home/<USER>"},{icon:"HomeSharp",label:"隐藏的站点轮播",path:"/home/<USER>"},{label:"三大屏",path:"/home/<USER>"}]},{label:"电站管理",icon:"Apps",children:[{label:"电站列表",icon:"AppsOutline",path:"/plantManage/plantList"},{label:"逆变器列表",icon:"AppsSharp",path:"/plantManage/inverterList"},{label:"运维器列表",icon:"AppsSharp",path:"/plantManage/edgeServerList"}]},{label:"设备监测",icon:"Calendar",children:[{label:"电站详情",icon:"CalendarClear",path:"/deviceMonitor/plantDetail"},{label:"逆变器详情",icon:"CalendarClearOutline",path:"/deviceMonitor/inverterDetail"},{label:"实时数据",icon:"CalendarSharp",path:"/deviceMonitor/realTimeDate"}]},{label:"异常统计",icon:"TrailSign",children:[{label:"报警列表",icon:"TrailSignOutline",path:"/alarmAnalysis/alarmList",meta:{page:1,pageSize:10,startTime:t().format("YYYY-MM-DD"),endTime:t().format("YYYY-MM-DD")}},{label:"自检提示",icon:"TrailSignSharp",path:"/alarmAnalysis/inspectionList"},{label:"运维器事件",icon:"TrailSignSharp",path:"/alarmAnalysis/edgeServerEventList"}]},{label:"统计报表",icon:"Menu",children:[{label:"能效收益统计",icon:"MenuOutline",path:"/statisticReport/incomeStatistics"},{label:"电站统计",icon:"MenuSharp",path:"/statisticReport/plantStatistics"},{label:"电站日常统计",icon:"MenuSharp",path:"/statisticReport/plantNormalStatistics"}]},{label:"数据分析",icon:"Analytics",children:[{label:"发电量运维",icon:"AnalyticsOutline",path:"/dataAnalysis/electricOperations"},{label:"故障诊断",icon:"AnalyticsSharp",path:"/dataAnalysis/faultPrediction"},{label:"诊断",path:"/dataAnalysis/diagnosis",icon:"false"}]},{label:"权限管理",icon:"Accessibility",children:[{label:"用户管理",icon:"AccessibilityOutline",path:"/authorityManage/userManage"},{label:"角色管理",icon:"AccessibilitySharp",path:"/authorityManage/roleManage"},{label:"项目管理",icon:"AccessibilitySharp",path:"/authorityManage/projectManage"},{label:"菜单管理",icon:"AccessibilitySharp",path:"/authorityManage/manuManage"},{label:"项目信息",icon:"AccessibilitySharp",path:"/authorityManage/projectInfo"}]},{label:"测试菜单",children:[{label:"测试边框",path:"/test/testBoard"},{label:"测试表格",path:"/test/testTable"}]},{label:"日志管理",children:[{label:"登录日志",path:"/log/loginLog"},{label:"操作日志",path:"/log/operationLog"}]},{label:"测试跳转",path:"https://workorder.btosolarman.com/"}]},u=a("xwmenu",(()=>{let a=n([]),t=o((()=>[...u().$state.routers].map((e=>y(e.path,e.label,e.icon,e.children))))),d=r(!0),b=r("站点概览"),m=r([]),g=r(),S=r(""),f=n({});const v=Object.entries(Object.assign({})).reduce(((e,[a,t])=>(e.set(a.replace(/.*views(.*)\.vue/,"$1"),t),e)),new Map);function y(a,t,n,o){a=(null==o?void 0:o.length)?t:a;const r=l._.set({},"to.path",a);return{label:(null==o?void 0:o.length)?t:()=>s(e,r,{default:()=>t}),key:t,path:a??t,show:"false"!=n,icon:i(n?c[n]:p),children:(null==o?void 0:o.length)?o.map((e=>y(e.path,e.label,e.icon,e.children))):void 0}}return{routers:a,reloadRoute:function(){const e=function e(a){return a.reduce(((a,t)=>{const l=e((null==t?void 0:t.children)??[]);return null==t||t.path,(t={path:t.path,name:t.label,component:v.get(null==t?void 0:t.path),meta:{name:t.label,keepAlive:!0}}).component?[...a,...l,t]:[...a,...l]}),[])}(u().$state.routers);if(e.length){for(const a of e)router.addRoute("index",a);router.addRoute({path:"/:pathMatch(.*)",redirect:"404",component:v.get("/404/index")})}},coverRoute:function(e=h.router){u().$state.routers=n(e),a=n(e)},menuOptions:t,views:v,changeSidebarCol:function(e){d.value=e},collapsed:d,value:b,expandedKeys:m,menuRef:g,title:S,params:f}}),{persist:{key:"xwmenu",storage:sessionStorage}});export{u as x};
