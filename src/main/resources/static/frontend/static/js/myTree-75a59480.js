import{d as e}from"./quasar-df1bac18.js";import"./vue-5bfa3a54.js";import{x as a}from"./menuStore-30bf76d3.js";import{L as t}from"./ui-385bff4c.js";import{c as l}from"./countUtil-d7099b62.js";import{a as n,g as s}from"./api-360ec627.js";import{l as o}from"./lodash-6d99edc3.js";import{m as i}from"./notification-950a5f80.js";import{_ as r}from"./index-a5df0f75.js";import{D as d}from"./@vueuse-5227c686.js";import{q as u}from"./naive-ui-0ee0b8c3.js";import{h as c,e as p,m as v,n as m,v as y,az as f,a9 as k,b as h,o as w,c as g,x,H as b}from"./@vue-5e5cdef9.js";const j=(e=1,a=100,t="")=>n("/device/deviceManage/inverterTree",{currentPage:e,pageSize:a,queryString:t}),_=r({__name:"myTree",props:{type:{type:String,default:"plant"},defaultKey:{type:String,default:""}},emits:["nodeClick"],setup(n,{expose:r,emit:_}){const N=c([]),C=c(null),S=new t;let L=new l(0);const U=c("");c("站点名称");const q=a(),K=_,T=c([]),V=n,D=c([]),I=c(""),M=c([]),z=p((()=>{var e,a;return null==(a=null==(e=C.value)?void 0:e.virtualListInstRef)?void 0:a.viewportItems}));d(z,(async()=>{!z.value.some((e=>"plant"==V.type?e.isLastChild&&e.isLeaf:e.isLastChild&&e.rawNode.key!=e.rawNode.label))||S.get()||L.isEnd()||await A()}),{throttle:0,trailing:!1,leading:!0});const R="plant"==V.type?XWplantTreeApi:j;async function A(){let e;S.set(!0);try{e=await R(L.inc(),100,U.value),S.set(!1);const a=s(e).data;return a.list=a.list.map((e=>(e.key=e.plantUid,e.label=e.plantName,e.children&&e.children.map((a=>{a.key=a.inverterSn,a.label=a.inverterName,a.plantName=e.plantName})),e))),L.setMax(Math.ceil(a.total/100)),N.value.push(...o._.cloneDeep(a.list)),N.value=o._.uniqBy(N.value,"key"),o._.cloneDeep(a.list)}catch(a){i.error("获取电站树数据失败 ！！！")}}const B=o._.throttle((async function(){var e;L=new l(0),(null==(e=N.value)?void 0:e.length)&&C.value.scrollTo({key:N.value.at(0).key});const a=await A();N.value=a}),1e3);function E(e,a){D.value=e,I.value=a}function H(e,a){M.value=e,I.value=a}function P(e){const{option:a}=e;return{onClick(){var e;(null==(e=null==a?void 0:a.children)?void 0:e.length)?K("nodeClick",a.children[0]):K("nodeClick",a),location.href=location.href.split("?")[0]}}}return v((async()=>{var e,a;let t;if(D.value=[],M.value=[],await A(),V.defaultKey.length)t={key:V.defaultKey};else{q.title=N.value[0].label;const l=(null==(a=null==(e=N.value)?void 0:e.at(0))?void 0:a.children)||N.value;t=l[0]}D.value=[t.key],m((()=>{K("nodeClick",t)}))})),y((()=>{L=new l(0)})),r({onUpdateSelectedKeys:E}),(a,t)=>{const l=e,n=u,s=f("skeleton-item"),o=f("skeleton");return k((w(),g("div",null,[x(l,{standout:"tw-bg-gray-400 text-black",class:"tw-mb-2 tw-text-black",color:"black",modelValue:h(U),"onUpdate:modelValue":[t[0]||(t[0]=e=>b(U)?U.value=e:null),h(B)],dense:""},null,8,["modelValue","onUpdate:modelValue"]),k(x(n,{"block-line":"","virtual-scroll":"","key-field":"key","label-field":"label",ref_key:"treeRef",ref:C,"selected-keys":h(D),class:"tree tw-rounded shadow-2 tw-w-[250px]","expanded-keys":h(M),"default-expanded-keys":["parent"],"default-selected-keys":h(T),data:h(N),"node-props":P,"on-update:selected-keys":E,"on-update:expanded-keys":H,"expand-on-click":""},null,8,["selected-keys","expanded-keys","default-selected-keys","data"]),[[s]])])),[[o,h(S).value,void 0,{animated:!0}]])}}},[["__scopeId","data-v-7ee7a9c8"]]);export{_};
