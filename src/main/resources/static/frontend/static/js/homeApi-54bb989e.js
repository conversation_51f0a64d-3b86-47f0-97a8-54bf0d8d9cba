import{e as s,f as a,h as t,i as e,a as i,j as r,c as m,d as o,g as d,b as n,k as c}from"./index-15186f59.js";import{i as f}from"./index-8cc8d4b8.js";import"./api-b858041e.js";import{d as Y}from"./dayjs-d60cc07f.js";const j=(a=0,t=50)=>s({startIndex:a,endIndex:t}),p=()=>r(),g=()=>m(),x=()=>o(),y=()=>i(),D=()=>d(),h=()=>n(),M=()=>c(),u=()=>f({url:"/system/statistics/getDeviceNumInfo",method:"get",headers:{msg:!1}}),I=(s,t)=>a({country:t,province:s}),b=(s,a)=>t({city:s,area:a}),k=s=>e({city:s,startTime:Y().format("YYYY-MM-DD"),endTime:Y().format("YYYY-MM-DD")});export{y as X,D as a,h as b,u as c,M as d,p as e,g as f,x as g,j as h,I as i,b as j,k};
