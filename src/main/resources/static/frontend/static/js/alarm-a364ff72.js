import"./vue-5bfa3a54.js";import{e as t,a as e,d as s,t as a}from"./exportEchart-130b148d.js";import{c as i,d as r}from"./statisticReportApi-dc9fa149.js";import{g as o}from"./api-b858041e.js";import{e as l}from"./echartsInit-0067e609.js";import{t as m}from"./taskUitls-36951a34.js";import{l as n}from"./lodash-6d99edc3.js";import{h as p,j as u,o as j,c,a as v,t as d,b as f}from"./@vue-5e5cdef9.js";import"./@babel-f3c0a00c.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./exceljs-b3a0e81d.js";import"./file-saver-8735aaf5.js";import"./index-8cc8d4b8.js";import"./element-plus-d975be09.js";import"./lodash-es-ea7deab5.js";import"./@vueuse-af86c621.js";import"./vue-router-6159329f.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./dayjs-d60cc07f.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./xe-utils-fe99d42a.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";import"./quasar-b3f06d8a.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-7bbbb435.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./menuStore-26f8ddd8.js";import"./icons-95011f8c.js";import"./naive-ui-0ee0b8c3.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./date-fns-tz-0cc073c8.js";import"./@vicons-f32a0bdb.js";import"./notification-950a5f80.js";const w={class:"tw-flex tw-w-full tw-h-full"},x={class:"tw-flex-1 tw-h-full tw-flex-col"},b={class:"tw-flex-1 tw-h-1/2 tw-pb-2"},y={class:"tw-text-xl tw-w-full tw-flex tw-justify-between"},g=v("div",null," 异常率 ",-1),h={class:"tw-text-base"},k={class:"tw-flex-1 tw-h-1/2"},R={class:"tw-text-xl tw-w-full tw-flex tw-justify-between"},q=v("div",null," 等效时间 ",-1),E={class:"tw-text-base"},F={class:"tw-flex-1 tw-h-full tw-flex tw-justify-center tw-flex-col"},O={class:"tw-text-xl tw-w-full tw-flex tw-justify-between"},z=v("div",null," 时间 ",-1),D={class:"tw-text-base"},_={__name:"alarm",setup(_,{expose:A}){const N=p(),P=p(),B=p(),S=p([]),C=u({abnormalRate:null,equivalent:null,total:null}),H=async(t=[])=>{var s;const a=(null==(s=null==C?void 0:C.abnormalRate)?void 0:s.getOption())||e;a.xAxis[0].data=t.data.map((t=>t.collectDate)).reverse()||[],a.series[0].data=t.data.map((t=>t.abnormalPlantNum)).reverse()||[],a.series[1].data=t.data.map((t=>t.normalPlantNum)).reverse()||[],C.abnormalRate?C.abnormalRate.setOption(a,!0):C.abnormalRate=await l(N,a)},U=async(t=[])=>{var e;const a=(null==(e=null==C?void 0:C.equivalent)?void 0:e.getOption())||s;a.xAxis[0].data=t.data.map((t=>t.collectDate)).reverse()||[],a.series[0].data=t.data.map((t=>t.dailyEfficiencyPerHour)).reverse()||[],C.day?C.equivalent.setOption(a,!0):C.equivalent=await l(P,a)},I=async(t={})=>{var e;const s=(null==(e=null==C?void 0:C.total)?void 0:e.getOption())||a;let{normalDeviceNum:i,alarmDeviceNum:r,offlineDeviceNum:o}=t,m=["#0E7CE2","#FF8352","#E271DE","#F8456B","#00FFFF","#4AEAB0"];s.series[0].data=[{label:{color:m[0]},name:"正常",value:i},{label:{color:m[1]},name:"告警",value:r},{label:{color:m[2]},name:"离线",value:o}],C.total?C.total.setOption(s,!0):C.total=await l(B,s)};return A({getData:async t=>{S.value=t.date;const e=new m;e.add((async t=>{try{const e=await r(t.id,t.electricityPrice,...t.power,...t.date),s=o(e);return H(s),U(s),s}catch(e){return e}})(t)),e.add((async t=>{try{const e=await i(t.id,t.electricityPrice,...t.date,...t.power),s=o(e);return I(s.data),s}catch(e){return e}})(t)),await e.run()},exportFile:function(){const e={abnormalRate:"异常率",equivalent:"等效时间",total:"设备统计"};t(n._.mapKeys(C,((t,s)=>e[s])),"电站日常统计(设备统计)"+S.value.join("--"))}}),(t,e)=>(j(),c("div",w,[v("div",x,[v("div",b,[v("div",y,[g,v("div",h,d(f(S).map((t=>t.slice(0,7))).join(" 至 ")),1)]),v("figure",{ref_key:"abnormalRateOptionRef",ref:N,class:"tw-h-full"},null,512)]),v("div",k,[v("div",R,[q,v("div",E,d(f(S).map((t=>t.slice(0,7))).join(" 至 ")),1)]),v("figure",{ref_key:"equivalentOptionRef",ref:P,class:"tw-h-5/6"},null,512)])]),v("div",F,[v("div",O,[z,v("div",D,d(f(S).map((t=>t.slice(0,7))).join(" 至 ")),1)]),v("figure",{class:"tw-h-[60%] tw-w-full",ref_key:"totalOptionRef",ref:B},null,512)])]))}};export{_ as default};
