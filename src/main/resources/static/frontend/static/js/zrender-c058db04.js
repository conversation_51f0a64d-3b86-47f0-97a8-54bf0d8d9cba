import{_ as t}from"./tslib-a4e99503.js";var e=function(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1},r=new function(){this.browser=new e,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow="undefined"!=typeof window};"object"==typeof wx&&"function"==typeof wx.getSystemInfoSync?(r.wxa=!0,r.touchEventsSupported=!0):"undefined"==typeof document&&"undefined"!=typeof self?r.worker=!0:"undefined"==typeof navigator||0===navigator.userAgent.indexOf("Node.js")?(r.node=!0,r.svgSupported=!0):function(t,e){var r=e.browser,i=t.match(/Firefox\/([\d.]+)/),n=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),o=t.match(/Edge?\/([\d.]+)/),a=/micromessenger/i.test(t);i&&(r.firefox=!0,r.version=i[1]);n&&(r.ie=!0,r.version=n[1]);o&&(r.edge=!0,r.version=o[1],r.newEdge=+o[1].split(".")[0]>18);a&&(r.weChat=!0);e.svgSupported="undefined"!=typeof SVGRect,e.touchEventsSupported="ontouchstart"in window&&!r.ie&&!r.edge,e.pointerEventsSupported="onpointerdown"in window&&(r.edge||r.ie&&+r.version>=11),e.domSupported="undefined"!=typeof document;var s=document.documentElement.style;e.transform3dSupported=(r.ie&&"transition"in s||r.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in s)&&!("OTransition"in s),e.transformSupported=e.transform3dSupported||r.ie&&+r.version>=9}(navigator.userAgent,r);const i=r;var n=12,o="sans-serif",a=n+"px "+o;var s,h,l=function(t){var e={};if("undefined"==typeof JSON)return e;for(var r=0;r<t.length;r++){var i=String.fromCharCode(r+32),n=(t.charCodeAt(r)-20)/100;e[i]=n}return e}("007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\WQb\\0FWLg\\bWb\\WQ\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\FFF5.5N"),u={createCanvas:function(){return"undefined"!=typeof document&&document.createElement("canvas")},measureText:function(t,e){if(!s){var r=u.createCanvas();s=r&&r.getContext("2d")}if(s)return h!==e&&(h=s.font=e||a),s.measureText(t);t=t||"";var i=/(\d+)px/.exec(e=e||a),o=i&&+i[1]||n,c=0;if(e.indexOf("mono")>=0)c=o*t.length;else for(var f=0;f<t.length;f++){var p=l[t[f]];c+=null==p?o:p*o}return{width:c}},loadImage:function(t,e,r){var i=new Image;return i.onload=e,i.onerror=r,i.src=t,i}};function c(t){for(var e in u)t[e]&&(u[e]=t[e])}var f=N(["Function","RegExp","Date","Error","CanvasGradient","CanvasPattern","Image","Canvas"],(function(t,e){return t["[object "+e+"]"]=!0,t}),{}),p=N(["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64"],(function(t,e){return t["[object "+e+"Array]"]=!0,t}),{}),d=Object.prototype.toString,v=Array.prototype,y=v.forEach,g=v.filter,_=v.slice,m=v.map,x=function(){}.constructor,w=x?x.prototype:null,b="__proto__",k=2311;function S(){return k++}function T(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e]}function C(t){if(null==t||"object"!=typeof t)return t;var e=t,r=d.call(t);if("[object Array]"===r){if(!ft(t)){e=[];for(var i=0,n=t.length;i<n;i++)e[i]=C(t[i])}}else if(p[r]){if(!ft(t)){var o=t.constructor;if(o.from)e=o.from(t);else{e=new o(t.length);for(i=0,n=t.length;i<n;i++)e[i]=t[i]}}}else if(!f[r]&&!ft(t)&&!$(t))for(var a in e={},t)t.hasOwnProperty(a)&&a!==b&&(e[a]=C(t[a]));return e}function P(t,e,r){if(!G(e)||!G(t))return r?C(e):t;for(var i in e)if(e.hasOwnProperty(i)&&i!==b){var n=t[i],o=e[i];!G(o)||!G(n)||q(o)||q(n)||$(o)||$(n)||K(o)||K(n)||ft(o)||ft(n)?!r&&i in t||(t[i]=C(e[i])):P(n,o,r)}return t}function M(t,e){for(var r=t[0],i=1,n=t.length;i<n;i++)r=P(r,t[i],e);return r}function A(t,e){if(Object.assign)Object.assign(t,e);else for(var r in e)e.hasOwnProperty(r)&&r!==b&&(t[r]=e[r]);return t}function L(t,e,r){for(var i=W(e),n=0;n<i.length;n++){var o=i[n];(r?null!=e[o]:null==t[o])&&(t[o]=e[o])}return t}var D=u.createCanvas;function z(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var r=0,i=t.length;r<i;r++)if(t[r]===e)return r}return-1}function O(t,e){var r=t.prototype;function i(){}for(var n in i.prototype=e.prototype,t.prototype=new i,r)r.hasOwnProperty(n)&&(t.prototype[n]=r[n]);t.prototype.constructor=t,t.superClass=e}function I(t,e,r){if(t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,Object.getOwnPropertyNames)for(var i=Object.getOwnPropertyNames(e),n=0;n<i.length;n++){var o=i[n];"constructor"!==o&&(r?null!=e[o]:null==t[o])&&(t[o]=e[o])}else L(t,e,r)}function R(t){return!!t&&("string"!=typeof t&&"number"==typeof t.length)}function F(t,e,r){if(t&&e)if(t.forEach&&t.forEach===y)t.forEach(e,r);else if(t.length===+t.length)for(var i=0,n=t.length;i<n;i++)e.call(r,t[i],i,t);else for(var o in t)t.hasOwnProperty(o)&&e.call(r,t[o],o,t)}function B(t,e,r){if(!t)return[];if(!e)return at(t);if(t.map&&t.map===m)return t.map(e,r);for(var i=[],n=0,o=t.length;n<o;n++)i.push(e.call(r,t[n],n,t));return i}function N(t,e,r,i){if(t&&e){for(var n=0,o=t.length;n<o;n++)r=e.call(i,r,t[n],n,t);return r}}function H(t,e,r){if(!t)return[];if(!e)return at(t);if(t.filter&&t.filter===g)return t.filter(e,r);for(var i=[],n=0,o=t.length;n<o;n++)e.call(r,t[n],n,t)&&i.push(t[n]);return i}function E(t,e,r){if(t&&e)for(var i=0,n=t.length;i<n;i++)if(e.call(r,t[i],i,t))return t[i]}function W(t){if(!t)return[];if(Object.keys)return Object.keys(t);var e=[];for(var r in t)t.hasOwnProperty(r)&&e.push(r);return e}var X=w&&Y(w.bind)?w.call.bind(w.bind):function(t,e){for(var r=[],i=2;i<arguments.length;i++)r[i-2]=arguments[i];return function(){return t.apply(e,r.concat(_.call(arguments)))}};function j(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];return function(){return t.apply(this,e.concat(_.call(arguments)))}}function q(t){return Array.isArray?Array.isArray(t):"[object Array]"===d.call(t)}function Y(t){return"function"==typeof t}function V(t){return"string"==typeof t}function U(t){return"[object String]"===d.call(t)}function Z(t){return"number"==typeof t}function G(t){var e=typeof t;return"function"===e||!!t&&"object"===e}function K(t){return!!f[d.call(t)]}function Q(t){return!!p[d.call(t)]}function $(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function J(t){return null!=t.colorStops}function tt(t){return null!=t.image}function et(t){return"[object RegExp]"===d.call(t)}function rt(t){return t!=t}function it(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var r=0,i=t.length;r<i;r++)if(null!=t[r])return t[r]}function nt(t,e){return null!=t?t:e}function ot(t,e,r){return null!=t?t:null!=e?e:r}function at(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];return _.apply(t,e)}function st(t){if("number"==typeof t)return[t,t,t,t];var e=t.length;return 2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function ht(t,e){if(!t)throw new Error(e)}function lt(t){return null==t?null:"function"==typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var ut="__ec_primitive__";function ct(t){t[ut]=!0}function ft(t){return t[ut]}var pt=function(){function t(){this.data={}}return t.prototype.delete=function(t){var e=this.has(t);return e&&delete this.data[t],e},t.prototype.has=function(t){return this.data.hasOwnProperty(t)},t.prototype.get=function(t){return this.data[t]},t.prototype.set=function(t,e){return this.data[t]=e,this},t.prototype.keys=function(){return W(this.data)},t.prototype.forEach=function(t){var e=this.data;for(var r in e)e.hasOwnProperty(r)&&t(e[r],r)},t}(),dt="function"==typeof Map;var vt=function(){function t(e){var r=q(e);this.data=dt?new Map:new pt;var i=this;function n(t,e){r?i.set(t,e):i.set(e,t)}e instanceof t?e.each(n):e&&F(e,n)}return t.prototype.hasKey=function(t){return this.data.has(t)},t.prototype.get=function(t){return this.data.get(t)},t.prototype.set=function(t,e){return this.data.set(t,e),e},t.prototype.each=function(t,e){this.data.forEach((function(r,i){t.call(e,r,i)}))},t.prototype.keys=function(){var t=this.data.keys();return dt?Array.from(t):t},t.prototype.removeKey=function(t){this.data.delete(t)},t}();function yt(t){return new vt(t)}function gt(t,e){for(var r=new t.constructor(t.length+e.length),i=0;i<t.length;i++)r[i]=t[i];var n=t.length;for(i=0;i<e.length;i++)r[i+n]=e[i];return r}function _t(t,e){var r;if(Object.create)r=Object.create(t);else{var i=function(){};i.prototype=t,r=new i}return e&&A(r,e),r}function mt(t){var e=t.style;e.webkitUserSelect="none",e.userSelect="none",e.webkitTapHighlightColor="rgba(0,0,0,0)",e["-webkit-touch-callout"]="none"}function xt(t,e){return t.hasOwnProperty(e)}function wt(){}var bt=180/Math.PI;const kt=Object.freeze(Object.defineProperty({__proto__:null,HashMap:vt,RADIAN_TO_DEGREE:bt,assert:ht,bind:X,clone:C,concatArray:gt,createCanvas:D,createHashMap:yt,createObject:_t,curry:j,defaults:L,disableUserSelect:mt,each:F,eqNaN:rt,extend:A,filter:H,find:E,guid:S,hasOwn:xt,indexOf:z,inherits:O,isArray:q,isArrayLike:R,isBuiltInObject:K,isDom:$,isFunction:Y,isGradientObject:J,isImagePatternObject:tt,isNumber:Z,isObject:G,isPrimitive:ft,isRegExp:et,isString:V,isStringSafe:U,isTypedArray:Q,keys:W,logError:T,map:B,merge:P,mergeAll:M,mixin:I,noop:wt,normalizeCssArray:st,reduce:N,retrieve:it,retrieve2:nt,retrieve3:ot,setAsPrimitive:ct,slice:at,trim:lt},Symbol.toStringTag,{value:"Module"}));function St(t,e){return null==t&&(t=0),null==e&&(e=0),[t,e]}function Tt(t,e){return t[0]=e[0],t[1]=e[1],t}function Ct(t){return[t[0],t[1]]}function Pt(t,e,r){return t[0]=e,t[1]=r,t}function Mt(t,e,r){return t[0]=e[0]+r[0],t[1]=e[1]+r[1],t}function At(t,e,r,i){return t[0]=e[0]+r[0]*i,t[1]=e[1]+r[1]*i,t}function Lt(t,e,r){return t[0]=e[0]-r[0],t[1]=e[1]-r[1],t}function Dt(t){return Math.sqrt(Ot(t))}var zt=Dt;function Ot(t){return t[0]*t[0]+t[1]*t[1]}var It=Ot;function Rt(t,e,r){return t[0]=e[0]*r,t[1]=e[1]*r,t}function Ft(t,e){var r=Dt(e);return 0===r?(t[0]=0,t[1]=0):(t[0]=e[0]/r,t[1]=e[1]/r),t}function Bt(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}var Nt=Bt;function Ht(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}var Et=Ht;function Wt(t,e,r,i){return t[0]=e[0]+i*(r[0]-e[0]),t[1]=e[1]+i*(r[1]-e[1]),t}function Xt(t,e,r){var i=e[0],n=e[1];return t[0]=r[0]*i+r[2]*n+r[4],t[1]=r[1]*i+r[3]*n+r[5],t}function jt(t,e,r){return t[0]=Math.min(e[0],r[0]),t[1]=Math.min(e[1],r[1]),t}function qt(t,e,r){return t[0]=Math.max(e[0],r[0]),t[1]=Math.max(e[1],r[1]),t}const Yt=Object.freeze(Object.defineProperty({__proto__:null,add:Mt,applyTransform:Xt,clone:Ct,copy:Tt,create:St,dist:Nt,distSquare:Et,distance:Bt,distanceSquare:Ht,div:function(t,e,r){return t[0]=e[0]/r[0],t[1]=e[1]/r[1],t},dot:function(t,e){return t[0]*e[0]+t[1]*e[1]},len:Dt,lenSquare:Ot,length:zt,lengthSquare:It,lerp:Wt,max:qt,min:jt,mul:function(t,e,r){return t[0]=e[0]*r[0],t[1]=e[1]*r[1],t},negate:function(t,e){return t[0]=-e[0],t[1]=-e[1],t},normalize:Ft,scale:Rt,scaleAndAdd:At,set:Pt,sub:Lt},Symbol.toStringTag,{value:"Module"}));var Vt=function(t,e){this.target=t,this.topTarget=e&&e.topTarget};const Ut=function(){function t(t){this.handler=t,t.on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}return t.prototype._dragStart=function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent||e.__hostTarget;e&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new Vt(e,t),"dragstart",t.event))},t.prototype._drag=function(t){var e=this._draggingTarget;if(e){var r=t.offsetX,i=t.offsetY,n=r-this._x,o=i-this._y;this._x=r,this._y=i,e.drift(n,o,t),this.handler.dispatchToElement(new Vt(e,t),"drag",t.event);var a=this.handler.findHover(r,i,e).target,s=this._dropTarget;this._dropTarget=a,e!==a&&(s&&a!==s&&this.handler.dispatchToElement(new Vt(s,t),"dragleave",t.event),a&&a!==s&&this.handler.dispatchToElement(new Vt(a,t),"dragenter",t.event))}},t.prototype._dragEnd=function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.handler.dispatchToElement(new Vt(e,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new Vt(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},t}();const Zt=function(){function t(t){t&&(this._$eventProcessor=t)}return t.prototype.on=function(t,e,r,i){this._$handlers||(this._$handlers={});var n=this._$handlers;if("function"==typeof e&&(i=r,r=e,e=null),!r||!t)return this;var o=this._$eventProcessor;null!=e&&o&&o.normalizeQuery&&(e=o.normalizeQuery(e)),n[t]||(n[t]=[]);for(var a=0;a<n[t].length;a++)if(n[t][a].h===r)return this;var s={h:r,query:e,ctx:i||this,callAtLast:r.zrEventfulCallAtLast},h=n[t].length-1,l=n[t][h];return l&&l.callAtLast?n[t].splice(h,0,s):n[t].push(s),this},t.prototype.isSilent=function(t){var e=this._$handlers;return!e||!e[t]||!e[t].length},t.prototype.off=function(t,e){var r=this._$handlers;if(!r)return this;if(!t)return this._$handlers={},this;if(e){if(r[t]){for(var i=[],n=0,o=r[t].length;n<o;n++)r[t][n].h!==e&&i.push(r[t][n]);r[t]=i}r[t]&&0===r[t].length&&delete r[t]}else delete r[t];return this},t.prototype.trigger=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];if(!this._$handlers)return this;var i=this._$handlers[t],n=this._$eventProcessor;if(i)for(var o=e.length,a=i.length,s=0;s<a;s++){var h=i[s];if(!n||!n.filter||null==h.query||n.filter(t,h.query))switch(o){case 0:h.h.call(h.ctx);break;case 1:h.h.call(h.ctx,e[0]);break;case 2:h.h.call(h.ctx,e[0],e[1]);break;default:h.h.apply(h.ctx,e)}}return n&&n.afterTrigger&&n.afterTrigger(t),this},t.prototype.triggerWithContext=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];if(!this._$handlers)return this;var i=this._$handlers[t],n=this._$eventProcessor;if(i)for(var o=e.length,a=e[o-1],s=i.length,h=0;h<s;h++){var l=i[h];if(!n||!n.filter||null==l.query||n.filter(t,l.query))switch(o){case 0:l.h.call(a);break;case 1:l.h.call(a,e[0]);break;case 2:l.h.call(a,e[0],e[1]);break;default:l.h.apply(a,e.slice(1,o-1))}}return n&&n.afterTrigger&&n.afterTrigger(t),this},t}();var Gt=Math.log(2);function Kt(t,e,r,i,n,o){var a=i+"-"+n,s=t.length;if(o.hasOwnProperty(a))return o[a];if(1===e){var h=Math.round(Math.log((1<<s)-1&~n)/Gt);return t[r][h]}for(var l=i|1<<r,u=r+1;i&1<<u;)u++;for(var c=0,f=0,p=0;f<s;f++){var d=1<<f;d&n||(c+=(p%2?-1:1)*t[r][f]*Kt(t,e-1,u,l,n|d,o),p++)}return o[a]=c,c}function Qt(t,e){var r=[[t[0],t[1],1,0,0,0,-e[0]*t[0],-e[0]*t[1]],[0,0,0,t[0],t[1],1,-e[1]*t[0],-e[1]*t[1]],[t[2],t[3],1,0,0,0,-e[2]*t[2],-e[2]*t[3]],[0,0,0,t[2],t[3],1,-e[3]*t[2],-e[3]*t[3]],[t[4],t[5],1,0,0,0,-e[4]*t[4],-e[4]*t[5]],[0,0,0,t[4],t[5],1,-e[5]*t[4],-e[5]*t[5]],[t[6],t[7],1,0,0,0,-e[6]*t[6],-e[6]*t[7]],[0,0,0,t[6],t[7],1,-e[7]*t[6],-e[7]*t[7]]],i={},n=Kt(r,8,0,0,0,i);if(0!==n){for(var o=[],a=0;a<8;a++)for(var s=0;s<8;s++)null==o[s]&&(o[s]=0),o[s]+=((a+s)%2?-1:1)*Kt(r,7,0===a?1:0,1<<a,1<<s,i)/n*e[a];return function(t,e,r){var i=e*o[6]+r*o[7]+1;t[0]=(e*o[0]+r*o[1]+o[2])/i,t[1]=(e*o[3]+r*o[4]+o[5])/i}}}var $t="___zrEVENTSAVED",Jt=[];function te(t,e,r,i,n){return ee(Jt,e,i,n,!0)&&ee(t,r,Jt[0],Jt[1])}function ee(t,e,r,n,o){if(e.getBoundingClientRect&&i.domSupported&&!re(e)){var a=e[$t]||(e[$t]={}),s=function(t,e){var r=e.markers;if(r)return r;r=e.markers=[];for(var i=["left","right"],n=["top","bottom"],o=0;o<4;o++){var a=document.createElement("div"),s=o%2,h=(o>>1)%2;a.style.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[s]+":0",n[h]+":0",i[1-s]+":auto",n[1-h]+":auto",""].join("!important;"),t.appendChild(a),r.push(a)}return r}(e,a),h=function(t,e,r){for(var i=r?"invTrans":"trans",n=e[i],o=e.srcCoords,a=[],s=[],h=!0,l=0;l<4;l++){var u=t[l].getBoundingClientRect(),c=2*l,f=u.left,p=u.top;a.push(f,p),h=h&&o&&f===o[c]&&p===o[c+1],s.push(t[l].offsetLeft,t[l].offsetTop)}return h&&n?n:(e.srcCoords=a,e[i]=r?Qt(s,a):Qt(a,s))}(s,a,o);if(h)return h(t,r,n),!0}return!1}function re(t){return"CANVAS"===t.nodeName.toUpperCase()}var ie=/([&<>"'])/g,ne={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function oe(t){return null==t?"":(t+"").replace(ie,(function(t,e){return ne[e]}))}var ae=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,se=[],he=i.browser.firefox&&+i.browser.version.split(".")[0]<39;function le(t,e,r,i){return r=r||{},i?ue(t,e,r):he&&null!=e.layerX&&e.layerX!==e.offsetX?(r.zrX=e.layerX,r.zrY=e.layerY):null!=e.offsetX?(r.zrX=e.offsetX,r.zrY=e.offsetY):ue(t,e,r),r}function ue(t,e,r){if(i.domSupported&&t.getBoundingClientRect){var n=e.clientX,o=e.clientY;if(re(t)){var a=t.getBoundingClientRect();return r.zrX=n-a.left,void(r.zrY=o-a.top)}if(ee(se,t,n,o))return r.zrX=se[0],void(r.zrY=se[1])}r.zrX=r.zrY=0}function ce(t){return t||window.event}function fe(t,e,r){if(null!=(e=ce(e)).zrX)return e;var i=e.type;if(i&&i.indexOf("touch")>=0){var n="touchend"!==i?e.targetTouches[0]:e.changedTouches[0];n&&le(t,n,e,r)}else{le(t,e,e,r);var o=function(t){var e=t.wheelDelta;if(e)return e;var r=t.deltaX,i=t.deltaY;if(null==r||null==i)return e;return 3*(0!==i?Math.abs(i):Math.abs(r))*(i>0?-1:i<0?1:r>0?-1:1)}(e);e.zrDelta=o?o/120:-(e.detail||0)/3}var a=e.button;return null==e.which&&void 0!==a&&ae.test(e.type)&&(e.which=1&a?1:2&a?3:4&a?2:0),e}function pe(t,e,r,i){t.addEventListener(e,r,i)}var de=function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0};function ve(t){return 2===t.which||3===t.which}var ye=function(){function t(){this._track=[]}return t.prototype.recognize=function(t,e,r){return this._doTrack(t,e,r),this._recognize(t)},t.prototype.clear=function(){return this._track.length=0,this},t.prototype._doTrack=function(t,e,r){var i=t.touches;if(i){for(var n={points:[],touches:[],target:e,event:t},o=0,a=i.length;o<a;o++){var s=i[o],h=le(r,s,{});n.points.push([h.zrX,h.zrY]),n.touches.push(s)}this._track.push(n)}},t.prototype._recognize=function(t){for(var e in _e)if(_e.hasOwnProperty(e)){var r=_e[e](this._track,t);if(r)return r}},t}();function ge(t){var e=t[1][0]-t[0][0],r=t[1][1]-t[0][1];return Math.sqrt(e*e+r*r)}var _e={pinch:function(t,e){var r=t.length;if(r){var i,n=(t[r-1]||{}).points,o=(t[r-2]||{}).points||n;if(o&&o.length>1&&n&&n.length>1){var a=ge(n)/ge(o);!isFinite(a)&&(a=1),e.pinchScale=a;var s=[((i=n)[0][0]+i[1][0])/2,(i[0][1]+i[1][1])/2];return e.pinchX=s[0],e.pinchY=s[1],{type:"pinch",target:t[0].target,event:e}}}}};function me(){return[1,0,0,1,0,0]}function xe(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function we(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function be(t,e,r){var i=e[0]*r[0]+e[2]*r[1],n=e[1]*r[0]+e[3]*r[1],o=e[0]*r[2]+e[2]*r[3],a=e[1]*r[2]+e[3]*r[3],s=e[0]*r[4]+e[2]*r[5]+e[4],h=e[1]*r[4]+e[3]*r[5]+e[5];return t[0]=i,t[1]=n,t[2]=o,t[3]=a,t[4]=s,t[5]=h,t}function ke(t,e,r){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+r[0],t[5]=e[5]+r[1],t}function Se(t,e,r,i){void 0===i&&(i=[0,0]);var n=e[0],o=e[2],a=e[4],s=e[1],h=e[3],l=e[5],u=Math.sin(r),c=Math.cos(r);return t[0]=n*c+s*u,t[1]=-n*u+s*c,t[2]=o*c+h*u,t[3]=-o*u+c*h,t[4]=c*(a-i[0])+u*(l-i[1])+i[0],t[5]=c*(l-i[1])-u*(a-i[0])+i[1],t}function Te(t,e,r){var i=r[0],n=r[1];return t[0]=e[0]*i,t[1]=e[1]*n,t[2]=e[2]*i,t[3]=e[3]*n,t[4]=e[4]*i,t[5]=e[5]*n,t}function Ce(t,e){var r=e[0],i=e[2],n=e[4],o=e[1],a=e[3],s=e[5],h=r*a-o*i;return h?(h=1/h,t[0]=a*h,t[1]=-o*h,t[2]=-i*h,t[3]=r*h,t[4]=(i*s-a*n)*h,t[5]=(o*n-r*s)*h,t):null}function Pe(t){var e=[1,0,0,1,0,0];return we(e,t),e}const Me=Object.freeze(Object.defineProperty({__proto__:null,clone:Pe,copy:we,create:me,identity:xe,invert:Ce,mul:be,rotate:Se,scale:Te,translate:ke},Symbol.toStringTag,{value:"Module"}));const Ae=function(){function t(t,e){this.x=t||0,this.y=e||0}return t.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},t.prototype.clone=function(){return new t(this.x,this.y)},t.prototype.set=function(t,e){return this.x=t,this.y=e,this},t.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},t.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},t.prototype.scale=function(t){this.x*=t,this.y*=t},t.prototype.scaleAndAdd=function(t,e){this.x+=t.x*e,this.y+=t.y*e},t.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},t.prototype.dot=function(t){return this.x*t.x+this.y*t.y},t.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},t.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},t.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},t.prototype.distance=function(t){var e=this.x-t.x,r=this.y-t.y;return Math.sqrt(e*e+r*r)},t.prototype.distanceSquare=function(t){var e=this.x-t.x,r=this.y-t.y;return e*e+r*r},t.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},t.prototype.transform=function(t){if(t){var e=this.x,r=this.y;return this.x=t[0]*e+t[2]*r+t[4],this.y=t[1]*e+t[3]*r+t[5],this}},t.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},t.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},t.set=function(t,e,r){t.x=e,t.y=r},t.copy=function(t,e){t.x=e.x,t.y=e.y},t.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},t.lenSquare=function(t){return t.x*t.x+t.y*t.y},t.dot=function(t,e){return t.x*e.x+t.y*e.y},t.add=function(t,e,r){t.x=e.x+r.x,t.y=e.y+r.y},t.sub=function(t,e,r){t.x=e.x-r.x,t.y=e.y-r.y},t.scale=function(t,e,r){t.x=e.x*r,t.y=e.y*r},t.scaleAndAdd=function(t,e,r,i){t.x=e.x+r.x*i,t.y=e.y+r.y*i},t.lerp=function(t,e,r,i){var n=1-i;t.x=n*e.x+i*r.x,t.y=n*e.y+i*r.y},t}();var Le=Math.min,De=Math.max,ze=new Ae,Oe=new Ae,Ie=new Ae,Re=new Ae,Fe=new Ae,Be=new Ae;const Ne=function(){function t(t,e,r,i){r<0&&(t+=r,r=-r),i<0&&(e+=i,i=-i),this.x=t,this.y=e,this.width=r,this.height=i}return t.prototype.union=function(t){var e=Le(t.x,this.x),r=Le(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=De(t.x+t.width,this.x+this.width)-e:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=De(t.y+t.height,this.y+this.height)-r:this.height=t.height,this.x=e,this.y=r},t.prototype.applyTransform=function(e){t.applyTransform(this,this,e)},t.prototype.calculateTransform=function(t){var e=this,r=t.width/e.width,i=t.height/e.height,n=[1,0,0,1,0,0];return ke(n,n,[-e.x,-e.y]),Te(n,n,[r,i]),ke(n,n,[t.x,t.y]),n},t.prototype.intersect=function(e,r){if(!e)return!1;e instanceof t||(e=t.create(e));var i=this,n=i.x,o=i.x+i.width,a=i.y,s=i.y+i.height,h=e.x,l=e.x+e.width,u=e.y,c=e.y+e.height,f=!(o<h||l<n||s<u||c<a);if(r){var p=1/0,d=0,v=Math.abs(o-h),y=Math.abs(l-n),g=Math.abs(s-u),_=Math.abs(c-a),m=Math.min(v,y),x=Math.min(g,_);o<h||l<n?m>d&&(d=m,v<y?Ae.set(Be,-v,0):Ae.set(Be,y,0)):m<p&&(p=m,v<y?Ae.set(Fe,v,0):Ae.set(Fe,-y,0)),s<u||c<a?x>d&&(d=x,g<_?Ae.set(Be,0,-g):Ae.set(Be,0,_)):m<p&&(p=m,g<_?Ae.set(Fe,0,g):Ae.set(Fe,0,-_))}return r&&Ae.copy(r,f?Fe:Be),f},t.prototype.contain=function(t,e){var r=this;return t>=r.x&&t<=r.x+r.width&&e>=r.y&&e<=r.y+r.height},t.prototype.clone=function(){return new t(this.x,this.y,this.width,this.height)},t.prototype.copy=function(e){t.copy(this,e)},t.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},t.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},t.prototype.isZero=function(){return 0===this.width||0===this.height},t.create=function(e){return new t(e.x,e.y,e.width,e.height)},t.copy=function(t,e){t.x=e.x,t.y=e.y,t.width=e.width,t.height=e.height},t.applyTransform=function(e,r,i){if(i){if(i[1]<1e-5&&i[1]>-1e-5&&i[2]<1e-5&&i[2]>-1e-5){var n=i[0],o=i[3],a=i[4],s=i[5];return e.x=r.x*n+a,e.y=r.y*o+s,e.width=r.width*n,e.height=r.height*o,e.width<0&&(e.x+=e.width,e.width=-e.width),void(e.height<0&&(e.y+=e.height,e.height=-e.height))}ze.x=Ie.x=r.x,ze.y=Re.y=r.y,Oe.x=Re.x=r.x+r.width,Oe.y=Ie.y=r.y+r.height,ze.transform(i),Re.transform(i),Oe.transform(i),Ie.transform(i),e.x=Le(ze.x,Oe.x,Ie.x,Re.x),e.y=Le(ze.y,Oe.y,Ie.y,Re.y);var h=De(ze.x,Oe.x,Ie.x,Re.x),l=De(ze.y,Oe.y,Ie.y,Re.y);e.width=h-e.x,e.height=l-e.y}else e!==r&&t.copy(e,r)},t}();var He="silent";function Ee(){de(this.event)}var We=function(e){function r(){var t=null!==e&&e.apply(this,arguments)||this;return t.handler=null,t}return t(r,e),r.prototype.dispose=function(){},r.prototype.setCursor=function(){},r}(Zt),Xe=function(t,e){this.x=t,this.y=e},je=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],qe=new Ne(0,0,0,0),Ye=function(e){function r(t,r,i,n,o){var a=e.call(this)||this;return a._hovered=new Xe(0,0),a.storage=t,a.painter=r,a.painterRoot=n,a._pointerSize=o,i=i||new We,a.proxy=null,a.setHandlerProxy(i),a._draggingMgr=new Ut(a),a}return t(r,e),r.prototype.setHandlerProxy=function(t){this.proxy&&this.proxy.dispose(),t&&(F(je,(function(e){t.on&&t.on(e,this[e],this)}),this),t.handler=this),this.proxy=t},r.prototype.mousemove=function(t){var e=t.zrX,r=t.zrY,i=Ze(this,e,r),n=this._hovered,o=n.target;o&&!o.__zr&&(o=(n=this.findHover(n.x,n.y)).target);var a=this._hovered=i?new Xe(e,r):this.findHover(e,r),s=a.target,h=this.proxy;h.setCursor&&h.setCursor(s?s.cursor:"default"),o&&s!==o&&this.dispatchToElement(n,"mouseout",t),this.dispatchToElement(a,"mousemove",t),s&&s!==o&&this.dispatchToElement(a,"mouseover",t)},r.prototype.mouseout=function(t){var e=t.zrEventControl;"only_globalout"!==e&&this.dispatchToElement(this._hovered,"mouseout",t),"no_globalout"!==e&&this.trigger("globalout",{type:"globalout",event:t})},r.prototype.resize=function(){this._hovered=new Xe(0,0)},r.prototype.dispatch=function(t,e){var r=this[t];r&&r.call(this,e)},r.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},r.prototype.setCursorStyle=function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},r.prototype.dispatchToElement=function(t,e,r){var i=(t=t||{}).target;if(!i||!i.silent){for(var n="on"+e,o=function(t,e,r){return{type:t,event:r,target:e.target,topTarget:e.topTarget,cancelBubble:!1,offsetX:r.zrX,offsetY:r.zrY,gestureEvent:r.gestureEvent,pinchX:r.pinchX,pinchY:r.pinchY,pinchScale:r.pinchScale,wheelDelta:r.zrDelta,zrByTouch:r.zrByTouch,which:r.which,stop:Ee}}(e,t,r);i&&(i[n]&&(o.cancelBubble=!!i[n].call(i,o)),i.trigger(e,o),i=i.__hostTarget?i.__hostTarget:i.parent,!o.cancelBubble););o.cancelBubble||(this.trigger(e,o),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer((function(t){"function"==typeof t[n]&&t[n].call(t,o),t.trigger&&t.trigger(e,o)})))}},r.prototype.findHover=function(t,e,r){var i=this.storage.getDisplayList(),n=new Xe(t,e);if(Ue(i,n,t,e,r),this._pointerSize&&!n.target){for(var o=[],a=this._pointerSize,s=a/2,h=new Ne(t-s,e-s,a,a),l=i.length-1;l>=0;l--){var u=i[l];u===r||u.ignore||u.ignoreCoarsePointer||u.parent&&u.parent.ignoreCoarsePointer||(qe.copy(u.getBoundingRect()),u.transform&&qe.applyTransform(u.transform),qe.intersect(h)&&o.push(u))}if(o.length)for(var c=Math.PI/12,f=2*Math.PI,p=0;p<s;p+=4)for(var d=0;d<f;d+=c){if(Ue(o,n,t+p*Math.cos(d),e+p*Math.sin(d),r),n.target)return n}}return n},r.prototype.processGesture=function(t,e){this._gestureMgr||(this._gestureMgr=new ye);var r=this._gestureMgr;"start"===e&&r.clear();var i=r.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom);if("end"===e&&r.clear(),i){var n=i.type;t.gestureEvent=n;var o=new Xe;o.target=i.target,this.dispatchToElement(o,n,i.event)}},r}(Zt);function Ve(t,e,r){if(t[t.rectHover?"rectContain":"contain"](e,r)){for(var i=t,n=void 0,o=!1;i;){if(i.ignoreClip&&(o=!0),!o){var a=i.getClipPath();if(a&&!a.contain(e,r))return!1}i.silent&&(n=!0);var s=i.__hostTarget;i=s||i.parent}return!n||He}return!1}function Ue(t,e,r,i,n){for(var o=t.length-1;o>=0;o--){var a=t[o],s=void 0;if(a!==n&&!a.ignore&&(s=Ve(a,r,i))&&(!e.topTarget&&(e.topTarget=a),s!==He)){e.target=a;break}}}function Ze(t,e,r){var i=t.painter;return e<0||e>i.getWidth()||r<0||r>i.getHeight()}F(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],(function(t){Ye.prototype[t]=function(e){var r,i,n=e.zrX,o=e.zrY,a=Ze(this,n,o);if("mouseup"===t&&a||(i=(r=this.findHover(n,o)).target),"mousedown"===t)this._downEl=i,this._downPoint=[e.zrX,e.zrY],this._upEl=i;else if("mouseup"===t)this._upEl=i;else if("click"===t){if(this._downEl!==this._upEl||!this._downPoint||Nt(this._downPoint,[e.zrX,e.zrY])>4)return;this._downPoint=null}this.dispatchToElement(r,t,e)}}));const Ge=Ye;var Ke=32,Qe=7;function $e(t,e,r,i){var n=e+1;if(n===r)return 1;if(i(t[n++],t[e])<0){for(;n<r&&i(t[n],t[n-1])<0;)n++;!function(t,e,r){r--;for(;e<r;){var i=t[e];t[e++]=t[r],t[r--]=i}}(t,e,n)}else for(;n<r&&i(t[n],t[n-1])>=0;)n++;return n-e}function Je(t,e,r,i,n){for(i===e&&i++;i<r;i++){for(var o,a=t[i],s=e,h=i;s<h;)n(a,t[o=s+h>>>1])<0?h=o:s=o+1;var l=i-s;switch(l){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;l>0;)t[s+l]=t[s+l-1],l--}t[s]=a}}function tr(t,e,r,i,n,o){var a=0,s=0,h=1;if(o(t,e[r+n])>0){for(s=i-n;h<s&&o(t,e[r+n+h])>0;)a=h,(h=1+(h<<1))<=0&&(h=s);h>s&&(h=s),a+=n,h+=n}else{for(s=n+1;h<s&&o(t,e[r+n-h])<=0;)a=h,(h=1+(h<<1))<=0&&(h=s);h>s&&(h=s);var l=a;a=n-h,h=n-l}for(a++;a<h;){var u=a+(h-a>>>1);o(t,e[r+u])>0?a=u+1:h=u}return h}function er(t,e,r,i,n,o){var a=0,s=0,h=1;if(o(t,e[r+n])<0){for(s=n+1;h<s&&o(t,e[r+n-h])<0;)a=h,(h=1+(h<<1))<=0&&(h=s);h>s&&(h=s);var l=a;a=n-h,h=n-l}else{for(s=i-n;h<s&&o(t,e[r+n+h])>=0;)a=h,(h=1+(h<<1))<=0&&(h=s);h>s&&(h=s),a+=n,h+=n}for(a++;a<h;){var u=a+(h-a>>>1);o(t,e[r+u])<0?h=u:a=u+1}return h}function rr(t,e){var r,i,n=Qe,o=0,a=[];function s(s){var h=r[s],l=i[s],u=r[s+1],c=i[s+1];i[s]=l+c,s===o-3&&(r[s+1]=r[s+2],i[s+1]=i[s+2]),o--;var f=er(t[u],t,h,l,0,e);h+=f,0!==(l-=f)&&0!==(c=tr(t[h+l-1],t,u,c,c-1,e))&&(l<=c?function(r,i,o,s){var h=0;for(h=0;h<i;h++)a[h]=t[r+h];var l=0,u=o,c=r;if(t[c++]=t[u++],0==--s){for(h=0;h<i;h++)t[c+h]=a[l+h];return}if(1===i){for(h=0;h<s;h++)t[c+h]=t[u+h];return void(t[c+s]=a[l])}var f,p,d,v=n;for(;;){f=0,p=0,d=!1;do{if(e(t[u],a[l])<0){if(t[c++]=t[u++],p++,f=0,0==--s){d=!0;break}}else if(t[c++]=a[l++],f++,p=0,1==--i){d=!0;break}}while((f|p)<v);if(d)break;do{if(0!==(f=er(t[u],a,l,i,0,e))){for(h=0;h<f;h++)t[c+h]=a[l+h];if(c+=f,l+=f,(i-=f)<=1){d=!0;break}}if(t[c++]=t[u++],0==--s){d=!0;break}if(0!==(p=tr(a[l],t,u,s,0,e))){for(h=0;h<p;h++)t[c+h]=t[u+h];if(c+=p,u+=p,0===(s-=p)){d=!0;break}}if(t[c++]=a[l++],1==--i){d=!0;break}v--}while(f>=Qe||p>=Qe);if(d)break;v<0&&(v=0),v+=2}if((n=v)<1&&(n=1),1===i){for(h=0;h<s;h++)t[c+h]=t[u+h];t[c+s]=a[l]}else{if(0===i)throw new Error;for(h=0;h<i;h++)t[c+h]=a[l+h]}}(h,l,u,c):function(r,i,o,s){var h=0;for(h=0;h<s;h++)a[h]=t[o+h];var l=r+i-1,u=s-1,c=o+s-1,f=0,p=0;if(t[c--]=t[l--],0==--i){for(f=c-(s-1),h=0;h<s;h++)t[f+h]=a[h];return}if(1===s){for(p=(c-=i)+1,f=(l-=i)+1,h=i-1;h>=0;h--)t[p+h]=t[f+h];return void(t[c]=a[u])}var d=n;for(;;){var v=0,y=0,g=!1;do{if(e(a[u],t[l])<0){if(t[c--]=t[l--],v++,y=0,0==--i){g=!0;break}}else if(t[c--]=a[u--],y++,v=0,1==--s){g=!0;break}}while((v|y)<d);if(g)break;do{if(0!==(v=i-er(a[u],t,r,i,i-1,e))){for(i-=v,p=(c-=v)+1,f=(l-=v)+1,h=v-1;h>=0;h--)t[p+h]=t[f+h];if(0===i){g=!0;break}}if(t[c--]=a[u--],1==--s){g=!0;break}if(0!==(y=s-tr(t[l],a,0,s,s-1,e))){for(s-=y,p=(c-=y)+1,f=(u-=y)+1,h=0;h<y;h++)t[p+h]=a[f+h];if(s<=1){g=!0;break}}if(t[c--]=t[l--],0==--i){g=!0;break}d--}while(v>=Qe||y>=Qe);if(g)break;d<0&&(d=0),d+=2}(n=d)<1&&(n=1);if(1===s){for(p=(c-=i)+1,f=(l-=i)+1,h=i-1;h>=0;h--)t[p+h]=t[f+h];t[c]=a[u]}else{if(0===s)throw new Error;for(f=c-(s-1),h=0;h<s;h++)t[f+h]=a[h]}}(h,l,u,c))}return r=[],i=[],{mergeRuns:function(){for(;o>1;){var t=o-2;if(t>=1&&i[t-1]<=i[t]+i[t+1]||t>=2&&i[t-2]<=i[t]+i[t-1])i[t-1]<i[t+1]&&t--;else if(i[t]>i[t+1])break;s(t)}},forceMergeRuns:function(){for(;o>1;){var t=o-2;t>0&&i[t-1]<i[t+1]&&t--,s(t)}},pushRun:function(t,e){r[o]=t,i[o]=e,o+=1}}}function ir(t,e,r,i){r||(r=0),i||(i=t.length);var n=i-r;if(!(n<2)){var o=0;if(n<Ke)Je(t,r,i,r+(o=$e(t,r,i,e)),e);else{var a=rr(t,e),s=function(t){for(var e=0;t>=Ke;)e|=1&t,t>>=1;return t+e}(n);do{if((o=$e(t,r,i,e))<s){var h=n;h>s&&(h=s),Je(t,r,r+h,r+o,e),o=h}a.pushRun(r,o),a.mergeRuns(),n-=o,r+=o}while(0!==n);a.forceMergeRuns()}}}var nr=1,or=4,ar=!1;function sr(){ar||(ar=!0)}function hr(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}const lr=function(){function t(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=hr}return t.prototype.traverse=function(t,e){for(var r=0;r<this._roots.length;r++)this._roots[r].traverse(t,e)},t.prototype.getDisplayList=function(t,e){e=e||!1;var r=this._displayList;return!t&&r.length||this.updateDisplayList(e),r},t.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var e=this._roots,r=this._displayList,i=0,n=e.length;i<n;i++)this._updateAndAddDisplayable(e[i],null,t);r.length=this._displayListLen,ir(r,hr)},t.prototype._updateAndAddDisplayable=function(t,e,r){if(!t.ignore||r){t.beforeUpdate(),t.update(),t.afterUpdate();var i=t.getClipPath();if(t.ignoreClip)e=null;else if(i){e=e?e.slice():[];for(var n=i,o=t;n;)n.parent=o,n.updateTransform(),e.push(n),o=n,n=n.getClipPath()}if(t.childrenRef){for(var a=t.childrenRef(),s=0;s<a.length;s++){var h=a[s];t.__dirty&&(h.__dirty|=nr),this._updateAndAddDisplayable(h,e,r)}t.__dirty=0}else{var l=t;e&&e.length?l.__clipPaths=e:l.__clipPaths&&l.__clipPaths.length>0&&(l.__clipPaths=[]),isNaN(l.z)&&(sr(),l.z=0),isNaN(l.z2)&&(sr(),l.z2=0),isNaN(l.zlevel)&&(sr(),l.zlevel=0),this._displayList[this._displayListLen++]=l}var u=t.getDecalElement&&t.getDecalElement();u&&this._updateAndAddDisplayable(u,e,r);var c=t.getTextGuideLine();c&&this._updateAndAddDisplayable(c,e,r);var f=t.getTextContent();f&&this._updateAndAddDisplayable(f,e,r)}},t.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},t.prototype.delRoot=function(t){if(t instanceof Array)for(var e=0,r=t.length;e<r;e++)this.delRoot(t[e]);else{var i=z(this._roots,t);i>=0&&this._roots.splice(i,1)}},t.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},t.prototype.getRoots=function(){return this._roots},t.prototype.dispose=function(){this._displayList=null,this._roots=null},t}();const ur=i.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){return setTimeout(t,16)};var cr={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,r=.1;return 0===t?0:1===t?1:(!r||r<1?(r=1,e=.1):e=.4*Math.asin(1/r)/(2*Math.PI),-r*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/.4))},elasticOut:function(t){var e,r=.1;return 0===t?0:1===t?1:(!r||r<1?(r=1,e=.1):e=.4*Math.asin(1/r)/(2*Math.PI),r*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/.4)+1)},elasticInOut:function(t){var e,r=.1,i=.4;return 0===t?0:1===t?1:(!r||r<1?(r=1,e=.1):e=i*Math.asin(1/r)/(2*Math.PI),(t*=2)<1?r*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i)*-.5:r*Math.pow(2,-10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i)*.5+1)},backIn:function(t){var e=1.70158;return t*t*((e+1)*t-e)},backOut:function(t){var e=1.70158;return--t*t*((e+1)*t+e)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((e+1)*t-e)*.5:.5*((t-=2)*t*((e+1)*t+e)+2)},bounceIn:function(t){return 1-cr.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*cr.bounceIn(2*t):.5*cr.bounceOut(2*t-1)+.5}};const fr=cr;var pr=Math.pow,dr=Math.sqrt,vr=1e-8,yr=1e-4,gr=dr(3),_r=1/3,mr=St(),xr=St(),wr=St();function br(t){return t>-vr&&t<vr}function kr(t){return t>vr||t<-vr}function Sr(t,e,r,i,n){var o=1-n;return o*o*(o*t+3*n*e)+n*n*(n*i+3*o*r)}function Tr(t,e,r,i,n){var o=1-n;return 3*(((e-t)*o+2*(r-e)*n)*o+(i-r)*n*n)}function Cr(t,e,r,i,n,o){var a=i+3*(e-r)-t,s=3*(r-2*e+t),h=3*(e-t),l=t-n,u=s*s-3*a*h,c=s*h-9*a*l,f=h*h-3*s*l,p=0;if(br(u)&&br(c)){if(br(s))o[0]=0;else(S=-h/s)>=0&&S<=1&&(o[p++]=S)}else{var d=c*c-4*u*f;if(br(d)){var v=c/u,y=-v/2;(S=-s/a+v)>=0&&S<=1&&(o[p++]=S),y>=0&&y<=1&&(o[p++]=y)}else if(d>0){var g=dr(d),_=u*s+1.5*a*(-c+g),m=u*s+1.5*a*(-c-g);(S=(-s-((_=_<0?-pr(-_,_r):pr(_,_r))+(m=m<0?-pr(-m,_r):pr(m,_r))))/(3*a))>=0&&S<=1&&(o[p++]=S)}else{var x=(2*u*s-3*a*c)/(2*dr(u*u*u)),w=Math.acos(x)/3,b=dr(u),k=Math.cos(w),S=(-s-2*b*k)/(3*a),T=(y=(-s+b*(k+gr*Math.sin(w)))/(3*a),(-s+b*(k-gr*Math.sin(w)))/(3*a));S>=0&&S<=1&&(o[p++]=S),y>=0&&y<=1&&(o[p++]=y),T>=0&&T<=1&&(o[p++]=T)}}return p}function Pr(t,e,r,i,n){var o=6*r-12*e+6*t,a=9*e+3*i-3*t-9*r,s=3*e-3*t,h=0;if(br(a)){if(kr(o))(u=-s/o)>=0&&u<=1&&(n[h++]=u)}else{var l=o*o-4*a*s;if(br(l))n[0]=-o/(2*a);else if(l>0){var u,c=dr(l),f=(-o-c)/(2*a);(u=(-o+c)/(2*a))>=0&&u<=1&&(n[h++]=u),f>=0&&f<=1&&(n[h++]=f)}}return h}function Mr(t,e,r,i,n,o){var a=(e-t)*n+t,s=(r-e)*n+e,h=(i-r)*n+r,l=(s-a)*n+a,u=(h-s)*n+s,c=(u-l)*n+l;o[0]=t,o[1]=a,o[2]=l,o[3]=c,o[4]=c,o[5]=u,o[6]=h,o[7]=i}function Ar(t,e,r,i,n,o,a,s,h,l,u){var c,f,p,d,v,y=.005,g=1/0;mr[0]=h,mr[1]=l;for(var _=0;_<1;_+=.05)xr[0]=Sr(t,r,n,a,_),xr[1]=Sr(e,i,o,s,_),(d=Et(mr,xr))<g&&(c=_,g=d);g=1/0;for(var m=0;m<32&&!(y<yr);m++)f=c-y,p=c+y,xr[0]=Sr(t,r,n,a,f),xr[1]=Sr(e,i,o,s,f),d=Et(xr,mr),f>=0&&d<g?(c=f,g=d):(wr[0]=Sr(t,r,n,a,p),wr[1]=Sr(e,i,o,s,p),v=Et(wr,mr),p<=1&&v<g?(c=p,g=v):y*=.5);return u&&(u[0]=Sr(t,r,n,a,c),u[1]=Sr(e,i,o,s,c)),dr(g)}function Lr(t,e,r,i,n,o,a,s,h){for(var l=t,u=e,c=0,f=1/h,p=1;p<=h;p++){var d=p*f,v=Sr(t,r,n,a,d),y=Sr(e,i,o,s,d),g=v-l,_=y-u;c+=Math.sqrt(g*g+_*_),l=v,u=y}return c}function Dr(t,e,r,i){var n=1-i;return n*(n*t+2*i*e)+i*i*r}function zr(t,e,r,i){return 2*((1-i)*(e-t)+i*(r-e))}function Or(t,e,r){var i=t+r-2*e;return 0===i?.5:(t-e)/i}function Ir(t,e,r,i,n){var o=(e-t)*i+t,a=(r-e)*i+e,s=(a-o)*i+o;n[0]=t,n[1]=o,n[2]=s,n[3]=s,n[4]=a,n[5]=r}function Rr(t,e,r,i,n,o,a,s,h){var l,u=.005,c=1/0;mr[0]=a,mr[1]=s;for(var f=0;f<1;f+=.05){xr[0]=Dr(t,r,n,f),xr[1]=Dr(e,i,o,f),(y=Et(mr,xr))<c&&(l=f,c=y)}c=1/0;for(var p=0;p<32&&!(u<yr);p++){var d=l-u,v=l+u;xr[0]=Dr(t,r,n,d),xr[1]=Dr(e,i,o,d);var y=Et(xr,mr);if(d>=0&&y<c)l=d,c=y;else{wr[0]=Dr(t,r,n,v),wr[1]=Dr(e,i,o,v);var g=Et(wr,mr);v<=1&&g<c?(l=v,c=g):u*=.5}}return h&&(h[0]=Dr(t,r,n,l),h[1]=Dr(e,i,o,l)),dr(c)}function Fr(t,e,r,i,n,o,a){for(var s=t,h=e,l=0,u=1/a,c=1;c<=a;c++){var f=c*u,p=Dr(t,r,n,f),d=Dr(e,i,o,f),v=p-s,y=d-h;l+=Math.sqrt(v*v+y*y),s=p,h=d}return l}var Br=/cubic-bezier\(([0-9,\.e ]+)\)/;function Nr(t){var e=t&&Br.exec(t);if(e){var r=e[1].split(","),i=+lt(r[0]),n=+lt(r[1]),o=+lt(r[2]),a=+lt(r[3]);if(isNaN(i+n+o+a))return;var s=[];return function(t){return t<=0?0:t>=1?1:Cr(0,i,o,1,t,s)&&Sr(0,n,a,1,s[0])}}}const Hr=function(){function t(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||wt,this.ondestroy=t.ondestroy||wt,this.onrestart=t.onrestart||wt,t.easing&&this.setEasing(t.easing)}return t.prototype.step=function(t,e){if(this._inited||(this._startTime=t+this._delay,this._inited=!0),!this._paused){var r=this._life,i=t-this._startTime-this._pausedTime,n=i/r;n<0&&(n=0),n=Math.min(n,1);var o=this.easingFunc,a=o?o(n):n;if(this.onframe(a),1===n){if(!this.loop)return!0;var s=i%r;this._startTime=t-s,this._pausedTime=0,this.onrestart()}return!1}this._pausedTime+=e},t.prototype.pause=function(){this._paused=!0},t.prototype.resume=function(){this._paused=!1},t.prototype.setEasing=function(t){this.easing=t,this.easingFunc=Y(t)?t:fr[t]||Nr(t)},t}();var Er=function(t){this.value=t},Wr=function(){function t(){this._len=0}return t.prototype.insert=function(t){var e=new Er(t);return this.insertEntry(e),e},t.prototype.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},t.prototype.remove=function(t){var e=t.prev,r=t.next;e?e.next=r:this.head=r,r?r.prev=e:this.tail=e,t.next=t.prev=null,this._len--},t.prototype.len=function(){return this._len},t.prototype.clear=function(){this.head=this.tail=null,this._len=0},t}();const Xr=function(){function t(t){this._list=new Wr,this._maxSize=10,this._map={},this._maxSize=t}return t.prototype.put=function(t,e){var r=this._list,i=this._map,n=null;if(null==i[t]){var o=r.len(),a=this._lastRemovedEntry;if(o>=this._maxSize&&o>0){var s=r.head;r.remove(s),delete i[s.key],n=s.value,this._lastRemovedEntry=s}a?a.value=e:a=new Er(e),a.key=t,r.insertEntry(a),i[t]=a}return n},t.prototype.get=function(t){var e=this._map[t],r=this._list;if(null!=e)return e!==r.tail&&(r.remove(e),r.insertEntry(e)),e.value},t.prototype.clear=function(){this._list.clear(),this._map={}},t.prototype.len=function(){return this._list.len()},t}();var jr={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function qr(t){return(t=Math.round(t))<0?0:t>255?255:t}function Yr(t){return t<0?0:t>1?1:t}function Vr(t){var e=t;return e.length&&"%"===e.charAt(e.length-1)?qr(parseFloat(e)/100*255):qr(parseInt(e,10))}function Ur(t){var e=t;return e.length&&"%"===e.charAt(e.length-1)?Yr(parseFloat(e)/100):Yr(parseFloat(e))}function Zr(t,e,r){return r<0?r+=1:r>1&&(r-=1),6*r<1?t+(e-t)*r*6:2*r<1?e:3*r<2?t+(e-t)*(2/3-r)*6:t}function Gr(t,e,r){return t+(e-t)*r}function Kr(t,e,r,i,n){return t[0]=e,t[1]=r,t[2]=i,t[3]=n,t}function Qr(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}var $r=new Xr(20),Jr=null;function ti(t,e){Jr&&Qr(Jr,e),Jr=$r.put(t,Jr||e.slice())}function ei(t,e){if(t){e=e||[];var r=$r.get(t);if(r)return Qr(e,r);var i=(t+="").replace(/ /g,"").toLowerCase();if(i in jr)return Qr(e,jr[i]),ti(t,e),e;var n,o=i.length;if("#"===i.charAt(0))return 4===o||5===o?(n=parseInt(i.slice(1,4),16))>=0&&n<=4095?(Kr(e,(3840&n)>>4|(3840&n)>>8,240&n|(240&n)>>4,15&n|(15&n)<<4,5===o?parseInt(i.slice(4),16)/15:1),ti(t,e),e):void Kr(e,0,0,0,1):7===o||9===o?(n=parseInt(i.slice(1,7),16))>=0&&n<=16777215?(Kr(e,(16711680&n)>>16,(65280&n)>>8,255&n,9===o?parseInt(i.slice(7),16)/255:1),ti(t,e),e):void Kr(e,0,0,0,1):void 0;var a=i.indexOf("("),s=i.indexOf(")");if(-1!==a&&s+1===o){var h=i.substr(0,a),l=i.substr(a+1,s-(a+1)).split(","),u=1;switch(h){case"rgba":if(4!==l.length)return 3===l.length?Kr(e,+l[0],+l[1],+l[2],1):Kr(e,0,0,0,1);u=Ur(l.pop());case"rgb":return l.length>=3?(Kr(e,Vr(l[0]),Vr(l[1]),Vr(l[2]),3===l.length?u:Ur(l[3])),ti(t,e),e):void Kr(e,0,0,0,1);case"hsla":return 4!==l.length?void Kr(e,0,0,0,1):(l[3]=Ur(l[3]),ri(l,e),ti(t,e),e);case"hsl":return 3!==l.length?void Kr(e,0,0,0,1):(ri(l,e),ti(t,e),e);default:return}}Kr(e,0,0,0,1)}}function ri(t,e){var r=(parseFloat(t[0])%360+360)%360/360,i=Ur(t[1]),n=Ur(t[2]),o=n<=.5?n*(i+1):n+i-n*i,a=2*n-o;return Kr(e=e||[],qr(255*Zr(a,o,r+1/3)),qr(255*Zr(a,o,r)),qr(255*Zr(a,o,r-1/3)),1),4===t.length&&(e[3]=t[3]),e}function ii(t,e){var r=ei(t);if(r){for(var i=0;i<3;i++)r[i]=e<0?r[i]*(1-e)|0:(255-r[i])*e+r[i]|0,r[i]>255?r[i]=255:r[i]<0&&(r[i]=0);return ui(r,4===r.length?"rgba":"rgb")}}function ni(t,e,r){if(e&&e.length&&t>=0&&t<=1){r=r||[];var i=t*(e.length-1),n=Math.floor(i),o=Math.ceil(i),a=e[n],s=e[o],h=i-n;return r[0]=qr(Gr(a[0],s[0],h)),r[1]=qr(Gr(a[1],s[1],h)),r[2]=qr(Gr(a[2],s[2],h)),r[3]=Yr(Gr(a[3],s[3],h)),r}}var oi=ni;function ai(t,e,r){if(e&&e.length&&t>=0&&t<=1){var i=t*(e.length-1),n=Math.floor(i),o=Math.ceil(i),a=ei(e[n]),s=ei(e[o]),h=i-n,l=ui([qr(Gr(a[0],s[0],h)),qr(Gr(a[1],s[1],h)),qr(Gr(a[2],s[2],h)),Yr(Gr(a[3],s[3],h))],"rgba");return r?{color:l,leftIndex:n,rightIndex:o,value:i}:l}}var si=ai;function hi(t,e,r,i){var n,o=ei(t);if(t)return o=function(t){if(t){var e,r,i=t[0]/255,n=t[1]/255,o=t[2]/255,a=Math.min(i,n,o),s=Math.max(i,n,o),h=s-a,l=(s+a)/2;if(0===h)e=0,r=0;else{r=l<.5?h/(s+a):h/(2-s-a);var u=((s-i)/6+h/2)/h,c=((s-n)/6+h/2)/h,f=((s-o)/6+h/2)/h;i===s?e=f-c:n===s?e=1/3+u-f:o===s&&(e=2/3+c-u),e<0&&(e+=1),e>1&&(e-=1)}var p=[360*e,r,l];return null!=t[3]&&p.push(t[3]),p}}(o),null!=e&&(o[0]=(n=e,(n=Math.round(n))<0?0:n>360?360:n)),null!=r&&(o[1]=Ur(r)),null!=i&&(o[2]=Ur(i)),ui(ri(o),"rgba")}function li(t,e){var r=ei(t);if(r&&null!=e)return r[3]=Yr(e),ui(r,"rgba")}function ui(t,e){if(t&&t.length){var r=t[0]+","+t[1]+","+t[2];return"rgba"!==e&&"hsva"!==e&&"hsla"!==e||(r+=","+t[3]),e+"("+r+")"}}function ci(t,e){var r=ei(t);return r?(.299*r[0]+.587*r[1]+.114*r[2])*r[3]/255+(1-r[3])*e:0}var fi=new Xr(100);function pi(t){if(V(t)){var e=fi.get(t);return e||(e=ii(t,-.1),fi.put(t,e)),e}if(J(t)){var r=A({},t);return r.colorStops=B(t.colorStops,(function(t){return{offset:t.offset,color:ii(t.color,-.1)}})),r}return t}const di=Object.freeze(Object.defineProperty({__proto__:null,fastLerp:ni,fastMapToColor:oi,lerp:ai,lift:ii,liftColor:pi,lum:ci,mapToColor:si,modifyAlpha:li,modifyHSL:hi,parse:ei,random:function(){return ui([Math.round(255*Math.random()),Math.round(255*Math.random()),Math.round(255*Math.random())],"rgb")},stringify:ui,toHex:function(t){var e=ei(t);if(e)return((1<<24)+(e[0]<<16)+(e[1]<<8)+ +e[2]).toString(16).slice(1)}},Symbol.toStringTag,{value:"Module"}));var vi=Math.round;function yi(t){var e;if(t&&"transparent"!==t){if("string"==typeof t&&t.indexOf("rgba")>-1){var r=ei(t);r&&(t="rgb("+r[0]+","+r[1]+","+r[2]+")",e=r[3])}}else t="none";return{color:t,opacity:null==e?1:e}}var gi=1e-4;function _i(t){return t<gi&&t>-gi}function mi(t){return vi(1e3*t)/1e3}function xi(t){return vi(1e4*t)/1e4}var wi={left:"start",right:"end",center:"middle",middle:"middle"};function bi(t){return t&&!!t.image}function ki(t){return bi(t)||function(t){return t&&!!t.svgElement}(t)}function Si(t){return"linear"===t.type}function Ti(t){return"radial"===t.type}function Ci(t){return t&&("linear"===t.type||"radial"===t.type)}function Pi(t){return"url(#"+t+")"}function Mi(t){var e=t.getGlobalScale(),r=Math.max(e[0],e[1]);return Math.max(Math.ceil(Math.log(r)/Math.log(10)),1)}function Ai(t){var e=t.x||0,r=t.y||0,i=(t.rotation||0)*bt,n=nt(t.scaleX,1),o=nt(t.scaleY,1),a=t.skewX||0,s=t.skewY||0,h=[];return(e||r)&&h.push("translate("+e+"px,"+r+"px)"),i&&h.push("rotate("+i+")"),1===n&&1===o||h.push("scale("+n+","+o+")"),(a||s)&&h.push("skew("+vi(a*bt)+"deg, "+vi(s*bt)+"deg)"),h.join(" ")}var Li=i.hasGlobalWindow&&Y(window.btoa)?function(t){return window.btoa(unescape(encodeURIComponent(t)))}:"undefined"!=typeof Buffer?function(t){return Buffer.from(t).toString("base64")}:function(t){return null},Di=Array.prototype.slice;function zi(t,e,r){return(e-t)*r+t}function Oi(t,e,r,i){for(var n=e.length,o=0;o<n;o++)t[o]=zi(e[o],r[o],i);return t}function Ii(t,e,r,i){for(var n=e.length,o=0;o<n;o++)t[o]=e[o]+r[o]*i;return t}function Ri(t,e,r,i){for(var n=e.length,o=n&&e[0].length,a=0;a<n;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=e[a][s]+r[a][s]*i}return t}function Fi(t,e){for(var r=t.length,i=e.length,n=r>i?e:t,o=Math.min(r,i),a=n[o-1]||{color:[0,0,0,0],offset:0},s=o;s<Math.max(r,i);s++)n.push({offset:a.offset,color:a.color.slice()})}function Bi(t,e,r){var i=t,n=e;if(i.push&&n.push){var o=i.length,a=n.length;if(o!==a)if(o>a)i.length=a;else for(var s=o;s<a;s++)i.push(1===r?n[s]:Di.call(n[s]));var h=i[0]&&i[0].length;for(s=0;s<i.length;s++)if(1===r)isNaN(i[s])&&(i[s]=n[s]);else for(var l=0;l<h;l++)isNaN(i[s][l])&&(i[s][l]=n[s][l])}}function Ni(t){if(R(t)){var e=t.length;if(R(t[0])){for(var r=[],i=0;i<e;i++)r.push(Di.call(t[i]));return r}return Di.call(t)}return t}function Hi(t){return t[0]=Math.floor(t[0])||0,t[1]=Math.floor(t[1])||0,t[2]=Math.floor(t[2])||0,t[3]=null==t[3]?1:t[3],"rgba("+t.join(",")+")"}function Ei(t){return 4===t||5===t}function Wi(t){return 1===t||2===t}var Xi=[0,0,0,0],ji=function(){function t(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}return t.prototype.isFinished=function(){return this._finished},t.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},t.prototype.needsAnimate=function(){return this.keyframes.length>=1},t.prototype.getAdditiveTrack=function(){return this._additiveTrack},t.prototype.addKeyframe=function(t,e,r){this._needsSort=!0;var i=this.keyframes,n=i.length,o=!1,a=6,s=e;if(R(e)){var h=function(t){return R(t&&t[0])?2:1}(e);a=h,(1===h&&!Z(e[0])||2===h&&!Z(e[0][0]))&&(o=!0)}else if(Z(e)&&!rt(e))a=0;else if(V(e))if(isNaN(+e)){var l=ei(e);l&&(s=l,a=3)}else a=0;else if(J(e)){var u=A({},s);u.colorStops=B(e.colorStops,(function(t){return{offset:t.offset,color:ei(t.color)}})),Si(e)?a=4:Ti(e)&&(a=5),s=u}0===n?this.valType=a:a===this.valType&&6!==a||(o=!0),this.discrete=this.discrete||o;var c={time:t,value:s,rawValue:e,percent:0};return r&&(c.easing=r,c.easingFunc=Y(r)?r:fr[r]||Nr(r)),i.push(c),c},t.prototype.prepare=function(t,e){var r=this.keyframes;this._needsSort&&r.sort((function(t,e){return t.time-e.time}));for(var i=this.valType,n=r.length,o=r[n-1],a=this.discrete,s=Wi(i),h=Ei(i),l=0;l<n;l++){var u=r[l],c=u.value,f=o.value;u.percent=u.time/t,a||(s&&l!==n-1?Bi(c,f,i):h&&Fi(c.colorStops,f.colorStops))}if(!a&&5!==i&&e&&this.needsAnimate()&&e.needsAnimate()&&i===e.valType&&!e._finished){this._additiveTrack=e;var p=r[0].value;for(l=0;l<n;l++)0===i?r[l].additiveValue=r[l].value-p:3===i?r[l].additiveValue=Ii([],r[l].value,p,-1):Wi(i)&&(r[l].additiveValue=1===i?Ii([],r[l].value,p,-1):Ri([],r[l].value,p,-1))}},t.prototype.step=function(t,e){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var r,i,n,o=null!=this._additiveTrack,a=o?"additiveValue":"value",s=this.valType,h=this.keyframes,l=h.length,u=this.propName,c=3===s,f=this._lastFr,p=Math.min;if(1===l)i=n=h[0];else{if(e<0)r=0;else if(e<this._lastFrP){for(r=p(f+1,l-1);r>=0&&!(h[r].percent<=e);r--);r=p(r,l-2)}else{for(r=f;r<l&&!(h[r].percent>e);r++);r=p(r-1,l-2)}n=h[r+1],i=h[r]}if(i&&n){this._lastFr=r,this._lastFrP=e;var d=n.percent-i.percent,v=0===d?1:p((e-i.percent)/d,1);n.easingFunc&&(v=n.easingFunc(v));var y=o?this._additiveValue:c?Xi:t[u];if(!Wi(s)&&!c||y||(y=this._additiveValue=[]),this.discrete)t[u]=v<1?i.rawValue:n.rawValue;else if(Wi(s))1===s?Oi(y,i[a],n[a],v):function(t,e,r,i){for(var n=e.length,o=n&&e[0].length,a=0;a<n;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=zi(e[a][s],r[a][s],i)}}(y,i[a],n[a],v);else if(Ei(s)){var g=i[a],_=n[a],m=4===s;t[u]={type:m?"linear":"radial",x:zi(g.x,_.x,v),y:zi(g.y,_.y,v),colorStops:B(g.colorStops,(function(t,e){var r=_.colorStops[e];return{offset:zi(t.offset,r.offset,v),color:Hi(Oi([],t.color,r.color,v))}})),global:_.global},m?(t[u].x2=zi(g.x2,_.x2,v),t[u].y2=zi(g.y2,_.y2,v)):t[u].r=zi(g.r,_.r,v)}else if(c)Oi(y,i[a],n[a],v),o||(t[u]=Hi(y));else{var x=zi(i[a],n[a],v);o?this._additiveValue=x:t[u]=x}o&&this._addToTarget(t)}}},t.prototype._addToTarget=function(t){var e=this.valType,r=this.propName,i=this._additiveValue;0===e?t[r]=t[r]+i:3===e?(ei(t[r],Xi),Ii(Xi,Xi,i,1),t[r]=Hi(Xi)):1===e?Ii(t[r],t[r],i,1):2===e&&Ri(t[r],t[r],i,1)},t}();const qi=function(){function t(t,e,r,i){this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,this._loop=e,e&&i?T("Can' use additive animation on looped animation."):(this._additiveAnimators=i,this._allowDiscrete=r)}return t.prototype.getMaxTime=function(){return this._maxTime},t.prototype.getDelay=function(){return this._delay},t.prototype.getLoop=function(){return this._loop},t.prototype.getTarget=function(){return this._target},t.prototype.changeTarget=function(t){this._target=t},t.prototype.when=function(t,e,r){return this.whenWithKeys(t,e,W(e),r)},t.prototype.whenWithKeys=function(t,e,r,i){for(var n=this._tracks,o=0;o<r.length;o++){var a=r[o],s=n[a];if(!s){s=n[a]=new ji(a);var h=void 0,l=this._getAdditiveTrack(a);if(l){var u=l.keyframes,c=u[u.length-1];h=c&&c.value,3===l.valType&&h&&(h=Hi(h))}else h=this._target[a];if(null==h)continue;t>0&&s.addKeyframe(0,Ni(h),i),this._trackKeys.push(a)}s.addKeyframe(t,Ni(e[a]),i)}return this._maxTime=Math.max(this._maxTime,t),this},t.prototype.pause=function(){this._clip.pause(),this._paused=!0},t.prototype.resume=function(){this._clip.resume(),this._paused=!1},t.prototype.isPaused=function(){return!!this._paused},t.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},t.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var e=t.length,r=0;r<e;r++)t[r].call(this)},t.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,e=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,e)for(var r=0;r<e.length;r++)e[r].call(this)},t.prototype._setTracksFinished=function(){for(var t=this._tracks,e=this._trackKeys,r=0;r<e.length;r++)t[e[r]].setFinished()},t.prototype._getAdditiveTrack=function(t){var e,r=this._additiveAnimators;if(r)for(var i=0;i<r.length;i++){var n=r[i].getTrack(t);n&&(e=n)}return e},t.prototype.start=function(t){if(!(this._started>0)){this._started=1;for(var e=this,r=[],i=this._maxTime||0,n=0;n<this._trackKeys.length;n++){var o=this._trackKeys[n],a=this._tracks[o],s=this._getAdditiveTrack(o),h=a.keyframes,l=h.length;if(a.prepare(i,s),a.needsAnimate())if(!this._allowDiscrete&&a.discrete){var u=h[l-1];u&&(e._target[a.propName]=u.rawValue),a.setFinished()}else r.push(a)}if(r.length||this._force){var c=new Hr({life:i,loop:this._loop,delay:this._delay||0,onframe:function(t){e._started=2;var i=e._additiveAnimators;if(i){for(var n=!1,o=0;o<i.length;o++)if(i[o]._clip){n=!0;break}n||(e._additiveAnimators=null)}for(o=0;o<r.length;o++)r[o].step(e._target,t);var a=e._onframeCbs;if(a)for(o=0;o<a.length;o++)a[o](e._target,t)},ondestroy:function(){e._doneCallback()}});this._clip=c,this.animation&&this.animation.addClip(c),t&&c.setEasing(t)}else this._doneCallback();return this}},t.prototype.stop=function(t){if(this._clip){var e=this._clip;t&&e.onframe(1),this._abortedCallback()}},t.prototype.delay=function(t){return this._delay=t,this},t.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},t.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},t.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},t.prototype.getClip=function(){return this._clip},t.prototype.getTrack=function(t){return this._tracks[t]},t.prototype.getTracks=function(){var t=this;return B(this._trackKeys,(function(e){return t._tracks[e]}))},t.prototype.stopTracks=function(t,e){if(!t.length||!this._clip)return!0;for(var r=this._tracks,i=this._trackKeys,n=0;n<t.length;n++){var o=r[t[n]];o&&!o.isFinished()&&(e?o.step(this._target,1):1===this._started&&o.step(this._target,0),o.setFinished())}var a=!0;for(n=0;n<i.length;n++)if(!r[i[n]].isFinished()){a=!1;break}return a&&this._abortedCallback(),a},t.prototype.saveTo=function(t,e,r){if(t){e=e||this._trackKeys;for(var i=0;i<e.length;i++){var n=e[i],o=this._tracks[n];if(o&&!o.isFinished()){var a=o.keyframes,s=a[r?0:a.length-1];s&&(t[n]=Ni(s.rawValue))}}}},t.prototype.__changeFinalValue=function(t,e){e=e||W(t);for(var r=0;r<e.length;r++){var i=e[r],n=this._tracks[i];if(n){var o=n.keyframes;if(o.length>1){var a=o.pop();n.addKeyframe(a.time,t[i]),n.prepare(this._maxTime,n.getAdditiveTrack())}}}},t}();function Yi(){return(new Date).getTime()}const Vi=function(e){function r(t){var r=e.call(this)||this;return r._running=!1,r._time=0,r._pausedTime=0,r._pauseStart=0,r._paused=!1,t=t||{},r.stage=t.stage||{},r}return t(r,e),r.prototype.addClip=function(t){t.animation&&this.removeClip(t),this._head?(this._tail.next=t,t.prev=this._tail,t.next=null,this._tail=t):this._head=this._tail=t,t.animation=this},r.prototype.addAnimator=function(t){t.animation=this;var e=t.getClip();e&&this.addClip(e)},r.prototype.removeClip=function(t){if(t.animation){var e=t.prev,r=t.next;e?e.next=r:this._head=r,r?r.prev=e:this._tail=e,t.next=t.prev=t.animation=null}},r.prototype.removeAnimator=function(t){var e=t.getClip();e&&this.removeClip(e),t.animation=null},r.prototype.update=function(t){for(var e=Yi()-this._pausedTime,r=e-this._time,i=this._head;i;){var n=i.next;i.step(e,r)?(i.ondestroy(),this.removeClip(i),i=n):i=n}this._time=e,t||(this.trigger("frame",r),this.stage.update&&this.stage.update())},r.prototype._startLoop=function(){var t=this;this._running=!0,ur((function e(){t._running&&(ur(e),!t._paused&&t.update())}))},r.prototype.start=function(){this._running||(this._time=Yi(),this._pausedTime=0,this._startLoop())},r.prototype.stop=function(){this._running=!1},r.prototype.pause=function(){this._paused||(this._pauseStart=Yi(),this._paused=!0)},r.prototype.resume=function(){this._paused&&(this._pausedTime+=Yi()-this._pauseStart,this._paused=!1)},r.prototype.clear=function(){for(var t=this._head;t;){var e=t.next;t.prev=t.next=t.animation=null,t=e}this._head=this._tail=null},r.prototype.isFinished=function(){return null==this._head},r.prototype.animate=function(t,e){e=e||{},this.start();var r=new qi(t,e.loop);return this.addAnimator(r),r},r}(Zt);var Ui,Zi,Gi=i.domSupported,Ki=(Zi={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},{mouse:Ui=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],touch:["touchstart","touchend","touchmove"],pointer:B(Ui,(function(t){var e=t.replace("mouse","pointer");return Zi.hasOwnProperty(e)?e:t}))}),Qi=["mousemove","mouseup"],$i=["pointermove","pointerup"],Ji=!1;function tn(t){var e=t.pointerType;return"pen"===e||"touch"===e}function en(t){t&&(t.zrByTouch=!0)}function rn(t,e){for(var r=e,i=!1;r&&9!==r.nodeType&&!(i=r.domBelongToZr||r!==e&&r===t.painterRoot);)r=r.parentNode;return i}var nn=function(t,e){this.stopPropagation=wt,this.stopImmediatePropagation=wt,this.preventDefault=wt,this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY},on={mousedown:function(t){t=fe(this.dom,t),this.__mayPointerCapture=[t.zrX,t.zrY],this.trigger("mousedown",t)},mousemove:function(t){t=fe(this.dom,t);var e=this.__mayPointerCapture;!e||t.zrX===e[0]&&t.zrY===e[1]||this.__togglePointerCapture(!0),this.trigger("mousemove",t)},mouseup:function(t){t=fe(this.dom,t),this.__togglePointerCapture(!1),this.trigger("mouseup",t)},mouseout:function(t){rn(this,(t=fe(this.dom,t)).toElement||t.relatedTarget)||(this.__pointerCapturing&&(t.zrEventControl="no_globalout"),this.trigger("mouseout",t))},wheel:function(t){Ji=!0,t=fe(this.dom,t),this.trigger("mousewheel",t)},mousewheel:function(t){Ji||(t=fe(this.dom,t),this.trigger("mousewheel",t))},touchstart:function(t){en(t=fe(this.dom,t)),this.__lastTouchMoment=new Date,this.handler.processGesture(t,"start"),on.mousemove.call(this,t),on.mousedown.call(this,t)},touchmove:function(t){en(t=fe(this.dom,t)),this.handler.processGesture(t,"change"),on.mousemove.call(this,t)},touchend:function(t){en(t=fe(this.dom,t)),this.handler.processGesture(t,"end"),on.mouseup.call(this,t),+new Date-+this.__lastTouchMoment<300&&on.click.call(this,t)},pointerdown:function(t){on.mousedown.call(this,t)},pointermove:function(t){tn(t)||on.mousemove.call(this,t)},pointerup:function(t){on.mouseup.call(this,t)},pointerout:function(t){tn(t)||on.mouseout.call(this,t)}};F(["click","dblclick","contextmenu"],(function(t){on[t]=function(e){e=fe(this.dom,e),this.trigger(t,e)}}));var an={pointermove:function(t){tn(t)||an.mousemove.call(this,t)},pointerup:function(t){an.mouseup.call(this,t)},mousemove:function(t){this.trigger("mousemove",t)},mouseup:function(t){var e=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",t),e&&(t.zrEventControl="only_globalout",this.trigger("mouseout",t))}};function sn(t,e){var r=e.domHandlers;i.pointerEventsSupported?F(Ki.pointer,(function(i){ln(e,i,(function(e){r[i].call(t,e)}))})):(i.touchEventsSupported&&F(Ki.touch,(function(i){ln(e,i,(function(n){r[i].call(t,n),function(t){t.touching=!0,null!=t.touchTimer&&(clearTimeout(t.touchTimer),t.touchTimer=null),t.touchTimer=setTimeout((function(){t.touching=!1,t.touchTimer=null}),700)}(e)}))})),F(Ki.mouse,(function(i){ln(e,i,(function(n){n=ce(n),e.touching||r[i].call(t,n)}))})))}function hn(t,e){function r(r){ln(e,r,(function(i){i=ce(i),rn(t,i.target)||(i=function(t,e){return fe(t.dom,new nn(t,e),!0)}(t,i),e.domHandlers[r].call(t,i))}),{capture:!0})}i.pointerEventsSupported?F($i,r):i.touchEventsSupported||F(Qi,r)}function ln(t,e,r,i){t.mounted[e]=r,t.listenerOpts[e]=i,pe(t.domTarget,e,r,i)}function un(t){var e,r,i,n,o=t.mounted;for(var a in o)o.hasOwnProperty(a)&&(e=t.domTarget,r=a,i=o[a],n=t.listenerOpts[a],e.removeEventListener(r,i,n));t.mounted={}}var cn=function(t,e){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=e};const fn=function(e){function r(t,r){var i=e.call(this)||this;return i.__pointerCapturing=!1,i.dom=t,i.painterRoot=r,i._localHandlerScope=new cn(t,on),Gi&&(i._globalHandlerScope=new cn(document,an)),sn(i,i._localHandlerScope),i}return t(r,e),r.prototype.dispose=function(){un(this._localHandlerScope),Gi&&un(this._globalHandlerScope)},r.prototype.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},r.prototype.__togglePointerCapture=function(t){if(this.__mayPointerCapture=null,Gi&&+this.__pointerCapturing^+t){this.__pointerCapturing=t;var e=this._globalHandlerScope;t?hn(this,e):un(e)}},r}(Zt);var pn=1;i.hasGlobalWindow&&(pn=Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1));var dn=pn,vn="#333",yn="#ccc",gn=xe,_n=5e-5;function mn(t){return t>_n||t<-5e-5}var xn=[],wn=[],bn=[1,0,0,1,0,0],kn=Math.abs,Sn=function(){function t(){}var e;return t.prototype.getLocalTransform=function(e){return t.getLocalTransform(this,e)},t.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},t.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},t.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},t.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},t.prototype.needLocalTransform=function(){return mn(this.rotation)||mn(this.x)||mn(this.y)||mn(this.scaleX-1)||mn(this.scaleY-1)||mn(this.skewX)||mn(this.skewY)},t.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,e=this.needLocalTransform(),r=this.transform;e||t?(r=r||[1,0,0,1,0,0],e?this.getLocalTransform(r):gn(r),t&&(e?be(r,t,r):we(r,t)),this.transform=r,this._resolveGlobalScaleRatio(r)):r&&(gn(r),this.invTransform=null)},t.prototype._resolveGlobalScaleRatio=function(t){var e=this.globalScaleRatio;if(null!=e&&1!==e){this.getGlobalScale(xn);var r=xn[0]<0?-1:1,i=xn[1]<0?-1:1,n=((xn[0]-r)*e+r)/xn[0]||0,o=((xn[1]-i)*e+i)/xn[1]||0;t[0]*=n,t[1]*=n,t[2]*=o,t[3]*=o}this.invTransform=this.invTransform||[1,0,0,1,0,0],Ce(this.invTransform,t)},t.prototype.getComputedTransform=function(){for(var t=this,e=[];t;)e.push(t),t=t.parent;for(;t=e.pop();)t.updateTransform();return this.transform},t.prototype.setLocalTransform=function(t){if(t){var e=t[0]*t[0]+t[1]*t[1],r=t[2]*t[2]+t[3]*t[3],i=Math.atan2(t[1],t[0]),n=Math.PI/2+i-Math.atan2(t[3],t[2]);r=Math.sqrt(r)*Math.cos(n),e=Math.sqrt(e),this.skewX=n,this.skewY=0,this.rotation=-i,this.x=+t[4],this.y=+t[5],this.scaleX=e,this.scaleY=r,this.originX=0,this.originY=0}},t.prototype.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(t.invTransform=t.invTransform||[1,0,0,1,0,0],be(wn,t.invTransform,e),e=wn);var r=this.originX,i=this.originY;(r||i)&&(bn[4]=r,bn[5]=i,be(wn,e,bn),wn[4]-=r,wn[5]-=i,e=wn),this.setLocalTransform(e)}},t.prototype.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1]),t):(t[0]=1,t[1]=1,t)},t.prototype.transformCoordToLocal=function(t,e){var r=[t,e],i=this.invTransform;return i&&Xt(r,r,i),r},t.prototype.transformCoordToGlobal=function(t,e){var r=[t,e],i=this.transform;return i&&Xt(r,r,i),r},t.prototype.getLineScale=function(){var t=this.transform;return t&&kn(t[0]-1)>1e-10&&kn(t[3]-1)>1e-10?Math.sqrt(kn(t[0]*t[3]-t[2]*t[1])):1},t.prototype.copyTransform=function(t){Cn(this,t)},t.getLocalTransform=function(t,e){e=e||[];var r=t.originX||0,i=t.originY||0,n=t.scaleX,o=t.scaleY,a=t.anchorX,s=t.anchorY,h=t.rotation||0,l=t.x,u=t.y,c=t.skewX?Math.tan(t.skewX):0,f=t.skewY?Math.tan(-t.skewY):0;if(r||i||a||s){var p=r+a,d=i+s;e[4]=-p*n-c*d*o,e[5]=-d*o-f*p*n}else e[4]=e[5]=0;return e[0]=n,e[3]=o,e[1]=f*n,e[2]=c*o,h&&Se(e,e,h),e[4]+=r+l,e[5]+=i+u,e},t.initDefaultProps=((e=t.prototype).scaleX=e.scaleY=e.globalScaleRatio=1,void(e.x=e.y=e.originX=e.originY=e.skewX=e.skewY=e.rotation=e.anchorX=e.anchorY=0)),t}(),Tn=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];function Cn(t,e){for(var r=0;r<Tn.length;r++){var i=Tn[r];t[i]=e[i]}}const Pn=Sn;var Mn={};function An(t,e){var r=Mn[e=e||a];r||(r=Mn[e]=new Xr(500));var i=r.get(t);return null==i&&(i=u.measureText(t,e).width,r.put(t,i)),i}function Ln(t,e,r,i){var n=An(t,e),o=In(e),a=zn(0,n,r),s=On(0,o,i);return new Ne(a,s,n,o)}function Dn(t,e,r,i){var n=((t||"")+"").split("\n");if(1===n.length)return Ln(n[0],e,r,i);for(var o=new Ne(0,0,0,0),a=0;a<n.length;a++){var s=Ln(n[a],e,r,i);0===a?o.copy(s):o.union(s)}return o}function zn(t,e,r){return"right"===r?t-=e:"center"===r&&(t-=e/2),t}function On(t,e,r){return"middle"===r?t-=e/2:"bottom"===r&&(t-=e),t}function In(t){return An("国",t)}function Rn(t,e){return"string"==typeof t?t.lastIndexOf("%")>=0?parseFloat(t)/100*e:parseFloat(t):t}function Fn(t,e,r){var i=e.position||"inside",n=null!=e.distance?e.distance:5,o=r.height,a=r.width,s=o/2,h=r.x,l=r.y,u="left",c="top";if(i instanceof Array)h+=Rn(i[0],r.width),l+=Rn(i[1],r.height),u=null,c=null;else switch(i){case"left":h-=n,l+=s,u="right",c="middle";break;case"right":h+=n+a,l+=s,c="middle";break;case"top":h+=a/2,l-=n,u="center",c="bottom";break;case"bottom":h+=a/2,l+=o+n,u="center";break;case"inside":h+=a/2,l+=s,u="center",c="middle";break;case"insideLeft":h+=n,l+=s,c="middle";break;case"insideRight":h+=a-n,l+=s,u="right",c="middle";break;case"insideTop":h+=a/2,l+=n,u="center";break;case"insideBottom":h+=a/2,l+=o-n,u="center",c="bottom";break;case"insideTopLeft":h+=n,l+=n;break;case"insideTopRight":h+=a-n,l+=n,u="right";break;case"insideBottomLeft":h+=n,l+=o-n,c="bottom";break;case"insideBottomRight":h+=a-n,l+=o-n,u="right",c="bottom"}return(t=t||{}).x=h,t.y=l,t.align=u,t.verticalAlign=c,t}var Bn="__zr_normal__",Nn=Tn.concat(["ignore"]),Hn=N(Tn,(function(t,e){return t[e]=!0,t}),{ignore:!1}),En={},Wn=new Ne(0,0,0,0),Xn=function(){function t(t){this.id=S(),this.animators=[],this.currentStates=[],this.states={},this._init(t)}return t.prototype._init=function(t){this.attr(t)},t.prototype.drift=function(t,e,r){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var i=this.transform;i||(i=this.transform=[1,0,0,1,0,0]),i[4]+=t,i[5]+=e,this.decomposeTransform(),this.markRedraw()},t.prototype.beforeUpdate=function(){},t.prototype.afterUpdate=function(){},t.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},t.prototype.updateInnerText=function(t){var e=this._textContent;if(e&&(!e.ignore||t)){this.textConfig||(this.textConfig={});var r=this.textConfig,i=r.local,n=e.innerTransformable,o=void 0,a=void 0,s=!1;n.parent=i?this:null;var h=!1;if(n.copyTransform(e),null!=r.position){var l=Wn;r.layoutRect?l.copy(r.layoutRect):l.copy(this.getBoundingRect()),i||l.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(En,r,l):Fn(En,r,l),n.x=En.x,n.y=En.y,o=En.align,a=En.verticalAlign;var u=r.origin;if(u&&null!=r.rotation){var c=void 0,f=void 0;"center"===u?(c=.5*l.width,f=.5*l.height):(c=Rn(u[0],l.width),f=Rn(u[1],l.height)),h=!0,n.originX=-n.x+c+(i?0:l.x),n.originY=-n.y+f+(i?0:l.y)}}null!=r.rotation&&(n.rotation=r.rotation);var p=r.offset;p&&(n.x+=p[0],n.y+=p[1],h||(n.originX=-p[0],n.originY=-p[1]));var d=null==r.inside?"string"==typeof r.position&&r.position.indexOf("inside")>=0:r.inside,v=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),y=void 0,g=void 0,_=void 0;d&&this.canBeInsideText()?(y=r.insideFill,g=r.insideStroke,null!=y&&"auto"!==y||(y=this.getInsideTextFill()),null!=g&&"auto"!==g||(g=this.getInsideTextStroke(y),_=!0)):(y=r.outsideFill,g=r.outsideStroke,null!=y&&"auto"!==y||(y=this.getOutsideFill()),null!=g&&"auto"!==g||(g=this.getOutsideStroke(y),_=!0)),(y=y||"#000")===v.fill&&g===v.stroke&&_===v.autoStroke&&o===v.align&&a===v.verticalAlign||(s=!0,v.fill=y,v.stroke=g,v.autoStroke=_,v.align=o,v.verticalAlign=a,e.setDefaultTextStyle(v)),e.__dirty|=nr,s&&e.dirtyStyle(!0)}},t.prototype.canBeInsideText=function(){return!0},t.prototype.getInsideTextFill=function(){return"#fff"},t.prototype.getInsideTextStroke=function(t){return"#000"},t.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?yn:vn},t.prototype.getOutsideStroke=function(t){var e=this.__zr&&this.__zr.getBackgroundColor(),r="string"==typeof e&&ei(e);r||(r=[255,255,255,1]);for(var i=r[3],n=this.__zr.isDarkMode(),o=0;o<3;o++)r[o]=r[o]*i+(n?0:255)*(1-i);return r[3]=1,ui(r,"rgba")},t.prototype.traverse=function(t,e){},t.prototype.attrKV=function(t,e){"textConfig"===t?this.setTextConfig(e):"textContent"===t?this.setTextContent(e):"clipPath"===t?this.setClipPath(e):"extra"===t?(this.extra=this.extra||{},A(this.extra,e)):this[t]=e},t.prototype.hide=function(){this.ignore=!0,this.markRedraw()},t.prototype.show=function(){this.ignore=!1,this.markRedraw()},t.prototype.attr=function(t,e){if("string"==typeof t)this.attrKV(t,e);else if(G(t))for(var r=W(t),i=0;i<r.length;i++){var n=r[i];this.attrKV(n,t[n])}return this.markRedraw(),this},t.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var e=this._normalState,r=0;r<this.animators.length;r++){var i=this.animators[r],n=i.__fromStateTransition;if(!(i.getLoop()||n&&n!==Bn)){var o=i.targetName,a=o?e[o]:e;i.saveTo(a)}}},t.prototype._innerSaveToNormal=function(t){var e=this._normalState;e||(e=this._normalState={}),t.textConfig&&!e.textConfig&&(e.textConfig=this.textConfig),this._savePrimaryToNormal(t,e,Nn)},t.prototype._savePrimaryToNormal=function(t,e,r){for(var i=0;i<r.length;i++){var n=r[i];null==t[n]||n in e||(e[n]=this[n])}},t.prototype.hasState=function(){return this.currentStates.length>0},t.prototype.getState=function(t){return this.states[t]},t.prototype.ensureState=function(t){var e=this.states;return e[t]||(e[t]={}),e[t]},t.prototype.clearStates=function(t){this.useState(Bn,!1,t)},t.prototype.useState=function(t,e,r,i){var n=t===Bn;if(this.hasState()||!n){var o=this.currentStates,a=this.stateTransition;if(!(z(o,t)>=0)||!e&&1!==o.length){var s;if(this.stateProxy&&!n&&(s=this.stateProxy(t)),s||(s=this.states&&this.states[t]),s||n){n||this.saveCurrentToNormalState(s);var h=!!(s&&s.hoverLayer||i);h&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,s,this._normalState,e,!r&&!this.__inHover&&a&&a.duration>0,a);var l=this._textContent,u=this._textGuide;return l&&l.useState(t,e,r,h),u&&u.useState(t,e,r,h),n?(this.currentStates=[],this._normalState={}):e?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!h&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~nr),s}T("State "+t+" not exists.")}}},t.prototype.useStates=function(t,e,r){if(t.length){var i=[],n=this.currentStates,o=t.length,a=o===n.length;if(a)for(var s=0;s<o;s++)if(t[s]!==n[s]){a=!1;break}if(a)return;for(s=0;s<o;s++){var h=t[s],l=void 0;this.stateProxy&&(l=this.stateProxy(h,t)),l||(l=this.states[h]),l&&i.push(l)}var u=i[o-1],c=!!(u&&u.hoverLayer||r);c&&this._toggleHoverLayerFlag(!0);var f=this._mergeStates(i),p=this.stateTransition;this.saveCurrentToNormalState(f),this._applyStateObj(t.join(","),f,this._normalState,!1,!e&&!this.__inHover&&p&&p.duration>0,p);var d=this._textContent,v=this._textGuide;d&&d.useStates(t,e,c),v&&v.useStates(t,e,c),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!c&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~nr)}else this.clearStates()},t.prototype.isSilent=function(){for(var t=this.silent,e=this.parent;!t&&e;){if(e.silent){t=!0;break}e=e.parent}return t},t.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var e=this.animators[t];e.targetName&&e.changeTarget(this[e.targetName])}},t.prototype.removeState=function(t){var e=z(this.currentStates,t);if(e>=0){var r=this.currentStates.slice();r.splice(e,1),this.useStates(r)}},t.prototype.replaceState=function(t,e,r){var i=this.currentStates.slice(),n=z(i,t),o=z(i,e)>=0;n>=0?o?i.splice(n,1):i[n]=e:r&&!o&&i.push(e),this.useStates(i)},t.prototype.toggleState=function(t,e){e?this.useState(t,!0):this.removeState(t)},t.prototype._mergeStates=function(t){for(var e,r={},i=0;i<t.length;i++){var n=t[i];A(r,n),n.textConfig&&A(e=e||{},n.textConfig)}return e&&(r.textConfig=e),r},t.prototype._applyStateObj=function(t,e,r,i,n,o){var a=!(e&&i);e&&e.textConfig?(this.textConfig=A({},i?this.textConfig:r.textConfig),A(this.textConfig,e.textConfig)):a&&r.textConfig&&(this.textConfig=r.textConfig);for(var s={},h=!1,l=0;l<Nn.length;l++){var u=Nn[l],c=n&&Hn[u];e&&null!=e[u]?c?(h=!0,s[u]=e[u]):this[u]=e[u]:a&&null!=r[u]&&(c?(h=!0,s[u]=r[u]):this[u]=r[u])}if(!n)for(l=0;l<this.animators.length;l++){var f=this.animators[l],p=f.targetName;f.getLoop()||f.__changeFinalValue(p?(e||r)[p]:e||r)}h&&this._transitionState(t,s,o)},t.prototype._attachComponent=function(t){if((!t.__zr||t.__hostTarget)&&t!==this){var e=this.__zr;e&&t.addSelfToZr(e),t.__zr=e,t.__hostTarget=this}},t.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},t.prototype.getClipPath=function(){return this._clipPath},t.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},t.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},t.prototype.getTextContent=function(){return this._textContent},t.prototype.setTextContent=function(t){var e=this._textContent;e!==t&&(e&&e!==t&&this.removeTextContent(),t.innerTransformable=new Pn,this._attachComponent(t),this._textContent=t,this.markRedraw())},t.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),A(this.textConfig,t),this.markRedraw()},t.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},t.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},t.prototype.getTextGuideLine=function(){return this._textGuide},t.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},t.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},t.prototype.markRedraw=function(){this.__dirty|=nr;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},t.prototype.dirty=function(){this.markRedraw()},t.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var e=this._textContent,r=this._textGuide;e&&(e.__inHover=t),r&&(r.__inHover=t)},t.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var e=this.animators;if(e)for(var r=0;r<e.length;r++)t.animation.addAnimator(e[r]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},t.prototype.removeSelfFromZr=function(t){if(this.__zr){this.__zr=null;var e=this.animators;if(e)for(var r=0;r<e.length;r++)t.animation.removeAnimator(e[r]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},t.prototype.animate=function(t,e,r){var i=t?this[t]:this,n=new qi(i,e,r);return t&&(n.targetName=t),this.addAnimator(n,t),n},t.prototype.addAnimator=function(t,e){var r=this.__zr,i=this;t.during((function(){i.updateDuringAnimation(e)})).done((function(){var e=i.animators,r=z(e,t);r>=0&&e.splice(r,1)})),this.animators.push(t),r&&r.animation.addAnimator(t),r&&r.wakeUp()},t.prototype.updateDuringAnimation=function(t){this.markRedraw()},t.prototype.stopAnimation=function(t,e){for(var r=this.animators,i=r.length,n=[],o=0;o<i;o++){var a=r[o];t&&t!==a.scope?n.push(a):a.stop(e)}return this.animators=n,this},t.prototype.animateTo=function(t,e,r){jn(this,t,e,r)},t.prototype.animateFrom=function(t,e,r){jn(this,t,e,r,!0)},t.prototype._transitionState=function(t,e,r,i){for(var n=jn(this,e,r,i),o=0;o<n.length;o++)n[o].__fromStateTransition=t},t.prototype.getBoundingRect=function(){return null},t.prototype.getPaintRect=function(){return null},t.initDefaultProps=function(){var e=t.prototype;function r(t,r,i,n){function o(t,e){Object.defineProperty(e,0,{get:function(){return t[i]},set:function(e){t[i]=e}}),Object.defineProperty(e,1,{get:function(){return t[n]},set:function(e){t[n]=e}})}Object.defineProperty(e,t,{get:function(){this[r]||o(this,this[r]=[]);return this[r]},set:function(t){this[i]=t[0],this[n]=t[1],this[r]=t,o(this,t)}})}e.type="element",e.name="",e.ignore=e.silent=e.isGroup=e.draggable=e.dragging=e.ignoreClip=e.__inHover=!1,e.__dirty=nr,Object.defineProperty&&(r("position","_legacyPos","x","y"),r("scale","_legacyScale","scaleX","scaleY"),r("origin","_legacyOrigin","originX","originY"))}(),t}();function jn(t,e,r,i,n){var o=[];Vn(t,"",t,e,r=r||{},i,o,n);var a=o.length,s=!1,h=r.done,l=r.aborted,u=function(){s=!0,--a<=0&&(s?h&&h():l&&l())},c=function(){--a<=0&&(s?h&&h():l&&l())};a||h&&h(),o.length>0&&r.during&&o[0].during((function(t,e){r.during(e)}));for(var f=0;f<o.length;f++){var p=o[f];u&&p.done(u),c&&p.aborted(c),r.force&&p.duration(r.duration),p.start(r.easing)}return o}function qn(t,e,r){for(var i=0;i<r;i++)t[i]=e[i]}function Yn(t,e,r){if(R(e[r]))if(R(t[r])||(t[r]=[]),Q(e[r])){var i=e[r].length;t[r].length!==i&&(t[r]=new e[r].constructor(i),qn(t[r],e[r],i))}else{var n=e[r],o=t[r],a=n.length;if(R(n[0]))for(var s=n[0].length,h=0;h<a;h++)o[h]?qn(o[h],n[h],s):o[h]=Array.prototype.slice.call(n[h]);else qn(o,n,a);o.length=n.length}else t[r]=e[r]}function Vn(t,e,r,i,n,o,a,s){for(var h=W(i),l=n.duration,u=n.delay,c=n.additive,f=n.setToFinal,p=!G(o),d=t.animators,v=[],y=0;y<h.length;y++){var g=h[y],_=i[g];if(null!=_&&null!=r[g]&&(p||o[g]))if(!G(_)||R(_)||J(_))v.push(g);else{if(e){s||(r[g]=_,t.updateDuringAnimation(e));continue}Vn(t,g,r[g],_,n,o&&o[g],a,s)}else s||(r[g]=_,t.updateDuringAnimation(e),v.push(g))}var m=v.length;if(!c&&m)for(var x=0;x<d.length;x++){if((b=d[x]).targetName===e)if(b.stopTracks(v)){var w=z(d,b);d.splice(w,1)}}if(n.force||(v=H(v,(function(t){return e=i[t],n=r[t],!(e===n||R(e)&&R(n)&&function(t,e){var r=t.length;if(r!==e.length)return!1;for(var i=0;i<r;i++)if(t[i]!==e[i])return!1;return!0}(e,n));var e,n})),m=v.length),m>0||n.force&&!a.length){var b,k=void 0,S=void 0,T=void 0;if(s){S={},f&&(k={});for(x=0;x<m;x++){S[g=v[x]]=r[g],f?k[g]=i[g]:r[g]=i[g]}}else if(f){T={};for(x=0;x<m;x++){T[g=v[x]]=Ni(r[g]),Yn(r,i,g)}}(b=new qi(r,!1,!1,c?H(d,(function(t){return t.targetName===e})):null)).targetName=e,n.scope&&(b.scope=n.scope),f&&k&&b.whenWithKeys(0,k,v),T&&b.whenWithKeys(0,T,v),b.whenWithKeys(null==l?500:l,s?S:i,v).delay(u||0),t.addAnimator(b,e),a.push(b)}}I(Xn,Zt),I(Xn,Pn);const Un=Xn;var Zn=function(e){function r(t){var r=e.call(this)||this;return r.isGroup=!0,r._children=[],r.attr(t),r}return t(r,e),r.prototype.childrenRef=function(){return this._children},r.prototype.children=function(){return this._children.slice()},r.prototype.childAt=function(t){return this._children[t]},r.prototype.childOfName=function(t){for(var e=this._children,r=0;r<e.length;r++)if(e[r].name===t)return e[r]},r.prototype.childCount=function(){return this._children.length},r.prototype.add=function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},r.prototype.addBefore=function(t,e){if(t&&t!==this&&t.parent!==this&&e&&e.parent===this){var r=this._children,i=r.indexOf(e);i>=0&&(r.splice(i,0,t),this._doAdd(t))}return this},r.prototype.replace=function(t,e){var r=z(this._children,t);return r>=0&&this.replaceAt(e,r),this},r.prototype.replaceAt=function(t,e){var r=this._children,i=r[e];if(t&&t!==this&&t.parent!==this&&t!==i){r[e]=t,i.parent=null;var n=this.__zr;n&&i.removeSelfFromZr(n),this._doAdd(t)}return this},r.prototype._doAdd=function(t){t.parent&&t.parent.remove(t),t.parent=this;var e=this.__zr;e&&e!==t.__zr&&t.addSelfToZr(e),e&&e.refresh()},r.prototype.remove=function(t){var e=this.__zr,r=this._children,i=z(r,t);return i<0||(r.splice(i,1),t.parent=null,e&&t.removeSelfFromZr(e),e&&e.refresh()),this},r.prototype.removeAll=function(){for(var t=this._children,e=this.__zr,r=0;r<t.length;r++){var i=t[r];e&&i.removeSelfFromZr(e),i.parent=null}return t.length=0,this},r.prototype.eachChild=function(t,e){for(var r=this._children,i=0;i<r.length;i++){var n=r[i];t.call(e,n,i)}return this},r.prototype.traverse=function(t,e){for(var r=0;r<this._children.length;r++){var i=this._children[r],n=t.call(e,i);i.isGroup&&!n&&i.traverse(t,e)}return this},r.prototype.addSelfToZr=function(t){e.prototype.addSelfToZr.call(this,t);for(var r=0;r<this._children.length;r++){this._children[r].addSelfToZr(t)}},r.prototype.removeSelfFromZr=function(t){e.prototype.removeSelfFromZr.call(this,t);for(var r=0;r<this._children.length;r++){this._children[r].removeSelfFromZr(t)}},r.prototype.getBoundingRect=function(t){for(var e=new Ne(0,0,0,0),r=t||this._children,i=[],n=null,o=0;o<r.length;o++){var a=r[o];if(!a.ignore&&!a.invisible){var s=a.getBoundingRect(),h=a.getLocalTransform(i);h?(Ne.applyTransform(e,s,h),(n=n||e.clone()).union(e)):(n=n||s.clone()).union(s)}}return n||e},r}(Un);Zn.prototype.type="group";const Gn=Zn;
/*!
* ZRender, a high performance 2d drawing library.
*
* Copyright (c) 2013, Baidu Inc.
* All rights reserved.
*
* LICENSE
* https://github.com/ecomfe/zrender/blob/master/LICENSE.txt
*/var Kn={},Qn={};var $n,Jn=function(){function t(t,e,r){var n=this;this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,r=r||{},this.dom=e,this.id=t;var o=new lr,a=r.renderer||"canvas";Kn[a]||(a=W(Kn)[0]),r.useDirtyRect=null!=r.useDirtyRect&&r.useDirtyRect;var s=new Kn[a](e,o,r,t),h=r.ssr||s.ssrOnly;this.storage=o,this.painter=s;var l,u=i.node||i.worker||h?null:new fn(s.getViewportRoot(),s.root),c=r.useCoarsePointer;(null==c||"auto"===c?i.touchEventsSupported:!!c)&&(l=nt(r.pointerSize,44)),this.handler=new Ge(o,s,u,s.root,l),this.animation=new Vi({stage:{update:h?null:function(){return n._flush(!0)}}}),h||this.animation.start()}return t.prototype.add=function(t){!this._disposed&&t&&(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},t.prototype.remove=function(t){!this._disposed&&t&&(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},t.prototype.configLayer=function(t,e){this._disposed||(this.painter.configLayer&&this.painter.configLayer(t,e),this.refresh())},t.prototype.setBackgroundColor=function(t){this._disposed||(this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=function(t){if(!t)return!1;if("string"==typeof t)return ci(t,1)<.4;if(t.colorStops){for(var e=t.colorStops,r=0,i=e.length,n=0;n<i;n++)r+=ci(e[n].color,1);return(r/=i)<.4}return!1}(t))},t.prototype.getBackgroundColor=function(){return this._backgroundColor},t.prototype.setDarkMode=function(t){this._darkMode=t},t.prototype.isDarkMode=function(){return this._darkMode},t.prototype.refreshImmediately=function(t){this._disposed||(t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1)},t.prototype.refresh=function(){this._disposed||(this._needsRefresh=!0,this.animation.start())},t.prototype.flush=function(){this._disposed||this._flush(!1)},t.prototype._flush=function(t){var e,r=Yi();this._needsRefresh&&(e=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(e=!0,this.refreshHoverImmediately());var i=Yi();e?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:i-r})):this._sleepAfterStill>0&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill&&this.animation.stop())},t.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},t.prototype.wakeUp=function(){this._disposed||(this.animation.start(),this._stillFrameAccum=0)},t.prototype.refreshHover=function(){this._needsRefreshHover=!0},t.prototype.refreshHoverImmediately=function(){this._disposed||(this._needsRefreshHover=!1,this.painter.refreshHover&&"canvas"===this.painter.getType()&&this.painter.refreshHover())},t.prototype.resize=function(t){this._disposed||(t=t||{},this.painter.resize(t.width,t.height),this.handler.resize())},t.prototype.clearAnimation=function(){this._disposed||this.animation.clear()},t.prototype.getWidth=function(){if(!this._disposed)return this.painter.getWidth()},t.prototype.getHeight=function(){if(!this._disposed)return this.painter.getHeight()},t.prototype.setCursorStyle=function(t){this._disposed||this.handler.setCursorStyle(t)},t.prototype.findHover=function(t,e){if(!this._disposed)return this.handler.findHover(t,e)},t.prototype.on=function(t,e,r){return this._disposed||this.handler.on(t,e,r),this},t.prototype.off=function(t,e){this._disposed||this.handler.off(t,e)},t.prototype.trigger=function(t,e){this._disposed||this.handler.trigger(t,e)},t.prototype.clear=function(){if(!this._disposed){for(var t=this.storage.getRoots(),e=0;e<t.length;e++)t[e]instanceof Gn&&t[e].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()}},t.prototype.dispose=function(){var t;this._disposed||(this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,this._disposed=!0,t=this.id,delete Qn[t])},t}();function to(t,e){var r=new Jn(S(),t,e);return Qn[r.id]=r,r}function eo(t,e){Kn[t]=e}function ro(t){if("function"==typeof $n)return $n(t)}function io(t){$n=t}const no=Object.freeze(Object.defineProperty({__proto__:null,dispose:function(t){t.dispose()},disposeAll:function(){for(var t in Qn)Qn.hasOwnProperty(t)&&Qn[t].dispose();Qn={}},getElementSSRData:ro,getInstance:function(t){return Qn[t]},init:to,registerPainter:eo,registerSSRDataGetter:io,version:"5.5.0"},Symbol.toStringTag,{value:"Module"}));var oo=new Xr(50);function ao(t){if("string"==typeof t){var e=oo.get(t);return e&&e.image}return t}function so(t,e,r,i,n){if(t){if("string"==typeof t){if(e&&e.__zrImageSrc===t||!r)return e;var o=oo.get(t),a={hostEl:r,cb:i,cbPayload:n};return o?!lo(e=o.image)&&o.pending.push(a):((e=u.loadImage(t,ho,ho)).__zrImageSrc=t,oo.put(t,e.__cachedImgObj={image:e,pending:[a]})),e}return t}return e}function ho(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var r=t.pending[e],i=r.cb;i&&i(this,r.cbPayload),r.hostEl.dirty()}t.pending.length=0}function lo(t){return t&&t.width&&t.height}var uo=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function co(t,e,r,i,n){if(!e)return"";var o=(t+"").split("\n");n=fo(e,r,i,n);for(var a=0,s=o.length;a<s;a++)o[a]=po(o[a],n);return o.join("\n")}function fo(t,e,r,i){var n=A({},i=i||{});n.font=e,r=nt(r,"..."),n.maxIterations=nt(i.maxIterations,2);var o=n.minChar=nt(i.minChar,0);n.cnCharWidth=An("国",e);var a=n.ascCharWidth=An("a",e);n.placeholder=nt(i.placeholder,"");for(var s=t=Math.max(0,t-1),h=0;h<o&&s>=a;h++)s-=a;var l=An(r,e);return l>s&&(r="",l=0),s=t-l,n.ellipsis=r,n.ellipsisWidth=l,n.contentWidth=s,n.containerWidth=t,n}function po(t,e){var r=e.containerWidth,i=e.font,n=e.contentWidth;if(!r)return"";var o=An(t,i);if(o<=r)return t;for(var a=0;;a++){if(o<=n||a>=e.maxIterations){t+=e.ellipsis;break}var s=0===a?vo(t,n,e.ascCharWidth,e.cnCharWidth):o>0?Math.floor(t.length*n/o):0;o=An(t=t.substr(0,s),i)}return""===t&&(t=e.placeholder),t}function vo(t,e,r,i){for(var n=0,o=0,a=t.length;o<a&&n<e;o++){var s=t.charCodeAt(o);n+=0<=s&&s<=127?r:i}return o}var yo=function(){},go=function(t){this.tokens=[],t&&(this.tokens=t)},_o=function(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[]};function mo(t,e,r,i,n){var o,a,s=""===e,h=n&&r.rich[n]||{},l=t.lines,u=h.font||r.font,c=!1;if(i){var f=h.padding,p=f?f[1]+f[3]:0;if(null!=h.width&&"auto"!==h.width){var d=Rn(h.width,i.width)+p;l.length>0&&d+i.accumWidth>i.width&&(o=e.split("\n"),c=!0),i.accumWidth=d}else{var v=bo(e,u,i.width,i.breakAll,i.accumWidth);i.accumWidth=v.accumWidth+p,a=v.linesWidths,o=v.lines}}else o=e.split("\n");for(var y=0;y<o.length;y++){var g=o[y],_=new yo;if(_.styleName=n,_.text=g,_.isLineHolder=!g&&!s,"number"==typeof h.width?_.width=h.width:_.width=a?a[y]:An(g,u),y||c)l.push(new go([_]));else{var m=(l[l.length-1]||(l[0]=new go)).tokens,x=m.length;1===x&&m[0].isLineHolder?m[0]=_:(g||!x||s)&&m.push(_)}}}var xo=N(",&?/;] ".split(""),(function(t,e){return t[e]=!0,t}),{});function wo(t){return!function(t){var e=t.charCodeAt(0);return e>=32&&e<=591||e>=880&&e<=4351||e>=4608&&e<=5119||e>=7680&&e<=8303}(t)||!!xo[t]}function bo(t,e,r,i,n){for(var o=[],a=[],s="",h="",l=0,u=0,c=0;c<t.length;c++){var f=t.charAt(c);if("\n"!==f){var p=An(f,e),d=!i&&!wo(f);(o.length?u+p>r:n+u+p>r)?u?(s||h)&&(d?(s||(s=h,h="",u=l=0),o.push(s),a.push(u-l),h+=f,s="",u=l+=p):(h&&(s+=h,h="",l=0),o.push(s),a.push(u),s=f,u=p)):d?(o.push(h),a.push(l),h=f,l=p):(o.push(f),a.push(p)):(u+=p,d?(h+=f,l+=p):(h&&(s+=h,h="",l=0),s+=f))}else h&&(s+=h,u+=l),o.push(s),a.push(u),s="",h="",l=0,u=0}return o.length||s||(s=t,h="",l=0),h&&(s+=h),s&&(o.push(s),a.push(u)),1===o.length&&(u+=n),{accumWidth:u,lines:o,linesWidths:a}}var ko="__zr_style_"+Math.round(10*Math.random()),So={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},To={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}};So[ko]=!0;var Co=["z","z2","invisible"],Po=["invisible"],Mo=function(e){function r(t){return e.call(this,t)||this}var i;return t(r,e),r.prototype._init=function(t){for(var r=W(t),i=0;i<r.length;i++){var n=r[i];"style"===n?this.useStyle(t[n]):e.prototype.attrKV.call(this,n,t[n])}this.style||this.useStyle({})},r.prototype.beforeBrush=function(){},r.prototype.afterBrush=function(){},r.prototype.innerBeforeBrush=function(){},r.prototype.innerAfterBrush=function(){},r.prototype.shouldBePainted=function(t,e,r,i){var n=this.transform;if(this.ignore||this.invisible||0===this.style.opacity||this.culling&&function(t,e,r){Ao.copy(t.getBoundingRect()),t.transform&&Ao.applyTransform(t.transform);return Lo.width=e,Lo.height=r,!Ao.intersect(Lo)}(this,t,e)||n&&!n[0]&&!n[3])return!1;if(r&&this.__clipPaths)for(var o=0;o<this.__clipPaths.length;++o)if(this.__clipPaths[o].isZeroArea())return!1;if(i&&this.parent)for(var a=this.parent;a;){if(a.ignore)return!1;a=a.parent}return!0},r.prototype.contain=function(t,e){return this.rectContain(t,e)},r.prototype.traverse=function(t,e){t.call(e,this)},r.prototype.rectContain=function(t,e){var r=this.transformCoordToLocal(t,e);return this.getBoundingRect().contain(r[0],r[1])},r.prototype.getPaintRect=function(){var t=this._paintRect;if(!this._paintRect||this.__dirty){var e=this.transform,r=this.getBoundingRect(),i=this.style,n=i.shadowBlur||0,o=i.shadowOffsetX||0,a=i.shadowOffsetY||0;t=this._paintRect||(this._paintRect=new Ne(0,0,0,0)),e?Ne.applyTransform(t,r,e):t.copy(r),(n||o||a)&&(t.width+=2*n+Math.abs(o),t.height+=2*n+Math.abs(a),t.x=Math.min(t.x,t.x+o-n),t.y=Math.min(t.y,t.y+a-n));var s=this.dirtyRectTolerance;t.isZero()||(t.x=Math.floor(t.x-s),t.y=Math.floor(t.y-s),t.width=Math.ceil(t.width+1+2*s),t.height=Math.ceil(t.height+1+2*s))}return t},r.prototype.setPrevPaintRect=function(t){t?(this._prevPaintRect=this._prevPaintRect||new Ne(0,0,0,0),this._prevPaintRect.copy(t)):this._prevPaintRect=null},r.prototype.getPrevPaintRect=function(){return this._prevPaintRect},r.prototype.animateStyle=function(t){return this.animate("style",t)},r.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():this.markRedraw()},r.prototype.attrKV=function(t,r){"style"!==t?e.prototype.attrKV.call(this,t,r):this.style?this.setStyle(r):this.useStyle(r)},r.prototype.setStyle=function(t,e){return"string"==typeof t?this.style[t]=e:A(this.style,t),this.dirtyStyle(),this},r.prototype.dirtyStyle=function(t){t||this.markRedraw(),this.__dirty|=2,this._rect&&(this._rect=null)},r.prototype.dirty=function(){this.dirtyStyle()},r.prototype.styleChanged=function(){return!!(2&this.__dirty)},r.prototype.styleUpdated=function(){this.__dirty&=-3},r.prototype.createStyle=function(t){return _t(So,t)},r.prototype.useStyle=function(t){t[ko]||(t=this.createStyle(t)),this.__inHover?this.__hoverStyle=t:this.style=t,this.dirtyStyle()},r.prototype.isStyleObject=function(t){return t[ko]},r.prototype._innerSaveToNormal=function(t){e.prototype._innerSaveToNormal.call(this,t);var r=this._normalState;t.style&&!r.style&&(r.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(t,r,Co)},r.prototype._applyStateObj=function(t,r,i,n,o,a){e.prototype._applyStateObj.call(this,t,r,i,n,o,a);var s,h=!(r&&n);if(r&&r.style?o?n?s=r.style:(s=this._mergeStyle(this.createStyle(),i.style),this._mergeStyle(s,r.style)):(s=this._mergeStyle(this.createStyle(),n?this.style:i.style),this._mergeStyle(s,r.style)):h&&(s=i.style),s)if(o){var l=this.style;if(this.style=this.createStyle(h?{}:l),h)for(var u=W(l),c=0;c<u.length;c++){(p=u[c])in s&&(s[p]=s[p],this.style[p]=l[p])}var f=W(s);for(c=0;c<f.length;c++){var p=f[c];this.style[p]=this.style[p]}this._transitionState(t,{style:s},a,this.getAnimationStyleProps())}else this.useStyle(s);var d=this.__inHover?Po:Co;for(c=0;c<d.length;c++){p=d[c];r&&null!=r[p]?this[p]=r[p]:h&&null!=i[p]&&(this[p]=i[p])}},r.prototype._mergeStates=function(t){for(var r,i=e.prototype._mergeStates.call(this,t),n=0;n<t.length;n++){var o=t[n];o.style&&(r=r||{},this._mergeStyle(r,o.style))}return r&&(i.style=r),i},r.prototype._mergeStyle=function(t,e){return A(t,e),t},r.prototype.getAnimationStyleProps=function(){return To},r.initDefaultProps=((i=r.prototype).type="displayable",i.invisible=!1,i.z=0,i.z2=0,i.zlevel=0,i.culling=!1,i.cursor="pointer",i.rectHover=!1,i.incremental=!1,i._rect=null,i.dirtyRectTolerance=0,void(i.__dirty=2|nr)),r}(Un),Ao=new Ne(0,0,0,0),Lo=new Ne(0,0,0,0);const Do=Mo;var zo=Math.min,Oo=Math.max,Io=Math.sin,Ro=Math.cos,Fo=2*Math.PI,Bo=St(),No=St(),Ho=St();function Eo(t,e,r){if(0!==t.length){for(var i=t[0],n=i[0],o=i[0],a=i[1],s=i[1],h=1;h<t.length;h++)i=t[h],n=zo(n,i[0]),o=Oo(o,i[0]),a=zo(a,i[1]),s=Oo(s,i[1]);e[0]=n,e[1]=a,r[0]=o,r[1]=s}}function Wo(t,e,r,i,n,o){n[0]=zo(t,r),n[1]=zo(e,i),o[0]=Oo(t,r),o[1]=Oo(e,i)}var Xo=[],jo=[];function qo(t,e,r,i,n,o,a,s,h,l){var u=Pr,c=Sr,f=u(t,r,n,a,Xo);h[0]=1/0,h[1]=1/0,l[0]=-1/0,l[1]=-1/0;for(var p=0;p<f;p++){var d=c(t,r,n,a,Xo[p]);h[0]=zo(d,h[0]),l[0]=Oo(d,l[0])}f=u(e,i,o,s,jo);for(p=0;p<f;p++){var v=c(e,i,o,s,jo[p]);h[1]=zo(v,h[1]),l[1]=Oo(v,l[1])}h[0]=zo(t,h[0]),l[0]=Oo(t,l[0]),h[0]=zo(a,h[0]),l[0]=Oo(a,l[0]),h[1]=zo(e,h[1]),l[1]=Oo(e,l[1]),h[1]=zo(s,h[1]),l[1]=Oo(s,l[1])}function Yo(t,e,r,i,n,o,a,s){var h=Or,l=Dr,u=Oo(zo(h(t,r,n),1),0),c=Oo(zo(h(e,i,o),1),0),f=l(t,r,n,u),p=l(e,i,o,c);a[0]=zo(t,n,f),a[1]=zo(e,o,p),s[0]=Oo(t,n,f),s[1]=Oo(e,o,p)}function Vo(t,e,r,i,n,o,a,s,h){var l=jt,u=qt,c=Math.abs(n-o);if(c%Fo<1e-4&&c>1e-4)return s[0]=t-r,s[1]=e-i,h[0]=t+r,void(h[1]=e+i);if(Bo[0]=Ro(n)*r+t,Bo[1]=Io(n)*i+e,No[0]=Ro(o)*r+t,No[1]=Io(o)*i+e,l(s,Bo,No),u(h,Bo,No),(n%=Fo)<0&&(n+=Fo),(o%=Fo)<0&&(o+=Fo),n>o&&!a?o+=Fo:n<o&&a&&(n+=Fo),a){var f=o;o=n,n=f}for(var p=0;p<o;p+=Math.PI/2)p>n&&(Ho[0]=Ro(p)*r+t,Ho[1]=Io(p)*i+e,l(s,Ho,s),u(h,Ho,h))}var Uo={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},Zo=[],Go=[],Ko=[],Qo=[],$o=[],Jo=[],ta=Math.min,ea=Math.max,ra=Math.cos,ia=Math.sin,na=Math.abs,oa=Math.PI,aa=2*oa,sa="undefined"!=typeof Float32Array,ha=[];function la(t){return Math.round(t/oa*1e8)/1e8%2*oa}function ua(t,e){var r=la(t[0]);r<0&&(r+=aa);var i=r-t[0],n=t[1];n+=i,!e&&n-r>=aa?n=r+aa:e&&r-n>=aa?n=r-aa:!e&&r>n?n=r+(aa-la(r-n)):e&&r<n&&(n=r-(aa-la(n-r))),t[0]=r,t[1]=n}const ca=function(){function t(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}var e;return t.prototype.increaseVersion=function(){this._version++},t.prototype.getVersion=function(){return this._version},t.prototype.setScale=function(t,e,r){(r=r||0)>0&&(this._ux=na(r/dn/t)||0,this._uy=na(r/dn/e)||0)},t.prototype.setDPR=function(t){this.dpr=t},t.prototype.setContext=function(t){this._ctx=t},t.prototype.getContext=function(){return this._ctx},t.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},t.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},t.prototype.moveTo=function(t,e){return this._drawPendingPt(),this.addData(Uo.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},t.prototype.lineTo=function(t,e){var r=na(t-this._xi),i=na(e-this._yi),n=r>this._ux||i>this._uy;if(this.addData(Uo.L,t,e),this._ctx&&n&&this._ctx.lineTo(t,e),n)this._xi=t,this._yi=e,this._pendingPtDist=0;else{var o=r*r+i*i;o>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=e,this._pendingPtDist=o)}return this},t.prototype.bezierCurveTo=function(t,e,r,i,n,o){return this._drawPendingPt(),this.addData(Uo.C,t,e,r,i,n,o),this._ctx&&this._ctx.bezierCurveTo(t,e,r,i,n,o),this._xi=n,this._yi=o,this},t.prototype.quadraticCurveTo=function(t,e,r,i){return this._drawPendingPt(),this.addData(Uo.Q,t,e,r,i),this._ctx&&this._ctx.quadraticCurveTo(t,e,r,i),this._xi=r,this._yi=i,this},t.prototype.arc=function(t,e,r,i,n,o){this._drawPendingPt(),ha[0]=i,ha[1]=n,ua(ha,o),i=ha[0];var a=(n=ha[1])-i;return this.addData(Uo.A,t,e,r,r,i,a,0,o?0:1),this._ctx&&this._ctx.arc(t,e,r,i,n,o),this._xi=ra(n)*r+t,this._yi=ia(n)*r+e,this},t.prototype.arcTo=function(t,e,r,i,n){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,e,r,i,n),this},t.prototype.rect=function(t,e,r,i){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,e,r,i),this.addData(Uo.R,t,e,r,i),this},t.prototype.closePath=function(){this._drawPendingPt(),this.addData(Uo.Z);var t=this._ctx,e=this._x0,r=this._y0;return t&&t.closePath(),this._xi=e,this._yi=r,this},t.prototype.fill=function(t){t&&t.fill(),this.toStatic()},t.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},t.prototype.len=function(){return this._len},t.prototype.setData=function(t){var e=t.length;this.data&&this.data.length===e||!sa||(this.data=new Float32Array(e));for(var r=0;r<e;r++)this.data[r]=t[r];this._len=e},t.prototype.appendPath=function(t){t instanceof Array||(t=[t]);for(var e=t.length,r=0,i=this._len,n=0;n<e;n++)r+=t[n].len();sa&&this.data instanceof Float32Array&&(this.data=new Float32Array(i+r));for(n=0;n<e;n++)for(var o=t[n].data,a=0;a<o.length;a++)this.data[i++]=o[a];this._len=i},t.prototype.addData=function(t,e,r,i,n,o,a,s,h){if(this._saveData){var l=this.data;this._len+arguments.length>l.length&&(this._expandData(),l=this.data);for(var u=0;u<arguments.length;u++)l[this._len++]=arguments[u]}},t.prototype._drawPendingPt=function(){this._pendingPtDist>0&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},t.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},t.prototype.toStatic=function(){if(this._saveData){this._drawPendingPt();var t=this.data;t instanceof Array&&(t.length=this._len,sa&&this._len>11&&(this.data=new Float32Array(t)))}},t.prototype.getBoundingRect=function(){Ko[0]=Ko[1]=$o[0]=$o[1]=Number.MAX_VALUE,Qo[0]=Qo[1]=Jo[0]=Jo[1]=-Number.MAX_VALUE;var t,e=this.data,r=0,i=0,n=0,o=0;for(t=0;t<this._len;){var a=e[t++],s=1===t;switch(s&&(n=r=e[t],o=i=e[t+1]),a){case Uo.M:r=n=e[t++],i=o=e[t++],$o[0]=n,$o[1]=o,Jo[0]=n,Jo[1]=o;break;case Uo.L:Wo(r,i,e[t],e[t+1],$o,Jo),r=e[t++],i=e[t++];break;case Uo.C:qo(r,i,e[t++],e[t++],e[t++],e[t++],e[t],e[t+1],$o,Jo),r=e[t++],i=e[t++];break;case Uo.Q:Yo(r,i,e[t++],e[t++],e[t],e[t+1],$o,Jo),r=e[t++],i=e[t++];break;case Uo.A:var h=e[t++],l=e[t++],u=e[t++],c=e[t++],f=e[t++],p=e[t++]+f;t+=1;var d=!e[t++];s&&(n=ra(f)*u+h,o=ia(f)*c+l),Vo(h,l,u,c,f,p,d,$o,Jo),r=ra(p)*u+h,i=ia(p)*c+l;break;case Uo.R:Wo(n=r=e[t++],o=i=e[t++],n+e[t++],o+e[t++],$o,Jo);break;case Uo.Z:r=n,i=o}jt(Ko,Ko,$o),qt(Qo,Qo,Jo)}return 0===t&&(Ko[0]=Ko[1]=Qo[0]=Qo[1]=0),new Ne(Ko[0],Ko[1],Qo[0]-Ko[0],Qo[1]-Ko[1])},t.prototype._calculateLength=function(){var t=this.data,e=this._len,r=this._ux,i=this._uy,n=0,o=0,a=0,s=0;this._pathSegLen||(this._pathSegLen=[]);for(var h=this._pathSegLen,l=0,u=0,c=0;c<e;){var f=t[c++],p=1===c;p&&(a=n=t[c],s=o=t[c+1]);var d=-1;switch(f){case Uo.M:n=a=t[c++],o=s=t[c++];break;case Uo.L:var v=t[c++],y=(m=t[c++])-o;(na(A=v-n)>r||na(y)>i||c===e-1)&&(d=Math.sqrt(A*A+y*y),n=v,o=m);break;case Uo.C:var g=t[c++],_=t[c++],m=(v=t[c++],t[c++]),x=t[c++],w=t[c++];d=Lr(n,o,g,_,v,m,x,w,10),n=x,o=w;break;case Uo.Q:d=Fr(n,o,g=t[c++],_=t[c++],v=t[c++],m=t[c++],10),n=v,o=m;break;case Uo.A:var b=t[c++],k=t[c++],S=t[c++],T=t[c++],C=t[c++],P=t[c++],M=P+C;c+=1,p&&(a=ra(C)*S+b,s=ia(C)*T+k),d=ea(S,T)*ta(aa,Math.abs(P)),n=ra(M)*S+b,o=ia(M)*T+k;break;case Uo.R:a=n=t[c++],s=o=t[c++],d=2*t[c++]+2*t[c++];break;case Uo.Z:var A=a-n;y=s-o;d=Math.sqrt(A*A+y*y),n=a,o=s}d>=0&&(h[u++]=d,l+=d)}return this._pathLen=l,l},t.prototype.rebuildPath=function(t,e){var r,i,n,o,a,s,h,l,u,c,f=this.data,p=this._ux,d=this._uy,v=this._len,y=e<1,g=0,_=0,m=0;if(!y||(this._pathSegLen||this._calculateLength(),h=this._pathSegLen,l=e*this._pathLen))t:for(var x=0;x<v;){var w=f[x++],b=1===x;switch(b&&(r=n=f[x],i=o=f[x+1]),w!==Uo.L&&m>0&&(t.lineTo(u,c),m=0),w){case Uo.M:r=n=f[x++],i=o=f[x++],t.moveTo(n,o);break;case Uo.L:a=f[x++],s=f[x++];var k=na(a-n),S=na(s-o);if(k>p||S>d){if(y){if(g+(Z=h[_++])>l){var T=(l-g)/Z;t.lineTo(n*(1-T)+a*T,o*(1-T)+s*T);break t}g+=Z}t.lineTo(a,s),n=a,o=s,m=0}else{var C=k*k+S*S;C>m&&(u=a,c=s,m=C)}break;case Uo.C:var P=f[x++],M=f[x++],A=f[x++],L=f[x++],D=f[x++],z=f[x++];if(y){if(g+(Z=h[_++])>l){Mr(n,P,A,D,T=(l-g)/Z,Zo),Mr(o,M,L,z,T,Go),t.bezierCurveTo(Zo[1],Go[1],Zo[2],Go[2],Zo[3],Go[3]);break t}g+=Z}t.bezierCurveTo(P,M,A,L,D,z),n=D,o=z;break;case Uo.Q:P=f[x++],M=f[x++],A=f[x++],L=f[x++];if(y){if(g+(Z=h[_++])>l){Ir(n,P,A,T=(l-g)/Z,Zo),Ir(o,M,L,T,Go),t.quadraticCurveTo(Zo[1],Go[1],Zo[2],Go[2]);break t}g+=Z}t.quadraticCurveTo(P,M,A,L),n=A,o=L;break;case Uo.A:var O=f[x++],I=f[x++],R=f[x++],F=f[x++],B=f[x++],N=f[x++],H=f[x++],E=!f[x++],W=R>F?R:F,X=na(R-F)>.001,j=B+N,q=!1;if(y)g+(Z=h[_++])>l&&(j=B+N*(l-g)/Z,q=!0),g+=Z;if(X&&t.ellipse?t.ellipse(O,I,R,F,H,B,j,E):t.arc(O,I,W,B,j,E),q)break t;b&&(r=ra(B)*R+O,i=ia(B)*F+I),n=ra(j)*R+O,o=ia(j)*F+I;break;case Uo.R:r=n=f[x],i=o=f[x+1],a=f[x++],s=f[x++];var Y=f[x++],V=f[x++];if(y){if(g+(Z=h[_++])>l){var U=l-g;t.moveTo(a,s),t.lineTo(a+ta(U,Y),s),(U-=Y)>0&&t.lineTo(a+Y,s+ta(U,V)),(U-=V)>0&&t.lineTo(a+ea(Y-U,0),s+V),(U-=Y)>0&&t.lineTo(a,s+ea(V-U,0));break t}g+=Z}t.rect(a,s,Y,V);break;case Uo.Z:if(y){var Z;if(g+(Z=h[_++])>l){T=(l-g)/Z;t.lineTo(n*(1-T)+r*T,o*(1-T)+i*T);break t}g+=Z}t.closePath(),n=r,o=i}}},t.prototype.clone=function(){var e=new t,r=this.data;return e.data=r.slice?r.slice():Array.prototype.slice.call(r),e._len=this._len,e},t.CMD=Uo,t.initDefaultProps=((e=t.prototype)._saveData=!0,e._ux=0,e._uy=0,e._pendingPtDist=0,void(e._version=0)),t}();function fa(t,e,r,i,n,o,a){if(0===n)return!1;var s=n,h=0;if(a>e+s&&a>i+s||a<e-s&&a<i-s||o>t+s&&o>r+s||o<t-s&&o<r-s)return!1;if(t===r)return Math.abs(o-t)<=s/2;var l=(h=(e-i)/(t-r))*o-a+(t*i-r*e)/(t-r);return l*l/(h*h+1)<=s/2*s/2}function pa(t,e,r,i,n,o,a,s,h,l,u){if(0===h)return!1;var c=h;return!(u>e+c&&u>i+c&&u>o+c&&u>s+c||u<e-c&&u<i-c&&u<o-c&&u<s-c||l>t+c&&l>r+c&&l>n+c&&l>a+c||l<t-c&&l<r-c&&l<n-c&&l<a-c)&&Ar(t,e,r,i,n,o,a,s,l,u,null)<=c/2}function da(t,e,r,i,n,o,a,s,h){if(0===a)return!1;var l=a;return!(h>e+l&&h>i+l&&h>o+l||h<e-l&&h<i-l&&h<o-l||s>t+l&&s>r+l&&s>n+l||s<t-l&&s<r-l&&s<n-l)&&Rr(t,e,r,i,n,o,s,h,null)<=l/2}var va=2*Math.PI;function ya(t){return(t%=va)<0&&(t+=va),t}var ga=2*Math.PI;function _a(t,e,r,i,n,o,a,s,h){if(0===a)return!1;var l=a;s-=t,h-=e;var u=Math.sqrt(s*s+h*h);if(u-l>r||u+l<r)return!1;if(Math.abs(i-n)%ga<1e-4)return!0;if(o){var c=i;i=ya(n),n=ya(c)}else i=ya(i),n=ya(n);i>n&&(n+=ga);var f=Math.atan2(h,s);return f<0&&(f+=ga),f>=i&&f<=n||f+ga>=i&&f+ga<=n}function ma(t,e,r,i,n,o){if(o>e&&o>i||o<e&&o<i)return 0;if(i===e)return 0;var a=(o-e)/(i-e),s=i<e?1:-1;1!==a&&0!==a||(s=i<e?.5:-.5);var h=a*(r-t)+t;return h===n?1/0:h>n?s:0}var xa=ca.CMD,wa=2*Math.PI,ba=1e-4;var ka=[-1,-1,-1],Sa=[-1,-1];function Ta(t,e,r,i,n,o,a,s,h,l){if(l>e&&l>i&&l>o&&l>s||l<e&&l<i&&l<o&&l<s)return 0;var u,c=Cr(e,i,o,s,l,ka);if(0===c)return 0;for(var f=0,p=-1,d=void 0,v=void 0,y=0;y<c;y++){var g=ka[y],_=0===g||1===g?.5:1;Sr(t,r,n,a,g)<h||(p<0&&(p=Pr(e,i,o,s,Sa),Sa[1]<Sa[0]&&p>1&&(u=void 0,u=Sa[0],Sa[0]=Sa[1],Sa[1]=u),d=Sr(e,i,o,s,Sa[0]),p>1&&(v=Sr(e,i,o,s,Sa[1]))),2===p?g<Sa[0]?f+=d<e?_:-_:g<Sa[1]?f+=v<d?_:-_:f+=s<v?_:-_:g<Sa[0]?f+=d<e?_:-_:f+=s<d?_:-_)}return f}function Ca(t,e,r,i,n,o,a,s){if(s>e&&s>i&&s>o||s<e&&s<i&&s<o)return 0;var h=function(t,e,r,i,n){var o=t-2*e+r,a=2*(e-t),s=t-i,h=0;if(br(o))kr(a)&&(u=-s/a)>=0&&u<=1&&(n[h++]=u);else{var l=a*a-4*o*s;if(br(l))(u=-a/(2*o))>=0&&u<=1&&(n[h++]=u);else if(l>0){var u,c=dr(l),f=(-a-c)/(2*o);(u=(-a+c)/(2*o))>=0&&u<=1&&(n[h++]=u),f>=0&&f<=1&&(n[h++]=f)}}return h}(e,i,o,s,ka);if(0===h)return 0;var l=Or(e,i,o);if(l>=0&&l<=1){for(var u=0,c=Dr(e,i,o,l),f=0;f<h;f++){var p=0===ka[f]||1===ka[f]?.5:1;Dr(t,r,n,ka[f])<a||(ka[f]<l?u+=c<e?p:-p:u+=o<c?p:-p)}return u}p=0===ka[0]||1===ka[0]?.5:1;return Dr(t,r,n,ka[0])<a?0:o<e?p:-p}function Pa(t,e,r,i,n,o,a,s){if((s-=e)>r||s<-r)return 0;var h=Math.sqrt(r*r-s*s);ka[0]=-h,ka[1]=h;var l=Math.abs(i-n);if(l<1e-4)return 0;if(l>=wa-1e-4){i=0,n=wa;var u=o?1:-1;return a>=ka[0]+t&&a<=ka[1]+t?u:0}if(i>n){var c=i;i=n,n=c}i<0&&(i+=wa,n+=wa);for(var f=0,p=0;p<2;p++){var d=ka[p];if(d+t>a){var v=Math.atan2(s,d);u=o?1:-1;v<0&&(v=wa+v),(v>=i&&v<=n||v+wa>=i&&v+wa<=n)&&(v>Math.PI/2&&v<1.5*Math.PI&&(u=-u),f+=u)}}return f}function Ma(t,e,r,i,n){for(var o,a,s,h,l=t.data,u=t.len(),c=0,f=0,p=0,d=0,v=0,y=0;y<u;){var g=l[y++],_=1===y;switch(g===xa.M&&y>1&&(r||(c+=ma(f,p,d,v,i,n))),_&&(d=f=l[y],v=p=l[y+1]),g){case xa.M:f=d=l[y++],p=v=l[y++];break;case xa.L:if(r){if(fa(f,p,l[y],l[y+1],e,i,n))return!0}else c+=ma(f,p,l[y],l[y+1],i,n)||0;f=l[y++],p=l[y++];break;case xa.C:if(r){if(pa(f,p,l[y++],l[y++],l[y++],l[y++],l[y],l[y+1],e,i,n))return!0}else c+=Ta(f,p,l[y++],l[y++],l[y++],l[y++],l[y],l[y+1],i,n)||0;f=l[y++],p=l[y++];break;case xa.Q:if(r){if(da(f,p,l[y++],l[y++],l[y],l[y+1],e,i,n))return!0}else c+=Ca(f,p,l[y++],l[y++],l[y],l[y+1],i,n)||0;f=l[y++],p=l[y++];break;case xa.A:var m=l[y++],x=l[y++],w=l[y++],b=l[y++],k=l[y++],S=l[y++];y+=1;var T=!!(1-l[y++]);o=Math.cos(k)*w+m,a=Math.sin(k)*b+x,_?(d=o,v=a):c+=ma(f,p,o,a,i,n);var C=(i-m)*b/w+m;if(r){if(_a(m,x,b,k,k+S,T,e,C,n))return!0}else c+=Pa(m,x,b,k,k+S,T,C,n);f=Math.cos(k+S)*w+m,p=Math.sin(k+S)*b+x;break;case xa.R:if(d=f=l[y++],v=p=l[y++],o=d+l[y++],a=v+l[y++],r){if(fa(d,v,o,v,e,i,n)||fa(o,v,o,a,e,i,n)||fa(o,a,d,a,e,i,n)||fa(d,a,d,v,e,i,n))return!0}else c+=ma(o,v,o,a,i,n),c+=ma(d,a,d,v,i,n);break;case xa.Z:if(r){if(fa(f,p,d,v,e,i,n))return!0}else c+=ma(f,p,d,v,i,n);f=d,p=v}}return r||(s=p,h=v,Math.abs(s-h)<ba)||(c+=ma(f,p,d,v,i,n)||0),0!==c}var Aa=L({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},So),La={style:L({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},To.style)},Da=Tn.concat(["invisible","culling","z","z2","zlevel","parent"]);const za=function(e){function r(t){return e.call(this,t)||this}var i;return t(r,e),r.prototype.update=function(){var t=this;e.prototype.update.call(this);var i=this.style;if(i.decal){var n=this._decalEl=this._decalEl||new r;n.buildPath===r.prototype.buildPath&&(n.buildPath=function(e){t.buildPath(e,t.shape)}),n.silent=!0;var o=n.style;for(var a in i)o[a]!==i[a]&&(o[a]=i[a]);o.fill=i.fill?i.decal:null,o.decal=null,o.shadowColor=null,i.strokeFirst&&(o.stroke=null);for(var s=0;s<Da.length;++s)n[Da[s]]=this[Da[s]];n.__dirty|=nr}else this._decalEl&&(this._decalEl=null)},r.prototype.getDecalElement=function(){return this._decalEl},r.prototype._init=function(t){var r=W(t);this.shape=this.getDefaultShape();var i=this.getDefaultStyle();i&&this.useStyle(i);for(var n=0;n<r.length;n++){var o=r[n],a=t[o];"style"===o?this.style?A(this.style,a):this.useStyle(a):"shape"===o?A(this.shape,a):e.prototype.attrKV.call(this,o,a)}this.style||this.useStyle({})},r.prototype.getDefaultStyle=function(){return null},r.prototype.getDefaultShape=function(){return{}},r.prototype.canBeInsideText=function(){return this.hasFill()},r.prototype.getInsideTextFill=function(){var t=this.style.fill;if("none"!==t){if(V(t)){var e=ci(t,0);return e>.5?vn:e>.2?"#eee":yn}if(t)return yn}return vn},r.prototype.getInsideTextStroke=function(t){var e=this.style.fill;if(V(e)){var r=this.__zr;if(!(!r||!r.isDarkMode())===ci(t,0)<.4)return e}},r.prototype.buildPath=function(t,e,r){},r.prototype.pathUpdated=function(){this.__dirty&=~or},r.prototype.getUpdatedPathProxy=function(t){return!this.path&&this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,t),this.path},r.prototype.createPathProxy=function(){this.path=new ca(!1)},r.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return!(null==e||"none"===e||!(t.lineWidth>0))},r.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},r.prototype.getBoundingRect=function(){var t=this._rect,e=this.style,r=!t;if(r){var i=!1;this.path||(i=!0,this.createPathProxy());var n=this.path;(i||this.__dirty&or)&&(n.beginPath(),this.buildPath(n,this.shape,!1),this.pathUpdated()),t=n.getBoundingRect()}if(this._rect=t,this.hasStroke()&&this.path&&this.path.len()>0){var o=this._rectStroke||(this._rectStroke=t.clone());if(this.__dirty||r){o.copy(t);var a=e.strokeNoScale?this.getLineScale():1,s=e.lineWidth;if(!this.hasFill()){var h=this.strokeContainThreshold;s=Math.max(s,null==h?4:h)}a>1e-10&&(o.width+=s/a,o.height+=s/a,o.x-=s/a/2,o.y-=s/a/2)}return o}return t},r.prototype.contain=function(t,e){var r=this.transformCoordToLocal(t,e),i=this.getBoundingRect(),n=this.style;if(t=r[0],e=r[1],i.contain(t,e)){var o=this.path;if(this.hasStroke()){var a=n.lineWidth,s=n.strokeNoScale?this.getLineScale():1;if(s>1e-10&&(this.hasFill()||(a=Math.max(a,this.strokeContainThreshold)),function(t,e,r,i){return Ma(t,e,!0,r,i)}(o,a/s,t,e)))return!0}if(this.hasFill())return function(t,e,r){return Ma(t,0,!1,e,r)}(o,t,e)}return!1},r.prototype.dirtyShape=function(){this.__dirty|=or,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},r.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},r.prototype.animateShape=function(t){return this.animate("shape",t)},r.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():"shape"===t?this.dirtyShape():this.markRedraw()},r.prototype.attrKV=function(t,r){"shape"===t?this.setShape(r):e.prototype.attrKV.call(this,t,r)},r.prototype.setShape=function(t,e){var r=this.shape;return r||(r=this.shape={}),"string"==typeof t?r[t]=e:A(r,t),this.dirtyShape(),this},r.prototype.shapeChanged=function(){return!!(this.__dirty&or)},r.prototype.createStyle=function(t){return _t(Aa,t)},r.prototype._innerSaveToNormal=function(t){e.prototype._innerSaveToNormal.call(this,t);var r=this._normalState;t.shape&&!r.shape&&(r.shape=A({},this.shape))},r.prototype._applyStateObj=function(t,r,i,n,o,a){e.prototype._applyStateObj.call(this,t,r,i,n,o,a);var s,h=!(r&&n);if(r&&r.shape?o?n?s=r.shape:(s=A({},i.shape),A(s,r.shape)):(s=A({},n?this.shape:i.shape),A(s,r.shape)):h&&(s=i.shape),s)if(o){this.shape=A({},this.shape);for(var l={},u=W(s),c=0;c<u.length;c++){var f=u[c];"object"==typeof s[f]?this.shape[f]=s[f]:l[f]=s[f]}this._transitionState(t,{shape:l},a)}else this.shape=s,this.dirtyShape()},r.prototype._mergeStates=function(t){for(var r,i=e.prototype._mergeStates.call(this,t),n=0;n<t.length;n++){var o=t[n];o.shape&&(r=r||{},this._mergeStyle(r,o.shape))}return r&&(i.shape=r),i},r.prototype.getAnimationStyleProps=function(){return La},r.prototype.isZeroArea=function(){return!1},r.extend=function(e){var i=function(r){function i(t){var i=r.call(this,t)||this;return e.init&&e.init.call(i,t),i}return t(i,r),i.prototype.getDefaultStyle=function(){return C(e.style)},i.prototype.getDefaultShape=function(){return C(e.shape)},i}(r);for(var n in e)"function"==typeof e[n]&&(i.prototype[n]=e[n]);return i},r.initDefaultProps=((i=r.prototype).type="path",i.strokeContainThreshold=5,i.segmentIgnoreThreshold=0,i.subPixelOptimize=!1,i.autoBatch=!1,void(i.__dirty=2|nr|or)),r}(Do);var Oa=L({strokeFirst:!0,font:a,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},Aa),Ia=function(e){function r(){return null!==e&&e.apply(this,arguments)||this}return t(r,e),r.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return null!=e&&"none"!==e&&t.lineWidth>0},r.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},r.prototype.createStyle=function(t){return _t(Oa,t)},r.prototype.setBoundingRect=function(t){this._rect=t},r.prototype.getBoundingRect=function(){var t=this.style;if(!this._rect){var e=t.text;null!=e?e+="":e="";var r=Dn(e,t.font,t.textAlign,t.textBaseline);if(r.x+=t.x||0,r.y+=t.y||0,this.hasStroke()){var i=t.lineWidth;r.x-=i/2,r.y-=i/2,r.width+=i,r.height+=i}this._rect=r}return this._rect},r.initDefaultProps=void(r.prototype.dirtyRectTolerance=10),r}(Do);Ia.prototype.type="tspan";const Ra=Ia;var Fa=L({x:0,y:0},So),Ba={style:L({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},To.style)};var Na=function(e){function r(){return null!==e&&e.apply(this,arguments)||this}return t(r,e),r.prototype.createStyle=function(t){return _t(Fa,t)},r.prototype._getSize=function(t){var e=this.style,r=e[t];if(null!=r)return r;var i,n=(i=e.image)&&"string"!=typeof i&&i.width&&i.height?e.image:this.__image;if(!n)return 0;var o="width"===t?"height":"width",a=e[o];return null==a?n[t]:n[t]/n[o]*a},r.prototype.getWidth=function(){return this._getSize("width")},r.prototype.getHeight=function(){return this._getSize("height")},r.prototype.getAnimationStyleProps=function(){return Ba},r.prototype.getBoundingRect=function(){var t=this.style;return this._rect||(this._rect=new Ne(t.x||0,t.y||0,this.getWidth(),this.getHeight())),this._rect},r}(Do);Na.prototype.type="image";const Ha=Na;var Ea=Math.round;function Wa(t,e,r){if(e){var i=e.x1,n=e.x2,o=e.y1,a=e.y2;t.x1=i,t.x2=n,t.y1=o,t.y2=a;var s=r&&r.lineWidth;return s?(Ea(2*i)===Ea(2*n)&&(t.x1=t.x2=ja(i,s,!0)),Ea(2*o)===Ea(2*a)&&(t.y1=t.y2=ja(o,s,!0)),t):t}}function Xa(t,e,r){if(e){var i=e.x,n=e.y,o=e.width,a=e.height;t.x=i,t.y=n,t.width=o,t.height=a;var s=r&&r.lineWidth;return s?(t.x=ja(i,s,!0),t.y=ja(n,s,!0),t.width=Math.max(ja(i+o,s,!1)-t.x,0===o?0:1),t.height=Math.max(ja(n+a,s,!1)-t.y,0===a?0:1),t):t}}function ja(t,e,r){if(!e)return t;var i=Ea(2*t);return(i+Ea(e))%2==0?i/2:(i+(r?1:-1))/2}var qa=function(){this.x=0,this.y=0,this.width=0,this.height=0},Ya={},Va=function(e){function r(t){return e.call(this,t)||this}return t(r,e),r.prototype.getDefaultShape=function(){return new qa},r.prototype.buildPath=function(t,e){var r,i,n,o;if(this.subPixelOptimize){var a=Xa(Ya,e,this.style);r=a.x,i=a.y,n=a.width,o=a.height,a.r=e.r,e=a}else r=e.x,i=e.y,n=e.width,o=e.height;e.r?function(t,e){var r,i,n,o,a,s=e.x,h=e.y,l=e.width,u=e.height,c=e.r;l<0&&(s+=l,l=-l),u<0&&(h+=u,u=-u),"number"==typeof c?r=i=n=o=c:c instanceof Array?1===c.length?r=i=n=o=c[0]:2===c.length?(r=n=c[0],i=o=c[1]):3===c.length?(r=c[0],i=o=c[1],n=c[2]):(r=c[0],i=c[1],n=c[2],o=c[3]):r=i=n=o=0,r+i>l&&(r*=l/(a=r+i),i*=l/a),n+o>l&&(n*=l/(a=n+o),o*=l/a),i+n>u&&(i*=u/(a=i+n),n*=u/a),r+o>u&&(r*=u/(a=r+o),o*=u/a),t.moveTo(s+r,h),t.lineTo(s+l-i,h),0!==i&&t.arc(s+l-i,h+i,i,-Math.PI/2,0),t.lineTo(s+l,h+u-n),0!==n&&t.arc(s+l-n,h+u-n,n,0,Math.PI/2),t.lineTo(s+o,h+u),0!==o&&t.arc(s+o,h+u-o,o,Math.PI/2,Math.PI),t.lineTo(s,h+r),0!==r&&t.arc(s+r,h+r,r,Math.PI,1.5*Math.PI)}(t,e):t.rect(r,i,n,o)},r.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},r}(za);Va.prototype.type="rect";const Ua=Va;var Za={fill:"#000"},Ga={style:L({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},To.style)},Ka=function(e){function r(t){var r=e.call(this)||this;return r.type="text",r._children=[],r._defaultStyle=Za,r.attr(t),r}return t(r,e),r.prototype.childrenRef=function(){return this._children},r.prototype.update=function(){e.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var t=0;t<this._children.length;t++){var r=this._children[t];r.zlevel=this.zlevel,r.z=this.z,r.z2=this.z2,r.culling=this.culling,r.cursor=this.cursor,r.invisible=this.invisible}},r.prototype.updateTransform=function(){var t=this.innerTransformable;t?(t.updateTransform(),t.transform&&(this.transform=t.transform)):e.prototype.updateTransform.call(this)},r.prototype.getLocalTransform=function(t){var r=this.innerTransformable;return r?r.getLocalTransform(t):e.prototype.getLocalTransform.call(this,t)},r.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),e.prototype.getComputedTransform.call(this)},r.prototype._updateSubTexts=function(){var t;this._childCursor=0,is(t=this.style),F(t.rich,is),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},r.prototype.addSelfToZr=function(t){e.prototype.addSelfToZr.call(this,t);for(var r=0;r<this._children.length;r++)this._children[r].__zr=t},r.prototype.removeSelfFromZr=function(t){e.prototype.removeSelfFromZr.call(this,t);for(var r=0;r<this._children.length;r++)this._children[r].__zr=null},r.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var t=new Ne(0,0,0,0),e=this._children,r=[],i=null,n=0;n<e.length;n++){var o=e[n],a=o.getBoundingRect(),s=o.getLocalTransform(r);s?(t.copy(a),t.applyTransform(s),(i=i||t.clone()).union(t)):(i=i||a.clone()).union(a)}this._rect=i||t}return this._rect},r.prototype.setDefaultTextStyle=function(t){this._defaultStyle=t||Za},r.prototype.setTextContent=function(t){},r.prototype._mergeStyle=function(t,e){if(!e)return t;var r=e.rich,i=t.rich||r&&{};return A(t,e),r&&i?(this._mergeRich(i,r),t.rich=i):i&&(t.rich=i),t},r.prototype._mergeRich=function(t,e){for(var r=W(e),i=0;i<r.length;i++){var n=r[i];t[n]=t[n]||{},A(t[n],e[n])}},r.prototype.getAnimationStyleProps=function(){return Ga},r.prototype._getOrCreateChild=function(t){var e=this._children[this._childCursor];return e&&e instanceof t||(e=new t),this._children[this._childCursor++]=e,e.__zr=this.__zr,e.parent=this,e},r.prototype._updatePlainTexts=function(){var t=this.style,e=t.font||a,r=t.padding,i=function(t,e){null!=t&&(t+="");var r,i=e.overflow,n=e.padding,o=e.font,a="truncate"===i,s=In(o),h=nt(e.lineHeight,s),l=!!e.backgroundColor,u="truncate"===e.lineOverflow,c=e.width,f=(r=null==c||"break"!==i&&"breakAll"!==i?t?t.split("\n"):[]:t?bo(t,e.font,c,"breakAll"===i,0).lines:[]).length*h,p=nt(e.height,f);if(f>p&&u){var d=Math.floor(p/h);r=r.slice(0,d)}if(t&&a&&null!=c)for(var v=fo(c,o,e.ellipsis,{minChar:e.truncateMinChar,placeholder:e.placeholder}),y=0;y<r.length;y++)r[y]=po(r[y],v);var g=p,_=0;for(y=0;y<r.length;y++)_=Math.max(An(r[y],o),_);null==c&&(c=_);var m=_;return n&&(g+=n[0]+n[2],m+=n[1]+n[3],c+=n[1]+n[3]),l&&(m=c),{lines:r,height:p,outerWidth:m,outerHeight:g,lineHeight:h,calculatedLineHeight:s,contentWidth:_,contentHeight:f,width:c}}(ss(t),t),n=hs(t),o=!!t.backgroundColor,s=i.outerHeight,h=i.outerWidth,l=i.contentWidth,u=i.lines,c=i.lineHeight,f=this._defaultStyle,p=t.x||0,d=t.y||0,v=t.align||f.align||"left",y=t.verticalAlign||f.verticalAlign||"top",g=p,_=On(d,i.contentHeight,y);if(n||r){var m=zn(p,h,v),x=On(d,s,y);n&&this._renderBackground(t,t,m,x,h,s)}_+=c/2,r&&(g=as(p,v,r),"top"===y?_+=r[0]:"bottom"===y&&(_-=r[2]));for(var w=0,b=!1,k=(os("fill"in t?t.fill:(b=!0,f.fill))),S=(ns("stroke"in t?t.stroke:o||f.autoStroke&&!b?null:(w=2,f.stroke))),T=t.textShadowBlur>0,C=null!=t.width&&("truncate"===t.overflow||"break"===t.overflow||"breakAll"===t.overflow),P=i.calculatedLineHeight,M=0;M<u.length;M++){var A=this._getOrCreateChild(Ra),L=A.createStyle();A.useStyle(L),L.text=u[M],L.x=g,L.y=_,v&&(L.textAlign=v),L.textBaseline="middle",L.opacity=t.opacity,L.strokeFirst=!0,T&&(L.shadowBlur=t.textShadowBlur||0,L.shadowColor=t.textShadowColor||"transparent",L.shadowOffsetX=t.textShadowOffsetX||0,L.shadowOffsetY=t.textShadowOffsetY||0),L.stroke=S,L.fill=k,S&&(L.lineWidth=t.lineWidth||w,L.lineDash=t.lineDash,L.lineDashOffset=t.lineDashOffset||0),L.font=e,es(L,t),_+=c,C&&A.setBoundingRect(new Ne(zn(L.x,t.width,L.textAlign),On(L.y,P,L.textBaseline),l,P))}},r.prototype._updateRichTexts=function(){var t=this.style,e=function(t,e){var r=new _o;if(null!=t&&(t+=""),!t)return r;for(var i,n=e.width,o=e.height,a=e.overflow,s="break"!==a&&"breakAll"!==a||null==n?null:{width:n,accumWidth:0,breakAll:"breakAll"===a},h=uo.lastIndex=0;null!=(i=uo.exec(t));){var l=i.index;l>h&&mo(r,t.substring(h,l),e,s),mo(r,i[2],e,s,i[1]),h=uo.lastIndex}h<t.length&&mo(r,t.substring(h,t.length),e,s);var u=[],c=0,f=0,p=e.padding,d="truncate"===a,v="truncate"===e.lineOverflow;function y(t,e,r){t.width=e,t.lineHeight=r,c+=r,f=Math.max(f,e)}t:for(var g=0;g<r.lines.length;g++){for(var _=r.lines[g],m=0,x=0,w=0;w<_.tokens.length;w++){var b=(z=_.tokens[w]).styleName&&e.rich[z.styleName]||{},k=z.textPadding=b.padding,S=k?k[1]+k[3]:0,T=z.font=b.font||e.font;z.contentHeight=In(T);var C=nt(b.height,z.contentHeight);if(z.innerHeight=C,k&&(C+=k[0]+k[2]),z.height=C,z.lineHeight=ot(b.lineHeight,e.lineHeight,C),z.align=b&&b.align||e.align,z.verticalAlign=b&&b.verticalAlign||"middle",v&&null!=o&&c+z.lineHeight>o){w>0?(_.tokens=_.tokens.slice(0,w),y(_,x,m),r.lines=r.lines.slice(0,g+1)):r.lines=r.lines.slice(0,g);break t}var P=b.width,M=null==P||"auto"===P;if("string"==typeof P&&"%"===P.charAt(P.length-1))z.percentWidth=P,u.push(z),z.contentWidth=An(z.text,T);else{if(M){var A=b.backgroundColor,L=A&&A.image;L&&lo(L=ao(L))&&(z.width=Math.max(z.width,L.width*C/L.height))}var D=d&&null!=n?n-x:null;null!=D&&D<z.width?!M||D<S?(z.text="",z.width=z.contentWidth=0):(z.text=co(z.text,D-S,T,e.ellipsis,{minChar:e.truncateMinChar}),z.width=z.contentWidth=An(z.text,T)):z.contentWidth=An(z.text,T)}z.width+=S,x+=z.width,b&&(m=Math.max(m,z.lineHeight))}y(_,x,m)}for(r.outerWidth=r.width=nt(n,f),r.outerHeight=r.height=nt(o,c),r.contentHeight=c,r.contentWidth=f,p&&(r.outerWidth+=p[1]+p[3],r.outerHeight+=p[0]+p[2]),g=0;g<u.length;g++){var z,O=(z=u[g]).percentWidth;z.width=parseInt(O,10)/100*r.width}return r}(ss(t),t),r=e.width,i=e.outerWidth,n=e.outerHeight,o=t.padding,a=t.x||0,s=t.y||0,h=this._defaultStyle,l=t.align||h.align,u=t.verticalAlign||h.verticalAlign,c=zn(a,i,l),f=On(s,n,u),p=c,d=f;o&&(p+=o[3],d+=o[0]);var v=p+r;hs(t)&&this._renderBackground(t,t,c,f,i,n);for(var y=!!t.backgroundColor,g=0;g<e.lines.length;g++){for(var _=e.lines[g],m=_.tokens,x=m.length,w=_.lineHeight,b=_.width,k=0,S=p,T=v,C=x-1,P=void 0;k<x&&(!(P=m[k]).align||"left"===P.align);)this._placeToken(P,t,w,d,S,"left",y),b-=P.width,S+=P.width,k++;for(;C>=0&&"right"===(P=m[C]).align;)this._placeToken(P,t,w,d,T,"right",y),b-=P.width,T-=P.width,C--;for(S+=(r-(S-p)-(v-T)-b)/2;k<=C;)P=m[k],this._placeToken(P,t,w,d,S+P.width/2,"center",y),S+=P.width,k++;d+=w}},r.prototype._placeToken=function(t,e,r,i,n,o,s){var h=e.rich[t.styleName]||{};h.text=t.text;var l=t.verticalAlign,u=i+r/2;"top"===l?u=i+t.height/2:"bottom"===l&&(u=i+r-t.height/2),!t.isLineHolder&&hs(h)&&this._renderBackground(h,e,"right"===o?n-t.width:"center"===o?n-t.width/2:n,u-t.height/2,t.width,t.height);var c=!!h.backgroundColor,f=t.textPadding;f&&(n=as(n,o,f),u-=t.height/2-f[0]-t.innerHeight/2);var p=this._getOrCreateChild(Ra),d=p.createStyle();p.useStyle(d);var v=this._defaultStyle,y=!1,g=0,_=os("fill"in h?h.fill:"fill"in e?e.fill:(y=!0,v.fill)),m=ns("stroke"in h?h.stroke:"stroke"in e?e.stroke:c||s||v.autoStroke&&!y?null:(g=2,v.stroke)),x=h.textShadowBlur>0||e.textShadowBlur>0;d.text=t.text,d.x=n,d.y=u,x&&(d.shadowBlur=h.textShadowBlur||e.textShadowBlur||0,d.shadowColor=h.textShadowColor||e.textShadowColor||"transparent",d.shadowOffsetX=h.textShadowOffsetX||e.textShadowOffsetX||0,d.shadowOffsetY=h.textShadowOffsetY||e.textShadowOffsetY||0),d.textAlign=o,d.textBaseline="middle",d.font=t.font||a,d.opacity=ot(h.opacity,e.opacity,1),es(d,h),m&&(d.lineWidth=ot(h.lineWidth,e.lineWidth,g),d.lineDash=nt(h.lineDash,e.lineDash),d.lineDashOffset=e.lineDashOffset||0,d.stroke=m),_&&(d.fill=_);var w=t.contentWidth,b=t.contentHeight;p.setBoundingRect(new Ne(zn(d.x,w,d.textAlign),On(d.y,b,d.textBaseline),w,b))},r.prototype._renderBackground=function(t,e,r,i,n,o){var a,s,h,l=t.backgroundColor,u=t.borderWidth,c=t.borderColor,f=l&&l.image,p=l&&!f,d=t.borderRadius,v=this;if(p||t.lineHeight||u&&c){(a=this._getOrCreateChild(Ua)).useStyle(a.createStyle()),a.style.fill=null;var y=a.shape;y.x=r,y.y=i,y.width=n,y.height=o,y.r=d,a.dirtyShape()}if(p)(h=a.style).fill=l||null,h.fillOpacity=nt(t.fillOpacity,1);else if(f){(s=this._getOrCreateChild(Ha)).onload=function(){v.dirtyStyle()};var g=s.style;g.image=l.image,g.x=r,g.y=i,g.width=n,g.height=o}u&&c&&((h=a.style).lineWidth=u,h.stroke=c,h.strokeOpacity=nt(t.strokeOpacity,1),h.lineDash=t.borderDash,h.lineDashOffset=t.borderDashOffset||0,a.strokeContainThreshold=0,a.hasFill()&&a.hasStroke()&&(h.strokeFirst=!0,h.lineWidth*=2));var _=(a||s).style;_.shadowBlur=t.shadowBlur||0,_.shadowColor=t.shadowColor||"transparent",_.shadowOffsetX=t.shadowOffsetX||0,_.shadowOffsetY=t.shadowOffsetY||0,_.opacity=ot(t.opacity,e.opacity,1)},r.makeFont=function(t){var e="";return rs(t)&&(e=[t.fontStyle,t.fontWeight,ts(t.fontSize),t.fontFamily||"sans-serif"].join(" ")),e&&lt(e)||t.textFont||t.font},r}(Do),Qa={left:!0,right:1,center:1},$a={top:1,bottom:1,middle:1},Ja=["fontStyle","fontWeight","fontSize","fontFamily"];function ts(t){return"string"!=typeof t||-1===t.indexOf("px")&&-1===t.indexOf("rem")&&-1===t.indexOf("em")?isNaN(+t)?n+"px":t+"px":t}function es(t,e){for(var r=0;r<Ja.length;r++){var i=Ja[r],n=e[i];null!=n&&(t[i]=n)}}function rs(t){return null!=t.fontSize||t.fontFamily||t.fontWeight}function is(t){if(t){t.font=Ka.makeFont(t);var e=t.align;"middle"===e&&(e="center"),t.align=null==e||Qa[e]?e:"left";var r=t.verticalAlign;"center"===r&&(r="middle"),t.verticalAlign=null==r||$a[r]?r:"top",t.padding&&(t.padding=st(t.padding))}}function ns(t,e){return null==t||e<=0||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function os(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function as(t,e,r){return"right"===e?t-r[1]:"center"===e?t+r[3]/2-r[1]/2:t+r[3]}function ss(t){var e=t.text;return null!=e&&(e+=""),e}function hs(t){return!!(t.backgroundColor||t.lineHeight||t.borderWidth&&t.borderColor)}const ls=Ka;var us=ca.CMD,cs=[[],[],[]],fs=Math.sqrt,ps=Math.atan2;function ds(t,e){if(e){var r,i,n,o,a,s,h=t.data,l=t.len(),u=us.M,c=us.C,f=us.L,p=us.R,d=us.A,v=us.Q;for(n=0,o=0;n<l;){switch(r=h[n++],o=n,i=0,r){case u:case f:i=1;break;case c:i=3;break;case v:i=2;break;case d:var y=e[4],g=e[5],_=fs(e[0]*e[0]+e[1]*e[1]),m=fs(e[2]*e[2]+e[3]*e[3]),x=ps(-e[1]/m,e[0]/_);h[n]*=_,h[n++]+=y,h[n]*=m,h[n++]+=g,h[n++]*=_,h[n++]*=m,h[n++]+=x,h[n++]+=x,o=n+=2;break;case p:s[0]=h[n++],s[1]=h[n++],Xt(s,s,e),h[o++]=s[0],h[o++]=s[1],s[0]+=h[n++],s[1]+=h[n++],Xt(s,s,e),h[o++]=s[0],h[o++]=s[1]}for(a=0;a<i;a++){var w=cs[a];w[0]=h[n++],w[1]=h[n++],Xt(w,w,e),h[o++]=w[0],h[o++]=w[1]}}t.increaseVersion()}}var vs=Math.sqrt,ys=Math.sin,gs=Math.cos,_s=Math.PI;function ms(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}function xs(t,e){return(t[0]*e[0]+t[1]*e[1])/(ms(t)*ms(e))}function ws(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(xs(t,e))}function bs(t,e,r,i,n,o,a,s,h,l,u){var c=h*(_s/180),f=gs(c)*(t-r)/2+ys(c)*(e-i)/2,p=-1*ys(c)*(t-r)/2+gs(c)*(e-i)/2,d=f*f/(a*a)+p*p/(s*s);d>1&&(a*=vs(d),s*=vs(d));var v=(n===o?-1:1)*vs((a*a*(s*s)-a*a*(p*p)-s*s*(f*f))/(a*a*(p*p)+s*s*(f*f)))||0,y=v*a*p/s,g=v*-s*f/a,_=(t+r)/2+gs(c)*y-ys(c)*g,m=(e+i)/2+ys(c)*y+gs(c)*g,x=ws([1,0],[(f-y)/a,(p-g)/s]),w=[(f-y)/a,(p-g)/s],b=[(-1*f-y)/a,(-1*p-g)/s],k=ws(w,b);if(xs(w,b)<=-1&&(k=_s),xs(w,b)>=1&&(k=0),k<0){var S=Math.round(k/_s*1e6)/1e6;k=2*_s+S%2*_s}u.addData(l,_,m,a,s,x,k,c,o)}var ks=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,Ss=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;var Ts=function(e){function r(){return null!==e&&e.apply(this,arguments)||this}return t(r,e),r.prototype.applyTransform=function(t){},r}(za);function Cs(t){return null!=t.setData}function Ps(t,e){var r=function(t){var e=new ca;if(!t)return e;var r,i=0,n=0,o=i,a=n,s=ca.CMD,h=t.match(ks);if(!h)return e;for(var l=0;l<h.length;l++){for(var u=h[l],c=u.charAt(0),f=void 0,p=u.match(Ss)||[],d=p.length,v=0;v<d;v++)p[v]=parseFloat(p[v]);for(var y=0;y<d;){var g=void 0,_=void 0,m=void 0,x=void 0,w=void 0,b=void 0,k=void 0,S=i,T=n,C=void 0,P=void 0;switch(c){case"l":i+=p[y++],n+=p[y++],f=s.L,e.addData(f,i,n);break;case"L":i=p[y++],n=p[y++],f=s.L,e.addData(f,i,n);break;case"m":i+=p[y++],n+=p[y++],f=s.M,e.addData(f,i,n),o=i,a=n,c="l";break;case"M":i=p[y++],n=p[y++],f=s.M,e.addData(f,i,n),o=i,a=n,c="L";break;case"h":i+=p[y++],f=s.L,e.addData(f,i,n);break;case"H":i=p[y++],f=s.L,e.addData(f,i,n);break;case"v":n+=p[y++],f=s.L,e.addData(f,i,n);break;case"V":n=p[y++],f=s.L,e.addData(f,i,n);break;case"C":f=s.C,e.addData(f,p[y++],p[y++],p[y++],p[y++],p[y++],p[y++]),i=p[y-2],n=p[y-1];break;case"c":f=s.C,e.addData(f,p[y++]+i,p[y++]+n,p[y++]+i,p[y++]+n,p[y++]+i,p[y++]+n),i+=p[y-2],n+=p[y-1];break;case"S":g=i,_=n,C=e.len(),P=e.data,r===s.C&&(g+=i-P[C-4],_+=n-P[C-3]),f=s.C,S=p[y++],T=p[y++],i=p[y++],n=p[y++],e.addData(f,g,_,S,T,i,n);break;case"s":g=i,_=n,C=e.len(),P=e.data,r===s.C&&(g+=i-P[C-4],_+=n-P[C-3]),f=s.C,S=i+p[y++],T=n+p[y++],i+=p[y++],n+=p[y++],e.addData(f,g,_,S,T,i,n);break;case"Q":S=p[y++],T=p[y++],i=p[y++],n=p[y++],f=s.Q,e.addData(f,S,T,i,n);break;case"q":S=p[y++]+i,T=p[y++]+n,i+=p[y++],n+=p[y++],f=s.Q,e.addData(f,S,T,i,n);break;case"T":g=i,_=n,C=e.len(),P=e.data,r===s.Q&&(g+=i-P[C-4],_+=n-P[C-3]),i=p[y++],n=p[y++],f=s.Q,e.addData(f,g,_,i,n);break;case"t":g=i,_=n,C=e.len(),P=e.data,r===s.Q&&(g+=i-P[C-4],_+=n-P[C-3]),i+=p[y++],n+=p[y++],f=s.Q,e.addData(f,g,_,i,n);break;case"A":m=p[y++],x=p[y++],w=p[y++],b=p[y++],k=p[y++],bs(S=i,T=n,i=p[y++],n=p[y++],b,k,m,x,w,f=s.A,e);break;case"a":m=p[y++],x=p[y++],w=p[y++],b=p[y++],k=p[y++],bs(S=i,T=n,i+=p[y++],n+=p[y++],b,k,m,x,w,f=s.A,e)}}"z"!==c&&"Z"!==c||(f=s.Z,e.addData(f),i=o,n=a),r=f}return e.toStatic(),e}(t),i=A({},e);return i.buildPath=function(t){if(Cs(t)){t.setData(r.data),(e=t.getContext())&&t.rebuildPath(e,1)}else{var e=t;r.rebuildPath(e,1)}},i.applyTransform=function(t){ds(r,t),this.dirtyShape()},i}function Ms(t,e){return new Ts(Ps(t,e))}function As(e,r){var i=Ps(e,r);return function(e){function r(t){var r=e.call(this,t)||this;return r.applyTransform=i.applyTransform,r.buildPath=i.buildPath,r}return t(r,e),r}(Ts)}function Ls(t,e){for(var r=[],i=t.length,n=0;n<i;n++){var o=t[n];r.push(o.getUpdatedPathProxy(!0))}var a=new za(e);return a.createPathProxy(),a.buildPath=function(t){if(Cs(t)){t.appendPath(r);var e=t.getContext();e&&t.rebuildPath(e,1)}},a}function Ds(t,e){e=e||{};var r=new za;return t.shape&&r.setShape(t.shape),r.setStyle(t.style),e.bakeTransform?ds(r.path,t.getComputedTransform()):e.toLocal?r.setLocalTransform(t.getComputedTransform()):r.copyTransform(t),r.buildPath=t.buildPath,r.applyTransform=r.applyTransform,r.z=t.z,r.z2=t.z2,r.zlevel=t.zlevel,r}var zs=function(){this.cx=0,this.cy=0,this.r=0},Os=function(e){function r(t){return e.call(this,t)||this}return t(r,e),r.prototype.getDefaultShape=function(){return new zs},r.prototype.buildPath=function(t,e){t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI)},r}(za);Os.prototype.type="circle";const Is=Os;var Rs=function(){this.cx=0,this.cy=0,this.rx=0,this.ry=0},Fs=function(e){function r(t){return e.call(this,t)||this}return t(r,e),r.prototype.getDefaultShape=function(){return new Rs},r.prototype.buildPath=function(t,e){var r=.5522848,i=e.cx,n=e.cy,o=e.rx,a=e.ry,s=o*r,h=a*r;t.moveTo(i-o,n),t.bezierCurveTo(i-o,n-h,i-s,n-a,i,n-a),t.bezierCurveTo(i+s,n-a,i+o,n-h,i+o,n),t.bezierCurveTo(i+o,n+h,i+s,n+a,i,n+a),t.bezierCurveTo(i-s,n+a,i-o,n+h,i-o,n),t.closePath()},r}(za);Fs.prototype.type="ellipse";const Bs=Fs;var Ns=Math.PI,Hs=2*Ns,Es=Math.sin,Ws=Math.cos,Xs=Math.acos,js=Math.atan2,qs=Math.abs,Ys=Math.sqrt,Vs=Math.max,Us=Math.min,Zs=1e-4;function Gs(t,e,r,i,n,o,a){var s=t-r,h=e-i,l=(a?o:-o)/Ys(s*s+h*h),u=l*h,c=-l*s,f=t+u,p=e+c,d=r+u,v=i+c,y=(f+d)/2,g=(p+v)/2,_=d-f,m=v-p,x=_*_+m*m,w=n-o,b=f*v-d*p,k=(m<0?-1:1)*Ys(Vs(0,w*w*x-b*b)),S=(b*m-_*k)/x,T=(-b*_-m*k)/x,C=(b*m+_*k)/x,P=(-b*_+m*k)/x,M=S-y,A=T-g,L=C-y,D=P-g;return M*M+A*A>L*L+D*D&&(S=C,T=P),{cx:S,cy:T,x0:-u,y0:-c,x1:S*(n/w-1),y1:T*(n/w-1)}}function Ks(t,e){var r,i=Vs(e.r,0),n=Vs(e.r0||0,0),o=i>0;if(o||n>0){if(o||(i=n,n=0),n>i){var a=i;i=n,n=a}var s=e.startAngle,h=e.endAngle;if(!isNaN(s)&&!isNaN(h)){var l=e.cx,u=e.cy,c=!!e.clockwise,f=qs(h-s),p=f>Hs&&f%Hs;if(p>Zs&&(f=p),i>Zs)if(f>Hs-Zs)t.moveTo(l+i*Ws(s),u+i*Es(s)),t.arc(l,u,i,s,h,!c),n>Zs&&(t.moveTo(l+n*Ws(h),u+n*Es(h)),t.arc(l,u,n,h,s,c));else{var d=void 0,v=void 0,y=void 0,g=void 0,_=void 0,m=void 0,x=void 0,w=void 0,b=void 0,k=void 0,S=void 0,T=void 0,C=void 0,P=void 0,M=void 0,A=void 0,L=i*Ws(s),D=i*Es(s),z=n*Ws(h),O=n*Es(h),I=f>Zs;if(I){var R=e.cornerRadius;R&&(d=(r=function(t){var e;if(q(t)){var r=t.length;if(!r)return t;e=1===r?[t[0],t[0],0,0]:2===r?[t[0],t[0],t[1],t[1]]:3===r?t.concat(t[2]):t}else e=[t,t,t,t];return e}(R))[0],v=r[1],y=r[2],g=r[3]);var F=qs(i-n)/2;if(_=Us(F,y),m=Us(F,g),x=Us(F,d),w=Us(F,v),S=b=Vs(_,m),T=k=Vs(x,w),(b>Zs||k>Zs)&&(C=i*Ws(h),P=i*Es(h),M=n*Ws(s),A=n*Es(s),f<Ns)){var B=function(t,e,r,i,n,o,a,s){var h=r-t,l=i-e,u=a-n,c=s-o,f=c*h-u*l;if(!(f*f<Zs))return[t+(f=(u*(e-o)-c*(t-n))/f)*h,e+f*l]}(L,D,M,A,C,P,z,O);if(B){var N=L-B[0],H=D-B[1],E=C-B[0],W=P-B[1],X=1/Es(Xs((N*E+H*W)/(Ys(N*N+H*H)*Ys(E*E+W*W)))/2),j=Ys(B[0]*B[0]+B[1]*B[1]);S=Us(b,(i-j)/(X+1)),T=Us(k,(n-j)/(X-1))}}}if(I)if(S>Zs){var Y=Us(y,S),V=Us(g,S),U=Gs(M,A,L,D,i,Y,c),Z=Gs(C,P,z,O,i,V,c);t.moveTo(l+U.cx+U.x0,u+U.cy+U.y0),S<b&&Y===V?t.arc(l+U.cx,u+U.cy,S,js(U.y0,U.x0),js(Z.y0,Z.x0),!c):(Y>0&&t.arc(l+U.cx,u+U.cy,Y,js(U.y0,U.x0),js(U.y1,U.x1),!c),t.arc(l,u,i,js(U.cy+U.y1,U.cx+U.x1),js(Z.cy+Z.y1,Z.cx+Z.x1),!c),V>0&&t.arc(l+Z.cx,u+Z.cy,V,js(Z.y1,Z.x1),js(Z.y0,Z.x0),!c))}else t.moveTo(l+L,u+D),t.arc(l,u,i,s,h,!c);else t.moveTo(l+L,u+D);if(n>Zs&&I)if(T>Zs){Y=Us(d,T),U=Gs(z,O,C,P,n,-(V=Us(v,T)),c),Z=Gs(L,D,M,A,n,-Y,c);t.lineTo(l+U.cx+U.x0,u+U.cy+U.y0),T<k&&Y===V?t.arc(l+U.cx,u+U.cy,T,js(U.y0,U.x0),js(Z.y0,Z.x0),!c):(V>0&&t.arc(l+U.cx,u+U.cy,V,js(U.y0,U.x0),js(U.y1,U.x1),!c),t.arc(l,u,n,js(U.cy+U.y1,U.cx+U.x1),js(Z.cy+Z.y1,Z.cx+Z.x1),c),Y>0&&t.arc(l+Z.cx,u+Z.cy,Y,js(Z.y1,Z.x1),js(Z.y0,Z.x0),!c))}else t.lineTo(l+z,u+O),t.arc(l,u,n,h,s,c);else t.lineTo(l+z,u+O)}else t.moveTo(l,u);t.closePath()}}}var Qs=function(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0,this.cornerRadius=0},$s=function(e){function r(t){return e.call(this,t)||this}return t(r,e),r.prototype.getDefaultShape=function(){return new Qs},r.prototype.buildPath=function(t,e){Ks(t,e)},r.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},r}(za);$s.prototype.type="sector";const Js=$s;var th=function(){this.cx=0,this.cy=0,this.r=0,this.r0=0},eh=function(e){function r(t){return e.call(this,t)||this}return t(r,e),r.prototype.getDefaultShape=function(){return new th},r.prototype.buildPath=function(t,e){var r=e.cx,i=e.cy,n=2*Math.PI;t.moveTo(r+e.r,i),t.arc(r,i,e.r,0,n,!1),t.moveTo(r+e.r0,i),t.arc(r,i,e.r0,0,n,!0)},r}(za);eh.prototype.type="ring";const rh=eh;function ih(t,e,r){var i=e.smooth,n=e.points;if(n&&n.length>=2){if(i){var o=function(t,e,r,i){var n,o,a,s,h=[],l=[],u=[],c=[];if(i){a=[1/0,1/0],s=[-1/0,-1/0];for(var f=0,p=t.length;f<p;f++)jt(a,a,t[f]),qt(s,s,t[f]);jt(a,a,i[0]),qt(s,s,i[1])}for(f=0,p=t.length;f<p;f++){var d=t[f];if(r)n=t[f?f-1:p-1],o=t[(f+1)%p];else{if(0===f||f===p-1){h.push(Ct(t[f]));continue}n=t[f-1],o=t[f+1]}Lt(l,o,n),Rt(l,l,e);var v=Bt(d,n),y=Bt(d,o),g=v+y;0!==g&&(v/=g,y/=g),Rt(u,l,-v),Rt(c,l,y);var _=Mt([],d,u),m=Mt([],d,c);i&&(qt(_,_,a),jt(_,_,s),qt(m,m,a),jt(m,m,s)),h.push(_),h.push(m)}return r&&h.push(h.shift()),h}(n,i,r,e.smoothConstraint);t.moveTo(n[0][0],n[0][1]);for(var a=n.length,s=0;s<(r?a:a-1);s++){var h=o[2*s],l=o[2*s+1],u=n[(s+1)%a];t.bezierCurveTo(h[0],h[1],l[0],l[1],u[0],u[1])}}else{t.moveTo(n[0][0],n[0][1]);s=1;for(var c=n.length;s<c;s++)t.lineTo(n[s][0],n[s][1])}r&&t.closePath()}}var nh=function(){this.points=null,this.smooth=0,this.smoothConstraint=null},oh=function(e){function r(t){return e.call(this,t)||this}return t(r,e),r.prototype.getDefaultShape=function(){return new nh},r.prototype.buildPath=function(t,e){ih(t,e,!0)},r}(za);oh.prototype.type="polygon";const ah=oh;var sh=function(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null},hh=function(e){function r(t){return e.call(this,t)||this}return t(r,e),r.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},r.prototype.getDefaultShape=function(){return new sh},r.prototype.buildPath=function(t,e){ih(t,e,!1)},r}(za);hh.prototype.type="polyline";const lh=hh;var uh={},ch=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1},fh=function(e){function r(t){return e.call(this,t)||this}return t(r,e),r.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},r.prototype.getDefaultShape=function(){return new ch},r.prototype.buildPath=function(t,e){var r,i,n,o;if(this.subPixelOptimize){var a=Wa(uh,e,this.style);r=a.x1,i=a.y1,n=a.x2,o=a.y2}else r=e.x1,i=e.y1,n=e.x2,o=e.y2;var s=e.percent;0!==s&&(t.moveTo(r,i),s<1&&(n=r*(1-s)+n*s,o=i*(1-s)+o*s),t.lineTo(n,o))},r.prototype.pointAt=function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]},r}(za);fh.prototype.type="line";const ph=fh;var dh=[],vh=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1};function yh(t,e,r){var i=t.cpx2,n=t.cpy2;return null!=i||null!=n?[(r?Tr:Sr)(t.x1,t.cpx1,t.cpx2,t.x2,e),(r?Tr:Sr)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(r?zr:Dr)(t.x1,t.cpx1,t.x2,e),(r?zr:Dr)(t.y1,t.cpy1,t.y2,e)]}var gh=function(e){function r(t){return e.call(this,t)||this}return t(r,e),r.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},r.prototype.getDefaultShape=function(){return new vh},r.prototype.buildPath=function(t,e){var r=e.x1,i=e.y1,n=e.x2,o=e.y2,a=e.cpx1,s=e.cpy1,h=e.cpx2,l=e.cpy2,u=e.percent;0!==u&&(t.moveTo(r,i),null==h||null==l?(u<1&&(Ir(r,a,n,u,dh),a=dh[1],n=dh[2],Ir(i,s,o,u,dh),s=dh[1],o=dh[2]),t.quadraticCurveTo(a,s,n,o)):(u<1&&(Mr(r,a,h,n,u,dh),a=dh[1],h=dh[2],n=dh[3],Mr(i,s,l,o,u,dh),s=dh[1],l=dh[2],o=dh[3]),t.bezierCurveTo(a,s,h,l,n,o)))},r.prototype.pointAt=function(t){return yh(this.shape,t,!1)},r.prototype.tangentAt=function(t){var e=yh(this.shape,t,!0);return Ft(e,e)},r}(za);gh.prototype.type="bezier-curve";const _h=gh;var mh=function(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0},xh=function(e){function r(t){return e.call(this,t)||this}return t(r,e),r.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},r.prototype.getDefaultShape=function(){return new mh},r.prototype.buildPath=function(t,e){var r=e.cx,i=e.cy,n=Math.max(e.r,0),o=e.startAngle,a=e.endAngle,s=e.clockwise,h=Math.cos(o),l=Math.sin(o);t.moveTo(h*n+r,l*n+i),t.arc(r,i,n,o,a,!s)},r}(za);xh.prototype.type="arc";const wh=xh;const bh=function(e){function r(){var t=null!==e&&e.apply(this,arguments)||this;return t.type="compound",t}return t(r,e),r.prototype._updatePathDirty=function(){for(var t=this.shape.paths,e=this.shapeChanged(),r=0;r<t.length;r++)e=e||t[r].shapeChanged();e&&this.dirtyShape()},r.prototype.beforeBrush=function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),r=0;r<t.length;r++)t[r].path||t[r].createPathProxy(),t[r].path.setScale(e[0],e[1],t[r].segmentIgnoreThreshold)},r.prototype.buildPath=function(t,e){for(var r=e.paths||[],i=0;i<r.length;i++)r[i].buildPath(t,r[i].shape,!0)},r.prototype.afterBrush=function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].pathUpdated()},r.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),za.prototype.getBoundingRect.call(this)},r}(za);const kh=function(){function t(t){this.colorStops=t||[]}return t.prototype.addColorStop=function(t,e){this.colorStops.push({offset:t,color:e})},t}();const Sh=function(e){function r(t,r,i,n,o,a){var s=e.call(this,o)||this;return s.x=null==t?0:t,s.y=null==r?0:r,s.x2=null==i?1:i,s.y2=null==n?0:n,s.type="linear",s.global=a||!1,s}return t(r,e),r}(kh);const Th=function(e){function r(t,r,i,n,o){var a=e.call(this,n)||this;return a.x=null==t?.5:t,a.y=null==r?.5:r,a.r=null==i?.5:i,a.type="radial",a.global=o||!1,a}return t(r,e),r}(kh);var Ch=[0,0],Ph=[0,0],Mh=new Ae,Ah=new Ae;const Lh=function(){function t(t,e){this._corners=[],this._axes=[],this._origin=[0,0];for(var r=0;r<4;r++)this._corners[r]=new Ae;for(r=0;r<2;r++)this._axes[r]=new Ae;t&&this.fromBoundingRect(t,e)}return t.prototype.fromBoundingRect=function(t,e){var r=this._corners,i=this._axes,n=t.x,o=t.y,a=n+t.width,s=o+t.height;if(r[0].set(n,o),r[1].set(a,o),r[2].set(a,s),r[3].set(n,s),e)for(var h=0;h<4;h++)r[h].transform(e);Ae.sub(i[0],r[1],r[0]),Ae.sub(i[1],r[3],r[0]),i[0].normalize(),i[1].normalize();for(h=0;h<2;h++)this._origin[h]=i[h].dot(r[0])},t.prototype.intersect=function(t,e){var r=!0,i=!e;return Mh.set(1/0,1/0),Ah.set(0,0),!this._intersectCheckOneSide(this,t,Mh,Ah,i,1)&&(r=!1,i)||!this._intersectCheckOneSide(t,this,Mh,Ah,i,-1)&&(r=!1,i)||i||Ae.copy(e,r?Mh:Ah),r},t.prototype._intersectCheckOneSide=function(t,e,r,i,n,o){for(var a=!0,s=0;s<2;s++){var h=this._axes[s];if(this._getProjMinMaxOnAxis(s,t._corners,Ch),this._getProjMinMaxOnAxis(s,e._corners,Ph),Ch[1]<Ph[0]||Ch[0]>Ph[1]){if(a=!1,n)return a;var l=Math.abs(Ph[0]-Ch[1]),u=Math.abs(Ch[0]-Ph[1]);Math.min(l,u)>i.len()&&(l<u?Ae.scale(i,h,-l*o):Ae.scale(i,h,u*o))}else if(r){l=Math.abs(Ph[0]-Ch[1]),u=Math.abs(Ch[0]-Ph[1]);Math.min(l,u)<r.len()&&(l<u?Ae.scale(r,h,l*o):Ae.scale(r,h,-u*o))}}return a},t.prototype._getProjMinMaxOnAxis=function(t,e,r){for(var i=this._axes[t],n=this._origin,o=e[0].dot(i)+n[t],a=o,s=o,h=1;h<e.length;h++){var l=e[h].dot(i)+n[t];a=Math.min(l,a),s=Math.max(l,s)}r[0]=a,r[1]=s},t}();var Dh=[];const zh=function(e){function r(){var t=null!==e&&e.apply(this,arguments)||this;return t.notClear=!0,t.incremental=!0,t._displayables=[],t._temporaryDisplayables=[],t._cursor=0,t}return t(r,e),r.prototype.traverse=function(t,e){t.call(e,this)},r.prototype.useStyle=function(){this.style={}},r.prototype.getCursor=function(){return this._cursor},r.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},r.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},r.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},r.prototype.addDisplayable=function(t,e){e?this._temporaryDisplayables.push(t):this._displayables.push(t),this.markRedraw()},r.prototype.addDisplayables=function(t,e){e=e||!1;for(var r=0;r<t.length;r++)this.addDisplayable(t[r],e)},r.prototype.getDisplayables=function(){return this._displayables},r.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},r.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},r.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++){(e=this._displayables[t]).parent=this,e.update(),e.parent=null}for(t=0;t<this._temporaryDisplayables.length;t++){var e;(e=this._temporaryDisplayables[t]).parent=this,e.update(),e.parent=null}},r.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new Ne(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var r=this._displayables[e],i=r.getBoundingRect().clone();r.needLocalTransform()&&i.applyTransform(r.getLocalTransform(Dh)),t.union(i)}this._rect=t}return this._rect},r.prototype.contain=function(t,e){var r=this.transformCoordToLocal(t,e);if(this.getBoundingRect().contain(r[0],r[1]))for(var i=0;i<this._displayables.length;i++){if(this._displayables[i].contain(t,e))return!0}return!1},r}(Do);var Oh=Math.round(9*Math.random()),Ih="function"==typeof Object.defineProperty;const Rh=function(){function t(){this._id="__ec_inner_"+Oh++}return t.prototype.get=function(t){return this._guard(t)[this._id]},t.prototype.set=function(t,e){var r=this._guard(t);return Ih?Object.defineProperty(r,this._id,{value:e,enumerable:!1,configurable:!0}):r[this._id]=e,this},t.prototype.delete=function(t){return!!this.has(t)&&(delete this._guard(t)[this._id],!0)},t.prototype.has=function(t){return!!this._guard(t)[this._id]},t.prototype._guard=function(t){if(t!==Object(t))throw TypeError("Value of WeakMap is not a non-null object.");return t},t}();function Fh(t){return isFinite(t)}function Bh(t,e,r){for(var i="radial"===e.type?function(t,e,r){var i=r.width,n=r.height,o=Math.min(i,n),a=null==e.x?.5:e.x,s=null==e.y?.5:e.y,h=null==e.r?.5:e.r;return e.global||(a=a*i+r.x,s=s*n+r.y,h*=o),a=Fh(a)?a:.5,s=Fh(s)?s:.5,h=h>=0&&Fh(h)?h:.5,t.createRadialGradient(a,s,0,a,s,h)}(t,e,r):function(t,e,r){var i=null==e.x?0:e.x,n=null==e.x2?1:e.x2,o=null==e.y?0:e.y,a=null==e.y2?0:e.y2;return e.global||(i=i*r.width+r.x,n=n*r.width+r.x,o=o*r.height+r.y,a=a*r.height+r.y),i=Fh(i)?i:0,n=Fh(n)?n:1,o=Fh(o)?o:0,a=Fh(a)?a:0,t.createLinearGradient(i,o,n,a)}(t,e,r),n=e.colorStops,o=0;o<n.length;o++)i.addColorStop(n[o].offset,n[o].color);return i}function Nh(t){return parseInt(t,10)}function Hh(t,e,r){var i=["width","height"][e],n=["clientWidth","clientHeight"][e],o=["paddingLeft","paddingTop"][e],a=["paddingRight","paddingBottom"][e];if(null!=r[i]&&"auto"!==r[i])return parseFloat(r[i]);var s=document.defaultView.getComputedStyle(t);return(t[n]||Nh(s[i])||Nh(t.style[i]))-(Nh(s[o])||0)-(Nh(s[a])||0)|0}function Eh(t){var e,r,i=t.style,n=i.lineDash&&i.lineWidth>0&&(e=i.lineDash,r=i.lineWidth,e&&"solid"!==e&&r>0?"dashed"===e?[4*r,2*r]:"dotted"===e?[r]:Z(e)?[e]:q(e)?e:null:null),o=i.lineDashOffset;if(n){var a=i.strokeNoScale&&t.getLineScale?t.getLineScale():1;a&&1!==a&&(n=B(n,(function(t){return t/a})),o/=a)}return[n,o]}var Wh=new ca(!0);function Xh(t){var e=t.stroke;return!(null==e||"none"===e||!(t.lineWidth>0))}function jh(t){return"string"==typeof t&&"none"!==t}function qh(t){var e=t.fill;return null!=e&&"none"!==e}function Yh(t,e){if(null!=e.fillOpacity&&1!==e.fillOpacity){var r=t.globalAlpha;t.globalAlpha=e.fillOpacity*e.opacity,t.fill(),t.globalAlpha=r}else t.fill()}function Vh(t,e){if(null!=e.strokeOpacity&&1!==e.strokeOpacity){var r=t.globalAlpha;t.globalAlpha=e.strokeOpacity*e.opacity,t.stroke(),t.globalAlpha=r}else t.stroke()}function Uh(t,e,r){var i=so(e.image,e.__image,r);if(lo(i)){var n=t.createPattern(i,e.repeat||"repeat");if("function"==typeof DOMMatrix&&n&&n.setTransform){var o=new DOMMatrix;o.translateSelf(e.x||0,e.y||0),o.rotateSelf(0,0,(e.rotation||0)*bt),o.scaleSelf(e.scaleX||1,e.scaleY||1),n.setTransform(o)}return n}}var Zh=["shadowBlur","shadowOffsetX","shadowOffsetY"],Gh=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function Kh(t,e,r,i,n){var o=!1;if(!i&&e===(r=r||{}))return!1;if(i||e.opacity!==r.opacity){il(t,n),o=!0;var a=Math.max(Math.min(e.opacity,1),0);t.globalAlpha=isNaN(a)?So.opacity:a}(i||e.blend!==r.blend)&&(o||(il(t,n),o=!0),t.globalCompositeOperation=e.blend||So.blend);for(var s=0;s<Zh.length;s++){var h=Zh[s];(i||e[h]!==r[h])&&(o||(il(t,n),o=!0),t[h]=t.dpr*(e[h]||0))}return(i||e.shadowColor!==r.shadowColor)&&(o||(il(t,n),o=!0),t.shadowColor=e.shadowColor||So.shadowColor),o}function Qh(t,e,r,i,n){var o=nl(e,n.inHover),a=i?null:r&&nl(r,n.inHover)||{};if(o===a)return!1;var s=Kh(t,o,a,i,n);if((i||o.fill!==a.fill)&&(s||(il(t,n),s=!0),jh(o.fill)&&(t.fillStyle=o.fill)),(i||o.stroke!==a.stroke)&&(s||(il(t,n),s=!0),jh(o.stroke)&&(t.strokeStyle=o.stroke)),(i||o.opacity!==a.opacity)&&(s||(il(t,n),s=!0),t.globalAlpha=null==o.opacity?1:o.opacity),e.hasStroke()){var h=o.lineWidth/(o.strokeNoScale&&e.getLineScale?e.getLineScale():1);t.lineWidth!==h&&(s||(il(t,n),s=!0),t.lineWidth=h)}for(var l=0;l<Gh.length;l++){var u=Gh[l],c=u[0];(i||o[c]!==a[c])&&(s||(il(t,n),s=!0),t[c]=o[c]||u[1])}return s}function $h(t,e){var r=e.transform,i=t.dpr||1;r?t.setTransform(i*r[0],i*r[1],i*r[2],i*r[3],i*r[4],i*r[5]):t.setTransform(i,0,0,i,0,0)}var Jh=1,tl=2,el=3,rl=4;function il(t,e){e.batchFill&&t.fill(),e.batchStroke&&t.stroke(),e.batchFill="",e.batchStroke=""}function nl(t,e){return e&&t.__hoverStyle||t.style}function ol(t,e){al(t,e,{inHover:!1,viewWidth:0,viewHeight:0},!0)}function al(t,e,r,i){var n=e.transform;if(!e.shouldBePainted(r.viewWidth,r.viewHeight,!1,!1))return e.__dirty&=~nr,void(e.__isRendered=!1);var o=e.__clipPaths,s=r.prevElClipPaths,h=!1,l=!1;if(s&&!function(t,e){if(t===e||!t&&!e)return!1;if(!t||!e||t.length!==e.length)return!0;for(var r=0;r<t.length;r++)if(t[r]!==e[r])return!0;return!1}(o,s)||(s&&s.length&&(il(t,r),t.restore(),l=h=!0,r.prevElClipPaths=null,r.allClipped=!1,r.prevEl=null),o&&o.length&&(il(t,r),t.save(),function(t,e,r){for(var i=!1,n=0;n<t.length;n++){var o=t[n];i=i||o.isZeroArea(),$h(e,o),e.beginPath(),o.buildPath(e,o.shape),e.clip()}r.allClipped=i}(o,t,r),h=!0),r.prevElClipPaths=o),r.allClipped)e.__isRendered=!1;else{e.beforeBrush&&e.beforeBrush(),e.innerBeforeBrush();var u=r.prevEl;u||(l=h=!0);var c,f,p=e instanceof za&&e.autoBatch&&function(t){var e=qh(t),r=Xh(t);return!(t.lineDash||!(+e^+r)||e&&"string"!=typeof t.fill||r&&"string"!=typeof t.stroke||t.strokePercent<1||t.strokeOpacity<1||t.fillOpacity<1)}(e.style);h||(c=n,f=u.transform,c&&f?c[0]!==f[0]||c[1]!==f[1]||c[2]!==f[2]||c[3]!==f[3]||c[4]!==f[4]||c[5]!==f[5]:c||f)?(il(t,r),$h(t,e)):p||il(t,r);var d=nl(e,r.inHover);e instanceof za?(r.lastDrawType!==Jh&&(l=!0,r.lastDrawType=Jh),Qh(t,e,u,l,r),p&&(r.batchFill||r.batchStroke)||t.beginPath(),function(t,e,r,i){var n,o=Xh(r),a=qh(r),s=r.strokePercent,h=s<1,l=!e.path;e.silent&&!h||!l||e.createPathProxy();var u=e.path||Wh,c=e.__dirty;if(!i){var f=r.fill,p=r.stroke,d=a&&!!f.colorStops,v=o&&!!p.colorStops,y=a&&!!f.image,g=o&&!!p.image,_=void 0,m=void 0,x=void 0,w=void 0,b=void 0;(d||v)&&(b=e.getBoundingRect()),d&&(_=c?Bh(t,f,b):e.__canvasFillGradient,e.__canvasFillGradient=_),v&&(m=c?Bh(t,p,b):e.__canvasStrokeGradient,e.__canvasStrokeGradient=m),y&&(x=c||!e.__canvasFillPattern?Uh(t,f,e):e.__canvasFillPattern,e.__canvasFillPattern=x),g&&(w=c||!e.__canvasStrokePattern?Uh(t,p,e):e.__canvasStrokePattern,e.__canvasStrokePattern=x),d?t.fillStyle=_:y&&(x?t.fillStyle=x:a=!1),v?t.strokeStyle=m:g&&(w?t.strokeStyle=w:o=!1)}var k,S,T=e.getGlobalScale();u.setScale(T[0],T[1],e.segmentIgnoreThreshold),t.setLineDash&&r.lineDash&&(k=(n=Eh(e))[0],S=n[1]);var C=!0;(l||c&or)&&(u.setDPR(t.dpr),h?u.setContext(null):(u.setContext(t),C=!1),u.reset(),e.buildPath(u,e.shape,i),u.toStatic(),e.pathUpdated()),C&&u.rebuildPath(t,h?s:1),k&&(t.setLineDash(k),t.lineDashOffset=S),i||(r.strokeFirst?(o&&Vh(t,r),a&&Yh(t,r)):(a&&Yh(t,r),o&&Vh(t,r))),k&&t.setLineDash([])}(t,e,d,p),p&&(r.batchFill=d.fill||"",r.batchStroke=d.stroke||"")):e instanceof Ra?(r.lastDrawType!==el&&(l=!0,r.lastDrawType=el),Qh(t,e,u,l,r),function(t,e,r){var i,n=r.text;if(null!=n&&(n+=""),n){t.font=r.font||a,t.textAlign=r.textAlign,t.textBaseline=r.textBaseline;var o=void 0,s=void 0;t.setLineDash&&r.lineDash&&(o=(i=Eh(e))[0],s=i[1]),o&&(t.setLineDash(o),t.lineDashOffset=s),r.strokeFirst?(Xh(r)&&t.strokeText(n,r.x,r.y),qh(r)&&t.fillText(n,r.x,r.y)):(qh(r)&&t.fillText(n,r.x,r.y),Xh(r)&&t.strokeText(n,r.x,r.y)),o&&t.setLineDash([])}}(t,e,d)):e instanceof Ha?(r.lastDrawType!==tl&&(l=!0,r.lastDrawType=tl),function(t,e,r,i,n){Kh(t,nl(e,n.inHover),r&&nl(r,n.inHover),i,n)}(t,e,u,l,r),function(t,e,r){var i=e.__image=so(r.image,e.__image,e,e.onload);if(i&&lo(i)){var n=r.x||0,o=r.y||0,a=e.getWidth(),s=e.getHeight(),h=i.width/i.height;if(null==a&&null!=s?a=s*h:null==s&&null!=a?s=a/h:null==a&&null==s&&(a=i.width,s=i.height),r.sWidth&&r.sHeight){var l=r.sx||0,u=r.sy||0;t.drawImage(i,l,u,r.sWidth,r.sHeight,n,o,a,s)}else if(r.sx&&r.sy){var c=a-(l=r.sx),f=s-(u=r.sy);t.drawImage(i,l,u,c,f,n,o,a,s)}else t.drawImage(i,n,o,a,s)}}(t,e,d)):e.getTemporalDisplayables&&(r.lastDrawType!==rl&&(l=!0,r.lastDrawType=rl),function(t,e,r){var i=e.getDisplayables(),n=e.getTemporalDisplayables();t.save();var o,a,s={prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:r.viewWidth,viewHeight:r.viewHeight,inHover:r.inHover};for(o=e.getCursor(),a=i.length;o<a;o++){(u=i[o]).beforeBrush&&u.beforeBrush(),u.innerBeforeBrush(),al(t,u,s,o===a-1),u.innerAfterBrush(),u.afterBrush&&u.afterBrush(),s.prevEl=u}for(var h=0,l=n.length;h<l;h++){var u;(u=n[h]).beforeBrush&&u.beforeBrush(),u.innerBeforeBrush(),al(t,u,s,h===l-1),u.innerAfterBrush(),u.afterBrush&&u.afterBrush(),s.prevEl=u}e.clearTemporalDisplayables(),e.notClear=!0,t.restore()}(t,e,r)),p&&i&&il(t,r),e.innerAfterBrush(),e.afterBrush&&e.afterBrush(),r.prevEl=e,e.__dirty=0,e.__isRendered=!0}}var sl=1e-8;function hl(t,e){return Math.abs(t-e)<sl}function ll(t,e,r){var i=0,n=t[0];if(!n)return!1;for(var o=1;o<t.length;o++){var a=t[o];i+=ma(n[0],n[1],a[0],a[1],e,r),n=a}var s=t[0];return hl(n[0],s[0])&&hl(n[1],s[1])||(i+=ma(n[0],n[1],s[0],s[1],e,r)),0!==i}var ul=Math.sin,cl=Math.cos,fl=Math.PI,pl=2*Math.PI,dl=180/fl;const vl=function(){function t(){}return t.prototype.reset=function(t){this._start=!0,this._d=[],this._str="",this._p=Math.pow(10,t||4)},t.prototype.moveTo=function(t,e){this._add("M",t,e)},t.prototype.lineTo=function(t,e){this._add("L",t,e)},t.prototype.bezierCurveTo=function(t,e,r,i,n,o){this._add("C",t,e,r,i,n,o)},t.prototype.quadraticCurveTo=function(t,e,r,i){this._add("Q",t,e,r,i)},t.prototype.arc=function(t,e,r,i,n,o){this.ellipse(t,e,r,r,0,i,n,o)},t.prototype.ellipse=function(t,e,r,i,n,o,a,s){var h=a-o,l=!s,u=Math.abs(h),c=_i(u-pl)||(l?h>=pl:-h>=pl),f=h>0?h%pl:h%pl+pl,p=!1;p=!!c||!_i(u)&&f>=fl==!!l;var d=t+r*cl(o),v=e+i*ul(o);this._start&&this._add("M",d,v);var y=Math.round(n*dl);if(c){var g=1/this._p,_=(l?1:-1)*(pl-g);this._add("A",r,i,y,1,+l,t+r*cl(o+_),e+i*ul(o+_)),g>.01&&this._add("A",r,i,y,0,+l,d,v)}else{var m=t+r*cl(a),x=e+i*ul(a);this._add("A",r,i,y,+p,+l,m,x)}},t.prototype.rect=function(t,e,r,i){this._add("M",t,e),this._add("l",r,0),this._add("l",0,i),this._add("l",-r,0),this._add("Z")},t.prototype.closePath=function(){this._d.length>0&&this._add("Z")},t.prototype._add=function(t,e,r,i,n,o,a,s,h){for(var l=[],u=this._p,c=1;c<arguments.length;c++){var f=arguments[c];if(isNaN(f))return void(this._invalid=!0);l.push(Math.round(f*u)/u)}this._d.push(t+l.join(" ")),this._start="Z"===t},t.prototype.generateStr=function(){this._str=this._invalid?"":this._d.join(""),this._d=[]},t.prototype.getStr=function(){return this._str},t}();var yl="none",gl=Math.round;var _l=["lineCap","miterLimit","lineJoin"],ml=B(_l,(function(t){return"stroke-"+t.toLowerCase()}));function xl(t,e,r,i){var n=null==e.opacity?1:e.opacity;if(r instanceof Ha)t("opacity",n);else{if(function(t){var e=t.fill;return null!=e&&e!==yl}(e)){var o=yi(e.fill);t("fill",o.color);var a=null!=e.fillOpacity?e.fillOpacity*o.opacity*n:o.opacity*n;(i||a<1)&&t("fill-opacity",a)}else t("fill",yl);if(function(t){var e=t.stroke;return null!=e&&e!==yl}(e)){var s=yi(e.stroke);t("stroke",s.color);var h=e.strokeNoScale?r.getLineScale():1,l=h?(e.lineWidth||0)/h:0,u=null!=e.strokeOpacity?e.strokeOpacity*s.opacity*n:s.opacity*n,c=e.strokeFirst;if((i||1!==l)&&t("stroke-width",l),(i||c)&&t("paint-order",c?"stroke":"fill"),(i||u<1)&&t("stroke-opacity",u),e.lineDash){var f=Eh(r),p=f[0],d=f[1];p&&(d=gl(d||0),t("stroke-dasharray",p.join(",")),(d||i)&&t("stroke-dashoffset",d))}else i&&t("stroke-dasharray",yl);for(var v=0;v<_l.length;v++){var y=_l[v];if(i||e[y]!==Aa[y]){var g=e[y]||Aa[y];g&&t(ml[v],g)}}}else i&&t("stroke",yl)}}var wl="http://www.w3.org/2000/svg",bl="http://www.w3.org/1999/xlink",kl="http://www.w3.org/2000/xmlns/",Sl="http://www.w3.org/XML/1998/namespace",Tl="ecmeta_";function Cl(t){return document.createElementNS(wl,t)}function Pl(t,e,r,i,n){return{tag:t,attrs:r||{},children:i,text:n,key:e}}function Ml(t,e){var r=(e=e||{}).newline?"\n":"";return function t(e){var i=e.children,n=e.tag,o=e.attrs,a=e.text;return function(t,e){var r=[];if(e)for(var i in e){var n=e[i],o=i;!1!==n&&(!0!==n&&null!=n&&(o+='="'+n+'"'),r.push(o))}return"<"+t+" "+r.join(" ")+">"}(n,o)+("style"!==n?oe(a):a||"")+(i?""+r+B(i,(function(e){return t(e)})).join(r)+r:"")+("</"+n+">")}(t)}function Al(t){return{zrId:t,shadowCache:{},patternCache:{},gradientCache:{},clipPathCache:{},defs:{},cssNodes:{},cssAnims:{},cssStyleCache:{},cssAnimIdx:0,shadowIdx:0,gradientIdx:0,patternIdx:0,clipPathIdx:0}}function Ll(t,e,r,i){return Pl("svg","root",{width:t,height:e,xmlns:wl,"xmlns:xlink":bl,version:"1.1",baseProfile:"full",viewBox:!!i&&"0 0 "+t+" "+e},r)}var Dl=0;function zl(){return Dl++}var Ol={cubicIn:"0.32,0,0.67,0",cubicOut:"0.33,1,0.68,1",cubicInOut:"0.65,0,0.35,1",quadraticIn:"0.11,0,0.5,0",quadraticOut:"0.5,1,0.89,1",quadraticInOut:"0.45,0,0.55,1",quarticIn:"0.5,0,0.75,0",quarticOut:"0.25,1,0.5,1",quarticInOut:"0.76,0,0.24,1",quinticIn:"0.64,0,0.78,0",quinticOut:"0.22,1,0.36,1",quinticInOut:"0.83,0,0.17,1",sinusoidalIn:"0.12,0,0.39,0",sinusoidalOut:"0.61,1,0.88,1",sinusoidalInOut:"0.37,0,0.63,1",exponentialIn:"0.7,0,0.84,0",exponentialOut:"0.16,1,0.3,1",exponentialInOut:"0.87,0,0.13,1",circularIn:"0.55,0,1,0.45",circularOut:"0,0.55,0.45,1",circularInOut:"0.85,0,0.15,1"},Il="transform-origin";function Rl(t,e,r){var i=A({},t.shape);A(i,e),t.buildPath(r,i);var n=new vl;return n.reset(Mi(t)),r.rebuildPath(n,1),n.generateStr(),n.getStr()}function Fl(t,e){var r=e.originX,i=e.originY;(r||i)&&(t[Il]=r+"px "+i+"px")}var Bl={fill:"fill",opacity:"opacity",lineWidth:"stroke-width",lineDashOffset:"stroke-dashoffset"};function Nl(t,e){var r=e.zrId+"-ani-"+e.cssAnimIdx++;return e.cssAnims[r]=t,r}function Hl(t){return V(t)?Ol[t]?"cubic-bezier("+Ol[t]+")":Nr(t)?t:"":""}function El(t,e,r,i){var n=t.animators,o=n.length,a=[];if(t instanceof bh){var s=function(t,e,r){var i,n,o=t.shape.paths,a={};if(F(o,(function(t){var e=Al(r.zrId);e.animation=!0,El(t,{},e,!0);var o=e.cssAnims,s=e.cssNodes,h=W(o),l=h.length;if(l){var u=o[n=h[l-1]];for(var c in u){var f=u[c];a[c]=a[c]||{d:""},a[c].d+=f.d||""}for(var p in s){var d=s[p].animation;d.indexOf(n)>=0&&(i=d)}}})),i){e.d=!1;var s=Nl(a,r);return i.replace(n,s)}}(t,e,r);if(s)a.push(s);else if(!o)return}else if(!o)return;for(var h={},l=0;l<o;l++){var u=n[l],c=[u.getMaxTime()/1e3+"s"],f=Hl(u.getClip().easing),p=u.getDelay();f?c.push(f):c.push("linear"),p&&c.push(p/1e3+"s"),u.getLoop()&&c.push("infinite");var d=c.join(" ");h[d]=h[d]||[d,[]],h[d][1].push(u)}function v(n){var o,a=n[1],s=a.length,h={},l={},u={},c="animation-timing-function";function f(t,e,r){for(var i=t.getTracks(),n=t.getMaxTime(),o=0;o<i.length;o++){var a=i[o];if(a.needsAnimate()){var s=a.keyframes,h=a.propName;if(r&&(h=r(h)),h)for(var l=0;l<s.length;l++){var u=s[l],f=Math.round(u.time/n*100)+"%",p=Hl(u.easing),d=u.rawValue;(V(d)||Z(d))&&(e[f]=e[f]||{},e[f][h]=u.rawValue,p&&(e[f][c]=p))}}}}for(var p=0;p<s;p++){(k=(b=a[p]).targetName)?"shape"===k&&f(b,l):!i&&f(b,h)}for(var d in h){var v={};Cn(v,t),A(v,h[d]);var y=Ai(v),g=h[d][c];u[d]=y?{transform:y}:{},Fl(u[d],v),g&&(u[d][c]=g)}var _=!0;for(var d in l){u[d]=u[d]||{};var m=!o;g=l[d][c];m&&(o=new ca);var x=o.len();o.reset(),u[d].d=Rl(t,l[d],o);var w=o.len();if(!m&&x!==w){_=!1;break}g&&(u[d][c]=g)}if(!_)for(var d in u)delete u[d].d;if(!i)for(p=0;p<s;p++){var b,k;"style"===(k=(b=a[p]).targetName)&&f(b,u,(function(t){return Bl[t]}))}var S,T=W(u),C=!0;for(p=1;p<T.length;p++){var P=T[p-1],M=T[p];if(u[P][Il]!==u[M][Il]){C=!1;break}S=u[P][Il]}if(C&&S){for(var d in u)u[d][Il]&&delete u[d][Il];e[Il]=S}if(H(T,(function(t){return W(u[t]).length>0})).length)return Nl(u,r)+" "+n[0]+" both"}for(var y in h){(s=v(h[y]))&&a.push(s)}if(a.length){var g=r.zrId+"-cls-"+zl();r.cssNodes["."+g]={animation:a.join(",")},e.class=g}}function Wl(t,e,r,i){var n=JSON.stringify(t),o=r.cssStyleCache[n];o||(o=r.zrId+"-cls-"+zl(),r.cssStyleCache[n]=o,r.cssNodes["."+o+(i?":hover":"")]=t),e.class=e.class?e.class+" "+o:o}var Xl=Math.round;function jl(t){return t&&V(t.src)}function ql(t){return t&&Y(t.toDataURL)}function Yl(t,e,r,i){xl((function(n,o){var a="fill"===n||"stroke"===n;a&&Ci(o)?ru(e,t,n,i):a&&ki(o)?iu(r,t,n,i):t[n]=a&&"none"===o?"transparent":o}),e,r,!1),function(t,e,r){var i=t.style;if(function(t){return t&&(t.shadowBlur||t.shadowOffsetX||t.shadowOffsetY)}(i)){var n=function(t){var e=t.style,r=t.getGlobalScale();return[e.shadowColor,(e.shadowBlur||0).toFixed(2),(e.shadowOffsetX||0).toFixed(2),(e.shadowOffsetY||0).toFixed(2),r[0],r[1]].join(",")}(t),o=r.shadowCache,a=o[n];if(!a){var s=t.getGlobalScale(),h=s[0],l=s[1];if(!h||!l)return;var u=i.shadowOffsetX||0,c=i.shadowOffsetY||0,f=i.shadowBlur,p=yi(i.shadowColor),d=p.opacity,v=p.color,y=f/2/h+" "+f/2/l;a=r.zrId+"-s"+r.shadowIdx++,r.defs[a]=Pl("filter",a,{id:a,x:"-100%",y:"-100%",width:"300%",height:"300%"},[Pl("feDropShadow","",{dx:u/h,dy:c/l,stdDeviation:y,"flood-color":v,"flood-opacity":d})]),o[n]=a}e.filter=Pi(a)}}(r,t,i)}function Vl(t,e){var r=ro(e);r&&(r.each((function(e,r){null!=e&&(t[(Tl+r).toLowerCase()]=e+"")})),e.isSilent()&&(t[Tl+"silent"]="true"))}function Ul(t){return _i(t[0]-1)&&_i(t[1])&&_i(t[2])&&_i(t[3]-1)}function Zl(t,e,r){if(e&&(!function(t){return _i(t[4])&&_i(t[5])}(e)||!Ul(e))){var i=r?10:1e4;t.transform=Ul(e)?"translate("+Xl(e[4]*i)/i+" "+Xl(e[5]*i)/i+")":function(t){return"matrix("+mi(t[0])+","+mi(t[1])+","+mi(t[2])+","+mi(t[3])+","+xi(t[4])+","+xi(t[5])+")"}(e)}}function Gl(t,e,r){for(var i=t.points,n=[],o=0;o<i.length;o++)n.push(Xl(i[o][0]*r)/r),n.push(Xl(i[o][1]*r)/r);e.points=n.join(" ")}function Kl(t){return!t.smooth}var Ql,$l,Jl={circle:[(Ql=["cx","cy","r"],$l=B(Ql,(function(t){return"string"==typeof t?[t,t]:t})),function(t,e,r){for(var i=0;i<$l.length;i++){var n=$l[i],o=t[n[0]];null!=o&&(e[n[1]]=Xl(o*r)/r)}})],polyline:[Gl,Kl],polygon:[Gl,Kl]};function tu(t,e){var r=t.style,i=t.shape,n=Jl[t.type],o={},a=e.animation,s="path",h=t.style.strokePercent,l=e.compress&&Mi(t)||4;if(!n||e.willUpdate||n[1]&&!n[1](i)||a&&function(t){for(var e=t.animators,r=0;r<e.length;r++)if("shape"===e[r].targetName)return!0;return!1}(t)||h<1){var u=!t.path||t.shapeChanged();t.path||t.createPathProxy();var c=t.path;u&&(c.beginPath(),t.buildPath(c,t.shape),t.pathUpdated());var f=c.getVersion(),p=t,d=p.__svgPathBuilder;p.__svgPathVersion===f&&d&&h===p.__svgPathStrokePercent||(d||(d=p.__svgPathBuilder=new vl),d.reset(l),c.rebuildPath(d,h),d.generateStr(),p.__svgPathVersion=f,p.__svgPathStrokePercent=h),o.d=d.getStr()}else{s=t.type;var v=Math.pow(10,l);n[0](i,o,v)}return Zl(o,t.transform),Yl(o,r,t,e),Vl(o,t),e.animation&&El(t,o,e),e.emphasis&&function(t,e,r){if(!t.ignore)if(t.isSilent())Wl(l={"pointer-events":"none"},e,r,!0);else{var i=t.states.emphasis&&t.states.emphasis.style?t.states.emphasis.style:{},n=i.fill;if(!n){var o=t.style&&t.style.fill,a=t.states.select&&t.states.select.style&&t.states.select.style.fill,s=t.currentStates.indexOf("select")>=0&&a||o;s&&(n=pi(s))}var h=i.lineWidth;h&&(h/=!i.strokeNoScale&&t.transform?t.transform[0]:1);var l={cursor:"pointer"};n&&(l.fill=n),i.stroke&&(l.stroke=i.stroke),h&&(l["stroke-width"]=h),Wl(l,e,r,!0)}}(t,o,e),Pl(s,t.id+"",o)}function eu(t,e){return t instanceof za?tu(t,e):t instanceof Ha?function(t,e){var r=t.style,i=r.image;if(i&&!V(i)&&(jl(i)?i=i.src:ql(i)&&(i=i.toDataURL())),i){var n=r.x||0,o=r.y||0,a={href:i,width:r.width,height:r.height};return n&&(a.x=n),o&&(a.y=o),Zl(a,t.transform),Yl(a,r,t,e),Vl(a,t),e.animation&&El(t,a,e),Pl("image",t.id+"",a)}}(t,e):t instanceof Ra?function(t,e){var r=t.style,i=r.text;if(null!=i&&(i+=""),i&&!isNaN(r.x)&&!isNaN(r.y)){var n=r.font||a,s=r.x||0,h=function(t,e,r){return"top"===r?t+=e/2:"bottom"===r&&(t-=e/2),t}(r.y||0,In(n),r.textBaseline),l={"dominant-baseline":"central","text-anchor":wi[r.textAlign]||r.textAlign};if(rs(r)){var u="",c=r.fontStyle,f=ts(r.fontSize);if(!parseFloat(f))return;var p=r.fontFamily||o,d=r.fontWeight;u+="font-size:"+f+";font-family:"+p+";",c&&"normal"!==c&&(u+="font-style:"+c+";"),d&&"normal"!==d&&(u+="font-weight:"+d+";"),l.style=u}else l.style="font: "+n;return i.match(/\s/)&&(l["xml:space"]="preserve"),s&&(l.x=s),h&&(l.y=h),Zl(l,t.transform),Yl(l,r,t,e),Vl(l,t),e.animation&&El(t,l,e),Pl("text",t.id+"",l,void 0,i)}}(t,e):void 0}function ru(t,e,r,i){var n,o=t[r],a={gradientUnits:o.global?"userSpaceOnUse":"objectBoundingBox"};if(Si(o))n="linearGradient",a.x1=o.x,a.y1=o.y,a.x2=o.x2,a.y2=o.y2;else{if(!Ti(o))return;n="radialGradient",a.cx=nt(o.x,.5),a.cy=nt(o.y,.5),a.r=nt(o.r,.5)}for(var s=o.colorStops,h=[],l=0,u=s.length;l<u;++l){var c=100*xi(s[l].offset)+"%",f=yi(s[l].color),p=f.color,d=f.opacity,v={offset:c};v["stop-color"]=p,d<1&&(v["stop-opacity"]=d),h.push(Pl("stop",l+"",v))}var y=Ml(Pl(n,"",a,h)),g=i.gradientCache,_=g[y];_||(_=i.zrId+"-g"+i.gradientIdx++,g[y]=_,a.id=_,i.defs[_]=Pl(n,_,a,h)),e[r]=Pi(_)}function iu(t,e,r,i){var n,o=t.style[r],a=t.getBoundingRect(),s={},h=o.repeat,l="no-repeat"===h,u="repeat-x"===h,c="repeat-y"===h;if(bi(o)){var f=o.imageWidth,p=o.imageHeight,d=void 0,v=o.image;if(V(v)?d=v:jl(v)?d=v.src:ql(v)&&(d=v.toDataURL()),"undefined"==typeof Image){var y="Image width/height must been given explictly in svg-ssr renderer.";ht(f,y),ht(p,y)}else if(null==f||null==p){var g=function(t,e){if(t){var r=t.elm,i=f||e.width,n=p||e.height;"pattern"===t.tag&&(u?(n=1,i/=a.width):c&&(i=1,n/=a.height)),t.attrs.width=i,t.attrs.height=n,r&&(r.setAttribute("width",i),r.setAttribute("height",n))}},_=so(d,null,t,(function(t){l||g(b,t),g(n,t)}));_&&_.width&&_.height&&(f=f||_.width,p=p||_.height)}n=Pl("image","img",{href:d,width:f,height:p}),s.width=f,s.height=p}else o.svgElement&&(n=C(o.svgElement),s.width=o.svgWidth,s.height=o.svgHeight);if(n){var m,x;l?m=x=1:u?(x=1,m=s.width/a.width):c?(m=1,x=s.height/a.height):s.patternUnits="userSpaceOnUse",null==m||isNaN(m)||(s.width=m),null==x||isNaN(x)||(s.height=x);var w=Ai(o);w&&(s.patternTransform=w);var b=Pl("pattern","",s,[n]),k=Ml(b),S=i.patternCache,T=S[k];T||(T=i.zrId+"-p"+i.patternIdx++,S[k]=T,s.id=T,b=i.defs[T]=Pl("pattern",T,s,[n])),e[r]=Pi(T)}}function nu(t,e,r){var i=r.clipPathCache,n=r.defs,o=i[t.id];if(!o){var a={id:o=r.zrId+"-c"+r.clipPathIdx++};i[t.id]=o,n[o]=Pl("clipPath",o,a,[tu(t,r)])}e["clip-path"]=Pi(o)}function ou(t){return document.createTextNode(t)}function au(t,e,r){t.insertBefore(e,r)}function su(t,e){t.removeChild(e)}function hu(t,e){t.appendChild(e)}function lu(t){return t.parentNode}function uu(t){return t.nextSibling}function cu(t,e){t.textContent=e}var fu=58,pu=120,du=Pl("","");function vu(t){return void 0===t}function yu(t){return void 0!==t}function gu(t,e,r){for(var i={},n=e;n<=r;++n){var o=t[n].key;void 0!==o&&(i[o]=n)}return i}function _u(t,e){var r=t.key===e.key;return t.tag===e.tag&&r}function mu(t){var e,r=t.children,i=t.tag;if(yu(i)){var n=t.elm=Cl(i);if(bu(du,t),q(r))for(e=0;e<r.length;++e){var o=r[e];null!=o&&hu(n,mu(o))}else yu(t.text)&&!G(t.text)&&hu(n,ou(t.text))}else t.elm=ou(t.text);return t.elm}function xu(t,e,r,i,n){for(;i<=n;++i){var o=r[i];null!=o&&au(t,mu(o),e)}}function wu(t,e,r,i){for(;r<=i;++r){var n=e[r];if(null!=n)if(yu(n.tag))su(lu(n.elm),n.elm);else su(t,n.elm)}}function bu(t,e){var r,i=e.elm,n=t&&t.attrs||{},o=e.attrs||{};if(n!==o){for(r in o){var a=o[r];n[r]!==a&&(!0===a?i.setAttribute(r,""):!1===a?i.removeAttribute(r):"style"===r?i.style.cssText=a:r.charCodeAt(0)!==pu?i.setAttribute(r,a):"xmlns:xlink"===r||"xmlns"===r?i.setAttributeNS(kl,r,a):r.charCodeAt(3)===fu?i.setAttributeNS(Sl,r,a):r.charCodeAt(5)===fu?i.setAttributeNS(bl,r,a):i.setAttribute(r,a))}for(r in n)r in o||i.removeAttribute(r)}}function ku(t,e){var r=e.elm=t.elm,i=t.children,n=e.children;t!==e&&(bu(t,e),vu(e.text)?yu(i)&&yu(n)?i!==n&&function(t,e,r){for(var i,n,o,a=0,s=0,h=e.length-1,l=e[0],u=e[h],c=r.length-1,f=r[0],p=r[c];a<=h&&s<=c;)null==l?l=e[++a]:null==u?u=e[--h]:null==f?f=r[++s]:null==p?p=r[--c]:_u(l,f)?(ku(l,f),l=e[++a],f=r[++s]):_u(u,p)?(ku(u,p),u=e[--h],p=r[--c]):_u(l,p)?(ku(l,p),au(t,l.elm,uu(u.elm)),l=e[++a],p=r[--c]):_u(u,f)?(ku(u,f),au(t,u.elm,l.elm),u=e[--h],f=r[++s]):(vu(i)&&(i=gu(e,a,h)),vu(n=i[f.key])||(o=e[n]).tag!==f.tag?au(t,mu(f),l.elm):(ku(o,f),e[n]=void 0,au(t,o.elm,l.elm)),f=r[++s]);(a<=h||s<=c)&&(a>h?xu(t,null==r[c+1]?null:r[c+1].elm,r,s,c):wu(t,e,a,h))}(r,i,n):yu(n)?(yu(t.text)&&cu(r,""),xu(r,null,n,0,n.length-1)):yu(i)?wu(r,i,0,i.length-1):yu(t.text)&&cu(r,""):t.text!==e.text&&(yu(i)&&wu(r,i,0,i.length-1),cu(r,e.text)))}var Su=0;const Tu=function(){function t(t,e,r){if(this.type="svg",this.refreshHover=function(){},this.configLayer=function(){},this.storage=e,this._opts=r=A({},r),this.root=t,this._id="zr"+Su++,this._oldVNode=Ll(r.width,r.height),t&&!r.ssr){var i=this._viewport=document.createElement("div");i.style.cssText="position:relative;overflow:hidden";var n=this._svgDom=this._oldVNode.elm=Cl("svg");bu(null,this._oldVNode),i.appendChild(n),t.appendChild(i)}this.resize(r.width,r.height)}return t.prototype.getType=function(){return this.type},t.prototype.getViewportRoot=function(){return this._viewport},t.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},t.prototype.getSvgDom=function(){return this._svgDom},t.prototype.refresh=function(){if(this.root){var t=this.renderToVNode({willUpdate:!0});t.attrs.style="position:absolute;left:0;top:0;user-select:none",function(t,e){if(_u(t,e))ku(t,e);else{var r=t.elm,i=lu(r);mu(e),null!==i&&(au(i,e.elm,uu(r)),wu(i,[t],0,0))}}(this._oldVNode,t),this._oldVNode=t}},t.prototype.renderOneToVNode=function(t){return eu(t,Al(this._id))},t.prototype.renderToVNode=function(t){t=t||{};var e=this.storage.getDisplayList(!0),r=this._width,i=this._height,n=Al(this._id);n.animation=t.animation,n.willUpdate=t.willUpdate,n.compress=t.compress,n.emphasis=t.emphasis;var o=[],a=this._bgVNode=function(t,e,r,i){var n;if(r&&"none"!==r)if(n=Pl("rect","bg",{width:t,height:e,x:"0",y:"0"}),Ci(r))ru({fill:r},n.attrs,"fill",i);else if(ki(r))iu({style:{fill:r},dirty:wt,getBoundingRect:function(){return{width:t,height:e}}},n.attrs,"fill",i);else{var o=yi(r),a=o.color,s=o.opacity;n.attrs.fill=a,s<1&&(n.attrs["fill-opacity"]=s)}return n}(r,i,this._backgroundColor,n);a&&o.push(a);var s=t.compress?null:this._mainVNode=Pl("g","main",{},[]);this._paintList(e,n,s?s.children:o),s&&o.push(s);var h=B(W(n.defs),(function(t){return n.defs[t]}));if(h.length&&o.push(Pl("defs","defs",{},h)),t.animation){var l=function(t,e,r){var i=(r=r||{}).newline?"\n":"",n=" {"+i,o=i+"}",a=B(W(t),(function(e){return e+n+B(W(t[e]),(function(r){return r+":"+t[e][r]+";"})).join(i)+o})).join(i),s=B(W(e),(function(t){return"@keyframes "+t+n+B(W(e[t]),(function(r){return r+n+B(W(e[t][r]),(function(i){var n=e[t][r][i];return"d"===i&&(n='path("'+n+'")'),i+":"+n+";"})).join(i)+o})).join(i)+o})).join(i);return a||s?["<![CDATA[",a,s,"]]>"].join(i):""}(n.cssNodes,n.cssAnims,{newline:!0});if(l){var u=Pl("style","stl",{},[],l);o.push(u)}}return Ll(r,i,o,t.useViewBox)},t.prototype.renderToString=function(t){return t=t||{},Ml(this.renderToVNode({animation:nt(t.cssAnimation,!0),emphasis:nt(t.cssEmphasis,!0),willUpdate:!1,compress:!0,useViewBox:nt(t.useViewBox,!0)}),{newline:!0})},t.prototype.setBackgroundColor=function(t){this._backgroundColor=t},t.prototype.getSvgRoot=function(){return this._mainVNode&&this._mainVNode.elm},t.prototype._paintList=function(t,e,r){for(var i,n,o=t.length,a=[],s=0,h=0,l=0;l<o;l++){var u=t[l];if(!u.invisible){var c=u.__clipPaths,f=c&&c.length||0,p=n&&n.length||0,d=void 0;for(d=Math.max(f-1,p-1);d>=0&&(!c||!n||c[d]!==n[d]);d--);for(var v=p-1;v>d;v--)i=a[--s-1];for(var y=d+1;y<f;y++){var g={};nu(c[y],g,e);var _=Pl("g","clip-g-"+h++,g,[]);(i?i.children:r).push(_),a[s++]=_,i=_}n=c;var m=eu(u,e);m&&(i?i.children:r).push(m)}}},t.prototype.resize=function(t,e){var r=this._opts,i=this.root,n=this._viewport;if(null!=t&&(r.width=t),null!=e&&(r.height=e),i&&n&&(n.style.display="none",t=Hh(i,0,r),e=Hh(i,1,r),n.style.display=""),this._width!==t||this._height!==e){if(this._width=t,this._height=e,n){var o=n.style;o.width=t+"px",o.height=e+"px"}if(ki(this._backgroundColor))this.refresh();else{var a=this._svgDom;a&&(a.setAttribute("width",t),a.setAttribute("height",e));var s=this._bgVNode&&this._bgVNode.elm;s&&(s.setAttribute("width",t),s.setAttribute("height",e))}}},t.prototype.getWidth=function(){return this._width},t.prototype.getHeight=function(){return this._height},t.prototype.dispose=function(){this.root&&(this.root.innerHTML=""),this._svgDom=this._viewport=this.storage=this._oldVNode=this._bgVNode=this._mainVNode=null},t.prototype.clear=function(){this._svgDom&&(this._svgDom.innerHTML=null),this._oldVNode=null},t.prototype.toDataURL=function(t){var e=this.renderToString(),r="data:image/svg+xml;";return t?(e=Li(e))&&r+"base64,"+e:r+"charset=UTF-8,"+encodeURIComponent(e)},t}();function Cu(t,e,r){var i=u.createCanvas(),n=e.getWidth(),o=e.getHeight(),a=i.style;return a&&(a.position="absolute",a.left="0",a.top="0",a.width=n+"px",a.height=o+"px",i.setAttribute("data-zr-dom-id",t)),i.width=n*r,i.height=o*r,i}const Pu=function(e){function r(t,r,i){var n,o=e.call(this)||this;o.motionBlur=!1,o.lastFrameAlpha=.7,o.dpr=1,o.virtual=!1,o.config={},o.incremental=!1,o.zlevel=0,o.maxRepaintRectCount=5,o.__dirty=!0,o.__firstTimePaint=!0,o.__used=!1,o.__drawIndex=0,o.__startIndex=0,o.__endIndex=0,o.__prevStartIndex=null,o.__prevEndIndex=null,i=i||dn,"string"==typeof t?n=Cu(t,r,i):G(t)&&(t=(n=t).id),o.id=t,o.dom=n;var a=n.style;return a&&(mt(n),n.onselectstart=function(){return!1},a.padding="0",a.margin="0",a.borderWidth="0"),o.painter=r,o.dpr=i,o}return t(r,e),r.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},r.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},r.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},r.prototype.setUnpainted=function(){this.__firstTimePaint=!0},r.prototype.createBackBuffer=function(){var t=this.dpr;this.domBack=Cu("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},r.prototype.createRepaintRects=function(t,e,r,i){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var n,o=[],a=this.maxRepaintRectCount,s=!1,h=new Ne(0,0,0,0);function l(t){if(t.isFinite()&&!t.isZero())if(0===o.length){(e=new Ne(0,0,0,0)).copy(t),o.push(e)}else{for(var e,r=!1,i=1/0,n=0,l=0;l<o.length;++l){var u=o[l];if(u.intersect(t)){var c=new Ne(0,0,0,0);c.copy(u),c.union(t),o[l]=c,r=!0;break}if(s){h.copy(t),h.union(u);var f=t.width*t.height,p=u.width*u.height,d=h.width*h.height-f-p;d<i&&(i=d,n=l)}}if(s&&(o[n].union(t),r=!0),!r)(e=new Ne(0,0,0,0)).copy(t),o.push(e);s||(s=o.length>=a)}}for(var u=this.__startIndex;u<this.__endIndex;++u){if(p=t[u]){var c=p.shouldBePainted(r,i,!0,!0);(d=p.__isRendered&&(p.__dirty&nr||!c)?p.getPrevPaintRect():null)&&l(d);var f=c&&(p.__dirty&nr||!p.__isRendered)?p.getPaintRect():null;f&&l(f)}}for(u=this.__prevStartIndex;u<this.__prevEndIndex;++u){var p,d;c=(p=e[u])&&p.shouldBePainted(r,i,!0,!0);if(p&&(!c||!p.__zr)&&p.__isRendered)(d=p.getPrevPaintRect())&&l(d)}do{n=!1;for(u=0;u<o.length;)if(o[u].isZero())o.splice(u,1);else{for(var v=u+1;v<o.length;)o[u].intersect(o[v])?(n=!0,o[u].union(o[v]),o.splice(v,1)):v++;u++}}while(n);return this._paintRects=o,o},r.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},r.prototype.resize=function(t,e){var r=this.dpr,i=this.dom,n=i.style,o=this.domBack;n&&(n.width=t+"px",n.height=e+"px"),i.width=t*r,i.height=e*r,o&&(o.width=t*r,o.height=e*r,1!==r&&this.ctxBack.scale(r,r))},r.prototype.clear=function(t,e,r){var i=this.dom,n=this.ctx,o=i.width,a=i.height;e=e||this.clearColor;var s=this.motionBlur&&!t,h=this.lastFrameAlpha,l=this.dpr,u=this;s&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(i,0,0,o/l,a/l));var c=this.domBack;function f(t,r,i,o){if(n.clearRect(t,r,i,o),e&&"transparent"!==e){var a=void 0;if(J(e))a=(e.global||e.__width===i&&e.__height===o)&&e.__canvasGradient||Bh(n,e,{x:0,y:0,width:i,height:o}),e.__canvasGradient=a,e.__width=i,e.__height=o;else tt(e)&&(e.scaleX=e.scaleX||l,e.scaleY=e.scaleY||l,a=Uh(n,e,{dirty:function(){u.setUnpainted(),u.painter.refresh()}}));n.save(),n.fillStyle=a||e,n.fillRect(t,r,i,o),n.restore()}s&&(n.save(),n.globalAlpha=h,n.drawImage(c,t,r,i,o),n.restore())}!r||s?f(0,0,o,a):r.length&&F(r,(function(t){f(t.x*l,t.y*l,t.width*l,t.height*l)}))},r}(Zt);var Mu=1e5,Au=314159,Lu=.01;const Du=function(){function t(t,e,r,i){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var n=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=r=A({},r||{}),this.dpr=r.devicePixelRatio||dn,this._singleCanvas=n,this.root=t,t.style&&(mt(t),t.innerHTML=""),this.storage=e;var o=this._zlevelList;this._prevDisplayList=[];var a=this._layers;if(n){var s=t,h=s.width,l=s.height;null!=r.width&&(h=r.width),null!=r.height&&(l=r.height),this.dpr=r.devicePixelRatio||1,s.width=h*this.dpr,s.height=l*this.dpr,this._width=h,this._height=l;var u=new Pu(s,this,this.dpr);u.__builtin__=!0,u.initContext(),a[314159]=u,u.zlevel=Au,o.push(Au),this._domRoot=t}else{this._width=Hh(t,0,r),this._height=Hh(t,1,r);var c=this._domRoot=function(t,e){var r=document.createElement("div");return r.style.cssText=["position:relative","width:"+t+"px","height:"+e+"px","padding:0","margin:0","border-width:0"].join(";")+";",r}(this._width,this._height);t.appendChild(c)}}return t.prototype.getType=function(){return"canvas"},t.prototype.isSingleCanvas=function(){return this._singleCanvas},t.prototype.getViewportRoot=function(){return this._domRoot},t.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},t.prototype.refresh=function(t){var e=this.storage.getDisplayList(!0),r=this._prevDisplayList,i=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,r,t,this._redrawId);for(var n=0;n<i.length;n++){var o=i[n],a=this._layers[o];if(!a.__builtin__&&a.refresh){var s=0===n?this._backgroundColor:null;a.refresh(s)}}return this._opts.useDirtyRect&&(this._prevDisplayList=e.slice()),this},t.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},t.prototype._paintHoverList=function(t){var e=t.length,r=this._hoverlayer;if(r&&r.clear(),e){for(var i,n={inHover:!0,viewWidth:this._width,viewHeight:this._height},o=0;o<e;o++){var a=t[o];a.__inHover&&(r||(r=this._hoverlayer=this.getLayer(Mu)),i||(i=r.ctx).save(),al(i,a,n,o===e-1))}i&&i.restore()}},t.prototype.getHoverLayer=function(){return this.getLayer(Mu)},t.prototype.paintOne=function(t,e){ol(t,e)},t.prototype._paintList=function(t,e,r,i){if(this._redrawId===i){r=r||!1,this._updateLayerStatus(t);var n=this._doPaintList(t,e,r),o=n.finished,a=n.needsRefreshHover;if(this._needsManuallyCompositing&&this._compositeManually(),a&&this._paintHoverList(t),o)this.eachLayer((function(t){t.afterBrush&&t.afterBrush()}));else{var s=this;ur((function(){s._paintList(t,e,r,i)}))}}},t.prototype._compositeManually=function(){var t=this.getLayer(Au).ctx,e=this._domRoot.width,r=this._domRoot.height;t.clearRect(0,0,e,r),this.eachBuiltinLayer((function(i){i.virtual&&t.drawImage(i.dom,0,0,e,r)}))},t.prototype._doPaintList=function(t,e,r){for(var n=this,o=[],a=this._opts.useDirtyRect,s=0;s<this._zlevelList.length;s++){var h=this._zlevelList[s],l=this._layers[h];l.__builtin__&&l!==this._hoverlayer&&(l.__dirty||r)&&o.push(l)}for(var u=!0,c=!1,f=function(i){var s,h=o[i],l=h.ctx,f=a&&h.createRepaintRects(t,e,p._width,p._height),d=r?h.__startIndex:h.__drawIndex,v=!r&&h.incremental&&Date.now,y=v&&Date.now(),g=h.zlevel===p._zlevelList[0]?p._backgroundColor:null;if(h.__startIndex===h.__endIndex)h.clear(!1,g,f);else if(d===h.__startIndex){var _=t[d];_.incremental&&_.notClear&&!r||h.clear(!1,g,f)}-1===d&&(d=h.__startIndex);var m=function(e){var r={inHover:!1,allClipped:!1,prevEl:null,viewWidth:n._width,viewHeight:n._height};for(s=d;s<h.__endIndex;s++){var i=t[s];if(i.__inHover&&(c=!0),n._doPaintEl(i,h,a,e,r,s===h.__endIndex-1),v)if(Date.now()-y>15)break}r.prevElClipPaths&&l.restore()};if(f)if(0===f.length)s=h.__endIndex;else for(var x=p.dpr,w=0;w<f.length;++w){var b=f[w];l.save(),l.beginPath(),l.rect(b.x*x,b.y*x,b.width*x,b.height*x),l.clip(),m(b),l.restore()}else l.save(),m(),l.restore();h.__drawIndex=s,h.__drawIndex<h.__endIndex&&(u=!1)},p=this,d=0;d<o.length;d++)f(d);return i.wxa&&F(this._layers,(function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()})),{finished:u,needsRefreshHover:c}},t.prototype._doPaintEl=function(t,e,r,i,n,o){var a=e.ctx;if(r){var s=t.getPaintRect();(!i||s&&s.intersect(i))&&(al(a,t,n,o),t.setPrevPaintRect(s))}else al(a,t,n,o)},t.prototype.getLayer=function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=Au);var r=this._layers[t];return r||((r=new Pu("zr_"+t,this,this.dpr)).zlevel=t,r.__builtin__=!0,this._layerConfig[t]?P(r,this._layerConfig[t],!0):this._layerConfig[t-Lu]&&P(r,this._layerConfig[t-Lu],!0),e&&(r.virtual=e),this.insertLayer(t,r),r.initContext()),r},t.prototype.insertLayer=function(t,e){var r=this._layers,i=this._zlevelList,n=i.length,o=this._domRoot,a=null,s=-1;if(!r[t]&&function(t){return!!t&&(!!t.__builtin__||"function"==typeof t.resize&&"function"==typeof t.refresh)}(e)){if(n>0&&t>i[0]){for(s=0;s<n-1&&!(i[s]<t&&i[s+1]>t);s++);a=r[i[s]]}if(i.splice(s+1,0,t),r[t]=e,!e.virtual)if(a){var h=a.dom;h.nextSibling?o.insertBefore(e.dom,h.nextSibling):o.appendChild(e.dom)}else o.firstChild?o.insertBefore(e.dom,o.firstChild):o.appendChild(e.dom);e.painter||(e.painter=this)}},t.prototype.eachLayer=function(t,e){for(var r=this._zlevelList,i=0;i<r.length;i++){var n=r[i];t.call(e,this._layers[n],n)}},t.prototype.eachBuiltinLayer=function(t,e){for(var r=this._zlevelList,i=0;i<r.length;i++){var n=r[i],o=this._layers[n];o.__builtin__&&t.call(e,o,n)}},t.prototype.eachOtherLayer=function(t,e){for(var r=this._zlevelList,i=0;i<r.length;i++){var n=r[i],o=this._layers[n];o.__builtin__||t.call(e,o,n)}},t.prototype.getLayers=function(){return this._layers},t.prototype._updateLayerStatus=function(t){function e(t){o&&(o.__endIndex!==t&&(o.__dirty=!0),o.__endIndex=t)}if(this.eachBuiltinLayer((function(t,e){t.__dirty=t.__used=!1})),this._singleCanvas)for(var r=1;r<t.length;r++){if((s=t[r]).zlevel!==t[r-1].zlevel||s.incremental){this._needsManuallyCompositing=!0;break}}var i,n,o=null,a=0;for(n=0;n<t.length;n++){var s,h=(s=t[n]).zlevel,l=void 0;i!==h&&(i=h,a=0),s.incremental?((l=this.getLayer(h+.001,this._needsManuallyCompositing)).incremental=!0,a=1):l=this.getLayer(h+(a>0?Lu:0),this._needsManuallyCompositing),l.__builtin__||T("ZLevel "+h+" has been used by unkown layer "+l.id),l!==o&&(l.__used=!0,l.__startIndex!==n&&(l.__dirty=!0),l.__startIndex=n,l.incremental?l.__drawIndex=-1:l.__drawIndex=n,e(n),o=l),s.__dirty&nr&&!s.__inHover&&(l.__dirty=!0,l.incremental&&l.__drawIndex<0&&(l.__drawIndex=n))}e(n),this.eachBuiltinLayer((function(t,e){!t.__used&&t.getElementCount()>0&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)}))},t.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},t.prototype._clearLayer=function(t){t.clear()},t.prototype.setBackgroundColor=function(t){this._backgroundColor=t,F(this._layers,(function(t){t.setUnpainted()}))},t.prototype.configLayer=function(t,e){if(e){var r=this._layerConfig;r[t]?P(r[t],e,!0):r[t]=e;for(var i=0;i<this._zlevelList.length;i++){var n=this._zlevelList[i];if(n===t||n===t+Lu)P(this._layers[n],r[t],!0)}}},t.prototype.delLayer=function(t){var e=this._layers,r=this._zlevelList,i=e[t];i&&(i.dom.parentNode.removeChild(i.dom),delete e[t],r.splice(z(r,t),1))},t.prototype.resize=function(t,e){if(this._domRoot.style){var r=this._domRoot;r.style.display="none";var i=this._opts,n=this.root;if(null!=t&&(i.width=t),null!=e&&(i.height=e),t=Hh(n,0,i),e=Hh(n,1,i),r.style.display="",this._width!==t||e!==this._height){for(var o in r.style.width=t+"px",r.style.height=e+"px",this._layers)this._layers.hasOwnProperty(o)&&this._layers[o].resize(t,e);this.refresh(!0)}this._width=t,this._height=e}else{if(null==t||null==e)return;this._width=t,this._height=e,this.getLayer(Au).resize(t,e)}return this},t.prototype.clearLayer=function(t){var e=this._layers[t];e&&e.clear()},t.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},t.prototype.getRenderedCanvas=function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[314159].dom;var e=new Pu("image",this,t.pixelRatio||this.dpr);e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor);var r=e.ctx;if(t.pixelRatio<=this.dpr){this.refresh();var i=e.dom.width,n=e.dom.height;this.eachLayer((function(t){t.__builtin__?r.drawImage(t.dom,0,0,i,n):t.renderToCanvas&&(r.save(),t.renderToCanvas(r),r.restore())}))}else for(var o={inHover:!1,viewWidth:this._width,viewHeight:this._height},a=this.storage.getDisplayList(!0),s=0,h=a.length;s<h;s++){var l=a[s];al(r,l,o,s===h-1)}return e.dom},t.prototype.getWidth=function(){return this._width},t.prototype.getHeight=function(){return this._height},t}();function zu(t){V(t)&&(t=(new DOMParser).parseFromString(t,"text/xml"));var e=t;for(9===e.nodeType&&(e=e.firstChild);"svg"!==e.nodeName.toLowerCase()||1!==e.nodeType;)e=e.nextSibling;return e}var Ou,Iu={fill:"fill",stroke:"stroke","stroke-width":"lineWidth",opacity:"opacity","fill-opacity":"fillOpacity","stroke-opacity":"strokeOpacity","stroke-dasharray":"lineDash","stroke-dashoffset":"lineDashOffset","stroke-linecap":"lineCap","stroke-linejoin":"lineJoin","stroke-miterlimit":"miterLimit","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","text-anchor":"textAlign",visibility:"visibility",display:"display"},Ru=W(Iu),Fu={"alignment-baseline":"textBaseline","stop-color":"stopColor"},Bu=W(Fu),Nu=function(){function t(){this._defs={},this._root=null}return t.prototype.parse=function(t,e){e=e||{};var r=zu(t);this._defsUsePending=[];var i=new Gn;this._root=i;var n=[],o=r.getAttribute("viewBox")||"",a=parseFloat(r.getAttribute("width")||e.width),s=parseFloat(r.getAttribute("height")||e.height);isNaN(a)&&(a=null),isNaN(s)&&(s=null),qu(r,i,null,!0,!1);for(var h,l,u=r.firstChild;u;)this._parseNode(u,i,n,null,!1,!1),u=u.nextSibling;if(function(t,e){for(var r=0;r<e.length;r++){var i=e[r];i[0].style[i[1]]=t[i[2]]}}(this._defs,this._defsUsePending),this._defsUsePending=[],o){var c=Zu(o);c.length>=4&&(h={x:parseFloat(c[0]||0),y:parseFloat(c[1]||0),width:parseFloat(c[2]),height:parseFloat(c[3])})}if(h&&null!=a&&null!=s&&(l=Ju(h,{x:0,y:0,width:a,height:s}),!e.ignoreViewBox)){var f=i;(i=new Gn).add(f),f.scaleX=f.scaleY=l.scale,f.x=l.x,f.y=l.y}return e.ignoreRootClip||null==a||null==s||i.setClipPath(new Ua({shape:{x:0,y:0,width:a,height:s}})),{root:i,width:a,height:s,viewBoxRect:h,viewBoxTransform:l,named:n}},t.prototype._parseNode=function(t,e,r,i,n,o){var a,s=t.nodeName.toLowerCase(),h=i;if("defs"===s&&(n=!0),"text"===s&&(o=!0),"defs"===s||"switch"===s)a=e;else{if(!n){var l=Ou[s];if(l&&xt(Ou,s)){a=l.call(this,t,e);var u=t.getAttribute("name");if(u){var c={name:u,namedFrom:null,svgNodeTagLower:s,el:a};r.push(c),"g"===s&&(h=c)}else i&&r.push({name:i.name,namedFrom:i,svgNodeTagLower:s,el:a});e.add(a)}}var f=Hu[s];if(f&&xt(Hu,s)){var p=f.call(this,t),d=t.getAttribute("id");d&&(this._defs[d]=p)}}if(a&&a.isGroup)for(var v=t.firstChild;v;)1===v.nodeType?this._parseNode(v,a,r,h,n,o):3===v.nodeType&&o&&this._parseText(v,a),v=v.nextSibling},t.prototype._parseText=function(t,e){var r=new Ra({style:{text:t.textContent},silent:!0,x:this._textX||0,y:this._textY||0});Xu(e,r),qu(t,r,this._defsUsePending,!1,!1),function(t,e){var r=e.__selfStyle;if(r){var i=r.textBaseline,n=i;i&&"auto"!==i?"baseline"===i?n="alphabetic":"before-edge"===i||"text-before-edge"===i?n="top":"after-edge"===i||"text-after-edge"===i?n="bottom":"central"!==i&&"mathematical"!==i||(n="middle"):n="alphabetic",t.style.textBaseline=n}var o=e.__inheritedStyle;if(o){var a=o.textAlign,s=a;a&&("middle"===a&&(s="center"),t.style.textAlign=s)}}(r,e);var i=r.style,n=i.fontSize;n&&n<9&&(i.fontSize=9,r.scaleX*=n/9,r.scaleY*=n/9);var o=(i.fontSize||i.fontFamily)&&[i.fontStyle,i.fontWeight,(i.fontSize||12)+"px",i.fontFamily||"sans-serif"].join(" ");i.font=o;var a=r.getBoundingRect();return this._textX+=a.width,e.add(r),r},t.internalField=void(Ou={g:function(t,e){var r=new Gn;return Xu(e,r),qu(t,r,this._defsUsePending,!1,!1),r},rect:function(t,e){var r=new Ua;return Xu(e,r),qu(t,r,this._defsUsePending,!1,!1),r.setShape({x:parseFloat(t.getAttribute("x")||"0"),y:parseFloat(t.getAttribute("y")||"0"),width:parseFloat(t.getAttribute("width")||"0"),height:parseFloat(t.getAttribute("height")||"0")}),r.silent=!0,r},circle:function(t,e){var r=new Is;return Xu(e,r),qu(t,r,this._defsUsePending,!1,!1),r.setShape({cx:parseFloat(t.getAttribute("cx")||"0"),cy:parseFloat(t.getAttribute("cy")||"0"),r:parseFloat(t.getAttribute("r")||"0")}),r.silent=!0,r},line:function(t,e){var r=new ph;return Xu(e,r),qu(t,r,this._defsUsePending,!1,!1),r.setShape({x1:parseFloat(t.getAttribute("x1")||"0"),y1:parseFloat(t.getAttribute("y1")||"0"),x2:parseFloat(t.getAttribute("x2")||"0"),y2:parseFloat(t.getAttribute("y2")||"0")}),r.silent=!0,r},ellipse:function(t,e){var r=new Bs;return Xu(e,r),qu(t,r,this._defsUsePending,!1,!1),r.setShape({cx:parseFloat(t.getAttribute("cx")||"0"),cy:parseFloat(t.getAttribute("cy")||"0"),rx:parseFloat(t.getAttribute("rx")||"0"),ry:parseFloat(t.getAttribute("ry")||"0")}),r.silent=!0,r},polygon:function(t,e){var r,i=t.getAttribute("points");i&&(r=ju(i));var n=new ah({shape:{points:r||[]},silent:!0});return Xu(e,n),qu(t,n,this._defsUsePending,!1,!1),n},polyline:function(t,e){var r,i=t.getAttribute("points");i&&(r=ju(i));var n=new lh({shape:{points:r||[]},silent:!0});return Xu(e,n),qu(t,n,this._defsUsePending,!1,!1),n},image:function(t,e){var r=new Ha;return Xu(e,r),qu(t,r,this._defsUsePending,!1,!1),r.setStyle({image:t.getAttribute("xlink:href")||t.getAttribute("href"),x:+t.getAttribute("x"),y:+t.getAttribute("y"),width:+t.getAttribute("width"),height:+t.getAttribute("height")}),r.silent=!0,r},text:function(t,e){var r=t.getAttribute("x")||"0",i=t.getAttribute("y")||"0",n=t.getAttribute("dx")||"0",o=t.getAttribute("dy")||"0";this._textX=parseFloat(r)+parseFloat(n),this._textY=parseFloat(i)+parseFloat(o);var a=new Gn;return Xu(e,a),qu(t,a,this._defsUsePending,!1,!0),a},tspan:function(t,e){var r=t.getAttribute("x"),i=t.getAttribute("y");null!=r&&(this._textX=parseFloat(r)),null!=i&&(this._textY=parseFloat(i));var n=t.getAttribute("dx")||"0",o=t.getAttribute("dy")||"0",a=new Gn;return Xu(e,a),qu(t,a,this._defsUsePending,!1,!0),this._textX+=parseFloat(n),this._textY+=parseFloat(o),a},path:function(t,e){var r=Ms(t.getAttribute("d")||"");return Xu(e,r),qu(t,r,this._defsUsePending,!1,!1),r.silent=!0,r}}),t}(),Hu={lineargradient:function(t){var e=parseInt(t.getAttribute("x1")||"0",10),r=parseInt(t.getAttribute("y1")||"0",10),i=parseInt(t.getAttribute("x2")||"10",10),n=parseInt(t.getAttribute("y2")||"0",10),o=new Sh(e,r,i,n);return Eu(t,o),Wu(t,o),o},radialgradient:function(t){var e=parseInt(t.getAttribute("cx")||"0",10),r=parseInt(t.getAttribute("cy")||"0",10),i=parseInt(t.getAttribute("r")||"0",10),n=new Th(e,r,i);return Eu(t,n),Wu(t,n),n}};function Eu(t,e){"userSpaceOnUse"===t.getAttribute("gradientUnits")&&(e.global=!0)}function Wu(t,e){for(var r=t.firstChild;r;){if(1===r.nodeType&&"stop"===r.nodeName.toLocaleLowerCase()){var i=r.getAttribute("offset"),n=void 0;n=i&&i.indexOf("%")>0?parseInt(i,10)/100:i?parseFloat(i):0;var o={};$u(r,o,o);var a=o.stopColor||r.getAttribute("stop-color")||"#000000";e.colorStops.push({offset:n,color:a})}r=r.nextSibling}}function Xu(t,e){t&&t.__inheritedStyle&&(e.__inheritedStyle||(e.__inheritedStyle={}),L(e.__inheritedStyle,t.__inheritedStyle))}function ju(t){for(var e=Zu(t),r=[],i=0;i<e.length;i+=2){var n=parseFloat(e[i]),o=parseFloat(e[i+1]);r.push([n,o])}return r}function qu(t,e,r,i,n){var o=e,a=o.__inheritedStyle=o.__inheritedStyle||{},s={};1===t.nodeType&&(function(t,e){var r=t.getAttribute("transform");if(r){r=r.replace(/,/g," ");var i=[],n=null;r.replace(Gu,(function(t,e,r){return i.push(e,r),""}));for(var o=i.length-1;o>0;o-=2){var a=i[o],s=i[o-1],h=Zu(a);switch(n=n||[1,0,0,1,0,0],s){case"translate":ke(n,n,[parseFloat(h[0]),parseFloat(h[1]||"0")]);break;case"scale":Te(n,n,[parseFloat(h[0]),parseFloat(h[1]||h[0])]);break;case"rotate":Se(n,n,-parseFloat(h[0])*Ku,[parseFloat(h[1]||"0"),parseFloat(h[2]||"0")]);break;case"skewX":be(n,[1,0,Math.tan(parseFloat(h[0])*Ku),1,0,0],n);break;case"skewY":be(n,[1,Math.tan(parseFloat(h[0])*Ku),0,1,0,0],n);break;case"matrix":n[0]=parseFloat(h[0]),n[1]=parseFloat(h[1]),n[2]=parseFloat(h[2]),n[3]=parseFloat(h[3]),n[4]=parseFloat(h[4]),n[5]=parseFloat(h[5])}}e.setLocalTransform(n)}}(t,e),$u(t,a,s),i||function(t,e,r){for(var i=0;i<Ru.length;i++){var n=Ru[i];null!=(o=t.getAttribute(n))&&(e[Iu[n]]=o)}for(i=0;i<Bu.length;i++){var o;n=Bu[i];null!=(o=t.getAttribute(n))&&(r[Fu[n]]=o)}}(t,a,s)),o.style=o.style||{},null!=a.fill&&(o.style.fill=Vu(o,"fill",a.fill,r)),null!=a.stroke&&(o.style.stroke=Vu(o,"stroke",a.stroke,r)),F(["lineWidth","opacity","fillOpacity","strokeOpacity","miterLimit","fontSize"],(function(t){null!=a[t]&&(o.style[t]=parseFloat(a[t]))})),F(["lineDashOffset","lineCap","lineJoin","fontWeight","fontFamily","fontStyle","textAlign"],(function(t){null!=a[t]&&(o.style[t]=a[t])})),n&&(o.__selfStyle=s),a.lineDash&&(o.style.lineDash=B(Zu(a.lineDash),(function(t){return parseFloat(t)}))),"hidden"!==a.visibility&&"collapse"!==a.visibility||(o.invisible=!0),"none"===a.display&&(o.ignore=!0)}var Yu=/^url\(\s*#(.*?)\)/;function Vu(t,e,r,i){var n=r&&r.match(Yu);if(!n)return"none"===r&&(r=null),r;var o=lt(n[1]);i.push([t,e,o])}var Uu=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function Zu(t){return t.match(Uu)||[]}var Gu=/(translate|scale|rotate|skewX|skewY|matrix)\(([\-\s0-9\.eE,]*)\)/g,Ku=Math.PI/180;var Qu=/([^\s:;]+)\s*:\s*([^:;]+)/g;function $u(t,e,r){var i,n=t.getAttribute("style");if(n)for(Qu.lastIndex=0;null!=(i=Qu.exec(n));){var o=i[1],a=xt(Iu,o)?Iu[o]:null;a&&(e[a]=i[2]);var s=xt(Fu,o)?Fu[o]:null;s&&(r[s]=i[2])}}function Ju(t,e){var r=e.width/t.width,i=e.height/t.height,n=Math.min(r,i);return{scale:n,x:-(t.x+t.width/2)*n+(e.x+e.width/2),y:-(t.y+t.height/2)*n+(e.y+e.height/2)}}function tc(t,e){return(new Nu).parse(t,e)}var ec=ca.CMD;function rc(t,e){return Math.abs(t-e)<1e-5}function ic(t){var e,r,i,n,o,a,s,h,l,u,c,f,p,d,v,y,g,_,m,x,w,b,k,S,T=t.data,C=t.len(),P=[],M=0,A=0,L=0,D=0;function z(t,r){e&&e.length>2&&P.push(e),e=[t,r]}function O(t,r,i,n){rc(t,i)&&rc(r,n)||e.push(t,r,i,n,i,n)}for(var I=0;I<C;){var R=T[I++],F=1===I;switch(F&&(L=M=T[I],D=A=T[I+1],R!==ec.L&&R!==ec.C&&R!==ec.Q||(e=[L,D])),R){case ec.M:M=L=T[I++],A=D=T[I++],z(L,D);break;case ec.L:O(M,A,r=T[I++],i=T[I++]),M=r,A=i;break;case ec.C:e.push(T[I++],T[I++],T[I++],T[I++],M=T[I++],A=T[I++]);break;case ec.Q:r=T[I++],i=T[I++],n=T[I++],o=T[I++],e.push(M+2/3*(r-M),A+2/3*(i-A),n+2/3*(r-n),o+2/3*(i-o),n,o),M=n,A=o;break;case ec.A:var B=T[I++],N=T[I++],H=T[I++],E=T[I++],W=T[I++],X=T[I++]+W;I+=1;var j=!T[I++];r=Math.cos(W)*H+B,i=Math.sin(W)*E+N,F?z(L=r,D=i):O(M,A,r,i),M=Math.cos(X)*H+B,A=Math.sin(X)*E+N;for(var q=(j?-1:1)*Math.PI/2,Y=W;j?Y>X:Y<X;Y+=q){var V=j?Math.max(Y+q,X):Math.min(Y+q,X);a=Y,s=V,h=B,l=N,u=H,c=E,f=void 0,p=void 0,d=void 0,v=void 0,y=void 0,g=void 0,_=void 0,m=void 0,x=void 0,w=void 0,b=void 0,k=void 0,S=void 0,f=Math.abs(s-a),p=4*Math.tan(f/4)/3,d=s<a?-1:1,v=Math.cos(a),y=Math.sin(a),g=Math.cos(s),_=Math.sin(s),m=v*u+h,x=y*c+l,w=g*u+h,b=_*c+l,k=u*p*d,S=c*p*d,e.push(m-k*y,x+S*v,w+k*_,b-S*g,w,b)}break;case ec.R:L=M=T[I++],D=A=T[I++],r=L+T[I++],i=D+T[I++],z(r,D),O(r,D,r,i),O(r,i,L,i),O(L,i,L,D),O(L,D,r,D);break;case ec.Z:e&&O(M,A,L,D),M=L,A=D}}return e&&e.length>2&&P.push(e),P}function nc(t,e,r,i,n,o,a,s,h,l){if(rc(t,r)&&rc(e,i)&&rc(n,a)&&rc(o,s))h.push(a,s);else{var u=2/l,c=u*u,f=a-t,p=s-e,d=Math.sqrt(f*f+p*p);f/=d,p/=d;var v=r-t,y=i-e,g=n-a,_=o-s,m=v*v+y*y,x=g*g+_*_;if(m<c&&x<c)h.push(a,s);else{var w=f*v+p*y,b=-f*g-p*_;if(m-w*w<c&&w>=0&&x-b*b<c&&b>=0)h.push(a,s);else{var k=[],S=[];Mr(t,r,n,a,.5,k),Mr(e,i,o,s,.5,S),nc(k[0],S[0],k[1],S[1],k[2],S[2],k[3],S[3],h,l),nc(k[4],S[4],k[5],S[5],k[6],S[6],k[7],S[7],h,l)}}}}function oc(t,e,r){var i=t[e],n=t[1-e],o=Math.abs(i/n),a=Math.ceil(Math.sqrt(o*r)),s=Math.floor(r/a);0===s&&(s=1,a=r);for(var h=[],l=0;l<a;l++)h.push(s);var u=r-a*s;if(u>0)for(l=0;l<u;l++)h[l%a]+=1;return h}function ac(t,e,r){for(var i=t.r0,n=t.r,o=t.startAngle,a=t.endAngle,s=Math.abs(a-o),h=s*n,l=n-i,u=h>Math.abs(l),c=oc([h,l],u?0:1,e),f=(u?s:l)/c.length,p=0;p<c.length;p++)for(var d=(u?l:s)/c[p],v=0;v<c[p];v++){var y={};u?(y.startAngle=o+f*p,y.endAngle=o+f*(p+1),y.r0=i+d*v,y.r=i+d*(v+1)):(y.startAngle=o+d*v,y.endAngle=o+d*(v+1),y.r0=i+f*p,y.r=i+f*(p+1)),y.clockwise=t.clockwise,y.cx=t.cx,y.cy=t.cy,r.push(y)}}function sc(t,e,r,i){return t*i-r*e}function hc(t,e,r,i,n,o,a,s){var h=r-t,l=i-e,u=a-n,c=s-o,f=sc(u,c,h,l);if(Math.abs(f)<1e-6)return null;var p=sc(t-n,e-o,u,c)/f;return p<0||p>1?null:new Ae(p*h+t,p*l+e)}function lc(t,e,r){var i=new Ae;Ae.sub(i,r,e),i.normalize();var n=new Ae;return Ae.sub(n,t,e),n.dot(i)}function uc(t,e){var r=t[t.length-1];r&&r[0]===e[0]&&r[1]===e[1]||t.push(e)}function cc(t){var e=t.points,r=[],i=[];Eo(e,r,i);var n=new Ne(r[0],r[1],i[0]-r[0],i[1]-r[1]),o=n.width,a=n.height,s=n.x,h=n.y,l=new Ae,u=new Ae;return o>a?(l.x=u.x=s+o/2,l.y=h,u.y=h+a):(l.y=u.y=h+a/2,l.x=s,u.x=s+o),function(t,e,r){for(var i=t.length,n=[],o=0;o<i;o++){var a=t[o],s=t[(o+1)%i],h=hc(a[0],a[1],s[0],s[1],e.x,e.y,r.x,r.y);h&&n.push({projPt:lc(h,e,r),pt:h,idx:o})}if(n.length<2)return[{points:t},{points:t}];n.sort((function(t,e){return t.projPt-e.projPt}));var l=n[0],u=n[n.length-1];if(u.idx<l.idx){var c=l;l=u,u=c}var f=[l.pt.x,l.pt.y],p=[u.pt.x,u.pt.y],d=[f],v=[p];for(o=l.idx+1;o<=u.idx;o++)uc(d,t[o].slice());for(uc(d,p),uc(d,f),o=u.idx+1;o<=l.idx+i;o++)uc(v,t[o%i].slice());return uc(v,f),uc(v,p),[{points:d},{points:v}]}(e,l,u)}function fc(t,e,r,i){if(1===r)i.push(e);else{var n=Math.floor(r/2),o=t(e);fc(t,o[0],n,i),fc(t,o[1],r-n,i)}return i}function pc(t,e){var r,i=[],n=t.shape;switch(t.type){case"rect":!function(t,e,r){for(var i=t.width,n=t.height,o=i>n,a=oc([i,n],o?0:1,e),s=o?"width":"height",h=o?"height":"width",l=o?"x":"y",u=o?"y":"x",c=t[s]/a.length,f=0;f<a.length;f++)for(var p=t[h]/a[f],d=0;d<a[f];d++){var v={};v[l]=f*c,v[u]=d*p,v[s]=c,v[h]=p,v.x+=t.x,v.y+=t.y,r.push(v)}}(n,e,i),r=Ua;break;case"sector":ac(n,e,i),r=Js;break;case"circle":ac({r0:0,r:n.r,startAngle:0,endAngle:2*Math.PI,cx:n.cx,cy:n.cy},e,i),r=Js;break;default:var o=t.getComputedTransform(),a=o?Math.sqrt(Math.max(o[0]*o[0]+o[1]*o[1],o[2]*o[2]+o[3]*o[3])):1,s=B(function(t,e){var r=ic(t),i=[];e=e||1;for(var n=0;n<r.length;n++){var o=r[n],a=[],s=o[0],h=o[1];a.push(s,h);for(var l=2;l<o.length;){var u=o[l++],c=o[l++],f=o[l++],p=o[l++],d=o[l++],v=o[l++];nc(s,h,u,c,f,p,d,v,a,e),s=d,h=v}i.push(a)}return i}(t.getUpdatedPathProxy(),a),(function(t){return function(t){for(var e=[],r=0;r<t.length;)e.push([t[r++],t[r++]]);return e}(t)})),h=s.length;if(0===h)fc(cc,{points:s[0]},e,i);else if(h===e)for(var l=0;l<h;l++)i.push({points:s[l]});else{var u=0,c=B(s,(function(t){var e=[],r=[];Eo(t,e,r);var i=(r[1]-e[1])*(r[0]-e[0]);return u+=i,{poly:t,area:i}}));c.sort((function(t,e){return e.area-t.area}));var f=e;for(l=0;l<h;l++){var p=c[l];if(f<=0)break;var d=l===h-1?f:Math.ceil(p.area/u*e);d<0||(fc(cc,{points:p.poly},d,i),f-=d)}}r=ah}if(!r)return function(t,e){for(var r=[],i=0;i<e;i++)r.push(Ds(t));return r}(t,e);var v,y,g=[];for(l=0;l<i.length;l++){var _=new r;_.setShape(i[l]),v=t,(y=_).setStyle(v.style),y.z=v.z,y.z2=v.z2,y.zlevel=v.zlevel,g.push(_)}return g}function dc(t,e){var r=t.length,i=e.length;if(r===i)return[t,e];for(var n=[],o=[],a=r<i?t:e,s=Math.min(r,i),h=Math.abs(i-r)/6,l=(s-2)/6,u=Math.ceil(h/l)+1,c=[a[0],a[1]],f=h,p=2;p<s;){var d=a[p-2],v=a[p-1],y=a[p++],g=a[p++],_=a[p++],m=a[p++],x=a[p++],w=a[p++];if(f<=0)c.push(y,g,_,m,x,w);else{for(var b=Math.min(f,u-1)+1,k=1;k<=b;k++){var S=k/b;Mr(d,y,_,x,S,n),Mr(v,g,m,w,S,o),d=n[3],v=o[3],c.push(n[1],o[1],n[2],o[2],d,v),y=n[5],g=o[5],_=n[6],m=o[6]}f-=b-1}}return a===t?[c,e]:[t,c]}function vc(t,e){for(var r=t.length,i=t[r-2],n=t[r-1],o=[],a=0;a<e.length;)o[a++]=i,o[a++]=n;return o}function yc(t){for(var e=0,r=0,i=0,n=t.length,o=0,a=n-2;o<n;a=o,o+=2){var s=t[a],h=t[a+1],l=t[o],u=t[o+1],c=s*u-l*h;e+=c,r+=(s+l)*c,i+=(h+u)*c}return 0===e?[t[0]||0,t[1]||0]:[r/e/3,i/e/3,e]}function gc(t,e,r,i){for(var n=(t.length-2)/6,o=1/0,a=0,s=t.length,h=s-2,l=0;l<n;l++){for(var u=6*l,c=0,f=0;f<s;f+=2){var p=0===f?u:(u+f-2)%h+2,d=t[p]-r[0],v=t[p+1]-r[1],y=e[f]-i[0]-d,g=e[f+1]-i[1]-v;c+=y*y+g*g}c<o&&(o=c,a=l)}return a}function _c(t){for(var e=[],r=t.length,i=0;i<r;i+=2)e[i]=t[r-i-2],e[i+1]=t[r-i-1];return e}function mc(t){return t.__isCombineMorphing}var xc="__mOriginal_";function wc(t,e,r){var i=xc+e,n=t[i]||t[e];t[i]||(t[i]=t[e]);var o=r.replace,a=r.after,s=r.before;t[e]=function(){var t,e=arguments;return s&&s.apply(this,e),t=o?o.apply(this,e):n.apply(this,e),a&&a.apply(this,e),t}}function bc(t,e){var r=xc+e;t[r]&&(t[e]=t[r],t[r]=null)}function kc(t,e){for(var r=0;r<t.length;r++)for(var i=t[r],n=0;n<i.length;){var o=i[n],a=i[n+1];i[n++]=e[0]*o+e[2]*a+e[4],i[n++]=e[1]*o+e[3]*a+e[5]}}function Sc(t,e){var r=t.getUpdatedPathProxy(),i=e.getUpdatedPathProxy(),n=function(t,e){for(var r,i,n,o=[],a=[],s=0;s<Math.max(t.length,e.length);s++){var h=t[s],l=e[s],u=void 0,c=void 0;h?l?(i=u=(r=dc(h,l))[0],n=c=r[1]):(c=vc(n||h,h),u=h):(u=vc(i||l,l),c=l),o.push(u),a.push(c)}return[o,a]}(ic(r),ic(i)),o=n[0],a=n[1],s=t.getComputedTransform(),h=e.getComputedTransform();s&&kc(o,s),h&&kc(a,h),wc(e,"updateTransform",{replace:function(){this.transform=null}}),e.transform=null;var l=function(t,e,r,i){for(var n,o=[],a=0;a<t.length;a++){var s=t[a],h=e[a],l=yc(s),u=yc(h);null==n&&(n=l[2]<0!=u[2]<0);var c=[],f=[],p=0,d=1/0,v=[],y=s.length;n&&(s=_c(s));for(var g=6*gc(s,h,l,u),_=y-2,m=0;m<_;m+=2){var x=(g+m)%_+2;c[m+2]=s[x]-l[0],c[m+3]=s[x+1]-l[1]}if(c[0]=s[g]-l[0],c[1]=s[g+1]-l[1],r>0)for(var w=i/r,b=-i/2;b<=i/2;b+=w){var k=Math.sin(b),S=Math.cos(b),T=0;for(m=0;m<s.length;m+=2){var C=c[m],P=c[m+1],M=h[m]-u[0],A=h[m+1]-u[1],L=M*S-A*k,D=M*k+A*S;v[m]=L,v[m+1]=D;var z=L-C,O=D-P;T+=z*z+O*O}if(T<d){d=T,p=b;for(var I=0;I<v.length;I++)f[I]=v[I]}}else for(var R=0;R<y;R+=2)f[R]=h[R]-u[0],f[R+1]=h[R+1]-u[1];o.push({from:c,to:f,fromCp:l,toCp:u,rotation:-p})}return o}(o,a,10,Math.PI),u=[];wc(e,"buildPath",{replace:function(t){for(var r=e.__morphT,i=1-r,n=[],o=0;o<l.length;o++){var a=l[o],s=a.from,h=a.to,c=a.rotation*r,f=a.fromCp,p=a.toCp,d=Math.sin(c),v=Math.cos(c);Wt(n,f,p,r);for(var y=0;y<s.length;y+=2){var g=s[y],_=s[y+1],m=g*i+(k=h[y])*r,x=_*i+(S=h[y+1])*r;u[y]=m*v-x*d+n[0],u[y+1]=m*d+x*v+n[1]}var w=u[0],b=u[1];t.moveTo(w,b);for(y=2;y<s.length;){var k=u[y++],S=u[y++],T=u[y++],C=u[y++],P=u[y++],M=u[y++];w===k&&b===S&&T===P&&C===M?t.lineTo(P,M):t.bezierCurveTo(k,S,T,C,P,M),w=P,b=M}}}})}function Tc(t,e,r){if(!t||!e)return e;var i=r.done,n=r.during;return Sc(t,e),e.__morphT=0,e.animateTo({__morphT:1},L({during:function(t){e.dirtyShape(),n&&n(t)},done:function(){bc(e,"buildPath"),bc(e,"updateTransform"),e.__morphT=-1,e.createPathProxy(),e.dirtyShape(),i&&i()}},r)),e}function Cc(t,e,r,i,n,o){t=n===r?0:Math.round(32767*(t-r)/(n-r)),e=o===i?0:Math.round(32767*(e-i)/(o-i));for(var a,s=0,h=32768;h>0;h/=2){var l=0,u=0;(t&h)>0&&(l=1),(e&h)>0&&(u=1),s+=h*h*(3*l^u),0===u&&(1===l&&(t=h-1-t,e=h-1-e),a=t,t=e,e=a)}return s}function Pc(t){var e=1/0,r=1/0,i=-1/0,n=-1/0,o=B(t,(function(t){var o=t.getBoundingRect(),a=t.getComputedTransform(),s=o.x+o.width/2+(a?a[4]:0),h=o.y+o.height/2+(a?a[5]:0);return e=Math.min(s,e),r=Math.min(h,r),i=Math.max(s,i),n=Math.max(h,n),[s,h]}));return B(o,(function(o,a){return{cp:o,z:Cc(o[0],o[1],e,r,i,n),path:t[a]}})).sort((function(t,e){return t.z-e.z})).map((function(t){return t.path}))}function Mc(t){return pc(t.path,t.count)}function Ac(t,e,r){var i=[];!function t(e){for(var r=0;r<e.length;r++){var n=e[r];mc(n)?t(n.childrenRef()):n instanceof za&&i.push(n)}}(t);var n=i.length;if(!n)return{fromIndividuals:[],toIndividuals:[],count:0};var o=(r.dividePath||Mc)({path:e,count:n});if(o.length!==n)return{fromIndividuals:[],toIndividuals:[],count:0};i=Pc(i),o=Pc(o);for(var a=r.done,s=r.during,h=r.individualDelay,l=new Pn,u=0;u<n;u++){var c=i[u],f=o[u];f.parent=e,f.copyTransform(l),h||Sc(c,f)}function p(t){for(var e=0;e<o.length;e++)o[e].addSelfToZr(t)}function d(){e.__isCombineMorphing=!1,e.__morphT=-1,e.childrenRef=null,bc(e,"addSelfToZr"),bc(e,"removeSelfFromZr")}e.__isCombineMorphing=!0,e.childrenRef=function(){return o},wc(e,"addSelfToZr",{after:function(t){p(t)}}),wc(e,"removeSelfFromZr",{after:function(t){for(var e=0;e<o.length;e++)o[e].removeSelfFromZr(t)}});var v=o.length;if(h){var y=v,g=function(){0===--y&&(d(),a&&a())};for(u=0;u<v;u++){var _=h?L({delay:(r.delay||0)+h(u,v,i[u],o[u]),done:g},r):r;Tc(i[u],o[u],_)}}else e.__morphT=0,e.animateTo({__morphT:1},L({during:function(t){for(var r=0;r<v;r++){var i=o[r];i.__morphT=e.__morphT,i.dirtyShape()}s&&s(t)},done:function(){d();for(var e=0;e<t.length;e++)bc(t[e],"updateTransform");a&&a()}},r));return e.__zr&&p(e.__zr),{fromIndividuals:i,toIndividuals:o,count:v}}function Lc(t,e,r){var i=e.length,n=[],o=r.dividePath||Mc;if(mc(t)){!function t(e){for(var r=0;r<e.length;r++){var i=e[r];mc(i)?t(i.childrenRef()):i instanceof za&&n.push(i)}}(t.childrenRef());var a=n.length;if(a<i)for(var s=0,h=a;h<i;h++)n.push(Ds(n[s++%a]));n.length=i}else{n=o({path:t,count:i});var l=t.getComputedTransform();for(h=0;h<n.length;h++)n[h].setLocalTransform(l);if(n.length!==i)return{fromIndividuals:[],toIndividuals:[],count:0}}n=Pc(n),e=Pc(e);var u=r.individualDelay;for(h=0;h<i;h++){var c=u?L({delay:(r.delay||0)+u(h,i,n[h],e[h])},r):r;Tc(n[h],e[h],c)}return{fromIndividuals:n,toIndividuals:e,count:e.length}}export{lt as $,lh as A,Ua as B,Is as C,_h as D,Bs as E,wh as F,Wa as G,Xa as H,ja as I,Ce as J,Xt as K,ph as L,xt as M,Ne as N,bh as O,za as P,Gn as Q,rh as R,Js as S,Pn as T,zh as U,Sh as V,Lh as W,Ae as X,Th as Y,Ha as Z,ls as _,G as a,hi as a$,P as a0,C as a1,I as a2,st as a3,oe as a4,j as a5,Q as a6,H as a7,X as a8,ct as a9,ya as aA,Tu as aB,Du as aC,Cr as aD,Sr as aE,ai as aF,ua as aG,ot as aH,it as aI,Se as aJ,E as aK,ve as aL,de as aM,zu as aN,tc as aO,Ju as aP,Do as aQ,we as aR,me as aS,Tt as aT,M as aU,Eo as aV,ke as aW,Te as aX,ni as aY,ui as aZ,ei as a_,gt as aa,wt as ab,Fn as ac,Rh as ad,Xr as ae,u as af,ol as ag,Zt as ah,c as ai,io as aj,to as ak,ir as al,eo as am,at as an,Rn as ao,rt as ap,ll as aq,jt as ar,qt as as,co as at,Dn as au,ca as av,Nt as aw,Wt as ax,Rr as ay,Ar as az,Z as b,li as b0,Ct as b1,Ft as b2,Rt as b3,St as b4,Lt as b5,Dt as b6,Pt as b7,At as b8,Ir as b9,Dr as ba,Et as bb,Pe as bc,zr as bd,fa as be,da as bf,ii as bg,Tn as bh,Ni as bi,$ as bj,pe as bk,fe as bl,te as bm,et as bn,mc as bo,Tc as bp,Ac as bq,Lc as br,Ds as bs,di as bt,Me as bu,Yt as bv,kt as bw,no as bx,U as c,q as d,ht as e,yt as f,F as g,z as h,V as i,i as j,O as k,A as l,B as m,Y as n,R as o,W as p,pi as q,N as r,nt as s,Ms as t,Ls as u,xe as v,be as w,L as x,As as y,ah as z};
