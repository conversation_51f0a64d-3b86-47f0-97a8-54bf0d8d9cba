import{r as e,s as a}from"./element-plus-95e0b914.js";import"./vue-5bfa3a54.js";import{a as t}from"./vxe-table-3a25f2d2.js";import{_ as s,u as l,j as o,k as i}from"./index-a5df0f75.js";import{g as r}from"./index-dde80e00.js";import{j as n,h as u,m,as as d,o as p,c,a as f,b as v,F as j,k as g,t as h,x,a8 as y,f as w,aa as b,af as k,H as P,C as _,D as U}from"./@vue-5e5cdef9.js";import"./lodash-es-ea7deab5.js";import"./@vueuse-5227c686.js";import"./vue-router-6159329f.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./dayjs-67f8ddef.js";import"./@babel-f3c0a00c.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./xe-utils-fe99d42a.js";import"./dom-zindex-5f662ad1.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./nprogress-e136a1b4.js";import"./quasar-df1bac18.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-be30dbb6.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./lodash-6d99edc3.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";const V=e=>(_("data-v-586768fa"),e=e(),U(),e),L={class:"app-container u-flex-col-x-center"},N={class:"info u-flex-col-x-center shadow-md"},z=V((()=>f("div",{class:"regular-title w-full"},"个人信息",-1))),A={class:"content w-full flex-1 u-flex-col-x-center"},D={class:"avatar u-flex-center-no"},E={key:0,class:"add u-flex-center-no"},C=["src"],I=V((()=>f("p",{class:"tip supplementary-text"},"点击头像可进行更换",-1))),R={class:"detail"},S={class:"key small-title text-center"},q={class:"value body-text text-center"},B={key:1,class:"w-full u-flex-center-no gap-10"},F={class:"upLoad u-flex-center-no u-border"},H={key:0,class:"img-box u-flex-center-no"},M=["src"],O={class:"img-remove u-flex-center-no"},$={class:"notice flex-1 u-flex-column gap-10 justify-end"},K=V((()=>f("p",{class:"body-small-text"},[f("span",null,"注意事项："),f("span",null,"图片格式需为png、jpg格式，图片大小不超过2M")],-1))),T={class:"u-text-right"},W={key:2,class:"w-full u-flex-center-no gap-10"},Z=s(Object.assign({name:"accoutCenter"},{__name:"index",setup(s){const _=l(),U=n({imgPath:"",userAvatar:"",data:{}}),V=u(!1),Z=u(!1),G=n({type:"userinfo",modelData:{userName:"",userPhone:"",userEmail:""},imgPath:"",uploadPath:"",fileList:[]}),J=n({userName:[{required:!0,message:"请输入用户名"}]}),Q=e=>{G.type=e,"userinfo"==e&&(G.modelData={userName:U.data["账号昵称"],userPhone:U.data["手机号码"],userEmail:U.data["电子邮箱"]}),V.value=!0},X=()=>{G.modelData={userName:"",userPhone:"",userEmail:""}},Y=async()=>{Z.value=!0;try{const e=await o(G.modelData);Z.value=!1,"00000"==e.status&&(V.value=!1,G.modelData={userName:"",userPhone:"",userEmail:""})}catch(e){Z.value=!1}},ee=async e=>{if(0==G.fileList.length)return void t.modal.message({content:"请先上传图片",status:"info"});let a;Z.value=!0,a=G.fileList.length>0?G.fileList[0].raw:"",((e,a,t="jpeg")=>new Promise(""==e?(e,a)=>e(""):(s,l)=>{const o=e,i=new FileReader;i.onload=function(){const e=new Image;e.src=i.result,e.onload=function(){const l=document.createElement("canvas"),i=l.getContext("2d");let r=e.width,n=e.height;r>a&&(n*=a/r,r=a),i.fillStyle="#fff",l.width=r,l.height=n,i.drawImage(e,0,0,r,n);const u=l.toDataURL(`image/${t}`,.7),m=atob(u.split(",")[1]),d=u.split(",")[0].split(":")[1].split(";")[0],p=new ArrayBuffer(m.length),c=new Uint8Array(p);for(let e=0;e<m.length;e++)c[e]=m.charCodeAt(e);const f=new Blob([p],{type:d}),v=new File([f],o.name,{type:d});s(v)}},i.readAsDataURL(o)}))(a,100).then((async e=>{try{const a=await i(_.userInfo.userUid,e);if(Z.value=!1,"00000"==a.status){V.value=!1;let e={..._.userInfo,avatar:a.data.url};_.saveUser(e),se(_.userInfo.username)}}catch(a){Z.value=!1}})).catch((e=>{t.modal.message({content:"压缩图片时出错",status:"info"})}))},ae=e=>{G.uploadPath=URL.createObjectURL(e.raw)},te=()=>{G.uploadPath="",G.fileList=[],t.modal.message({content:"清除成功",status:"info"})},se=async e=>{try{const a=await r({userName:e,currentPage:1,pageSize:15});"00000"==a.status&&a.data.records.length>0&&(U.userAvatar=a.data.records[0].userAvatar,U.data=(e=>{const a={userName:"账号昵称",userStatus:"账号状态",roleName:"所属角色",projectName:"所在项目",userType:"用户类型",userPhone:"手机号码",userEmail:"电子邮箱"},t={};for(let s in a)t[a[s]]=e[s];return t})(a.data.records[0]))}catch(a){t.modal.message({content:"获取用户信息异常",status:"info"})}};return m((()=>{se(_.userInfo.username)})),(t,s)=>{const l=d("EditPen"),o=e,i=d("vxe-input"),r=d("vxe-form-item"),n=d("vxe-button"),u=d("vxe-form"),m=d("Delete"),_=d("Plus"),se=a,le=d("vxe-modal");return p(),c("div",L,[f("section",N,[z,f("div",A,[f("p",D,[v(U).userAvatar?(p(),c("img",{key:1,src:v(U).userAvatar},null,8,C)):(p(),c("span",E,"+")),f("span",{class:"avatar-upload",onClick:s[0]||(s[0]=e=>Q("avatar"))},"头像上传")]),I,f("p",R,[(p(!0),c(j,null,g(v(U).data,((e,a)=>(p(),c("span",{class:"detail-item u-flex-center-no",key:a},[f("i",S,h(a),1),f("i",q,h(e),1)])))),128))]),f("p",{class:"edit flex",onClick:s[1]||(s[1]=e=>Q("userinfo"))},[x(o,{size:20},{default:y((()=>[x(l)])),_:1})])])]),x(le,{modelValue:v(V),"onUpdate:modelValue":s[3]||(s[3]=e=>P(V)?V.value=e:null),title:"userinfo"==v(G).type?"编辑信息":"个性化设置",width:"600","min-width":"400","min-height":"300",loading:v(Z),resize:"","destroy-on-close":"",onHide:X},{default:y((()=>["userinfo"==v(G).type?(p(),w(u,{key:0,data:v(G).modelData,rules:v(J),"title-align":"right","title-width":"100",onSubmit:Y},{default:y((()=>[x(r,{field:"userName",title:"用户名",span:24,"item-render":{}},{default:y((({data:e})=>[x(i,{modelValue:e.userName,"onUpdate:modelValue":a=>e.userName=a},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),x(r,{field:"userPhone",title:"手机号",span:24,"item-render":{}},{default:y((({data:e})=>[x(i,{modelValue:e.userPhone,"onUpdate:modelValue":a=>e.userPhone=a},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),x(r,{field:"userEmail",title:"电子邮箱",span:24,"item-render":{}},{default:y((({data:e})=>[x(i,{modelValue:e.userEmail,"onUpdate:modelValue":a=>e.userEmail=a},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),x(r,{align:"right","title-align":"left",span:24},{default:y((()=>[x(n,{type:"submit"},{default:y((()=>[b("提交")])),_:1})])),_:1})])),_:1},8,["data","rules"])):"avatar"==v(G).type?(p(),c("div",B,[f("div",F,[x(se,{ref:"uploadRef",action:"","auto-upload":!1,"show-file-list":!1,limit:1,"file-list":v(G).fileList,"onUpdate:fileList":s[2]||(s[2]=e=>v(G).fileList=e),"on-change":ae},{default:y((()=>[v(G).uploadPath?(p(),c("div",H,[f("img",{src:v(G).uploadPath},null,8,M),f("span",O,[x(o,{size:30,onClick:k(te,["stop"])},{default:y((()=>[x(m)])),_:1})])])):(p(),w(o,{key:1,size:30,class:"avatar-uploader-icon"},{default:y((()=>[x(_)])),_:1}))])),_:1},8,["file-list"])]),f("div",$,[K,f("p",T,[x(n,{onClick:ee},{default:y((()=>[b("提交")])),_:1})])])])):(p(),c("div",W,"个性化"))])),_:1},8,["modelValue","title","loading"])])}}}),[["__scopeId","data-v-586768fa"]]);export{Z as default};
