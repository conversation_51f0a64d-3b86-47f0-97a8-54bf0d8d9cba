import{b as e,a as t,C as a,f as o,G as l,H as i,I as s}from"./element-plus-95e0b914.js";import"./vue-5bfa3a54.js";import"./vxe-table-3a25f2d2.js";import{U as r,r as n,a as d,b as u,g as c,c as m}from"./user-ce7ab9a9.js";import{S as p}from"./@element-plus-4c34063a.js";import{n as f}from"./@vicons-f32a0bdb.js";import{l as g}from"./lodash-6d99edc3.js";import{p as v,_ as j}from"./index-a5df0f75.js";import{x as b}from"./xe-utils-fe99d42a.js";import{h,j as k,m as y,as as _,o as w,c as x,x as D,a8 as C,a as S,aa as P,t as z,q as V,b as I,a6 as U,f as N,l as O}from"./@vue-5e5cdef9.js";import"./lodash-es-ea7deab5.js";import"./@vueuse-5227c686.js";import"./vue-router-6159329f.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./dayjs-67f8ddef.js";import"./@babel-f3c0a00c.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./dom-zindex-5f662ad1.js";import"./index-dde80e00.js";import"./user-cde6e84b.js";import"./addUser-bb6895b7.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./nprogress-e136a1b4.js";import"./quasar-df1bac18.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-be30dbb6.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";const R={class:"form"},q={class:"table-btn"},T={class:"flex justify-center items-center"},L={class:"flex"},M={key:0},A={key:1},B=j(Object.assign({name:"role"},{__name:"index",setup(j){const B=h(),F=h(),K=f,G=h(),H=h(),E=h(!0),W=h(!1),Z=h(!1),$=h(),J=h(111),Q=k({condition:{roleName:""},tablePage:{totalResult:0,currentPage:1,pageSize:15},modelData:{title:"新增用户",roleName:"",roleRemark:"",menuList:[]}}),X=g._.omit(g._.cloneDeep(Q.condition),[]),Y=g._.omit(g._.cloneDeep(Q.modelData),["projectProps"]);g._.curry(v)("/deviceMonitor/plantDetail?plantUid=");const ee=k({roleName:[{required:!0,message:"该输入框不能为空",trigger:"blur"}]}),te=k({border:"full",showFooter:!1,loading:!1,minHeight:600,height:"auto",autoResize:!0,columnConfig:{resizable:!0},customConfig:{storage:{visible:!0,fixed:!0},checkMethod:({column:e})=>!["seq"].includes(e.field)},editConfig:{trigger:"click",mode:"cell"},rowConfig:{},sortConfig:{remote:!0},data:[],toolbarConfig:{custom:{allowFixed:!1},slots:{buttons:"toolbar_buttons",tools:"toolbar_tools"}},columns:[{field:"seq",type:"seq",width:50},{field:"roleID",title:"角色ID",width:"auto"},{field:"roleName",title:"角色名称",width:"auto"},{field:"roleRemark",title:"备注",width:"auto"},{field:"createTime",title:"创建时间",width:"auto"},{field:"operations",title:"操作",fixed:"right",showOverflow:!1,slots:{default:"row-operate"},width:250}]}),ae=h(!1),oe=h(),le=({row:e,_rowIndex:t,column:a,visibleData:o,columnIndex:l})=>{const i=e[a.field];if("合计："===i&&"plantType"===a.field)return{rowspan:1,colspan:3};if("合计："===i&&"city"===a.field)return{rowspan:1,colspan:0};if("合计："===i&&"projectId"===a.field)return{rowspan:1,colspan:0};if(i&&["plantType","projectId"].includes(a.field)){const e=o[t-1];let l=o[t+1];if(e&&e[a.field]===i)return{rowspan:0,colspan:0};{let e=1;for(;l&&l[a.field]===i;)l=o[++e+t];if(e>1)return{rowspan:e,colspan:1}}}},ie=[],se=k({menuOptions:[],selectOptions:[]}),re=({field:e,order:t})=>{Q.condition.order=e,null!==t?Q.condition.isAsc="asc"===t:(Q.condition.order="",Q.condition.isAsc=""),me()},ne=(e,t)=>{},de=async()=>{Z.value=!0;let e="";try{"编辑用户"===Q.modelData.title?e=await n({...g._.omit(Q.modelData,[])}):"新增用户"===Q.modelData.title?e=await d({...g._.omit(Q.modelData,[])}):(Q.modelData.menuList=$.value.getCheckedNodes(!1,!0).map((e=>e.id)),e=await n({...g._.omit(Q.modelData,[])}))}catch(t){Z.value=!1}Z.value=!1,"00000"===e.status&&await me(),W.value=!1},ue=e=>{Object.assign(Q.modelData,Y),Q.modelData.title="新增用户",W.value=!0};const ce=e=>{me(e.currentPage,e.pageSize)};const me=async(e=1,t=15)=>{te.loading=!0,Q.tablePage.currentPage=e,Q.tablePage.pageSize=t,Q.condition.projectSpecial=g._.uniq(g._.flattenDeep(Q.condition.projectSpecial));const a=await m({...Q.condition,...Q.tablePage});te.loading=!1,te.data=a.data.records,Q.tablePage.totalResult=a.data.total};return y((()=>{me(),async function(){try{const e=await c();se.menuOptions=e.data,se.menuOptions.forEach((e=>{0!==e.children.length||ie.includes(e.id+"")||ie.push(e.id+"")}))}catch(e){}}()})),(n,d)=>{const c=_("vxe-input"),m=_("vxe-form-item"),f=_("vxe-button"),g=_("vxe-form"),v=a,j=o,h=l,k=_("vxe-pager"),y=_("vxe-grid"),Y=i,pe=_("vxe-modal"),fe=s;return w(),x("div",{ref_key:"appContainerRef",ref:G,class:"app-container"},[D(y,U({id:"roleTable",ref_key:"xGrid",ref:H,"scroll-y":{enabled:!1},"span-method":le,class:"my-grid66"},te,{onCustom:ne,onSortChange:re}),{form:C((()=>[S("div",R,[D(g,{ref_key:"ordinaryForm",ref:F,collapseStatus:E.value,"onUpdate:collapseStatus":d[2]||(d[2]=e=>E.value=e),data:Q.condition},{default:C((()=>[D(m,{field:"userName",title:"角色名称"},{default:C((({data:e})=>[D(c,{modelValue:e.roleName,"onUpdate:modelValue":t=>e.roleName=t,placeholder:"请输入角色名称"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),D(m,null,{default:C((()=>[D(f,{status:"danger",onClick:d[0]||(d[0]=e=>(e=>{if("senior"===e){const e=B.value.getItems().map((e=>e.field));Object.assign(Q.condition,b.pick(X,e))}else{const e=F.value.getItems().map((e=>e.field));Object.assign(Q.condition,b.pick(X,e))}})("ordinary"))},{default:C((()=>[P("重置")])),_:1})])),_:1}),D(m,null,{default:C((()=>[D(f,{status:"primary",onClick:d[1]||(d[1]=e=>me(1))},{default:C((()=>[P("查询")])),_:1})])),_:1})])),_:1},8,["collapseStatus","data"])])])),toolbar_buttons:C((()=>[])),toolbar_tools:C((()=>[S("div",q,[D(f,{status:"primary",onClick:ue},{default:C((()=>[P("新增")])),_:1})])])),top:C((()=>[])),"row-plantStatus":C((({row:e})=>[D(v,{type:"正常"===e.plantStatus?"success":"warning"},{default:C((()=>[P(z(e.plantStatus),1)])),_:2},1032,["type"])])),"row-userStatus":C((({row:e})=>[S("div",T,[S("div",{style:V({background:"启用"===e.userStatus?"#24b276":"grey"}),class:"circle"},null,4),S("span",null,z(e.userStatus),1)])])),"row-operate":C((({row:a})=>[S("div",L,[D(h,{content:"修改",effect:"dark",placement:"top"},{default:C((()=>[D(j,{icon:I(K).PencilSharp,plain:"",onClick:e=>(e=>{Object.assign(Q.modelData,e),Q.modelData.userType="个人用户"===Q.modelData.userType?"0":"1",Q.modelData.title="编辑用户",W.value=!0})(a)},null,8,["icon","onClick"])])),_:2},1024),D(h,{content:"分配用户",effect:"dark",placement:"top"},{default:C((()=>[D(j,{icon:I(p),plain:"",onClick:e=>(e=>{oe.value="分配用户 - "+e.roleName,ae.value=!0,J.value=e.roleID})(a)},null,8,["icon","onClick"])])),_:2},1024),D(h,{content:"授权角色权限",effect:"dark",placement:"top"},{default:C((()=>[D(j,{icon:I(K).KeySharp,plain:"",type:"primary",onClick:e=>function(e){Q.modelData.title="授权角色",Object.assign(Q.modelData,e),$.value?$.value.setCheckedKeys(e.menuList.filter((e=>e>=100)),!0):se.defaultMenusID=e.menuList.filter((e=>e>=100||ie.includes(e)?e:void 0)),W.value=!0}(a)},null,8,["icon","onClick"])])),_:2},1024),D(h,{content:"删除",effect:"dark",placement:"top"},{default:C((()=>[D(j,{icon:I(K).TrashSharp,plain:"",type:"danger",onClick:o=>{return l=a.roleID,void e.confirm("是否要删除该角色？(该删除操作不可逆)",{confirmButtonText:"确定",cancelButtonText:"取消"}).then((async()=>{try{"00000"===(await u(l)).status&&(t.success("角色删除成功"),me())}catch(e){}})).catch((()=>{}));var l}},null,8,["icon","onClick"])])),_:2},1024)])])),bottom:C((()=>[])),pager:C((()=>[D(k,{"current-page":Q.tablePage.currentPage,"onUpdate:currentPage":d[3]||(d[3]=e=>Q.tablePage.currentPage=e),"page-size":Q.tablePage.pageSize,"onUpdate:pageSize":d[4]||(d[4]=e=>Q.tablePage.pageSize=e),"page-sizes":[10,15,20],total:Q.tablePage.totalResult,perfect:"",onPageChange:ce},null,8,["current-page","page-size","total"])])),_:1},16),D(pe,{modelValue:W.value,"onUpdate:modelValue":d[6]||(d[6]=e=>W.value=e),loading:Z.value,title:Q.modelData.title,"destroy-on-close":"",escClosable:"","min-height":"400","min-width":"600",resize:"","show-footer":"",width:"800"},{default:C((()=>[D(g,{data:Q.modelData,rules:ee,"title-align":"right","title-width":"100"},{default:C((()=>["编辑用户"===Q.modelData.title||"新增用户"===Q.modelData.title?(w(),x("div",M,[D(m,{"item-render":{},span:24,field:"roleName",title:"角色名称"},{default:C((({data:e})=>[D(c,{modelValue:e.roleName,"onUpdate:modelValue":t=>e.roleName=t,placeholder:"请输入角色名称"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),D(m,{"item-render":{},span:24,field:"roleRemark",title:"备注"},{default:C((({data:e})=>[D(c,{modelValue:e.roleRemark,"onUpdate:modelValue":t=>e.roleRemark=t,placeholder:"请输入备注"},null,8,["modelValue","onUpdate:modelValue"])])),_:1})])):(w(),x("div",A,[D(m,{"item-render":{},span:24,field:"userPassword"},{default:C((({data:e})=>[D(Y,{ref_key:"treeRef",ref:$,data:se.menuOptions,"default-checked-keys":se.defaultMenusID,"node-key":"id","show-checkbox":""},null,8,["data","default-checked-keys"])])),_:1})]))])),_:1},8,["data","rules"])])),footer:C((()=>[D(f,{type:"submit",onClick:de},{default:C((()=>[P("提交")])),_:1}),D(f,{type:"cancel",onClick:d[5]||(d[5]=e=>W.value=!1)},{default:C((()=>[P("取消")])),_:1})])),_:1},8,["modelValue","loading","title"]),ae.value?(w(),N(fe,{key:0,modelValue:ae.value,"onUpdate:modelValue":d[7]||(d[7]=e=>ae.value=e),"close-on-press-escape":!1,size:1e3,title:oe.value},{default:C((()=>[D(r,{roleId:J.value},null,8,["roleId"])])),_:1},8,["modelValue","title"])):O("",!0)],512)}}}),[["__scopeId","data-v-c3d31b20"]]);export{B as default};
