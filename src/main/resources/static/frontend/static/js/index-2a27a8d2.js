import{v as t}from"./@vueuse-af86c621.js";import{r as e,f as a,v as i,k as s,d as l,B as o}from"./element-plus-d975be09.js";import"./vue-5bfa3a54.js";import{M as r}from"./@element-plus-4c34063a.js";import{a as d}from"./vxe-table-3a25f2d2.js";import{X as n}from"./xe-utils-fe99d42a.js";import{g as c}from"./index-18c14ee1.js";import{f as m,d as p,c as u}from"./chartResize-3e3d11d7.js";import{g as f,a as h}from"./index-d11362ff.js";import{u as g}from"./vue-router-6159329f.js";import{d as v}from"./dayjs-d60cc07f.js";import{i as b}from"./echarts-f30da64f.js";import{h as y,j as x,m as j,p as w,as as P,o as k,c as C,a as N,t as V,b as S,x as z,a8 as D,aa as _,F as U,k as L,y as A,f as I,a6 as T,a9 as E,ak as B,l as M,C as R,D as Y,B as q}from"./@vue-5e5cdef9.js";import{_ as W}from"./index-8cc8d4b8.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./lodash-es-ea7deab5.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./@babel-f3c0a00c.js";import"./dom-zindex-5f662ad1.js";import"./element-resize-detector-0d37a2ab.js";import"./batch-processor-06abf2b4.js";import"./lodash-6d99edc3.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./nprogress-e136a1b4.js";import"./quasar-b3f06d8a.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-7bbbb435.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";const O={class:"station-sel flex items-center justify-start u-gap-10"},G=(t=>(R("data-v-2ffd5ebd"),t=t(),Y(),t))((()=>N("span",{class:"u-flex-y-center"},[N("i",{class:"square"}),N("i",{class:"small-title"},"当前电站")],-1))),H={class:"body-text"},F={class:"small-title u-flex-1 text-right"},J={class:"inverter-list"},X={class:"scrollbar-flex-content"},$=["onClick"],K={class:"table-btn"},Z={key:1,class:"chart-box","element-loading-text":"正在生成图表"},Q={class:"table-btn"},tt=[q('<div class="u-flex-column chart-power-item" data-v-2ffd5ebd><p class="chart-title small-title u-flex-y-center" data-v-2ffd5ebd>lr</p><figure id="lrChart" class="u-flex-1" data-v-2ffd5ebd></figure></div><div class="u-flex-column chart-today-item" data-v-2ffd5ebd><p class="chart-title small-title u-flex-y-center" data-v-2ffd5ebd>日发电量</p><figure id="todayChart" class="u-flex-1" data-v-2ffd5ebd></figure></div><div class="u-flex-column chart-PV-item" data-v-2ffd5ebd><p class="chart-title small-title u-flex-y-center justify-between" data-v-2ffd5ebd><span data-v-2ffd5ebd>电流</span></p><figure id="iacChart" class="u-flex-1" data-v-2ffd5ebd></figure></div><div class="u-flex-column chart-L-item" data-v-2ffd5ebd><p class="chart-title small-title u-flex-y-center justify-between" data-v-2ffd5ebd><span data-v-2ffd5ebd>电压</span></p><figure id="vacChart" class="u-flex-1" data-v-2ffd5ebd></figure></div>',4)],et={class:"station-list"},at={class:"infinite-list",style:{overflow:"auto"}},it={key:0,class:"infinite-list-empty supplementary-text text-center"},st=["onClick"],lt=W(Object.assign({name:"realtime"},{__name:"index",setup(R){const Y=g(),q=y(),W=y(),lt=y();y(!0);const ot=y("选择电站"),rt=y(!1),dt=x({show:!1,loading:!0,opt:{title:{show:!1,text:"",textStyle:{fontSize:m(16)}},legend:{show:!0,bottom:"2%",selected:{}},tooltip:{show:!0,trigger:"axis",confine:!0,formatter:null},grid:{left:"10%",right:"10%",top:"20%",bottom:"25%"},xAxis:{data:[]},yAxis:{type:"value",axisLine:{show:!0},axisTick:{show:!0},splitLine:{show:!1}},series:[]},originData:{}}),nt={lrChart:null,todayChart:null,iacChart:null,vacChart:null},ct=x({first:!0,plantUid:"",data:[],page:{currentPage:1,pageSize:50}}),mt=x({type:"station",visible:!1,stationData:[],submitLoading:!1,list:{plantUid:"",plantName:"",pageSize:15,currentPage:1}}),pt=x({condition:{deviceName:"",counterId:"",date:v().format("YYYY-MM-DD")},modelData:{},tablePage:{totalResult:0,currentPage:1,pageSize:15}}),ut=x({id:"realTime",border:!0,showFooter:!1,minHeight:500,height:"96%",maxHeight:860,loading:!1,autoResize:!0,editConfig:{trigger:"click",mode:"cell"},scrollX:{enabled:!1},scrollY:{enabled:!1},sortConfig:{sortMethod:({sortList:t})=>{let e={};t.forEach((t=>{e[t.field]=t.order}))}},data:[],customConfig:{storage:{visible:!0,fixed:!0}},toolbarConfig:{custom:!0,slots:{buttons:"toolbar_buttons",tools:"toolbar_tools"}},columns:[{title:" 时间",field:"initTime",width:120,fixed:"left",slots:{default:"time"}},{title:"设备状态",field:"status",width:120},{title:"电流",children:[{title:"电流 N",field:"currentN",width:120},{title:"电流 A",field:"iac1",width:120},{title:"电流 B",field:"iac2",width:120},{title:"电流 C",field:"iac3",width:120}]},{title:"电压",children:[{title:"电压 A",field:"vac1",width:120},{title:"电压 B",field:"vac2",width:120},{title:"电压 C",field:"vac3",width:120}]},,{title:"漏电流",field:"drainCurrent",width:120},{title:"漏电等级",field:"drainGrade",width:120},{title:"上电试合闸",field:"electrifyStatus",width:120},{title:"lr",children:[{title:"lr1 整定值",field:"lr1Sv",width:120},{title:"lr1 延迟时间",field:"lr1Time",width:120},{title:"lr2 整定值",field:"lr2Sv",width:120},{title:"lr2 延迟时间",field:"lr2Time",width:120},{title:"lr3 整定值",field:"lr3Sv",width:120}]},{fixed:"right",title:"当日发电量(kWh)",field:"todayElectricity",width:150},{fixed:"right",title:"总发电量(kWh)",field:"totalElectricity",width:150}],spanMethod:({row:t,_rowIndex:e,column:a,visibleData:i})=>{const s=t[a.field];if(s&&["initTime","power","totalElectricity","todayElectricity"].includes(a.field)){const t=i[e-1];let l=i[e+1];if(t&&t[a.field]===s)return{rowspan:0,colspan:0};{let t=1;for(;l&&l[a.field]===s;)l=i[++t+e];if(t>1)return{rowspan:t,colspan:1}}}}}),ft=async(t,e)=>{if(pt.condition.deviceName=t,pt.condition.counterId=e,rt.value=!0,ut.loading)d.modal.message({content:"正在请求数据中，请勿频繁点击",status:"info"});else{ut.loading=!0;try{const t=await f({...pt.tablePage,...pt.condition});if("00000"==t.status){if(dt.show)dt.originData=(t=>{let e={time:[],PV:{vac1:{name:"A点电压",type:"line",smooth:!0,data:[]},vac2:{name:"B点电压",type:"line",smooth:!0,data:[]},vac3:{name:"C点电压",type:"line",smooth:!0,data:[]},iac1:{name:"A点电流",type:"line",smooth:!0,data:[]},iac2:{name:"B点电流",type:"line",smooth:!0,data:[]},iac3:{name:"C点电流",type:"line",smooth:!0,data:[]},currentN:{name:"N点电流",type:"line",smooth:!0,data:[]}},todayElec:{name:"日发电量(kWh)",type:"bar",data:[],lineStyle:{color:"#f8b62d"},itemStyle:{color:"#f8b62d",normal:{color:"#f8b62d"}},areaStyle:{color:"#f8b62d"}},lrs:{lr1:{name:"lr1 整定值",type:"line",smooth:!0,data:[]},lr2:{name:"lr2 整定值",type:"line",smooth:!0,data:[]},lr3:{name:"lr3 整定值",type:"line",smooth:!0,data:[]}}};return t.forEach((t=>{e.time.push(t.initTime.substr(11,5)),e.todayElec.data.push(t.todayElec),e.PV.vac1.data.push(t.vac1),e.PV.vac2.data.push(t.vac2),e.PV.vac3.data.push(t.vac3),e.PV.iac1.data.push(t.iac1),e.PV.iac2.data.push(t.iac2),e.PV.iac3.data.push(t.iac3),e.PV.currentN.data.push(t.currentN),e.lrs.lr1.data.push(t.lr1Sv),e.lrs.lr2.data.push(t.lr2Sv),e.lrs.lr3.data.push(t.lr3Sv)})),e})(t.data.records.reverse()),Pt(dt.originData,nt);else{pt.tablePage.totalResult=t.data.total;let e=n.clone(t.data.records,!0);ut.data=e}ut.loading=!1}else ut.data=[],pt.tablePage.totalResult=0,ut.loading=!1,dt.show&&(dt.loading=!1,Pt(dt.chartTem,nt))}catch(a){ut.data=[],pt.tablePage.totalResult=0,ut.loading=!1}finally{rt.value=!1}}},ht=async(t,e)=>{mt.submitLoading=!0;try{const a=await h({plantUid:t,...ct.page});mt.submitLoading=!1,ot.value=e,mt.visible=!1,"00000"==a.status&&(pt.tablePage={totalResult:0,currentPage:1,pageSize:20},ct.data=a.data.records,pt.condition.deviceName=a.data.records[0].deviceName,pt.condition.counterId=a.data.records[0].deviceId,ft(pt.condition.deviceName,pt.condition.counterId))}catch(a){mt.submitLoading=!1}},gt=()=>{mt.list.currentPage+=1,vt("next")},vt=async(t,e)=>{mt.list.plantUid="","reset"==t?mt.list.currentPage=1:"page"==t&&(mt.list.plantUid=e),mt.submitLoading=!0;try{const e=await c(mt.list);mt.submitLoading=!1,"00000"==e.status?(1==mt.list.currentPage?mt.stationData=e.data.records:mt.stationData=[...mt.stationData,...e.data.records],ct.first&&(ot.value=mt.stationData[0].plantName,ht(mt.stationData[0].plantUid,mt.stationData[0].plantName)),"page"==t&&(mt.list.plantName=mt.stationData[0].plantName)):mt.stationData=[]}catch(a){mt.submitLoading=!1}},bt=()=>{ft(pt.condition.deviceName,pt.condition.counterId)},yt=async()=>{bt()},xt=t=>t.getTime()>Date.now(),jt=()=>{},wt=(t,e)=>{let a=document.getElementById(t),i=b(a);return i.setOption(e),i},Pt=(t,e)=>{let a;dt.loading=!0;for(let i in e){switch(a=JSON.parse(JSON.stringify(dt.opt)),a.xAxis.data=t.time,i){case"todayChart":a.title.text="日发电量(kWh)",a.yAxis.name="kWh",a.series.push(t.todayElec);break;case"lrChart":a.title.text="lr",a.yAxis.name="",a.series.push(t.lrs.lr1),a.series.push(t.lrs.lr2),a.series.push(t.lrs.lr3);break;case"iacChart":a.title.text="电流(A)",a.yAxis.name="A",a.series.push(t.PV.iac1),a.series.push(t.PV.iac2),a.series.push(t.PV.iac3),a.series.push(t.PV.currentN);break;case"vacChart":a.title.text="电压(V)",a.yAxis.name="v",a.series.push(t.PV.vac1),a.series.push(t.PV.vac2),a.series.push(t.PV.vac3)}e[i]&&(e[i].dispose(),p(lt.value)),e[i]=wt(i,a)}setTimeout((()=>{dt.loading=!1}),1e3),u(lt.value,e)},kt=t=>{for(let e in t)t[e]&&t[e].dispose()},Ct=async t=>{"chart"==t?(dt.show=!0,pt.tablePage={currentPage:1,pageSize:100},await ft(pt.condition.deviceName,pt.condition.counterId)):(dt.PVSel=[],dt.LSel=[],dt.pvSelected=[],dt.Lselected=[],kt(nt),pt.tablePage={currentPage:1,pageSize:15},p(lt.value),dt.loading=!0,dt.show=!1,await ft(pt.condition.deviceName,pt.condition.counterId))},Nt=async t=>{await ft(pt.condition.deviceName,pt.condition.counterId)};return j((async()=>{Y.query.plantUid&&""!=Y.query.plantUid?await vt("page",Y.query.plantUid):await vt("reset"),ct.first=!1})),w((()=>{p(lt.value),kt(nt)})),(d,n)=>{const c=P("CaretBottom"),m=e,p=a,u=i,f=s,h=P("vxe-button"),g=P("vxe-pager"),v=P("vxe-grid"),b=l,y=P("vxe-modal"),x=o,j=t;return k(),C("div",{class:"app-container",ref_key:"appContainerRef",ref:q},[N("div",O,[N("p",{class:"plant-sel h-full u-flex-center u-gap-10 shadow-md body-text justify-between",onClick:n[0]||(n[0]=t=>{return e="station",mt.type=e,void(mt.visible=!0);var e})},[G,N("span",H,V(S(ot)),1),z(m,{class:"sel-btn",size:20},{default:D((()=>[z(c)])),_:1})]),N("span",F,[z(p,{type:"primary",onClick:yt,loading:S(rt)},{default:D((()=>[_("刷新")])),_:1},8,["loading"]),_("  "),z(u,{modelValue:S(pt).condition.date,"onUpdate:modelValue":n[1]||(n[1]=t=>S(pt).condition.date=t),disabled:S(ut).loading,type:"date","value-format":"YYYY-MM-DD",onChange:bt,"disabled-date":xt},null,8,["modelValue","disabled"])])]),N("div",J,[z(f,null,{default:D((()=>[N("div",X,[(k(!0),C(U,null,L(S(ct).data,((t,e)=>(k(),C("p",{class:A(["inverter-list-item h-full body-text u-flex-center",S(pt).condition.deviceName==t.deviceName?"is-active":""]),onClick:e=>ft(t.deviceName,t.deviceId)},V(t.deviceName),11,$)))),256))])])),_:1})]),S(dt).show?E((k(),C("div",Z,[N("div",Q,[z(h,{status:"primary",onClick:n[5]||(n[5]=t=>Ct("table"))},{default:D((()=>[_("表格")])),_:1})]),N("div",{ref_key:"gatherChart",ref:lt,id:"chartGather"},tt,512)])),[[x,S(dt).loading]]):(k(),I(v,T({key:0,ref_key:"xGrid",ref:W,class:"my-grid66"},S(ut)),{toolbar_buttons:D((()=>[])),toolbar_tools:D((()=>[N("div",K,[z(h,{status:"warning",icon:"vxe-icon-chart-line",onClick:n[2]||(n[2]=t=>Ct("chart"))})])])),time:D((({row:t})=>[N("span",null,V(t.initTime.substr(11,5)),1)])),"row-operate":D((({row:t})=>[z(p,{link:"",type:"primary",onClick:e=>d.seeTableItem(t)},{default:D((()=>[_("查看详情")])),_:2},1032,["onClick"])])),bottom:D((()=>[])),pager:D((()=>[z(g,{perfect:"","current-page":S(pt).tablePage.currentPage,"onUpdate:currentPage":n[3]||(n[3]=t=>S(pt).tablePage.currentPage=t),"page-size":S(pt).tablePage.pageSize,"onUpdate:pageSize":n[4]||(n[4]=t=>S(pt).tablePage.pageSize=t),total:S(pt).tablePage.totalResult,onPageChange:Nt},null,8,["current-page","page-size","total"])])),_:1},16)),z(y,{modelValue:S(mt).visible,"onUpdate:modelValue":n[9]||(n[9]=t=>S(mt).visible=t),title:"电站选择",width:"500","min-width":"400","min-height":"100",loading:S(mt).submitLoading,resize:"","destroy-on-close":"",onHide:jt},{default:D((()=>[N("div",et,[z(b,{modelValue:S(mt).list.plantName,"onUpdate:modelValue":n[7]||(n[7]=t=>S(mt).list.plantName=t),clearable:"",onKeyup:n[8]||(n[8]=B((t=>vt("reset")),["enter"]))},{append:D((()=>[z(p,{icon:S(r),onClick:n[6]||(n[6]=t=>vt("reset"))},null,8,["icon"])])),_:1},8,["modelValue"]),E((k(),C("ul",at,[0==S(mt).stationData.length?(k(),C("li",it,"暂无数据")):M("",!0),(k(!0),C(U,null,L(S(mt).stationData,(t=>(k(),C("li",{class:"infinite-list-item body-text",key:t.plantUid,onClick:e=>ht(t.plantUid,t.plantName)},V(t.plantName),9,st)))),128))])),[[j,gt]])])])),_:1},8,["modelValue","loading"])],512)}}}),[["__scopeId","data-v-2ffd5ebd"]]);export{lt as default};
