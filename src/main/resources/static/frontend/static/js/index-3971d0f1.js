import{r as e,x as t,G as i,f as a,C as l}from"./element-plus-d975be09.js";import"./vue-5bfa3a54.js";import{a as o}from"./vue-router-6159329f.js";import"./vxe-table-3a25f2d2.js";import{O as s,S as r,b as d}from"./prop-8069d58a.js";import{i as n,_ as c}from"./index-8cc8d4b8.js";import{x as u}from"./xe-utils-fe99d42a.js";import"./dayjs-d60cc07f.js";import"./notification-950a5f80.js";import{h as m,j as p,m as f,p as v,as as b,o as j,c as g,x as T,a8 as h,b as y,H as w,aa as S,a as V,y as _,a6 as C,an as U,ao as I,t as N,C as O,D as x}from"./@vue-5e5cdef9.js";import"./lodash-es-ea7deab5.js";import"./@vueuse-af86c621.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./@babel-f3c0a00c.js";import"./dom-zindex-5f662ad1.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./nprogress-e136a1b4.js";import"./quasar-b3f06d8a.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-7bbbb435.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./lodash-6d99edc3.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./naive-ui-0ee0b8c3.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./date-fns-tz-0cc073c8.js";const R=e=>(O("data-v-1916cfa1"),e=e(),x(),e),P=R((()=>V("p",null,"高级筛选",-1))),k={class:"u-flex-y-center justify-end"},M=R((()=>V("div",{class:"table-btn"},null,-1))),z={class:"u-wh-full u-flex-center-no"},A=c(Object.assign({name:"oamEquip"},{__name:"index",setup(c){o();const O=m(),x=m(),R=m(!0),A=p({type:"SIM",visible:!1,submitLoading:!1}),B=p({visible:!1});p({exportBtn:!1});const E=p({condition:{plantUid:"",plantName:"",imei:"",operatorSn:"",order:"",isAsc:"",enable:"",status:""},tablePage:{totalResult:0,currentPage:1,pageSize:15}}),L=p({border:!0,minHeight:50,columns:[{field:"cardStatusStr",title:"状态",slots:{default:"sim-status"}},{field:"cimi",title:"cimi"},{field:"iccid",title:"iccid"},{field:"imei",title:"imei"},{field:"phoneNumber",title:"手机号码"},{field:"endTime",title:"过期时间"}],data:[]}),D=p({id:"OAMInfo",border:"full",showFooter:!1,minHeight:600,height:"auto",loading:!1,autoResize:!0,editConfig:{trigger:"click",mode:"cell"},sortConfig:{remote:!0,multiple:!1,defaultSort:[]},data:[],toolbarConfig:{className:"toolbar",custom:!0,slots:{buttons:"toolbar_buttons",tools:"toolbar_tools"}},customConfig:{storage:{visible:!0,fixed:!0}},columns:[{type:"seq",width:50,fixed:"left"},{field:"plantName",title:"电站名称",width:200},{field:"counterName",title:"配电房",width:150},{field:"deviceName",title:"设备名称",width:150},{field:"currentTransformer",title:"电流互感器变比",width:150},{field:"potentialTransformer",title:"电压互感器变比",width:150},{field:"deviceAddress",title:"设备地址",width:150},{field:"deviceId",title:"设备编号",width:150},{field:"devicePc",title:"设备pc码",width:150},{field:"deviceType",title:"设备类型",width:150},{field:"displayVersion",title:"显示版本号",width:150},{field:"enable",title:"设备开关机状态",width:150},{field:"endTime",title:"质保时间",width:150},{field:"iccid",title:"物联网卡号",width:150},{field:"cimi",title:"cimi",width:200,visible:!0},{field:"imei",title:"imei",width:150},{field:"manufacturer",title:"厂家",width:150},{field:"module",title:"型号",width:150},{field:"receiveType",title:"逆变器接入类型",width:150},{field:"softwareVersion",title:"软件版本号",width:150},{field:"startTime",title:"激活时间",width:150},{field:"status",title:"设备状态",width:150}]}),Q=e=>{"simple"==e?E.condition={plantUid:"",plantName:"",imei:"",operatorSn:"",order:"",isAsc:"",enable:"",status:""}:(E.condition.enable="",E.condition.status="")},G=({field:e,order:t})=>{null===t?(E.condition.order="",E.isAsc=""):(E.condition.order=e,E.condition.isAsc="asc"==t),q(!1)},q=async e=>{D.loading=!0,e&&(E.tablePage={totalResult:0,currentPage:1,pageSize:15});try{const e=await(t={...E.tablePage,...E.condition},n({url:"/system/counter/page",method:"get",data:{...t}}));"00000"==e.status?(D.data=e.data.records,E.tablePage.totalResult=e.data.total):(D.data=[],E.tablePage.totalResult=0),D.loading=!1}catch(i){D.loading=!1}var t},H=()=>{},Y=async e=>{A.submitLoading=!0;let t=""==e.iccid;L.data=[];try{const a=await d(e.iccid,e.plantUid);if(A.submitLoading=!1,"00000"==a.status){if(t)for(let t in a.data.records)a.data.records[t].imei==e.imei&&L.data.push(a.data.records[t]);else L.data=a.data.records;i="SIM",A.type=i,A.visible=!0}}catch(a){A.submitLoading=!1}var i},F=async(e,t="defalut")=>{"defalut"==t?e?B.visible=!0:(B.visible=!1,await q(!0)):B.visible=!B.visible},J=async e=>{await q(!1)};return f((async()=>{await q(!1);u.toArrayTree([{plantUid:"66666",deviceId:"66666",imei:"6666",deviceType:2,manufacturer:"三晶",module:"R6-T2-20K",projectSpecial:30,deviceAddress:"02",devicePc:"",cimi:"",iccid:"",status:"0",enable:"",cluster:"",startTime:null,endTime:null,softwareVersion:"",displayVersion:"",controlVersion:"",creator:"",receiveType:"",deviceName:"测试逆变器",currentTransformer:null,potentialTransformer:null,pid:"COUNTER001"},{plantUid:"BTOPLANT-20240612-INDUSTRY-COMMERCE-0001",deviceId:"BTO20240612CGQ1201",imei:"BTO20240612CGQ1201",deviceType:12,manufacturer:"BTO",module:"CJQ",projectSpecial:30,deviceAddress:"01",devicePc:"",cimi:"",iccid:"",status:"0",enable:"",cluster:"",startTime:null,endTime:null,softwareVersion:"",displayVersion:"",controlVersion:"",creator:"",receiveType:"",deviceName:"配电房1烟感(室内)",currentTransformer:null,potentialTransformer:null,pid:"DISTRIBUTION-ROOM01"},{plantUid:"BTOPLANT-20240612-INDUSTRY-COMMERCE-0001",deviceId:"BTO20240612CGQ1202",imei:"BTO20240612CGQ1202",deviceType:12,manufacturer:"BTO",module:"CJQ",projectSpecial:30,deviceAddress:"02",devicePc:"",cimi:"",iccid:"",status:"0",enable:"",cluster:"",startTime:null,endTime:null,softwareVersion:"",displayVersion:"",controlVersion:"",creator:"",receiveType:"",deviceName:"配电房1烟感(室外)",currentTransformer:null,potentialTransformer:null,pid:"DISTRIBUTION-ROOM01"},{plantUid:"BTOPLANT-20240612-INDUSTRY-COMMERCE-0001",deviceId:"COUNTER001",imei:"",deviceType:11,manufacturer:"",module:"",projectSpecial:30,deviceAddress:"01",devicePc:"",cimi:"",iccid:"",status:"0",enable:"",cluster:"",startTime:null,endTime:null,softwareVersion:"",displayVersion:"",controlVersion:"",creator:"",receiveType:"",deviceName:"配电柜",currentTransformer:null,potentialTransformer:null,pid:"DISTRIBUTION-ROOM01"},{plantUid:"BTOPLANT-20240612-INDUSTRY-COMMERCE-0001",deviceId:"DISTRIBUTION-ROOM01",imei:"",deviceType:10,manufacturer:"",module:"",projectSpecial:30,deviceAddress:"",devicePc:"",cimi:"",iccid:"",status:"0",enable:"",cluster:"",startTime:null,endTime:null,softwareVersion:"",displayVersion:"",controlVersion:"",creator:"",receiveType:"",deviceName:"配电房",currentTransformer:null,potentialTransformer:null,pid:"0"}],{parentKey:"pid",key:"deviceId",children:"children",data:"data"})})),v((()=>{})),(o,d)=>{const n=b("vxe-input"),c=b("vxe-form-item"),u=b("vxe-button"),m=b("vxe-option"),p=b("vxe-select"),f=b("vxe-form"),v=b("Filter"),K=e,$=t,W=i,Z=a,X=b("vxe-pager"),ee=b("vxe-grid"),te=l,ie=b("vxe-modal");return j(),g("div",{class:"app-container",ref_key:"appContainerRef",ref:O},[T(ee,C({ref_key:"xGrid",ref:x,class:"my-grid66"},y(D),{onSortChange:G}),{form:h((()=>[T(f,{data:y(E).condition,collapseStatus:y(R),"onUpdate:collapseStatus":d[5]||(d[5]=e=>w(R)?R.value=e:null)},{default:h((()=>[T(c,{title:"运维器SN",field:"operatorSn"},{default:h((({data:e})=>[T(n,{modelValue:e.operatorSn,"onUpdate:modelValue":t=>e.operatorSn=t,placeholder:"sn码",clearable:""},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),T(c,{title:"IMEI",field:"imei"},{default:h((({data:e})=>[T(n,{modelValue:e.imei,"onUpdate:modelValue":t=>e.imei=t,placeholder:"请输入运维器IMEI",clearable:""},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),T(c,{title:"所属电站",field:"plantName"},{default:h((({data:e})=>[T(n,{modelValue:e.plantName,"onUpdate:modelValue":t=>e.plantName=t,placeholder:"请输入电站名称",clearable:""},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),T(c,null,{default:h((()=>[T(u,{status:"danger",onClick:d[0]||(d[0]=e=>Q("simple"))},{default:h((()=>[S("重置")])),_:1})])),_:1}),T(c,null,{default:h((()=>[T(u,{status:"primary",onClick:d[1]||(d[1]=e=>q(!0))},{default:h((()=>[S("查询")])),_:1})])),_:1}),T(c,null,{default:h((()=>[T($,{visible:y(B).visible,placement:"bottom",width:400},{reference:h((()=>[T(K,{class:"detail-btn cursor-pointer",size:20,onClick:d[4]||(d[4]=e=>F(!0,"btn"))},{default:h((()=>[T(v)])),_:1})])),default:h((()=>[P,V("div",k,[T(f,{data:y(E).condition},{default:h((()=>[T(c,{title:"运行状态",field:"enable"},{default:h((({data:e})=>[T(p,{modelValue:e.enable,"onUpdate:modelValue":t=>e.enable=t,placeholder:"请选择状态",clearable:""},{default:h((()=>[T(m,{value:"0",label:"关机"}),T(m,{value:"1",label:"启动"})])),_:2},1032,["modelValue","onUpdate:modelValue"])])),_:1}),T(c,{title:"设备状态",field:"status"},{default:h((({data:e})=>[T(p,{modelValue:e.status,"onUpdate:modelValue":t=>e.status=t,placeholder:"请选择状态",clearable:""},{default:h((()=>[T(m,{value:"0",label:"离线"}),T(m,{value:"1",label:"正常运行"}),T(m,{value:"2",label:"告警运行"}),T(m,{value:"3",label:"自检提示"}),T(m,{value:"4",label:"夜间离线"})])),_:2},1032,["modelValue","onUpdate:modelValue"])])),_:1}),T(c,null,{default:h((()=>[T(u,{status:"danger",onClick:d[2]||(d[2]=e=>Q("hightLevel"))},{default:h((()=>[S("重置")])),_:1}),T(u,{status:"primary",onClick:d[3]||(d[3]=e=>F(!1))},{default:h((()=>[S("查询")])),_:1})])),_:1})])),_:1},8,["data"])])])),_:1},8,["visible"])])),_:1})])),_:1},8,["data","collapseStatus"])])),toolbar_buttons:h((()=>[])),toolbar_tools:h((()=>[M])),status:h((({row:e})=>[T(W,{effect:"dark",placement:"bottom",content:e.statusStr},{default:h((()=>[V("div",z,[V("span",{class:_(["dot",`${y(s)[e.statusStr]}`])},null,2)])])),_:2},1032,["content"])])),"row-operate":h((({row:e})=>[T(Z,{link:"",type:"primary",onClick:t=>Y(e)},{default:h((()=>[S("物联网卡信息")])),_:2},1032,["onClick"])])),pager:h((()=>[T(X,{perfect:"","current-page":y(E).tablePage.currentPage,"onUpdate:currentPage":d[6]||(d[6]=e=>y(E).tablePage.currentPage=e),"page-size":y(E).tablePage.pageSize,"onUpdate:pageSize":d[7]||(d[7]=e=>y(E).tablePage.pageSize=e),total:y(E).tablePage.totalResult,onPageChange:J},null,8,["current-page","page-size","total"])])),_:1},16),T(ie,{modelValue:y(A).visible,"onUpdate:modelValue":d[8]||(d[8]=e=>y(A).visible=e),title:"物联网卡信息",width:"1000","min-width":"400","min-height":"100",loading:y(A).submitLoading,resize:"","destroy-on-close":"",onHide:H},{default:h((()=>[T(ee,U(I(y(L))),{"sim-status":h((({row:e})=>[T(te,{type:y(r)[e.cardStatusStr]},{default:h((()=>[S(N(e.cardStatusStr),1)])),_:2},1032,["type"])])),_:1},16)])),_:1},8,["modelValue","loading"])],512)}}}),[["__scopeId","data-v-1916cfa1"]]);export{A as default};
