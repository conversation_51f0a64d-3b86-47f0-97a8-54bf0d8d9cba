var e="top",t="bottom",n="right",r="left",o="auto",i=[e,t,n,r],a="start",s="end",f="clippingParents",c="viewport",p="popper",u="reference",l=i.reduce((function(e,t){return e.concat([t+"-"+a,t+"-"+s])}),[]),d=[].concat(i,[o]).reduce((function(e,t){return e.concat([t,t+"-"+a,t+"-"+s])}),[]),h=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function m(e){return e?(e.nodeName||"").toLowerCase():null}function v(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function g(e){return e instanceof v(e).Element||e instanceof Element}function y(e){return e instanceof v(e).HTMLElement||e instanceof HTMLElement}function b(e){return"undefined"!=typeof ShadowRoot&&(e instanceof v(e).ShadowRoot||e instanceof ShadowRoot)}var x={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach((function(e){var n=t.styles[e]||{},r=t.attributes[e]||{},o=t.elements[e];!y(o)||!m(o)||(Object.assign(o.style,n),Object.keys(r).forEach((function(e){var t=r[e];!1===t?o.removeAttribute(e):o.setAttribute(e,!0===t?"":t)})))}))},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach((function(e){var r=t.elements[e],o=t.attributes[e]||{},i=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce((function(e,t){return e[t]="",e}),{});!y(r)||!m(r)||(Object.assign(r.style,i),Object.keys(o).forEach((function(e){r.removeAttribute(e)})))}))}},requires:["computeStyles"]};function w(e){return e.split("-")[0]}var O=Math.max,j=Math.min,E=Math.round;function D(e,t){void 0===t&&(t=!1);var n=e.getBoundingClientRect(),r=1,o=1;if(y(e)&&t){var i=e.offsetHeight,a=e.offsetWidth;a>0&&(r=E(n.width)/a||1),i>0&&(o=E(n.height)/i||1)}return{width:n.width/r,height:n.height/o,top:n.top/o,right:n.right/r,bottom:n.bottom/o,left:n.left/r,x:n.left/r,y:n.top/o}}function A(e){var t=D(e),n=e.offsetWidth,r=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-r)<=1&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function k(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&b(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function L(e){return v(e).getComputedStyle(e)}function M(e){return["table","td","th"].indexOf(m(e))>=0}function P(e){return((g(e)?e.ownerDocument:e.document)||window.document).documentElement}function W(e){return"html"===m(e)?e:e.assignedSlot||e.parentNode||(b(e)?e.host:null)||P(e)}function B(e){return y(e)&&"fixed"!==L(e).position?e.offsetParent:null}function H(e){for(var t=v(e),n=B(e);n&&M(n)&&"static"===L(n).position;)n=B(n);return n&&("html"===m(n)||"body"===m(n)&&"static"===L(n).position)?t:n||function(e){var t=-1!==navigator.userAgent.toLowerCase().indexOf("firefox");if(-1!==navigator.userAgent.indexOf("Trident")&&y(e)&&"fixed"===L(e).position)return null;var n=W(e);for(b(n)&&(n=n.host);y(n)&&["html","body"].indexOf(m(n))<0;){var r=L(n);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||t&&"filter"===r.willChange||t&&r.filter&&"none"!==r.filter)return n;n=n.parentNode}return null}(e)||t}function R(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function T(e,t,n){return O(e,j(t,n))}function S(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function C(e,t){return t.reduce((function(t,n){return t[n]=e,t}),{})}var q={name:"arrow",enabled:!0,phase:"main",fn:function(o){var a,s=o.state,f=o.name,c=o.options,p=s.elements.arrow,u=s.modifiersData.popperOffsets,l=w(s.placement),d=R(l),h=[r,n].indexOf(l)>=0?"height":"width";if(p&&u){var m=function(e,t){return S("number"!=typeof(e="function"==typeof e?e(Object.assign({},t.rects,{placement:t.placement})):e)?e:C(e,i))}(c.padding,s),v=A(p),g="y"===d?e:r,y="y"===d?t:n,b=s.rects.reference[h]+s.rects.reference[d]-u[d]-s.rects.popper[h],x=u[d]-s.rects.reference[d],O=H(p),j=O?"y"===d?O.clientHeight||0:O.clientWidth||0:0,E=b/2-x/2,D=m[g],k=j-v[h]-m[y],L=j/2-v[h]/2+E,M=T(D,L,k),P=d;s.modifiersData[f]=((a={})[P]=M,a.centerOffset=M-L,a)}},effect:function(e){var t=e.state,n=e.options.element,r=void 0===n?"[data-popper-arrow]":n;null!=r&&("string"==typeof r&&!(r=t.elements.popper.querySelector(r))||!k(t.elements.popper,r)||(t.elements.arrow=r))},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function V(e){return e.split("-")[1]}var N={top:"auto",right:"auto",bottom:"auto",left:"auto"};function I(o){var i,a=o.popper,f=o.popperRect,c=o.placement,p=o.variation,u=o.offsets,l=o.position,d=o.gpuAcceleration,h=o.adaptive,m=o.roundOffsets,g=o.isFixed,y=u.x,b=void 0===y?0:y,x=u.y,w=void 0===x?0:x,O="function"==typeof m?m({x:b,y:w}):{x:b,y:w};b=O.x,w=O.y;var j=u.hasOwnProperty("x"),D=u.hasOwnProperty("y"),A=r,k=e,M=window;if(h){var W=H(a),B="clientHeight",R="clientWidth";if(W===v(a)&&("static"!==L(W=P(a)).position&&"absolute"===l&&(B="scrollHeight",R="scrollWidth")),c===e||(c===r||c===n)&&p===s)k=t,w-=(g&&W===M&&M.visualViewport?M.visualViewport.height:W[B])-f.height,w*=d?1:-1;if(c===r||(c===e||c===t)&&p===s)A=n,b-=(g&&W===M&&M.visualViewport?M.visualViewport.width:W[R])-f.width,b*=d?1:-1}var T,S=Object.assign({position:l},h&&N),C=!0===m?function(e){var t=e.x,n=e.y,r=window.devicePixelRatio||1;return{x:E(t*r)/r||0,y:E(n*r)/r||0}}({x:b,y:w}):{x:b,y:w};return b=C.x,w=C.y,d?Object.assign({},S,((T={})[k]=D?"0":"",T[A]=j?"0":"",T.transform=(M.devicePixelRatio||1)<=1?"translate("+b+"px, "+w+"px)":"translate3d("+b+"px, "+w+"px, 0)",T)):Object.assign({},S,((i={})[k]=D?w+"px":"",i[A]=j?b+"px":"",i.transform="",i))}var F={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,r=n.gpuAcceleration,o=void 0===r||r,i=n.adaptive,a=void 0===i||i,s=n.roundOffsets,f=void 0===s||s,c={placement:w(t.placement),variation:V(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:o,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,I(Object.assign({},c,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:a,roundOffsets:f})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,I(Object.assign({},c,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:f})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},U={passive:!0};var z={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,r=e.options,o=r.scroll,i=void 0===o||o,a=r.resize,s=void 0===a||a,f=v(t.elements.popper),c=[].concat(t.scrollParents.reference,t.scrollParents.popper);return i&&c.forEach((function(e){e.addEventListener("scroll",n.update,U)})),s&&f.addEventListener("resize",n.update,U),function(){i&&c.forEach((function(e){e.removeEventListener("scroll",n.update,U)})),s&&f.removeEventListener("resize",n.update,U)}},data:{}},_={left:"right",right:"left",bottom:"top",top:"bottom"};function X(e){return e.replace(/left|right|bottom|top/g,(function(e){return _[e]}))}var Y={start:"end",end:"start"};function G(e){return e.replace(/start|end/g,(function(e){return Y[e]}))}function J(e){var t=v(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function K(e){return D(P(e)).left+J(e).scrollLeft}function Q(e){var t=L(e),n=t.overflow,r=t.overflowX,o=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+o+r)}function Z(e){return["html","body","#document"].indexOf(m(e))>=0?e.ownerDocument.body:y(e)&&Q(e)?e:Z(W(e))}function $(e,t){var n;void 0===t&&(t=[]);var r=Z(e),o=r===(null==(n=e.ownerDocument)?void 0:n.body),i=v(r),a=o?[i].concat(i.visualViewport||[],Q(r)?r:[]):r,s=t.concat(a);return o?s:s.concat($(W(a)))}function ee(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function te(e,t){return t===c?ee(function(e){var t=v(e),n=P(e),r=t.visualViewport,o=n.clientWidth,i=n.clientHeight,a=0,s=0;return r&&(o=r.width,i=r.height,/^((?!chrome|android).)*safari/i.test(navigator.userAgent)||(a=r.offsetLeft,s=r.offsetTop)),{width:o,height:i,x:a+K(e),y:s}}(e)):g(t)?function(e){var t=D(e);return t.top=t.top+e.clientTop,t.left=t.left+e.clientLeft,t.bottom=t.top+e.clientHeight,t.right=t.left+e.clientWidth,t.width=e.clientWidth,t.height=e.clientHeight,t.x=t.left,t.y=t.top,t}(t):ee(function(e){var t,n=P(e),r=J(e),o=null==(t=e.ownerDocument)?void 0:t.body,i=O(n.scrollWidth,n.clientWidth,o?o.scrollWidth:0,o?o.clientWidth:0),a=O(n.scrollHeight,n.clientHeight,o?o.scrollHeight:0,o?o.clientHeight:0),s=-r.scrollLeft+K(e),f=-r.scrollTop;return"rtl"===L(o||n).direction&&(s+=O(n.clientWidth,o?o.clientWidth:0)-i),{width:i,height:a,x:s,y:f}}(P(e)))}function ne(e,t,n){var r="clippingParents"===t?function(e){var t=$(W(e)),n=["absolute","fixed"].indexOf(L(e).position)>=0&&y(e)?H(e):e;return g(n)?t.filter((function(e){return g(e)&&k(e,n)&&"body"!==m(e)})):[]}(e):[].concat(t),o=[].concat(r,[n]),i=o[0],a=o.reduce((function(t,n){var r=te(e,n);return t.top=O(r.top,t.top),t.right=j(r.right,t.right),t.bottom=j(r.bottom,t.bottom),t.left=O(r.left,t.left),t}),te(e,i));return a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}function re(o){var i,f=o.reference,c=o.element,p=o.placement,u=p?w(p):null,l=p?V(p):null,d=f.x+f.width/2-c.width/2,h=f.y+f.height/2-c.height/2;switch(u){case e:i={x:d,y:f.y-c.height};break;case t:i={x:d,y:f.y+f.height};break;case n:i={x:f.x+f.width,y:h};break;case r:i={x:f.x-c.width,y:h};break;default:i={x:f.x,y:f.y}}var m=u?R(u):null;if(null!=m){var v="y"===m?"height":"width";switch(l){case a:i[m]=i[m]-(f[v]/2-c[v]/2);break;case s:i[m]=i[m]+(f[v]/2-c[v]/2)}}return i}function oe(r,o){void 0===o&&(o={});var a=o,s=a.placement,l=void 0===s?r.placement:s,d=a.boundary,h=void 0===d?f:d,m=a.rootBoundary,v=void 0===m?c:m,y=a.elementContext,b=void 0===y?p:y,x=a.altBoundary,w=void 0!==x&&x,O=a.padding,j=void 0===O?0:O,E=S("number"!=typeof j?j:C(j,i)),A=b===p?u:p,k=r.rects.popper,L=r.elements[w?A:b],M=ne(g(L)?L:L.contextElement||P(r.elements.popper),h,v),W=D(r.elements.reference),B=re({reference:W,element:k,strategy:"absolute",placement:l}),H=ee(Object.assign({},k,B)),R=b===p?H:W,T={top:M.top-R.top+E.top,bottom:R.bottom-M.bottom+E.bottom,left:M.left-R.left+E.left,right:R.right-M.right+E.right},q=r.modifiersData.offset;if(b===p&&q){var V=q[l];Object.keys(T).forEach((function(r){var o=[n,t].indexOf(r)>=0?1:-1,i=[e,t].indexOf(r)>=0?"y":"x";T[r]+=V[i]*o}))}return T}var ie={name:"flip",enabled:!0,phase:"main",fn:function(s){var f=s.state,c=s.options,p=s.name;if(!f.modifiersData[p]._skip){for(var u=c.mainAxis,h=void 0===u||u,m=c.altAxis,v=void 0===m||m,g=c.fallbackPlacements,y=c.padding,b=c.boundary,x=c.rootBoundary,O=c.altBoundary,j=c.flipVariations,E=void 0===j||j,D=c.allowedAutoPlacements,A=f.options.placement,k=w(A),L=g||(k===A||!E?[X(A)]:function(e){if(w(e)===o)return[];var t=X(e);return[G(e),t,G(t)]}(A)),M=[A].concat(L).reduce((function(e,t){return e.concat(w(t)===o?function(e,t){void 0===t&&(t={});var n=t,r=n.placement,o=n.boundary,a=n.rootBoundary,s=n.padding,f=n.flipVariations,c=n.allowedAutoPlacements,p=void 0===c?d:c,u=V(r),h=u?f?l:l.filter((function(e){return V(e)===u})):i,m=h.filter((function(e){return p.indexOf(e)>=0}));0===m.length&&(m=h);var v=m.reduce((function(t,n){return t[n]=oe(e,{placement:n,boundary:o,rootBoundary:a,padding:s})[w(n)],t}),{});return Object.keys(v).sort((function(e,t){return v[e]-v[t]}))}(f,{placement:t,boundary:b,rootBoundary:x,padding:y,flipVariations:E,allowedAutoPlacements:D}):t)}),[]),P=f.rects.reference,W=f.rects.popper,B=new Map,H=!0,R=M[0],T=0;T<M.length;T++){var S=M[T],C=w(S),q=V(S)===a,N=[e,t].indexOf(C)>=0,I=N?"width":"height",F=oe(f,{placement:S,boundary:b,rootBoundary:x,altBoundary:O,padding:y}),U=N?q?n:r:q?t:e;P[I]>W[I]&&(U=X(U));var z=X(U),_=[];if(h&&_.push(F[C]<=0),v&&_.push(F[U]<=0,F[z]<=0),_.every((function(e){return e}))){R=S,H=!1;break}B.set(S,_)}if(H)for(var Y=function(e){var t=M.find((function(t){var n=B.get(t);if(n)return n.slice(0,e).every((function(e){return e}))}));if(t)return R=t,"break"},J=E?3:1;J>0;J--){if("break"===Y(J))break}f.placement!==R&&(f.modifiersData[p]._skip=!0,f.placement=R,f.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function ae(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function se(o){return[e,n,t,r].some((function(e){return o[e]>=0}))}var fe={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,r=t.rects.reference,o=t.rects.popper,i=t.modifiersData.preventOverflow,a=oe(t,{elementContext:"reference"}),s=oe(t,{altBoundary:!0}),f=ae(a,r),c=ae(s,o,i),p=se(f),u=se(c);t.modifiersData[n]={referenceClippingOffsets:f,popperEscapeOffsets:c,isReferenceHidden:p,hasPopperEscaped:u},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":p,"data-popper-escaped":u})}};var ce={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(t){var o=t.state,i=t.options,a=t.name,s=i.offset,f=void 0===s?[0,0]:s,c=d.reduce((function(t,i){return t[i]=function(t,o,i){var a=w(t),s=[r,e].indexOf(a)>=0?-1:1,f="function"==typeof i?i(Object.assign({},o,{placement:t})):i,c=f[0],p=f[1];return c=c||0,p=(p||0)*s,[r,n].indexOf(a)>=0?{x:p,y:c}:{x:c,y:p}}(i,o.rects,f),t}),{}),p=c[o.placement],u=p.x,l=p.y;null!=o.modifiersData.popperOffsets&&(o.modifiersData.popperOffsets.x+=u,o.modifiersData.popperOffsets.y+=l),o.modifiersData[a]=c}};var pe={name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=re({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}};var ue={name:"preventOverflow",enabled:!0,phase:"main",fn:function(o){var i=o.state,s=o.options,f=o.name,c=s.mainAxis,p=void 0===c||c,u=s.altAxis,l=void 0!==u&&u,d=s.boundary,h=s.rootBoundary,m=s.altBoundary,v=s.padding,g=s.tether,y=void 0===g||g,b=s.tetherOffset,x=void 0===b?0:b,E=oe(i,{boundary:d,rootBoundary:h,padding:v,altBoundary:m}),D=w(i.placement),k=V(i.placement),L=!k,M=R(D),P=function(e){return"x"===e?"y":"x"}(M),W=i.modifiersData.popperOffsets,B=i.rects.reference,S=i.rects.popper,C="function"==typeof x?x(Object.assign({},i.rects,{placement:i.placement})):x,q="number"==typeof C?{mainAxis:C,altAxis:C}:Object.assign({mainAxis:0,altAxis:0},C),N=i.modifiersData.offset?i.modifiersData.offset[i.placement]:null,I={x:0,y:0};if(W){if(p){var F,U="y"===M?e:r,z="y"===M?t:n,_="y"===M?"height":"width",X=W[M],Y=X+E[U],G=X-E[z],J=y?-S[_]/2:0,K=k===a?B[_]:S[_],Q=k===a?-S[_]:-B[_],Z=i.elements.arrow,$=y&&Z?A(Z):{width:0,height:0},ee=i.modifiersData["arrow#persistent"]?i.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},te=ee[U],ne=ee[z],re=T(0,B[_],$[_]),ie=L?B[_]/2-J-re-te-q.mainAxis:K-re-te-q.mainAxis,ae=L?-B[_]/2+J+re+ne+q.mainAxis:Q+re+ne+q.mainAxis,se=i.elements.arrow&&H(i.elements.arrow),fe=se?"y"===M?se.clientTop||0:se.clientLeft||0:0,ce=null!=(F=null==N?void 0:N[M])?F:0,pe=X+ae-ce,ue=T(y?j(Y,X+ie-ce-fe):Y,X,y?O(G,pe):G);W[M]=ue,I[M]=ue-X}if(l){var le,de="x"===M?e:r,he="x"===M?t:n,me=W[P],ve="y"===P?"height":"width",ge=me+E[de],ye=me-E[he],be=-1!==[e,r].indexOf(D),xe=null!=(le=null==N?void 0:N[P])?le:0,we=be?ge:me-B[ve]-S[ve]-xe+q.altAxis,Oe=be?me+B[ve]+S[ve]-xe-q.altAxis:ye,je=y&&be?function(e,t,n){var r=T(e,t,n);return r>n?n:r}(we,me,Oe):T(y?we:ge,me,y?Oe:ye);W[P]=je,I[P]=je-me}i.modifiersData[f]=I}},requiresIfExists:["offset"]};function le(e,t,n){void 0===n&&(n=!1);var r=y(t),o=y(t)&&function(e){var t=e.getBoundingClientRect(),n=E(t.width)/e.offsetWidth||1,r=E(t.height)/e.offsetHeight||1;return 1!==n||1!==r}(t),i=P(t),a=D(e,o),s={scrollLeft:0,scrollTop:0},f={x:0,y:0};return(r||!r&&!n)&&(("body"!==m(t)||Q(i))&&(s=function(e){return e!==v(e)&&y(e)?function(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}(e):J(e)}(t)),y(t)?((f=D(t,!0)).x+=t.clientLeft,f.y+=t.clientTop):i&&(f.x=K(i))),{x:a.left+s.scrollLeft-f.x,y:a.top+s.scrollTop-f.y,width:a.width,height:a.height}}function de(e){var t=new Map,n=new Set,r=[];function o(e){n.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach((function(e){if(!n.has(e)){var r=t.get(e);r&&o(r)}})),r.push(e)}return e.forEach((function(e){t.set(e.name,e)})),e.forEach((function(e){n.has(e.name)||o(e)})),r}function he(e){var t;return function(){return t||(t=new Promise((function(n){Promise.resolve().then((function(){t=void 0,n(e())}))}))),t}}var me={placement:"bottom",modifiers:[],strategy:"absolute"};function ve(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some((function(e){return!(e&&"function"==typeof e.getBoundingClientRect)}))}function ge(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,r=void 0===n?[]:n,o=t.defaultOptions,i=void 0===o?me:o;return function(e,t,n){void 0===n&&(n=i);var o={placement:"bottom",orderedModifiers:[],options:Object.assign({},me,i),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},a=[],s=!1,f={state:o,setOptions:function(n){var s="function"==typeof n?n(o.options):n;c(),o.options=Object.assign({},i,o.options,s),o.scrollParents={reference:g(e)?$(e):e.contextElement?$(e.contextElement):[],popper:$(t)};var p=function(e){var t=de(e);return h.reduce((function(e,n){return e.concat(t.filter((function(e){return e.phase===n})))}),[])}(function(e){var t=e.reduce((function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e}),{});return Object.keys(t).map((function(e){return t[e]}))}([].concat(r,o.options.modifiers)));return o.orderedModifiers=p.filter((function(e){return e.enabled})),o.orderedModifiers.forEach((function(e){var t=e.name,n=e.options,r=void 0===n?{}:n,i=e.effect;if("function"==typeof i){var s=i({state:o,name:t,instance:f,options:r}),c=function(){};a.push(s||c)}})),f.update()},forceUpdate:function(){if(!s){var e=o.elements,t=e.reference,n=e.popper;if(ve(t,n)){o.rects={reference:le(t,H(n),"fixed"===o.options.strategy),popper:A(n)},o.reset=!1,o.placement=o.options.placement,o.orderedModifiers.forEach((function(e){return o.modifiersData[e.name]=Object.assign({},e.data)}));for(var r=0;r<o.orderedModifiers.length;r++)if(!0!==o.reset){var i=o.orderedModifiers[r],a=i.fn,c=i.options,p=void 0===c?{}:c,u=i.name;"function"==typeof a&&(o=a({state:o,options:p,name:u,instance:f})||o)}else o.reset=!1,r=-1}}},update:he((function(){return new Promise((function(e){f.forceUpdate(),e(o)}))})),destroy:function(){c(),s=!0}};if(!ve(e,t))return f;function c(){a.forEach((function(e){return e()})),a=[]}return f.setOptions(n).then((function(e){!s&&n.onFirstUpdate&&n.onFirstUpdate(e)})),f}}ge(),ge({defaultModifiers:[z,pe,F,x]});var ye=ge({defaultModifiers:[z,pe,F,x,ce,ie,ue,q,fe]});export{d as E,ye as y};
