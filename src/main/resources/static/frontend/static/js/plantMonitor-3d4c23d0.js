import{K as e,F as t,Y as s,U as o,g as a,$ as i,j as l}from"./quasar-df1bac18.js";import{_ as r}from"./pagination-c4d8e88e.js";import"./vue-5bfa3a54.js";import{j as n,h as p,m,az as c,o as u,c as d,a9 as j,b as g,f as b,a8 as w,x as f,l as v,y as k,aa as y,t as h,H as x,C as _,D as z,a as C}from"./@vue-5e5cdef9.js";import{r as B}from"./icons-95011f8c.js";import{L as O}from"./ui-385bff4c.js";import{C as q,s as E}from"./@vueuse-5227c686.js";import{m as S}from"./notification-950a5f80.js";import{_ as U}from"./index-a5df0f75.js";import{o as N}from"./naive-ui-0ee0b8c3.js";import"./@babel-f3c0a00c.js";import"./@x-ui-vue3-df3ba55b.js";import"./vue-router-6159329f.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./element-plus-95e0b914.js";import"./lodash-es-ea7deab5.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./dayjs-67f8ddef.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./xe-utils-fe99d42a.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";/* empty css                *//* empty css                    */import"./vxe-table-plugin-element-be30dbb6.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./lodash-6d99edc3.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./date-fns-tz-0cc073c8.js";const V=n([{label:"站点名称",field:"plantName",align:"center",search:p("")},{label:"功率",field:"power",align:"center",search:p("")},{label:"当日发电量",field:"todayElectricity",align:"center",search:p("")},{label:"工作效率",field:"workEfficiency",align:"center",search:p("")},{label:"诊断",field:"button",align:"center",search:p("")}]),L=e=>(_("data-v-d30eed09"),e=e(),z(),e),R={class:"q-pa-md tw-h-full"},M=L((()=>C("h6",{class:"tw-text-2xl"},"电站诊断",-1))),P={key:0,class:"tw-w-1/6 tw-flex tw-justify-between"},$={key:0},A={key:1},D=L((()=>C("div",{class:"full-width row flex-center text-accent q-gutter-sm"},[C("span",null," 没有可用数据 ")],-1))),F=L((()=>C("div",{class:"text-h6"},"Close icon",-1))),H=U({__name:"plantMonitor",setup(_){let z=p([]),C=p(100),[U,L]=q();const H=B({name:"Close"});let I=n({isBreakdown:p("无故障"),attribute:p("发电量"),mode:p("峰值"),range:p("20%"),attributeOptions:p([{key:"发电量",label:"发电量"},{key:"工作效率",label:"工作效率"}]),modeOptions:p([{key:"峰值",label:"峰值"},{key:"平均值",label:"平均值"}]),rangeOptions:p([{key:"20%",label:"20%"},{key:"40%",label:"40%"}]),isBreakdownOptions:p([{key:"无故障",label:"无故障"},{key:"有故障",label:"有故障"}])});const K=new O(!1);async function Q(e={page:1,pageSize:10}){K.set(!0);try{E(z,[{plantName:"plantName",power:" power",todayElectricity:"todayElectricity",workEfficiency:"workEfficiency"}])}catch(t){S.error("电站列表请求失败！！！")}K.set(!1)}async function T(){L()}return m((async()=>{await Q()})),(n,p)=>{const m=e,_=t,B=N,O=s,q=r,E=o,S=a,L=i,W=l,X=c("skeleton-item"),Y=c("skeleton");return u(),d("div",R,[j((u(),b(E,{class:"tw-rounded-sm","row-key":"plantUid","virtual-scroll":"",separator:"cell","table-header-class":"","title-class":"tw-bg-blue-300","table-class":"tw-bg-gray-100 tw-h-full",rows:g(z),columns:g(V),"rows-per-page-options":[0],loading:g(K).value.loading,"table-style":{height:"780px"}},{top:w((()=>[M,f(m),"无故障"==g(I).isBreakdown?(u(),d("section",P,[f(B,{trigger:"hover",size:"huge",options:g(I).attributeOptions,onSelect:p[0]||(p[0]=e=>g(I).attribute=e)},{default:w((()=>[j(f(_,{class:"tw-bg-green-500 tw-text-white",label:g(I).attribute,"no-caps":"",onClick:()=>{}},null,8,["label"]),[[X]])])),_:1},8,["options"]),f(B,{trigger:"hover",size:"huge",options:g(I).modeOptions,onSelect:p[1]||(p[1]=e=>g(I).mode=e)},{default:w((()=>[j(f(_,{class:"tw-bg-yellow-500 tw-text-white",label:g(I).mode,"no-caps":"",onClick:()=>{}},null,8,["label"]),[[X]])])),_:1},8,["options"]),f(B,{trigger:"hover",size:"huge",options:g(I).rangeOptions,onSelect:p[2]||(p[2]=e=>g(I).range=e)},{default:w((()=>[j(f(_,{class:"tw-bg-orange-500 tw-text-white",label:g(I).range,"no-caps":"",onClick:()=>{}},null,8,["label"]),[[X]])])),_:1},8,["options"])])):v("",!0),f(B,{trigger:"hover",size:"huge",options:g(I).isBreakdownOptions,onSelect:p[3]||(p[3]=e=>g(I).isBreakdown=e)},{default:w((()=>[j(f(_,{class:k(["tw-text-white tw-ml-8","有故障"==g(I).isBreakdown?"tw-bg-red-600":"tw-bg-green-500"]),label:g(I).isBreakdown,"no-caps":"",onClick:()=>{}},null,8,["class","label"]),[[X]])])),_:1},8,["options"]),f(g(H))])),"body-cell":w((e=>[f(O,{props:e},{default:w((()=>["诊断"==e.col.label?(u(),d("section",$,[f(_,{onClick:T},{default:w((()=>[y(h(e.col.label),1)])),_:2},1024)])):(u(),d("section",A,h(e.value),1))])),_:2},1032,["props"])])),pagination:w((e=>[f(q,{onUpdatePage:Q,onUpdatePageSize:Q,total:g(C)},null,8,["total"])])),"no-data":w((({icon:e,message:t,filter:s})=>[D])),_:1},8,["rows","columns","loading"])),[[Y,g(K).value,void 0,{animated:!0}]]),f(W,{modelValue:g(U),"onUpdate:modelValue":p[4]||(p[4]=e=>x(U)?U.value=e:U=e),persistent:"","transition-show":"flip-down","transition-hide":"flip-up"},{default:w((()=>[f(L,null,{default:w((()=>[f(S,{class:"row items-center q-pb-none"},{default:w((()=>[F,f(m),f(g(H))])),_:1}),f(S,null,{default:w((()=>[y(" Lorem ipsum dolor sit amet consectetur adipisicing elit. Rerum repellendus sit voluptate voluptas eveniet porro. Rerum blanditiis perferendis totam, ea at omnis vel numquam exercitationem aut, natus minima, porro labore. ")])),_:1})])),_:1})])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-d30eed09"]]);export{H as default};
