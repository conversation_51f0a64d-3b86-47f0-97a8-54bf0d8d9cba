import{M as e,C as a,f as t,v as l}from"./element-plus-d975be09.js";import"./vue-5bfa3a54.js";import{V as o}from"./vxe-table-3a25f2d2.js";import{w as i}from"./v-distpicker-511d160b.js";import{u as r,e as n,g as s,a as d}from"./index-fec80322.js";import{p,a as m,b as u}from"./plant-5d7dddcf.js";import{u as c,p as f,_ as g}from"./index-8cc8d4b8.js";import{c as v}from"./tableMapUtils-2651efc6.js";import{l as j}from"./lodash-6d99edc3.js";import{x as b}from"./xe-utils-fe99d42a.js";import{e as y}from"./exportFile-7631667a.js";import{h,j as w,e as V,M as P,m as _,as as S,o as k,c as x,x as U,a8 as C,a as N,aa as D,t as z,a6 as q,b as I}from"./@vue-5e5cdef9.js";import"./lodash-es-ea7deab5.js";import"./@vueuse-af86c621.js";import"./vue-router-6159329f.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./dayjs-d60cc07f.js";import"./@babel-f3c0a00c.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./dom-zindex-5f662ad1.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./nprogress-e136a1b4.js";import"./quasar-b3f06d8a.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-7bbbb435.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./notification-950a5f80.js";import"./naive-ui-0ee0b8c3.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./date-fns-tz-0cc073c8.js";const O={plantType:"电站类型",plantCapacity:"装机容量:KWp",plantStatus:"运行状态",createTime:"建站日期",inverterNum:"逆变器数量",dailyEfficiencyPerHour:"日等效小时",yearlyEfficiencyPerHour:"年等效小时",powerDistributor:"配电箱状态",orientation:"电站朝向",salePrice:"电价",meterId:"电表编号"},M={class:"form"},T={class:"table-btn"},R={class:"block"},Y=g(Object.assign({name:"plantInfo"},{__name:"index",setup(g){const Y=c(),A=h(),H=h(),$=h(),E=h(),F=h(),W=h(!0),L=w({condition:{plantName:"",projectId:"",city:"",order:"",isAsc:"",midPlantStatus:[],multiPlantStatus:[],powerDistributor:[]},tablePage:{totalResult:0,currentPage:1,pageSize:15},modelData:{plantName:"",plantCapacity:"",meterId:"",orientation:"",address:"",createTime:"",salePrice:"",province:"",city:"",area:"",region:""},detailVis:!1}),B=V((()=>{if(L.modelData.area&&L.modelData.city&&L.modelData.province)return`${L.modelData.province}/${L.modelData.city}/${L.modelData.area}`}));P((()=>{L.modelData.region=B.value}));const G=j._.cloneDeep(L.condition),K=h(!1),Q=h(!1),Z=h(!1);j._.curry(f)("/newPlantManage/powerPlantSignage?plantUid=?plantName");const J=w({plantName:[{required:!0,message:"请输入电站名称"}],plantCapacity:[{required:!0,message:"请输入电站容量"}],orientation:[{required:!0,message:"请输入电站朝向"}],createTime:[{required:!0,message:"请输入并网时间"}],address:[{required:!0,message:"请输入地址"}],region:[{required:!0,message:"请输入地址"}],salePrice:[{required:!0,message:"请输入电价"}]}),X=w({border:"full",showFooter:!1,loading:!1,minHeight:600,height:"auto",autoResize:!0,rowConfig:{isHover:!1,isCurrent:!1},columnConfig:{resizable:!0},customConfig:{storage:{visible:!0,fixed:!0},checkMethod:({column:e})=>!["seq"].includes(e.field)},editConfig:{trigger:"click",mode:"cell"},sortConfig:{remote:!0},data:[],toolbarConfig:{slots:{tools:"toolbar_tools"}},columns:[{field:"seq",type:"seq",width:50,fixed:"left"},{field:"plantName",title:"电站名称",align:"center",fixed:"left",slots:{default:"row-plantName"}},{field:"label",title:"电站属性",align:"center"},{field:"value",title:"属性值",align:"center"},{field:"operations1",title:"操作",fixed:"right",showOverflow:!1,slots:{default:"row-operate"},width:"auto"}]}),ee=e=>e.getTime()>Date.now(),ae=({field:e,order:a})=>{L.condition.order=e,null!==a?L.condition.isAsc="asc"===a:(L.condition.order="",L.condition.isAsc=""),me()},te=w({entryNameOption:[],plantOptions:p,powerDistributorOptions:m,powerStationOrientation:[{label:"一致",value:"1"},{label:"不一致",value:"0"},{label:"无朝向",value:"-1"}]});function le({code:e,value:a}){L.modelData.province=a}function oe({code:e,value:a}){L.modelData.city=a}function ie({code:e,value:a}){L.modelData.area=a}const re=(e,a)=>{},ne=async()=>{K.value=!0;const e=await r({...L.modelData});K.value=!1,"00000"===e.status&&(o.modal.message({content:"保存成功",status:"success"}),await me()),Z.value=!1},se=e=>{me(e.currentPage,e.pageSize)};async function de(){var e;Q.value=!0;try{L.condition.multiPlantStatus=null==(e=L.condition.midPlantStatus)?void 0:e.map((e=>u[e])).flat();const a=await n({...L.condition,columnsList:X.columns.map((e=>e.field)),sheetName:""});y(a,"电站列表")}catch(a){}finally{Q.value=!1}}const pe=({row:e,_rowIndex:a,column:t,visibleData:l,columnIndex:o})=>{const i=e[t.field];if(i&&["plantName","operations1"].includes(t.field)){const e=l[a-1];let o=l[a+1];if(e&&e[t.field]===i)return{rowspan:0,colspan:0};{let e=1;for(;o&&o[t.field]===i;)o=l[++e+a];if(e>1)return{rowspan:e,colspan:1}}}},me=async(e=1,a=1)=>{var t;X.loading=!0,L.detailVis=!1,L.condition.multiPlantStatus=null==(t=L.condition.midPlantStatus)?void 0:t.map((e=>u[e])).flat(),L.tablePage.currentPage=e,L.tablePage.pageSize=a;const l=await s({...L.condition,...L.tablePage});X.loading=!1,X.data=v(l.data.records,O,"plantName"),L.tablePage.totalResult=l.data.total};return h(null),_((()=>{me(),(async()=>{const e=await d();!function e(a){var t;for(const l of a)(null==(t=null==l?void 0:l.children)?void 0:t.length)?e(l.children):delete l.children}(e.data),te.entryNameOption=e.data})(),"超级管理员"!==Y.userInfo.roleName&&"管理员"!==Y.userInfo.roleName&&(X.columns.pop(),X.columns[4].width=300)})),(o,r)=>{const n=S("vxe-input"),s=S("vxe-form-item"),d=e,p=S("vxe-button"),m=S("vxe-form"),u=a,c=S("router-link"),f=t,g=S("vxe-pager"),v=S("vxe-grid"),j=l,y=S("vxe-modal");return k(),x("div",{ref_key:"appContainerRef",ref:$,class:"app-container"},[U(v,q({id:"plantTable",ref_key:"xGrid",ref:F,class:"my-grid66"},X,{onCustom:re,"span-method":pe,onSortChange:ae}),{form:C((()=>[N("div",M,[U(m,{ref_key:"ordinaryForm",ref:H,collapseStatus:W.value,"onUpdate:collapseStatus":r[2]||(r[2]=e=>W.value=e),data:L.condition},{default:C((()=>[U(s,{field:"plantName",title:"电站名称"},{default:C((({data:e})=>[U(n,{modelValue:e.plantName,"onUpdate:modelValue":a=>e.plantName=a,clearable:"",placeholder:"请输入电站名称"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),U(s,{field:"midPlantStatus",title:"运行状态"},{default:C((({data:e})=>[U(d,{modelValue:e.midPlantStatus,"onUpdate:modelValue":a=>e.midPlantStatus=a,options:te.plantOptions,clearable:"","collapse-tags":"",multiple:"",placeholder:"请选择电站状态",style:{width:"220px"}},null,8,["modelValue","onUpdate:modelValue","options"])])),_:1}),U(s,null,{default:C((()=>[U(p,{status:"danger",onClick:r[0]||(r[0]=e=>(e=>{if("senior"===e){const e=A.value.getItems().map((e=>e.field));Object.assign(L.condition,b.pick(G,e))}else{const e=H.value.getItems().map((e=>e.field));Object.assign(L.condition,b.pick(G,e))}})("ordinary"))},{default:C((()=>[D("重置")])),_:1})])),_:1}),U(s,null,{default:C((()=>[U(p,{status:"primary",onClick:r[1]||(r[1]=e=>me(1))},{default:C((()=>[D("查询")])),_:1})])),_:1})])),_:1},8,["collapseStatus","data"])])])),toolbar_tools:C((()=>[N("div",T,[U(p,{status:"primary",onClick:de,loading:Q.value},{default:C((()=>[D("导出")])),_:1},8,["loading"])])])),header:C((({row:e})=>[])),"row-plantStatus":C((({row:e})=>[U(u,{type:"正常"===e.plantStatus?"success":"warning"},{default:C((()=>[D(z(e.plantStatus),1)])),_:2},1032,["type"])])),"row-plantName":C((({row:e})=>[U(c,{to:{path:"/newPlantManage/powerPlantSignage",query:{plantUid:e.plantUid,plantName:e.plantName}},class:"link",target:"_blank"},{default:C((()=>[D(z(e.plantName),1)])),_:2},1032,["to"])])),"row-operate":C((({row:e})=>[U(f,{link:"",type:"primary",onClick:a=>(e=>{Object.assign(L.modelData,e),Z.value=!0})(e)},{default:C((()=>[D("编辑")])),_:2},1032,["onClick"])])),bottom:C((()=>[])),pager:C((()=>[U(g,{"current-page":L.tablePage.currentPage,"onUpdate:currentPage":r[3]||(r[3]=e=>L.tablePage.currentPage=e),"page-size":L.tablePage.pageSize,"onUpdate:pageSize":r[4]||(r[4]=e=>L.tablePage.pageSize=e),"page-sizes":[1,2,5],total:L.tablePage.totalResult,perfect:"",onPageChange:se},null,8,["current-page","page-size","total"])])),_:1},16),U(y,{modelValue:Z.value,"onUpdate:modelValue":r[6]||(r[6]=e=>Z.value=e),loading:K.value,"destroy-on-close":"",ref_key:"modelRef",ref:E,escClosable:"","min-height":"300","min-width":"600",resize:"",title:"编辑",width:"800"},{default:C((()=>[U(m,{data:L.modelData,rules:J,"title-align":"right","title-width":"100",onSubmit:ne},{default:C((()=>[U(s,{"item-render":{},span:24,field:"plantName",title:"电站名称"},{default:C((({data:e})=>[U(n,{modelValue:e.plantName,"onUpdate:modelValue":a=>e.plantName=a,placeholder:"请输入电站名称"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),U(s,{"item-render":{},span:24,field:"plantCapacity",title:"电站容量"},{default:C((({data:e})=>[U(n,{modelValue:e.plantCapacity,"onUpdate:modelValue":a=>e.plantCapacity=a,placeholder:"请输入电站容量"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),U(s,{"item-render":{},span:24,field:"meterId",title:"电表编号"},{default:C((({data:e})=>[U(n,{modelValue:e.meterId,"onUpdate:modelValue":a=>e.meterId=a,placeholder:"请输入电表编号"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),U(s,{"item-render":{},span:24,field:"orientation",title:"电站朝向"},{default:C((({data:e})=>[U(d,{modelValue:e.orientation,"onUpdate:modelValue":a=>e.orientation=a,options:te.powerStationOrientation,clearable:"",placeholder:"请输入电站朝向","popper-class":"Pc"},null,8,["modelValue","onUpdate:modelValue","options"])])),_:1}),U(s,{field:"region",title:"地址","title-width":"100"},{default:C((({data:e})=>[U(I(i),{province:e.province,city:e.city,area:e.area,onProvince:le,onCity:oe,onArea:ie},null,8,["province","city","area"])])),_:1}),U(s,{"item-render":{},span:24,field:"address",title:"电站地址"},{default:C((({data:e})=>[U(n,{modelValue:e.address,"onUpdate:modelValue":a=>e.address=a,placeholder:"请输入电站地址"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),U(s,{"item-render":{},span:24,field:"createTime",title:"并网时间"},{default:C((({data:e})=>[N("div",R,[U(j,{modelValue:e.createTime,"onUpdate:modelValue":a=>e.createTime=a,"disabled-date":ee,"date-format":"YYYY-MM-DD",placeholder:"请选择并网时间","popper-class":"Pc",type:"date","value-format":"YYYY-MM-DD"},null,8,["modelValue","onUpdate:modelValue"])])])),_:1}),U(s,{"item-render":{},span:24,field:"salePrice",title:"电价设置"},{default:C((({data:e})=>[U(n,{modelValue:e.salePrice,"onUpdate:modelValue":a=>e.salePrice=a,placeholder:"请输入电价￥"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),U(s,{span:24,align:"center","title-align":"left"},{default:C((()=>[U(p,{type:"submit"},{default:C((()=>[D("提交")])),_:1}),U(p,{type:"cancel",onClick:r[5]||(r[5]=e=>Z.value=!1)},{default:C((()=>[D("取消")])),_:1})])),_:1})])),_:1},8,["data","rules"])])),_:1},8,["modelValue","loading"])],512)}}}),[["__scopeId","data-v-129edad8"]]);export{Y as default};
