import{p as e}from"./proxyUtil-6f30f7ef.js";import"./vue-5bfa3a54.js";import{j as i}from"./@vue-5e5cdef9.js";function t(t){const a=new e({page:1,pageSize:10,pageSizes:[10,20,30,40],total:10,displayOrder:["quick-jumper","pages","size-picker"],updatePagination:t,history:null,mounted(){this.setProxy((async(e,i,t)=>{var a;e[i]=t,"page"!==i&&"pageSize"!==i||("pageSize"===i&&(this.page=1),null==(a=this.history)||a.commit(),await this.updatePagination())}))}});return i(a)}export{t as c};
