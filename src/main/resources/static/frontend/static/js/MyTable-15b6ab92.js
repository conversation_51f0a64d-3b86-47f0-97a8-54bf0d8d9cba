import{Y as s,a as o,U as a}from"./quasar-df1bac18.js";import"./vue-5bfa3a54.js";import{_ as l}from"./index-a5df0f75.js";import{_ as r}from"./naive-ui-0ee0b8c3.js";import{o as t,c as e,x as p,a8 as i,g as m,F as u,k as c,f as n,an as d,a6 as f,y as w,aa as v,t as _}from"./@vue-5e5cdef9.js";const y=l({__name:"MyTable",props:["rowKey","rows","columns","visibleColumns","class"],setup(l){const y=l;return(b,k)=>{const j=r,x=s,$=o,g=a;return t(),e("main",{class:w(y.class??"tw-h-full tw-w-full")},[p(g,{"row-key":l.row<PERSON><PERSON>,separator:"cell",rows:l.rows,columns:l.columns,"rows-per-page-options":[0],"visible-columns":y.visibleColumns},{top:i((()=>[m(b.$slots,"top",{},void 0,!0)])),body:i((s=>[p($,{props:s},{default:i((()=>[(t(!0),e(u,null,c(l.columns,(o=>(t(),n(x,{key:o.field,props:s},{default:i((()=>[o.slot?m(b.$slots,o.slot,d(f({key:0},{col:o,props:s})),void 0,!0):(t(),n(j,{key:1,class:w(o.class??"")},{default:i((()=>[v(_(s.row[o.field]),1)])),_:2},1032,["class"]))])),_:2},1032,["props"])))),128))])),_:2},1032,["props"])])),bottom:i((()=>[m(b.$slots,"bottom",{},void 0,!0)])),_:3},8,["row-key","rows","columns","visible-columns"])],2)}}},[["__scopeId","data-v-dcfd51f5"]]);export{y as _};
