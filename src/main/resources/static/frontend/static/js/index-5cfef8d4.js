import{v as e,M as t,C as a}from"./element-plus-d975be09.js";import"./vue-5bfa3a54.js";import"./vxe-table-3a25f2d2.js";import"./plant-5d7dddcf.js";import{f as o,h as i,i as s,j as l}from"./index-2b100cb4.js";import{c as r}from"./tableMapUtils-2651efc6.js";import{p as n}from"./@vueuse-af86c621.js";import{d as p}from"./dayjs-d60cc07f.js";import{l as m}from"./lodash-6d99edc3.js";import{x as d}from"./xe-utils-fe99d42a.js";import{e as u}from"./exportFile-7631667a.js";import{h as c,j,m as g,as as f,o as v,c as b,x as y,a8 as h,a as _,aa as x,t as w,a6 as k}from"./@vue-5e5cdef9.js";import{_ as P}from"./index-8cc8d4b8.js";import"./lodash-es-ea7deab5.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./@babel-f3c0a00c.js";import"./dom-zindex-5f662ad1.js";import"./vue-router-6159329f.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./notification-950a5f80.js";import"./naive-ui-0ee0b8c3.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./date-fns-tz-0cc073c8.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./nprogress-e136a1b4.js";import"./quasar-b3f06d8a.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-7bbbb435.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";const N={plantType:"电站类型",deviceId:"设备编号",deviceType:"告警设备",startTime:"开始时间",alarmMean:"告警信息",source:"告警来源"},z={class:"app-container u-wh-full u-flex-column u-gap-10"},C={class:"form"},T={class:"table-btn"},S=P(Object.assign({name:"alarmManage"},{__name:"index",setup(P){const S=c(),V=c(),U=c(),I=c(!0);n("status");const M=n("deviceType"),q=c(!1),D=j({condition:{plantName:"",alarmInfo:"",deviceId:"",deviceType:[],source:[],alarmLevel:[],alarmStatus:[],startTime:p().format("YYYY-MM-DD")},tablePage:{totalResult:0,currentPage:1,pageSize:15},modelData:{plantName:"",userPhone:"",creator:"",plantCapacity:"",projectId:"",address:"",userName:"",contractId:""},batchData:[{address:"",contractId:"",plantCapacity:"",plantName:"",projectId:"",userName:"",userPhone:""}],creator:""}),Y=m._.omit(m._.cloneDeep(D.condition)),O=j({border:"full",showFooter:!1,loading:!1,minHeight:600,height:"auto",autoResize:!0,rowConfig:{isHover:!1,isCurrent:!1},customConfig:{storage:{visible:!0,fixed:!0},checkMethod:({column:e})=>!["seq"].includes(e.field)},editConfig:{trigger:"click",mode:"cell"},sortConfig:{remote:!0},data:[],toolbarConfig:{slots:{buttons:"toolbar_buttons",tools:"toolbar_tools"}},columns:[{field:"seq",type:"seq",width:50},{field:"plantName",title:"电站名称",width:"auto",slots:{default:"row-plantName"}},{field:"label",title:"电站属性",align:"center"},{field:"value",title:"属性值",align:"center"}]}),R=j({entryNameOption:[],alarmEquipmentOptions:o,alarmSourceOptions:i}),L=e=>e.getTime()>Date.now(),A=({field:e,order:t})=>{D.condition.order=e,null!==t?D.condition.isAsc="asc"===t:(D.condition.order="",D.condition.isAsc=""),B()},E=(e,t)=>{},F=e=>{B(e.currentPage,e.pageSize)};async function H(){try{q.value=!0;const e=await s({...D.condition,columnsList:O.columns.map((e=>e.field)),sheetName:""});u(e,"报警列表")}catch(e){}finally{q.value=!1}}const W=({row:e,_rowIndex:t,column:a,visibleData:o,columnIndex:i})=>{const s=e[a.field];if(s&&["plantName","operations1"].includes(a.field)){const e=o[t-1];let i=o[t+1];if(e&&e[a.field]===s)return{rowspan:0,colspan:0};{let e=1;for(;i&&i[a.field]===s;)i=o[++e+t];if(e>1)return{rowspan:e,colspan:1}}}},B=async(e=1,t=1)=>{var a;O.loading=!0,D.tablePage.currentPage=e,D.tablePage.pageSize=t;try{const e=await l({...D.condition,...D.tablePage});O.data=r(e.data.records,N,"plantName"),D.tablePage.totalResult=null==(a=e.data)?void 0:a.total}catch{O.loading=!1}O.loading=!1};return g((()=>{M.value&&(D.condition.deviceType=M.value),B()})),(o,i)=>{const s=f("vxe-input"),l=f("vxe-form-item"),r=e,n=t,p=f("vxe-button"),m=f("vxe-form"),u=a,c=f("router-link"),j=f("vxe-pager"),g=f("vxe-grid");return v(),b("div",z,[y(g,k({id:"alarmListTable",ref_key:"xGrid",ref:U,class:"my-grid66"},O,{onCustom:E,"span-method":W,onSortChange:A}),{form:h((()=>[_("div",C,[y(m,{ref_key:"ordinaryForm",ref:V,collapseStatus:I.value,"onUpdate:collapseStatus":i[2]||(i[2]=e=>I.value=e),data:D.condition},{default:h((()=>[y(l,{field:"plantName",title:"电站名称"},{default:h((({data:e})=>[y(s,{modelValue:e.plantName,"onUpdate:modelValue":t=>e.plantName=t,clearable:"",placeholder:"请输入电站名称"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),y(l,{field:"startTime",title:"开始时间"},{default:h((({data:e})=>[y(r,{modelValue:e.startTime,"onUpdate:modelValue":t=>e.startTime=t,"disabled-date":L,placeholder:"开始时间",type:"date","value-format":"YYYY-MM-DD"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),y(l,{field:"deviceType",title:"告警设备"},{default:h((({data:e})=>[y(n,{modelValue:e.deviceType,"onUpdate:modelValue":t=>e.deviceType=t,options:R.alarmEquipmentOptions,clearable:"",style:{width:"180px"},placeholder:"请输入告警设备"},null,8,["modelValue","onUpdate:modelValue","options"])])),_:1}),y(l,null,{default:h((()=>[y(p,{status:"danger",onClick:i[0]||(i[0]=e=>(e=>{if("senior"===e){const e=S.value.getItems().map((e=>e.field));Object.assign(D.condition,d.pick(Y,e))}else{const e=V.value.getItems().map((e=>e.field));Object.assign(D.condition,d.pick(Y,e))}})("ordinary"))},{default:h((()=>[x("重置")])),_:1})])),_:1}),y(l,null,{default:h((()=>[y(p,{status:"primary",onClick:i[1]||(i[1]=e=>B(1))},{default:h((()=>[x("查询")])),_:1})])),_:1})])),_:1},8,["collapseStatus","data"])])])),toolbar_buttons:h((()=>[])),toolbar_tools:h((()=>[_("div",T,[y(p,{status:"primary",onClick:H,loading:q.value},{default:h((()=>[x("导出")])),_:1},8,["loading"])])])),top:h((()=>[])),"row-state":h((({row:e})=>[y(u,{type:1===e.state?"success":"warning"},{default:h((()=>[x(w(1===e.state?"已注册":"未注册"),1)])),_:2},1032,["type"])])),"row-plantName":h((({row:e})=>[y(c,{to:{path:"/newPlantManage/powerPlantSignage",query:{plantUid:e.plantUid,plantName:e.plantName}},class:"link",target:"_blank"},{default:h((()=>[x(w(e.plantName),1)])),_:2},1032,["to"])])),bottom:h((()=>[])),pager:h((()=>[y(j,{"current-page":D.tablePage.currentPage,"onUpdate:currentPage":i[3]||(i[3]=e=>D.tablePage.currentPage=e),"page-size":D.tablePage.pageSize,"onUpdate:pageSize":i[4]||(i[4]=e=>D.tablePage.pageSize=e),"page-sizes":[1,2,5],total:D.tablePage.totalResult,perfect:"",onPageChange:F},null,8,["current-page","page-size","total"])])),_:1},16)])}}}),[["__scopeId","data-v-5b66b1e3"]]);export{S as default};
