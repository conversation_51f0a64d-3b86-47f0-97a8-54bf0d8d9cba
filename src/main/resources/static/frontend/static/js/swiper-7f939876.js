import"./vue-5bfa3a54.js";import{h as e,N as t,a5 as i,w as s,n as r,m as a,p as n,A as o,at as l,e as d}from"./@vue-5e5cdef9.js";function p(e){return null!==e&&"object"==typeof e&&"constructor"in e&&e.constructor===Object}function c(e,t){void 0===e&&(e={}),void 0===t&&(t={}),Object.keys(t).forEach((i=>{void 0===e[i]?e[i]=t[i]:p(t[i])&&p(e[i])&&Object.keys(t[i]).length>0&&c(e[i],t[i])}))}const u={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector:()=>null,querySelectorAll:()=>[],getElementById:()=>null,createEvent:()=>({initEvent(){}}),createElement:()=>({children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName:()=>[]}),createElementNS:()=>({}),importNode:()=>null,location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function v(){const e="undefined"!=typeof document?document:{};return c(e,u),e}const f={document:u,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle:()=>({getPropertyValue:()=>""}),Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia:()=>({}),requestAnimationFrame:e=>"undefined"==typeof setTimeout?(e(),null):setTimeout(e,0),cancelAnimationFrame(e){"undefined"!=typeof setTimeout&&clearTimeout(e)}};function m(){const e="undefined"!=typeof window?window:{};return c(e,f),e}function h(e,t){return void 0===t&&(t=0),setTimeout(e,t)}function g(){return Date.now()}function w(e,t){void 0===t&&(t="x");const i=m();let s,r,a;const n=function(e){const t=m();let i;return t.getComputedStyle&&(i=t.getComputedStyle(e,null)),!i&&e.currentStyle&&(i=e.currentStyle),i||(i=e.style),i}(e);return i.WebKitCSSMatrix?(r=n.transform||n.webkitTransform,r.split(",").length>6&&(r=r.split(", ").map((e=>e.replace(",","."))).join(", ")),a=new i.WebKitCSSMatrix("none"===r?"":r)):(a=n.MozTransform||n.OTransform||n.MsTransform||n.msTransform||n.transform||n.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),s=a.toString().split(",")),"x"===t&&(r=i.WebKitCSSMatrix?a.m41:16===s.length?parseFloat(s[12]):parseFloat(s[4])),"y"===t&&(r=i.WebKitCSSMatrix?a.m42:16===s.length?parseFloat(s[13]):parseFloat(s[5])),r||0}function y(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)}function S(){const e=Object(arguments.length<=0?void 0:arguments[0]),t=["__proto__","constructor","prototype"];for(let s=1;s<arguments.length;s+=1){const r=s<0||arguments.length<=s?void 0:arguments[s];if(null!=r&&(i=r,!("undefined"!=typeof window&&void 0!==window.HTMLElement?i instanceof HTMLElement:i&&(1===i.nodeType||11===i.nodeType)))){const i=Object.keys(Object(r)).filter((e=>t.indexOf(e)<0));for(let t=0,s=i.length;t<s;t+=1){const s=i[t],a=Object.getOwnPropertyDescriptor(r,s);void 0!==a&&a.enumerable&&(y(e[s])&&y(r[s])?r[s].__swiper__?e[s]=r[s]:S(e[s],r[s]):!y(e[s])&&y(r[s])?(e[s]={},r[s].__swiper__?e[s]=r[s]:S(e[s],r[s])):e[s]=r[s])}}}var i;return e}function b(e,t,i){e.style.setProperty(t,i)}function T(e){let{swiper:t,targetPosition:i,side:s}=e;const r=m(),a=-t.translate;let n,o=null;const l=t.params.speed;t.wrapperEl.style.scrollSnapType="none",r.cancelAnimationFrame(t.cssModeFrameID);const d=i>a?"next":"prev",p=(e,t)=>"next"===d&&e>=t||"prev"===d&&e<=t,c=()=>{n=(new Date).getTime(),null===o&&(o=n);const e=Math.max(Math.min((n-o)/l,1),0),d=.5-Math.cos(e*Math.PI)/2;let u=a+d*(i-a);if(p(u,i)&&(u=i),t.wrapperEl.scrollTo({[s]:u}),p(u,i))return t.wrapperEl.style.overflow="hidden",t.wrapperEl.style.scrollSnapType="",setTimeout((()=>{t.wrapperEl.style.overflow="",t.wrapperEl.scrollTo({[s]:u})})),void r.cancelAnimationFrame(t.cssModeFrameID);t.cssModeFrameID=r.requestAnimationFrame(c)};c()}function E(e,t){return void 0===t&&(t=""),[...e.children].filter((e=>e.matches(t)))}function x(e,t){return m().getComputedStyle(e,null).getPropertyValue(t)}function C(e){let t,i=e;if(i){for(t=0;null!==(i=i.previousSibling);)1===i.nodeType&&(t+=1);return t}}function P(e,t,i){const s=m();return i?e["width"===t?"offsetWidth":"offsetHeight"]+parseFloat(s.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-right":"margin-top"))+parseFloat(s.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-left":"margin-bottom")):e.offsetWidth}let M,O,L;function k(){return M||(M=function(){const e=m(),t=v();return{smoothScroll:t.documentElement&&t.documentElement.style&&"scrollBehavior"in t.documentElement.style,touch:!!("ontouchstart"in e||e.DocumentTouch&&t instanceof e.DocumentTouch)}}()),M}function I(e){return void 0===e&&(e={}),O||(O=function(e){let{userAgent:t}=void 0===e?{}:e;const i=k(),s=m(),r=s.navigator.platform,a=t||s.navigator.userAgent,n={ios:!1,android:!1},o=s.screen.width,l=s.screen.height,d=a.match(/(Android);?[\s\/]+([\d.]+)?/);let p=a.match(/(iPad).*OS\s([\d_]+)/);const c=a.match(/(iPod)(.*OS\s([\d_]+))?/),u=!p&&a.match(/(iPhone\sOS|iOS)\s([\d_]+)/),v="Win32"===r;let f="MacIntel"===r;return!p&&f&&i.touch&&["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf(`${o}x${l}`)>=0&&(p=a.match(/(Version)\/([\d.]+)/),p||(p=[0,1,"13_0_0"]),f=!1),d&&!v&&(n.os="android",n.android=!0),(p||u||c)&&(n.os="ios",n.ios=!0),n}(e)),O}function _(){return L||(L=function(){const e=m();let t=!1;function i(){const t=e.navigator.userAgent.toLowerCase();return t.indexOf("safari")>=0&&t.indexOf("chrome")<0&&t.indexOf("android")<0}if(i()){const i=String(e.navigator.userAgent);if(i.includes("Version/")){const[e,s]=i.split("Version/")[1].split(" ")[0].split(".").map((e=>Number(e)));t=e<16||16===e&&s<2}}return{isSafari:t||i(),needPerspectiveFix:t,isWebView:/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(e.navigator.userAgent)}}()),L}const z=(e,t)=>{if(!e||e.destroyed||!e.params)return;const i=t.closest(e.isElement?"swiper-slide":`.${e.params.slideClass}`);if(i){let t=i.querySelector(`.${e.params.lazyPreloaderClass}`);!t&&e.isElement&&(i.shadowRoot?t=i.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`):requestAnimationFrame((()=>{i.shadowRoot&&(t=i.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`),t&&t.remove())}))),t&&t.remove()}},A=(e,t)=>{if(!e.slides[t])return;const i=e.slides[t].querySelector('[loading="lazy"]');i&&i.removeAttribute("loading")},B=e=>{if(!e||e.destroyed||!e.params)return;let t=e.params.lazyPreloadPrevNext;const i=e.slides.length;if(!i||!t||t<0)return;t=Math.min(t,i);const s="auto"===e.params.slidesPerView?e.slidesPerViewDynamic():Math.ceil(e.params.slidesPerView),r=e.activeIndex;if(e.params.grid&&e.params.grid.rows>1){const i=r,a=[i-t];return a.push(...Array.from({length:t}).map(((e,t)=>i+s+t))),void e.slides.forEach(((t,i)=>{a.includes(t.column)&&A(e,i)}))}const a=r+s-1;if(e.params.rewind||e.params.loop)for(let n=r-t;n<=a+t;n+=1){const t=(n%i+i)%i;(t<r||t>a)&&A(e,t)}else for(let n=Math.max(r-t,0);n<=Math.min(a+t,i-1);n+=1)n!==r&&(n>a||n<r)&&A(e,n)};function N(e){let{swiper:t,runCallbacks:i,direction:s,step:r}=e;const{activeIndex:a,previousIndex:n}=t;let o=s;if(o||(o=a>n?"next":a<n?"prev":"reset"),t.emit(`transition${r}`),i&&a!==n){if("reset"===o)return void t.emit(`slideResetTransition${r}`);t.emit(`slideChangeTransition${r}`),"next"===o?t.emit(`slideNextTransition${r}`):t.emit(`slidePrevTransition${r}`)}}function G(e){const t=this,i=v(),s=m(),r=t.touchEventsData;r.evCache.push(e);const{params:a,touches:n,enabled:o}=t;if(!o)return;if(!a.simulateTouch&&"mouse"===e.pointerType)return;if(t.animating&&a.preventInteractionOnTransition)return;!t.animating&&a.cssMode&&a.loop&&t.loopFix();let l=e;l.originalEvent&&(l=l.originalEvent);let d=l.target;if("wrapper"===a.touchEventsTarget&&!t.wrapperEl.contains(d))return;if("which"in l&&3===l.which)return;if("button"in l&&l.button>0)return;if(r.isTouched&&r.isMoved)return;const p=!!a.noSwipingClass&&""!==a.noSwipingClass,c=e.composedPath?e.composedPath():e.path;p&&l.target&&l.target.shadowRoot&&c&&(d=c[0]);const u=a.noSwipingSelector?a.noSwipingSelector:`.${a.noSwipingClass}`,f=!(!l.target||!l.target.shadowRoot);if(a.noSwiping&&(f?function(e,t){return void 0===t&&(t=this),function t(i){if(!i||i===v()||i===m())return null;i.assignedSlot&&(i=i.assignedSlot);const s=i.closest(e);return s||i.getRootNode?s||t(i.getRootNode().host):null}(t)}(u,d):d.closest(u)))return void(t.allowClick=!0);if(a.swipeHandler&&!d.closest(a.swipeHandler))return;n.currentX=l.pageX,n.currentY=l.pageY;const h=n.currentX,w=n.currentY,y=a.edgeSwipeDetection||a.iOSEdgeSwipeDetection,S=a.edgeSwipeThreshold||a.iOSEdgeSwipeThreshold;if(y&&(h<=S||h>=s.innerWidth-S)){if("prevent"!==y)return;e.preventDefault()}Object.assign(r,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),n.startX=h,n.startY=w,r.touchStartTime=g(),t.allowClick=!0,t.updateSize(),t.swipeDirection=void 0,a.threshold>0&&(r.allowThresholdMove=!1);let b=!0;d.matches(r.focusableElements)&&(b=!1,"SELECT"===d.nodeName&&(r.isTouched=!1)),i.activeElement&&i.activeElement.matches(r.focusableElements)&&i.activeElement!==d&&i.activeElement.blur();const T=b&&t.allowTouchMove&&a.touchStartPreventDefault;!a.touchStartForcePreventDefault&&!T||d.isContentEditable||l.preventDefault(),a.freeMode&&a.freeMode.enabled&&t.freeMode&&t.animating&&!a.cssMode&&t.freeMode.onTouchStart(),t.emit("touchStart",l)}function D(e){const t=v(),i=this,s=i.touchEventsData,{params:r,touches:a,rtlTranslate:n,enabled:o}=i;if(!o)return;if(!r.simulateTouch&&"mouse"===e.pointerType)return;let l=e;if(l.originalEvent&&(l=l.originalEvent),!s.isTouched)return void(s.startMoving&&s.isScrolling&&i.emit("touchMoveOpposite",l));const d=s.evCache.findIndex((e=>e.pointerId===l.pointerId));d>=0&&(s.evCache[d]=l);const p=s.evCache.length>1?s.evCache[0]:l,c=p.pageX,u=p.pageY;if(l.preventedByNestedSwiper)return a.startX=c,void(a.startY=u);if(!i.allowTouchMove)return l.target.matches(s.focusableElements)||(i.allowClick=!1),void(s.isTouched&&(Object.assign(a,{startX:c,startY:u,prevX:i.touches.currentX,prevY:i.touches.currentY,currentX:c,currentY:u}),s.touchStartTime=g()));if(r.touchReleaseOnEdges&&!r.loop)if(i.isVertical()){if(u<a.startY&&i.translate<=i.maxTranslate()||u>a.startY&&i.translate>=i.minTranslate())return s.isTouched=!1,void(s.isMoved=!1)}else if(c<a.startX&&i.translate<=i.maxTranslate()||c>a.startX&&i.translate>=i.minTranslate())return;if(t.activeElement&&l.target===t.activeElement&&l.target.matches(s.focusableElements))return s.isMoved=!0,void(i.allowClick=!1);if(s.allowTouchCallbacks&&i.emit("touchMove",l),l.targetTouches&&l.targetTouches.length>1)return;a.currentX=c,a.currentY=u;const f=a.currentX-a.startX,m=a.currentY-a.startY;if(i.params.threshold&&Math.sqrt(f**2+m**2)<i.params.threshold)return;if(void 0===s.isScrolling){let e;i.isHorizontal()&&a.currentY===a.startY||i.isVertical()&&a.currentX===a.startX?s.isScrolling=!1:f*f+m*m>=25&&(e=180*Math.atan2(Math.abs(m),Math.abs(f))/Math.PI,s.isScrolling=i.isHorizontal()?e>r.touchAngle:90-e>r.touchAngle)}if(s.isScrolling&&i.emit("touchMoveOpposite",l),void 0===s.startMoving&&(a.currentX===a.startX&&a.currentY===a.startY||(s.startMoving=!0)),s.isScrolling||i.zoom&&i.params.zoom&&i.params.zoom.enabled&&s.evCache.length>1)return void(s.isTouched=!1);if(!s.startMoving)return;i.allowClick=!1,!r.cssMode&&l.cancelable&&l.preventDefault(),r.touchMoveStopPropagation&&!r.nested&&l.stopPropagation();let h=i.isHorizontal()?f:m,w=i.isHorizontal()?a.currentX-a.previousX:a.currentY-a.previousY;r.oneWayMovement&&(h=Math.abs(h)*(n?1:-1),w=Math.abs(w)*(n?1:-1)),a.diff=h,h*=r.touchRatio,n&&(h=-h,w=-w);const y=i.touchesDirection;i.swipeDirection=h>0?"prev":"next",i.touchesDirection=w>0?"prev":"next";const S=i.params.loop&&!r.cssMode,b="next"===i.swipeDirection&&i.allowSlideNext||"prev"===i.swipeDirection&&i.allowSlidePrev;if(!s.isMoved){if(S&&b&&i.loopFix({direction:i.swipeDirection}),s.startTranslate=i.getTranslate(),i.setTransition(0),i.animating){const e=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0});i.wrapperEl.dispatchEvent(e)}s.allowMomentumBounce=!1,!r.grabCursor||!0!==i.allowSlideNext&&!0!==i.allowSlidePrev||i.setGrabCursor(!0),i.emit("sliderFirstMove",l)}let T;s.isMoved&&y!==i.touchesDirection&&S&&b&&Math.abs(h)>=1&&(i.loopFix({direction:i.swipeDirection,setTranslate:!0}),T=!0),i.emit("sliderMove",l),s.isMoved=!0,s.currentTranslate=h+s.startTranslate;let E=!0,x=r.resistanceRatio;if(r.touchReleaseOnEdges&&(x=0),h>0?(S&&b&&!T&&s.currentTranslate>(r.centeredSlides?i.minTranslate()-i.size/2:i.minTranslate())&&i.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),s.currentTranslate>i.minTranslate()&&(E=!1,r.resistance&&(s.currentTranslate=i.minTranslate()-1+(-i.minTranslate()+s.startTranslate+h)**x))):h<0&&(S&&b&&!T&&s.currentTranslate<(r.centeredSlides?i.maxTranslate()+i.size/2:i.maxTranslate())&&i.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:i.slides.length-("auto"===r.slidesPerView?i.slidesPerViewDynamic():Math.ceil(parseFloat(r.slidesPerView,10)))}),s.currentTranslate<i.maxTranslate()&&(E=!1,r.resistance&&(s.currentTranslate=i.maxTranslate()+1-(i.maxTranslate()-s.startTranslate-h)**x))),E&&(l.preventedByNestedSwiper=!0),!i.allowSlideNext&&"next"===i.swipeDirection&&s.currentTranslate<s.startTranslate&&(s.currentTranslate=s.startTranslate),!i.allowSlidePrev&&"prev"===i.swipeDirection&&s.currentTranslate>s.startTranslate&&(s.currentTranslate=s.startTranslate),i.allowSlidePrev||i.allowSlideNext||(s.currentTranslate=s.startTranslate),r.threshold>0){if(!(Math.abs(h)>r.threshold||s.allowThresholdMove))return void(s.currentTranslate=s.startTranslate);if(!s.allowThresholdMove)return s.allowThresholdMove=!0,a.startX=a.currentX,a.startY=a.currentY,s.currentTranslate=s.startTranslate,void(a.diff=i.isHorizontal()?a.currentX-a.startX:a.currentY-a.startY)}r.followFinger&&!r.cssMode&&((r.freeMode&&r.freeMode.enabled&&i.freeMode||r.watchSlidesProgress)&&(i.updateActiveIndex(),i.updateSlidesClasses()),r.freeMode&&r.freeMode.enabled&&i.freeMode&&i.freeMode.onTouchMove(),i.updateProgress(s.currentTranslate),i.setTranslate(s.currentTranslate))}function j(e){const t=this,i=t.touchEventsData,s=i.evCache.findIndex((t=>t.pointerId===e.pointerId));if(s>=0&&i.evCache.splice(s,1),["pointercancel","pointerout","pointerleave","contextmenu"].includes(e.type)){if(!(["pointercancel","contextmenu"].includes(e.type)&&(t.browser.isSafari||t.browser.isWebView)))return}const{params:r,touches:a,rtlTranslate:n,slidesGrid:o,enabled:l}=t;if(!l)return;if(!r.simulateTouch&&"mouse"===e.pointerType)return;let d=e;if(d.originalEvent&&(d=d.originalEvent),i.allowTouchCallbacks&&t.emit("touchEnd",d),i.allowTouchCallbacks=!1,!i.isTouched)return i.isMoved&&r.grabCursor&&t.setGrabCursor(!1),i.isMoved=!1,void(i.startMoving=!1);r.grabCursor&&i.isMoved&&i.isTouched&&(!0===t.allowSlideNext||!0===t.allowSlidePrev)&&t.setGrabCursor(!1);const p=g(),c=p-i.touchStartTime;if(t.allowClick){const e=d.path||d.composedPath&&d.composedPath();t.updateClickedSlide(e&&e[0]||d.target,e),t.emit("tap click",d),c<300&&p-i.lastClickTime<300&&t.emit("doubleTap doubleClick",d)}if(i.lastClickTime=g(),h((()=>{t.destroyed||(t.allowClick=!0)})),!i.isTouched||!i.isMoved||!t.swipeDirection||0===a.diff||i.currentTranslate===i.startTranslate)return i.isTouched=!1,i.isMoved=!1,void(i.startMoving=!1);let u;if(i.isTouched=!1,i.isMoved=!1,i.startMoving=!1,u=r.followFinger?n?t.translate:-t.translate:-i.currentTranslate,r.cssMode)return;if(r.freeMode&&r.freeMode.enabled)return void t.freeMode.onTouchEnd({currentPos:u});let v=0,f=t.slidesSizesGrid[0];for(let h=0;h<o.length;h+=h<r.slidesPerGroupSkip?1:r.slidesPerGroup){const e=h<r.slidesPerGroupSkip-1?1:r.slidesPerGroup;void 0!==o[h+e]?u>=o[h]&&u<o[h+e]&&(v=h,f=o[h+e]-o[h]):u>=o[h]&&(v=h,f=o[o.length-1]-o[o.length-2])}let m=null,w=null;r.rewind&&(t.isBeginning?w=r.virtual&&r.virtual.enabled&&t.virtual?t.virtual.slides.length-1:t.slides.length-1:t.isEnd&&(m=0));const y=(u-o[v])/f,S=v<r.slidesPerGroupSkip-1?1:r.slidesPerGroup;if(c>r.longSwipesMs){if(!r.longSwipes)return void t.slideTo(t.activeIndex);"next"===t.swipeDirection&&(y>=r.longSwipesRatio?t.slideTo(r.rewind&&t.isEnd?m:v+S):t.slideTo(v)),"prev"===t.swipeDirection&&(y>1-r.longSwipesRatio?t.slideTo(v+S):null!==w&&y<0&&Math.abs(y)>r.longSwipesRatio?t.slideTo(w):t.slideTo(v))}else{if(!r.shortSwipes)return void t.slideTo(t.activeIndex);t.navigation&&(d.target===t.navigation.nextEl||d.target===t.navigation.prevEl)?d.target===t.navigation.nextEl?t.slideTo(v+S):t.slideTo(v):("next"===t.swipeDirection&&t.slideTo(null!==m?m:v+S),"prev"===t.swipeDirection&&t.slideTo(null!==w?w:v))}}function F(){const e=this,{params:t,el:i}=e;if(i&&0===i.offsetWidth)return;t.breakpoints&&e.setBreakpoint();const{allowSlideNext:s,allowSlidePrev:r,snapGrid:a}=e,n=e.virtual&&e.params.virtual.enabled;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses();const o=n&&t.loop;!("auto"===t.slidesPerView||t.slidesPerView>1)||!e.isEnd||e.isBeginning||e.params.centeredSlides||o?e.params.loop&&!n?e.slideToLoop(e.realIndex,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0):e.slideTo(e.slides.length-1,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&(clearTimeout(e.autoplay.resizeTimeout),e.autoplay.resizeTimeout=setTimeout((()=>{e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.resume()}),500)),e.allowSlidePrev=r,e.allowSlideNext=s,e.params.watchOverflow&&a!==e.snapGrid&&e.checkOverflow()}function V(e){const t=this;t.enabled&&(t.allowClick||(t.params.preventClicks&&e.preventDefault(),t.params.preventClicksPropagation&&t.animating&&(e.stopPropagation(),e.stopImmediatePropagation())))}function R(){const e=this,{wrapperEl:t,rtlTranslate:i,enabled:s}=e;if(!s)return;let r;e.previousTranslate=e.translate,e.isHorizontal()?e.translate=-t.scrollLeft:e.translate=-t.scrollTop,0===e.translate&&(e.translate=0),e.updateActiveIndex(),e.updateSlidesClasses();const a=e.maxTranslate()-e.minTranslate();r=0===a?0:(e.translate-e.minTranslate())/a,r!==e.progress&&e.updateProgress(i?-e.translate:e.translate),e.emit("setTranslate",e.translate,!1)}function $(e){const t=this;z(t,e.target),t.params.cssMode||"auto"!==t.params.slidesPerView&&!t.params.autoHeight||t.update()}let H=!1;function W(){}const q=(e,t)=>{const i=v(),{params:s,el:r,wrapperEl:a,device:n}=e,o=!!s.nested,l="on"===t?"addEventListener":"removeEventListener",d=t;r[l]("pointerdown",e.onTouchStart,{passive:!1}),i[l]("pointermove",e.onTouchMove,{passive:!1,capture:o}),i[l]("pointerup",e.onTouchEnd,{passive:!0}),i[l]("pointercancel",e.onTouchEnd,{passive:!0}),i[l]("pointerout",e.onTouchEnd,{passive:!0}),i[l]("pointerleave",e.onTouchEnd,{passive:!0}),i[l]("contextmenu",e.onTouchEnd,{passive:!0}),(s.preventClicks||s.preventClicksPropagation)&&r[l]("click",e.onClick,!0),s.cssMode&&a[l]("scroll",e.onScroll),s.updateOnWindowResize?e[d](n.ios||n.android?"resize orientationchange observerUpdate":"resize observerUpdate",F,!0):e[d]("observerUpdate",F,!0),r[l]("load",e.onLoad,{capture:!0})};const X=(e,t)=>e.grid&&t.grid&&t.grid.rows>1;var Y={init:!0,direction:"horizontal",oneWayMovement:!1,touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopedSlides:null,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};function U(e,t){return function(i){void 0===i&&(i={});const s=Object.keys(i)[0],r=i[s];"object"==typeof r&&null!==r?(!0===e[s]&&(e[s]={enabled:!0}),"navigation"===s&&e[s]&&e[s].enabled&&!e[s].prevEl&&!e[s].nextEl&&(e[s].auto=!0),["pagination","scrollbar"].indexOf(s)>=0&&e[s]&&e[s].enabled&&!e[s].el&&(e[s].auto=!0),s in e&&"enabled"in r?("object"!=typeof e[s]||"enabled"in e[s]||(e[s].enabled=!0),e[s]||(e[s]={enabled:!1}),S(t,i)):S(t,i)):S(t,i)}}const K={eventsEmitter:{on(e,t,i){const s=this;if(!s.eventsListeners||s.destroyed)return s;if("function"!=typeof t)return s;const r=i?"unshift":"push";return e.split(" ").forEach((e=>{s.eventsListeners[e]||(s.eventsListeners[e]=[]),s.eventsListeners[e][r](t)})),s},once(e,t,i){const s=this;if(!s.eventsListeners||s.destroyed)return s;if("function"!=typeof t)return s;function r(){s.off(e,r),r.__emitterProxy&&delete r.__emitterProxy;for(var i=arguments.length,a=new Array(i),n=0;n<i;n++)a[n]=arguments[n];t.apply(s,a)}return r.__emitterProxy=t,s.on(e,r,i)},onAny(e,t){const i=this;if(!i.eventsListeners||i.destroyed)return i;if("function"!=typeof e)return i;const s=t?"unshift":"push";return i.eventsAnyListeners.indexOf(e)<0&&i.eventsAnyListeners[s](e),i},offAny(e){const t=this;if(!t.eventsListeners||t.destroyed)return t;if(!t.eventsAnyListeners)return t;const i=t.eventsAnyListeners.indexOf(e);return i>=0&&t.eventsAnyListeners.splice(i,1),t},off(e,t){const i=this;return!i.eventsListeners||i.destroyed?i:i.eventsListeners?(e.split(" ").forEach((e=>{void 0===t?i.eventsListeners[e]=[]:i.eventsListeners[e]&&i.eventsListeners[e].forEach(((s,r)=>{(s===t||s.__emitterProxy&&s.__emitterProxy===t)&&i.eventsListeners[e].splice(r,1)}))})),i):i},emit(){const e=this;if(!e.eventsListeners||e.destroyed)return e;if(!e.eventsListeners)return e;let t,i,s;for(var r=arguments.length,a=new Array(r),n=0;n<r;n++)a[n]=arguments[n];"string"==typeof a[0]||Array.isArray(a[0])?(t=a[0],i=a.slice(1,a.length),s=e):(t=a[0].events,i=a[0].data,s=a[0].context||e),i.unshift(s);return(Array.isArray(t)?t:t.split(" ")).forEach((t=>{e.eventsAnyListeners&&e.eventsAnyListeners.length&&e.eventsAnyListeners.forEach((e=>{e.apply(s,[t,...i])})),e.eventsListeners&&e.eventsListeners[t]&&e.eventsListeners[t].forEach((e=>{e.apply(s,i)}))})),e}},update:{updateSize:function(){const e=this;let t,i;const s=e.el;t=void 0!==e.params.width&&null!==e.params.width?e.params.width:s.clientWidth,i=void 0!==e.params.height&&null!==e.params.height?e.params.height:s.clientHeight,0===t&&e.isHorizontal()||0===i&&e.isVertical()||(t=t-parseInt(x(s,"padding-left")||0,10)-parseInt(x(s,"padding-right")||0,10),i=i-parseInt(x(s,"padding-top")||0,10)-parseInt(x(s,"padding-bottom")||0,10),Number.isNaN(t)&&(t=0),Number.isNaN(i)&&(i=0),Object.assign(e,{width:t,height:i,size:e.isHorizontal()?t:i}))},updateSlides:function(){const e=this;function t(t){return e.isHorizontal()?t:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[t]}function i(e,i){return parseFloat(e.getPropertyValue(t(i))||0)}const s=e.params,{wrapperEl:r,slidesEl:a,size:n,rtlTranslate:o,wrongRTL:l}=e,d=e.virtual&&s.virtual.enabled,p=d?e.virtual.slides.length:e.slides.length,c=E(a,`.${e.params.slideClass}, swiper-slide`),u=d?e.virtual.slides.length:c.length;let v=[];const f=[],m=[];let h=s.slidesOffsetBefore;"function"==typeof h&&(h=s.slidesOffsetBefore.call(e));let g=s.slidesOffsetAfter;"function"==typeof g&&(g=s.slidesOffsetAfter.call(e));const w=e.snapGrid.length,y=e.slidesGrid.length;let S=s.spaceBetween,T=-h,C=0,M=0;if(void 0===n)return;"string"==typeof S&&S.indexOf("%")>=0?S=parseFloat(S.replace("%",""))/100*n:"string"==typeof S&&(S=parseFloat(S)),e.virtualSize=-S,c.forEach((e=>{o?e.style.marginLeft="":e.style.marginRight="",e.style.marginBottom="",e.style.marginTop=""})),s.centeredSlides&&s.cssMode&&(b(r,"--swiper-centered-offset-before",""),b(r,"--swiper-centered-offset-after",""));const O=s.grid&&s.grid.rows>1&&e.grid;let L;O&&e.grid.initSlides(u);const k="auto"===s.slidesPerView&&s.breakpoints&&Object.keys(s.breakpoints).filter((e=>void 0!==s.breakpoints[e].slidesPerView)).length>0;for(let b=0;b<u;b+=1){let r;if(L=0,c[b]&&(r=c[b]),O&&e.grid.updateSlide(b,r,u,t),!c[b]||"none"!==x(r,"display")){if("auto"===s.slidesPerView){k&&(c[b].style[t("width")]="");const a=getComputedStyle(r),n=r.style.transform,o=r.style.webkitTransform;if(n&&(r.style.transform="none"),o&&(r.style.webkitTransform="none"),s.roundLengths)L=e.isHorizontal()?P(r,"width",!0):P(r,"height",!0);else{const e=i(a,"width"),t=i(a,"padding-left"),s=i(a,"padding-right"),n=i(a,"margin-left"),o=i(a,"margin-right"),l=a.getPropertyValue("box-sizing");if(l&&"border-box"===l)L=e+n+o;else{const{clientWidth:i,offsetWidth:a}=r;L=e+t+s+n+o+(a-i)}}n&&(r.style.transform=n),o&&(r.style.webkitTransform=o),s.roundLengths&&(L=Math.floor(L))}else L=(n-(s.slidesPerView-1)*S)/s.slidesPerView,s.roundLengths&&(L=Math.floor(L)),c[b]&&(c[b].style[t("width")]=`${L}px`);c[b]&&(c[b].swiperSlideSize=L),m.push(L),s.centeredSlides?(T=T+L/2+C/2+S,0===C&&0!==b&&(T=T-n/2-S),0===b&&(T=T-n/2-S),Math.abs(T)<.001&&(T=0),s.roundLengths&&(T=Math.floor(T)),M%s.slidesPerGroup==0&&v.push(T),f.push(T)):(s.roundLengths&&(T=Math.floor(T)),(M-Math.min(e.params.slidesPerGroupSkip,M))%e.params.slidesPerGroup==0&&v.push(T),f.push(T),T=T+L+S),e.virtualSize+=L+S,C=L,M+=1}}if(e.virtualSize=Math.max(e.virtualSize,n)+g,o&&l&&("slide"===s.effect||"coverflow"===s.effect)&&(r.style.width=`${e.virtualSize+S}px`),s.setWrapperSize&&(r.style[t("width")]=`${e.virtualSize+S}px`),O&&e.grid.updateWrapperSize(L,v,t),!s.centeredSlides){const t=[];for(let i=0;i<v.length;i+=1){let r=v[i];s.roundLengths&&(r=Math.floor(r)),v[i]<=e.virtualSize-n&&t.push(r)}v=t,Math.floor(e.virtualSize-n)-Math.floor(v[v.length-1])>1&&v.push(e.virtualSize-n)}if(d&&s.loop){const t=m[0]+S;if(s.slidesPerGroup>1){const i=Math.ceil((e.virtual.slidesBefore+e.virtual.slidesAfter)/s.slidesPerGroup),r=t*s.slidesPerGroup;for(let e=0;e<i;e+=1)v.push(v[v.length-1]+r)}for(let i=0;i<e.virtual.slidesBefore+e.virtual.slidesAfter;i+=1)1===s.slidesPerGroup&&v.push(v[v.length-1]+t),f.push(f[f.length-1]+t),e.virtualSize+=t}if(0===v.length&&(v=[0]),0!==S){const i=e.isHorizontal()&&o?"marginLeft":t("marginRight");c.filter(((e,t)=>!(s.cssMode&&!s.loop)||t!==c.length-1)).forEach((e=>{e.style[i]=`${S}px`}))}if(s.centeredSlides&&s.centeredSlidesBounds){let e=0;m.forEach((t=>{e+=t+(S||0)})),e-=S;const t=e-n;v=v.map((e=>e<=0?-h:e>t?t+g:e))}if(s.centerInsufficientSlides){let e=0;if(m.forEach((t=>{e+=t+(S||0)})),e-=S,e<n){const t=(n-e)/2;v.forEach(((e,i)=>{v[i]=e-t})),f.forEach(((e,i)=>{f[i]=e+t}))}}if(Object.assign(e,{slides:c,snapGrid:v,slidesGrid:f,slidesSizesGrid:m}),s.centeredSlides&&s.cssMode&&!s.centeredSlidesBounds){b(r,"--swiper-centered-offset-before",-v[0]+"px"),b(r,"--swiper-centered-offset-after",e.size/2-m[m.length-1]/2+"px");const t=-e.snapGrid[0],i=-e.slidesGrid[0];e.snapGrid=e.snapGrid.map((e=>e+t)),e.slidesGrid=e.slidesGrid.map((e=>e+i))}if(u!==p&&e.emit("slidesLengthChange"),v.length!==w&&(e.params.watchOverflow&&e.checkOverflow(),e.emit("snapGridLengthChange")),f.length!==y&&e.emit("slidesGridLengthChange"),s.watchSlidesProgress&&e.updateSlidesOffset(),!(d||s.cssMode||"slide"!==s.effect&&"fade"!==s.effect)){const t=`${s.containerModifierClass}backface-hidden`,i=e.el.classList.contains(t);u<=s.maxBackfaceHiddenSlides?i||e.el.classList.add(t):i&&e.el.classList.remove(t)}},updateAutoHeight:function(e){const t=this,i=[],s=t.virtual&&t.params.virtual.enabled;let r,a=0;"number"==typeof e?t.setTransition(e):!0===e&&t.setTransition(t.params.speed);const n=e=>s?t.slides[t.getSlideIndexByData(e)]:t.slides[e];if("auto"!==t.params.slidesPerView&&t.params.slidesPerView>1)if(t.params.centeredSlides)(t.visibleSlides||[]).forEach((e=>{i.push(e)}));else for(r=0;r<Math.ceil(t.params.slidesPerView);r+=1){const e=t.activeIndex+r;if(e>t.slides.length&&!s)break;i.push(n(e))}else i.push(n(t.activeIndex));for(r=0;r<i.length;r+=1)if(void 0!==i[r]){const e=i[r].offsetHeight;a=e>a?e:a}(a||0===a)&&(t.wrapperEl.style.height=`${a}px`)},updateSlidesOffset:function(){const e=this,t=e.slides,i=e.isElement?e.isHorizontal()?e.wrapperEl.offsetLeft:e.wrapperEl.offsetTop:0;for(let s=0;s<t.length;s+=1)t[s].swiperSlideOffset=(e.isHorizontal()?t[s].offsetLeft:t[s].offsetTop)-i-e.cssOverflowAdjustment()},updateSlidesProgress:function(e){void 0===e&&(e=this&&this.translate||0);const t=this,i=t.params,{slides:s,rtlTranslate:r,snapGrid:a}=t;if(0===s.length)return;void 0===s[0].swiperSlideOffset&&t.updateSlidesOffset();let n=-e;r&&(n=e),s.forEach((e=>{e.classList.remove(i.slideVisibleClass)})),t.visibleSlidesIndexes=[],t.visibleSlides=[];let o=i.spaceBetween;"string"==typeof o&&o.indexOf("%")>=0?o=parseFloat(o.replace("%",""))/100*t.size:"string"==typeof o&&(o=parseFloat(o));for(let l=0;l<s.length;l+=1){const e=s[l];let d=e.swiperSlideOffset;i.cssMode&&i.centeredSlides&&(d-=s[0].swiperSlideOffset);const p=(n+(i.centeredSlides?t.minTranslate():0)-d)/(e.swiperSlideSize+o),c=(n-a[0]+(i.centeredSlides?t.minTranslate():0)-d)/(e.swiperSlideSize+o),u=-(n-d),v=u+t.slidesSizesGrid[l];(u>=0&&u<t.size-1||v>1&&v<=t.size||u<=0&&v>=t.size)&&(t.visibleSlides.push(e),t.visibleSlidesIndexes.push(l),s[l].classList.add(i.slideVisibleClass)),e.progress=r?-p:p,e.originalProgress=r?-c:c}},updateProgress:function(e){const t=this;if(void 0===e){const i=t.rtlTranslate?-1:1;e=t&&t.translate&&t.translate*i||0}const i=t.params,s=t.maxTranslate()-t.minTranslate();let{progress:r,isBeginning:a,isEnd:n,progressLoop:o}=t;const l=a,d=n;if(0===s)r=0,a=!0,n=!0;else{r=(e-t.minTranslate())/s;const i=Math.abs(e-t.minTranslate())<1,o=Math.abs(e-t.maxTranslate())<1;a=i||r<=0,n=o||r>=1,i&&(r=0),o&&(r=1)}if(i.loop){const i=t.getSlideIndexByData(0),s=t.getSlideIndexByData(t.slides.length-1),r=t.slidesGrid[i],a=t.slidesGrid[s],n=t.slidesGrid[t.slidesGrid.length-1],l=Math.abs(e);o=l>=r?(l-r)/n:(l+n-a)/n,o>1&&(o-=1)}Object.assign(t,{progress:r,progressLoop:o,isBeginning:a,isEnd:n}),(i.watchSlidesProgress||i.centeredSlides&&i.autoHeight)&&t.updateSlidesProgress(e),a&&!l&&t.emit("reachBeginning toEdge"),n&&!d&&t.emit("reachEnd toEdge"),(l&&!a||d&&!n)&&t.emit("fromEdge"),t.emit("progress",r)},updateSlidesClasses:function(){const e=this,{slides:t,params:i,slidesEl:s,activeIndex:r}=e,a=e.virtual&&i.virtual.enabled,n=e=>E(s,`.${i.slideClass}${e}, swiper-slide${e}`)[0];let o;if(t.forEach((e=>{e.classList.remove(i.slideActiveClass,i.slideNextClass,i.slidePrevClass)})),a)if(i.loop){let t=r-e.virtual.slidesBefore;t<0&&(t=e.virtual.slides.length+t),t>=e.virtual.slides.length&&(t-=e.virtual.slides.length),o=n(`[data-swiper-slide-index="${t}"]`)}else o=n(`[data-swiper-slide-index="${r}"]`);else o=t[r];if(o){o.classList.add(i.slideActiveClass);let e=function(e,t){const i=[];for(;e.nextElementSibling;){const s=e.nextElementSibling;t?s.matches(t)&&i.push(s):i.push(s),e=s}return i}(o,`.${i.slideClass}, swiper-slide`)[0];i.loop&&!e&&(e=t[0]),e&&e.classList.add(i.slideNextClass);let s=function(e,t){const i=[];for(;e.previousElementSibling;){const s=e.previousElementSibling;t?s.matches(t)&&i.push(s):i.push(s),e=s}return i}(o,`.${i.slideClass}, swiper-slide`)[0];i.loop&&0===!s&&(s=t[t.length-1]),s&&s.classList.add(i.slidePrevClass)}e.emitSlidesClasses()},updateActiveIndex:function(e){const t=this,i=t.rtlTranslate?t.translate:-t.translate,{snapGrid:s,params:r,activeIndex:a,realIndex:n,snapIndex:o}=t;let l,d=e;const p=e=>{let i=e-t.virtual.slidesBefore;return i<0&&(i=t.virtual.slides.length+i),i>=t.virtual.slides.length&&(i-=t.virtual.slides.length),i};if(void 0===d&&(d=function(e){const{slidesGrid:t,params:i}=e,s=e.rtlTranslate?e.translate:-e.translate;let r;for(let a=0;a<t.length;a+=1)void 0!==t[a+1]?s>=t[a]&&s<t[a+1]-(t[a+1]-t[a])/2?r=a:s>=t[a]&&s<t[a+1]&&(r=a+1):s>=t[a]&&(r=a);return i.normalizeSlideIndex&&(r<0||void 0===r)&&(r=0),r}(t)),s.indexOf(i)>=0)l=s.indexOf(i);else{const e=Math.min(r.slidesPerGroupSkip,d);l=e+Math.floor((d-e)/r.slidesPerGroup)}if(l>=s.length&&(l=s.length-1),d===a)return l!==o&&(t.snapIndex=l,t.emit("snapIndexChange")),void(t.params.loop&&t.virtual&&t.params.virtual.enabled&&(t.realIndex=p(d)));let c;c=t.virtual&&r.virtual.enabled&&r.loop?p(d):t.slides[d]?parseInt(t.slides[d].getAttribute("data-swiper-slide-index")||d,10):d,Object.assign(t,{previousSnapIndex:o,snapIndex:l,previousRealIndex:n,realIndex:c,previousIndex:a,activeIndex:d}),t.initialized&&B(t),t.emit("activeIndexChange"),t.emit("snapIndexChange"),(t.initialized||t.params.runCallbacksOnInit)&&(n!==c&&t.emit("realIndexChange"),t.emit("slideChange"))},updateClickedSlide:function(e,t){const i=this,s=i.params;let r=e.closest(`.${s.slideClass}, swiper-slide`);!r&&i.isElement&&t&&t.length>1&&t.includes(e)&&[...t.slice(t.indexOf(e)+1,t.length)].forEach((e=>{!r&&e.matches&&e.matches(`.${s.slideClass}, swiper-slide`)&&(r=e)}));let a,n=!1;if(r)for(let o=0;o<i.slides.length;o+=1)if(i.slides[o]===r){n=!0,a=o;break}if(!r||!n)return i.clickedSlide=void 0,void(i.clickedIndex=void 0);i.clickedSlide=r,i.virtual&&i.params.virtual.enabled?i.clickedIndex=parseInt(r.getAttribute("data-swiper-slide-index"),10):i.clickedIndex=a,s.slideToClickedSlide&&void 0!==i.clickedIndex&&i.clickedIndex!==i.activeIndex&&i.slideToClickedSlide()}},translate:{getTranslate:function(e){void 0===e&&(e=this.isHorizontal()?"x":"y");const{params:t,rtlTranslate:i,translate:s,wrapperEl:r}=this;if(t.virtualTranslate)return i?-s:s;if(t.cssMode)return s;let a=w(r,e);return a+=this.cssOverflowAdjustment(),i&&(a=-a),a||0},setTranslate:function(e,t){const i=this,{rtlTranslate:s,params:r,wrapperEl:a,progress:n}=i;let o,l=0,d=0;i.isHorizontal()?l=s?-e:e:d=e,r.roundLengths&&(l=Math.floor(l),d=Math.floor(d)),i.previousTranslate=i.translate,i.translate=i.isHorizontal()?l:d,r.cssMode?a[i.isHorizontal()?"scrollLeft":"scrollTop"]=i.isHorizontal()?-l:-d:r.virtualTranslate||(i.isHorizontal()?l-=i.cssOverflowAdjustment():d-=i.cssOverflowAdjustment(),a.style.transform=`translate3d(${l}px, ${d}px, 0px)`);const p=i.maxTranslate()-i.minTranslate();o=0===p?0:(e-i.minTranslate())/p,o!==n&&i.updateProgress(e),i.emit("setTranslate",i.translate,t)},minTranslate:function(){return-this.snapGrid[0]},maxTranslate:function(){return-this.snapGrid[this.snapGrid.length-1]},translateTo:function(e,t,i,s,r){void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===i&&(i=!0),void 0===s&&(s=!0);const a=this,{params:n,wrapperEl:o}=a;if(a.animating&&n.preventInteractionOnTransition)return!1;const l=a.minTranslate(),d=a.maxTranslate();let p;if(p=s&&e>l?l:s&&e<d?d:e,a.updateProgress(p),n.cssMode){const e=a.isHorizontal();if(0===t)o[e?"scrollLeft":"scrollTop"]=-p;else{if(!a.support.smoothScroll)return T({swiper:a,targetPosition:-p,side:e?"left":"top"}),!0;o.scrollTo({[e?"left":"top"]:-p,behavior:"smooth"})}return!0}return 0===t?(a.setTransition(0),a.setTranslate(p),i&&(a.emit("beforeTransitionStart",t,r),a.emit("transitionEnd"))):(a.setTransition(t),a.setTranslate(p),i&&(a.emit("beforeTransitionStart",t,r),a.emit("transitionStart")),a.animating||(a.animating=!0,a.onTranslateToWrapperTransitionEnd||(a.onTranslateToWrapperTransitionEnd=function(e){a&&!a.destroyed&&e.target===this&&(a.wrapperEl.removeEventListener("transitionend",a.onTranslateToWrapperTransitionEnd),a.onTranslateToWrapperTransitionEnd=null,delete a.onTranslateToWrapperTransitionEnd,i&&a.emit("transitionEnd"))}),a.wrapperEl.addEventListener("transitionend",a.onTranslateToWrapperTransitionEnd))),!0}},transition:{setTransition:function(e,t){const i=this;i.params.cssMode||(i.wrapperEl.style.transitionDuration=`${e}ms`,i.wrapperEl.style.transitionDelay=0===e?"0ms":""),i.emit("setTransition",e,t)},transitionStart:function(e,t){void 0===e&&(e=!0);const i=this,{params:s}=i;s.cssMode||(s.autoHeight&&i.updateAutoHeight(),N({swiper:i,runCallbacks:e,direction:t,step:"Start"}))},transitionEnd:function(e,t){void 0===e&&(e=!0);const i=this,{params:s}=i;i.animating=!1,s.cssMode||(i.setTransition(0),N({swiper:i,runCallbacks:e,direction:t,step:"End"}))}},slide:{slideTo:function(e,t,i,s,r){void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===i&&(i=!0),"string"==typeof e&&(e=parseInt(e,10));const a=this;let n=e;n<0&&(n=0);const{params:o,snapGrid:l,slidesGrid:d,previousIndex:p,activeIndex:c,rtlTranslate:u,wrapperEl:v,enabled:f}=a;if(a.animating&&o.preventInteractionOnTransition||!f&&!s&&!r)return!1;const m=Math.min(a.params.slidesPerGroupSkip,n);let h=m+Math.floor((n-m)/a.params.slidesPerGroup);h>=l.length&&(h=l.length-1);const g=-l[h];if(o.normalizeSlideIndex)for(let y=0;y<d.length;y+=1){const e=-Math.floor(100*g),t=Math.floor(100*d[y]),i=Math.floor(100*d[y+1]);void 0!==d[y+1]?e>=t&&e<i-(i-t)/2?n=y:e>=t&&e<i&&(n=y+1):e>=t&&(n=y)}if(a.initialized&&n!==c){if(!a.allowSlideNext&&(u?g>a.translate&&g>a.minTranslate():g<a.translate&&g<a.minTranslate()))return!1;if(!a.allowSlidePrev&&g>a.translate&&g>a.maxTranslate()&&(c||0)!==n)return!1}let w;if(n!==(p||0)&&i&&a.emit("beforeSlideChangeStart"),a.updateProgress(g),w=n>c?"next":n<c?"prev":"reset",u&&-g===a.translate||!u&&g===a.translate)return a.updateActiveIndex(n),o.autoHeight&&a.updateAutoHeight(),a.updateSlidesClasses(),"slide"!==o.effect&&a.setTranslate(g),"reset"!==w&&(a.transitionStart(i,w),a.transitionEnd(i,w)),!1;if(o.cssMode){const e=a.isHorizontal(),i=u?g:-g;if(0===t){const t=a.virtual&&a.params.virtual.enabled;t&&(a.wrapperEl.style.scrollSnapType="none",a._immediateVirtual=!0),t&&!a._cssModeVirtualInitialSet&&a.params.initialSlide>0?(a._cssModeVirtualInitialSet=!0,requestAnimationFrame((()=>{v[e?"scrollLeft":"scrollTop"]=i}))):v[e?"scrollLeft":"scrollTop"]=i,t&&requestAnimationFrame((()=>{a.wrapperEl.style.scrollSnapType="",a._immediateVirtual=!1}))}else{if(!a.support.smoothScroll)return T({swiper:a,targetPosition:i,side:e?"left":"top"}),!0;v.scrollTo({[e?"left":"top"]:i,behavior:"smooth"})}return!0}return a.setTransition(t),a.setTranslate(g),a.updateActiveIndex(n),a.updateSlidesClasses(),a.emit("beforeTransitionStart",t,s),a.transitionStart(i,w),0===t?a.transitionEnd(i,w):a.animating||(a.animating=!0,a.onSlideToWrapperTransitionEnd||(a.onSlideToWrapperTransitionEnd=function(e){a&&!a.destroyed&&e.target===this&&(a.wrapperEl.removeEventListener("transitionend",a.onSlideToWrapperTransitionEnd),a.onSlideToWrapperTransitionEnd=null,delete a.onSlideToWrapperTransitionEnd,a.transitionEnd(i,w))}),a.wrapperEl.addEventListener("transitionend",a.onSlideToWrapperTransitionEnd)),!0},slideToLoop:function(e,t,i,s){if(void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===i&&(i=!0),"string"==typeof e){e=parseInt(e,10)}const r=this;let a=e;return r.params.loop&&(r.virtual&&r.params.virtual.enabled?a+=r.virtual.slidesBefore:a=r.getSlideIndexByData(a)),r.slideTo(a,t,i,s)},slideNext:function(e,t,i){void 0===e&&(e=this.params.speed),void 0===t&&(t=!0);const s=this,{enabled:r,params:a,animating:n}=s;if(!r)return s;let o=a.slidesPerGroup;"auto"===a.slidesPerView&&1===a.slidesPerGroup&&a.slidesPerGroupAuto&&(o=Math.max(s.slidesPerViewDynamic("current",!0),1));const l=s.activeIndex<a.slidesPerGroupSkip?1:o,d=s.virtual&&a.virtual.enabled;if(a.loop){if(n&&!d&&a.loopPreventsSliding)return!1;if(s.loopFix({direction:"next"}),s._clientLeft=s.wrapperEl.clientLeft,s.activeIndex===s.slides.length-1&&a.cssMode)return requestAnimationFrame((()=>{s.slideTo(s.activeIndex+l,e,t,i)})),!0}return a.rewind&&s.isEnd?s.slideTo(0,e,t,i):s.slideTo(s.activeIndex+l,e,t,i)},slidePrev:function(e,t,i){void 0===e&&(e=this.params.speed),void 0===t&&(t=!0);const s=this,{params:r,snapGrid:a,slidesGrid:n,rtlTranslate:o,enabled:l,animating:d}=s;if(!l)return s;const p=s.virtual&&r.virtual.enabled;if(r.loop){if(d&&!p&&r.loopPreventsSliding)return!1;s.loopFix({direction:"prev"}),s._clientLeft=s.wrapperEl.clientLeft}function c(e){return e<0?-Math.floor(Math.abs(e)):Math.floor(e)}const u=c(o?s.translate:-s.translate),v=a.map((e=>c(e)));let f=a[v.indexOf(u)-1];if(void 0===f&&r.cssMode){let e;a.forEach(((t,i)=>{u>=t&&(e=i)})),void 0!==e&&(f=a[e>0?e-1:e])}let m=0;if(void 0!==f&&(m=n.indexOf(f),m<0&&(m=s.activeIndex-1),"auto"===r.slidesPerView&&1===r.slidesPerGroup&&r.slidesPerGroupAuto&&(m=m-s.slidesPerViewDynamic("previous",!0)+1,m=Math.max(m,0))),r.rewind&&s.isBeginning){const r=s.params.virtual&&s.params.virtual.enabled&&s.virtual?s.virtual.slides.length-1:s.slides.length-1;return s.slideTo(r,e,t,i)}return r.loop&&0===s.activeIndex&&r.cssMode?(requestAnimationFrame((()=>{s.slideTo(m,e,t,i)})),!0):s.slideTo(m,e,t,i)},slideReset:function(e,t,i){return void 0===e&&(e=this.params.speed),void 0===t&&(t=!0),this.slideTo(this.activeIndex,e,t,i)},slideToClosest:function(e,t,i,s){void 0===e&&(e=this.params.speed),void 0===t&&(t=!0),void 0===s&&(s=.5);const r=this;let a=r.activeIndex;const n=Math.min(r.params.slidesPerGroupSkip,a),o=n+Math.floor((a-n)/r.params.slidesPerGroup),l=r.rtlTranslate?r.translate:-r.translate;if(l>=r.snapGrid[o]){const e=r.snapGrid[o];l-e>(r.snapGrid[o+1]-e)*s&&(a+=r.params.slidesPerGroup)}else{const e=r.snapGrid[o-1];l-e<=(r.snapGrid[o]-e)*s&&(a-=r.params.slidesPerGroup)}return a=Math.max(a,0),a=Math.min(a,r.slidesGrid.length-1),r.slideTo(a,e,t,i)},slideToClickedSlide:function(){const e=this,{params:t,slidesEl:i}=e,s="auto"===t.slidesPerView?e.slidesPerViewDynamic():t.slidesPerView;let r,a=e.clickedIndex;const n=e.isElement?"swiper-slide":`.${t.slideClass}`;if(t.loop){if(e.animating)return;r=parseInt(e.clickedSlide.getAttribute("data-swiper-slide-index"),10),t.centeredSlides?a<e.loopedSlides-s/2||a>e.slides.length-e.loopedSlides+s/2?(e.loopFix(),a=e.getSlideIndex(E(i,`${n}[data-swiper-slide-index="${r}"]`)[0]),h((()=>{e.slideTo(a)}))):e.slideTo(a):a>e.slides.length-s?(e.loopFix(),a=e.getSlideIndex(E(i,`${n}[data-swiper-slide-index="${r}"]`)[0]),h((()=>{e.slideTo(a)}))):e.slideTo(a)}else e.slideTo(a)}},loop:{loopCreate:function(e){const t=this,{params:i,slidesEl:s}=t;if(!i.loop||t.virtual&&t.params.virtual.enabled)return;E(s,`.${i.slideClass}, swiper-slide`).forEach(((e,t)=>{e.setAttribute("data-swiper-slide-index",t)})),t.loopFix({slideRealIndex:e,direction:i.centeredSlides?void 0:"next"})},loopFix:function(e){let{slideRealIndex:t,slideTo:i=!0,direction:s,setTranslate:r,activeSlideIndex:a,byController:n,byMousewheel:o}=void 0===e?{}:e;const l=this;if(!l.params.loop)return;l.emit("beforeLoopFix");const{slides:d,allowSlidePrev:p,allowSlideNext:c,slidesEl:u,params:v}=l;if(l.allowSlidePrev=!0,l.allowSlideNext=!0,l.virtual&&v.virtual.enabled)return i&&(v.centeredSlides||0!==l.snapIndex?v.centeredSlides&&l.snapIndex<v.slidesPerView?l.slideTo(l.virtual.slides.length+l.snapIndex,0,!1,!0):l.snapIndex===l.snapGrid.length-1&&l.slideTo(l.virtual.slidesBefore,0,!1,!0):l.slideTo(l.virtual.slides.length,0,!1,!0)),l.allowSlidePrev=p,l.allowSlideNext=c,void l.emit("loopFix");const f="auto"===v.slidesPerView?l.slidesPerViewDynamic():Math.ceil(parseFloat(v.slidesPerView,10));let m=v.loopedSlides||f;m%v.slidesPerGroup!=0&&(m+=v.slidesPerGroup-m%v.slidesPerGroup),l.loopedSlides=m;const h=[],g=[];let w=l.activeIndex;void 0===a?a=l.getSlideIndex(l.slides.filter((e=>e.classList.contains(v.slideActiveClass)))[0]):w=a;const y="next"===s||!s,S="prev"===s||!s;let b=0,T=0;if(a<m){b=Math.max(m-a,v.slidesPerGroup);for(let e=0;e<m-a;e+=1){const t=e-Math.floor(e/d.length)*d.length;h.push(d.length-t-1)}}else if(a>l.slides.length-2*m){T=Math.max(a-(l.slides.length-2*m),v.slidesPerGroup);for(let e=0;e<T;e+=1){const t=e-Math.floor(e/d.length)*d.length;g.push(t)}}if(S&&h.forEach((e=>{l.slides[e].swiperLoopMoveDOM=!0,u.prepend(l.slides[e]),l.slides[e].swiperLoopMoveDOM=!1})),y&&g.forEach((e=>{l.slides[e].swiperLoopMoveDOM=!0,u.append(l.slides[e]),l.slides[e].swiperLoopMoveDOM=!1})),l.recalcSlides(),"auto"===v.slidesPerView&&l.updateSlides(),v.watchSlidesProgress&&l.updateSlidesOffset(),i)if(h.length>0&&S)if(void 0===t){const e=l.slidesGrid[w],t=l.slidesGrid[w+b]-e;o?l.setTranslate(l.translate-t):(l.slideTo(w+b,0,!1,!0),r&&(l.touches[l.isHorizontal()?"startX":"startY"]+=t,l.touchEventsData.currentTranslate=l.translate))}else r&&(l.slideToLoop(t,0,!1,!0),l.touchEventsData.currentTranslate=l.translate);else if(g.length>0&&y)if(void 0===t){const e=l.slidesGrid[w],t=l.slidesGrid[w-T]-e;o?l.setTranslate(l.translate-t):(l.slideTo(w-T,0,!1,!0),r&&(l.touches[l.isHorizontal()?"startX":"startY"]+=t,l.touchEventsData.currentTranslate=l.translate))}else l.slideToLoop(t,0,!1,!0);if(l.allowSlidePrev=p,l.allowSlideNext=c,l.controller&&l.controller.control&&!n){const e={slideRealIndex:t,direction:s,setTranslate:r,activeSlideIndex:a,byController:!0};Array.isArray(l.controller.control)?l.controller.control.forEach((t=>{!t.destroyed&&t.params.loop&&t.loopFix({...e,slideTo:t.params.slidesPerView===v.slidesPerView&&i})})):l.controller.control instanceof l.constructor&&l.controller.control.params.loop&&l.controller.control.loopFix({...e,slideTo:l.controller.control.params.slidesPerView===v.slidesPerView&&i})}l.emit("loopFix")},loopDestroy:function(){const e=this,{params:t,slidesEl:i}=e;if(!t.loop||e.virtual&&e.params.virtual.enabled)return;e.recalcSlides();const s=[];e.slides.forEach((e=>{const t=void 0===e.swiperSlideIndex?1*e.getAttribute("data-swiper-slide-index"):e.swiperSlideIndex;s[t]=e})),e.slides.forEach((e=>{e.removeAttribute("data-swiper-slide-index")})),s.forEach((e=>{i.append(e)})),e.recalcSlides(),e.slideTo(e.realIndex,0)}},grabCursor:{setGrabCursor:function(e){const t=this;if(!t.params.simulateTouch||t.params.watchOverflow&&t.isLocked||t.params.cssMode)return;const i="container"===t.params.touchEventsTarget?t.el:t.wrapperEl;t.isElement&&(t.__preventObserver__=!0),i.style.cursor="move",i.style.cursor=e?"grabbing":"grab",t.isElement&&requestAnimationFrame((()=>{t.__preventObserver__=!1}))},unsetGrabCursor:function(){const e=this;e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e.isElement&&(e.__preventObserver__=!0),e["container"===e.params.touchEventsTarget?"el":"wrapperEl"].style.cursor="",e.isElement&&requestAnimationFrame((()=>{e.__preventObserver__=!1})))}},events:{attachEvents:function(){const e=this,t=v(),{params:i}=e;e.onTouchStart=G.bind(e),e.onTouchMove=D.bind(e),e.onTouchEnd=j.bind(e),i.cssMode&&(e.onScroll=R.bind(e)),e.onClick=V.bind(e),e.onLoad=$.bind(e),H||(t.addEventListener("touchstart",W),H=!0),q(e,"on")},detachEvents:function(){q(this,"off")}},breakpoints:{setBreakpoint:function(){const e=this,{realIndex:t,initialized:i,params:s,el:r}=e,a=s.breakpoints;if(!a||a&&0===Object.keys(a).length)return;const n=e.getBreakpoint(a,e.params.breakpointsBase,e.el);if(!n||e.currentBreakpoint===n)return;const o=(n in a?a[n]:void 0)||e.originalParams,l=X(e,s),d=X(e,o),p=s.enabled;l&&!d?(r.classList.remove(`${s.containerModifierClass}grid`,`${s.containerModifierClass}grid-column`),e.emitContainerClasses()):!l&&d&&(r.classList.add(`${s.containerModifierClass}grid`),(o.grid.fill&&"column"===o.grid.fill||!o.grid.fill&&"column"===s.grid.fill)&&r.classList.add(`${s.containerModifierClass}grid-column`),e.emitContainerClasses()),["navigation","pagination","scrollbar"].forEach((t=>{if(void 0===o[t])return;const i=s[t]&&s[t].enabled,r=o[t]&&o[t].enabled;i&&!r&&e[t].disable(),!i&&r&&e[t].enable()}));const c=o.direction&&o.direction!==s.direction,u=s.loop&&(o.slidesPerView!==s.slidesPerView||c),v=s.loop;c&&i&&e.changeDirection(),S(e.params,o);const f=e.params.enabled,m=e.params.loop;Object.assign(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),p&&!f?e.disable():!p&&f&&e.enable(),e.currentBreakpoint=n,e.emit("_beforeBreakpoint",o),i&&(u?(e.loopDestroy(),e.loopCreate(t),e.updateSlides()):!v&&m?(e.loopCreate(t),e.updateSlides()):v&&!m&&e.loopDestroy()),e.emit("breakpoint",o)},getBreakpoint:function(e,t,i){if(void 0===t&&(t="window"),!e||"container"===t&&!i)return;let s=!1;const r=m(),a="window"===t?r.innerHeight:i.clientHeight,n=Object.keys(e).map((e=>{if("string"==typeof e&&0===e.indexOf("@")){const t=parseFloat(e.substr(1));return{value:a*t,point:e}}return{value:e,point:e}}));n.sort(((e,t)=>parseInt(e.value,10)-parseInt(t.value,10)));for(let o=0;o<n.length;o+=1){const{point:e,value:a}=n[o];"window"===t?r.matchMedia(`(min-width: ${a}px)`).matches&&(s=e):a<=i.clientWidth&&(s=e)}return s||"max"}},checkOverflow:{checkOverflow:function(){const e=this,{isLocked:t,params:i}=e,{slidesOffsetBefore:s}=i;if(s){const t=e.slides.length-1,i=e.slidesGrid[t]+e.slidesSizesGrid[t]+2*s;e.isLocked=e.size>i}else e.isLocked=1===e.snapGrid.length;!0===i.allowSlideNext&&(e.allowSlideNext=!e.isLocked),!0===i.allowSlidePrev&&(e.allowSlidePrev=!e.isLocked),t&&t!==e.isLocked&&(e.isEnd=!1),t!==e.isLocked&&e.emit(e.isLocked?"lock":"unlock")}},classes:{addClasses:function(){const e=this,{classNames:t,params:i,rtl:s,el:r,device:a}=e,n=function(e,t){const i=[];return e.forEach((e=>{"object"==typeof e?Object.keys(e).forEach((s=>{e[s]&&i.push(t+s)})):"string"==typeof e&&i.push(t+e)})),i}(["initialized",i.direction,{"free-mode":e.params.freeMode&&i.freeMode.enabled},{autoheight:i.autoHeight},{rtl:s},{grid:i.grid&&i.grid.rows>1},{"grid-column":i.grid&&i.grid.rows>1&&"column"===i.grid.fill},{android:a.android},{ios:a.ios},{"css-mode":i.cssMode},{centered:i.cssMode&&i.centeredSlides},{"watch-progress":i.watchSlidesProgress}],i.containerModifierClass);t.push(...n),r.classList.add(...t),e.emitContainerClasses()},removeClasses:function(){const{el:e,classNames:t}=this;e.classList.remove(...t),this.emitContainerClasses()}}},Z={};let J=class e{constructor(){let t,i;for(var s=arguments.length,r=new Array(s),a=0;a<s;a++)r[a]=arguments[a];1===r.length&&r[0].constructor&&"Object"===Object.prototype.toString.call(r[0]).slice(8,-1)?i=r[0]:[t,i]=r,i||(i={}),i=S({},i),t&&!i.el&&(i.el=t);const n=v();if(i.el&&"string"==typeof i.el&&n.querySelectorAll(i.el).length>1){const t=[];return n.querySelectorAll(i.el).forEach((s=>{const r=S({},i,{el:s});t.push(new e(r))})),t}const o=this;o.__swiper__=!0,o.support=k(),o.device=I({userAgent:i.userAgent}),o.browser=_(),o.eventsListeners={},o.eventsAnyListeners=[],o.modules=[...o.__modules__],i.modules&&Array.isArray(i.modules)&&o.modules.push(...i.modules);const l={};o.modules.forEach((e=>{e({params:i,swiper:o,extendParams:U(i,l),on:o.on.bind(o),once:o.once.bind(o),off:o.off.bind(o),emit:o.emit.bind(o)})}));const d=S({},Y,l);return o.params=S({},d,Z,i),o.originalParams=S({},o.params),o.passedParams=S({},i),o.params&&o.params.on&&Object.keys(o.params.on).forEach((e=>{o.on(e,o.params.on[e])})),o.params&&o.params.onAny&&o.onAny(o.params.onAny),Object.assign(o,{enabled:o.params.enabled,el:t,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:()=>"horizontal"===o.params.direction,isVertical:()=>"vertical"===o.params.direction,activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return Math.trunc(this.translate/2**23)*2**23},allowSlideNext:o.params.allowSlideNext,allowSlidePrev:o.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:o.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,evCache:[]},allowClick:!0,allowTouchMove:o.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),o.emit("_swiper"),o.params.init&&o.init(),o}getSlideIndex(e){const{slidesEl:t,params:i}=this,s=C(E(t,`.${i.slideClass}, swiper-slide`)[0]);return C(e)-s}getSlideIndexByData(e){return this.getSlideIndex(this.slides.filter((t=>1*t.getAttribute("data-swiper-slide-index")===e))[0])}recalcSlides(){const{slidesEl:e,params:t}=this;this.slides=E(e,`.${t.slideClass}, swiper-slide`)}enable(){const e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&&e.setGrabCursor(),e.emit("enable"))}disable(){const e=this;e.enabled&&(e.enabled=!1,e.params.grabCursor&&e.unsetGrabCursor(),e.emit("disable"))}setProgress(e,t){const i=this;e=Math.min(Math.max(e,0),1);const s=i.minTranslate(),r=(i.maxTranslate()-s)*e+s;i.translateTo(r,void 0===t?0:t),i.updateActiveIndex(),i.updateSlidesClasses()}emitContainerClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=e.el.className.split(" ").filter((t=>0===t.indexOf("swiper")||0===t.indexOf(e.params.containerModifierClass)));e.emit("_containerClasses",t.join(" "))}getSlideClasses(e){const t=this;return t.destroyed?"":e.className.split(" ").filter((e=>0===e.indexOf("swiper-slide")||0===e.indexOf(t.params.slideClass))).join(" ")}emitSlidesClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=[];e.slides.forEach((i=>{const s=e.getSlideClasses(i);t.push({slideEl:i,classNames:s}),e.emit("_slideClass",i,s)})),e.emit("_slideClasses",t)}slidesPerViewDynamic(e,t){void 0===e&&(e="current"),void 0===t&&(t=!1);const{params:i,slides:s,slidesGrid:r,slidesSizesGrid:a,size:n,activeIndex:o}=this;let l=1;if("number"==typeof i.slidesPerView)return i.slidesPerView;if(i.centeredSlides){let e,t=s[o]?s[o].swiperSlideSize:0;for(let i=o+1;i<s.length;i+=1)s[i]&&!e&&(t+=s[i].swiperSlideSize,l+=1,t>n&&(e=!0));for(let i=o-1;i>=0;i-=1)s[i]&&!e&&(t+=s[i].swiperSlideSize,l+=1,t>n&&(e=!0))}else if("current"===e)for(let d=o+1;d<s.length;d+=1){(t?r[d]+a[d]-r[o]<n:r[d]-r[o]<n)&&(l+=1)}else for(let d=o-1;d>=0;d-=1){r[o]-r[d]<n&&(l+=1)}return l}update(){const e=this;if(!e||e.destroyed)return;const{snapGrid:t,params:i}=e;function s(){const t=e.rtlTranslate?-1*e.translate:e.translate,i=Math.min(Math.max(t,e.maxTranslate()),e.minTranslate());e.setTranslate(i),e.updateActiveIndex(),e.updateSlidesClasses()}let r;if(i.breakpoints&&e.setBreakpoint(),[...e.el.querySelectorAll('[loading="lazy"]')].forEach((t=>{t.complete&&z(e,t)})),e.updateSize(),e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),i.freeMode&&i.freeMode.enabled&&!i.cssMode)s(),i.autoHeight&&e.updateAutoHeight();else{if(("auto"===i.slidesPerView||i.slidesPerView>1)&&e.isEnd&&!i.centeredSlides){const t=e.virtual&&i.virtual.enabled?e.virtual.slides:e.slides;r=e.slideTo(t.length-1,0,!1,!0)}else r=e.slideTo(e.activeIndex,0,!1,!0);r||s()}i.watchOverflow&&t!==e.snapGrid&&e.checkOverflow(),e.emit("update")}changeDirection(e,t){void 0===t&&(t=!0);const i=this,s=i.params.direction;return e||(e="horizontal"===s?"vertical":"horizontal"),e===s||"horizontal"!==e&&"vertical"!==e||(i.el.classList.remove(`${i.params.containerModifierClass}${s}`),i.el.classList.add(`${i.params.containerModifierClass}${e}`),i.emitContainerClasses(),i.params.direction=e,i.slides.forEach((t=>{"vertical"===e?t.style.width="":t.style.height=""})),i.emit("changeDirection"),t&&i.update()),i}changeLanguageDirection(e){const t=this;t.rtl&&"rtl"===e||!t.rtl&&"ltr"===e||(t.rtl="rtl"===e,t.rtlTranslate="horizontal"===t.params.direction&&t.rtl,t.rtl?(t.el.classList.add(`${t.params.containerModifierClass}rtl`),t.el.dir="rtl"):(t.el.classList.remove(`${t.params.containerModifierClass}rtl`),t.el.dir="ltr"),t.update())}mount(e){const t=this;if(t.mounted)return!0;let i=e||t.params.el;if("string"==typeof i&&(i=document.querySelector(i)),!i)return!1;i.swiper=t,i.parentNode&&i.parentNode.host&&"SWIPER-CONTAINER"===i.parentNode.host.nodeName&&(t.isElement=!0);const s=()=>`.${(t.params.wrapperClass||"").trim().split(" ").join(".")}`;let r=(()=>{if(i&&i.shadowRoot&&i.shadowRoot.querySelector){return i.shadowRoot.querySelector(s())}return E(i,s())[0]})();return!r&&t.params.createElements&&(r=function(e,t){void 0===t&&(t=[]);const i=document.createElement(e);return i.classList.add(...Array.isArray(t)?t:[t]),i}("div",t.params.wrapperClass),i.append(r),E(i,`.${t.params.slideClass}`).forEach((e=>{r.append(e)}))),Object.assign(t,{el:i,wrapperEl:r,slidesEl:t.isElement&&!i.parentNode.host.slideSlots?i.parentNode.host:r,hostEl:t.isElement?i.parentNode.host:i,mounted:!0,rtl:"rtl"===i.dir.toLowerCase()||"rtl"===x(i,"direction"),rtlTranslate:"horizontal"===t.params.direction&&("rtl"===i.dir.toLowerCase()||"rtl"===x(i,"direction")),wrongRTL:"-webkit-box"===x(r,"display")}),!0}init(e){const t=this;if(t.initialized)return t;if(!1===t.mount(e))return t;t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.loop&&t.virtual&&t.params.virtual.enabled?t.slideTo(t.params.initialSlide+t.virtual.slidesBefore,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.params.loop&&t.loopCreate(),t.attachEvents();const i=[...t.el.querySelectorAll('[loading="lazy"]')];return t.isElement&&i.push(...t.hostEl.querySelectorAll('[loading="lazy"]')),i.forEach((e=>{e.complete?z(t,e):e.addEventListener("load",(e=>{z(t,e.target)}))})),B(t),t.initialized=!0,B(t),t.emit("init"),t.emit("afterInit"),t}destroy(e,t){void 0===e&&(e=!0),void 0===t&&(t=!0);const i=this,{params:s,el:r,wrapperEl:a,slides:n}=i;return void 0===i.params||i.destroyed||(i.emit("beforeDestroy"),i.initialized=!1,i.detachEvents(),s.loop&&i.loopDestroy(),t&&(i.removeClasses(),r.removeAttribute("style"),a.removeAttribute("style"),n&&n.length&&n.forEach((e=>{e.classList.remove(s.slideVisibleClass,s.slideActiveClass,s.slideNextClass,s.slidePrevClass),e.removeAttribute("style"),e.removeAttribute("data-swiper-slide-index")}))),i.emit("destroy"),Object.keys(i.eventsListeners).forEach((e=>{i.off(e)})),!1!==e&&(i.el.swiper=null,function(e){const t=e;Object.keys(t).forEach((e=>{try{t[e]=null}catch(i){}try{delete t[e]}catch(i){}}))}(i)),i.destroyed=!0),null}static extendDefaults(e){S(Z,e)}static get extendedDefaults(){return Z}static get defaults(){return Y}static installModule(t){e.prototype.__modules__||(e.prototype.__modules__=[]);const i=e.prototype.__modules__;"function"==typeof t&&i.indexOf(t)<0&&i.push(t)}static use(t){return Array.isArray(t)?(t.forEach((t=>e.installModule(t))),e):(e.installModule(t),e)}};Object.keys(K).forEach((e=>{Object.keys(K[e]).forEach((t=>{J.prototype[t]=K[e][t]}))})),J.use([function(e){let{swiper:t,on:i,emit:s}=e;const r=m();let a=null,n=null;const o=()=>{t&&!t.destroyed&&t.initialized&&(s("beforeResize"),s("resize"))},l=()=>{t&&!t.destroyed&&t.initialized&&s("orientationchange")};i("init",(()=>{t.params.resizeObserver&&void 0!==r.ResizeObserver?t&&!t.destroyed&&t.initialized&&(a=new ResizeObserver((e=>{n=r.requestAnimationFrame((()=>{const{width:i,height:s}=t;let r=i,a=s;e.forEach((e=>{let{contentBoxSize:i,contentRect:s,target:n}=e;n&&n!==t.el||(r=s?s.width:(i[0]||i).inlineSize,a=s?s.height:(i[0]||i).blockSize)})),r===i&&a===s||o()}))})),a.observe(t.el)):(r.addEventListener("resize",o),r.addEventListener("orientationchange",l))})),i("destroy",(()=>{n&&r.cancelAnimationFrame(n),a&&a.unobserve&&t.el&&(a.unobserve(t.el),a=null),r.removeEventListener("resize",o),r.removeEventListener("orientationchange",l)}))},function(e){let{swiper:t,extendParams:i,on:s,emit:r}=e;const a=[],n=m(),o=function(e,i){void 0===i&&(i={});const s=new(n.MutationObserver||n.WebkitMutationObserver)((e=>{if(t.__preventObserver__)return;if(1===e.length)return void r("observerUpdate",e[0]);const i=function(){r("observerUpdate",e[0])};n.requestAnimationFrame?n.requestAnimationFrame(i):n.setTimeout(i,0)}));s.observe(e,{attributes:void 0===i.attributes||i.attributes,childList:void 0===i.childList||i.childList,characterData:void 0===i.characterData||i.characterData}),a.push(s)};i({observer:!1,observeParents:!1,observeSlideChildren:!1}),s("init",(()=>{if(t.params.observer){if(t.params.observeParents){const e=function(e,t){const i=[];let s=e.parentElement;for(;s;)t?s.matches(t)&&i.push(s):i.push(s),s=s.parentElement;return i}(t.hostEl);for(let t=0;t<e.length;t+=1)o(e[t])}o(t.hostEl,{childList:t.params.observeSlideChildren}),o(t.wrapperEl,{attributes:!1})}})),s("destroy",(()=>{a.forEach((e=>{e.disconnect()})),a.splice(0,a.length)}))}]);const Q=["eventsPrefix","injectStyles","injectStylesUrls","modules","init","_direction","oneWayMovement","touchEventsTarget","initialSlide","_speed","cssMode","updateOnWindowResize","resizeObserver","nested","focusableElements","_enabled","_width","_height","preventInteractionOnTransition","userAgent","url","_edgeSwipeDetection","_edgeSwipeThreshold","_freeMode","_autoHeight","setWrapperSize","virtualTranslate","_effect","breakpoints","breakpointsBase","_spaceBetween","_slidesPerView","maxBackfaceHiddenSlides","_grid","_slidesPerGroup","_slidesPerGroupSkip","_slidesPerGroupAuto","_centeredSlides","_centeredSlidesBounds","_slidesOffsetBefore","_slidesOffsetAfter","normalizeSlideIndex","_centerInsufficientSlides","_watchOverflow","roundLengths","touchRatio","touchAngle","simulateTouch","_shortSwipes","_longSwipes","longSwipesRatio","longSwipesMs","_followFinger","allowTouchMove","_threshold","touchMoveStopPropagation","touchStartPreventDefault","touchStartForcePreventDefault","touchReleaseOnEdges","uniqueNavElements","_resistance","_resistanceRatio","_watchSlidesProgress","_grabCursor","preventClicks","preventClicksPropagation","_slideToClickedSlide","_loop","loopedSlides","loopPreventsSliding","_rewind","_allowSlidePrev","_allowSlideNext","_swipeHandler","_noSwiping","noSwipingClass","noSwipingSelector","passiveListeners","containerModifierClass","slideClass","slideActiveClass","slideVisibleClass","slideNextClass","slidePrevClass","wrapperClass","lazyPreloaderClass","lazyPreloadPrevNext","runCallbacksOnInit","observer","observeParents","observeSlideChildren","a11y","_autoplay","_controller","coverflowEffect","cubeEffect","fadeEffect","flipEffect","creativeEffect","cardsEffect","hashNavigation","history","keyboard","mousewheel","_navigation","_pagination","parallax","_scrollbar","_thumbs","virtual","zoom","control"];function ee(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)&&!e.__swiper__}function te(e,t){const i=["__proto__","constructor","prototype"];Object.keys(t).filter((e=>i.indexOf(e)<0)).forEach((i=>{void 0===e[i]?e[i]=t[i]:ee(t[i])&&ee(e[i])&&Object.keys(t[i]).length>0?t[i].__swiper__?e[i]=t[i]:te(e[i],t[i]):e[i]=t[i]}))}function ie(e){return void 0===e&&(e={}),e.navigation&&void 0===e.navigation.nextEl&&void 0===e.navigation.prevEl}function se(e){return void 0===e&&(e={}),e.pagination&&void 0===e.pagination.el}function re(e){return void 0===e&&(e={}),e.scrollbar&&void 0===e.scrollbar.el}function ae(e){void 0===e&&(e="");const t=e.split(" ").map((e=>e.trim())).filter((e=>!!e)),i=[];return t.forEach((e=>{i.indexOf(e)<0&&i.push(e)})),i.join(" ")}function ne(e,t){void 0===e&&(e={}),void 0===t&&(t=!0);const i={on:{}},s={},r={};te(i,Y),i._emitClasses=!0,i.init=!1;const a={},n=Q.map((e=>e.replace(/_/,""))),o=Object.assign({},e);return Object.keys(o).forEach((o=>{void 0!==e[o]&&(n.indexOf(o)>=0?ee(e[o])?(i[o]={},r[o]={},te(i[o],e[o]),te(r[o],e[o])):(i[o]=e[o],r[o]=e[o]):0===o.search(/on[A-Z]/)&&"function"==typeof e[o]?t?s[`${o[2].toLowerCase()}${o.substr(3)}`]=e[o]:i.on[`${o[2].toLowerCase()}${o.substr(3)}`]=e[o]:a[o]=e[o])})),["navigation","pagination","scrollbar"].forEach((e=>{!0===i[e]&&(i[e]={}),!1===i[e]&&delete i[e]})),{params:i,passedParams:r,rest:a,events:s}}function oe(e,t,i){void 0===e&&(e={});const s=[],r={"container-start":[],"container-end":[],"wrapper-start":[],"wrapper-end":[]},a=(e,t)=>{Array.isArray(e)&&e.forEach((e=>{const i="symbol"==typeof e.type;"default"===t&&(t="container-end"),i&&e.children?a(e.children,t):!e.type||"SwiperSlide"!==e.type.name&&"AsyncComponentWrapper"!==e.type.name?r[t]&&r[t].push(e):s.push(e)}))};return Object.keys(e).forEach((t=>{if("function"!=typeof e[t])return;const i=e[t]();a(i,t)})),i.value=t.value,t.value=s,{slides:s,slots:r}}const le={name:"Swiper",props:{tag:{type:String,default:"div"},wrapperTag:{type:String,default:"div"},modules:{type:Array,default:void 0},init:{type:Boolean,default:void 0},direction:{type:String,default:void 0},oneWayMovement:{type:Boolean,default:void 0},touchEventsTarget:{type:String,default:void 0},initialSlide:{type:Number,default:void 0},speed:{type:Number,default:void 0},cssMode:{type:Boolean,default:void 0},updateOnWindowResize:{type:Boolean,default:void 0},resizeObserver:{type:Boolean,default:void 0},nested:{type:Boolean,default:void 0},focusableElements:{type:String,default:void 0},width:{type:Number,default:void 0},height:{type:Number,default:void 0},preventInteractionOnTransition:{type:Boolean,default:void 0},userAgent:{type:String,default:void 0},url:{type:String,default:void 0},edgeSwipeDetection:{type:[Boolean,String],default:void 0},edgeSwipeThreshold:{type:Number,default:void 0},autoHeight:{type:Boolean,default:void 0},setWrapperSize:{type:Boolean,default:void 0},virtualTranslate:{type:Boolean,default:void 0},effect:{type:String,default:void 0},breakpoints:{type:Object,default:void 0},spaceBetween:{type:[Number,String],default:void 0},slidesPerView:{type:[Number,String],default:void 0},maxBackfaceHiddenSlides:{type:Number,default:void 0},slidesPerGroup:{type:Number,default:void 0},slidesPerGroupSkip:{type:Number,default:void 0},slidesPerGroupAuto:{type:Boolean,default:void 0},centeredSlides:{type:Boolean,default:void 0},centeredSlidesBounds:{type:Boolean,default:void 0},slidesOffsetBefore:{type:Number,default:void 0},slidesOffsetAfter:{type:Number,default:void 0},normalizeSlideIndex:{type:Boolean,default:void 0},centerInsufficientSlides:{type:Boolean,default:void 0},watchOverflow:{type:Boolean,default:void 0},roundLengths:{type:Boolean,default:void 0},touchRatio:{type:Number,default:void 0},touchAngle:{type:Number,default:void 0},simulateTouch:{type:Boolean,default:void 0},shortSwipes:{type:Boolean,default:void 0},longSwipes:{type:Boolean,default:void 0},longSwipesRatio:{type:Number,default:void 0},longSwipesMs:{type:Number,default:void 0},followFinger:{type:Boolean,default:void 0},allowTouchMove:{type:Boolean,default:void 0},threshold:{type:Number,default:void 0},touchMoveStopPropagation:{type:Boolean,default:void 0},touchStartPreventDefault:{type:Boolean,default:void 0},touchStartForcePreventDefault:{type:Boolean,default:void 0},touchReleaseOnEdges:{type:Boolean,default:void 0},uniqueNavElements:{type:Boolean,default:void 0},resistance:{type:Boolean,default:void 0},resistanceRatio:{type:Number,default:void 0},watchSlidesProgress:{type:Boolean,default:void 0},grabCursor:{type:Boolean,default:void 0},preventClicks:{type:Boolean,default:void 0},preventClicksPropagation:{type:Boolean,default:void 0},slideToClickedSlide:{type:Boolean,default:void 0},loop:{type:Boolean,default:void 0},loopedSlides:{type:Number,default:void 0},loopPreventsSliding:{type:Boolean,default:void 0},rewind:{type:Boolean,default:void 0},allowSlidePrev:{type:Boolean,default:void 0},allowSlideNext:{type:Boolean,default:void 0},swipeHandler:{type:Boolean,default:void 0},noSwiping:{type:Boolean,default:void 0},noSwipingClass:{type:String,default:void 0},noSwipingSelector:{type:String,default:void 0},passiveListeners:{type:Boolean,default:void 0},containerModifierClass:{type:String,default:void 0},slideClass:{type:String,default:void 0},slideActiveClass:{type:String,default:void 0},slideVisibleClass:{type:String,default:void 0},slideNextClass:{type:String,default:void 0},slidePrevClass:{type:String,default:void 0},wrapperClass:{type:String,default:void 0},lazyPreloaderClass:{type:String,default:void 0},lazyPreloadPrevNext:{type:Number,default:void 0},runCallbacksOnInit:{type:Boolean,default:void 0},observer:{type:Boolean,default:void 0},observeParents:{type:Boolean,default:void 0},observeSlideChildren:{type:Boolean,default:void 0},a11y:{type:[Boolean,Object],default:void 0},autoplay:{type:[Boolean,Object],default:void 0},controller:{type:Object,default:void 0},coverflowEffect:{type:Object,default:void 0},cubeEffect:{type:Object,default:void 0},fadeEffect:{type:Object,default:void 0},flipEffect:{type:Object,default:void 0},creativeEffect:{type:Object,default:void 0},cardsEffect:{type:Object,default:void 0},hashNavigation:{type:[Boolean,Object],default:void 0},history:{type:[Boolean,Object],default:void 0},keyboard:{type:[Boolean,Object],default:void 0},mousewheel:{type:[Boolean,Object],default:void 0},navigation:{type:[Boolean,Object],default:void 0},pagination:{type:[Boolean,Object],default:void 0},parallax:{type:[Boolean,Object],default:void 0},scrollbar:{type:[Boolean,Object],default:void 0},thumbs:{type:Object,default:void 0},virtual:{type:[Boolean,Object],default:void 0},zoom:{type:[Boolean,Object],default:void 0},grid:{type:[Object],default:void 0},freeMode:{type:[Boolean,Object],default:void 0},enabled:{type:Boolean,default:void 0}},emits:["_beforeBreakpoint","_containerClasses","_slideClass","_slideClasses","_swiper","_freeModeNoMomentumRelease","activeIndexChange","afterInit","autoplay","autoplayStart","autoplayStop","autoplayPause","autoplayResume","autoplayTimeLeft","beforeDestroy","beforeInit","beforeLoopFix","beforeResize","beforeSlideChangeStart","beforeTransitionStart","breakpoint","breakpointsBase","changeDirection","click","disable","doubleTap","doubleClick","destroy","enable","fromEdge","hashChange","hashSet","init","keyPress","lock","loopFix","momentumBounce","navigationHide","navigationShow","navigationPrev","navigationNext","observerUpdate","orientationchange","paginationHide","paginationRender","paginationShow","paginationUpdate","progress","reachBeginning","reachEnd","realIndexChange","resize","scroll","scrollbarDragEnd","scrollbarDragMove","scrollbarDragStart","setTransition","setTranslate","slideChange","slideChangeTransitionEnd","slideChangeTransitionStart","slideNextTransitionEnd","slideNextTransitionStart","slidePrevTransitionEnd","slidePrevTransitionStart","slideResetTransitionStart","slideResetTransitionEnd","sliderMove","sliderFirstMove","slidesLengthChange","slidesGridLengthChange","snapGridLengthChange","snapIndexChange","swiper","tap","toEdge","touchEnd","touchMove","touchMoveOpposite","touchStart","transitionEnd","transitionStart","unlock","update","virtualUpdate","zoomChange"],setup(l,d){let{slots:p,emit:c}=d;const{tag:u,wrapperTag:v}=l,f=e("swiper"),m=e(null),h=e(!1),g=e(!1),w=e(null),y=e(null),S=e(null),b={value:[]},T={value:[]},E=e(null),x=e(null),C=e(null),P=e(null),{params:M,passedParams:O}=ne(l,!1);oe(p,b,T),S.value=O,T.value=b.value;M.onAny=function(e){for(var t=arguments.length,i=new Array(t>1?t-1:0),s=1;s<t;s++)i[s-1]=arguments[s];c(e,...i)},Object.assign(M.on,{_beforeBreakpoint:()=>{oe(p,b,T),h.value=!0},_containerClasses(e,t){f.value=t}});const L={...M};if(delete L.wrapperClass,y.value=new J(L),y.value.virtual&&y.value.params.virtual.enabled){y.value.virtual.slides=b.value;const e={cache:!1,slides:b.value,renderExternal:e=>{m.value=e},renderExternalUpdate:!1};te(y.value.params.virtual,e),te(y.value.originalParams.virtual,e)}function k(e){return M.virtual?function(e,t,i){if(!i)return null;const s=e=>{let i=e;return e<0?i=t.length+e:i>=t.length&&(i-=t.length),i},r=e.value.isHorizontal()?{[e.value.rtlTranslate?"right":"left"]:`${i.offset}px`}:{top:`${i.offset}px`},{from:a,to:n}=i,l=e.value.params.loop?-t.length:0,d=e.value.params.loop?2*t.length:t.length,p=[];for(let o=l;o<d;o+=1)o>=a&&o<=n&&p.push(t[s(o)]);return p.map((t=>(t.props||(t.props={}),t.props.style||(t.props.style={}),t.props.swiperRef=e,t.props.style=r,o(t.type,{...t.props},t.children))))}(y,e,m.value):(e.forEach(((e,t)=>{e.props||(e.props={}),e.props.swiperRef=y,e.props.swiperSlideIndex=t})),e)}return t((()=>{!g.value&&y.value&&(y.value.emitSlidesClasses(),g.value=!0);const{passedParams:e}=ne(l,!1),t=function(e,t,i,s,r){const a=[];if(!t)return a;const n=e=>{a.indexOf(e)<0&&a.push(e)};if(i&&s){const e=s.map(r),t=i.map(r);e.join("")!==t.join("")&&n("children"),s.length!==i.length&&n("children")}return Q.filter((e=>"_"===e[0])).map((e=>e.replace(/_/,""))).forEach((i=>{if(i in e&&i in t)if(ee(e[i])&&ee(t[i])){const s=Object.keys(e[i]),r=Object.keys(t[i]);s.length!==r.length?n(i):(s.forEach((s=>{e[i][s]!==t[i][s]&&n(i)})),r.forEach((s=>{e[i][s]!==t[i][s]&&n(i)})))}else e[i]!==t[i]&&n(i)})),a}(e,S.value,b.value,T.value,(e=>e.props&&e.props.key));S.value=e,(t.length||h.value)&&y.value&&!y.value.destroyed&&function(e){let{swiper:t,slides:i,passedParams:s,changedParams:r,nextEl:a,prevEl:n,scrollbarEl:o,paginationEl:l}=e;const d=r.filter((e=>"children"!==e&&"direction"!==e&&"wrapperClass"!==e)),{params:p,pagination:c,navigation:u,scrollbar:v,virtual:f,thumbs:m}=t;let h,g,w,y,S,b,T,E;r.includes("thumbs")&&s.thumbs&&s.thumbs.swiper&&p.thumbs&&!p.thumbs.swiper&&(h=!0),r.includes("controller")&&s.controller&&s.controller.control&&p.controller&&!p.controller.control&&(g=!0),r.includes("pagination")&&s.pagination&&(s.pagination.el||l)&&(p.pagination||!1===p.pagination)&&c&&!c.el&&(w=!0),r.includes("scrollbar")&&s.scrollbar&&(s.scrollbar.el||o)&&(p.scrollbar||!1===p.scrollbar)&&v&&!v.el&&(y=!0),r.includes("navigation")&&s.navigation&&(s.navigation.prevEl||n)&&(s.navigation.nextEl||a)&&(p.navigation||!1===p.navigation)&&u&&!u.prevEl&&!u.nextEl&&(S=!0);const x=e=>{t[e]&&(t[e].destroy(),"navigation"===e?(t.isElement&&(t[e].prevEl.remove(),t[e].nextEl.remove()),p[e].prevEl=void 0,p[e].nextEl=void 0,t[e].prevEl=void 0,t[e].nextEl=void 0):(t.isElement&&t[e].el.remove(),p[e].el=void 0,t[e].el=void 0))};r.includes("loop")&&t.isElement&&(p.loop&&!s.loop?b=!0:!p.loop&&s.loop?T=!0:E=!0),d.forEach((e=>{if(ee(p[e])&&ee(s[e]))te(p[e],s[e]),"navigation"!==e&&"pagination"!==e&&"scrollbar"!==e||!("enabled"in s[e])||s[e].enabled||x(e);else{const t=s[e];!0!==t&&!1!==t||"navigation"!==e&&"pagination"!==e&&"scrollbar"!==e?p[e]=s[e]:!1===t&&x(e)}})),d.includes("controller")&&!g&&t.controller&&t.controller.control&&p.controller&&p.controller.control&&(t.controller.control=p.controller.control),r.includes("children")&&i&&f&&p.virtual.enabled&&(f.slides=i,f.update(!0)),r.includes("children")&&i&&p.loop&&(E=!0),h&&m.init()&&m.update(!0);g&&(t.controller.control=p.controller.control),w&&(!t.isElement||l&&"string"!=typeof l||(l=document.createElement("div"),l.classList.add("swiper-pagination"),l.part.add("pagination"),t.el.appendChild(l)),l&&(p.pagination.el=l),c.init(),c.render(),c.update()),y&&(!t.isElement||o&&"string"!=typeof o||(o=document.createElement("div"),o.classList.add("swiper-scrollbar"),o.part.add("scrollbar"),t.el.appendChild(o)),o&&(p.scrollbar.el=o),v.init(),v.updateSize(),v.setTranslate()),S&&(t.isElement&&(a&&"string"!=typeof a||(a=document.createElement("div"),a.classList.add("swiper-button-next"),a.innerHTML=t.hostEl.constructor.nextButtonSvg,a.part.add("button-next"),t.el.appendChild(a)),n&&"string"!=typeof n||(n=document.createElement("div"),n.classList.add("swiper-button-prev"),n.innerHTML=t.hostEl.constructor.prevButtonSvg,n.part.add("button-prev"),t.el.appendChild(n))),a&&(p.navigation.nextEl=a),n&&(p.navigation.prevEl=n),u.init(),u.update()),r.includes("allowSlideNext")&&(t.allowSlideNext=s.allowSlideNext),r.includes("allowSlidePrev")&&(t.allowSlidePrev=s.allowSlidePrev),r.includes("direction")&&t.changeDirection(s.direction,!1),(b||E)&&t.loopDestroy(),(T||E)&&t.loopCreate(),t.update()}({swiper:y.value,slides:b.value,passedParams:e,changedParams:t,nextEl:E.value,prevEl:x.value,scrollbarEl:P.value,paginationEl:C.value}),h.value=!1})),i("swiper",y),s(m,(()=>{r((()=>{var e;!(e=y.value)||e.destroyed||!e.params.virtual||e.params.virtual&&!e.params.virtual.enabled||(e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),e.parallax&&e.params.parallax&&e.params.parallax.enabled&&e.parallax.setTranslate())}))})),a((()=>{w.value&&(!function(e,t){let{el:i,nextEl:s,prevEl:r,paginationEl:a,scrollbarEl:n,swiper:o}=e;ie(t)&&s&&r&&(o.params.navigation.nextEl=s,o.originalParams.navigation.nextEl=s,o.params.navigation.prevEl=r,o.originalParams.navigation.prevEl=r),se(t)&&a&&(o.params.pagination.el=a,o.originalParams.pagination.el=a),re(t)&&n&&(o.params.scrollbar.el=n,o.originalParams.scrollbar.el=n),o.init(i)}({el:w.value,nextEl:E.value,prevEl:x.value,paginationEl:C.value,scrollbarEl:P.value,swiper:y.value},M),c("swiper",y.value))})),n((()=>{y.value&&!y.value.destroyed&&y.value.destroy(!0,!1)})),()=>{const{slides:e,slots:t}=oe(p,b,T);return o(u,{ref:w,class:ae(f.value)},[t["container-start"],o(v,{class:(i=M.wrapperClass,void 0===i&&(i=""),i?i.includes("swiper-wrapper")?i:`swiper-wrapper ${i}`:"swiper-wrapper")},[t["wrapper-start"],k(e),t["wrapper-end"]]),ie(l)&&[o("div",{ref:x,class:"swiper-button-prev"}),o("div",{ref:E,class:"swiper-button-next"})],re(l)&&o("div",{ref:P,class:"swiper-scrollbar"}),se(l)&&o("div",{ref:C,class:"swiper-pagination"}),t["container-end"]]);var i}}},de={name:"SwiperSlide",props:{tag:{type:String,default:"div"},swiperRef:{type:Object,required:!1},swiperSlideIndex:{type:Number,default:void 0,required:!1},zoom:{type:Boolean,default:void 0,required:!1},lazy:{type:Boolean,default:!1,required:!1},virtualIndex:{type:[String,Number],default:void 0}},setup(s,r){let{slots:p}=r,c=!1;const{swiperRef:u}=s,v=e(null),f=e("swiper-slide"),m=e(!1);function h(e,t,i){t===v.value&&(f.value=i)}a((()=>{u&&u.value&&(u.value.on("_slideClass",h),c=!0)})),l((()=>{!c&&u&&u.value&&(u.value.on("_slideClass",h),c=!0)})),t((()=>{v.value&&u&&u.value&&(void 0!==s.swiperSlideIndex&&(v.value.swiperSlideIndex=s.swiperSlideIndex),u.value.destroyed&&"swiper-slide"!==f.value&&(f.value="swiper-slide"))})),n((()=>{u&&u.value&&u.value.off("_slideClass",h)}));const g=d((()=>({isActive:f.value.indexOf("swiper-slide-active")>=0,isVisible:f.value.indexOf("swiper-slide-visible")>=0,isPrev:f.value.indexOf("swiper-slide-prev")>=0,isNext:f.value.indexOf("swiper-slide-next")>=0})));i("swiperSlide",g);const w=()=>{m.value=!0};return()=>o(s.tag,{class:ae(`${f.value}`),ref:v,"data-swiper-slide-index":void 0===s.virtualIndex&&u&&u.value&&u.value.params.loop?s.swiperSlideIndex:s.virtualIndex,onLoadCapture:w},s.zoom?o("div",{class:"swiper-zoom-container","data-swiper-zoom":"number"==typeof s.zoom?s.zoom:void 0},[p.default&&p.default(g.value),s.lazy&&!m.value&&o("div",{class:"swiper-lazy-preloader"})]):[p.default&&p.default(g.value),s.lazy&&!m.value&&o("div",{class:"swiper-lazy-preloader"})])}};function pe(e){let t,i,{swiper:s,extendParams:r,on:a,emit:n,params:o}=e;s.autoplay={running:!1,paused:!1,timeLeft:0},r({autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!0,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}});let l,d,p,c,u,f,m,h=o&&o.autoplay?o.autoplay.delay:3e3,g=o&&o.autoplay?o.autoplay.delay:3e3,w=(new Date).getTime;function y(e){s&&!s.destroyed&&s.wrapperEl&&e.target===s.wrapperEl&&(s.wrapperEl.removeEventListener("transitionend",y),C())}const S=()=>{if(s.destroyed||!s.autoplay.running)return;s.autoplay.paused?d=!0:d&&(g=l,d=!1);const e=s.autoplay.paused?l:w+g-(new Date).getTime();s.autoplay.timeLeft=e,n("autoplayTimeLeft",e,e/h),i=requestAnimationFrame((()=>{S()}))},b=e=>{if(s.destroyed||!s.autoplay.running)return;cancelAnimationFrame(i),S();let r=void 0===e?s.params.autoplay.delay:e;h=s.params.autoplay.delay,g=s.params.autoplay.delay;const a=(()=>{let e;if(e=s.virtual&&s.params.virtual.enabled?s.slides.filter((e=>e.classList.contains("swiper-slide-active")))[0]:s.slides[s.activeIndex],!e)return;return parseInt(e.getAttribute("data-swiper-autoplay"),10)})();!Number.isNaN(a)&&a>0&&void 0===e&&(r=a,h=a,g=a),l=r;const o=s.params.speed,d=()=>{s&&!s.destroyed&&(s.params.autoplay.reverseDirection?!s.isBeginning||s.params.loop||s.params.rewind?(s.slidePrev(o,!0,!0),n("autoplay")):s.params.autoplay.stopOnLastSlide||(s.slideTo(s.slides.length-1,o,!0,!0),n("autoplay")):!s.isEnd||s.params.loop||s.params.rewind?(s.slideNext(o,!0,!0),n("autoplay")):s.params.autoplay.stopOnLastSlide||(s.slideTo(0,o,!0,!0),n("autoplay")),s.params.cssMode&&(w=(new Date).getTime(),requestAnimationFrame((()=>{b()}))))};return r>0?(clearTimeout(t),t=setTimeout((()=>{d()}),r)):requestAnimationFrame((()=>{d()})),r},T=()=>{s.autoplay.running=!0,b(),n("autoplayStart")},E=()=>{s.autoplay.running=!1,clearTimeout(t),cancelAnimationFrame(i),n("autoplayStop")},x=(e,i)=>{if(s.destroyed||!s.autoplay.running)return;clearTimeout(t),e||(m=!0);const r=()=>{n("autoplayPause"),s.params.autoplay.waitForTransition?s.wrapperEl.addEventListener("transitionend",y):C()};if(s.autoplay.paused=!0,i)return f&&(l=s.params.autoplay.delay),f=!1,void r();const a=l||s.params.autoplay.delay;l=a-((new Date).getTime()-w),s.isEnd&&l<0&&!s.params.loop||(l<0&&(l=0),r())},C=()=>{s.isEnd&&l<0&&!s.params.loop||s.destroyed||!s.autoplay.running||(w=(new Date).getTime(),m?(m=!1,b(l)):b(),s.autoplay.paused=!1,n("autoplayResume"))},P=()=>{if(s.destroyed||!s.autoplay.running)return;const e=v();"hidden"===e.visibilityState&&(m=!0,x(!0)),"visible"===e.visibilityState&&C()},M=e=>{"mouse"===e.pointerType&&(m=!0,s.animating||s.autoplay.paused||x(!0))},O=e=>{"mouse"===e.pointerType&&s.autoplay.paused&&C()};a("init",(()=>{s.params.autoplay.enabled&&(s.params.autoplay.pauseOnMouseEnter&&(s.el.addEventListener("pointerenter",M),s.el.addEventListener("pointerleave",O)),v().addEventListener("visibilitychange",P),w=(new Date).getTime(),T())})),a("destroy",(()=>{s.el.removeEventListener("pointerenter",M),s.el.removeEventListener("pointerleave",O),v().removeEventListener("visibilitychange",P),s.autoplay.running&&E()})),a("beforeTransitionStart",((e,t,i)=>{!s.destroyed&&s.autoplay.running&&(i||!s.params.autoplay.disableOnInteraction?x(!0,!0):E())})),a("sliderFirstMove",(()=>{!s.destroyed&&s.autoplay.running&&(s.params.autoplay.disableOnInteraction?E():(p=!0,c=!1,m=!1,u=setTimeout((()=>{m=!0,c=!0,x(!0)}),200)))})),a("touchEnd",(()=>{if(!s.destroyed&&s.autoplay.running&&p){if(clearTimeout(u),clearTimeout(t),s.params.autoplay.disableOnInteraction)return c=!1,void(p=!1);c&&s.params.cssMode&&C(),c=!1,p=!1}})),a("slideChange",(()=>{!s.destroyed&&s.autoplay.running&&(f=!0)})),Object.assign(s.autoplay,{start:T,stop:E,pause:x,resume:C})}export{pe as A,de as S,le as a};
