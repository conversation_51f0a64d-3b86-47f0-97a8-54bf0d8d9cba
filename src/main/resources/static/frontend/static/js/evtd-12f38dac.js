function e(e){return e.composedPath()[0]}const t={mousemoveoutside:new WeakMap,clickoutside:new WeakMap};function n(n,o,i){const r=t[n];let u=r.get(o);void 0===u&&r.set(o,u=new WeakMap);let c=u.get(i);return void 0===c&&u.set(i,c=function(t,n,o){if("mousemoveoutside"===t){const t=t=>{n.contains(e(t))||o(t)};return{mousemove:t,touchstart:t}}if("clickoutside"===t){let t=!1;const i=o=>{t=!n.contains(e(o))},r=i=>{t&&(n.contains(e(i))||o(i))};return{mousedown:i,mouseup:r,touchstart:i,touchend:r}}return{}}(n,o,i)),c}const{on:o,off:i}=function(){if("undefined"==typeof window)return{on:()=>{},off:()=>{}};const t=new WeakMap,r=new WeakMap;function u(){t.set(this,!0)}function c(){t.set(this,!0),r.set(this,!0)}function s(e,t,n){const o=e[t];return e[t]=function(){return n.apply(e,arguments),o.apply(e,arguments)},e}function a(e,t){e[t]=Event.prototype[t]}const f=new WeakMap,d=Object.getOwnPropertyDescriptor(Event.prototype,"currentTarget");function p(){var e;return null!==(e=f.get(this))&&void 0!==e?e:null}function l(e,t){void 0!==d&&Object.defineProperty(e,"currentTarget",{configurable:!0,enumerable:!0,get:null!=t?t:d.get})}const v={bubble:{},capture:{}},b={},w=function(){const n=function(n){const{type:o,eventPhase:i,bubbles:d}=n,b=e(n);if(2===i)return;const w=1===i?"capture":"bubble";let h=b;const m=[];for(;null===h&&(h=window),m.push(h),h!==window;)h=h.parentNode||null;const g=v.capture[o],y=v.bubble[o];if(s(n,"stopPropagation",u),s(n,"stopImmediatePropagation",c),l(n,p),"capture"===w){if(void 0===g)return;for(let e=m.length-1;e>=0&&!t.has(n);--e){const t=m[e],o=g.get(t);if(void 0!==o){f.set(n,t);for(const e of o){if(r.has(n))break;e(n)}}if(0===e&&!d&&void 0!==y){const e=y.get(t);if(void 0!==e)for(const t of e){if(r.has(n))break;t(n)}}}}else if("bubble"===w){if(void 0===y)return;for(let e=0;e<m.length&&!t.has(n);++e){const t=m[e],o=y.get(t);if(void 0!==o){f.set(n,t);for(const e of o){if(r.has(n))break;e(n)}}}}a(n,"stopPropagation"),a(n,"stopImmediatePropagation"),l(n)};return n.displayName="evtdUnifiedHandler",n}(),h=function(){const e=function(e){const{type:t,eventPhase:n}=e;if(2!==n)return;const o=b[t];void 0!==o&&o.forEach((t=>t(e)))};return e.displayName="evtdUnifiedWindowEventHandler",e}();function m(e,t){const n=v[e];return void 0===n[t]&&(n[t]=new Map,window.addEventListener(t,w,"capture"===e)),n[t]}function g(e,t){let n=e.get(t);return void 0===n&&e.set(t,n=new Set),n}function y(e,t,o,r){const u=function(e,t,o,r){if("mousemoveoutside"===e||"clickoutside"===e){const u=n(e,t,o);return Object.keys(u).forEach((e=>{i(e,document,u[e],r)})),!0}return!1}(e,t,o,r);if(u)return;const c=!0===r||"object"==typeof r&&!0===r.capture,s=c?"capture":"bubble",a=m(s,e),f=g(a,t);if(t===window){if(!function(e,t,n,o){const i=v[t][n];if(void 0!==i){const t=i.get(e);if(void 0!==t&&t.has(o))return!0}return!1}(t,c?"bubble":"capture",e,o)&&function(e,t){const n=b[e];return!(void 0===n||!n.has(t))}(e,o)){const t=b[e];t.delete(o),0===t.size&&(window.removeEventListener(e,h),b[e]=void 0)}}f.has(o)&&f.delete(o),0===f.size&&a.delete(t),0===a.size&&(window.removeEventListener(e,w,"capture"===s),v[s][e]=void 0)}return{on:function(e,t,i,r){let u;u="object"==typeof r&&!0===r.once?n=>{y(e,t,u,r),i(n)}:i;if(function(e,t,i,r){if("mousemoveoutside"===e||"clickoutside"===e){const u=n(e,t,i);return Object.keys(u).forEach((e=>{o(e,document,u[e],r)})),!0}return!1}(e,t,u,r))return;const c=g(m(!0===r||"object"==typeof r&&!0===r.capture?"capture":"bubble",e),t);if(c.has(u)||c.add(u),t===window){const t=function(e){return void 0===b[e]&&(b[e]=new Set,window.addEventListener(e,h)),b[e]}(e);t.has(u)||t.add(u)}},off:y}}();export{i as a,o};
