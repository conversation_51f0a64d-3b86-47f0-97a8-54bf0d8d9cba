import{e as s,f as a,h as t,i as e,a as i,j as r,c as m,d as o,g as d,b as n,k as c}from"./index-092b8780.js";import{i as f}from"./index-a5df0f75.js";import"./api-360ec627.js";import{d as Y}from"./dayjs-67f8ddef.js";const j=(a=0,t=50)=>s({startIndex:a,endIndex:t}),p=()=>r(),g=()=>m(),x=()=>o(),y=()=>i(),D=()=>d(),h=()=>n(),M=()=>c(),u=()=>f({url:"/system/statistics/getDeviceNumInfo",method:"get",headers:{msg:!1}}),I=(s,t)=>a({country:t,province:s}),T=(s,a)=>t({city:s,area:a}),b=s=>e({city:s,startTime:Y().format("YYYY-MM-DD"),endTime:Y().format("YYYY-MM-DD")});export{y as X,D as a,h as b,u as c,M as d,p as e,g as f,x as g,j as h,I as i,T as j,b as k};
