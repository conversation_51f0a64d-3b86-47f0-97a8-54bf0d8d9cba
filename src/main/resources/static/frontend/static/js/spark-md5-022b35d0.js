import{h as t}from"./@babel-f3c0a00c.js";var r={exports:{}};const e=t(r.exports=function(t){var r=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"];function e(t,r){var e=t[0],n=t[1],f=t[2],h=t[3];n=((n+=((f=((f+=((h=((h+=((e=((e+=(n&f|~n&h)+r[0]-680876936|0)<<7|e>>>25)+n|0)&n|~e&f)+r[1]-389564586|0)<<12|h>>>20)+e|0)&e|~h&n)+r[2]+606105819|0)<<17|f>>>15)+h|0)&h|~f&e)+r[3]-1044525330|0)<<22|n>>>10)+f|0,n=((n+=((f=((f+=((h=((h+=((e=((e+=(n&f|~n&h)+r[4]-176418897|0)<<7|e>>>25)+n|0)&n|~e&f)+r[5]+1200080426|0)<<12|h>>>20)+e|0)&e|~h&n)+r[6]-1473231341|0)<<17|f>>>15)+h|0)&h|~f&e)+r[7]-45705983|0)<<22|n>>>10)+f|0,n=((n+=((f=((f+=((h=((h+=((e=((e+=(n&f|~n&h)+r[8]+1770035416|0)<<7|e>>>25)+n|0)&n|~e&f)+r[9]-1958414417|0)<<12|h>>>20)+e|0)&e|~h&n)+r[10]-42063|0)<<17|f>>>15)+h|0)&h|~f&e)+r[11]-1990404162|0)<<22|n>>>10)+f|0,n=((n+=((f=((f+=((h=((h+=((e=((e+=(n&f|~n&h)+r[12]+1804603682|0)<<7|e>>>25)+n|0)&n|~e&f)+r[13]-40341101|0)<<12|h>>>20)+e|0)&e|~h&n)+r[14]-1502002290|0)<<17|f>>>15)+h|0)&h|~f&e)+r[15]+1236535329|0)<<22|n>>>10)+f|0,n=((n+=((f=((f+=((h=((h+=((e=((e+=(n&h|f&~h)+r[1]-165796510|0)<<5|e>>>27)+n|0)&f|n&~f)+r[6]-1069501632|0)<<9|h>>>23)+e|0)&n|e&~n)+r[11]+643717713|0)<<14|f>>>18)+h|0)&e|h&~e)+r[0]-373897302|0)<<20|n>>>12)+f|0,n=((n+=((f=((f+=((h=((h+=((e=((e+=(n&h|f&~h)+r[5]-701558691|0)<<5|e>>>27)+n|0)&f|n&~f)+r[10]+38016083|0)<<9|h>>>23)+e|0)&n|e&~n)+r[15]-660478335|0)<<14|f>>>18)+h|0)&e|h&~e)+r[4]-405537848|0)<<20|n>>>12)+f|0,n=((n+=((f=((f+=((h=((h+=((e=((e+=(n&h|f&~h)+r[9]+568446438|0)<<5|e>>>27)+n|0)&f|n&~f)+r[14]-1019803690|0)<<9|h>>>23)+e|0)&n|e&~n)+r[3]-187363961|0)<<14|f>>>18)+h|0)&e|h&~e)+r[8]+1163531501|0)<<20|n>>>12)+f|0,n=((n+=((f=((f+=((h=((h+=((e=((e+=(n&h|f&~h)+r[13]-1444681467|0)<<5|e>>>27)+n|0)&f|n&~f)+r[2]-51403784|0)<<9|h>>>23)+e|0)&n|e&~n)+r[7]+1735328473|0)<<14|f>>>18)+h|0)&e|h&~e)+r[12]-1926607734|0)<<20|n>>>12)+f|0,n=((n+=((f=((f+=((h=((h+=((e=((e+=(n^f^h)+r[5]-378558|0)<<4|e>>>28)+n|0)^n^f)+r[8]-2022574463|0)<<11|h>>>21)+e|0)^e^n)+r[11]+1839030562|0)<<16|f>>>16)+h|0)^h^e)+r[14]-35309556|0)<<23|n>>>9)+f|0,n=((n+=((f=((f+=((h=((h+=((e=((e+=(n^f^h)+r[1]-1530992060|0)<<4|e>>>28)+n|0)^n^f)+r[4]+1272893353|0)<<11|h>>>21)+e|0)^e^n)+r[7]-155497632|0)<<16|f>>>16)+h|0)^h^e)+r[10]-1094730640|0)<<23|n>>>9)+f|0,n=((n+=((f=((f+=((h=((h+=((e=((e+=(n^f^h)+r[13]+681279174|0)<<4|e>>>28)+n|0)^n^f)+r[0]-358537222|0)<<11|h>>>21)+e|0)^e^n)+r[3]-722521979|0)<<16|f>>>16)+h|0)^h^e)+r[6]+76029189|0)<<23|n>>>9)+f|0,n=((n+=((f=((f+=((h=((h+=((e=((e+=(n^f^h)+r[9]-640364487|0)<<4|e>>>28)+n|0)^n^f)+r[12]-421815835|0)<<11|h>>>21)+e|0)^e^n)+r[15]+530742520|0)<<16|f>>>16)+h|0)^h^e)+r[2]-995338651|0)<<23|n>>>9)+f|0,n=((n+=((h=((h+=(n^((e=((e+=(f^(n|~h))+r[0]-198630844|0)<<6|e>>>26)+n|0)|~f))+r[7]+1126891415|0)<<10|h>>>22)+e|0)^((f=((f+=(e^(h|~n))+r[14]-1416354905|0)<<15|f>>>17)+h|0)|~e))+r[5]-57434055|0)<<21|n>>>11)+f|0,n=((n+=((h=((h+=(n^((e=((e+=(f^(n|~h))+r[12]+1700485571|0)<<6|e>>>26)+n|0)|~f))+r[3]-1894986606|0)<<10|h>>>22)+e|0)^((f=((f+=(e^(h|~n))+r[10]-1051523|0)<<15|f>>>17)+h|0)|~e))+r[1]-2054922799|0)<<21|n>>>11)+f|0,n=((n+=((h=((h+=(n^((e=((e+=(f^(n|~h))+r[8]+1873313359|0)<<6|e>>>26)+n|0)|~f))+r[15]-30611744|0)<<10|h>>>22)+e|0)^((f=((f+=(e^(h|~n))+r[6]-1560198380|0)<<15|f>>>17)+h|0)|~e))+r[13]+1309151649|0)<<21|n>>>11)+f|0,n=((n+=((h=((h+=(n^((e=((e+=(f^(n|~h))+r[4]-145523070|0)<<6|e>>>26)+n|0)|~f))+r[11]-1120210379|0)<<10|h>>>22)+e|0)^((f=((f+=(e^(h|~n))+r[2]+718787259|0)<<15|f>>>17)+h|0)|~e))+r[9]-343485551|0)<<21|n>>>11)+f|0,t[0]=e+t[0]|0,t[1]=n+t[1]|0,t[2]=f+t[2]|0,t[3]=h+t[3]|0}function n(t){var r,e=[];for(r=0;r<64;r+=4)e[r>>2]=t.charCodeAt(r)+(t.charCodeAt(r+1)<<8)+(t.charCodeAt(r+2)<<16)+(t.charCodeAt(r+3)<<24);return e}function f(t){var r,e=[];for(r=0;r<64;r+=4)e[r>>2]=t[r]+(t[r+1]<<8)+(t[r+2]<<16)+(t[r+3]<<24);return e}function h(t){var r,f,h,i,o,s,a=t.length,u=[1732584193,-271733879,-1732584194,271733878];for(r=64;r<=a;r+=64)e(u,n(t.substring(r-64,r)));for(f=(t=t.substring(r-64)).length,h=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],r=0;r<f;r+=1)h[r>>2]|=t.charCodeAt(r)<<(r%4<<3);if(h[r>>2]|=128<<(r%4<<3),r>55)for(e(u,h),r=0;r<16;r+=1)h[r]=0;return i=(i=8*a).toString(16).match(/(.*?)(.{0,8})$/),o=parseInt(i[2],16),s=parseInt(i[1],16)||0,h[14]=o,h[15]=s,e(u,h),u}function i(t){var r,n,h,i,o,s,a=t.length,u=[1732584193,-271733879,-1732584194,271733878];for(r=64;r<=a;r+=64)e(u,f(t.subarray(r-64,r)));for(n=(t=r-64<a?t.subarray(r-64):new Uint8Array(0)).length,h=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],r=0;r<n;r+=1)h[r>>2]|=t[r]<<(r%4<<3);if(h[r>>2]|=128<<(r%4<<3),r>55)for(e(u,h),r=0;r<16;r+=1)h[r]=0;return i=(i=8*a).toString(16).match(/(.*?)(.{0,8})$/),o=parseInt(i[2],16),s=parseInt(i[1],16)||0,h[14]=o,h[15]=s,e(u,h),u}function o(t){var e,n="";for(e=0;e<4;e+=1)n+=r[t>>8*e+4&15]+r[t>>8*e&15];return n}function s(t){var r;for(r=0;r<t.length;r+=1)t[r]=o(t[r]);return t.join("")}function a(t){return/[\u0080-\uFFFF]/.test(t)&&(t=unescape(encodeURIComponent(t))),t}function u(t,r){var e,n=t.length,f=new ArrayBuffer(n),h=new Uint8Array(f);for(e=0;e<n;e+=1)h[e]=t.charCodeAt(e);return r?h:f}function p(t){return String.fromCharCode.apply(null,new Uint8Array(t))}function y(t,r,e){var n=new Uint8Array(t.byteLength+r.byteLength);return n.set(new Uint8Array(t)),n.set(new Uint8Array(r),t.byteLength),e?n:n.buffer}function c(t){var r,e=[],n=t.length;for(r=0;r<n-1;r+=2)e.push(parseInt(t.substr(r,2),16));return String.fromCharCode.apply(String,e)}function g(){this.reset()}return s(h("hello")),"undefined"==typeof ArrayBuffer||ArrayBuffer.prototype.slice||function(){function r(t,r){return(t=0|t||0)<0?Math.max(t+r,0):Math.min(t,r)}ArrayBuffer.prototype.slice=function(e,n){var f,h,i,o,s=this.byteLength,a=r(e,s),u=s;return n!==t&&(u=r(n,s)),a>u?new ArrayBuffer(0):(f=u-a,h=new ArrayBuffer(f),i=new Uint8Array(h),o=new Uint8Array(this,a,f),i.set(o),h)}}(),g.prototype.append=function(t){return this.appendBinary(a(t)),this},g.prototype.appendBinary=function(t){this._buff+=t,this._length+=t.length;var r,f=this._buff.length;for(r=64;r<=f;r+=64)e(this._hash,n(this._buff.substring(r-64,r)));return this._buff=this._buff.substring(r-64),this},g.prototype.end=function(t){var r,e,n=this._buff,f=n.length,h=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(r=0;r<f;r+=1)h[r>>2]|=n.charCodeAt(r)<<(r%4<<3);return this._finish(h,f),e=s(this._hash),t&&(e=c(e)),this.reset(),e},g.prototype.reset=function(){return this._buff="",this._length=0,this._hash=[1732584193,-271733879,-1732584194,271733878],this},g.prototype.getState=function(){return{buff:this._buff,length:this._length,hash:this._hash.slice()}},g.prototype.setState=function(t){return this._buff=t.buff,this._length=t.length,this._hash=t.hash,this},g.prototype.destroy=function(){delete this._hash,delete this._buff,delete this._length},g.prototype._finish=function(t,r){var n,f,h,i=r;if(t[i>>2]|=128<<(i%4<<3),i>55)for(e(this._hash,t),i=0;i<16;i+=1)t[i]=0;n=(n=8*this._length).toString(16).match(/(.*?)(.{0,8})$/),f=parseInt(n[2],16),h=parseInt(n[1],16)||0,t[14]=f,t[15]=h,e(this._hash,t)},g.hash=function(t,r){return g.hashBinary(a(t),r)},g.hashBinary=function(t,r){var e=s(h(t));return r?c(e):e},g.ArrayBuffer=function(){this.reset()},g.ArrayBuffer.prototype.append=function(t){var r,n=y(this._buff.buffer,t,!0),h=n.length;for(this._length+=t.byteLength,r=64;r<=h;r+=64)e(this._hash,f(n.subarray(r-64,r)));return this._buff=r-64<h?new Uint8Array(n.buffer.slice(r-64)):new Uint8Array(0),this},g.ArrayBuffer.prototype.end=function(t){var r,e,n=this._buff,f=n.length,h=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(r=0;r<f;r+=1)h[r>>2]|=n[r]<<(r%4<<3);return this._finish(h,f),e=s(this._hash),t&&(e=c(e)),this.reset(),e},g.ArrayBuffer.prototype.reset=function(){return this._buff=new Uint8Array(0),this._length=0,this._hash=[1732584193,-271733879,-1732584194,271733878],this},g.ArrayBuffer.prototype.getState=function(){var t=g.prototype.getState.call(this);return t.buff=p(t.buff),t},g.ArrayBuffer.prototype.setState=function(t){return t.buff=u(t.buff,!0),g.prototype.setState.call(this,t)},g.ArrayBuffer.prototype.destroy=g.prototype.destroy,g.ArrayBuffer.prototype._finish=g.prototype._finish,g.ArrayBuffer.hash=function(t,r){var e=s(i(new Uint8Array(t)));return r?c(e):e},g}());export{e as S};
