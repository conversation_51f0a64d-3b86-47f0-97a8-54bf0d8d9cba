import{h as e}from"./@babel-f3c0a00c.js";import{b as t}from"./batch-processor-06abf2b4.js";var n={exports:{}};(n.exports={}).forEach=function(e,t){for(var n=0;n<e.length;n++){var i=t(e[n]);if(i)return i}};var i=n.exports,o={exports:{}},r=o.exports={};r.isIE=function(e){return(-1!==(t=navigator.userAgent.toLowerCase()).indexOf("msie")||-1!==t.indexOf("trident")||-1!==t.indexOf(" edge/"))&&(!e||e===function(){var e=3,t=document.createElement("div"),n=t.getElementsByTagName("i");do{t.innerHTML="\x3c!--[if gt IE "+ ++e+"]><i></i><![endif]--\x3e"}while(n[0]);return e>4?e:undefined}());var t},r.isLegacyOpera=function(){return!!window.opera};var a=o.exports,l="_erd";function s(e){return e[l]}var d={initState:function(e){return e[l]={},s(e)},getState:s,cleanState:function(e){delete e[l]}},c=a,u=i.forEach,h=i.forEach,f=function(e){var t=e.stateHandler.getState;return{isDetectable:function(e){var n=t(e);return n&&!!n.isDetectable},markAsDetectable:function(e){t(e).isDetectable=!0},isBusy:function(e){return!!t(e).busy},markBusy:function(e,n){t(e).busy=!!n}}},m=function(e){var t={};function n(n){var i=e.get(n);return void 0===i?[]:t[i]||[]}return{get:n,add:function(n,i){var o=e.get(n);t[o]||(t[o]=[]),t[o].push(i)},removeListener:function(e,t){for(var i=n(e),o=0,r=i.length;o<r;++o)if(i[o]===t){i.splice(o,1);break}},removeAllListeners:function(e){var t=n(e);t&&(t.length=0)}}},p=function(){var e=1;return{generate:function(){return e++}}},g=function(e){var t=e.idGenerator,n=e.stateHandler.getState;return{get:function(e){var t=n(e);return t&&void 0!==t.id?t.id:null},set:function(e){var i=n(e);if(!i)throw new Error("setId required the element to have a resize detection state.");var o=t.generate();return i.id=o,o}}},v=function(e){function t(){}var n={log:t,warn:t,error:t};if(!e&&window.console){var i=function(e,t){e[t]=function(){var e=console[t];if(e.apply)e.apply(console,arguments);else for(var n=0;n<arguments.length;n++)e(arguments[n])}};i(n,"log"),i(n,"warn"),i(n,"error")}return n},b=a,y=t,w=d,E=function(e){var t=(e=e||{}).reporter,n=e.batchProcessor,i=e.stateHandler.getState;if(!t)throw new Error("Missing required dependency: reporter.");function o(e){return i(e).object}return{makeDetectable:function(o,r,a){a||(a=r,r=o,o=null),(o=o||{}).debug,c.isIE(8)?a(r):function(r,a){var l,s,d=(l=["display: block","position: absolute","top: 0","left: 0","width: 100%","height: 100%","border: none","padding: 0","margin: 0","opacity: 0","z-index: -1000","pointer-events: none"],s=e.important?" !important; ":"; ",(l.join(s)+s).trim()),u=!1,h=window.getComputedStyle(r),f=r.offsetWidth,m=r.offsetHeight;function p(){function e(){if("static"===h.position){r.style.setProperty("position","relative",o.important?"important":"");var e=function(e,t,n,i){var r=n[i];"auto"!==r&&"0"!==r.replace(/[^-\d\.]/g,"")&&(e.warn("An element that is positioned static has style."+i+"="+r+" which is ignored due to the static positioning. The element will need to be positioned relative, so the style."+i+" will be set to 0. Element: ",t),t.style.setProperty(i,"0",o.important?"important":""))};e(t,r,h,"top"),e(t,r,h,"right"),e(t,r,h,"bottom"),e(t,r,h,"left")}}""!==h.position&&(e(),u=!0);var n=document.createElement("object");n.style.cssText=d,n.tabIndex=-1,n.type="text/html",n.setAttribute("aria-hidden","true"),n.onload=function(){u||e(),function e(t,n){if(!t.contentDocument){var o=i(t);return o.checkForObjectDocumentTimeoutId&&window.clearTimeout(o.checkForObjectDocumentTimeoutId),void(o.checkForObjectDocumentTimeoutId=setTimeout((function(){o.checkForObjectDocumentTimeoutId=0,e(t,n)}),100))}n(t.contentDocument)}(this,(function(e){a(r)}))},c.isIE()||(n.data="about:blank"),i(r)&&(r.appendChild(n),i(r).object=n,c.isIE()&&(n.data="about:blank"))}i(r).startSize={width:f,height:m},n?n.add(p):p()}(r,a)},addListener:function(e,t){function n(){t(e)}if(c.isIE(8))i(e).object={proxy:n},e.attachEvent("onresize",n);else{var r=o(e);if(!r)throw new Error("Element is not detectable by this strategy.");r.contentDocument.defaultView.addEventListener("resize",n)}},uninstall:function(e){if(i(e)){var t=o(e);t&&(c.isIE(8)?e.detachEvent("onresize",t.proxy):e.removeChild(t),i(e).checkForObjectDocumentTimeoutId&&window.clearTimeout(i(e).checkForObjectDocumentTimeoutId),delete i(e).object)}}}},x=function(e){var t=(e=e||{}).reporter,n=e.batchProcessor,i=e.stateHandler.getState;e.stateHandler.hasState;var o=e.idHandler;if(!n)throw new Error("Missing required dependency: batchProcessor");if(!t)throw new Error("Missing required dependency: reporter.");var r=function(){var e=500,t=500,n=document.createElement("div");n.style.cssText=s(["position: absolute","width: 1000px","height: 1000px","visibility: hidden","margin: 0","padding: 0"]);var i=document.createElement("div");i.style.cssText=s(["position: absolute","width: 500px","height: 500px","overflow: scroll","visibility: none","top: -1500px","left: -1500px","visibility: hidden","margin: 0","padding: 0"]),i.appendChild(n),document.body.insertBefore(i,document.body.firstChild);var o=e-i.clientWidth,r=t-i.clientHeight;return document.body.removeChild(i),{width:o,height:r}}(),a="erd_scroll_detection_container";function l(e){!function(e,t,n){function i(n,i){i=i||function(t){e.head.appendChild(t)};var o=e.createElement("style");return o.innerHTML=n,o.id=t,i(o),o}if(!e.getElementById(t)){var o=n+"_animation",r=n+"_animation_active",a="/* Created by the element-resize-detector library. */\n";a+="."+n+" > div::-webkit-scrollbar { "+s(["display: none"])+" }\n\n",a+="."+r+" { "+s(["-webkit-animation-duration: 0.1s","animation-duration: 0.1s","-webkit-animation-name: "+o,"animation-name: "+o])+" }\n",a+="@-webkit-keyframes "+o+" { 0% { opacity: 1; } 50% { opacity: 0; } 100% { opacity: 1; } }\n",i(a+="@keyframes "+o+" { 0% { opacity: 1; } 50% { opacity: 0; } 100% { opacity: 1; } }")}}(e,"erd_scroll_detection_scrollbar_style",a)}function s(t){var n=e.important?" !important; ":"; ";return(t.join(n)+n).trim()}function d(e,n,i){if(e.addEventListener)e.addEventListener(n,i);else{if(!e.attachEvent)return t.error("[scroll] Don't know how to add event listeners.");e.attachEvent("on"+n,i)}}function c(e,n,i){if(e.removeEventListener)e.removeEventListener(n,i);else{if(!e.detachEvent)return t.error("[scroll] Don't know how to remove event listeners.");e.detachEvent("on"+n,i)}}function h(e){return i(e).container.childNodes[0].childNodes[0].childNodes[0]}function f(e){return i(e).container.childNodes[0].childNodes[0].childNodes[1]}return l(window.document),{makeDetectable:function(e,l,c){function m(){if(e.debug){var n=Array.prototype.slice.call(arguments);if(n.unshift(o.get(l),"Scroll: "),t.log.apply)t.log.apply(null,n);else for(var i=0;i<n.length;i++)t.log(n[i])}}function p(e){var t=i(e).container.childNodes[0],n=window.getComputedStyle(t);return!n.width||-1===n.width.indexOf("px")}function g(){var e=window.getComputedStyle(l),t={};return t.position=e.position,t.width=l.offsetWidth,t.height=l.offsetHeight,t.top=e.top,t.right=e.right,t.bottom=e.bottom,t.left=e.left,t.widthCSS=e.width,t.heightCSS=e.height,t}function v(){if(m("storeStyle invoked."),i(l)){var e=g();i(l).style=e}else m("Aborting because element has been uninstalled")}function b(e,t,n){i(e).lastWidth=t,i(e).lastHeight=n}function y(){return 2*r.width+1}function w(){return 2*r.height+1}function E(e){return e+10+y()}function x(e){return e+10+w()}function S(e,t,n){var i=h(e),o=f(e),r=E(t),a=x(n),l=function(e){return 2*e+y()}(t),s=function(e){return 2*e+w()}(n);i.scrollLeft=r,i.scrollTop=a,o.scrollLeft=l,o.scrollTop=s}function k(){var e=i(l).container;if(!e){(e=document.createElement("div")).className=a,e.style.cssText=s(["visibility: hidden","display: inline","width: 0px","height: 0px","z-index: -1","overflow: hidden","margin: 0","padding: 0"]),i(l).container=e,function(e){e.className+=" "+a+"_animation_active"}(e),l.appendChild(e);var t=function(){i(l).onRendered&&i(l).onRendered()};d(e,"animationstart",t),i(l).onAnimationStart=t}return e}function A(){if(m("Injecting elements"),i(l)){!function(){var n=i(l).style;if("static"===n.position){l.style.setProperty("position","relative",e.important?"important":"");var o=function(e,t,n,i){var o=n[i];"auto"!==o&&"0"!==o.replace(/[^-\d\.]/g,"")&&(e.warn("An element that is positioned static has style."+i+"="+o+" which is ignored due to the static positioning. The element will need to be positioned relative, so the style."+i+" will be set to 0. Element: ",t),t.style[i]=0)};o(t,l,n,"top"),o(t,l,n,"right"),o(t,l,n,"bottom"),o(t,l,n,"left")}}();var n=i(l).container;n||(n=k());var o,c,u,h,f=r.width,p=r.height,g=s(["position: absolute","flex: none","overflow: hidden","z-index: -1","visibility: hidden","width: 100%","height: 100%","left: 0px","top: 0px"]),v=s(["position: absolute","flex: none","overflow: hidden","z-index: -1","visibility: hidden"].concat(["left: "+(o=(o=-(1+f))?o+"px":"0"),"top: "+(c=(c=-(1+p))?c+"px":"0"),"right: "+(h=(h=-f)?h+"px":"0"),"bottom: "+(u=(u=-p)?u+"px":"0")])),b=s(["position: absolute","flex: none","overflow: scroll","z-index: -1","visibility: hidden","width: 100%","height: 100%"]),y=s(["position: absolute","flex: none","overflow: scroll","z-index: -1","visibility: hidden","width: 100%","height: 100%"]),w=s(["position: absolute","left: 0","top: 0"]),E=s(["position: absolute","width: 200%","height: 200%"]),x=document.createElement("div"),S=document.createElement("div"),A=document.createElement("div"),D=document.createElement("div"),z=document.createElement("div"),T=document.createElement("div");x.dir="ltr",x.style.cssText=g,x.className=a,S.className=a,S.style.cssText=v,A.style.cssText=b,D.style.cssText=w,z.style.cssText=y,T.style.cssText=E,A.appendChild(D),z.appendChild(T),S.appendChild(A),S.appendChild(z),x.appendChild(S),n.appendChild(x),d(A,"scroll",H),d(z,"scroll",L),i(l).onExpandScroll=H,i(l).onShrinkScroll=L}else m("Aborting because element has been uninstalled");function H(){var e=i(l);e&&e.onExpand?e.onExpand():m("Aborting expand scroll handler: element has been uninstalled")}function L(){var e=i(l);e&&e.onShrink?e.onShrink():m("Aborting shrink scroll handler: element has been uninstalled")}}function D(){function r(t,n,i){var o=function(e){return h(e).childNodes[0]}(t),r=E(n),a=x(i);o.style.setProperty("width",r+"px",e.important?"important":""),o.style.setProperty("height",a+"px",e.important?"important":"")}function a(a){var d=l.offsetWidth,c=l.offsetHeight,u=d!==i(l).lastWidth||c!==i(l).lastHeight;m("Storing current size",d,c),b(l,d,c),n.add(0,(function(){if(u)if(i(l))if(s()){if(e.debug){var n=l.offsetWidth,a=l.offsetHeight;n===d&&a===c||t.warn(o.get(l),"Scroll: Size changed before updating detector elements.")}r(l,d,c)}else m("Aborting because element container has not been initialized");else m("Aborting because element has been uninstalled")})),n.add(1,(function(){i(l)?s()?S(l,d,c):m("Aborting because element container has not been initialized"):m("Aborting because element has been uninstalled")})),u&&a&&n.add(2,(function(){i(l)?s()?a():m("Aborting because element container has not been initialized"):m("Aborting because element has been uninstalled")}))}function s(){return!!i(l).container}function d(){m("notifyListenersIfNeeded invoked");var e=i(l);return void 0===i(l).lastNotifiedWidth&&e.lastWidth===e.startSize.width&&e.lastHeight===e.startSize.height?m("Not notifying: Size is the same as the start size, and there has been no notification yet."):e.lastWidth===e.lastNotifiedWidth&&e.lastHeight===e.lastNotifiedHeight?m("Not notifying: Size already notified"):(m("Current size not notified, notifying..."),e.lastNotifiedWidth=e.lastWidth,e.lastNotifiedHeight=e.lastHeight,void u(i(l).listeners,(function(e){e(l)})))}function c(){m("Scroll detected."),p(l)?m("Scroll event fired while unrendered. Ignoring..."):a(d)}if(m("registerListenersAndPositionElements invoked."),i(l)){i(l).onRendered=function(){if(m("startanimation triggered."),p(l))m("Ignoring since element is still unrendered...");else{m("Element rendered.");var e=h(l),t=f(l);0!==e.scrollLeft&&0!==e.scrollTop&&0!==t.scrollLeft&&0!==t.scrollTop||(m("Scrollbars out of sync. Updating detector elements..."),a(d))}},i(l).onExpand=c,i(l).onShrink=c;var g=i(l).style;r(l,g.width,g.height)}else m("Aborting because element has been uninstalled")}function z(){if(m("finalizeDomMutation invoked."),i(l)){var e=i(l).style;b(l,e.width,e.height),S(l,e.width,e.height)}else m("Aborting because element has been uninstalled")}function T(){c(l)}function H(){var e;m("Installing..."),i(l).listeners=[],e=g(),i(l).startSize={width:e.width,height:e.height},m("Element start size",i(l).startSize),n.add(0,v),n.add(1,A),n.add(2,D),n.add(3,z),n.add(4,T)}var L,C,I;c||(c=l,l=e,e=null),e=e||{},m("Making detectable..."),I=(C=L=l).getRootNode&&C.getRootNode().contains(C),C!==C.ownerDocument.body&&!C.ownerDocument.body.contains(C)&&!I||null===window.getComputedStyle(L)?(m("Element is detached"),k(),m("Waiting until element is attached..."),i(l).onRendered=function(){m("Element is now attached"),H()}):H()},addListener:function(e,t){if(!i(e).listeners.push)throw new Error("Cannot add listener to an element that is not detectable.");i(e).listeners.push(t)},uninstall:function(e){var t=i(e);t&&(t.onExpandScroll&&c(h(e),"scroll",t.onExpandScroll),t.onShrinkScroll&&c(f(e),"scroll",t.onShrinkScroll),t.onAnimationStart&&c(t.container,"animationstart",t.onAnimationStart),t.container&&e.removeChild(t.container))},initDocument:l}};function S(e){return Array.isArray(e)||void 0!==e.length}function k(e){if(Array.isArray(e))return e;var t=[];return h(e,(function(e){t.push(e)})),t}function A(e){return e&&1===e.nodeType}function D(e,t,n){var i=e[t];return null==i&&void 0!==n?n:i}const z=e((function(e){var t;if((e=e||{}).idHandler)t={get:function(t){return e.idHandler.get(t,!0)},set:e.idHandler.set};else{var n=p(),i=g({idGenerator:n,stateHandler:w});t=i}var o=e.reporter;o||(o=v(!1===o));var r=D(e,"batchProcessor",y({reporter:o})),a={};a.callOnAdd=!!D(e,"callOnAdd",!0),a.debug=!!D(e,"debug",!1);var l,s=m(t),d=f({stateHandler:w}),c=D(e,"strategy","object"),u=D(e,"important",!1),z={reporter:o,batchProcessor:r,stateHandler:w,idHandler:t,important:u};if("scroll"===c&&(b.isLegacyOpera()?(o.warn("Scroll strategy is not supported on legacy Opera. Changing to object strategy."),c="object"):b.isIE(9)&&(o.warn("Scroll strategy is not supported on IE9. Changing to object strategy."),c="object")),"scroll"===c)l=x(z);else{if("object"!==c)throw new Error("Invalid strategy name: "+c);l=E(z)}var T={};return{listenTo:function(e,n,i){function r(e){var t=s.get(e);h(t,(function(t){t(e)}))}function c(e,t,n){s.add(t,n),e&&n(t)}if(i||(i=n,n=e,e={}),!n)throw new Error("At least one element required.");if(!i)throw new Error("Listener required.");if(A(n))n=[n];else{if(!S(n))return o.error("Invalid arguments. Must be a DOM element or a collection of DOM elements.");n=k(n)}var f=0,m=D(e,"callOnAdd",a.callOnAdd),p=D(e,"onReady",(function(){})),g=D(e,"debug",a.debug);h(n,(function(e){w.getState(e)||(w.initState(e),t.set(e));var a=t.get(e);if(g&&o.log("Attaching listener to element",a,e),!d.isDetectable(e))return g&&o.log(a,"Not detectable."),d.isBusy(e)?(g&&o.log(a,"System busy making it detectable"),c(m,e,i),T[a]=T[a]||[],void T[a].push((function(){++f===n.length&&p()}))):(g&&o.log(a,"Making detectable..."),d.markBusy(e,!0),l.makeDetectable({debug:g,important:u},e,(function(e){if(g&&o.log(a,"onElementDetectable"),w.getState(e)){d.markAsDetectable(e),d.markBusy(e,!1),l.addListener(e,r),c(m,e,i);var t=w.getState(e);if(t&&t.startSize){var s=e.offsetWidth,u=e.offsetHeight;t.startSize.width===s&&t.startSize.height===u||r(e)}T[a]&&h(T[a],(function(e){e()}))}else g&&o.log(a,"Element uninstalled before being detectable.");delete T[a],++f===n.length&&p()})));g&&o.log(a,"Already detecable, adding listener."),c(m,e,i),f++})),f===n.length&&p()},removeListener:s.removeListener,removeAllListeners:s.removeAllListeners,uninstall:function(e){if(!e)return o.error("At least one element is required.");if(A(e))e=[e];else{if(!S(e))return o.error("Invalid arguments. Must be a DOM element or a collection of DOM elements.");e=k(e)}h(e,(function(e){s.removeAllListeners(e),l.uninstall(e),w.cleanState(e)}))},initDocument:function(e){l.initDocument&&l.initDocument(e)}}}));export{z as e};
