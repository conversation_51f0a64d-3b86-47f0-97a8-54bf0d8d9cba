import{_ as s,u as e,a as l}from"./index-a5df0f75.js";import{_ as t}from"./index-04837d66.js";import{X as i}from"./element-plus-95e0b914.js";import"./vue-5bfa3a54.js";import{g as a}from"./index-05697651.js";import{g as n}from"./imgImport-cfe60b78.js";import"./fabric-8dd10b04.js";import{d as r}from"./dayjs-67f8ddef.js";import{x as o}from"./xe-utils-fe99d42a.js";import{d as c}from"./@vueuse-5227c686.js";import{h as m,j as p,e as u,m as g,v as f,as as d,o as j,c as v,a as h,t as b,b as x,f as k,l as y,x as w,F as z,k as _,y as N,a8 as C,C as I,D}from"./@vue-5e5cdef9.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./vue-router-6159329f.js";import"./bignumber.js-a537a5ca.js";import"./js-cookie-8253c38e.js";import"./axios-84f1a956.js";import"./lodash-es-ea7deab5.js";import"./spark-md5-022b35d0.js";import"./@babel-f3c0a00c.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";import"./quasar-df1bac18.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./@element-plus-4c34063a.js";import"./vxe-table-plugin-element-be30dbb6.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./lodash-6d99edc3.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./screenfull-c82f2093.js";import"./chartResize-3e3d11d7.js";import"./element-resize-detector-0d37a2ab.js";import"./batch-processor-06abf2b4.js";const R=s=>(I("data-v-6b1a8d24"),s=s(),D(),s),Y={class:"header"},H={class:"date font-small-text-size2 normal regular-title"},S={class:"title"},M={class:"main-title font-title-size text"},L={class:"screen-full"},W={class:"left-sensor"},X={class:"item-title-bg font-title-size"},q=["src"],P={class:"sensor-box"},U={key:0,class:"circle-img"},V=["src"],A=["src"],B={key:1,class:"circle-img"},F=["src"],G={class:"u-flex-column"},J={class:"font-small-text-size2"},K={key:0},O={key:1},T={class:"smokeValue font-small-text-size1"},Z={class:"smokeUnit font-small-text-size3"},$={class:"right-sensor"},E={class:"item-title-bg font-title-size"},Q=["src"],ss={class:"sensor-box"},es={key:0,class:"circle-img"},ls=["src"],ts=["src"],is={key:1,class:"circle-img"},as=["src"],ns={class:"u-flex-column"},rs={class:"font-small-text-size2"},os={key:0},cs={key:1},ms={class:"smokeValue font-small-text-size1"},ps={class:"smokeUnit font-small-text-size3"},us={class:"title"},gs={class:"item-title-bg font-title-size"},fs=["src"],ds=R((()=>h("span",null,"配电房1",-1))),js={class:"right-wiringDiagram"},vs={class:"title"},hs={class:"item-title-bg font-title-size"},bs=["src"],xs=R((()=>h("span",null,"配电房2",-1))),ks=s({__name:"gridConnectedCabinetCopy2",setup(s){const I=e(),D=m(),R=p({data:r().format("YYYY-MM-DD HH:mm:ss")}),ks=m();m(),m(),m(),m(),m(),m(),m(),m(),m(!1),m();const ys=p([]),ws={alarmStatus:{icon:"",label:"故障状态",value:"报警",unit:"",picName:"warn.png",end:!1},smokeConcentr:{icon:"mist",label:"烟雾浓度",value:0,unit:"PPM",picName:"",end:!1},temp:{icon:"temp",label:"当前温度",value:0,unit:"°C",picName:"",end:!1},humidity:{icon:"humidity",label:"当前湿度",value:0,unit:"%RH",picName:"",end:!0}},zs=p([]),_s=m(null),Ns=m(null);m(null);const Cs=p({width:1200,height:900}),Is={width:Cs.width,height:Cs.height},Ds=new window.Image;Ds.src=n("bingwang","R6-30-50K.png");const Rs=new window.Image;Rs.src=n("bingwang","1.png");const Ys={image:Ds,x:0,y:0,scaleX:.3,scaleY:.3},Hs={image:Rs,x:200,y:300,scaleX:.4,scaleY:.4};u((()=>Cs.width/10));const Ss=()=>{Ns.value.getNode().attrs.height=700},Ms=o.debounce((()=>{c(_s,(s=>{Cs.width=_s.value.offsetWidth,Cs.height=_s.value.offsetHeight,Ss()}))}),300);return g((()=>{(async()=>{const{data:s}=await a();for(let e=0;e<s.length;e++){const l=o.clone(ws,!0),t=Object.keys(l);ys.push(s[e].deviceName);for(let i=0;i<t.length;i++)"alarmStatus"===t[i]&&0==s[e].children[0].alarmStatus?l[t[i]].value="正常":"alarmStatus"===t[i]&&1==s[e].children[0].alarmStatus?l[t[i]].value="告警":l[t[i]].value=s[e].children[0][t[i]];zs.push(l)}})(),Ss(),Ms()})),f((()=>{})),(s,e)=>{const a=i,r=t,o=l,c=d("v-image"),m=d("v-layer"),p=d("v-stage");return j(),v("div",{class:"screenfull-content tw-h-full tw-w-full bwg-bg screen-box",ref_key:"screenRef",ref:D},[h("div",Y,[h("div",H,b(x(R).data),1),h("div",S,[x(I).userInfo.screenLogo?(j(),k(a,{key:0,src:x(I).userInfo.screenLogo,fit:"contain",class:"image"},null,8,["src"])):y("",!0),h("h1",M,b(x(I).userInfo.projectTitle),1)]),h("div",L,[w(r,{class:"setting-item",type:"font",element:x(D)},null,8,["element"])])]),h("div",W,[h("div",null,[h("p",X,[h("img",{src:x(n)("screen","title_icon.png"),class:"title-icon"},null,8,q),h("span",null,b(x(ys)[0]),1)])]),h("div",P,[(j(!0),v(z,null,_(x(zs)[0],((s,e)=>(j(),v("div",{key:e,class:"u-flex-1 h-full u-flex-center-no asset-content-item"},[""!==s.picName?(j(),v("p",U,[h("img",{src:x(n)("screen","circle.png"),class:"title-icon0"},null,8,V),h("img",{src:x(n)("screen",s.picName),class:"title-icon1"},null,8,A)])):(j(),v("p",B,[h("img",{src:x(n)("screen","circle.png"),class:"title-icon0"},null,8,F),w(o,{name:s.icon,class:"title-icon1"},null,8,["name"])])),h("p",G,[h("span",J,b(s.label),1),"故障状态"===s.label?(j(),v("span",K,[h("i",{class:N(["font-small-text-size1",!0===s.value?"normal":"abnormal"])},b(s.value),3)])):(j(),v("span",O,[h("i",T,b(s.value),1),h("i",Z,b(s.unit),1)]))])])))),128))])]),h("div",$,[h("p",E,[h("img",{src:x(n)("screen","title_icon.png"),class:"title-icon"},null,8,Q),h("span",null,b(x(ys)[1]),1)]),h("div",ss,[(j(!0),v(z,null,_(x(zs)[1],((s,e)=>(j(),v("div",{key:e,class:"u-flex-1 h-full u-flex-center-no asset-content-item"},[""!==s.picName?(j(),v("p",es,[h("img",{src:x(n)("screen","circle.png"),class:"title-icon0"},null,8,ls),h("img",{src:x(n)("screen",s.picName),class:"title-icon1"},null,8,ts)])):(j(),v("p",is,[h("img",{src:x(n)("screen","circle.png"),class:"title-icon0"},null,8,as),w(o,{name:s.icon,class:"title-icon1"},null,8,["name"])])),h("p",ns,[h("span",rs,b(s.label),1),"故障状态"===s.label?(j(),v("span",os,[h("i",{class:N(["font-small-text-size1",!0===s.value?"normal":"abnormal"])},b(s.value),3)])):(j(),v("span",cs,[h("i",ms,b(s.value),1),h("i",ps,b(s.unit),1)]))])])))),128))])]),h("div",{class:"left-wiringDiagram",ref_key:"leftWiringDiagramRef",ref:_s},[h("div",us,[h("p",gs,[h("img",{src:x(n)("screen","title_icon.png"),class:"title-icon"},null,8,fs),ds]),w(p,{config:Is},{default:C((()=>[w(m,{ref_key:"leftCanvasRef",ref:Ns},{default:C((()=>[(j(),v(z,null,_([0,1,2,3,4,5],((s,e)=>w(c,{ref_for:!0,ref_key:"iref",ref:ks,config:{...Ys,x:Ys.x+150*s}},null,8,["config"]))),64)),(j(),v(z,null,_([0,1],((s,e)=>w(c,{config:{...Hs,x:Hs.x+400*s}},null,8,["config"]))),64))])),_:1},512)])),_:1})])],512),h("div",js,[h("div",vs,[h("p",hs,[h("img",{src:x(n)("screen","title_icon.png"),class:"title-icon"},null,8,bs),xs])])])],512)}}},[["__scopeId","data-v-6b1a8d24"]]);export{ks as default};
