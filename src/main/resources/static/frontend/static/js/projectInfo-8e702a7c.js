import{f as t,R as e}from"./element-plus-95e0b914.js";import"./vue-5bfa3a54.js";import{j as a,k as s,l as o,m as l,h as i,b as r,n}from"./naive-ui-0ee0b8c3.js";import{o as p,f as m,a8 as c,g as u,as as d,c as f,F as j,aa as g,t as h,a6 as v,r as w,e as b,l as x,x as k,k as _,y,j as N,w as C,m as I,b as F,a as L,C as T,D as z}from"./@vue-5e5cdef9.js";import{_ as P}from"./MyTable-15b6ab92.js";import{_ as A}from"./pagination-c4d8e88e.js";import{K as D}from"./quasar-df1bac18.js";import{_ as U}from"./index-a5df0f75.js";import{l as E}from"./lodash-6d99edc3.js";import{f as S}from"./formatTableData-0442e1d7.js";import{a as B,s as V,g as q}from"./api-360ec627.js";import{m as M}from"./notification-950a5f80.js";import{f as O}from"./formUtil-7f692cbf.js";import{X as R}from"./statisticReportApi-8df5b5f8.js";import"./lodash-es-ea7deab5.js";import"./@vueuse-5227c686.js";import"./vue-router-6159329f.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./dayjs-67f8ddef.js";import"./@babel-f3c0a00c.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./date-fns-tz-0cc073c8.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./xe-utils-fe99d42a.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-be30dbb6.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./menuStore-30bf76d3.js";import"./icons-95011f8c.js";import"./@vicons-f32a0bdb.js";const K={__name:"MyDialog",props:["dialog"],setup(t){const e=t;return(t,s)=>{const o=a;return p(),m(o,{show:e.dialog.show,"onUpdate:show":s[0]||(s[0]=t=>e.dialog.show=t),"mask-closable":!1,preset:"dialog",showIcon:!1,title:e.dialog.title||"确认","positive-text":e.dialog.positive||"确认","negative-text":e.dialog.negative||"取消",onPositiveClick:e.dialog.callback,onNegativeClick:()=>{}},{default:c((()=>[u(t.$slots,"default")])),_:3},8,["show","title","positive-text","negative-text","onPositiveClick"])}}},W={__name:"ComFormItem",props:["item"],setup:t=>(e,a)=>{const o=d("ComFormItem",!0);return p(),m(w(s[t.item.is]),v({value:t.item.value,"onUpdate:value":a[0]||(a[0]=e=>t.item.value=e)},t.item,{onClick:a[1]||(a[1]=e=>!t.item.callback||t.item.callback(t.item))}),{default:c((()=>[t.item.slotText?(p(),f(j,{key:0},[g(h(t.item.slotText),1)],64)):(p(),m(o,{key:1,item:t.item.slot},null,8,["item"]))])),_:1},16,["value"])}},X={class:"tw-flex tw-items-center tw-w-full"},$=U({__name:"ComForm",props:["formList","title","inline","labelPosition","itemClass"],setup(t){const e=t,a=b((()=>e.formList.reduce(((t,e)=>(t[e.path]=e.value??e.loading,t)),{})));return e.formList._[e.title]=a,(t,a)=>{const s=o,r=W,n=l,u=D,d=i;return p(),f("div",X,[e.title?(p(),m(s,{key:0,class:"tw-text-[28px] tw-mr-2 tw-text-white"},{default:c((()=>[g(h(e.title),1)])),_:1})):x("",!0),k(d,{inline:e.inline??!0,"label-placement":e.labelPosition??"top","label-width":100,size:"medium",class:"tw-h-auto"},{default:c((()=>[(p(!0),f(j,null,_(e.formList,(t=>(p(),f(j,null,["space"!=t.is?(p(),m(n,{key:0,label:t.label,path:t.path,class:y(e.itemClass)},{default:c((()=>[k(r,{item:t},null,8,["item"])])),_:2},1032,["label","path","class"])):(p(),m(u,{key:1}))],64)))),256))])),_:1},8,["inline","label-placement"])])}}},[["__scopeId","data-v-335e68a5"]]);function J(t,e=t){if("object"!=typeof t||null===t)return t;return new Proxy(t,{get(t,a){if("_"===a)return e;return J(t[a],e)},set:(t,a,s)=>(t[a]=s,E._.isObject(s)&&Q(s,e),E._.isArray(s)&&s.forEach((t=>{Q(t,e)})),!0)})}function Q(t,e){if(t._=e,(t.path||t.prop)&&E._.isFunction(t.callback)){const a=t.prop||t.path;e.callback[a]=t}}function Z(t){if(Array.isArray(t))t.forEach((t=>{Z(t)}));else if(E._.isObject(t))for(const e in t)if("value"===e)t[e]=Array.isArray(t[e])?[]:"";else{if("_"===e)continue;Z(t[e])}}function G(t){t.callback={};const e=J(t);return e.clearValue=Z,e}const H={class:"tw-h-full tw-w-full tw-p-4"},Y={class:"tw-text-xl"},tt=(t=>(T("data-v-9073bbef"),t=t(),z(),t))((()=>L("span",null," excel 预览 ",-1))),et={class:"dialog-footer"},at=U({__name:"projectInfo",setup(a){const s=G(N({}));return s.page={page:0,pageSize:10,total:100},s.columns=S({plantName:"站点名称",contractId:"合同编号",userName:"用户名",userPhone:"用户手机号码",plantCapacity:"电站容量",address:"电站地址",state:"电站状态",projectName:"项目名称",createTime:"创建日期",updateTime:"更新日期",creator:"创建者",updater:"更新者"}),s.formList=[{is:"NInput",value:"",label:"站点名称",path:"plantName"},{is:"NInput",value:"",label:"手机号码",path:"userPhone"},{is:"NInput",value:"",label:"用户名",path:"userName"},{is:"NInput",value:"",label:"合同编号",path:"contractId"},{is:"NSelect",value:"",options:[{value:"1",label:"已注册"},{value:"0",label:"未注册"}],label:"状态",clearable:!0,class:"tw-w-[100px]",path:"state"},{is:"space"},{is:"NButton",type:"info",loading:!1,class:"tw-text-white ",slotText:"查询",path:"search",async callback(t){t.loading=!0;let e={...s.page,...s["项目信息"],currentPage:s.page.page};e=E._.omit(e,["_","undefined","total","page"]);const a=await(t=>B("/plant/CustomerContract/info",void 0,t,"post"))(e),o=q(a);s.rows=o.data.records.map((t=>({...t,state:t.state?"已注册":"未注册"}))),s.page.total=o.data.total,t.loading=!1}},{is:"NButton",type:"info",loading:!1,class:"tw-text-white",slotText:"新增",path:"add",async callback(){s.dialog.show=!0}},{is:"NUpload",async"custom-request"(t){const e=await V({method:"post",url:"/plant/CustomerContract/uploadExcel",data:{file:t.file.file},headers:{"Content-Type":"multipart/form-data"}});"00000"==e.status?(s.excelForm.show=!0,s.excelForm.data=e.data,s.excelData=e.data,t.onFinish()):t.onError()},"show-file-list":!1,slot:{is:"NButton",type:"info",loading:!1,class:"tw-text-white",slotText:"上传文件",path:"upload"}},{is:"NButton",type:"info",loading:!1,class:"tw-text-white",slotText:"模板下载",path:"downTemplate",callback(){window.open("https://www.btosolarman.com/assets/btosolar/picture/%E6%A8%A1%E6%9D%BF.xlsx")}}],s.dialog={show:!1,title:"新增",async callback(){const t=await(t=>B("/plant/CustomerContract/addInfo",void 0,t,"post"))(s["新增"]),e=q(t);"00000"!=e.status?M.error(e.message):M.success(e.message)},path:"dialog",formList:[{is:"NInput",value:"",label:"站点名称",path:"plantName"},{is:"NInput",value:"",label:"手机号码",path:"userPhone"},{is:"NInput",value:"",label:"创建者",path:"creator"},{is:"NInput",value:"",label:"电站容量",path:"plantCapacity"},{is:"NInput",value:"",label:"项目编号",path:"projectId"},{is:"NInput",value:"",label:"地址",path:"address"},{is:"NInput",value:"",label:"用户名",path:"userName"},{is:"NInput",value:"",label:"合同编号",path:"contractId"}]},s.excelForm={show:!1,title:"excel导入预览",async callback(){if(!s.excelForm.creator.trim().length)return void M.error("创建者是必填项");const t=[...s.excelForm.data.map((t=>E._.omit(t,"_")))].map((t=>({...t,creator:s.excelForm.creator})));JSON.stringify(t);const e=await(a=t,V({url:"/plant/CustomerContract/batchInsert",method:"post",data:a}));var a;"00000"==e.status?s.excelForm.show=!1:M.error(e.message)},path:"excel",pagination:{pageSize:10,page:1},creator:"",options:[],columns:O.createColumns({address:"地址",userPhone:"手机号",contractId:"合同编号",plantCapacity:"电站容量",userName:"用户名",projectId:"项目ID",plantName:"站点名称"},s,"excelData")},s.excelData=[],s.rows=[],C((()=>s.page.page),(()=>{const t=s.callback.search;t.callback(t)})),C((()=>s.page.pageSize),(()=>{s.page.page=1})),I((async()=>{s.page.page=1,await async function(){const t=await R(),e=q(t).data;!function t(e){var a;for(const s of e)(null==(a=null==s?void 0:s.children)?void 0:a.length)?t(s.children):delete s.children}(e),s.excelForm.options=e}()})),(a,o)=>{const l=A,i=P,m=K,u=r,d=n,j=t,h=e;return p(),f("div",H,[k(i,{columns:F(s).columns,rows:F(s).rows,rowKey:"plantUid"},{top:c((()=>[k($,{formList:F(s).formList,title:"项目信息"},null,8,["formList"])])),bottom:c((()=>[k(l,{page:F(s).page},null,8,["page"])])),_:1},8,["columns","rows"]),k(m,{dialog:F(s).dialog},{default:c((()=>[k($,{formList:F(s).dialog.formList,inline:!1,title:"新增",labelPosition:"left",itemClass:"tw-mt-2 tw--ml-[60px]"},null,8,["formList"])])),_:1},8,["dialog"]),k(h,{modelValue:F(s).excelForm.show,"onUpdate:modelValue":o[2]||(o[2]=t=>F(s).excelForm.show=t),"show-close":!1,width:"90%",top:"5vh",draggable:"","before-close":()=>{}},{header:c((()=>[L("section",Y,[tt,k(u,{value:F(s).excelForm.creator,"onUpdate:value":o[0]||(o[0]=t=>F(s).excelForm.creator=t),class:"tw-w-[300px] tw-ml-[200px] tw-inline-block"},{prefix:c((()=>[g(" 创建者 ")])),_:1},8,["value"])])])),footer:c((()=>[L("span",et,[k(j,{onClick:o[1]||(o[1]=t=>F(s).excelForm.show=!1)},{default:c((()=>[g("取消")])),_:1}),k(j,{type:"primary",onClick:F(s).excelForm.callback},{default:c((()=>[g(" 提交 ")])),_:1},8,["onClick"])])])),default:c((()=>[k(d,{columns:F(s).excelForm.columns,data:F(s).excelData,pagination:F(s).excelForm.pagination,"on-update:page":t=>F(s).excelForm.pagination.page=t},null,8,["columns","data","pagination","on-update:page"])])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-9073bbef"]]);export{at as default};
