import{F as t,U as s}from"./quasar-df1bac18.js";import{_ as e}from"./pagination-c4d8e88e.js";import{_ as o}from"./date-7dd9d7d0.js";import"./vue-5bfa3a54.js";import{c as i}from"./pageUtil-3bb2e07a.js";import{L as a}from"./ui-385bff4c.js";import{a as r,b as l}from"./statisticReportApi-8df5b5f8.js";import{g as m}from"./api-360ec627.js";import{i as p}from"./invertedTable-d05620ed.js";import{m as n}from"./notification-950a5f80.js";import{e as j}from"./exportFile-75030642.js";import{d as u}from"./dayjs-67f8ddef.js";import{l as c}from"./lodash-6d99edc3.js";import{_ as d}from"./index-a5df0f75.js";import{h as f,j as v,m as w,n as g,az as b,o as x,c as y,a as h,a9 as k,b as _,f as z,a8 as U,x as C,aa as E,H as S}from"./@vue-5e5cdef9.js";import{b as q}from"./naive-ui-0ee0b8c3.js";import"./@vicons-f32a0bdb.js";import"./@babel-f3c0a00c.js";import"./proxyUtil-6f30f7ef.js";import"./@x-ui-vue3-df3ba55b.js";import"./@vueuse-5227c686.js";import"./vue-router-6159329f.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./menuStore-30bf76d3.js";import"./icons-95011f8c.js";import"./element-plus-95e0b914.js";import"./lodash-es-ea7deab5.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./xe-utils-fe99d42a.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";/* empty css                *//* empty css                    */import"./vxe-table-plugin-element-be30dbb6.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./date-fns-tz-0cc073c8.js";const L={class:"q-pa-md tw-h-full tw-bg-gray-300"},M={class:"tw-flex tw-w-full tw-items-center tw-h-[30px]"},Y={class:"tw-flex-1 tw-justify-end tw-flex"},D=d({__name:"incomeStatistics",setup(d){let D=f([]);const H=f([]);f();const N=i($),R=f(),T=f(""),A=new a(!1);let B=f([]);const F=v({export:!1});async function $(){A.set(!0);try{const t=await r(N.page,N.pageSize,R.value.date,T.value.trim()),s=m(t),e=p(s.data.list||[],R.value.date);D.value=e.rows,H.value=e.columns,s.data.list||n.info("暂无数据"),N.total=s.data.total,g((()=>{!function(){const t=document.getElementsByTagName("tbody")[0].children,s=(...t)=>{for(const s of t)s.firstElementChild.style.borderLeft||s.firstElementChild.remove()},e=6;for(let o=0;o<t.length;o+=e){if(o%(2*e))for(let s=0;s<e;s++)t[o+s].style.background="#fff8e0";else for(let s=0;s<e;s++)t[o+s].style.background="#f1f7fb";t[o].firstElementChild.rowSpan=e;const i=c._.range(1,e);s(...i.map((s=>t[o+s])));for(let s of i)t[o+s].firstElementChild.style.borderLeft="1px solid #ccc"}}()}))}catch(t){n.error("报警列表请求失败！！！")}finally{A.set(!1)}}async function I(){F.export=!0;const t=await l(R.value.date,T.value.trim()),s=m(t);j(s,"能效收益统计"),F.export=!1}async function Q(t){const s=["","year","month","day"][t.split("-").length];B.value=[u(t).format("YYYY-MM-DD HH:mm:ss"),s],await $()}return w((async()=>{await $()})),(i,a)=>{const r=q,l=o,m=t,p=e,n=s,j=b("skeleton-item"),u=b("skeleton");return x(),y("div",L,[h("div",null,[k((x(),z(n,{"row-key":"plantUid",separator:"cell",class:"table tw-rounded-sm","table-header-class":"","title-class":"tw-bg-blue-300","table-class":"tw-bg-gray-100 tw-h-full",rows:_(D),columns:_(H),"rows-per-page-options":[0],loading:_(A).value.loading},{top:U((()=>[h("section",M,[C(r,{class:"tw-w-[220px] tw-mr-2",value:_(T),"onUpdate:value":a[0]||(a[0]=t=>S(T)?T.value=t:null)},{prefix:U((()=>[E(" 地址信息: ")])),_:1},8,["value"]),C(l,{onUpdateDate:Q,type:"single",ref_key:"dateRef",ref:R},null,512),h("div",Y,[k(C(m,{class:"tw-bg-blue-500 tw-text-white tw-w-[70px] tw-mr-2",label:"查询","no-caps":"",dense:"",onClick:$},null,512),[[j]]),k(C(m,{class:"tw-bg-blue-500 tw-text-white tw-w-[70px] tw-mr-2",label:"导出","no-caps":"",dense:"",onClick:I,loading:_(F).export},null,8,["loading"]),[[j]])])])])),pagination:U((t=>[C(p,{page:_(N)},null,8,["page"])])),_:1},8,["rows","columns","loading"])),[[u,_(A).value,void 0,{animated:!0}]])])])}}},[["__scopeId","data-v-f7b7dea8"]]);export{D as default};
