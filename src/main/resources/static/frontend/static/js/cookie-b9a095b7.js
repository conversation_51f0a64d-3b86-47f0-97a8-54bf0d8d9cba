/*!
 * cookie
 * Copyright(c) 2012-2014 <PERSON>
 * Copyright(c) 2015 <PERSON>
 * MIT Licensed
 */
var e=function(e,i){if("string"!=typeof e)throw new TypeError("argument str must be a string");for(var r={},n=i||{},o=e.split(";"),s=n.decode||t,p=0;p<o.length;p++){var f=o[p],m=f.indexOf("=");if(!(m<0)){var u=f.substring(0,m).trim();if(null==r[u]){var c=f.substring(m+1,f.length).trim();'"'===c[0]&&(c=c.slice(1,-1)),r[u]=a(c,s)}}}return r},i=function(e,i,t){var a=t||{},o=a.encode||r;if("function"!=typeof o)throw new TypeError("option encode is invalid");if(!n.test(e))throw new TypeError("argument name is invalid");var s=o(i);if(s&&!n.test(s))throw new TypeError("argument val is invalid");var p=e+"="+s;if(null!=a.maxAge){var f=a.maxAge-0;if(isNaN(f)||!isFinite(f))throw new TypeError("option maxAge is invalid");p+="; Max-Age="+Math.floor(f)}if(a.domain){if(!n.test(a.domain))throw new TypeError("option domain is invalid");p+="; Domain="+a.domain}if(a.path){if(!n.test(a.path))throw new TypeError("option path is invalid");p+="; Path="+a.path}if(a.expires){if("function"!=typeof a.expires.toUTCString)throw new TypeError("option expires is invalid");p+="; Expires="+a.expires.toUTCString()}a.httpOnly&&(p+="; HttpOnly");a.secure&&(p+="; Secure");if(a.sameSite){switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:p+="; SameSite=Strict";break;case"lax":p+="; SameSite=Lax";break;case"strict":p+="; SameSite=Strict";break;case"none":p+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}}return p},t=decodeURIComponent,r=encodeURIComponent,n=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/;function a(e,i){try{return i(e)}catch(t){return e}}export{e as p,i as s};
