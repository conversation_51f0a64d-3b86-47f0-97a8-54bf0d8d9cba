import{i as t}from"./index-8cc8d4b8.js";function e(e){return t({url:"/system/user/getUserList",method:"post",data:{...e,date:""}})}function r(){return t({url:"/system/project/getProjectSpecialInfo",method:"get",params:{}})}function s(e){return t({url:"/system/user/addUser",method:"post",data:{...e}})}function u(e){return t({url:"/system/user/editUser",method:"put",data:{...e}})}function a(e="",r){return t({url:"/system/user/editUser",method:"put",data:{userStatus:e,userUid:r}})}function o(e){return t({url:`/system/user/deleteUser/${e}`,method:"post"})}function n(e=1,r=10,s=""){return t({url:"/system/role/getRoleList",method:"post",data:{currentPage:e,pageSize:r,roleName:s}})}function d(e){return t({url:"/system/user/import",method:"post",data:{file:e},headers:{"Content-Type":"multipart/form-data"}})}export{u as a,a as b,o as c,r as d,n as e,e as g,d as i,s as u};
