import{a as e,y as a,A as l,r as t,v as u}from"./element-plus-95e0b914.js";import"./vue-5bfa3a54.js";import{N as r}from"./@element-plus-4c34063a.js";import{m as s}from"./@vueuse-5227c686.js";import{d as o}from"./dayjs-67f8ddef.js";import{l as d}from"./lodash-6d99edc3.js";import{_ as m}from"./index-a5df0f75.js";import{h as i,w as n,m as v,as as f,o as p,c as y,x as c,a8 as Y,aa as h,f as g,l as V,b,a as k}from"./@vue-5e5cdef9.js";const _={class:"Mtime"},w={key:0,class:"demo-date-picker"},C={class:"u-flex-center"},M={class:"block"},x={key:1},j=m({__name:"index",props:{isRange:{type:Boolean,default:!1},timeType:{type:String,default:"date"},timeValue:{type:[String,Array]},showAll:{type:Boolean,default:!1},isMountedChangeType:{type:Boolean,default:!0}},emits:["update:timeValue","typeChange","update:timeType","timeSwitch"],setup(m,{emit:j}){const T={date:"",daterange:"",month:"",monthrange:"",year:"",yearrange:""},D=m,S=j,R=s(D,"timeValue"),A=s(D,"timeType"),B=i("YYYY-MM-DD"),U=i("date"),z=i(),I=i(),N=i(),O=e=>!o(e).isBefore(o()),K=e=>o(e).isBefore(z.value[0]),L=e=>{switch(e){case"date":if(B.value="YYYY-MM-DD",!1===D.isRange){A.value="date",""===T[e]?S("update:timeValue",o().format(B.value)):S("update:timeValue",T[e]),S("typeChange","日");break}A.value="daterange",""===T.daterange?S("update:timeValue",[o().format(B.value),o().format(B.value)]):S("update:timeValue",T.daterange);break;case"month":if(B.value="YYYY-MM",!1===D.isRange){A.value="month",""===T[e]?S("update:timeValue",o().format(B.value)):S("update:timeValue",T[e]),S("typeChange","月");break}A.value="monthrange",""===T.monthrange?S("update:timeValue",[o().format(B.value),o().format(B.value)]):S("update:timeValue",T.monthrange);break;case"year":if(B.value="YYYY",!1===D.isRange){A.value="year",""===T[e]?S("update:timeValue",o().format(B.value)):S("update:timeValue",T[e]),S("typeChange","年");break}A.value="yearrange",""===T.yearrange?S("update:timeValue",[o().format(B.value),o().format(B.value)]):S("update:timeValue",T.yearrange);break;case"all":S("update:timeValue",""),S("typeChange","总"),A.value="all"}},q=(e,a)=>{if("startYear"===a)return S("update:timeValue",[e,R.value[1]]),void I.value.focus();"endYear"!==a?(S("update:timeValue",e),T[A.value]=z.value):S("update:timeValue",[R.value[0],e])};n((()=>D.timeValue),(e=>{d._.isArray(R.value)?z.value=d._.cloneDeep(e):z.value=e}),{immediate:!0}),n((()=>D.timeType),(e=>{if(D.timeType.includes("date"))B.value="YYYY-MM-DD",U.value="date";else if(D.timeType.includes("month"))B.value="YYYY-MM",U.value="month";else{if(D.timeType.includes("all"))return;B.value="YYYY",U.value="year"}}));const E=a=>{if("forward"===a&&"date"===A.value){if(o().isSame(o(z.value),"day"))return void e.warning("不能选择未来日期！");z.value=o(z.value).add(1,"day").format(B.value)}else if("back"===a&&"date"===A.value)z.value=o(z.value).subtract(1,"day").format(B.value);else if("forward"===a&&"month"===A.value){if(o().isSame(o(z.value),"month"))return void e.warning("不能选择未来日期！");z.value=o(z.value).add(1,"month").format(B.value)}else if("back"===a&&"month"===A.value)z.value=o(z.value).subtract(1,"month").format(B.value);else if("forward"===a&&"year"===A.value){if(o().isSame(o(z.value),"year"))return void e.warning("不能选择未来日期！");z.value=o(z.value).add(1,"year").format(B.value)}else"back"===a&&"year"===A.value&&(z.value=o(z.value).subtract(1,"year").format(B.value));S("update:timeValue",z.value),S("timeSwitch",a)};return v((()=>{D.isMountedChangeType&&L(A.value)})),(e,s)=>{const o=a,d=l,i=f("CaretLeft"),n=t,v=u;return p(),y("div",_,[c(d,{modelValue:U.value,"onUpdate:modelValue":s[0]||(s[0]=e=>U.value=e),class:"h-full mr-[5px]",onChange:L},{default:Y((()=>[c(o,{class:"h-full",label:"date"},{default:Y((()=>[h("日")])),_:1}),c(o,{class:"h-full",label:"month"},{default:Y((()=>[h("月")])),_:1}),c(o,{class:"h-full",label:"year"},{default:Y((()=>[h("年")])),_:1}),m.showAll?(p(),g(o,{key:0,class:"h-full",label:"all"},{default:Y((()=>[h("总")])),_:1})):V("",!0)])),_:1},8,["modelValue"]),U.value&&"yearrange"!==b(A)&&"all"!==U.value?(p(),y("div",w,[-1===b(A).indexOf("range")?(p(),g(n,{key:0,size:32,class:"mIcon",onClick:s[1]||(s[1]=e=>E("back"))},{default:Y((()=>[c(i,{class:"icons"})])),_:1})):V("",!0),k("div",C,[k("div",M,[c(v,{modelValue:z.value,"onUpdate:modelValue":s[2]||(s[2]=e=>z.value=e),clearable:!1,"disabled-date":O,format:B.value,type:b(A),"value-format":B.value,placeholder:"请输入",onChange:q},null,8,["modelValue","format","type","value-format"])])]),-1===b(A).indexOf("range")?(p(),g(n,{key:1,size:32,class:"mIcon",onClick:s[3]||(s[3]=e=>E("forward"))},{default:Y((()=>[c(b(r),{class:"icons"})])),_:1})):V("",!0)])):"yearrange"===b(A)?(p(),y("div",x,[c(v,{ref_key:"startYearRef",ref:N,modelValue:z.value[0],"onUpdate:modelValue":s[4]||(s[4]=e=>z.value[0]=e),clearable:!1,format:"YYYY",placeholder:"请输入",style:{width:"120px"},type:"year","value-format":"YYYY",onChange:s[5]||(s[5]=e=>{q(e,"startYear")})},null,8,["modelValue"]),h(" - "),c(v,{key:"pickerKey",ref_key:"endYearRef",ref:I,modelValue:z.value[1],"onUpdate:modelValue":s[6]||(s[6]=e=>z.value[1]=e),clearable:!1,"disabled-date":K,format:"YYYY",placeholder:"请输入",style:{width:"120px"},type:"year","value-format":"YYYY",onChange:s[7]||(s[7]=e=>{q(e,"endYear")})},null,8,["modelValue"])])):V("",!0)])}}},[["__scopeId","data-v-f3753400"]]);export{j as M};
