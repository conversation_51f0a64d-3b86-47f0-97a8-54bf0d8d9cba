import{v as t}from"./@vueuse-af86c621.js";import{r as e,f as a,v as i,k as l,d as s,B as o}from"./element-plus-d975be09.js";import"./vue-5bfa3a54.js";import{M as r}from"./@element-plus-4c34063a.js";import{a as d}from"./vxe-table-3a25f2d2.js";import{X as n}from"./xe-utils-fe99d42a.js";import{g as c}from"./index-18c14ee1.js";import{f as m,d as p,c as u}from"./chartResize-3e3d11d7.js";import{g as f,a as b}from"./index-d11362ff.js";import{u as g}from"./vue-router-6159329f.js";import{d as v}from"./dayjs-d60cc07f.js";import{i as h}from"./echarts-f30da64f.js";import{h as y,j as x,m as w,p as j,as as k,o as P,c as C,a as S,t as N,b as z,x as D,a8 as _,aa as L,F as V,k as I,y as U,f as T,a6 as E,a9 as A,ak as W,l as M,C as R,D as Y,B}from"./@vue-5e5cdef9.js";import{_ as q}from"./index-8cc8d4b8.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./lodash-es-ea7deab5.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./@babel-f3c0a00c.js";import"./dom-zindex-5f662ad1.js";import"./element-resize-detector-0d37a2ab.js";import"./batch-processor-06abf2b4.js";import"./lodash-6d99edc3.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./nprogress-e136a1b4.js";import"./quasar-b3f06d8a.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-7bbbb435.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";const O={class:"station-sel flex items-center justify-start u-gap-10"},G=(t=>(R("data-v-bd440ceb"),t=t(),Y(),t))((()=>S("span",{class:"u-flex-y-center"},[S("i",{class:"square"}),S("i",{class:"small-title"},"当前电站")],-1))),H={class:"body-text"},F={class:"small-title u-flex-1 text-right"},J={class:"inverter-list"},X={class:"scrollbar-flex-content"},$=["onClick"],K={class:"table-btn"},Z={key:1,class:"chart-box","element-loading-text":"正在生成图表"},Q={class:"table-btn"},tt=[B('<div class="u-flex-column chart-power-item" data-v-bd440ceb><p class="chart-title small-title u-flex-y-center" data-v-bd440ceb>日功率</p><figure id="lrChart" class="u-flex-1" data-v-bd440ceb></figure></div><div class="u-flex-column chart-today-item" data-v-bd440ceb><p class="chart-title small-title u-flex-y-center" data-v-bd440ceb>日发电量</p><figure id="todayChart" class="u-flex-1" data-v-bd440ceb></figure></div><div class="u-flex-column chart-PV-item" data-v-bd440ceb><p class="chart-title small-title u-flex-y-center justify-between" data-v-bd440ceb><span data-v-bd440ceb>直流输入</span></p><figure id="iacChart" class="u-flex-1" data-v-bd440ceb></figure></div><div class="u-flex-column chart-L-item" data-v-bd440ceb><p class="chart-title small-title u-flex-y-center justify-between" data-v-bd440ceb><span data-v-bd440ceb>交流输出</span></p><figure id="vacChart" class="u-flex-1" data-v-bd440ceb></figure></div>',4)],et={class:"station-list"},at={class:"infinite-list",style:{overflow:"auto"}},it={key:0,class:"infinite-list-empty supplementary-text text-center"},lt=["onClick"],st=q(Object.assign({name:"realtime"},{__name:"indexCopy",setup(R){const Y=g(),B=y(),q=y(),st=y();y(!0);const ot=y("选择电站"),rt=y(!1),dt=x({show:!1,loading:!0,opt:{title:{show:!1,text:"",textStyle:{fontSize:m(16)}},legend:{show:!0,bottom:"2%",selected:{}},tooltip:{show:!0,trigger:"axis",confine:!0,formatter:null},grid:{left:"10%",right:"10%",top:"20%",bottom:"25%"},xAxis:{data:[]},yAxis:{type:"value",axisLine:{show:!0},axisTick:{show:!0},splitLine:{show:!1}},series:[]},originData:{},PVSel:[],LSel:[],chartTem:{time:[],PV:{pv1:{name:"直流输入",data:[]}},L:{L1:{name:"交流输出",data:[]}},power:{name:"功率(W)",data:[]},todayElec:{name:"日发电量(kWh)",data:[]},totalElec:{name:"总发电量(kWh)",data:[]}},pvSelected:[],Lselected:[],pvOpt:{},LOpt:{}}),nt={lrChart:null,todayChart:null,iacChart:null,vacChart:null},ct=x({first:!0,plantUid:"",data:[],page:{currentPage:1,pageSize:50}}),mt=x({type:"station",visible:!1,stationData:[],submitLoading:!1,list:{plantUid:"",plantName:"",pageSize:15,currentPage:1}}),pt=x({condition:{deviceName:"",counterId:"",date:v().format("YYYY-MM-DD")},modelData:{},tablePage:{totalResult:0,currentPage:1,pageSize:15}}),ut=x({id:"realTime",border:!0,showFooter:!1,minHeight:500,height:"96%",maxHeight:860,loading:!1,autoResize:!0,editConfig:{trigger:"click",mode:"cell"},scrollX:{enabled:!1},scrollY:{enabled:!1},sortConfig:{sortMethod:({sortList:t})=>{let e={};t.forEach((t=>{e[t.field]=t.order}))}},data:[],customConfig:{storage:{visible:!0,fixed:!0}},toolbarConfig:{custom:!0,slots:{buttons:"toolbar_buttons",tools:"toolbar_tools"}},columns:[{title:" 时间",field:"initTime",width:120,fixed:"left",slots:{default:"time"}},{title:"设备状态",field:"status",width:120},{title:"电流",children:[{title:"电流 N",field:"currentN",width:120},{title:"电流 A",field:"iac1",width:120},{title:"电流 B",field:"iac2",width:120},{title:"电流 C",field:"iac3",width:120}]},{title:"电压",children:[{title:"电压 A",field:"vac1",width:120},{title:"电压 B",field:"vac2",width:120},{title:"电压 C",field:"vac3",width:120}]},,{title:"漏电流",field:"drainCurrent",width:120},{title:"漏电等级",field:"drainGrade",width:120},{title:"上电试合闸",field:"electrifyStatus",width:120},{title:"lr",children:[{title:"Ir1 整定值",field:"lr1Sv",width:120},{title:"Ir1 延迟时间",field:"lr1Time",width:120},{title:"Ir2 整定值",field:"lr2Sv",width:120},{title:"Ir2 延迟时间",field:"lr2Time",width:120},{title:"Ir3 整定值",field:"lr3Sv",width:120}]},{fixed:"right",title:"当日发电量(kWh)",field:"todayElectricity",width:150},{fixed:"right",title:"总发电量(kWh)",field:"totalElectricity",width:150}],spanMethod:({row:t,_rowIndex:e,column:a,visibleData:i})=>{const l=t[a.field];if(l&&["initTime","power","totalElectricity","todayElectricity"].includes(a.field)){const t=i[e-1];let s=i[e+1];if(t&&t[a.field]===l)return{rowspan:0,colspan:0};{let t=1;for(;s&&s[a.field]===l;)s=i[++t+e];if(t>1)return{rowspan:t,colspan:1}}}}}),ft=async(t,e)=>{if(pt.condition.deviceName=t,pt.condition.counterId=e,rt.value=!0,ut.loading)d.modal.message({content:"正在请求数据中，请勿频繁点击",status:"info"});else{ut.loading=!0;try{const t=await f({...pt.tablePage,...pt.condition});if("00000"==t.status){if(dt.show)dt.originData=(t=>{let e={time:[],PV:{vac1:{name:"功率(W)",type:"line",smooth:!0,data:[],lineStyle:{color:"#f8b62d"},itemStyle:{color:"#f8b62d",normal:{color:"#f8b62d"}},areaStyle:{color:"#f8b62d"}},vac2:{data:[]},vac3:{data:[]},iac1:{name:"功率(W)",type:"line",smooth:!0,data:[],lineStyle:{color:"#f8b62d"},itemStyle:{color:"#f8b62d",normal:{color:"#f8b62d"}},areaStyle:{color:"#f8b62d"}},iac2:{data:[]},iac3:{data:[]}},L:{},todayElec:{name:"日发电量(kWh)",type:"bar",data:[],lineStyle:{color:"#f8b62d"},itemStyle:{color:"#f8b62d",normal:{color:"#f8b62d"}},areaStyle:{color:"#f8b62d"}}};return t.forEach((t=>{e.time.push(t.initTime.substr(11,5)),e.todayElec.data.push(t.overtension),e.PV.vac1.data.push(t.vac1),e.PV.vac2.data.push(t.vac2),e.PV.vac3.data.push(t.vac3),e.PV.iac1.data.push(t.iac1),e.PV.iac2.data.push(t.iac2),e.PV.iac3.data.push(t.iac3)})),e})(t.data.records.reverse()),kt(dt.originData,nt);else{pt.tablePage.totalResult=t.data.total;let e=n.clone(t.data.records,!0);ut.data=e}ut.loading=!1}else ut.data=[],pt.tablePage.totalResult=0,ut.loading=!1,dt.show&&(dt.loading=!1,kt(dt.chartTem,nt))}catch(a){ut.data=[],pt.tablePage.totalResult=0,ut.loading=!1}finally{rt.value=!1}}},bt=async(t,e)=>{mt.submitLoading=!0;try{const a=await b({plantUid:t,...ct.page});mt.submitLoading=!1,ot.value=e,mt.visible=!1,"00000"==a.status&&(pt.tablePage={totalResult:0,currentPage:1,pageSize:20},ct.data=a.data.records,pt.condition.deviceName=a.data.records[0].deviceName,pt.condition.counterId=a.data.records[0].deviceId,ft(pt.condition.deviceName,pt.condition.counterId))}catch(a){mt.submitLoading=!1}},gt=()=>{mt.list.currentPage+=1,vt("next")},vt=async(t,e)=>{mt.list.plantUid="","reset"==t?mt.list.currentPage=1:"page"==t&&(mt.list.plantUid=e),mt.submitLoading=!0;try{const e=await c(mt.list);mt.submitLoading=!1,"00000"==e.status?(1==mt.list.currentPage?mt.stationData=e.data.records:mt.stationData=[...mt.stationData,...e.data.records],ct.first&&(ot.value=mt.stationData[0].plantName,bt(mt.stationData[0].plantUid,mt.stationData[0].plantName)),"page"==t&&(mt.list.plantName=mt.stationData[0].plantName)):mt.stationData=[]}catch(a){mt.submitLoading=!1}},ht=()=>{ft(pt.condition.deviceName,pt.condition.counterId)},yt=async()=>{ht()},xt=t=>t.getTime()>Date.now(),wt=()=>{},jt=(t,e)=>{let a=document.getElementById(t),i=h(a);return i.setOption(e),i},kt=(t,e)=>{let a;dt.loading=!0;for(let i in e){switch(a=JSON.parse(JSON.stringify(dt.opt)),a.xAxis.data=t.time,i){case"todayChart":a.title.text="日发电量(kWh)",a.yAxis.name="kWh",a.series.push(t.todayElec);break;case"lrChart":a.title.text="lr",a.yAxis.name="",a.series.push(t.power);break;case"iacChart":a.title.text="电流(A)",a.yAxis.name="A",a.series.push(t.PV.iac1)}e[i]&&(e[i].dispose(),p(st.value)),e[i]=jt(i,a)}setTimeout((()=>{dt.loading=!1}),1e3),u(st.value,e)},Pt=t=>{for(let e in t)t[e]&&t[e].dispose()},Ct=async t=>{"chart"==t?(dt.show=!0,pt.tablePage={currentPage:1,pageSize:100},await ft(pt.condition.deviceName,pt.condition.counterId)):(dt.PVSel=[],dt.LSel=[],dt.pvSelected=[],dt.Lselected=[],Pt(nt),pt.tablePage={currentPage:1,pageSize:15},p(st.value),dt.loading=!0,dt.show=!1,await ft(pt.condition.deviceName,pt.condition.counterId))},St=async t=>{await ft(pt.condition.deviceName,pt.condition.counterId)};return w((async()=>{Y.query.plantUid&&""!=Y.query.plantUid?await vt("page",Y.query.plantUid):await vt("reset"),ct.first=!1})),j((()=>{p(st.value),Pt(nt)})),(d,n)=>{const c=k("CaretBottom"),m=e,p=a,u=i,f=l,b=k("vxe-button"),g=k("vxe-pager"),v=k("vxe-grid"),h=s,y=k("vxe-modal"),x=o,w=t;return P(),C("div",{class:"app-container",ref_key:"appContainerRef",ref:B},[S("div",O,[S("p",{class:"plant-sel h-full u-flex-center u-gap-10 shadow-md body-text justify-between",onClick:n[0]||(n[0]=t=>{return e="station",mt.type=e,void(mt.visible=!0);var e})},[G,S("span",H,N(z(ot)),1),D(m,{class:"sel-btn",size:20},{default:_((()=>[D(c)])),_:1})]),S("span",F,[D(p,{type:"primary",onClick:yt,loading:z(rt)},{default:_((()=>[L("刷新")])),_:1},8,["loading"]),L("  "),D(u,{modelValue:z(pt).condition.date,"onUpdate:modelValue":n[1]||(n[1]=t=>z(pt).condition.date=t),disabled:z(ut).loading,type:"date","value-format":"YYYY-MM-DD",onChange:ht,"disabled-date":xt},null,8,["modelValue","disabled"])])]),S("div",J,[D(f,null,{default:_((()=>[S("div",X,[(P(!0),C(V,null,I(z(ct).data,((t,e)=>(P(),C("p",{class:U(["inverter-list-item h-full body-text u-flex-center",z(pt).condition.deviceName==t.deviceName?"is-active":""]),onClick:e=>ft(t.deviceName,t.deviceId)},N(t.deviceName),11,$)))),256))])])),_:1})]),z(dt).show?A((P(),C("div",Z,[S("div",Q,[D(b,{status:"primary",onClick:n[5]||(n[5]=t=>Ct("table"))},{default:_((()=>[L("表格")])),_:1})]),S("div",{ref_key:"gatherChart",ref:st,id:"chartGather"},tt,512)])),[[x,z(dt).loading]]):(P(),T(v,E({key:0,ref_key:"xGrid",ref:q,class:"my-grid66"},z(ut)),{toolbar_buttons:_((()=>[])),toolbar_tools:_((()=>[S("div",K,[D(b,{status:"warning",icon:"vxe-icon-chart-line",onClick:n[2]||(n[2]=t=>Ct("chart"))})])])),time:_((({row:t})=>[S("span",null,N(t.initTime.substr(11,5)),1)])),"row-operate":_((({row:t})=>[D(p,{link:"",type:"primary",onClick:e=>d.seeTableItem(t)},{default:_((()=>[L("查看详情")])),_:2},1032,["onClick"])])),bottom:_((()=>[])),pager:_((()=>[D(g,{perfect:"","current-page":z(pt).tablePage.currentPage,"onUpdate:currentPage":n[3]||(n[3]=t=>z(pt).tablePage.currentPage=t),"page-size":z(pt).tablePage.pageSize,"onUpdate:pageSize":n[4]||(n[4]=t=>z(pt).tablePage.pageSize=t),total:z(pt).tablePage.totalResult,onPageChange:St},null,8,["current-page","page-size","total"])])),_:1},16)),D(y,{modelValue:z(mt).visible,"onUpdate:modelValue":n[9]||(n[9]=t=>z(mt).visible=t),title:"电站选择",width:"500","min-width":"400","min-height":"100",loading:z(mt).submitLoading,resize:"","destroy-on-close":"",onHide:wt},{default:_((()=>[S("div",et,[D(h,{modelValue:z(mt).list.plantName,"onUpdate:modelValue":n[7]||(n[7]=t=>z(mt).list.plantName=t),clearable:"",onKeyup:n[8]||(n[8]=W((t=>vt("reset")),["enter"]))},{append:_((()=>[D(p,{icon:z(r),onClick:n[6]||(n[6]=t=>vt("reset"))},null,8,["icon"])])),_:1},8,["modelValue"]),A((P(),C("ul",at,[0==z(mt).stationData.length?(P(),C("li",it,"暂无数据")):M("",!0),(P(!0),C(V,null,I(z(mt).stationData,(t=>(P(),C("li",{class:"infinite-list-item body-text",key:t.plantUid,onClick:e=>bt(t.plantUid,t.plantName)},N(t.plantName),9,lt)))),128))])),[[w,gt]])])])),_:1},8,["modelValue","loading"])],512)}}}),[["__scopeId","data-v-bd440ceb"]]);export{st as default};
