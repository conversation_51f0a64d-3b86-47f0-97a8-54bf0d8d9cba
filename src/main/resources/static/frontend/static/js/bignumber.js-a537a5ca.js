var e=/^-?(?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?$/i,r=Math.ceil,n=Math.floor,t="[BigNumber Error] ",i=t+"Number primitive has more than 15 significant digits: ",o=1e14,s=14,u=9007199254740991,f=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8,1e9,1e10,1e11,1e12,1e13],l=1e7,c=1e9;function a(e){var r=0|e;return e>0||e===r?r:r-1}function h(e){for(var r,n,t=1,i=e.length,o=e[0]+"";t<i;){for(r=e[t++]+"",n=s-r.length;n--;r="0"+r);o+=r}for(i=o.length;48===o.charCodeAt(--i););return o.slice(0,i+1||1)}function g(e,r){var n,t,i=e.c,o=r.c,s=e.s,u=r.s,f=e.e,l=r.e;if(!s||!u)return null;if(n=i&&!i[0],t=o&&!o[0],n||t)return n?t?0:-u:s;if(s!=u)return s;if(n=s<0,t=f==l,!i||!o)return t?0:!i^n?1:-1;if(!t)return f>l^n?1:-1;for(u=(f=i.length)<(l=o.length)?f:l,s=0;s<u;s++)if(i[s]!=o[s])return i[s]>o[s]^n?1:-1;return f==l?0:f>l^n?1:-1}function p(e,r,i,o){if(e<r||e>i||e!==n(e))throw Error(t+(o||"Argument")+("number"==typeof e?e<r||e>i?" out of range: ":" not an integer: ":" not a primitive number: ")+String(e))}function w(e){var r=e.c.length-1;return a(e.e/s)==r&&e.c[r]%2!=0}function m(e,r){return(e.length>1?e.charAt(0)+"."+e.slice(1):e)+(r<0?"e":"e+")+r}function d(e,r,n){var t,i;if(r<0){for(i=n+".";++r;i+=n);e=i+e}else if(++r>(t=e.length)){for(i=n,r-=t;--r;i+=n);e+=i}else r<t&&(e=e.slice(0,r)+"."+e.slice(r));return e}var v=function v(N){var O,b,y,E,A,S,R,_,B,D,P=z.prototype={constructor:z,toString:null,valueOf:null},L=new z(1),x=20,U=4,I=-7,T=21,C=-1e7,M=1e7,G=!1,F=1,k=0,j={prefix:"",groupSize:3,secondaryGroupSize:0,groupSeparator:",",decimalSeparator:".",fractionGroupSize:0,fractionGroupSeparator:" ",suffix:""},q="0123456789abcdefghijklmnopqrstuvwxyz",$=!0;function z(r,t){var o,f,l,c,a,h,g,w,m=this;if(!(m instanceof z))return new z(r,t);if(null==t){if(r&&!0===r._isBigNumber)return m.s=r.s,void(!r.c||r.e>M?m.c=m.e=null:r.e<C?m.c=[m.e=0]:(m.e=r.e,m.c=r.c.slice()));if((h="number"==typeof r)&&0*r==0){if(m.s=1/r<0?(r=-r,-1):1,r===~~r){for(c=0,a=r;a>=10;a/=10,c++);return void(c>M?m.c=m.e=null:(m.e=c,m.c=[r]))}w=String(r)}else{if(!e.test(w=String(r)))return y(m,w,h);m.s=45==w.charCodeAt(0)?(w=w.slice(1),-1):1}(c=w.indexOf("."))>-1&&(w=w.replace(".","")),(a=w.search(/e/i))>0?(c<0&&(c=a),c+=+w.slice(a+1),w=w.substring(0,a)):c<0&&(c=w.length)}else{if(p(t,2,q.length,"Base"),10==t&&$)return X(m=new z(r),x+m.e+1,U);if(w=String(r),h="number"==typeof r){if(0*r!=0)return y(m,w,h,t);if(m.s=1/r<0?(w=w.slice(1),-1):1,z.DEBUG&&w.replace(/^0\.0*|\./,"").length>15)throw Error(i+r)}else m.s=45===w.charCodeAt(0)?(w=w.slice(1),-1):1;for(o=q.slice(0,t),c=a=0,g=w.length;a<g;a++)if(o.indexOf(f=w.charAt(a))<0){if("."==f){if(a>c){c=g;continue}}else if(!l&&(w==w.toUpperCase()&&(w=w.toLowerCase())||w==w.toLowerCase()&&(w=w.toUpperCase()))){l=!0,a=-1,c=0;continue}return y(m,String(r),h,t)}h=!1,(c=(w=b(w,t,10,m.s)).indexOf("."))>-1?w=w.replace(".",""):c=w.length}for(a=0;48===w.charCodeAt(a);a++);for(g=w.length;48===w.charCodeAt(--g););if(w=w.slice(a,++g)){if(g-=a,h&&z.DEBUG&&g>15&&(r>u||r!==n(r)))throw Error(i+m.s*r);if((c=c-a-1)>M)m.c=m.e=null;else if(c<C)m.c=[m.e=0];else{if(m.e=c,m.c=[],a=(c+1)%s,c<0&&(a+=s),a<g){for(a&&m.c.push(+w.slice(0,a)),g-=s;a<g;)m.c.push(+w.slice(a,a+=s));a=s-(w=w.slice(a)).length}else a-=g;for(;a--;w+="0");m.c.push(+w)}}else m.c=[m.e=0]}function H(e,r,n,t){var i,o,s,u,f;if(null==n?n=U:p(n,0,8),!e.c)return e.toString();if(i=e.c[0],s=e.e,null==r)f=h(e.c),f=1==t||2==t&&(s<=I||s>=T)?m(f,s):d(f,s,"0");else if(o=(e=X(new z(e),r,n)).e,u=(f=h(e.c)).length,1==t||2==t&&(r<=o||o<=I)){for(;u<r;f+="0",u++);f=m(f,o)}else if(r-=s,f=d(f,o,"0"),o+1>u){if(--r>0)for(f+=".";r--;f+="0");}else if((r+=o-u)>0)for(o+1==u&&(f+=".");r--;f+="0");return e.s<0&&i?"-"+f:f}function V(e,r){for(var n,t,i=1,o=new z(e[0]);i<e.length;i++)(!(t=new z(e[i])).s||(n=g(o,t))===r||0===n&&o.s===r)&&(o=t);return o}function W(e,r,n){for(var t=1,i=r.length;!r[--i];r.pop());for(i=r[0];i>=10;i/=10,t++);return(n=t+n*s-1)>M?e.c=e.e=null:n<C?e.c=[e.e=0]:(e.e=n,e.c=r),e}function X(e,t,i,u){var l,c,a,h,g,p,w,m=e.c,d=f;if(m){e:{for(l=1,h=m[0];h>=10;h/=10,l++);if((c=t-l)<0)c+=s,a=t,g=m[p=0],w=n(g/d[l-a-1]%10);else if((p=r((c+1)/s))>=m.length){if(!u)break e;for(;m.length<=p;m.push(0));g=w=0,l=1,a=(c%=s)-s+1}else{for(g=h=m[p],l=1;h>=10;h/=10,l++);w=(a=(c%=s)-s+l)<0?0:n(g/d[l-a-1]%10)}if(u=u||t<0||null!=m[p+1]||(a<0?g:g%d[l-a-1]),u=i<4?(w||u)&&(0==i||i==(e.s<0?3:2)):w>5||5==w&&(4==i||u||6==i&&(c>0?a>0?g/d[l-a]:0:m[p-1])%10&1||i==(e.s<0?8:7)),t<1||!m[0])return m.length=0,u?(t-=e.e+1,m[0]=d[(s-t%s)%s],e.e=-t||0):m[0]=e.e=0,e;if(0==c?(m.length=p,h=1,p--):(m.length=p+1,h=d[s-c],m[p]=a>0?n(g/d[l-a]%d[a])*h:0),u)for(;;){if(0==p){for(c=1,a=m[0];a>=10;a/=10,c++);for(a=m[0]+=h,h=1;a>=10;a/=10,h++);c!=h&&(e.e++,m[0]==o&&(m[0]=1));break}if(m[p]+=h,m[p]!=o)break;m[p--]=0,h=1}for(c=m.length;0===m[--c];m.pop());}e.e>M?e.c=e.e=null:e.e<C&&(e.c=[e.e=0])}return e}function Y(e){var r,n=e.e;return null===n?e.toString():(r=h(e.c),r=n<=I||n>=T?m(r,n):d(r,n,"0"),e.s<0?"-"+r:r)}return z.clone=v,z.ROUND_UP=0,z.ROUND_DOWN=1,z.ROUND_CEIL=2,z.ROUND_FLOOR=3,z.ROUND_HALF_UP=4,z.ROUND_HALF_DOWN=5,z.ROUND_HALF_EVEN=6,z.ROUND_HALF_CEIL=7,z.ROUND_HALF_FLOOR=8,z.EUCLID=9,z.config=z.set=function(e){var r,n;if(null!=e){if("object"!=typeof e)throw Error(t+"Object expected: "+e);if(e.hasOwnProperty(r="DECIMAL_PLACES")&&(p(n=e[r],0,c,r),x=n),e.hasOwnProperty(r="ROUNDING_MODE")&&(p(n=e[r],0,8,r),U=n),e.hasOwnProperty(r="EXPONENTIAL_AT")&&((n=e[r])&&n.pop?(p(n[0],-c,0,r),p(n[1],0,c,r),I=n[0],T=n[1]):(p(n,-c,c,r),I=-(T=n<0?-n:n))),e.hasOwnProperty(r="RANGE"))if((n=e[r])&&n.pop)p(n[0],-c,-1,r),p(n[1],1,c,r),C=n[0],M=n[1];else{if(p(n,-c,c,r),!n)throw Error(t+r+" cannot be zero: "+n);C=-(M=n<0?-n:n)}if(e.hasOwnProperty(r="CRYPTO")){if((n=e[r])!==!!n)throw Error(t+r+" not true or false: "+n);if(n){if("undefined"==typeof crypto||!crypto||!crypto.getRandomValues&&!crypto.randomBytes)throw G=!n,Error(t+"crypto unavailable");G=n}else G=n}if(e.hasOwnProperty(r="MODULO_MODE")&&(p(n=e[r],0,9,r),F=n),e.hasOwnProperty(r="POW_PRECISION")&&(p(n=e[r],0,c,r),k=n),e.hasOwnProperty(r="FORMAT")){if("object"!=typeof(n=e[r]))throw Error(t+r+" not an object: "+n);j=n}if(e.hasOwnProperty(r="ALPHABET")){if("string"!=typeof(n=e[r])||/^.?$|[+\-.\s]|(.).*\1/.test(n))throw Error(t+r+" invalid: "+n);$="0123456789"==n.slice(0,10),q=n}}return{DECIMAL_PLACES:x,ROUNDING_MODE:U,EXPONENTIAL_AT:[I,T],RANGE:[C,M],CRYPTO:G,MODULO_MODE:F,POW_PRECISION:k,FORMAT:j,ALPHABET:q}},z.isBigNumber=function(e){if(!e||!0!==e._isBigNumber)return!1;if(!z.DEBUG)return!0;var r,i,u=e.c,f=e.e,l=e.s;e:if("[object Array]"=={}.toString.call(u)){if((1===l||-1===l)&&f>=-c&&f<=c&&f===n(f)){if(0===u[0]){if(0===f&&1===u.length)return!0;break e}if((r=(f+1)%s)<1&&(r+=s),String(u[0]).length==r){for(r=0;r<u.length;r++)if((i=u[r])<0||i>=o||i!==n(i))break e;if(0!==i)return!0}}}else if(null===u&&null===f&&(null===l||1===l||-1===l))return!0;throw Error(t+"Invalid BigNumber: "+e)},z.maximum=z.max=function(){return V(arguments,-1)},z.minimum=z.min=function(){return V(arguments,1)},z.random=(E=9007199254740992,A=Math.random()*E&2097151?function(){return n(Math.random()*E)}:function(){return 8388608*(1073741824*Math.random()|0)+(8388608*Math.random()|0)},function(e){var i,o,u,l,a,h=0,g=[],w=new z(L);if(null==e?e=x:p(e,0,c),l=r(e/s),G)if(crypto.getRandomValues){for(i=crypto.getRandomValues(new Uint32Array(l*=2));h<l;)(a=131072*i[h]+(i[h+1]>>>11))>=9e15?(o=crypto.getRandomValues(new Uint32Array(2)),i[h]=o[0],i[h+1]=o[1]):(g.push(a%1e14),h+=2);h=l/2}else{if(!crypto.randomBytes)throw G=!1,Error(t+"crypto unavailable");for(i=crypto.randomBytes(l*=7);h<l;)(a=281474976710656*(31&i[h])+1099511627776*i[h+1]+4294967296*i[h+2]+16777216*i[h+3]+(i[h+4]<<16)+(i[h+5]<<8)+i[h+6])>=9e15?crypto.randomBytes(7).copy(i,h):(g.push(a%1e14),h+=7);h=l/7}if(!G)for(;h<l;)(a=A())<9e15&&(g[h++]=a%1e14);for(l=g[--h],e%=s,l&&e&&(a=f[s-e],g[h]=n(l/a)*a);0===g[h];g.pop(),h--);if(h<0)g=[u=0];else{for(u=-1;0===g[0];g.splice(0,1),u-=s);for(h=1,a=g[0];a>=10;a/=10,h++);h<s&&(u-=s-h)}return w.e=u,w.c=g,w}),z.sum=function(){for(var e=1,r=arguments,n=new z(r[0]);e<r.length;)n=n.plus(r[e++]);return n},b=function(){var e="0123456789";function r(e,r,n,t){for(var i,o,s=[0],u=0,f=e.length;u<f;){for(o=s.length;o--;s[o]*=r);for(s[0]+=t.indexOf(e.charAt(u++)),i=0;i<s.length;i++)s[i]>n-1&&(null==s[i+1]&&(s[i+1]=0),s[i+1]+=s[i]/n|0,s[i]%=n)}return s.reverse()}return function(n,t,i,o,s){var u,f,l,c,a,g,p,w,m=n.indexOf("."),v=x,N=U;for(m>=0&&(c=k,k=0,n=n.replace(".",""),g=(w=new z(t)).pow(n.length-m),k=c,w.c=r(d(h(g.c),g.e,"0"),10,i,e),w.e=w.c.length),l=c=(p=r(n,t,i,s?(u=q,e):(u=e,q))).length;0==p[--c];p.pop());if(!p[0])return u.charAt(0);if(m<0?--l:(g.c=p,g.e=l,g.s=o,p=(g=O(g,w,v,N,i)).c,a=g.r,l=g.e),m=p[f=l+v+1],c=i/2,a=a||f<0||null!=p[f+1],a=N<4?(null!=m||a)&&(0==N||N==(g.s<0?3:2)):m>c||m==c&&(4==N||a||6==N&&1&p[f-1]||N==(g.s<0?8:7)),f<1||!p[0])n=a?d(u.charAt(1),-v,u.charAt(0)):u.charAt(0);else{if(p.length=f,a)for(--i;++p[--f]>i;)p[f]=0,f||(++l,p=[1].concat(p));for(c=p.length;!p[--c];);for(m=0,n="";m<=c;n+=u.charAt(p[m++]));n=d(n,l,u.charAt(0))}return n}}(),O=function(){function e(e,r,n){var t,i,o,s,u=0,f=e.length,c=r%l,a=r/l|0;for(e=e.slice();f--;)u=((i=c*(o=e[f]%l)+(t=a*o+(s=e[f]/l|0)*c)%l*l+u)/n|0)+(t/l|0)+a*s,e[f]=i%n;return u&&(e=[u].concat(e)),e}function r(e,r,n,t){var i,o;if(n!=t)o=n>t?1:-1;else for(i=o=0;i<n;i++)if(e[i]!=r[i]){o=e[i]>r[i]?1:-1;break}return o}function t(e,r,n,t){for(var i=0;n--;)e[n]-=i,i=e[n]<r[n]?1:0,e[n]=i*t+e[n]-r[n];for(;!e[0]&&e.length>1;e.splice(0,1));}return function(i,u,f,l,c){var h,g,p,w,m,d,v,N,O,b,y,E,A,S,R,_,B,D=i.s==u.s?1:-1,P=i.c,L=u.c;if(!(P&&P[0]&&L&&L[0]))return new z(i.s&&u.s&&(P?!L||P[0]!=L[0]:L)?P&&0==P[0]||!L?0*D:D/0:NaN);for(O=(N=new z(D)).c=[],D=f+(g=i.e-u.e)+1,c||(c=o,g=a(i.e/s)-a(u.e/s),D=D/s|0),p=0;L[p]==(P[p]||0);p++);if(L[p]>(P[p]||0)&&g--,D<0)O.push(1),w=!0;else{for(S=P.length,_=L.length,p=0,D+=2,(m=n(c/(L[0]+1)))>1&&(L=e(L,m,c),P=e(P,m,c),_=L.length,S=P.length),A=_,y=(b=P.slice(0,_)).length;y<_;b[y++]=0);B=L.slice(),B=[0].concat(B),R=L[0],L[1]>=c/2&&R++;do{if(m=0,(h=r(L,b,_,y))<0){if(E=b[0],_!=y&&(E=E*c+(b[1]||0)),(m=n(E/R))>1)for(m>=c&&(m=c-1),v=(d=e(L,m,c)).length,y=b.length;1==r(d,b,v,y);)m--,t(d,_<v?B:L,v,c),v=d.length,h=1;else 0==m&&(h=m=1),v=(d=L.slice()).length;if(v<y&&(d=[0].concat(d)),t(b,d,y,c),y=b.length,-1==h)for(;r(L,b,_,y)<1;)m++,t(b,_<y?B:L,y,c),y=b.length}else 0===h&&(m++,b=[0]);O[p++]=m,b[0]?b[y++]=P[A]||0:(b=[P[A]],y=1)}while((A++<S||null!=b[0])&&D--);w=null!=b[0],O[0]||O.splice(0,1)}if(c==o){for(p=1,D=O[0];D>=10;D/=10,p++);X(N,f+(N.e=p+g*s-1)+1,l,w)}else N.e=g,N.r=+w;return N}}(),S=/^(-?)0([xbo])(?=\w[\w.]*$)/i,R=/^([^.]+)\.$/,_=/^\.([^.]+)$/,B=/^-?(Infinity|NaN)$/,D=/^\s*\+(?=[\w.])|^\s+|\s+$/g,y=function(e,r,n,i){var o,s=n?r:r.replace(D,"");if(B.test(s))e.s=isNaN(s)?null:s<0?-1:1;else{if(!n&&(s=s.replace(S,(function(e,r,n){return o="x"==(n=n.toLowerCase())?16:"b"==n?2:8,i&&i!=o?e:r})),i&&(o=i,s=s.replace(R,"$1").replace(_,"0.$1")),r!=s))return new z(s,o);if(z.DEBUG)throw Error(t+"Not a"+(i?" base "+i:"")+" number: "+r);e.s=null}e.c=e.e=null},P.absoluteValue=P.abs=function(){var e=new z(this);return e.s<0&&(e.s=1),e},P.comparedTo=function(e,r){return g(this,new z(e,r))},P.decimalPlaces=P.dp=function(e,r){var n,t,i,o=this;if(null!=e)return p(e,0,c),null==r?r=U:p(r,0,8),X(new z(o),e+o.e+1,r);if(!(n=o.c))return null;if(t=((i=n.length-1)-a(this.e/s))*s,i=n[i])for(;i%10==0;i/=10,t--);return t<0&&(t=0),t},P.dividedBy=P.div=function(e,r){return O(this,new z(e,r),x,U)},P.dividedToIntegerBy=P.idiv=function(e,r){return O(this,new z(e,r),0,1)},P.exponentiatedBy=P.pow=function(e,i){var o,u,f,l,c,a,h,g,p=this;if((e=new z(e)).c&&!e.isInteger())throw Error(t+"Exponent not an integer: "+Y(e));if(null!=i&&(i=new z(i)),c=e.e>14,!p.c||!p.c[0]||1==p.c[0]&&!p.e&&1==p.c.length||!e.c||!e.c[0])return g=new z(Math.pow(+Y(p),c?e.s*(2-w(e)):+Y(e))),i?g.mod(i):g;if(a=e.s<0,i){if(i.c?!i.c[0]:!i.s)return new z(NaN);(u=!a&&p.isInteger()&&i.isInteger())&&(p=p.mod(i))}else{if(e.e>9&&(p.e>0||p.e<-1||(0==p.e?p.c[0]>1||c&&p.c[1]>=24e7:p.c[0]<8e13||c&&p.c[0]<=9999975e7)))return l=p.s<0&&w(e)?-0:0,p.e>-1&&(l=1/l),new z(a?1/l:l);k&&(l=r(k/s+2))}for(c?(o=new z(.5),a&&(e.s=1),h=w(e)):h=(f=Math.abs(+Y(e)))%2,g=new z(L);;){if(h){if(!(g=g.times(p)).c)break;l?g.c.length>l&&(g.c.length=l):u&&(g=g.mod(i))}if(f){if(0===(f=n(f/2)))break;h=f%2}else if(X(e=e.times(o),e.e+1,1),e.e>14)h=w(e);else{if(0===(f=+Y(e)))break;h=f%2}p=p.times(p),l?p.c&&p.c.length>l&&(p.c.length=l):u&&(p=p.mod(i))}return u?g:(a&&(g=L.div(g)),i?g.mod(i):l?X(g,k,U,undefined):g)},P.integerValue=function(e){var r=new z(this);return null==e?e=U:p(e,0,8),X(r,r.e+1,e)},P.isEqualTo=P.eq=function(e,r){return 0===g(this,new z(e,r))},P.isFinite=function(){return!!this.c},P.isGreaterThan=P.gt=function(e,r){return g(this,new z(e,r))>0},P.isGreaterThanOrEqualTo=P.gte=function(e,r){return 1===(r=g(this,new z(e,r)))||0===r},P.isInteger=function(){return!!this.c&&a(this.e/s)>this.c.length-2},P.isLessThan=P.lt=function(e,r){return g(this,new z(e,r))<0},P.isLessThanOrEqualTo=P.lte=function(e,r){return-1===(r=g(this,new z(e,r)))||0===r},P.isNaN=function(){return!this.s},P.isNegative=function(){return this.s<0},P.isPositive=function(){return this.s>0},P.isZero=function(){return!!this.c&&0==this.c[0]},P.minus=function(e,r){var n,t,i,u,f=this,l=f.s;if(r=(e=new z(e,r)).s,!l||!r)return new z(NaN);if(l!=r)return e.s=-r,f.plus(e);var c=f.e/s,h=e.e/s,g=f.c,p=e.c;if(!c||!h){if(!g||!p)return g?(e.s=-r,e):new z(p?f:NaN);if(!g[0]||!p[0])return p[0]?(e.s=-r,e):new z(g[0]?f:3==U?-0:0)}if(c=a(c),h=a(h),g=g.slice(),l=c-h){for((u=l<0)?(l=-l,i=g):(h=c,i=p),i.reverse(),r=l;r--;i.push(0));i.reverse()}else for(t=(u=(l=g.length)<(r=p.length))?l:r,l=r=0;r<t;r++)if(g[r]!=p[r]){u=g[r]<p[r];break}if(u&&(i=g,g=p,p=i,e.s=-e.s),(r=(t=p.length)-(n=g.length))>0)for(;r--;g[n++]=0);for(r=o-1;t>l;){if(g[--t]<p[t]){for(n=t;n&&!g[--n];g[n]=r);--g[n],g[t]+=o}g[t]-=p[t]}for(;0==g[0];g.splice(0,1),--h);return g[0]?W(e,g,h):(e.s=3==U?-1:1,e.c=[e.e=0],e)},P.modulo=P.mod=function(e,r){var n,t,i=this;return e=new z(e,r),!i.c||!e.s||e.c&&!e.c[0]?new z(NaN):!e.c||i.c&&!i.c[0]?new z(i):(9==F?(t=e.s,e.s=1,n=O(i,e,0,3),e.s=t,n.s*=t):n=O(i,e,0,F),(e=i.minus(n.times(e))).c[0]||1!=F||(e.s=i.s),e)},P.multipliedBy=P.times=function(e,r){var n,t,i,u,f,c,h,g,p,w,m,d,v,N,O,b=this,y=b.c,E=(e=new z(e,r)).c;if(!(y&&E&&y[0]&&E[0]))return!b.s||!e.s||y&&!y[0]&&!E||E&&!E[0]&&!y?e.c=e.e=e.s=null:(e.s*=b.s,y&&E?(e.c=[0],e.e=0):e.c=e.e=null),e;for(t=a(b.e/s)+a(e.e/s),e.s*=b.s,(h=y.length)<(w=E.length)&&(v=y,y=E,E=v,i=h,h=w,w=i),i=h+w,v=[];i--;v.push(0));for(N=o,O=l,i=w;--i>=0;){for(n=0,m=E[i]%O,d=E[i]/O|0,u=i+(f=h);u>i;)n=((g=m*(g=y[--f]%O)+(c=d*g+(p=y[f]/O|0)*m)%O*O+v[u]+n)/N|0)+(c/O|0)+d*p,v[u--]=g%N;v[u]=n}return n?++t:v.splice(0,1),W(e,v,t)},P.negated=function(){var e=new z(this);return e.s=-e.s||null,e},P.plus=function(e,r){var n,t=this,i=t.s;if(r=(e=new z(e,r)).s,!i||!r)return new z(NaN);if(i!=r)return e.s=-r,t.minus(e);var u=t.e/s,f=e.e/s,l=t.c,c=e.c;if(!u||!f){if(!l||!c)return new z(i/0);if(!l[0]||!c[0])return c[0]?e:new z(l[0]?t:0*i)}if(u=a(u),f=a(f),l=l.slice(),i=u-f){for(i>0?(f=u,n=c):(i=-i,n=l),n.reverse();i--;n.push(0));n.reverse()}for((i=l.length)-(r=c.length)<0&&(n=c,c=l,l=n,r=i),i=0;r;)i=(l[--r]=l[r]+c[r]+i)/o|0,l[r]=o===l[r]?0:l[r]%o;return i&&(l=[i].concat(l),++f),W(e,l,f)},P.precision=P.sd=function(e,r){var n,t,i,o=this;if(null!=e&&e!==!!e)return p(e,1,c),null==r?r=U:p(r,0,8),X(new z(o),e,r);if(!(n=o.c))return null;if(t=(i=n.length-1)*s+1,i=n[i]){for(;i%10==0;i/=10,t--);for(i=n[0];i>=10;i/=10,t++);}return e&&o.e+1>t&&(t=o.e+1),t},P.shiftedBy=function(e){return p(e,-9007199254740991,u),this.times("1e"+e)},P.squareRoot=P.sqrt=function(){var e,r,n,t,i,o=this,s=o.c,u=o.s,f=o.e,l=x+4,c=new z("0.5");if(1!==u||!s||!s[0])return new z(!u||u<0&&(!s||s[0])?NaN:s?o:1/0);if(0==(u=Math.sqrt(+Y(o)))||u==1/0?(((r=h(s)).length+f)%2==0&&(r+="0"),u=Math.sqrt(+r),f=a((f+1)/2)-(f<0||f%2),n=new z(r=u==1/0?"5e"+f:(r=u.toExponential()).slice(0,r.indexOf("e")+1)+f)):n=new z(u+""),n.c[0])for((u=(f=n.e)+l)<3&&(u=0);;)if(i=n,n=c.times(i.plus(O(o,i,l,1))),h(i.c).slice(0,u)===(r=h(n.c)).slice(0,u)){if(n.e<f&&--u,"9999"!=(r=r.slice(u-3,u+1))&&(t||"4999"!=r)){+r&&(+r.slice(1)||"5"!=r.charAt(0))||(X(n,n.e+x+2,1),e=!n.times(n).eq(o));break}if(!t&&(X(i,i.e+x+2,0),i.times(i).eq(o))){n=i;break}l+=4,u+=4,t=1}return X(n,n.e+x+1,U,e)},P.toExponential=function(e,r){return null!=e&&(p(e,0,c),e++),H(this,e,r,1)},P.toFixed=function(e,r){return null!=e&&(p(e,0,c),e=e+this.e+1),H(this,e,r)},P.toFormat=function(e,r,n){var i,o=this;if(null==n)null!=e&&r&&"object"==typeof r?(n=r,r=null):e&&"object"==typeof e?(n=e,e=r=null):n=j;else if("object"!=typeof n)throw Error(t+"Argument not an object: "+n);if(i=o.toFixed(e,r),o.c){var s,u=i.split("."),f=+n.groupSize,l=+n.secondaryGroupSize,c=n.groupSeparator||"",a=u[0],h=u[1],g=o.s<0,p=g?a.slice(1):a,w=p.length;if(l&&(s=f,f=l,l=s,w-=s),f>0&&w>0){for(s=w%f||f,a=p.substr(0,s);s<w;s+=f)a+=c+p.substr(s,f);l>0&&(a+=c+p.slice(s)),g&&(a="-"+a)}i=h?a+(n.decimalSeparator||"")+((l=+n.fractionGroupSize)?h.replace(new RegExp("\\d{"+l+"}\\B","g"),"$&"+(n.fractionGroupSeparator||"")):h):a}return(n.prefix||"")+i+(n.suffix||"")},P.toFraction=function(e){var r,n,i,o,u,l,c,a,g,p,w,m,d=this,v=d.c;if(null!=e&&(!(c=new z(e)).isInteger()&&(c.c||1!==c.s)||c.lt(L)))throw Error(t+"Argument "+(c.isInteger()?"out of range: ":"not an integer: ")+Y(c));if(!v)return new z(d);for(r=new z(L),g=n=new z(L),i=a=new z(L),m=h(v),u=r.e=m.length-d.e-1,r.c[0]=f[(l=u%s)<0?s+l:l],e=!e||c.comparedTo(r)>0?u>0?r:g:c,l=M,M=1/0,c=new z(m),a.c[0]=0;p=O(c,r,0,1),1!=(o=n.plus(p.times(i))).comparedTo(e);)n=i,i=o,g=a.plus(p.times(o=g)),a=o,r=c.minus(p.times(o=r)),c=o;return o=O(e.minus(n),i,0,1),a=a.plus(o.times(g)),n=n.plus(o.times(i)),a.s=g.s=d.s,w=O(g,i,u*=2,U).minus(d).abs().comparedTo(O(a,n,u,U).minus(d).abs())<1?[g,i]:[a,n],M=l,w},P.toNumber=function(){return+Y(this)},P.toPrecision=function(e,r){return null!=e&&p(e,1,c),H(this,e,r,2)},P.toString=function(e){var r,n=this,t=n.s,i=n.e;return null===i?t?(r="Infinity",t<0&&(r="-"+r)):r="NaN":(null==e?r=i<=I||i>=T?m(h(n.c),i):d(h(n.c),i,"0"):10===e&&$?r=d(h((n=X(new z(n),x+i+1,U)).c),n.e,"0"):(p(e,2,q.length,"Base"),r=b(d(h(n.c),i,"0"),10,e,t,!0)),t<0&&n.c[0]&&(r="-"+r)),r},P.valueOf=P.toJSON=function(){return Y(this)},P._isBigNumber=!0,P[Symbol.toStringTag]="BigNumber",P[Symbol.for("nodejs.util.inspect.custom")]=P.valueOf,null!=N&&z.set(N),z}();export{v as B};
