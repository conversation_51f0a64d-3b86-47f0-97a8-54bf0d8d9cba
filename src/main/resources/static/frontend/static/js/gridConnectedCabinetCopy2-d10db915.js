import{_ as e,u as s,a as t}from"./index-8cc8d4b8.js";import{_ as i}from"./index-f2383b94.js";import{X as l}from"./element-plus-d975be09.js";import"./vue-5bfa3a54.js";import"./girdConnectedCabinet-7ccbd3a2.js";import{g as a}from"./index-791aa09c.js";import{g as n}from"./imgImport-3dead1a5.js";import"./fabric-8dd10b04.js";import{d as r}from"./dayjs-d60cc07f.js";import{x as o}from"./xe-utils-fe99d42a.js";import{d as c}from"./@vueuse-af86c621.js";import{h as m,j as p,e as u,m as g,v as f,as as d,o as j,c as v,a as b,t as h,b as x,f as k,l as y,x as w,F as z,k as _,y as N,a8 as C,C as D,D as I}from"./@vue-5e5cdef9.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./vue-router-6159329f.js";import"./bignumber.js-a537a5ca.js";import"./js-cookie-8253c38e.js";import"./axios-84f1a956.js";import"./lodash-es-ea7deab5.js";import"./spark-md5-022b35d0.js";import"./@babel-f3c0a00c.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";import"./quasar-b3f06d8a.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./@element-plus-4c34063a.js";import"./vxe-table-plugin-element-7bbbb435.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./lodash-6d99edc3.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./screenfull-c82f2093.js";import"./chartResize-3e3d11d7.js";import"./element-resize-detector-0d37a2ab.js";import"./batch-processor-06abf2b4.js";const R=e=>(D("data-v-aeab0203"),e=e(),I(),e),Y={class:"header"},M={class:"date font-small-text-size2 normal regular-title"},S={class:"title"},H={class:"main-title font-title-size text"},L={class:"screen-full"},W={class:"left-sensor"},X={class:"item-title-bg font-title-size"},q=["src"],A={class:"sensor-box"},B={key:0,class:"circle-img"},P=["src"],U=["src"],V={key:1,class:"circle-img"},F=["src"],G={class:"u-flex-column"},K={class:"font-small-text-size2"},O={key:0},T={key:1},Z={class:"smokeValue font-small-text-size1"},$={class:"smokeUnit font-small-text-size3"},E={class:"right-sensor"},J={class:"item-title-bg font-title-size"},Q=["src"],ee={class:"sensor-box"},se={key:0,class:"circle-img"},te=["src"],ie=["src"],le={key:1,class:"circle-img"},ae=["src"],ne={class:"u-flex-column"},re={class:"font-small-text-size2"},oe={key:0},ce={key:1},me={class:"smokeValue font-small-text-size1"},pe={class:"smokeUnit font-small-text-size3"},ue={class:"title"},ge={class:"item-title-bg font-title-size"},fe=["src"],de=R((()=>b("span",null,"配电房1",-1))),je={class:"right-wiringDiagram"},ve={class:"title"},be={class:"item-title-bg font-title-size"},he=["src"],xe=R((()=>b("span",null,"配电房2",-1))),ke=e({__name:"gridConnectedCabinetCopy2",setup(e){const D=s(),I=m(),R=p({data:r().format("YYYY-MM-DD HH:mm:ss")}),ke=m();m(),m(),m(),m(),m(),m(),m(),m(),m(!1),m();const ye=p([]),we={alarmStatus:{icon:"",label:"故障状态",value:"报警",unit:"",picName:"warn.png",end:!1},smokeConcentr:{icon:"mist",label:"烟雾浓度",value:0,unit:"PPM",picName:"",end:!1},temp:{icon:"temp",label:"当前温度",value:0,unit:"°C",picName:"",end:!1},humidity:{icon:"humidity",label:"当前湿度",value:0,unit:"%RH",picName:"",end:!0}},ze=p([]),_e=m(null),Ne=m(null);m(null);const Ce=p({width:1200,height:900}),De={width:Ce.width,height:Ce.height},Ie=new window.Image;Ie.src=n("bingwang","R6-30-50K.png");const Re=new window.Image;Re.src=n("bingwang","1.png");const Ye={image:Ie,x:0,y:0,scaleX:.3,scaleY:.3},Me={image:Re,x:200,y:300,scaleX:.4,scaleY:.4};u((()=>Ce.width/10));const Se=()=>{Ne.value.getNode().attrs.height=700},He=o.debounce((()=>{c(_e,(e=>{Ce.width=_e.value.offsetWidth,Ce.height=_e.value.offsetHeight,Se()}))}),300);return g((()=>{(async()=>{const{data:e}=await a();for(let s=0;s<e.length;s++){const t=o.clone(we,!0),i=Object.keys(t);ye.push(e[s].deviceName);for(let l=0;l<i.length;l++)"alarmStatus"===i[l]&&0==e[s].children[0].alarmStatus?t[i[l]].value="正常":"alarmStatus"===i[l]&&1==e[s].children[0].alarmStatus?t[i[l]].value="告警":t[i[l]].value=e[s].children[0][i[l]];ze.push(t)}})(),Se(),He()})),f((()=>{})),(e,s)=>{const a=l,r=i,o=t,c=d("v-image"),m=d("v-layer"),p=d("v-stage");return j(),v("div",{class:"screenfull-content tw-h-full tw-w-full bwg-bg screen-box",ref_key:"screenRef",ref:I},[b("div",Y,[b("div",M,h(x(R).data),1),b("div",S,[x(D).userInfo.screenLogo?(j(),k(a,{key:0,src:x(D).userInfo.screenLogo,fit:"contain",class:"image"},null,8,["src"])):y("",!0),b("h1",H,h(x(D).userInfo.projectTitle),1)]),b("div",L,[w(r,{class:"setting-item",type:"font",element:x(I)},null,8,["element"])])]),b("div",W,[b("div",null,[b("p",X,[b("img",{src:x(n)("screen","title_icon.png"),class:"title-icon"},null,8,q),b("span",null,h(x(ye)[0]),1)])]),b("div",A,[(j(!0),v(z,null,_(x(ze)[0],((e,s)=>(j(),v("div",{key:s,class:"u-flex-1 h-full u-flex-center-no asset-content-item"},[""!==e.picName?(j(),v("p",B,[b("img",{src:x(n)("screen","circle.png"),class:"title-icon0"},null,8,P),b("img",{src:x(n)("screen",e.picName),class:"title-icon1"},null,8,U)])):(j(),v("p",V,[b("img",{src:x(n)("screen","circle.png"),class:"title-icon0"},null,8,F),w(o,{name:e.icon,class:"title-icon1"},null,8,["name"])])),b("p",G,[b("span",K,h(e.label),1),"故障状态"===e.label?(j(),v("span",O,[b("i",{class:N(["font-small-text-size1",!0===e.value?"normal":"abnormal"])},h(e.value),3)])):(j(),v("span",T,[b("i",Z,h(e.value),1),b("i",$,h(e.unit),1)]))])])))),128))])]),b("div",E,[b("p",J,[b("img",{src:x(n)("screen","title_icon.png"),class:"title-icon"},null,8,Q),b("span",null,h(x(ye)[1]),1)]),b("div",ee,[(j(!0),v(z,null,_(x(ze)[1],((e,s)=>(j(),v("div",{key:s,class:"u-flex-1 h-full u-flex-center-no asset-content-item"},[""!==e.picName?(j(),v("p",se,[b("img",{src:x(n)("screen","circle.png"),class:"title-icon0"},null,8,te),b("img",{src:x(n)("screen",e.picName),class:"title-icon1"},null,8,ie)])):(j(),v("p",le,[b("img",{src:x(n)("screen","circle.png"),class:"title-icon0"},null,8,ae),w(o,{name:e.icon,class:"title-icon1"},null,8,["name"])])),b("p",ne,[b("span",re,h(e.label),1),"故障状态"===e.label?(j(),v("span",oe,[b("i",{class:N(["font-small-text-size1",!0===e.value?"normal":"abnormal"])},h(e.value),3)])):(j(),v("span",ce,[b("i",me,h(e.value),1),b("i",pe,h(e.unit),1)]))])])))),128))])]),b("div",{class:"left-wiringDiagram",ref_key:"leftWiringDiagramRef",ref:_e},[b("div",ue,[b("p",ge,[b("img",{src:x(n)("screen","title_icon.png"),class:"title-icon"},null,8,fe),de]),w(p,{config:De},{default:C((()=>[w(m,{ref_key:"leftCanvasRef",ref:Ne},{default:C((()=>[(j(),v(z,null,_([0,1,2,3,4,5],((e,s)=>w(c,{ref_for:!0,ref_key:"iref",ref:ke,config:{...Ye,x:Ye.x+150*e}},null,8,["config"]))),64)),(j(),v(z,null,_([0,1],((e,s)=>w(c,{config:{...Me,x:Me.x+400*e}},null,8,["config"]))),64))])),_:1},512)])),_:1})])],512),b("div",je,[b("div",ve,[b("p",be,[b("img",{src:x(n)("screen","title_icon.png"),class:"title-icon"},null,8,he),xe])])])],512)}}},[["__scopeId","data-v-aeab0203"]]);export{ke as default};
