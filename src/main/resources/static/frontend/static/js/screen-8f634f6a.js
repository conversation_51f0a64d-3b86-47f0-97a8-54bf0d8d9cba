import{c as t,_ as e,a as s}from"./mapUtils-1e5311e9.js";import{W as l,X as a}from"./element-plus-d975be09.js";import"./vue-5bfa3a54.js";import{_ as i,u as n}from"./index-8cc8d4b8.js";import{x as r}from"./homeStore-b8fb7ab6.js";import{S as o,a as c,A as m}from"./swiper-7f939876.js";import{B as p}from"./bignumber.js-a537a5ca.js";import{s as u}from"./startEndSlide-8c1800d1.js";import{C as f,G as d,H as x,F as j,J as w}from"./@vueuse-af86c621.js";import{h as v}from"./homeApi-54bb989e.js";import{S as y}from"./@vicons-f32a0bdb.js";import{j as h,h as g,m as b,v as z,az as I,o as k,c as S,a as C,x as D,a8 as E,t as N,b as _,f as T,l as L,a9 as M,aa as R,F as A,k as F,C as W,D as U}from"./@vue-5e5cdef9.js";import{_ as q}from"./naive-ui-0ee0b8c3.js";import"./three-59a86278.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";import"./chartResize-3e3d11d7.js";import"./element-resize-detector-0d37a2ab.js";import"./@babel-f3c0a00c.js";import"./batch-processor-06abf2b4.js";import"./lodash-6d99edc3.js";import"./echartsInit-0067e609.js";import"./menuStore-26f8ddd8.js";import"./vue-router-6159329f.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./dayjs-d60cc07f.js";import"./icons-95011f8c.js";import"./chartXY-a0399c4a.js";import"./api-b858041e.js";import"./notification-950a5f80.js";import"./axios-84f1a956.js";import"./quasar-b3f06d8a.js";import"./countUtil-c51cdcf8.js";import"./alarmAnalysisApi-e3a5f201.js";import"./lodash-es-ea7deab5.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./async-validator-cf877c1f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./xe-utils-fe99d42a.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-7bbbb435.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./taskUitls-36951a34.js";import"./index-15186f59.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./date-fns-tz-0cc073c8.js";const H=t=>(W("data-v-10a329ed"),t=t(),U(),t),P={class:"progress-left u-flex-center-no u-gap-8"},Y=H((()=>C("article",{class:"small-title font-small-text-size"}," 电站在线率 ",-1))),B={class:"tw-flex-1 light-blue"},O={class:"tw-text-xl tw-text-[#fff] tw-flex tw-items-center"},X={class:"supplementary-text font-small-text-size"},Z={class:"title u-flex-x-center item-start u-gap-8"},G={class:"main-title font-title-size"},J={class:"date-time"},Q={class:"progress-right u-flex-center-no u-gap-8"},V=H((()=>C("article",{class:"small-title font-small-text-size"}," 逆变器在线率 ",-1))),$={class:"tw-flex-1 light-blue"},K={class:"tw-text-xl tw-text-[#fff] tw-flex tw-items-center"},tt={class:"supplementary-text font-small-text-size"},et={class:"ElecDaily u-flex-column"},st=H((()=>C("div",{class:""},[C("article",{class:"regular-title sub-title title-border font-sub-title-size"},"发电汇总")],-1))),lt={class:"u-flex-1 u-flex-column plant-card-content"},at={class:"u-flex-1 u-flex-y-center justify-between"},it=H((()=>C("article",{class:"small-title font-text-size"}," 总发电量 ",-1))),nt={class:"u-flex-1 u-flex-y-center justify-end"},rt={class:"body-text font-text-size"},ot={class:"supplementary-text font-small-text-size2"},ct={class:"u-flex-1 u-flex-y-center justify-between"},mt=H((()=>C("article",{class:"small-title font-text-size"}," 月发电量 ",-1))),pt={class:"u-flex-1 u-flex-y-center justify-end"},ut={class:"body-text font-text-size"},ft={class:"supplementary-text font-small-text-size2"},dt={class:"u-flex-1 u-flex-y-center justify-between"},xt=H((()=>C("article",{class:"small-title font-text-size"}," 日发电量 ",-1))),jt={class:"u-flex-1 u-flex-y-center justify-end"},wt={class:"body-text font-text-size"},vt={class:"supplementary-text font-small-text-size2"},yt={class:"Assets u-flex-center-no"},ht={class:"u-flex-1 u-flex-column u-gap-10"},gt={class:"u-flex-1 u-flex-column"},bt=H((()=>C("span",{class:"small-title text-center font-sub-title-size"}," 总装机容量 ",-1))),zt={class:"u-flex-center-no"},It={class:"body-text font-text-size"},kt={class:"supplementary-text font-small-text-size2"},St={class:"u-flex-1 u-flex-column u-gap-10"},Ct={class:"u-flex-1 u-flex-column"},Dt=H((()=>C("span",{class:"small-title text-center font-sub-title-size"},"总电站数量",-1))),Et={class:"body-text text-center font-text-size"},Nt={class:"AlarmStatics u-flex-column"},_t=H((()=>C("div",{class:""},[C("article",{class:"regular-title sub-title title-border font-sub-title-size"},"实时告警")],-1))),Tt={class:"u-flex-1 u-flex-center-no"},Lt={class:"u-flex-1 h-full u-flex-column item-center justify-center"},Mt={class:"body-text text-center font-text-size"},Rt={class:"small-title text-center font-text-size"},At={class:"u-flex-1 h-full u-flex-column item-center justify-center"},Ft={class:"body-text text-center font-text-size"},Wt={class:"small-title text-center font-text-size"},Ut={class:"Map"},qt={class:"map-info u-flex-column items-center"},Ht={class:"area u-flex-y-center u-gap-6"},Pt=["onClick"],Yt={key:0,class:"weather u-flex-center u-gap-6"},Bt={class:"u-flex-center"},Ot=H((()=>C("i",{class:"font-small-text-size"},"温度：",-1))),Xt={class:"font-small-text-size weather-font-color"},Zt={class:"u-flex-center"},Gt=H((()=>C("i",{class:"font-small-text-size"},"风向：",-1))),Jt={class:"font-small-text-size weather-font-color"},Qt={class:"u-flex-center"},Vt=H((()=>C("i",{class:"font-small-text-size"},"风速：",-1))),$t={class:"font-small-text-size weather-font-color"},Kt={class:"u-flex-center weather-font-color"},te=H((()=>C("i",{class:"font-small-text-size"},"湿度：",-1))),ee={class:"font-small-text-size"},se={class:"u-flex-center u-gap-6 font-small-text-size weather-font-color"},le={class:"weather-font-color"},ae={class:"ElecChart"},ie={class:"SavingEnergy"},ne={class:"se-item-1"},re=H((()=>C("article",{class:"small-title text-center font-text-size"},"节约标准煤 (万吨)",-1))),oe={class:"body-text text-left font-small-text-size"},ce={class:"se-item-2"},me=H((()=>C("article",{class:"small-title text-center font-text-size"},"日CO2减排 (吨)",-1))),pe={class:"body-text text-right font-small-text-size"},ue={class:"se-item-3"},fe={class:"body-text text-left font-small-text-size"},de=H((()=>C("article",{class:"small-title text-center font-text-size"},"CO2减排 (万吨)",-1))),xe={class:"se-item-4"},je={class:"body-text text-right font-small-text-size"},we=H((()=>C("article",{class:"small-title text-center font-text-size"},"等效植树 (棵)",-1))),ve={class:"RotationalSeeding"},ye=H((()=>C("div",{class:"rs-title"},[C("article",{class:"regular-title sub-title title-border font-sub-title-size"},"电站效率")],-1))),he={class:"u-flex-1 rs-content"},ge={class:"tw-flex tw-items-center tw-justify-around text-ellipsis font-small-text-size"},be={class:"tw-w-[40%]"},ze={class:"inline-flex tw-w-[30%]"},Ie={class:"tw-text-[#F49A26] tw-w-[25%]"},ke={key:0,class:"rs-loading u-flex-center-no"},Se=i(Object.assign({name:"B2HomeScreen"},{__name:"screen",props:{iframe:{type:Boolean,default:!1}},setup(i){const W=i,U=h({start:null,end:!1,index:{startIndex:0,endIndex:50},total:0,displayData:[],originData:[]}),H={scrollTimer:null,retryTimer:null,dateTimer:null,elecChartTimer:null,resultTimer:null},Se=[m],Ce=new u(10,20,10,100),[De]=f(),Ee=r(),Ne=n();Ne.getUser();const _e=g([]),Te=h({curDate:d(x(),"HH:MM YYYY/MM/DD"),map:{alarmList:[],nodeStatus:"",weatherData:null,parentIdList:[]}}),Le=h({map:j("mapDom")}),Me=g(null),{isFullscreen:Re,enter:Ae,exit:Fe,toggle:We}=w(Me);async function Ue(){Ce.total<=10||(Ce.total<=20?_e.value.push(..._e.value):_e.value.length>=10&&_e.value.splice(De.value?Ce.step:0,Ce.step,...await async function(){const t=await v(...Ce.next());return"00000"==t.status&&Ce.resetTotal(t.data.total),t.data.records}()))}return b((async()=>{W.iframe&&document.querySelector(".screen-box").classList.remove("screenfull-content"),await Ee.getRankInfo(),Te.map=new t(Le.map),await Te.map.setChart();const e=await v(0,20);"00000"==(null==e?void 0:e.status)&&(Ce.resetTotal(e.data.total),_e.value=e.data.records),top!=self&&(document.querySelector("ul").style.display="none",document.querySelector("header").classList.add("tw-hidden"),document.querySelector("html").classList.add("tw-min-h-0"))})),z((()=>{var t,e,s,l;for(let a in H)H[a]&&(l=H)[s=a]&&(clearInterval(l[s]),l[s]=null);Te.map.timer&&(clearInterval(Te.map.timer),Te.map.timer=null,Le.map=null,Te.map=null,null==(e=null==(t=Te.map)?void 0:t.chart)||e.clear())})),(t,i)=>{var n,r,m,u,f,d,x;const j=l,w=a,v=e,h=s,g=q,b=I("go");return k(),S("div",{class:"screen-box bg screenfull-content",ref_key:"mainRef",ref:Me},[C("div",{class:"Header",onDblclick:i[0]||(i[0]=t=>_(We)())},[C("div",P,[Y,C("div",B,[D(j,{"stroke-width":10,percentage:new(_(p))(null==(n=_(Ee).plantNumInfo)?void 0:n.onlineRate).times(100).decimalPlaces(2).toNumber()||0},{default:E((()=>{var t;return[C("article",O,[C("span",X,N(new(_(p))(null==(t=_(Ee).plantNumInfo)?void 0:t.onlineRate).times(100).decimalPlaces(2).toString())+"% ",1)])]})),_:1},8,["percentage"])])]),C("div",Z,[_(Ne).userInfo.screenLogo?(k(),T(w,{key:0,src:_(Ne).userInfo.screenLogo,fit:"contain",class:"image"},null,8,["src"])):L("",!0),C("h1",G,N(_(Ne).userInfo.projectTitle),1),C("div",J,N(_(Te).curDate),1)]),C("div",Q,[V,C("div",$,[D(j,{"stroke-width":12,percentage:new(_(p))(null==(r=_(Ee).inverterNumInfo)?void 0:r.onlineRate).times(100).decimalPlaces(2).toNumber()||0},{default:E((()=>{var t;return[C("article",K,[C("span",tt,N(new(_(p))(null==(t=_(Ee).inverterNumInfo)?void 0:t.onlineRate).times(100).decimalPlaces(2).toString())+"% ",1)])]})),_:1},8,["percentage"])])])],32),C("div",et,[st,C("div",lt,[C("div",at,[D(_(y),{class:"tw-w-[30px] tw-mr-1 tw-text-[rgba(115, 212, 112, 1)]"}),it,C("article",nt,[C("span",rt,N((String(_(Ee).plantInfo.totalElectricity)||"000000.00").split("").join(" ")),1),C("span",ot,N(_(Ee).plantInfo.totalElectricityThousandUnit?"kWh":"MWh"),1)])]),C("div",ct,[D(_(y),{class:"tw-w-[30px] tw-mr-1 tw-text-[rgba(115, 212, 112, 1)]"}),mt,C("article",pt,[C("span",ut,N((String(_(Ee).plantInfo.monthElectricity)||"000000.00").split("").join(" ")),1),C("span",ft,N(_(Ee).plantInfo.monthElectricityThousandUnit?"kWh":"MWh"),1)])]),C("div",dt,[D(_(y),{class:"tw-w-[30px] tw-mr-1 tw-text-[rgba(115, 212, 112, 1)]"}),xt,C("article",jt,[C("span",wt,N((String(_(Ee).plantInfo.todayElectricity)||"000000.00").split("").join(" ")),1),C("span",vt,N(_(Ee).plantInfo.todayElectricityThousandUnit?"kWh":"MWh"),1)])])])]),C("div",yt,[C("div",ht,[D(w,{class:"as-image",src:"https://www.btosolarman.com/assets/btosolar/picture/screen/computer.png",fit:"contain"}),C("p",gt,[bt,C("span",zt,[C("i",It,N((_(Ee).plantInfo.plantCapacity+""||"000000.00").split("").join("")),1),C("i",kt,N(_(Ee).plantInfo.plantCapacityThousandUnit?"kWp":"MWp"),1)])])]),C("div",St,[D(w,{class:"as-image",src:"https://www.btosolarman.com/assets/btosolar/picture/screen/net.png",fit:"contain"}),C("p",Ct,[Dt,C("span",Et,N(_(Ee).plantNumInfo.totalNum),1)])])]),C("div",Nt,[_t,C("div",Tt,[C("div",Lt,[C("article",Mt,N(_(Ee).alarmNumInfo.alarmPlantNum),1),M((k(),S("article",Rt,[R(" 告警电站 ")])),[[b,"/plantManage/plantList?status=3"]])]),C("div",At,[C("article",Ft,N(_(Ee).alarmNumInfo.alarmInfoNum),1),M((k(),S("article",Wt,[R(" 告警 ")])),[[b,"/alarmAnalysis/alarmList?status=0"]])])])]),C("div",Ut,[C("div",qt,[C("p",Ht,[C("span",{class:"font-small-text-size",onClick:i[1]||(i[1]=t=>_(Te).map.titleEnter())},"全国"),(k(!0),S(A,null,F(_(Te).map.parentIdList,(t=>(k(),S("span",{class:"font-small-text-size",key:t.name,onClick:e=>_(Te).map.titleEnter(t)},N(t.name),9,Pt)))),128))]),(null==(m=_(Te).map.weatherData)?void 0:m.city)&&_(Te).map.parentIdList?(k(),S("p",Yt,[C("span",Bt,[Ot,C("i",Xt,N(null==(u=_(Te).map.weatherData)?void 0:u.temperature)+"℃ ",1)]),C("span",Zt,[Gt,C("i",Jt,N(null==(f=_(Te).map.weatherData)?void 0:f.wind),1)]),C("span",Qt,[Vt,C("i",$t,N(null==(d=_(Te).map.weatherData)?void 0:d.windspeed),1)]),C("span",Kt,[te,C("i",ee,N(null==(x=_(Te).map.weatherData)?void 0:x.humidity),1)])])):L("",!0),C("p",se,[C("span",null,N(_(Te).map.plantName),1),C("span",le,N(_(Te).map.nodeStatus),1)])]),C("figure",{id:"mapChart",class:"u-wh-full",ref:"mapDom",onClick:i[2]||(i[2]=(...e)=>t.exitHandle&&t.exitHandle(...e))},null,512)]),C("div",ae,[D(v)]),C("div",ie,[C("div",ne,[re,C("article",oe,N(_(Ee).plantInfo.totalCocal),1)]),C("div",ce,[me,C("article",pe,N(_(Ee).plantInfo.todayCo2),1)]),C("div",ue,[C("article",fe,N(_(Ee).plantInfo.totalCo2),1),de]),C("div",xe,[C("article",je,N(_(Ee).plantInfo.treeNum),1),we]),D(h)]),C("div",ve,[ye,C("div",he,[_(_e).length?(k(),T(_(c),{key:0,id:"swiperlist",ref:"swiperRef",autoplay:{delay:2e3,disableOnInteraction:!0},loop:!0,modules:Se,"slides-per-view":10,"space-between":0,speed:300,class:"tw-h-full swiper-no-swiping swipe tw-rounded-br-xl tw-rounded-bl-xl",direction:"vertical",onReachEnd:Ue},{default:E((()=>[(k(!0),S(A,null,F(_(_e),((t,e)=>(k(),T(_(o),{key:e,class:"tw-w-full"},{default:E((()=>[C("tr",ge,[C("td",be,[D(g,{class:"tw-w-[100%] tw-text-white"},{default:E((()=>[R(N(t.plantName),1)])),_:2},1024)]),C("td",ze,[D(j,{percentage:parseFloat((100*parseFloat(t.plantEfficiency)).toFixed(2)),"show-text":!1,"stroke-width":10,class:"tw-inline",color:"#F49A26",status:"success"},null,8,["percentage"])]),C("td",Ie,N((100*parseFloat(t.plantEfficiency)).toFixed(2))+" % ",1)])])),_:2},1024)))),128))])),_:1},512)):L("",!0)]),_(U).end?(k(),S("p",ke,"加载中...")):L("",!0)])],512)}}}),[["__scopeId","data-v-10a329ed"]]);export{Se as default};
