import"./echarts-f30da64f.js";import{V as e}from"./zrender-c058db04.js";import{e as o}from"./exceljs-b3a0e81d.js";import{F as a}from"./file-saver-8735aaf5.js";let l,t,r,s,i;{const o=["#d50000","#0091ea","#0090FF","#36CE9E","#FFC005","#FF515A","#8B5CFF","#00CA69"];let a=[{name:"1",value1:100,value2:233},{name:"2",value1:138,value2:233},{name:"3",value1:350,value2:200},{name:"4",value1:173,value2:180},{name:"5",value1:180,value2:199},{name:"6",value1:150,value2:233},{name:"7",value1:180,value2:210},{name:"8",value1:230,value2:180}],t=a.map((e=>e.name)),r=a.map((e=>e.value1)),s=a.map((e=>e.value2));const i=(e,o)=>{let a="";return/^#[\da-f]{6}$/i.test(e)&&(a=`rgba(${parseInt("0x"+e.slice(1,3))},${parseInt("0x"+e.slice(3,5))},${parseInt("0x"+e.slice(5,7))},${o})`),a};l={backgroundColor:"transparent",color:o,legend:{right:10},tooltip:{trigger:"axis"},grid:{top:"30px",bottom:"30px",left:"20px",right:"40px",containLabel:!0},xAxis:[{type:"category",boundaryGap:!1,axisLabel:{formatter:"{value}日",color:"#333"},axisLine:{lineStyle:{color:"#D9D9D9"}},data:t}],yAxis:[{type:"value",axisLabel:{color:"#666"},nameTextStyle:{color:"#666",fontSize:12,lineHeight:40},splitLine:{lineStyle:{type:"dashed",color:"#E9E9E9"}},axisLine:{show:!1},axisTick:{show:!1}}],series:[{name:"异常电站数",type:"line",smooth:!0,symbolSize:8,zlevel:3,lineStyle:{normal:{color:o[0],shadowBlur:3,shadowColor:i(o[0],.5),shadowOffsetY:8}},areaStyle:{normal:{color:new e(0,0,0,1,[{offset:0,color:i(o[0],.3)},{offset:1,color:i(o[0],.1)}],!1),shadowColor:i(o[0],.1),shadowBlur:10}},data:r},{name:"正常电站数",type:"line",smooth:!0,symbolSize:8,zlevel:3,lineStyle:{normal:{color:"#0091ea",shadowBlur:3,shadowColor:i("#0091ea",.5),shadowOffsetY:8}},areaStyle:{normal:{color:new e(0,0,0,1,[{offset:0,color:i(o[1],.3)},{offset:1,color:i(o[1],.1)}],!1),shadowColor:i(o[1],.1),shadowBlur:10}},data:s}]}}{const o=(e,o)=>{let a="";return/^#[\da-f]{6}$/i.test(e)&&(a=`rgba(${parseInt("0x"+e.slice(1,3))},${parseInt("0x"+e.slice(3,5))},${parseInt("0x"+e.slice(5,7))},${o})`),a};r={backgroundColor:"transparent",color:["#29def5"],grid:{top:"40px",bottom:"30px",left:"10%",right:"10%"},tooltip:{trigger:"axis",axisPointer:{type:"shadow",label:{show:!0}}},legend:{data:["发电量","收益"],right:"40%",textStyle:{color:"#333"}},xAxis:[{data:[],axisLine:{lineStyle:{color:"#D9D9D9"}},axisLabel:{show:!0,formatter:"{value}月",color:"#333"}}],yAxis:[{type:"value",name:"发电量 (MWh)",nameTextStyle:{color:"#333"},splitLine:{show:!1},lineStyle:{type:"dashed",color:"#E9E9E9"},axisTick:{show:!1},axisLine:{show:!1},axisLabel:{show:!0,color:"#333"}},{type:"value",name:"收益(万)",nameTextStyle:{color:"#333"},position:"right",splitLine:{show:!1},lineStyle:{type:"dashed",color:"#E9E9E9"},axisTick:{show:!1},axisLine:{show:!1},axisLabel:{show:!0,color:"#333"}}],series:[{name:"发电量",type:"bar",barWidth:15,itemStyle:{normal:{color:"#7a99de"}},data:[]},{name:"收益",type:"line",yAxisIndex:1,smooth:!0,showAllSymbol:!0,symbolSize:10,symbolSize:8,zlevel:3,lineStyle:{normal:{color:"#29def5",shadowBlur:3,shadowColor:o("#29def5",.5),shadowOffsetY:8}},areaStyle:{normal:{color:new e(0,0,0,1,[{offset:0,color:o("#29def5",.3)},{offset:1,color:o("#29def5",.1)}],!1),shadowColor:o("#29def5",.1),shadowBlur:10}},data:[]}]}}{const o=["#d50000","#0091ea","#0090FF","#36CE9E","#FFC005","#FF515A","#8B5CFF","#00CA69"],a=(e,o)=>{let a="";return/^#[\da-f]{6}$/i.test(e)&&(a=`rgba(${parseInt("0x"+e.slice(1,3))},${parseInt("0x"+e.slice(3,5))},${parseInt("0x"+e.slice(5,7))},${o})`),a};t={backgroundColor:"transparent",color:["#0091ea"],legend:{right:10},tooltip:{trigger:"axis"},grid:{top:"50px",bottom:"0px",left:"40px",right:"10px",containLabel:!0},xAxis:[{type:"category",boundaryGap:!1,axisLabel:{formatter:"{value}日",color:"#333"},axisLine:{lineStyle:{color:"#D9D9D9"}},data:[]}],yAxis:[{type:"value",axisLabel:{color:"#666"},nameTextStyle:{color:"#666",fontSize:12,lineHeight:40},splitLine:{lineStyle:{type:"dashed",color:"#E9E9E9"}},axisLine:{show:!1},axisTick:{show:!1}}],series:[{name:"日等效小时数",type:"line",smooth:!0,symbolSize:8,zlevel:3,lineStyle:{normal:{color:o[2],shadowBlur:3,shadowColor:a(o[1],.5),shadowOffsetY:8}},areaStyle:{normal:{color:new e(0,0,0,1,[{offset:0,color:a(o[1],.3)},{offset:1,color:a(o[1],.1)}],!1),shadowColor:a(o[1],.1),shadowBlur:10}},data:[]}]}}{const o=(e,o)=>{let a="";return/^#[\da-f]{6}$/i.test(e)&&(a=`rgba(${parseInt("0x"+e.slice(1,3))},${parseInt("0x"+e.slice(3,5))},${parseInt("0x"+e.slice(5,7))},${o})`),a};s={backgroundColor:"transparent",color:["#29def5"],grid:{top:"40px",bottom:"30px",left:"10%",right:"10%"},tooltip:{trigger:"axis",axisPointer:{type:"shadow",label:{show:!0}}},legend:{data:["发电量","收益"],right:"40%",textStyle:{color:"#333"}},xAxis:[{data:[],axisLabel:{formatter:"{value}日",color:"#333"},axisLine:{lineStyle:{color:"#D9D9D9"}}}],yAxis:[{type:"value",name:"发电量 (MWh)",nameTextStyle:{color:"#333"},splitLine:{show:!1},lineStyle:{type:"dashed",color:"#E9E9E9"},axisTick:{show:!1},axisLine:{show:!1},axisLabel:{show:!0,color:"#333"}},{type:"value",name:"收益(万)",nameTextStyle:{color:"#333"},position:"right",splitLine:{show:!1},lineStyle:{type:"dashed",color:"#E9E9E9"},axisTick:{show:!1},axisLine:{show:!1},axisLabel:{show:!0,color:"#333"}}],series:[{name:"发电量",type:"bar",barMixWidth:10,barMaxWidth:20,itemStyle:{normal:{color:"#5087ec"}},data:[]},{name:"收益",type:"line",yAxisIndex:1,smooth:!0,showAllSymbol:!0,symbolSize:10,symbolSize:8,zlevel:3,lineStyle:{normal:{color:"#29def5",shadowBlur:3,shadowColor:o("#29def5",.5),shadowOffsetY:8}},areaStyle:{normal:{color:new e(0,0,0,1,[{offset:0,color:o("#29def5",.3)},{offset:1,color:o("#29def5",.1)}],!1),shadowColor:o("#29def5",.1),shadowBlur:10}},data:[]}]}}{let e=["#0E7CE2","#FF8352","#E271DE","#F8456B","#00FFFF","#4AEAB0"],o=function(e){return e.toString().replace(/(?=(\B)(\d{3})+$)/g,",")};i={backgroundColor:"transparent",color:e,title:[{text:"设备统计",top:"center",left:"center",textStyle:{rich:{name:{fontSize:34,fontWeight:"normal",padding:[10,0]},val:{fontSize:32,fontWeight:"bold"}}}}],series:[{type:"pie",radius:["45%","60%"],center:["50%","50%"],data:[],color:e,hoverAnimation:!1,itemStyle:{normal:{borderColor:"#fff",borderWidth:2}},labelLine:{normal:{length:20,length2:120}},label:{normal:{formatter:e=>"{icon|●}{name|"+e.name+"}{value|"+o(e.value)+"}",padding:[0,-100,15,-100],rich:{icon:{fontSize:16},name:{fontSize:14,padding:[0,10,0,4]},value:{fontSize:16,fontWeight:"bold"}}}}}]}}async function n(e,l){const t=new o.Workbook;for(const o in e){const a=e[o],l=a.getOption(),r=l.series,s=(null==l?void 0:l.xAxis)&&l.xAxis[0].data,i=t.addWorksheet(o),n=[{header:"时间",key:"xAxis"}];r.forEach(((e,o)=>{const a=e.name;n.push({header:a,key:`series_${o}`})})),i.columns=n,s&&s.forEach(((e,o)=>{const a={xAxis:e};r.forEach(((e,l)=>{a[`series_${l}`]=e.data[o]})),i.addRow(a)})),i.columns.forEach((e=>{let o=0;e.eachCell({includeEmpty:!0},(e=>{const a=e.value?e.value.toString():"";o=Math.max(o,a.length)})),e.width=o<10?10:o}));const d=a.getDataURL(),c=new Image;c.src=d,c.width*=1.3,await new Promise(((e,o)=>{c.onload=e,c.onerror=o}));const h=t.addImage({base64:d,extension:"png"});i.addImage(h,{tl:{col:5,row:1},br:{col:17,row:26}})}const r=await t.xlsx.writeBuffer();a.saveAs(new Blob([r]),l+".xlsx")}export{l as a,s as b,t as d,n as e,r as m,i as t};
