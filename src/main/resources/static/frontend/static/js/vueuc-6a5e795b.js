import"./vue-5bfa3a54.js";import{a as t,o as e}from"./evtd-12f38dac.js";import{b as o,d as n,D as r,C as l,g as i}from"./seemly-4c770f35.js";import{aa as s,F as a,a2 as u,d,a5 as f,s as c,i as h,h as p,p as m,a9 as v,z as g,e as b,A as y,ai as w,m as x,w as S,n as T,g as $,aD as M,aj as R,a6 as z}from"./@vue-5e5cdef9.js";import{u as E}from"./@css-render-b2ef9604.js";import{d as B,u as F,o as A,i as I}from"./vooks-1a9eec0b.js";import{z as L}from"./vdirs-8b258dce.js";import{R as W}from"./@juggle-80d14552.js";import{C as k}from"./css-render-c37d0834.js";function N(t,e,o="default"){const n=e[o];if(void 0===n)throw new Error(`[vueuc/${t}]: slot[${o}] is empty.`);return n()}function C(t,e=!0,o=[]){return t.forEach((t=>{if(null!==t)if("object"==typeof t)if(Array.isArray(t))C(t,e,o);else if(t.type===a){if(null===t.children)return;Array.isArray(t.children)&&C(t.children,e,o)}else t.type!==u&&o.push(t);else"string"!=typeof t&&"number"!=typeof t||o.push(s(String(t)))})),o}function X(t,e,o="default"){const n=e[o];if(void 0===n)throw new Error(`[vueuc/${t}]: slot[${o}] is empty.`);const r=C(n());if(1===r.length)return r[0];throw new Error(`[vueuc/${t}]: slot[${o}] should have exactly one child.`)}let Y=null;function j(){if(null===Y&&(Y=document.getElementById("v-binder-view-measurer"),null===Y)){Y=document.createElement("div"),Y.id="v-binder-view-measurer";const{style:t}=Y;t.position="fixed",t.left="0",t.right="0",t.top="0",t.bottom="0",t.pointerEvents="none",t.visibility="hidden",document.body.appendChild(Y)}return Y.getBoundingClientRect()}function H(t){const e=t.getBoundingClientRect(),o=j();return{left:e.left-o.left,top:e.top-o.top,bottom:o.height+o.top-e.bottom,right:o.width+o.left-e.right,width:e.width,height:e.height}}function O(t){if(null===t)return null;const e=function(t){return 9===t.nodeType?null:t.parentNode}(t);if(null===e)return null;if(9===e.nodeType)return document;if(1===e.nodeType){const{overflow:t,overflowX:o,overflowY:n}=getComputedStyle(e);if(/(auto|scroll|overlay)/.test(t+n+o))return e}return O(e)}const D=d({name:"Binder",props:{syncTargetWithParent:Boolean,syncTarget:{type:Boolean,default:!0}},setup(n){var r;f("VBinder",null===(r=c())||void 0===r?void 0:r.proxy);const l=h("VBinder",null),i=p(null);let s=[];const a=()=>{for(const e of s)t("scroll",e,d,!0);s=[]},u=new Set,d=()=>{o(v)},v=()=>{u.forEach((t=>t()))},g=new Set,b=()=>{g.forEach((t=>t()))};return m((()=>{t("resize",window,b),a()})),{targetRef:i,setTargetRef:t=>{i.value=t,l&&n.syncTargetWithParent&&l.setTargetRef(t)},addScrollListener:t=>{0===u.size&&(()=>{let t=i.value;for(;t=O(t),null!==t;)s.push(t);for(const o of s)e("scroll",o,d,!0)})(),u.has(t)||u.add(t)},removeScrollListener:t=>{u.has(t)&&u.delete(t),0===u.size&&a()},addResizeListener:t=>{0===g.size&&e("resize",window,b),g.has(t)||g.add(t)},removeResizeListener:e=>{g.has(e)&&g.delete(e),0===g.size&&t("resize",window,b)}}},render(){return N("binder",this.$slots)}}),P=d({name:"Target",setup(){const{setTargetRef:t,syncTarget:e}=h("VBinder");return{syncTarget:e,setTargetDirective:{mounted:t,updated:t}}},render(){const{syncTarget:t,setTargetDirective:e}=this;return t?v(X("follower",this.$slots),[[e]]):X("follower",this.$slots)}});const{c:V}=k(),U="vueuc-style";function q(t){return t&-t}class K{constructor(t,e){this.l=t,this.min=e;const o=new Array(t+1);for(let n=0;n<t+1;++n)o[n]=0;this.ft=o}add(t,e){if(0===e)return;const{l:o,ft:n}=this;for(t+=1;t<=o;)n[t]+=e,t+=q(t)}get(t){return this.sum(t+1)-this.sum(t)}sum(t){if(void 0===t&&(t=this.l),t<=0)return 0;const{ft:e,min:o,l:n}=this;if(t>n)throw new Error("[FinweckTree.sum]: `i` is larger than length.");let r=t*o;for(;t>0;)r+=e[t],t-=q(t);return r}getBound(t){let e=0,o=this.l;for(;o>e;){const n=Math.floor((e+o)/2),r=this.sum(n);if(r>t)o=n;else{if(!(r<t))return n;if(e===n)return this.sum(e+1)<=t?e+1:n;e=n}}return e}}function _(t){return"string"==typeof t?document.querySelector(t):t()}const G=d({name:"LazyTeleport",props:{to:{type:[String,Object],default:void 0},disabled:Boolean,show:{type:Boolean,required:!0}},setup:t=>({showTeleport:B(g(t,"show")),mergedTo:b((()=>{const{to:e}=t;return null!=e?e:"body"}))}),render(){return this.showTeleport?this.disabled?N("lazy-teleport",this.$slots):y(w,{disabled:this.disabled,to:this.mergedTo},N("lazy-teleport",this.$slots)):null}}),J={top:"bottom",bottom:"top",left:"right",right:"left"},Q={start:"end",center:"center",end:"start"},Z={top:"height",bottom:"height",left:"width",right:"width"},tt={"bottom-start":"top left",bottom:"top center","bottom-end":"top right","top-start":"bottom left",top:"bottom center","top-end":"bottom right","right-start":"top left",right:"center left","right-end":"bottom left","left-start":"top right",left:"center right","left-end":"bottom right"},et={"bottom-start":"bottom left",bottom:"bottom center","bottom-end":"bottom right","top-start":"top left",top:"top center","top-end":"top right","right-start":"top right",right:"center right","right-end":"bottom right","left-start":"top left",left:"center left","left-end":"bottom left"},ot={"bottom-start":"right","bottom-end":"left","top-start":"right","top-end":"left","right-start":"bottom","right-end":"top","left-start":"bottom","left-end":"top"},nt={top:!0,bottom:!1,left:!0,right:!1},rt={top:"end",bottom:"start",left:"end",right:"start"};const lt=V([V(".v-binder-follower-container",{position:"absolute",left:"0",right:"0",top:"0",height:"0",pointerEvents:"none",zIndex:"auto"}),V(".v-binder-follower-content",{position:"absolute",zIndex:"auto"},[V("> *",{pointerEvents:"all"})])]),it=d({name:"Follower",inheritAttrs:!1,props:{show:Boolean,enabled:{type:Boolean,default:void 0},placement:{type:String,default:"bottom"},syncTrigger:{type:Array,default:["resize","scroll"]},to:[String,Object],flip:{type:Boolean,default:!0},internalShift:Boolean,x:Number,y:Number,width:String,minWidth:String,containerClass:String,teleportDisabled:Boolean,zindexable:{type:Boolean,default:!0},zIndex:Number,overlap:Boolean},setup(t){const e=h("VBinder"),o=F((()=>void 0!==t.enabled?t.enabled:t.show)),n=p(null),r=p(null),l=()=>{const{syncTrigger:o}=t;o.includes("scroll")&&e.addScrollListener(a),o.includes("resize")&&e.addResizeListener(a)},i=()=>{e.removeScrollListener(a),e.removeResizeListener(a)};x((()=>{o.value&&(a(),l())}));const s=E();lt.mount({id:"vueuc/binder",head:!0,anchorMetaName:U,ssr:s}),m((()=>{i()})),A((()=>{o.value&&a()}));const a=()=>{if(!o.value)return;const l=n.value;if(null===l)return;const i=e.targetRef,{x:s,y:a,overlap:u}=t,d=void 0!==s&&void 0!==a?function(t,e){const o=j();return{top:e,left:t,height:0,width:0,right:o.width-t,bottom:o.height-e}}(s,a):H(i);l.style.setProperty("--v-target-width",`${Math.round(d.width)}px`),l.style.setProperty("--v-target-height",`${Math.round(d.height)}px`);const{width:f,minWidth:c,placement:h,internalShift:p,flip:m}=t;l.setAttribute("v-placement",h),u?l.setAttribute("v-overlap",""):l.removeAttribute("v-overlap");const{style:v}=l;v.width="target"===f?`${d.width}px`:void 0!==f?f:"",v.minWidth="target"===c?`${d.width}px`:void 0!==c?c:"";const g=H(l),b=H(r.value),{left:y,top:w,placement:x}=function(t,e,o,n,r,l){if(!r||l)return{placement:t,top:0,left:0};const[i,s]=t.split("-");let a=null!=s?s:"center",u={top:0,left:0};const d=(t,r,l)=>{let i=0,s=0;const a=o[t]-e[r]-e[t];return a>0&&n&&(l?s=nt[r]?a:-a:i=nt[r]?a:-a),{left:i,top:s}},f="left"===i||"right"===i;if("center"!==a){const n=ot[t],r=J[n],l=Z[n];if(o[l]>e[l]){if(e[n]+e[l]<o[l]){const t=(o[l]-e[l])/2;e[n]<t||e[r]<t?e[n]<e[r]?(a=Q[s],u=d(l,r,f)):u=d(l,n,f):a="center"}}else o[l]<e[l]&&e[r]<0&&e[n]>e[r]&&(a=Q[s])}else{const t="bottom"===i||"top"===i?"left":"top",n=J[t],r=Z[t],l=(o[r]-e[r])/2;(e[t]<l||e[n]<l)&&(e[t]>e[n]?(a=rt[t],u=d(r,t,f)):(a=rt[n],u=d(r,n,f)))}let c=i;return e[i]<o[Z[i]]&&e[i]<e[J[i]]&&(c=J[i]),{placement:"center"!==a?`${c}-${a}`:c,left:u.left,top:u.top}}(h,d,g,p,m,u),S=function(t,e){return e?et[t]:tt[t]}(x,u),{left:T,top:$,transform:M}=function(t,e,o,n,r,l){if(l)switch(t){case"bottom-start":case"left-end":return{top:`${Math.round(o.top-e.top+o.height)}px`,left:`${Math.round(o.left-e.left)}px`,transform:"translateY(-100%)"};case"bottom-end":case"right-end":return{top:`${Math.round(o.top-e.top+o.height)}px`,left:`${Math.round(o.left-e.left+o.width)}px`,transform:"translateX(-100%) translateY(-100%)"};case"top-start":case"left-start":return{top:`${Math.round(o.top-e.top)}px`,left:`${Math.round(o.left-e.left)}px`,transform:""};case"top-end":case"right-start":return{top:`${Math.round(o.top-e.top)}px`,left:`${Math.round(o.left-e.left+o.width)}px`,transform:"translateX(-100%)"};case"top":return{top:`${Math.round(o.top-e.top)}px`,left:`${Math.round(o.left-e.left+o.width/2)}px`,transform:"translateX(-50%)"};case"right":return{top:`${Math.round(o.top-e.top+o.height/2)}px`,left:`${Math.round(o.left-e.left+o.width)}px`,transform:"translateX(-100%) translateY(-50%)"};case"left":return{top:`${Math.round(o.top-e.top+o.height/2)}px`,left:`${Math.round(o.left-e.left)}px`,transform:"translateY(-50%)"};default:return{top:`${Math.round(o.top-e.top+o.height)}px`,left:`${Math.round(o.left-e.left+o.width/2)}px`,transform:"translateX(-50%) translateY(-100%)"}}switch(t){case"bottom-start":return{top:`${Math.round(o.top-e.top+o.height+n)}px`,left:`${Math.round(o.left-e.left+r)}px`,transform:""};case"bottom-end":return{top:`${Math.round(o.top-e.top+o.height+n)}px`,left:`${Math.round(o.left-e.left+o.width+r)}px`,transform:"translateX(-100%)"};case"top-start":return{top:`${Math.round(o.top-e.top+n)}px`,left:`${Math.round(o.left-e.left+r)}px`,transform:"translateY(-100%)"};case"top-end":return{top:`${Math.round(o.top-e.top+n)}px`,left:`${Math.round(o.left-e.left+o.width+r)}px`,transform:"translateX(-100%) translateY(-100%)"};case"right-start":return{top:`${Math.round(o.top-e.top+n)}px`,left:`${Math.round(o.left-e.left+o.width+r)}px`,transform:""};case"right-end":return{top:`${Math.round(o.top-e.top+o.height+n)}px`,left:`${Math.round(o.left-e.left+o.width+r)}px`,transform:"translateY(-100%)"};case"left-start":return{top:`${Math.round(o.top-e.top+n)}px`,left:`${Math.round(o.left-e.left+r)}px`,transform:"translateX(-100%)"};case"left-end":return{top:`${Math.round(o.top-e.top+o.height+n)}px`,left:`${Math.round(o.left-e.left+r)}px`,transform:"translateX(-100%) translateY(-100%)"};case"top":return{top:`${Math.round(o.top-e.top+n)}px`,left:`${Math.round(o.left-e.left+o.width/2+r)}px`,transform:"translateY(-100%) translateX(-50%)"};case"right":return{top:`${Math.round(o.top-e.top+o.height/2+n)}px`,left:`${Math.round(o.left-e.left+o.width+r)}px`,transform:"translateY(-50%)"};case"left":return{top:`${Math.round(o.top-e.top+o.height/2+n)}px`,left:`${Math.round(o.left-e.left+r)}px`,transform:"translateY(-50%) translateX(-100%)"};default:return{top:`${Math.round(o.top-e.top+o.height+n)}px`,left:`${Math.round(o.left-e.left+o.width/2+r)}px`,transform:"translateX(-50%)"}}}(x,b,d,w,y,u);l.setAttribute("v-placement",x),l.style.setProperty("--v-offset-left",`${Math.round(y)}px`),l.style.setProperty("--v-offset-top",`${Math.round(w)}px`),l.style.transform=`translateX(${T}) translateY(${$}) ${M}`,l.style.setProperty("--v-transform-origin",S),l.style.transformOrigin=S};S(o,(t=>{t?(l(),u()):i()}));const u=()=>{T().then(a).catch((t=>{}))};["placement","x","y","internalShift","flip","width","overlap","minWidth"].forEach((e=>{S(g(t,e),a)})),["teleportDisabled"].forEach((e=>{S(g(t,e),u)})),S(g(t,"syncTrigger"),(t=>{t.includes("resize")?e.addResizeListener(a):e.removeResizeListener(a),t.includes("scroll")?e.addScrollListener(a):e.removeScrollListener(a)}));const d=I(),f=F((()=>{const{to:e}=t;if(void 0!==e)return e;d.value}));return{VBinder:e,mergedEnabled:o,offsetContainerRef:r,followerRef:n,mergedTo:f,syncPosition:a}},render(){return y(G,{show:this.show,to:this.mergedTo,disabled:this.teleportDisabled},{default:()=>{var t,e;const o=y("div",{class:["v-binder-follower-container",this.containerClass],ref:"offsetContainerRef"},[y("div",{class:"v-binder-follower-content",ref:"followerRef"},null===(e=(t=this.$slots).default)||void 0===e?void 0:e.call(t))]);return this.zindexable?v(o,[[L,{enabled:this.mergedEnabled,zIndex:this.zIndex}]]):o}})}});const st=new class{constructor(){this.handleResize=this.handleResize.bind(this),this.observer=new("undefined"!=typeof window&&window.ResizeObserver||W)(this.handleResize),this.elHandlersMap=new Map}handleResize(t){for(const e of t){const t=this.elHandlersMap.get(e.target);void 0!==t&&t(e)}}registerHandler(t,e){this.elHandlersMap.set(t,e),this.observer.observe(t)}unregisterHandler(t){this.elHandlersMap.has(t)&&(this.elHandlersMap.delete(t),this.observer.unobserve(t))}},at=d({name:"ResizeObserver",props:{onResize:Function},setup(t){let e=!1;const o=c().proxy;function n(e){const{onResize:o}=t;void 0!==o&&o(e)}x((()=>{const t=o.$el;void 0!==t&&(t.nextElementSibling!==t.nextSibling&&3===t.nodeType&&""!==t.nodeValue||null!==t.nextElementSibling&&(st.registerHandler(t.nextElementSibling,n),e=!0))})),m((()=>{e&&st.unregisterHandler(o.$el.nextElementSibling)}))},render(){return $(this.$slots,"default")}});let ut,dt;function ft(){return"undefined"==typeof document?1:(void 0===dt&&(dt="chrome"in window?window.devicePixelRatio:1),dt)}const ct=V(".v-vl",{maxHeight:"inherit",height:"100%",overflow:"auto",minWidth:"1px"},[V("&:not(.v-vl--show-scrollbar)",{scrollbarWidth:"none"},[V("&::-webkit-scrollbar, &::-webkit-scrollbar-track-piece, &::-webkit-scrollbar-thumb",{width:0,height:0,display:"none"})])]),ht=d({name:"VirtualList",inheritAttrs:!1,props:{showScrollbar:{type:Boolean,default:!0},items:{type:Array,default:()=>[]},itemSize:{type:Number,required:!0},itemResizable:Boolean,itemsStyle:[String,Object],visibleItemsTag:{type:[String,Object],default:"div"},visibleItemsProps:Object,ignoreItemResize:Boolean,onScroll:Function,onWheel:Function,onResize:Function,defaultScrollKey:[Number,String],defaultScrollIndex:Number,keyField:{type:String,default:"key"},paddingTop:{type:[Number,String],default:0},paddingBottom:{type:[Number,String],default:0}},setup(t){const e=E();ct.mount({id:"vueuc/virtual-list",head:!0,anchorMetaName:U,ssr:e}),x((()=>{const{defaultScrollIndex:e,defaultScrollKey:o}=t;null!=e?y({index:e}):null!=o&&y({key:o})}));let l=!1,i=!1;M((()=>{l=!1,i?y({top:m.value,left:h}):i=!0})),R((()=>{l=!0,i||(i=!0)}));const s=b((()=>{const e=new Map,{keyField:o}=t;return t.items.forEach(((t,n)=>{e.set(t[o],n)})),e})),a=p(null),u=p(void 0),d=new Map,f=b((()=>{const{items:e,itemSize:o,keyField:n}=t,r=new K(e.length,o);return e.forEach(((t,e)=>{const o=t[n],l=d.get(o);void 0!==l&&r.add(e,l)})),r})),c=p(0);let h=0;const m=p(0),v=F((()=>Math.max(f.value.getBound(m.value-n(t.paddingTop))-1,0))),g=b((()=>{const{value:e}=u;if(void 0===e)return[];const{items:o,itemSize:n}=t,r=v.value,l=Math.min(r+Math.ceil(e/n+1),o.length-1),i=[];for(let t=r;t<=l;++t)i.push(o[t]);return i})),y=(t,e)=>{if("number"==typeof t)return void $(t,e,"auto");const{left:o,top:n,index:r,key:l,position:i,behavior:a,debounce:u=!0}=t;if(void 0!==o||void 0!==n)$(o,n,a);else if(void 0!==r)T(r,a,u);else if(void 0!==l){const t=s.value.get(l);void 0!==t&&T(t,a,u)}else"bottom"===i?$(0,Number.MAX_SAFE_INTEGER,a):"top"===i&&$(0,0,a)};let w,S=null;function T(e,o,r){const{value:l}=f,i=l.sum(e)+n(t.paddingTop);if(r){w=e,null!==S&&window.clearTimeout(S),S=window.setTimeout((()=>{w=void 0,S=null}),16);const{scrollTop:t,offsetHeight:n}=a.value;if(i>t){const r=l.get(e);i+r<=t+n||a.value.scrollTo({left:0,top:i+r-n,behavior:o})}else a.value.scrollTo({left:0,top:i,behavior:o})}else a.value.scrollTo({left:0,top:i,behavior:o})}function $(t,e,o){a.value.scrollTo({left:t,top:e,behavior:o})}const z=!("undefined"!=typeof document&&(void 0===ut&&(ut="matchMedia"in window&&window.matchMedia("(pointer:coarse)").matches),ut));let B=!1;function A(){const{value:t}=a;null!=t&&(m.value=t.scrollTop,h=t.scrollLeft)}function I(t){let e=t;for(;null!==e;){if("none"===e.style.display)return!0;e=e.parentElement}return!1}return{listHeight:u,listStyle:{overflow:"auto"},keyToIndex:s,itemsStyle:b((()=>{const{itemResizable:e}=t,o=r(f.value.sum());return c.value,[t.itemsStyle,{boxSizing:"content-box",height:e?"":o,minHeight:e?o:"",paddingTop:r(t.paddingTop),paddingBottom:r(t.paddingBottom)}]})),visibleItemsStyle:b((()=>(c.value,{transform:`translateY(${r(f.value.sum(v.value))})`}))),viewportItems:g,listElRef:a,itemsElRef:p(null),scrollTo:y,handleListResize:function(e){if(l)return;if(I(e.target))return;if(e.contentRect.height===u.value)return;u.value=e.contentRect.height;const{onResize:o}=t;void 0!==o&&o(e)},handleListScroll:function(e){var o;null===(o=t.onScroll)||void 0===o||o.call(t,e),z&&B||A()},handleListWheel:function(e){var n;if(null===(n=t.onWheel)||void 0===n||n.call(t,e),z){const t=a.value;if(null!=t){if(0===e.deltaX){if(0===t.scrollTop&&e.deltaY<=0)return;if(t.scrollTop+t.offsetHeight>=t.scrollHeight&&e.deltaY>=0)return}e.preventDefault(),t.scrollTop+=e.deltaY/ft(),t.scrollLeft+=e.deltaX/ft(),A(),B=!0,o((()=>{B=!1}))}}},handleItemResize:function(e,o){var n,r,i;if(l)return;if(t.ignoreItemResize)return;if(I(o.target))return;const{value:u}=f,h=s.value.get(e),p=u.get(h),m=null!==(i=null===(r=null===(n=o.borderBoxSize)||void 0===n?void 0:n[0])||void 0===r?void 0:r.blockSize)&&void 0!==i?i:o.contentRect.height;if(m===p)return;0===m-t.itemSize?d.delete(e):d.set(e,m-t.itemSize);const v=m-p;if(0===v)return;u.add(h,v);const g=a.value;if(null!=g){if(void 0===w){const t=u.sum(h);g.scrollTop>t&&g.scrollBy(0,v)}else if(h<w)g.scrollBy(0,v);else if(h===w){m+u.sum(h)>g.scrollTop+g.offsetHeight&&g.scrollBy(0,v)}A()}c.value++}}},render(){const{itemResizable:t,keyField:e,keyToIndex:o,visibleItemsTag:n}=this;return y(at,{onResize:this.handleListResize},{default:()=>{var r,l;return y("div",z(this.$attrs,{class:["v-vl",this.showScrollbar&&"v-vl--show-scrollbar"],onScroll:this.handleListScroll,onWheel:this.handleListWheel,ref:"listElRef"}),[0!==this.items.length?y("div",{ref:"itemsElRef",class:"v-vl-items",style:this.itemsStyle},[y(n,Object.assign({class:"v-vl-visible-items",style:this.visibleItemsStyle},this.visibleItemsProps),{default:()=>this.viewportItems.map((n=>{const r=n[e],l=o.get(r),i=this.$slots.default({item:n,index:l})[0];return t?y(at,{key:r,onResize:t=>this.handleItemResize(r,t)},{default:()=>i}):(i.key=r,i)}))})]):null===(l=(r=this.$slots).empty)||void 0===l?void 0:l.call(r)])}})}}),pt=V(".v-x-scroll",{overflow:"auto",scrollbarWidth:"none"},[V("&::-webkit-scrollbar",{width:0,height:0})]),mt=d({name:"XScroll",props:{disabled:Boolean,onScroll:Function},setup(){const t=p(null);const e=E();pt.mount({id:"vueuc/x-scroll",head:!0,anchorMetaName:U,ssr:e});const o={scrollTo(...e){var o;null===(o=t.value)||void 0===o||o.scrollTo(...e)}};return Object.assign({selfRef:t,handleWheel:function(t){t.currentTarget.offsetWidth<t.currentTarget.scrollWidth&&0!==t.deltaY&&(t.currentTarget.scrollLeft+=t.deltaY+t.deltaX,t.preventDefault())}},o)},render(){return y("div",{ref:"selfRef",onScroll:this.onScroll,onWheel:this.disabled?void 0:this.handleWheel,class:"v-x-scroll"},this.$slots)}}),vt="v-hidden",gt=V("[v-hidden]",{display:"none!important"}),bt=d({name:"Overflow",props:{getCounter:Function,getTail:Function,updateCounter:Function,onUpdateCount:Function,onUpdateOverflow:Function},setup(t,{slots:e}){const o=p(null),n=p(null);function r(r){const{value:l}=o,{getCounter:i,getTail:s}=t;let a;if(a=void 0!==i?i():n.value,!l||!a)return;a.hasAttribute(vt)&&a.removeAttribute(vt);const{children:u}=l;if(r.showAllItemsBeforeCalculate)for(const t of u)t.hasAttribute(vt)&&t.removeAttribute(vt);const d=l.offsetWidth,f=[],c=e.tail?null==s?void 0:s():null;let h=c?c.offsetWidth:0,p=!1;const m=l.children.length-(e.tail?1:0);for(let e=0;e<m-1;++e){if(e<0)continue;const o=u[e];if(p){o.hasAttribute(vt)||o.setAttribute(vt,"");continue}o.hasAttribute(vt)&&o.removeAttribute(vt);const n=o.offsetWidth;if(h+=n,f[e]=n,h>d){const{updateCounter:o}=t;for(let n=e;n>=0;--n){const r=m-1-n;void 0!==o?o(r):a.textContent=`${r}`;const l=a.offsetWidth;if(h-=f[n],h+l<=d||0===n){p=!0,e=n-1,c&&(-1===e?(c.style.maxWidth=d-l+"px",c.style.boxSizing="border-box"):c.style.maxWidth="");const{onUpdateCount:o}=t;o&&o(r);break}}}}const{onUpdateOverflow:v}=t;p?void 0!==v&&v(!0):(void 0!==v&&v(!1),a.setAttribute(vt,""))}const l=E();return gt.mount({id:"vueuc/overflow",head:!0,anchorMetaName:U,ssr:l}),x((()=>r({showAllItemsBeforeCalculate:!1}))),{selfRef:o,counterRef:n,sync:r}},render(){const{$slots:t}=this;return T((()=>this.sync({showAllItemsBeforeCalculate:!1}))),y("div",{class:"v-overflow",ref:"selfRef"},[$(t,"default"),t.counter?t.counter():y("span",{style:{display:"inline-block"},ref:"counterRef"}),t.tail?t.tail():null])}});function yt(t){return t instanceof HTMLElement}function wt(t){for(let e=0;e<t.childNodes.length;e++){const o=t.childNodes[e];if(yt(o)&&(St(o)||wt(o)))return!0}return!1}function xt(t){for(let e=t.childNodes.length-1;e>=0;e--){const o=t.childNodes[e];if(yt(o)&&(St(o)||xt(o)))return!0}return!1}function St(t){if(!function(t){if(t.tabIndex>0||0===t.tabIndex&&null!==t.getAttribute("tabIndex"))return!0;if(t.getAttribute("disabled"))return!1;switch(t.nodeName){case"A":return!!t.href&&"ignore"!==t.rel;case"INPUT":return"hidden"!==t.type&&"file"!==t.type;case"BUTTON":case"SELECT":case"TEXTAREA":return!0;default:return!1}}(t))return!1;try{t.focus({preventScroll:!0})}catch(e){}return document.activeElement===t}let Tt=[];const $t=d({name:"FocusTrap",props:{disabled:Boolean,active:Boolean,autoFocus:{type:Boolean,default:!0},onEsc:Function,initialFocusTo:String,finalFocusTo:String,returnFocusOnDeactivated:{type:Boolean,default:!0}},setup(o){const n=l(),r=p(null),s=p(null);let a=!1,u=!1;const d="undefined"==typeof document?null:document.activeElement;function f(){return Tt[Tt.length-1]===n}function c(t){var e;"Escape"===t.code&&f()&&(null===(e=o.onEsc)||void 0===e||e.call(o,t))}function h(t){if(!u&&f()){const e=v();if(null===e)return;if(e.contains(i(t)))return;b("first")}}function v(){const t=r.value;if(null===t)return null;let e=t;for(;!(e=e.nextSibling,null===e||e instanceof Element&&"DIV"===e.tagName););return e}function g(){var t;if(o.disabled)return;if(document.removeEventListener("focus",h,!0),Tt=Tt.filter((t=>t!==n)),f())return;const{finalFocusTo:e}=o;void 0!==e?null===(t=_(e))||void 0===t||t.focus({preventScroll:!0}):o.returnFocusOnDeactivated&&d instanceof HTMLElement&&(u=!0,d.focus({preventScroll:!0}),u=!1)}function b(t){if(f()&&o.active){const e=r.value,o=s.value;if(null!==e&&null!==o){const n=v();if(null==n||n===o)return u=!0,e.focus({preventScroll:!0}),void(u=!1);u=!0;const r="first"===t?wt(n):xt(n);u=!1,r||(u=!0,e.focus({preventScroll:!0}),u=!1)}}}return x((()=>{S((()=>o.active),(r=>{r?(!function(){var t;if(o.disabled)return;if(Tt.push(n),o.autoFocus){const{initialFocusTo:e}=o;void 0===e?b("first"):null===(t=_(e))||void 0===t||t.focus({preventScroll:!0})}a=!0,document.addEventListener("focus",h,!0)}(),e("keydown",document,c)):(t("keydown",document,c),a&&g())}),{immediate:!0})})),m((()=>{t("keydown",document,c),a&&g()})),{focusableStartRef:r,focusableEndRef:s,focusableStyle:"position: absolute; height: 0; width: 0;",handleStartFocus:function(t){if(u)return;const e=v();null!==e&&(null!==t.relatedTarget&&e.contains(t.relatedTarget)?b("last"):b("first"))},handleEndFocus:function(t){u||(null!==t.relatedTarget&&t.relatedTarget===r.value?b("last"):b("first"))}}},render(){const{default:t}=this.$slots;if(void 0===t)return null;if(this.disabled)return t();const{active:e,focusableStyle:o}=this;return y(a,null,[y("div",{"aria-hidden":"true",tabindex:e?"0":"-1",ref:"focusableStartRef",style:o,onFocus:this.handleStartFocus}),t(),y("div",{"aria-hidden":"true",style:o,ref:"focusableEndRef",tabindex:e?"0":"-1",onFocus:this.handleEndFocus})])}});export{$t as F,G as L,at as V,ht as a,it as b,P as c,D as d,bt as e,mt as f,st as r};
