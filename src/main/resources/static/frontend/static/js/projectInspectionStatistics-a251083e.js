import{_ as t}from"./MyForm-f1cf6891.js";import{c as e,_ as s}from"./dateUtil-5e0180b5.js";import"./vue-5bfa3a54.js";import{a as i,b as o}from"./projectInspectionStatistics-f44bae7a.js";import{c as r}from"./pageUtil-3bb2e07a.js";import{c as a}from"./getSetObj-f4228515.js";import{f as p}from"./formUtil-7f692cbf.js";import{e as m}from"./exportFile-75030642.js";import{_ as l}from"./index-a5df0f75.js";import{h as n,j,e as c,m as u,as as d,o as f,c as v,x as w,a8 as b,b as y}from"./@vue-5e5cdef9.js";import"./quasar-df1bac18.js";import"./@vueuse-5227c686.js";import"./vue-router-6159329f.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./lodash-6d99edc3.js";import"./@babel-f3c0a00c.js";import"./naive-ui-0ee0b8c3.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./lodash-es-ea7deab5.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./date-fns-tz-0cc073c8.js";import"./async-validator-cf877c1f.js";import"./@vicons-f32a0bdb.js";import"./dayjs-67f8ddef.js";import"./notification-950a5f80.js";import"./proxyUtil-6f30f7ef.js";import"./api-360ec627.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./menuStore-30bf76d3.js";import"./icons-95011f8c.js";import"./element-plus-95e0b914.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./xe-utils-fe99d42a.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";/* empty css                */import"./@x-ui-vue3-df3ba55b.js";/* empty css                    */import"./vxe-table-plugin-element-be30dbb6.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";const x={class:"main tw-h-full tw-w-full tw-p-4 tw-min-w-[1280]"},g=l({__name:"projectInspectionStatistics",setup(l){let g=n();const h=r(N);n();const k=n([]),_=e(2,"日月年",0),T=j([{formType:"slot",label:"",prop:"time",value:c(a(_))},{formType:"button",label:"查询",value:!1,prop:"check",invoke:N},{formType:"space"},{formType:"button",label:"导出",value:!1,prop:"export",invoke:async function(t=p.getValue(T)){const e=await o(..._.date),s=await p.exportFile(e,T,"export");m(s,"项目巡检统计")}}]);async function z(t){await N()}const I=({row:t,_rowIndex:e,column:s,visibleData:i,columnIndex:o})=>{const r=t[s.field];if("合计："===r&&"plantType"===s.field)return{rowspan:1,colspan:3};if("合计："===r&&"city"===s.field)return{rowspan:1,colspan:0};if("合计："===r&&"projectId"===s.field)return{rowspan:1,colspan:0};if(r&&["plantType","projectId"].includes(s.field)){const t=i[e-1];let o=i[e+1];if(t&&t[s.field]===r)return{rowspan:0,colspan:0};{let t=1;for(;o&&o[s.field]===r;)o=i[++t+e];if(t>1)return{rowspan:t,colspan:1}}}};async function N(t=p.getValue(T),e,s){const o=await i(..._.date,t.plantName);k.value=o.data.value,p.tableResponse(o,g,h,T,"check",e,s)}return u((async()=>{await N()})),(e,i)=>{const o=s,r=t,a=d("vxe-column"),p=d("vxe-table");return f(),v("div",x,[w(r,{formList:y(T),page:y(h),class:"header tw-px-3 tw-rounded-sm",title:""},{time:b((()=>[w(o,{changeTab:z,date:y(_),class:"tw-w-[440px] tw-mr-2"},null,8,["date"])])),_:1},8,["formList","page"]),w(p,{"column-config":{resizable:!0},data:y(k),"scroll-y":{enabled:!1},"span-method":I,align:"center",class:"tw-rounded-sm text-lg","max-height":"800",border:"full"},{default:b((()=>[w(a,{field:"plantType",title:"站点类型"}),w(a,{field:"city",title:"区域"}),w(a,{field:"projectId",title:"项目名称"}),w(a,{field:"plantNum",title:"电站数量"}),w(a,{field:"plantCapacity",title:"装机容量:KWp"}),w(a,{field:"electricity",title:"发电量:KWh"}),w(a,{field:"dailyEfficiencyPerHour",title:"日等效小时"}),w(a,{field:"alarmPlantNum",title:"告警电站数量"})])),_:1},8,["data"])])}}},[["__scopeId","data-v-39f6604c"]]);export{g as default};
