import{a as e,F as s,d as a,Y as t,K as i,U as l}from"./quasar-b3f06d8a.js";import{_ as o}from"./pagination-c4d8e88e.js";import"./vue-5bfa3a54.js";import{j as r,h as n,e as p,m,az as d,o as c,c as j,a9 as u,b,f as g,a8 as v,x as f,F as h,k as w,aa as x,t as k,C as y,D as _,a as z}from"./@vue-5e5cdef9.js";import{l as U}from"./labelValueToObj-fe73517d.js";import{L as C}from"./ui-d5488bf7.js";import"./notification-950a5f80.js";import{_ as V}from"./index-8cc8d4b8.js";import"./naive-ui-0ee0b8c3.js";import"./css-render-c37d0834.js";import"./@emotion-b17c1a96.js";import"./@css-render-b2ef9604.js";import"./seemly-4c770f35.js";import"./lodash-es-ea7deab5.js";import"./vooks-1a9eec0b.js";import"./evtd-12f38dac.js";import"./vueuc-6a5e795b.js";import"./vdirs-8b258dce.js";import"./@juggle-80d14552.js";import"./treemate-b1f44b11.js";import"./date-fns-992c4b85.js";import"./@babel-f3c0a00c.js";import"./date-fns-tz-0cc073c8.js";import"./async-validator-cf877c1f.js";import"./lodash-6d99edc3.js";import"./@x-ui-vue3-df3ba55b.js";import"./@vueuse-af86c621.js";import"./vue-router-6159329f.js";import"./universal-cookie-eda71741.js";import"./cookie-b9a095b7.js";import"./axios-84f1a956.js";import"./element-plus-d975be09.js";import"./@element-plus-4c34063a.js";import"./@popperjs-b78c3215.js";import"./@ctrl-91de2ec7.js";import"./dayjs-d60cc07f.js";import"./memoize-one-63ab667a.js";import"./normalize-wheel-es-3222b0a2.js";import"./@floating-ui-3c499e77.js";import"./pinia-c7531a5f.js";import"./vue-demi-01e7384c.js";import"./pinia-plugin-persistedstate-35ef556e.js";import"./bignumber.js-a537a5ca.js";import"./xe-utils-fe99d42a.js";import"./js-cookie-8253c38e.js";import"./spark-md5-022b35d0.js";import"./vxe-table-3a25f2d2.js";import"./dom-zindex-5f662ad1.js";import"./nprogress-e136a1b4.js";/* empty css                *//* empty css                    */import"./vxe-table-plugin-element-7bbbb435.js";import"./@kjgl77-c885ad03.js";import"./vue-virtual-scroller-55ed7746.js";import"./vue-resize-6b3cb84f.js";import"./vue-observe-visibility-e7e33a1f.js";import"./vue3-seamless-scroll-99760548.js";import"./vue-konva-ae2c3fae.js";import"./konva-5c630d74.js";import"./echarts-f30da64f.js";import"./tslib-a4e99503.js";import"./zrender-c058db04.js";const q=r([{label:"逆变器SN",field:"plantStatus",align:"center",search:n("")},{label:"所属电站",field:"plantName",align:"center",search:n("")},{label:"运行状态",field:"createTime",align:"center",search:n("")},{label:"实时功率",field:"plantCapacity",align:"center",search:n("")},{label:"日发电量",field:"status",align:"center",search:n("")},{label:"月发电量",field:"inverNums",align:"center",search:n("")},{label:"年发电量",field:"plantTypeId",align:"center",search:n("")},{label:"总发电量",field:"power",align:"center",search:n("")},{label:"配电箱状态",field:"todayElectricity",align:"center",search:n("")},{label:"操作",field:"address",align:"center",search:n("")}]),N=e=>(y("data-v-bcb97ad4"),e=e(),_(),e),S={class:"q-pa-md"},T=N((()=>z("h6",{class:"tw-text-2xl"},"指标配置",-1))),D=N((()=>z("div",{class:"full-width row flex-center text-accent q-gutter-sm"},[z("span",null," 没有可用数据 ")],-1))),P=V({__name:"indexConfiguration",setup(r){let y=n([]);p((()=>U(q,"field","search")));const _=new C;return n([Date.now(),Date.now()]),m((async()=>{_.set(!1)})),(r,n)=>{const p=e,m=s,z=a,U=t,C=i,V=o,N=l,P=d("skeleton-item"),E=d("skeleton");return c(),j("div",S,[u((c(),g(N,{"row-key":"plantUid","virtual-scroll":"",separator:"cell","table-header-class":"","title-class":"tw-bg-blue-300","table-class":"tw-bg-gray-100 tw--h-100",rows:b(y),columns:b(q),"rows-per-page-options":[0],loading:b(_).value.loading,"table-style":{height:"780px"}},{top:v((()=>[T,f(p),u(f(m,{class:"tw-bg-yellow-400 tw-text-white tw-mr-3",label:"查询","no-caps":"",onClick:()=>{}},null,512),[[P]]),u(f(m,{class:"tw-bg-green-500 tw-text-white",label:"导出","no-caps":"",onClick:()=>{}},null,512),[[P]])])),"top-row":v((()=>[f(C,null,{default:v((()=>[(c(!0),j(h,null,w(b(q),(e=>(c(),g(U,{key:null==e?void 0:e.label,style:{"border-bottom-width":"1px"}},{default:v((()=>[u(f(z,{borderless:"",dense:"",modelValue:e.search,"onUpdate:modelValue":s=>e.search=s,placeholder:"🔍"},null,8,["modelValue","onUpdate:modelValue"]),[[P]])])),_:2},1024)))),128))])),_:1})])),"body-cell":v((e=>[f(U,{props:e},{default:v((()=>[x(k(e.value),1)])),_:2},1032,["props"])])),pagination:v((e=>[f(V,{onUpdatePagination:r.updatePagination,total:100},null,8,["onUpdatePagination"])])),"no-data":v((({icon:e,message:s,filter:a})=>[D])),_:1},8,["rows","columns","loading"])),[[E,b(_).value,void 0,{animated:!0}]])])}}},[["__scopeId","data-v-bcb97ad4"]]);export{P as default};
