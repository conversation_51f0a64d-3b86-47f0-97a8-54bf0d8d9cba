import io.xjar.XCryptos;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class XjarApplicationTests {

    @Test
    void contextLoads() {
        try {
            XCryptos.encryption()
                    .from("D:\\IdeaProjects\\local-business\\target\\local-business-1.0.0.jar")
                    .use("bto")
                    .include("/com/bto/**/**/*.class")
                    .include("/mapper/*Mapper.xml")
                    .exclude("/com/bto/BtoLocalBusinessApplication.class")
                    .exclude("/static/frontend/**/*")
                    .to("D:\\IdeaProjects\\local-business\\encrypted.jar");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
