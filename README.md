# bto-xhl-server

## 项目介绍
`bto-xhl-server` 是一个基于 Spring Boot 和 MyBatis-Plus 构建的企业级后端服务项目，主要用于管理光伏电站相关的业务逻辑。项目支持用户管理、设备监控、告警处理、数据统计等功能，适用于户用、工商业、整县等不同类型的光伏项目。

---

## 软件架构
### 技术栈
- **语言**: Java 1.8
- **框架**: Spring Boot, Spring Cloud, MyBatis-Plus
- **数据库**: MySQL
- **缓存**: Redis
- **安全**: Spring Security, OAuth2
- **工具库**:
   - Hutool: 提供通用工具类支持。
   - FastJSON: JSON 序列化与反序列化。
   - EasyExcel: Excel 文件处理。
   - Knife4j: API 文档生成。
   - MapStruct: 对象映射工具。
   - Lombok: 简化代码开发。
- **云存储**: 阿里云 OSS、腾讯云 COS、华为云 OBS、七牛云、MinIO。
- **其他**:
   - Swagger: 接口文档。
   - JWT: 用户认证。
   - XJar: 加密工具。

---

## 功能模块
### 核心功能
1. **用户管理**
   - 用户注册、登录、权限分配。
   - 支持多角色（超级管理员、普通用户等）。

2. **项目管理**
   - 支持不同类型项目的管理（户用、工商业、整县等）。

3. **设备管理**
   - 光伏设备的增删改查及实时监控。

4. **告警管理**
   - 设备异常告警、告警信息统计。

5. **数据统计**
   - 发电量、装机容量等数据的统计分析。

6. **文件存储**
   - 支持多种云存储服务（阿里云、腾讯云、华为云等）。

7. **API 文档**
   - 使用 Knife4j 提供接口文档。

---

## 安装教程
### 环境准备
1. JDK 1.8 或更高版本。
2. Maven 3.x。
3. MySQL 数据库。
4. Redis 缓存服务。

### 本地运行
1. 克隆项目代码到本地：
   ```bash
   git clone https://gitee.com/guangdong-botong-new-energy/bto-xhl-server.git
   ```

2. 配置数据库连接：
   - 修改 `src/main/resources/application.yml` 中的数据库配置。
   ```yaml
   spring:
     datasource:
       url: ***************************************************************************************************
       username: your_username
       password: your_password
   ```

3. 启动服务：
   ```bash
   mvn spring-boot:run
   ```


4. 访问服务地址：
   - 默认地址：`http://localhost:52000/system`。
   - API 文档地址：`http://localhost:52000/doc.html`。

---
